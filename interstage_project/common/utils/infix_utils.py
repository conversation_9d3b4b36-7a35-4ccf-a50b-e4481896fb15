"""
This module contains the utils classes for infix expression conversion to snowflake query
"""

## case insensitive flag will be removed in future and all functions will be case insensitive going forward
# and should be consistent across system.

import decimal

##To Do: For Flatten Types converter , extend this class and override the behaviour of the functions
## which requires typecast operator
import logging
from collections import defaultdict
from typing import Optional

from django.utils import timezone

from commission_engine.accessors.client_accessor import get_client_fiscal_start_month
from commission_engine.utils import CalType, PeriodUnit, period_to_freq_map
from commission_engine.utils.date_utils import get_period_start_and_end_date
from commission_engine.utils.general_data import Period

logger = logging.getLogger(__name__)


# ruff: noqa: PLR0911
# ruff: noqa: RET505
# ruff: noqa: TRY003
# ruff: noqa: PLR0913
# ruff: noqa: FBT003
class InfixToSnowflakeQuery:
    """
    Class to convert infix expression to snowflake query
    Initialize with infix expression and column prefix
    column prefix is used to prefix the column names lookup in the expression like data: for datasheet_data table
    convert method will return the snowflake query
    """

    def __init__(
        self,
        *,
        infix_expression: list,
        client_id: int,
        colname_type_map: Optional[dict] = None,
        col_prefix: Optional[str] = None,
        typecast_operator: Optional[str] = None,
        case_insensitive=False,
        date_trunc_precision: str = "second",
    ):
        self.infix_expression = infix_expression
        self.col_prefix = col_prefix if col_prefix is not None else ""
        self.col_type_map = colname_type_map if colname_type_map is not None else {}
        self.typecast_operator = typecast_operator
        # Fallback operator for functions where typecast operator is required like date functions and empty check
        self.fallback_typecast_operator = "::"
        self.client_id = client_id
        self.client_fiscal_start_month = get_client_fiscal_start_month(client_id)
        # Flag to check if the comparison is case insensitive
        # Contains , Not Contains, StartsWith, EndsWith are default case insensitive
        # If this flag is set to True, then all the string equality comparison will be case insensitive
        self.case_insensitive = case_insensitive
        # Precision for date truncation - can be 'second' or 'day'
        self.date_trunc_precision = date_trunc_precision

    @property
    def colname_typecast_map(self):
        """
        Mapping object the colname with type and typecast operator
        Example:
        Converts the colname_type_map
        {
            "co_1_name" : "STRING",
        }
        To
        {
            "co_1_name": "::STRING",
        }
        """
        colname_typecast_map = defaultdict(str)
        if not self.typecast_operator:
            return colname_typecast_map

        for colname, coltype in self.col_type_map.items():
            colname_typecast_map[colname] = f"{self.typecast_operator}{coltype}"
        return colname_typecast_map

    def convert(self):
        """
        Function to convert infix expression to snowflake query
        """

        conditional_expression = ""
        for token in self.infix_expression:
            if token["type"] == "VARIABLE":
                var = self._handle_variable(token)
                if conditional_expression.strip()[-2:] == "IN" and var[0] != "(":
                    # Wrap a variable with brackets for IN and NOT in Operators
                    conditional_expression += "(" + var + ") "
                else:
                    conditional_expression += var + " "
            elif token["type"] == "OPERATOR":
                name = (
                    self._operator_to_snowflake_query_dict().get(token["name"])
                    or token["name"]
                )
                conditional_expression += name + " "
            else:
                conditional_expression += token["name"] + " "

        return conditional_expression

    def get_default_typecast_operator(self):
        """
        IF Typecast operator is not provided, then return the default typecast operator
        """
        return (
            self.typecast_operator
            if self.typecast_operator
            else self.fallback_typecast_operator
        )

    def _handle_variable(self, token):
        if "function_name" in token:
            func_name = token["function_name"]
            res = self._custom_function_to_snowflake_query_transform_func(func_name)(
                *token["args"]
            )

        elif "meta" in token:
            res = token["meta"]["system_name"]
            data_type = token["data_type"].upper()
            # if data type is a date, truncate the fractional seconds part while comparing
            if data_type == "DATE":
                res = self._handle_date_token(res)
            elif data_type == "HIERARCHY":
                res = self._handle_hierarchy_token(res)
            elif data_type == "STRING" and self.case_insensitive:
                res = self._handle_case_insensitive_string(res)
            else:
                res = self.col_prefix + res + self.colname_typecast_map[res]
        else:
            res = str(token)

        return res

    def _handle_date_token(self, token):
        if not self.typecast_operator:
            logger.warning(
                "Typecast operator is required for date comparison, so '::' is used as default"
            )
        typecast_operator = self.get_default_typecast_operator()

        # Validate date_trunc_precision
        valid_precisions = ["second", "day"]
        if self.date_trunc_precision not in valid_precisions:
            logger.warning(
                "Invalid date_trunc_precision: {self.date_trunc_precision}. Using default 'second'"
            )
            trunc_precision = "second"
        else:
            trunc_precision = self.date_trunc_precision

        if typecast_operator == "spark_cast":
            return f" date_trunc('{trunc_precision}', {self.col_prefix + token}) "
        else:
            return f" date_trunc('{trunc_precision}', {self.col_prefix + token}{typecast_operator}DATETIME) "

    def _handle_hierarchy_token(self, token):
        return f"{self.col_prefix + token}::variant::object:repr::string"

    def _handle_case_insensitive_string(self, token):
        return f"LOWER({self.col_prefix + token}{self.colname_typecast_map[token]} )"

    def _custom_function_to_snowflake_query_transform_func(self, function):
        custom_to_snowflake_dict = {
            "DATEDIFF": self._date_diff,
            "DateAdd": self._date_add,
            "IsEmpty": self._is_empty,
            "IsNotEmpty": self._is_not_empty,
            "Round": self._round,
            "RoundUp": self._round_up,
            "RoundDown": self._round_down,
            "Contains": self._contains,
            "NotContains": self._not_contains,
            "Len": self._len,
            "Find": self._find,
            "Lower": self._lower,
            "CONSTANT": self._const,
            "StartsWith": self._starts_with,
            "EndsWith": self._ends_with,
            "DateIsIn": self._date_is_in,
        }

        return custom_to_snowflake_dict[function]

    def _operator_to_snowflake_query_dict(self):
        return {"NOTIN": "NOT IN", "==": "="}

    def _date_diff(self, *args):
        start_date, end_date, date_unit, _ = args
        start_date = self._handle_variable(start_date)
        end_date = self._handle_variable(end_date)
        if date_unit == Period.HALFYEAR.value:
            date_unit = Period.MONTH.value
            return f"DATEDIFF({date_unit}, {start_date}, {end_date}) / 6"

        return f"DATEDIFF({date_unit}, {start_date}, {end_date})"

    def _date_add(self, date_variable, interval, date_unit):
        date = self._handle_variable(date_variable)
        interval = self._handle_variable(interval)
        if date_unit == Period.HALFYEAR.value:
            date_unit = Period.MONTH.value
            return f"DATEADD({date_unit}, {interval} * 6, {date})"

        return f"DATEADD({date_unit}, {interval}, {date})"

    def _is_empty(self, token):
        value = self._handle_variable(token)
        return f"({value} is null OR {self._build_query_for_null_check(value)})"

    def _is_not_empty(self, token):
        value = self._handle_variable(token)
        return f"({value} is not null AND {self._build_query_for_null_check(value, 'not in')})"

    def _round(self, token, decimal_places):
        value = self._handle_variable(token)
        return f"ROUND({value}, {int(decimal_places)})"

    def _round_up(self, token, decimal_places):
        value = self._handle_variable(token)
        return f"CEIL({value}, {int(decimal_places)})"

    def _round_down(self, token, decimal_places):
        value = self._handle_variable(token)
        return f"FLOOR({value}, {int(decimal_places)})"

    def _contains(self, token, query):
        text = self._handle_variable(token)
        pattern = self._handle_variable(query)
        if self.case_insensitive:
            # If case insensitive is set, then text and pattern are converted to lower case in handle variable method
            return f"CONTAINS({text}, {pattern})"
        return f"CONTAINS(LOWER({text}), LOWER({pattern}))"

    def _not_contains(self, token, query):
        text = self._handle_variable(token)
        pattern = self._handle_variable(query)
        if self.case_insensitive:
            # If case insensitive is set, then text and pattern are converted to lower case in handle variable method
            return f"({text} is null OR NOT CONTAINS({text}, {pattern}))"
        return f"({text} is null OR NOT CONTAINS(LOWER({text}), LOWER({pattern})))"

    def _len(self, token):
        text = self._handle_variable(token)
        return f"LENGTH({text})"

    def _lower(self, token):
        text = self._handle_variable(token)
        if self.case_insensitive:
            # If case insensitive is set, then text and pattern are converted to lower case in handle variable method
            return text
        return f"LOWER({text})"

    def _find(self, token, query):
        text = self._handle_variable(token)
        pattern = self._handle_variable(query)
        return f"POSITION({text}, {pattern})"

    def _starts_with(self, token, query):
        text = self._handle_variable(token)
        pattern = self._handle_variable(query)
        return f"startswith({text}, {pattern})"

    def _ends_with(self, token, query):
        text = self._handle_variable(token)
        pattern = self._handle_variable(query)
        return f"endswith({text}, {pattern})"

    def _const(self, data_type, value):
        if data_type == "Percentage":
            return str(decimal.Decimal(value) / 100)
        elif data_type == "Date":
            return f"to_date('{value}')"
        elif data_type == "Integer":
            value = str(value)
            return str(decimal.Decimal(value))
        elif "StringArray" in data_type:
            return ",".join([self._handle_string_value(val) for val in value])
        elif "Array" in data_type:
            return ",".join([str(val) for val in value])
        elif data_type in ["String", "Email"]:
            if data_type == "String":
                return f"{self._handle_string_value(value)}"
            return f"'{self._format_string_for_snowflake(value)}'"
        else:
            return value

    def _date_is_in(self, date_token, unit, period, cal_type):
        date_token = self._handle_variable(date_token)
        curr_time = timezone.now()
        prev_required = unit == PeriodUnit.PREVIOUS.value
        start_month = (
            1 if cal_type == CalType.CALENDAR.value else self.client_fiscal_start_month
        )
        # if period is not in the map, then it is a custom period
        freq = period_to_freq_map.get(period, period)

        period_dict = get_period_start_and_end_date(
            curr_time, start_month, freq, prev_required, False, self.client_id
        )

        if prev_required:
            start_date = period_dict["prev_start_date"]
            end_date = period_dict["prev_end_date"]
        else:
            start_date = period_dict["start_date"]
            end_date = period_dict["end_date"]

        return f"({date_token} >= '{start_date}' and {date_token} <= '{end_date}' )"

    def _handle_string_value(self, value):
        if self.case_insensitive:
            return f"LOWER('{self._format_string_for_snowflake(value)}')"
        return f"'{self._format_string_for_snowflake(value)}'"

    def _format_string_for_snowflake(self, string):
        """
        Function to format string to make snowflake query valid
        """
        ##Snowflake treats double quotes as table identifier, so we need to use single quotes for string
        ##But if the string itself contains single quotes, then we need to escape it
        ## \\ will be converted to \ escape quote and then the quote will be escaped
        ## Example:
        ## "name != 'Deal\\'s'" will be converted to name != 'Deal\'s'
        return string.replace("'", "\\'")

    def _build_query_for_null_check(self, value, operator="in"):
        if not self.typecast_operator:
            logger.warning(
                "Typecast operator is required for date comparison, so '::' is used as default"
            )
        typecast_operator = self.get_default_typecast_operator()
        if typecast_operator == "spark_cast":
            return f"CAST({value} AS STRING) {operator} ('None', 'nan', '', 'none')"
        else:
            value = value.replace(f"{self.typecast_operator}STRING", "")
            return f"{value}{typecast_operator}STRING {operator} ('None', 'nan', '', 'none')"
