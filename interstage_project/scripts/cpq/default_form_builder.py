# python scripts/cpq/default_form_builder.py --client-id 31618
import argparse
import sys

from django import setup
from django.db import transaction
from django.utils import timezone


@transaction.atomic
def create_default_form_builder(client_id: int):
    from everstage_ddd.cpq.forms.service.form_service import get_form_spec_from_db
    from spm.services.config_services.employee_services import (
        get_power_admin_users_for_client,
    )

    print("Client ID: ", client_id)
    form_spec = get_form_spec_from_db(client_id=client_id)
    created_by = get_power_admin_users_for_client(client_id)[0]
    print("Created by: ", created_by)

    form_builder = FormBuilder(
        form_builder_id="f7c3c817-2a5c-4a88-94f1-fae970bd7395",
        form_builder_name="Everstage Form",
        form_builder_description="Default form for Everstage",
        status="active",
        form_spec=form_spec,
        created_at=timezone.now(),
        created_by=created_by,
        is_deleted=False,
        knowledge_end_date=None,
        knowledge_begin_date=timezone.now(),
        client_id=client_id,
    )
    form_builder.save()


if __name__ == "__main__":
    setup()

    parser = argparse.ArgumentParser(description="Create or check default form builder")
    parser.add_argument(
        "--client-id", type=int, required=True, help="Client ID for the form builder"
    )

    args = parser.parse_args()

    from everstage_ddd.cpq.forms.models.form_models import FormBuilder

    form_builder_exists = FormBuilder.objects.filter(
        form_builder_id="f7c3c817-2a5c-4a88-94f1-fae970bd7395",
        is_deleted=False,
        knowledge_end_date=None,
    ).exists()

    if form_builder_exists:
        print("Form builder already exists")
        sys.exit()
    print("Form builder does not exist, creating...")

    create_default_form_builder(args.client_id)
