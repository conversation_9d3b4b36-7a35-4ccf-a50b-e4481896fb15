"""
Check Missing Datasheet Variables that are used in Crystal Views
"""

import django

django.setup()
import json
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Set, Tuple

from celery import group, shared_task

from common.celery.celery_base_task import EverCeleryBaseTask
from crystal.accessors.crystal_admin_accessor import CrystalAdminAccessor
from crystal.utils.crystal_utils import (
    get_datasheet_variables_used_in_crystal_simulator,
)
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from scripts.global_customer_data_health_check.anomaly_log_service import (
    log_anomaly,
    log_heartbeat,
)

logger = logging.getLogger(__name__)


def is_get_env() -> str:
    """Returns the environment name, defaulting to 'local' if not set."""
    return os.environ.get("ENV", "local")


def get_datasheet_name_map(client_id: int) -> Dict[str, str]:
    from commission_engine.accessors.databook_accessor import DatasheetAccessor

    datasheets_list = DatasheetAccessor(client_id).retrieve_all_datasheets_for_client(
        projections=["datasheet_id", "name"]
    )
    return {str(ds["datasheet_id"]): ds["name"] for ds in datasheets_list}


def get_datasheet_variables_map(
    client_id: int, datasheet_ids: Set[str]
) -> Dict[str, Set[str]]:
    """Get mapping of datasheet IDs to their variables"""
    from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor

    datasheet_vars = {}
    if datasheet_ids:
        all_vars = DatasheetVariableAccessor(client_id).get_variables_for_ds_ids(
            datasheet_ids=list(datasheet_ids),
            as_dicts=True,
            columns=["system_name", "datasheet_id"],
        )
        for var in all_vars:
            ds_id = str(var["datasheet_id"])
            if ds_id not in datasheet_vars:
                datasheet_vars[ds_id] = set()
            datasheet_vars[ds_id].add(var["system_name"])
    return datasheet_vars


def get_used_datasheet_variables(
    client_id: int,
) -> Tuple[Set[str], Dict[str, Dict[str, Any]]]:
    """
    Get all datasheet IDs used in crystal views and their corresponding table settings.
    Returns:
        Tuple of (set of datasheet IDs, dict mapping crystal table ID to table settings)
    """
    crystal_admin = CrystalAdminAccessor(client_id)
    datasheet_ids = set()
    crystal_view_setting_details = {}

    for crystal_view in crystal_admin.get_all_crystal_views():
        for table_settings in crystal_view.settings_data:
            datasheet_id = table_settings.get("source_id")
            used_columns = get_datasheet_variables_used_in_crystal_simulator(
                table_settings
            )
            datasheet_ids.add(datasheet_id)
            crystal_table_id = table_settings.get("crystal_table_id")
            crystal_view_setting_details[crystal_table_id] = {
                "crystal_view_id": str(crystal_view.crystal_view_id),
                "crystal_view_name": crystal_view.crystal_view_name,
                "crystal_table_id": table_settings.get("crystal_table_id"),
                "crystal_table_name": table_settings.get("crystal_table_name"),
                "used_columns": used_columns,
                "datasheet_id": datasheet_id,
            }

    return datasheet_ids, crystal_view_setting_details


def check_missing_ds_variables_in_crystal_for_client(client_id: int) -> List[Dict]:
    """
    Check for missing datasheet variables in crystal views for a specific client.
    Returns list of anomalies found.
    """
    anomalies = []
    datasheet_ids, crystal_view_setting_details = get_used_datasheet_variables(
        client_id
    )
    datasheet_name_map = get_datasheet_name_map(client_id)
    datasheet_vars = get_datasheet_variables_map(client_id, datasheet_ids)

    for crystal_table_id, settings in crystal_view_setting_details.items():
        # Get variables available in datasheet
        available_columns = datasheet_vars.get(settings["datasheet_id"], set())
        # Find missing columns
        missing_columns = settings["used_columns"] - available_columns
        if missing_columns:
            anomalies.append(
                {
                    "client_id": client_id,
                    "crystal_view_id": settings["crystal_view_id"],
                    "crystal_view_name": settings["crystal_view_name"],
                    "crystal_table_id": crystal_table_id,
                    "crystal_table_name": settings["crystal_table_name"],
                    "datasheet_id": settings["datasheet_id"],
                    "datasheet_name": datasheet_name_map.get(
                        settings["datasheet_id"], "Unknown"
                    ),
                    "missing_columns": list(missing_columns),
                }
            )

    return anomalies


@shared_task(base=EverCeleryBaseTask)
def check_missing_ds_variables_in_crystal(client_id, client_name):
    try:
        start_time = datetime.now()
        logger.info(
            "Checking Missing Datasheet Variables in Canvas for Client - {}".format(
                client_id
            )
        )
        anomalies = check_missing_ds_variables_in_crystal_for_client(client_id)
        for anomaly in anomalies:
            created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            alert_key_val = (
                f"{client_name}_missing_datasheet_variables_in_crystal_{created_at}"
            )
            metadata_val = json.dumps(
                {
                    "affected_data": {
                        "crystal_view_id": anomaly["crystal_view_id"],
                        "crystal_view_name": anomaly["crystal_view_name"],
                        "crystal_table_id": anomaly["crystal_table_id"],
                        "crystal_table_name": anomaly["crystal_table_name"],
                        "datasheet_id": anomaly["datasheet_id"],
                        "datasheet_name": anomaly["datasheet_name"],
                    },
                    "expected_value": [],
                    "actual_value": list(anomaly["missing_columns"]),
                }
            )
            log_anomaly(
                client_id=client_id,
                client_name=client_name,
                module="Crystal",
                check_type="missing_datasheet_variables_in_crystal",
                alert_name="Missing Datasheet Variables In Crystal",
                severity="High",
                description=f"Missing datasheet variables in Crystal View '{anomaly['crystal_view_name']}' Table '{anomaly['crystal_table_name']}'",
                logger_email_id="<EMAIL>",
                assignee="<EMAIL>",
                cc_email_id="<EMAIL>",
                created_at=created_at,
                alert_key=alert_key_val,
                env=is_get_env(),
                metadata=metadata_val,
            )
        if not anomalies:
            log_heartbeat(
                client_id=client_id,
                client_name=client_name,
                module="Crystal",
                check_type="missing_datasheet_variables_in_crystal",
                env=is_get_env(),
            )
        duration = datetime.now() - start_time
        logger.info(
            f"Completed Checking Missing Datasheet Variables in Crystal for Client - {client_id}. Total execution time: {duration}"
        )
    except Exception:
        logging.exception(
            "Error checking missing datasheet variables in crystal for client {}".format(
                client_id
            )
        )


@shared_task(base=EverCeleryBaseTask)
def check_missing_ds_variables_in_crystal_wrapper():
    from commission_engine.accessors.client_accessor import (
        get_active_clients_features_excluding_churned,
    )

    task_group_name = TaskGroupEnum.ANOMALY_DETECT.value
    clients = get_active_clients_features_excluding_churned()

    tasks = group(
        [
            check_missing_ds_variables_in_crystal.si(client_id, client_name).set(
                queue=get_queue_name_respect_to_task_group(
                    client_id,
                    features.get("subscription_plan", "BASIC"),
                    task_group_name,
                )
            )
            for client_id, client_name, features in clients
        ]
    )

    tasks.apply_async(compression="lzma", serializer="pickle")
