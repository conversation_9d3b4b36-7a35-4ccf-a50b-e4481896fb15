import logging
import warnings

import django

django.setup()

logger = logging.getLogger(__name__)

from snowflake.snowpark.exceptions import SnowparkSQLException

from commission_engine.accessors.custom_object_accessor import CustomObjectAccessor
from commission_engine.accessors.databook_accessor import DatasheetAccessor
from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.database.snowflake_query_utils import (
    datasheet_data_schema,
    get_datasheet_data_table_name,
)
from commission_engine.snowflake_accessors.custom_object_data_accessor import (
    get_custom_object_data_schema,
)
from commission_engine.utils.general_utils import get_custom_object_data_table_name


def _create_table(session, table_name: str, schema: str) -> None:
    """Create a single table with the given schema."""
    create_table_query = f"""
    create table if not exists {table_name} (
        {schema}
    )
    cluster by (knowledge_end_date);
    """
    session.sql(create_table_query).collect()


def _create_custom_object_tables(
    session, source_client_id: int, target_client_id: int, custom_objects: set
) -> list:
    """Create custom object tables and copy data."""
    created_tables = []
    for co_id in custom_objects:
        table_name = get_custom_object_data_table_name(
            client_id=target_client_id, custom_object_id=co_id
        )
        _create_table(session, table_name, get_custom_object_data_schema())
        created_tables.append(table_name)

        if source_client_id is not None:
            source_table_name = get_custom_object_data_table_name(
                client_id=source_client_id, custom_object_id=co_id
            )
            copy_data_query = f"""
            insert into {table_name} (KNOWLEDGE_BEGIN_DATE, KNOWLEDGE_END_DATE, IS_DELETED, ADDITIONAL_DETAILS, CUSTOM_OBJECT_ID, ROW_KEY, DATA, SNAPSHOT_VALUE, CLIENT_ID, SOURCE)
            select KNOWLEDGE_BEGIN_DATE, KNOWLEDGE_END_DATE, IS_DELETED, ADDITIONAL_DETAILS, CUSTOM_OBJECT_ID, ROW_KEY, DATA, SNAPSHOT_VALUE, {target_client_id} as CLIENT_ID, SOURCE
            from {source_table_name}
            where client_id = {source_client_id} and knowledge_end_date is null and is_deleted = false
            """
            try:
                session.sql(copy_data_query).collect()
            except SnowparkSQLException as e:
                if "Object does not exist or not authorized" in str(e):
                    logger.warning(
                        f"Table {source_table_name} does not exist or is not authorized."
                    )
                else:
                    raise
    return created_tables


def _create_datasheet_tables(
    session, target_client_id: int, datasheets: list, ds_var_map: dict
) -> list:
    """Create datasheet tables."""
    created_tables = []
    for ds in datasheets:
        table_name = get_datasheet_data_table_name(
            target_client_id, str(ds_var_map[ds["datasheet_id"]])
        )
        _create_table(session, table_name, datasheet_data_schema())
        created_tables.append(table_name)
    return created_tables


def _cleanup_tables(session, created_tables: list):
    """Clean up created tables in case of error."""
    for table in created_tables:
        logger.info(f"Dropping table {table}")
        try:
            session.sql(f"DROP TABLE IF EXISTS {table}").collect()
        except Exception as drp_err:
            logger.error(f"Error dropping table {table}: {drp_err}")


def create_snowflake_data_tables(
    source_client_id: int, target_client_id: int, ds_var_map: dict, write: bool
):
    """Main function to create all Snowflake tables"""
    # Get all objects that need tables
    custom_object_accessor = CustomObjectAccessor(source_client_id)
    custom_objects = set(custom_object_accessor.get_custom_object_ids())

    datasheet_accessor = DatasheetAccessor(source_client_id)
    datasheets = datasheet_accessor.retrieve_all_datasheets_for_client(
        projections=["datasheet_id"]
    )

    logger.info(
        f"Will create {len(custom_objects)} custom object tables and {len(datasheets)} datasheet tables"
    )

    if not write:
        return

    with create_snowpark_session_wrapper(client_id=target_client_id) as session:
        try:
            created_tables = []

            # Create custom object tables
            custom_tables = _create_custom_object_tables(
                session, source_client_id, target_client_id, custom_objects
            )
            created_tables.extend(custom_tables)

            # Create datasheet tables
            datasheet_tables = _create_datasheet_tables(
                session, target_client_id, datasheets, ds_var_map
            )
            created_tables.extend(datasheet_tables)
            logger.info(f"Successfully created {len(created_tables)} tables")

        except Exception as e:
            # Clean up any created tables
            _cleanup_tables(session, created_tables)
            logger.error(f"Error creating Snowflake tables: {e}")
            logger.error("Full traceback:", exc_info=True)
            raise e
