import json
import logging

import django

django.setup()

logger = logging.getLogger(__name__)

from uuid import uuid4

from commission_engine.models.etl_config_models import (
    AccessTokenConfig,
    ApiAccessConfig,
    EnrichmentConfig,
    ExtractionConfig,
    Integration,
    TransformationConfig,
    TransformationLogic,
)
from commission_engine.models.etl_housekeeping_models import (
    ETLSyncStatus,
    UpstreamETLStatus,
)
from everstage_ddd.upstream.extraction.models import UpstreamTimestamps


def clone_integration_module(source_client_id: int, target_client_id: int, write: bool):
    logger.info(
        f"{'Writing' if write else 'Dry run'}: {source_client_id} → {target_client_id}"
    )
    try:
        # === Step 1: Clone Access Token Configs ===
        source_access_token_configs = list(
            AccessTokenConfig.objects.filter(
                client_id=source_client_id, knowledge_end_date__isnull=True
            )
        )
        new_access_token_configs = []
        access_token_id_map = {}

        for atc in source_access_token_configs:
            new_atc = AccessTokenConfig(
                client_id=target_client_id,
                **{
                    field.name: getattr(atc, field.name)
                    for field in atc._meta.fields
                    if field.name not in ["access_token_config_id", "client_id"]
                },
            )
            new_access_token_configs.append(new_atc)

        if write:
            created_configs = AccessTokenConfig.objects.bulk_create(
                new_access_token_configs, batch_size=100
            )
            # Build ID map after bulk create
            for original, created in zip(source_access_token_configs, created_configs):
                access_token_id_map[original.access_token_config_id] = (
                    created.access_token_config_id
                )
        else:
            # For dry run, just map to None
            for atc in source_access_token_configs:
                access_token_id_map[atc.access_token_config_id] = None

        logger.info(f"Access Token Configs: {len(new_access_token_configs)}")

        # === Step 2: Clone Integrations ===
        source_integrations = list(
            Integration.objects.filter(
                client_id=source_client_id, knowledge_end_date__isnull=True
            )
        )
        new_integrations = []
        integration_id_map = {}

        for integration in source_integrations:
            new_id = uuid4()
            integration_id_map[integration.integration_id] = new_id
            new_integrations.append(
                Integration(
                    integration_id=new_id,
                    client_id=target_client_id,
                    **{
                        field.name: getattr(integration, field.name)
                        for field in integration._meta.fields
                        if field.name not in ["id", "integration_id", "client_id"]
                    },
                )
            )
        if write:
            Integration.objects.bulk_create(new_integrations, batch_size=500)
        logger.info(f"Integrations: {len(new_integrations)}")

        # === Step 3: Clone Transformation Logic ===
        source_tl_qs = list(
            TransformationLogic.objects.filter(
                client_id=source_client_id, knowledge_end_date__isnull=True
            )
        )

        new_tls = []
        transformation_logic_id_map = {}

        for tl in source_tl_qs:
            base_fields = {
                "client_id": target_client_id,
                **{
                    field.name: getattr(tl, field.name)
                    for field in tl._meta.fields
                    if field.name not in ["id", "client_id"]
                },
            }
            new_tl = TransformationLogic(**base_fields)
            new_tls.append(new_tl)

        if write:
            created_tls = TransformationLogic.objects.bulk_create(
                new_tls, batch_size=500
            )

            # Build ID map
            for original, created in zip(source_tl_qs, created_tls):
                transformation_logic_id_map[original.id] = created.id

        logger.info(f"Transformation Logic: {len(new_tls)}")

        # === Step 4: Clone Transformation Configs ===
        source_transformation_configs = list(
            TransformationConfig.objects.filter(
                client_id=source_client_id, knowledge_end_date__isnull=True
            )
        )

        new_transformation_configs = []
        for config in source_transformation_configs:
            original_logic_id = config.transformation_logic_id
            original_integration_id = config.integration_id

            new_logic_id = (
                transformation_logic_id_map.get(original_logic_id)
                if original_logic_id in transformation_logic_id_map
                else None
            )
            new_integration_id = (
                integration_id_map.get(original_integration_id)
                if original_integration_id in integration_id_map
                else None
            )

            base_fields = {
                "client_id": target_client_id,
                **{
                    field.name: getattr(config, field.name)
                    for field in config._meta.fields
                    if field.name
                    not in [
                        "id",
                        "client_id",
                        "transformation_logic_id",
                        "integration_id",
                    ]
                },
                "transformation_logic_id": new_logic_id,
                "integration_id": new_integration_id,
            }

            new_config = TransformationConfig(**base_fields)
            new_transformation_configs.append(new_config)

        if write:
            TransformationConfig.objects.bulk_create(
                new_transformation_configs, batch_size=100
            )
        logger.info(f"Transformation Configs: {len(new_transformation_configs)}")

        # === Step 5: Clone Extraction Configs ===
        source_extraction_configs = list(
            ExtractionConfig.objects.filter(
                client_id=source_client_id,
                knowledge_end_date__isnull=True,
            )
        )

        new_extraction_configs = []
        for config in source_extraction_configs:
            original_integration_id = config.integration_id
            original_access_token_config_id = config.access_token_config_id

            new_integration_id = (
                integration_id_map.get(original_integration_id)
                if original_integration_id in integration_id_map
                else None
            )
            new_access_token_config_id = (
                access_token_id_map.get(original_access_token_config_id)
                if original_access_token_config_id in access_token_id_map
                else None
            )

            base_fields = {
                "client_id": target_client_id,
                **{
                    field.name: getattr(config, field.name)
                    for field in config._meta.fields
                    if field.name
                    not in [
                        "id",
                        "client_id",
                        "integration_id",
                        "access_token_config_id",
                    ]
                },
                "integration_id": new_integration_id,
                "access_token_config_id": new_access_token_config_id,
            }

            new_config = ExtractionConfig(**base_fields)
            new_extraction_configs.append(new_config)

        if write:
            ExtractionConfig.objects.bulk_create(new_extraction_configs, batch_size=100)

        logger.info(f"Extraction Configs: {len(new_extraction_configs)}")

        # === Step 6: Clone API Access Configs ===
        source_api_access_configs = list(
            ApiAccessConfig.objects.filter(
                client_id=source_client_id,
                knowledge_end_date__isnull=True,
            )
        )

        new_api_access_configs = []
        for config in source_api_access_configs:
            original_integration_id = config.integration_id
            original_access_token_config_id = config.access_token_config_id

            new_integration_id = integration_id_map.get(original_integration_id)
            new_access_token_config_id = access_token_id_map.get(
                original_access_token_config_id
            )

            base_fields = {
                "client_id": target_client_id,
                **{
                    field.name: getattr(config, field.name)
                    for field in config._meta.fields
                    if field.name
                    not in [
                        "id",
                        "client_id",
                        "integration_id",
                        "access_token_config_id",
                    ]
                },
                "integration_id": new_integration_id,
                "access_token_config_id": new_access_token_config_id,
            }

            new_config = ApiAccessConfig(**base_fields)
            new_api_access_configs.append(new_config)

        if write:
            ApiAccessConfig.objects.bulk_create(new_api_access_configs, batch_size=100)

        logger.info(f"API Access Configs: {len(new_api_access_configs)}")

        # === Step 7: Clone Enrichment Configs ===
        source_enrichment_configs = list(
            EnrichmentConfig.objects.filter(
                client_id=source_client_id,
                knowledge_end_date__isnull=True,
            )
        )

        new_enrichment_configs = []
        for config in source_enrichment_configs:
            original_integration_id = config.integration_id
            new_integration_id = integration_id_map.get(original_integration_id)

            base_fields = {
                "client_id": target_client_id,
                **{
                    field.name: getattr(config, field.name)
                    for field in config._meta.fields
                    if field.name
                    not in [
                        "id",
                        "client_id",
                        "integration_id",
                    ]
                },
                "integration_id": new_integration_id,
            }

            new_config = EnrichmentConfig(**base_fields)
            new_enrichment_configs.append(new_config)

        if write:
            EnrichmentConfig.objects.bulk_create(new_enrichment_configs, batch_size=100)

        logger.info(f"Enrichment Configs: {len(new_enrichment_configs)}")

        # === Step 8: Clone ETL Sync Status for Upstream Timestamps ===
        old_e2e_sync_run_ids = list(
            UpstreamTimestamps.objects.filter(
                client_id=source_client_id,
            ).values_list("last_extracted_by", flat=True)
        )

        source_etl_sync_statuses = list(
            ETLSyncStatus.objects.filter(
                client_id=source_client_id,
                e2e_sync_run_id__in=old_e2e_sync_run_ids,
            )
        )

        etl_sync_ids = [s.e2e_sync_run_id for s in source_etl_sync_statuses]

        # Create mapping for e2e_sync_run_ids
        old_to_new_e2e_sync_run_id_map = {
            str(s.e2e_sync_run_id): uuid4() for s in source_etl_sync_statuses
        }

        new_etl_sync_statuses = []
        for s in source_etl_sync_statuses:
            base_fields = {
                "e2e_sync_run_id": old_to_new_e2e_sync_run_id_map[
                    str(s.e2e_sync_run_id)
                ],
                "client_id": target_client_id,
                **{
                    field.name: getattr(s, field.name)
                    for field in s._meta.fields
                    if field.name not in ["id", "e2e_sync_run_id", "client_id"]
                },
            }
            if s.params and "integration_ids" in s.params:
                params_str = json.dumps(s.params)

                # Replace all integration IDs
                for old_id, new_id in integration_id_map.items():
                    params_str = params_str.replace(str(old_id), str(new_id))

                # Convert back to JSON and update
                base_fields["params"] = json.loads(params_str)
            new_etl_sync_statuses.append(ETLSyncStatus(**base_fields))
        if write:
            ETLSyncStatus.objects.bulk_create(new_etl_sync_statuses, batch_size=100)
        logger.info(f"ETL Sync Statuses: {len(new_etl_sync_statuses)}")

        # === Step 9: Clone Upstream ETL Status ===
        source_upstream_etl_statuses = list(
            UpstreamETLStatus.objects.filter(
                client_id=source_client_id,
                e2e_sync_run_id__in=etl_sync_ids,
            )
        )

        old_to_new_sync_run_id_map = {
            s.sync_run_id: uuid4() for s in source_upstream_etl_statuses
        }

        new_upstream_etl_statuses = [
            UpstreamETLStatus(
                client_id=target_client_id,
                e2e_sync_run_id=old_to_new_e2e_sync_run_id_map[str(s.e2e_sync_run_id)],
                sync_run_id=old_to_new_sync_run_id_map[s.sync_run_id],
                integration_id=integration_id_map.get(s.integration_id),
                **{
                    field.name: getattr(s, field.name)
                    for field in s._meta.fields
                    if field.name
                    not in [
                        "id",
                        "client_id",
                        "e2e_sync_run_id",
                        "sync_run_id",
                        "integration_id",
                    ]
                },
            )
            for s in source_upstream_etl_statuses
        ]

        if write:
            UpstreamETLStatus.objects.bulk_create(
                new_upstream_etl_statuses, batch_size=100
            )
        logger.info(f"Upstream ETL Statuses: {len(new_upstream_etl_statuses)}")

        # === Step 10: Clone Upstream Timestamps ===
        source_upstream_timestamps = list(
            UpstreamTimestamps.objects.filter(
                client_id=source_client_id,
                last_extracted_by__in=etl_sync_ids,
            )
        )

        new_upstream_timestamps = [
            UpstreamTimestamps(
                client_id=target_client_id,
                integration_id=integration_id_map.get(s.integration_id),
                last_extracted_by=old_to_new_e2e_sync_run_id_map[
                    str(s.last_extracted_by)
                ],
                **{
                    field.name: getattr(s, field.name)
                    for field in s._meta.fields
                    if field.name
                    not in [
                        "id",
                        "client_id",
                        "integration_id",
                        "last_extracted_by",
                    ]
                },
            )
            for s in source_upstream_timestamps
        ]

        if write:
            UpstreamTimestamps.objects.bulk_create(
                new_upstream_timestamps, batch_size=100
            )
        logger.info(f"Upstream Timestamps: {len(new_upstream_timestamps)}")

    except Exception as e:
        logger.error(f"Error in clone_integration_module: {e}")
        logger.error("Full traceback:", exc_info=True)
        raise e
