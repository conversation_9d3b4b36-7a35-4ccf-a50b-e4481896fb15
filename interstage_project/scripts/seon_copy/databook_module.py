import json
import logging
import uuid

import django

django.setup()


logger = logging.getLogger(__name__)
from commission_engine.accessors.client_accessor import get_show_data_sources_v2
from commission_engine.models.databook_models import (
    Databook,
    Datasheet,
    DatasheetVariable,
)
from everstage_ddd.datasheet.models import (
    DatasheetAdjustments,
    DatasheetView,
    DatasheetViewFilter,
)


def clone_databook_module(source_client_id: int, target_client_id: int, write: bool):
    try:
        logger.info(
            f"Cloning from source_client_id={source_client_id} to target_client_id={target_client_id}"
        )
        if not write:
            logger.info("Dry-run mode: No data will be written to the database.")
        # === Step 1: Clone Databooks ===
        source_databooks = list(
            Databook.objects.filter(
                client_id=source_client_id,
                is_deleted=False,
                knowledge_end_date__isnull=True,
            )
        )

        old_to_new_databook_id_map = {}
        old_db_id_to_datasheet_order_map = {}

        for db in source_databooks:
            old_to_new_databook_id_map[db.databook_id] = uuid.uuid4()
            old_db_id_to_datasheet_order_map[db.databook_id] = (
                db.datasheet_order.copy() if db.datasheet_order else []
            )

        new_databooks = [
            Databook(
                temporal_id=None,
                databook_id=old_to_new_databook_id_map[db.databook_id],
                client_id=target_client_id,
                datasheet_order=[],
                **{
                    field.name: getattr(db, field.name)
                    for field in db._meta.fields
                    if field.name
                    not in [
                        "temporal_id",
                        "databook_id",
                        "client_id",
                        "datasheet_order",
                    ]
                },
            )
            for db in source_databooks
        ]

        if write:
            Databook.objects.bulk_create(new_databooks, batch_size=100)
        logger.info(
            f"{'Copied' if write else 'Will copy'} {len(new_databooks)} databooks"
        )

        # === Step 2: Clone Datasheets ===
        # Get valid databook IDs first
        valid_databook_ids = {db.databook_id for db in source_databooks}

        source_datasheets = list(
            Datasheet.objects.filter(
                client_id=source_client_id,
                is_deleted=False,
                knowledge_end_date__isnull=True,
                databook_id__in=valid_databook_ids,  # Only get datasheets with valid databook IDs
            )
        )

        old_to_new_datasheet_id_map = {}
        old_to_new_str_datasheet_id_map = {}
        for ds in source_datasheets:
            new_id = uuid.uuid4()
            old_to_new_datasheet_id_map[ds.datasheet_id] = new_id
            old_to_new_str_datasheet_id_map[str(ds.datasheet_id)] = new_id

        old_datasheet_id_to_transformation_spec = {}
        new_datasheets = []
        for ds in source_datasheets:
            old_datasheet_id_to_transformation_spec[ds.datasheet_id] = (
                ds.transformation_spec
            )

            base_fields = {
                "temporal_id": None,
                "datasheet_id": old_to_new_datasheet_id_map[ds.datasheet_id],
                "client_id": target_client_id,
                "databook_id": old_to_new_databook_id_map[ds.databook_id],
                "is_pk_modified": True,
                **{
                    field.name: getattr(ds, field.name)
                    for field in ds._meta.fields
                    if field.name
                    not in [
                        "temporal_id",
                        "datasheet_id",
                        "client_id",
                        "databook_id",
                        "is_pk_modified",
                    ]
                },
            }
            new_datasheets.append(Datasheet(**base_fields))

        # Update source IDs separately
        for ds, new_ds in zip(source_datasheets, new_datasheets):
            if ds.source_type == "datasheet":
                if ds.source_id:
                    new_ds.source_id = old_to_new_str_datasheet_id_map.get(
                        str(ds.source_id)
                    )
                if ds.source_databook_id:
                    new_ds.source_databook_id = old_to_new_databook_id_map.get(
                        ds.source_databook_id
                    )
            if ds.source_type == "report" and ds.source_id == "user":
                # Update ordered_columns if they exist
                if ds.ordered_columns:
                    new_ds.ordered_columns = [
                        col.replace(
                            f"cf_{source_client_id}_", f"cf_{target_client_id}_"
                        )
                        for col in ds.ordered_columns
                    ]

                # Update primary_key if it exists
                if ds.primary_key:
                    new_ds.primary_key = [
                        key.replace(
                            f"cf_{source_client_id}_", f"cf_{target_client_id}_"
                        )
                        for key in ds.primary_key
                    ]

                logger.debug(f"Updated user report datasheet {new_ds.datasheet_id}")
                logger.debug(f"Ordered columns: {new_ds.ordered_columns}")
                logger.debug(f"Primary key: {new_ds.primary_key}")

        # === Step 3: Update datasheet_order in Databooks ===
        if write:
            databooks_to_update = []
            for old_db_id, datasheet_order in old_db_id_to_datasheet_order_map.items():
                new_db_id = old_to_new_databook_id_map[old_db_id]
                try:
                    new_db = Databook.objects.get(
                        databook_id=new_db_id,
                        client_id=target_client_id,
                        is_deleted=False,
                        knowledge_end_date__isnull=True,
                    )
                except Databook.DoesNotExist:
                    logger.warning(f"Skipped missing databook {new_db_id}")
                    continue

                new_db.datasheet_order = [
                    str(
                        old_to_new_datasheet_id_map.get(
                            uuid.UUID(ds_id), uuid.UUID(ds_id)
                        )
                    )
                    for ds_id in datasheet_order
                ]
                databooks_to_update.append(new_db)
            Databook.objects.bulk_update(databooks_to_update, ["datasheet_order"])
            logger.info(
                f"{'Updated' if write else 'Will update'} datasheet_order for {len(databooks_to_update)} databooks"
            )

        # === Step 6: Clone DatasheetVariable ===
        source_variables = list(
            DatasheetVariable.objects.filter(
                client_id=source_client_id,
                is_deleted=False,
                knowledge_end_date__isnull=True,
                databook_id__in=valid_databook_ids,
            )
        )

        # Ensure old_to_new_variable_id_map is defined
        old_to_new_variable_id_map = {}
        old_to_new_str_variable_id_map = {}
        for v in source_variables:
            new_id = uuid.uuid4()
            old_to_new_variable_id_map[v.variable_id] = new_id
            old_to_new_str_variable_id_map[str(v.variable_id)] = new_id

        new_variables = []
        for v in source_variables:
            base_fields = {
                "temporal_id": None,
                "variable_id": old_to_new_variable_id_map[v.variable_id],
                "client_id": target_client_id,
                "databook_id": old_to_new_databook_id_map[v.databook_id],
                "datasheet_id": old_to_new_datasheet_id_map[v.datasheet_id],
                **{
                    field.name: getattr(v, field.name)
                    for field in v._meta.fields
                    if field.name
                    not in [
                        "temporal_id",
                        "datasheet_id",
                        "client_id",
                        "databook_id",
                        "variable_id",
                    ]
                },
            }

            # Replace client_id in system_name if it exists
            if base_fields.get("system_name"):
                base_fields["system_name"] = base_fields["system_name"].replace(
                    f"cf_{source_client_id}_", f"cf_{target_client_id}_"
                )

            if v.source_type == "datasheet" and v.source_id:
                base_fields["source_id"] = old_to_new_str_datasheet_id_map.get(
                    v.source_id
                )

            if v.source_type == "report" and v.source_id == "user":
                base_fields["source_variable_id"] = base_fields[
                    "source_variable_id"
                ].replace(f"cf_{source_client_id}_", f"cf_{target_client_id}_")

            new_variables.append(DatasheetVariable(**base_fields))

        # === Step 8: Clone DatasheetViewFilter ===
        source_view_filters = list(
            DatasheetViewFilter.objects.filter(
                client_id=source_client_id,
                is_deleted=False,
                knowledge_end_date__isnull=True,
            )
        )

        old_to_new_view_filter_id_map = {
            vf.filter_id: uuid.uuid4() for vf in source_view_filters
        }
        new_view_filters = []
        for vf in source_view_filters:
            base_fields = {
                "temporal_id": None,
                "filter_id": old_to_new_view_filter_id_map[vf.filter_id],
                "client_id": target_client_id,
                **{
                    field.name: getattr(vf, field.name)
                    for field in vf._meta.fields
                    if field.name not in ["temporal_id", "filter_id", "client_id"]
                },
            }
            new_view_filters.append(DatasheetViewFilter(**base_fields))
        # === Step 7: Update filter_list with new UUIDs ===
        filters_to_update = []

        for f, new_f in zip(source_view_filters, new_view_filters):
            filter_data = f.filter_data
            if filter_data:
                # Convert to string for replacements
                filter_data_str = json.dumps(filter_data)

                # Replace all UUIDs
                for old_id, new_id in old_to_new_datasheet_id_map.items():
                    filter_data_str = filter_data_str.replace(str(old_id), str(new_id))
                for old_id, new_id in old_to_new_variable_id_map.items():
                    filter_data_str = filter_data_str.replace(str(old_id), str(new_id))
                for old_id, new_id in old_to_new_databook_id_map.items():
                    filter_data_str = filter_data_str.replace(str(old_id), str(new_id))
                for old_id, new_id in old_to_new_view_filter_id_map.items():
                    filter_data_str = filter_data_str.replace(str(old_id), str(new_id))

                filter_data_str = filter_data_str.replace(
                    f"cf_{source_client_id}_", f"cf_{target_client_id}_"
                )
                # Replace client_id
                filter_data_str = filter_data_str.replace(
                    f'"client_id": {source_client_id}',
                    f'"client_id": {target_client_id}',
                )

                # Convert back to JSON and update
                new_f.filter_data = json.loads(filter_data_str)
                filters_to_update.append(new_f)

        if write:
            DatasheetViewFilter.objects.bulk_create(filters_to_update, batch_size=100)
        logger.info(
            f"{'Updated' if write else 'Will update'} filter_data for {len(filters_to_update)} filters"
        )

        # === Step 9: Clone DatasheetView ===
        # Get valid datasheet IDs
        valid_datasheet_ids = {ds.datasheet_id for ds in source_datasheets}

        source_views = list(
            DatasheetView.objects.filter(
                client_id=source_client_id,
                is_deleted=False,
                knowledge_end_date__isnull=True,
                datasheet_id__in=valid_datasheet_ids,  # Only get views with valid datasheet IDs
            )
        )

        new_views = [
            DatasheetView(
                temporal_id=None,
                view_id=uuid.uuid4(),
                client_id=target_client_id,
                datasheet_id=old_to_new_datasheet_id_map[view.datasheet_id],
                filter_id=old_to_new_view_filter_id_map[view.filter_id],
                **{
                    field.name: getattr(view, field.name)
                    for field in view._meta.fields
                    if field.name
                    not in [
                        "temporal_id",
                        "view_id",
                        "client_id",
                        "datasheet_id",
                        "filter_id",
                    ]
                },
            )
            for view in source_views
        ]

        if write:
            DatasheetView.objects.bulk_create(new_views, batch_size=100)
        logger.info(
            f"{'Copied' if write else 'Will copy'} {len(new_views)} datasheet views"
        )

        # === Step 10: Clone Datasheet Adjustment Table ===
        source_adjustment_tables = list(
            DatasheetAdjustments.objects.filter(
                client_id=source_client_id,
                is_deleted=False,
                knowledge_end_date__isnull=True,
                databook_id__in=valid_databook_ids,
                datasheet_id__in=valid_datasheet_ids,
            )
        )

        old_to_new_adjustment_id_map = {
            a.adjustment_id: uuid.uuid4() for a in source_adjustment_tables
        }

        new_adjustment_tables = [
            DatasheetAdjustments(
                client_id=target_client_id,
                adjustment_id=old_to_new_adjustment_id_map[adjustment.adjustment_id],
                databook_id=old_to_new_databook_id_map[adjustment.databook_id],
                datasheet_id=old_to_new_datasheet_id_map[adjustment.datasheet_id],
                **{
                    field.name: getattr(adjustment, field.name)
                    for field in adjustment._meta.fields
                    if field.name
                    not in [
                        "temporal_id",
                        "client_id",
                        "adjustment_id",
                        "databook_id",
                        "datasheet_id",
                    ]
                },
            )
            for adjustment in source_adjustment_tables
        ]

        if write:
            DatasheetAdjustments.objects.bulk_create(
                new_adjustment_tables, batch_size=100
            )
        logger.info(
            f"{'Copied' if write else 'Will copy'} {len(new_adjustment_tables)} datasheet adjustments"
        )

        # === Step 11: Update transformation_spec JSON in Datasheets ===
        for ds in new_datasheets:
            if ds.transformation_spec:
                # Iterate over each item in the transformation_spec list
                updated_spec = []
                for item in ds.transformation_spec:
                    # Convert item to string
                    item_str = json.dumps(item)
                    # Replace UUIDs
                    for old_id, new_id in old_to_new_datasheet_id_map.items():
                        item_str = item_str.replace(str(old_id), str(new_id))
                    for old_id, new_id in old_to_new_variable_id_map.items():
                        item_str = item_str.replace(str(old_id), str(new_id))
                    for old_id, new_id in old_to_new_databook_id_map.items():
                        item_str = item_str.replace(str(old_id), str(new_id))
                    # Replace client_id in system_name patterns
                    item_str = item_str.replace(
                        f"cf_{source_client_id}_", f"cf_{target_client_id}_"
                    )

                    # Replace client_id
                    item_str = item_str.replace(
                        f'"client_id": {source_client_id}',
                        f'"client_id": {target_client_id}',
                    )

                    # Convert string back to JSON object
                    updated_item = json.loads(item_str)
                    # Append the updated item to the new list
                    updated_spec.append(updated_item)

                # Update the transformation_spec with the modified list
                ds.transformation_spec = updated_spec

            # If show data source v2 is enabled, it is datashhet v2
            # If show data source v2 is disabled, it is datashhet v1
            prev_version = "v1" if get_show_data_sources_v2(target_client_id) else "v2"
            if (
                ds.additional_details
                and f"transformation_spec_{prev_version}" in ds.additional_details
            ):
                # Convert JSON object to string for replacements
                transformation_spec_str = json.dumps(
                    ds.additional_details[f"transformation_spec_{prev_version}"]
                )

                # Replace UUIDs
                for old_id, new_id in old_to_new_datasheet_id_map.items():
                    transformation_spec_str = transformation_spec_str.replace(
                        str(old_id), str(new_id)
                    )
                for old_id, new_id in old_to_new_variable_id_map.items():
                    transformation_spec_str = transformation_spec_str.replace(
                        str(old_id), str(new_id)
                    )
                for old_id, new_id in old_to_new_databook_id_map.items():
                    transformation_spec_str = transformation_spec_str.replace(
                        str(old_id), str(new_id)
                    )

                # Replace client_id in system_name patterns
                transformation_spec_str = transformation_spec_str.replace(
                    f"cf_{source_client_id}_", f"cf_{target_client_id}_"
                )

                transformation_spec_str = transformation_spec_str.replace(
                    f'"client_id": {source_client_id}',
                    f'"client_id": {target_client_id}',
                )

                # Convert string back to JSON object
                ds.additional_details[f"transformation_spec_{prev_version}"] = (
                    json.loads(transformation_spec_str)
                )

        # Step 12: Update Meta Data for Datasheets
        for ds_variable in new_variables:
            if ds_variable.meta_data:

                # Convert JSON object to string for replacements
                meta_data_str = json.dumps(ds_variable.meta_data)

                # Replace UUIDs and client_id
                for old_id, new_id in old_to_new_datasheet_id_map.items():
                    meta_data_str = meta_data_str.replace(str(old_id), str(new_id))
                for old_id, new_id in old_to_new_variable_id_map.items():
                    meta_data_str = meta_data_str.replace(str(old_id), str(new_id))
                for old_id, new_id in old_to_new_databook_id_map.items():
                    meta_data_str = meta_data_str.replace(str(old_id), str(new_id))

                meta_data_str = meta_data_str.replace(
                    f"cf_{source_client_id}_", f"cf_{target_client_id}_"
                )

                # Replace client_id
                meta_data_str = meta_data_str.replace(
                    f'"client_id": {source_client_id}',
                    f'"client_id": {target_client_id}',
                )

                # Convert string back to JSON object
                ds_variable.meta_data = json.loads(meta_data_str)

        # === Step 13: Update source variable IDs separately
        for v, new_v in zip(source_variables, new_variables):
            if v.source_type == "datasheet" and v.source_variable_id:
                try:
                    if "__ss__" in v.source_variable_id:
                        parts = v.source_variable_id.split("__ss__")
                        base_id = parts[0] if parts[0] else None
                        suffix = f"__ss__{parts[1]}" if len(parts) > 1 else ""

                        if base_id:
                            new_id = old_to_new_str_variable_id_map.get(base_id)
                            if new_id:
                                new_v.source_variable_id = f"{str(new_id)}{suffix}"
                        else:
                            new_v.source_variable_id = suffix
                    else:
                        new_v.source_variable_id = old_to_new_str_variable_id_map.get(
                            v.source_variable_id
                        )
                except ValueError as e:
                    logger.error(
                        f"Error processing source_variable_id: {v.source_variable_id}, Error: {e}"
                    )
                    continue

        # === Step 14: Perform the bulk update for transformation_spec
        if write:
            Datasheet.objects.bulk_create(new_datasheets, batch_size=100)
        logger.info(
            f"{'Updated' if write else 'Will update'} {len(new_datasheets)} datasheets"
        )

        # === Step 15: After updating meta_data for all variables
        if write:
            DatasheetVariable.objects.bulk_create(new_variables, batch_size=100)
        logger.info(
            f"{'Created' if write else 'Will create '}{len(new_variables)} datasheet variables"
        )

        return old_to_new_datasheet_id_map
    except Exception as e:
        logger.error(f"Error in clone_databook_module: {e}")
        logger.error("Full traceback:", exc_info=True)
        raise e
