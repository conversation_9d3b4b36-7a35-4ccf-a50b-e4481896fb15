import argparse
import logging
import traceback

import django
from django.db import transaction

django.setup()
from django.db.models import Q

from commission_engine.accessors.client_accessor import (
    get_client_features,
    save_client_features,
)
from commission_engine.models.custom_object_models import (
    CustomObject,
    CustomObjectVariable,
)
from scripts.seon_copy import databook_module, integration_module, snowflake_tables
from spm.models.custom_field_models import CustomFields
from spm.models.quota_models import Quota

logger = logging.getLogger(__name__)


def models_to_copy():
    # The Tables that need to be copied and have client_id field alone to be changed
    quota_models = [Quota]
    integration_models = [CustomObject, CustomObjectVariable]
    custom_field_models = [CustomFields]

    return [*quota_models, *integration_models, *custom_field_models]


def copy_tables(source_client_id: int, target_client_id: int, write: bool):
    try:
        if write:
            save_client_features(
                target_client_id, get_client_features(source_client_id)
            )
            logger.info(
                f"[Write] Copied client_features from client_id={source_client_id} to client_id={target_client_id}"
            )
        else:
            logger.info(
                f"[Dry-run] Would copy client_features from client_id={source_client_id} to client_id={target_client_id}"
            )

        # Copy other tables
        for model in models_to_copy():
            if not hasattr(model, "client_id"):
                continue

            query = Q(client_id=source_client_id)
            if hasattr(model, "is_deleted"):
                query &= Q(is_deleted=False)
            if hasattr(model, "knowledge_end_date"):
                query &= Q(knowledge_end_date__isnull=True)

            source_objects = model.objects.filter(query)
            logger.info(
                f"\nModel: {model.__name__} | Records found: {source_objects.count()}"
            )

            if not write:
                logger.info(
                    f"[Dry-run] Would copy {source_objects.count()} records to client_id={target_client_id}"
                )
                continue

            new_instances = []
            for obj in source_objects:
                obj.pk = None
                obj.client_id = target_client_id

                # handling CustomFields system_name
                if isinstance(obj, CustomFields):
                    obj.system_name = obj.system_name.replace(
                        f"cf_{source_client_id}_", f"cf_{target_client_id}_"
                    )
                    logger.debug(f"Updated CustomField system_name: {obj.system_name}")
                new_instances.append(obj)

            model.objects.bulk_create(new_instances)
            logger.info(
                f"[Write] Copied {len(new_instances)} records to client_id={target_client_id}"
            )

    except Exception as e:
        logger.error(f"Error in copy_tables: {e}")
        logger.error("Full traceback:", exc_info=True)
        raise


def main(source_client_id: int, target_client_id: int, write: bool):
    try:
        # Input validation
        if not isinstance(source_client_id, int):
            raise TypeError(
                f"source_client_id must be an integer, got {type(source_client_id).__name__}"
            )

        if not isinstance(target_client_id, int):
            raise TypeError(
                f"target_client_id must be an integer, got {type(target_client_id).__name__}"
            )

        if source_client_id == target_client_id:
            raise ValueError("Source and target client IDs cannot be the same")

        with transaction.atomic():
            copy_tables(source_client_id, target_client_id, write)
            logger.info("Postgres copy started")
            integration_module.clone_integration_module(
                source_client_id=source_client_id,
                target_client_id=target_client_id,
                write=write,
            )
            old_to_new_datasheet_id_map = databook_module.clone_databook_module(
                source_client_id=source_client_id,
                target_client_id=target_client_id,
                write=write,
            )
            logger.info("Postgres copy completed - but not committed")

            logger.info("Snowflake copy started")
            snowflake_tables.create_snowflake_data_tables(
                source_client_id=source_client_id,
                target_client_id=target_client_id,
                ds_var_map=old_to_new_datasheet_id_map,
                write=write,
            )
            logger.info("Snowflake copy completed")
        logger.info("SCRIPT COMPLETED SUCCESSFULLY")
    except Exception as e:
        logger.error(f"An error occurred: {e}")
        logger.error("Full traceback:", exc_info=True)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Clone datasheet structure between clients."
    )
    parser.add_argument(
        "--source-client-id", required=True, type=int, help="Source client ID"
    )
    parser.add_argument(
        "--target-client-id", required=True, type=int, help="Target client ID"
    )
    parser.add_argument(
        "--write", action="store_true", help="Actually perform the DB writes"
    )

    args = parser.parse_args()

    main(args.source_client_id, args.target_client_id, args.write)
