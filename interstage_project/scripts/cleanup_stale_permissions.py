#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to clean up stale permissions for deleted datasheets.
This script:
1. Gets all active datasheet permissions
2. Checks which datasheets no longer exist
3. Invalidates permissions for deleted datasheets
4. Invalidates associated permission targets

Usage:
    python cleanup_stale_permissions.py [--client-id CLIENT_ID]

If client_id is not provided, it will clean up permissions for all clients.
"""

import argparse
import csv
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

# Add the project root to Python path
sys.path.append(str(Path(__file__).resolve().parent.parent))

import django
from django.utils import timezone

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "everstage.settings")
django.setup()

from commission_engine.accessors.databook_accessor import (
    DatasheetAccessor,
    DatasheetPermissionsAccessor,
    DatasheetPermissionsTargetAccessor,
)
from commission_engine.models import Client

# Constants
BACKUP_DIR = (
    Path(__file__).parent.parent / "data_backup" / "stale_datasheet_permissions"
)


def setup_logging(timestamp: str):
    """Setup logging configuration"""
    # Create backup directory if it doesn't exist
    BACKUP_DIR.mkdir(parents=True, exist_ok=True)

    # Create logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)

    # Create formatters and handlers
    formatter = logging.Formatter("%(asctime)s [%(levelname)s] %(message)s")

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # File handler - convert Path to string for file handler
    log_file = str(BACKUP_DIR / f"stale_permissions_cleanup_{timestamp}.log")
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # Prevent the logger from propagating to the root logger
    logger.propagate = False

    return logger


def backup_stale_permissions(
    client_id: int,
    stale_permissions: list[dict],
    logger: logging.Logger,
    timestamp: str,
) -> Optional[str]:
    """
    Append stale permissions to a CSV file for all clients.
    Args:
        client_id: The client ID
        stale_permissions: List of permission dicts
        logger: Logger instance
        timestamp: Timestamp string to use in the filename
    Returns:
        The path to the backup CSV file, or None if nothing was backed up
    """
    if not stale_permissions:
        logger.info(f"No stale permissions to backup for client {client_id}")
        return None

    # Create backup directory if it doesn't exist
    BACKUP_DIR.mkdir(parents=True, exist_ok=True)

    backup_file = BACKUP_DIR / f"stale_permissions_{timestamp}.csv"

    # Get all fields from the first permission to use as headers
    if stale_permissions:
        fieldnames = ["client_id"] + list(stale_permissions[0].keys())
    else:
        fieldnames = ["client_id"]

    # Check if file exists to determine if we need to write headers
    file_exists = backup_file.exists()

    # Append to CSV
    with open(backup_file, "a", newline="") as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        if not file_exists:
            writer.writeheader()  # Write header only if file doesn't exist
        for perm in stale_permissions:
            row = dict(perm)  # Convert to dict to ensure all fields are included
            row["client_id"] = client_id  # Add client_id to each row
            writer.writerow(row)

    logger.info(f"Appended {len(stale_permissions)} stale permissions to {backup_file}")
    return str(backup_file)


def backup_stale_permission_targets(
    client_id: int, permission_set_ids: list, logger: logging.Logger, timestamp: str
) -> Optional[str]:
    """
    Append permission targets to a CSV file for all clients.
    Args:
        client_id: The client ID
        permission_set_ids: List of permission_set_id values
        logger: Logger instance
        timestamp: Timestamp string to use in the filename
    Returns:
        The path to the backup CSV file, or None if nothing was backed up
    """
    if not permission_set_ids:
        logger.info(f"No permission targets to backup for client {client_id}")
        return None

    datasheet_target_perm = DatasheetPermissionsTargetAccessor(client_id)
    # Fetch all targets for these permission_set_ids using the correct queryset
    targets = (
        datasheet_target_perm.client_latest_kd_aware()
        .filter(permission_set_id__in=permission_set_ids)
        .values(
            "temporal_id",
            "permission_set_id",
            "target_type",
            "target",
        )
    )

    if not targets:
        logger.info(f"No targets found for permission_set_ids: {permission_set_ids}")
        return None

    # Create backup directory if it doesn't exist
    BACKUP_DIR.mkdir(parents=True, exist_ok=True)

    backup_file = BACKUP_DIR / f"stale_permission_targets_{timestamp}.csv"

    # Add client_id to each target
    targets_with_client = []
    for target in targets:
        target_dict = dict(target)
        target_dict["client_id"] = client_id
        targets_with_client.append(target_dict)

    # Check if file exists to determine if we need to write headers
    file_exists = backup_file.exists()

    # Append to CSV
    fieldnames = ["client_id"] + list(targets[0].keys())
    with open(backup_file, "a", newline="") as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        if not file_exists:
            writer.writeheader()  # Write header only if file doesn't exist
        writer.writerows(targets_with_client)

    logger.info(f"Appended {len(targets)} permission targets to {backup_file}")
    return str(backup_file)


def cleanup_stale_permissions(
    client_id: int, logger: logging.Logger, timestamp: str
) -> None:
    """
    Cleans up stale permissions for datasheets that have been deleted.
    Args:
        client_id: The client ID to clean up permissions for
        logger: Logger instance for logging
    """
    logger.info(f"Starting cleanup of stale permissions for client {client_id}")

    # Get accessors
    datasheet_perm = DatasheetPermissionsAccessor(client_id)
    datasheet_target_perm = DatasheetPermissionsTargetAccessor(client_id)
    datasheet_accessor = DatasheetAccessor(client_id)

    # Get all active permissions
    all_permissions = (
        datasheet_perm.client_latest_kd_aware()
        .values(
            "temporal_id",
            "databook_id",
            "datasheet_id",
            "permission_set_id",
        )
        .distinct()
    )

    # Track which permissions need to be invalidated
    permissions_to_invalidate = []
    knowledge_date = timezone.now()

    # Check each permission's datasheet
    for perm in all_permissions:
        databook_id = perm["databook_id"]
        datasheet_id = perm["datasheet_id"]
        # Check if datasheet exists
        if not datasheet_accessor.does_datasheet_exist(datasheet_id):
            logger.info(
                f"Found stale permission for deleted datasheet {datasheet_id} "
                f"in databook {databook_id}"
            )
            permissions_to_invalidate.append(perm)

    if not permissions_to_invalidate:
        logger.info("No stale permissions found")
        return

    # Backup stale permissions
    backup_stale_permissions(client_id, permissions_to_invalidate, logger, timestamp)

    # Backup stale permission targets
    permission_set_ids = [
        perm["permission_set_id"] for perm in permissions_to_invalidate
    ]
    backup_stale_permission_targets(client_id, permission_set_ids, logger, timestamp)

    # Group permissions by datasheet
    invalidation_groups = {}
    for perm in permissions_to_invalidate:
        key = (perm["databook_id"], perm["datasheet_id"])
        if key not in invalidation_groups:
            invalidation_groups[key] = []
        invalidation_groups[key].append(perm["permission_set_id"])

    # Invalidate permissions and targets
    for (databook_id, datasheet_id), permission_sets in invalidation_groups.items():
        try:
            # Invalidate permissions
            datasheet_perm.invalidate_permissions(
                databook_id, datasheet_id, knowledge_date
            )
            # Invalidate permission targets
            datasheet_target_perm.invalidate_permissions(
                permission_sets, knowledge_date
            )
            logger.info(
                f"Successfully invalidated permissions for datasheet {datasheet_id} "
                f"in databook {databook_id}"
            )
        except Exception as e:
            logger.error(
                f"Error invalidating permissions for datasheet {datasheet_id} "
                f"in databook {databook_id}: {str(e)}"
            )

    logger.info(f"Completed cleanup of stale permissions for client {client_id}")


def main():
    """Main function to run the cleanup script"""
    parser = argparse.ArgumentParser(
        description="Clean up stale permissions for deleted datasheets"
    )
    parser.add_argument(
        "--client-id",
        type=int,
        help="Client ID to clean up permissions for. If not provided, cleans up for all clients.",
        required=False,
    )
    args = parser.parse_args()

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    logger = setup_logging(timestamp)
    logger.info("Starting stale permissions cleanup script")

    try:
        if args.client_id:
            # Clean up for specific client
            cleanup_stale_permissions(args.client_id, logger, timestamp)
        else:
            # Clean up for all clients
            client_ids = list(Client.objects.values_list("client_id", flat=True))
            logger.info(f"Found {len(client_ids)} clients to process")

            for client_id in client_ids:
                try:
                    cleanup_stale_permissions(client_id, logger, timestamp)
                except Exception as e:
                    logger.error(f"Error processing client {client_id}: {str(e)}")
                    continue

        logger.info("Completed stale permissions cleanup script")
    except Exception as e:
        logger.error(f"Script failed with error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
