"""
This Script performs the following tasks:
1. Backs up existing permissions for each client.
2. Updates a specific permission record
3. Inserts new permission data for each client.
4. Updates Power Admin role permissions for each client.
"""

import argparse
import json
import os
from datetime import datetime

import django
from django.db.models import F
from django.utils import timezone

django.setup()

from commission_engine.models.client_models import Client
from spm.accessors.rbac_accessors import RolePermissionsAccessor
from spm.models.rbac_models import Permissions, RolePermissions
from spm.services.rbac_services import update_permissions_in_cache


def fetch_existing_permission_ids(client_id: int) -> set:
    """Fetch existing permission IDs for a client."""
    return set(
        Permissions.objects.filter(client_id=client_id).values_list(
            "permission_id", flat=True
        )
    )


def get_queries_component_order(client_id: int) -> int:
    """Get the component order of the 'queries' component."""
    order = (
        Permissions.objects.filter(client_id=client_id, component_system_name="queries")
        .values_list("component_order", flat=True)
        .first()
    )

    if order is None:
        # Fallback to the default order from permission_data.py if queries not found
        return 9
    return order


def get_territory_plans_component_order(client_id: int) -> int:
    """Determine the component order for territory plans by placing it after queries."""
    queries_order = get_queries_component_order(client_id)
    # Territory plans should be placed right after queries
    return queries_order + 1


def backup_permissions_and_roles(clients):
    """Backup Permissions and RolePermissions for all clients."""
    # separate directories for Permissions and RolePermissions
    permissions_dir = (
        f"permissions_backup_{datetime.now().strftime('%Y_%m_%d_%H_%M_%S')}"
    )
    role_permissions_dir = (
        f"role_permissions_backup_{datetime.now().strftime('%Y_%m_%d_%H_%M_%S')}"
    )
    os.makedirs(permissions_dir)
    os.makedirs(role_permissions_dir)
    print(f"Created directories: {permissions_dir}, {role_permissions_dir}")

    for client in clients:
        client_id = client.client_id

        # Backup Permissions table
        permissions_file = os.path.join(
            permissions_dir, f"permissions_{client_id}.json"
        )
        with open(permissions_file, "w") as fp:
            permissions = Permissions.objects.filter(client_id=client_id).values()
            fp.write(json.dumps(list(permissions), default=str))

        # Backup RolePermissions table
        role_permissions_file = os.path.join(
            role_permissions_dir, f"role_permissions_{client_id}.json"
        )
        with open(role_permissions_file, "w") as fp:
            role_permissions = RolePermissions.objects.filter(
                client_id=client_id
            ).values()
            fp.write(json.dumps(list(role_permissions), default=str))

        print(
            f"Backup completed for client {client_id}: Permissions and RolePermissions"
        )


def add_permission_if_not_exists(
    client_id: int,
    permission_data: dict,
    existing_permission_ids: set,
    write: bool,
    counters: dict,
):
    """Insert a new permission if it does not already exist."""
    permission_data["client_id"] = client_id
    if permission_data["permission_id"] not in existing_permission_ids:
        if write:
            Permissions.objects.bulk_create([Permissions(**permission_data)])
            counters["permissions_added"] += 1
        print(
            f"Added {permission_data['permission_id']} permission for client {client_id}"
        )
        return True
    else:
        print(
            f"Permission {permission_data['permission_id']} already exists for client {client_id}"
        )
        return False


def update_power_admin_role_permissions(
    client_id: int, permission_ids: list, write: bool, counters: dict
):
    """Update roles with manage:alladmins permission for a client."""
    # Get roles that have the manage:alladmins permission
    roles = RolePermissionsAccessor(client_id).get_power_admins_roles()

    for role in roles:
        current_permissions = role.permissions or {}
        territory_plans_permission = {
            "territory_plans": {
                "permissions": permission_ids,
                "data_permission": {"type": "ALL_DATA"},
            }
        }

        needs_update = False
        if "territory_plans" not in current_permissions:
            current_permissions.update(territory_plans_permission)
            needs_update = True
        elif not all(
            perm_id in current_permissions["territory_plans"]["permissions"]
            for perm_id in permission_ids
        ):
            for perm_id in permission_ids:
                if perm_id not in current_permissions["territory_plans"]["permissions"]:
                    current_permissions["territory_plans"]["permissions"].append(
                        perm_id
                    )
                    needs_update = True

        if needs_update:
            if write:
                time_now = timezone.now()
                role.permissions = current_permissions
                role.knowledge_begin_date = time_now
                role.save()
                update_cache_for_roles(client_id, [role.role_permission_id])
                counters["roles_updated"] += 1
            print(
                f"Updated role {role.display_name} permissions for client {client_id}"
            )
        else:
            print(
                f"Role {role.display_name} already has required permissions for client {client_id}"
            )


def update_cache_for_roles(client_id: int, role_ids: list):
    """Update the cache for the modified roles."""
    role_objs = RolePermissionsAccessor(client_id).get_role_by_role_permission_ids(
        role_ids, True
    )
    for role in role_objs:
        update_permissions_in_cache(client_id, role["role_permission_id"], role)


def update_component_order(
    client_id: int,
    permission_ids: list,
    territory_component_order: int,
    write: bool,
    counters: dict,
):
    """Update component order for other permissions."""
    if write:
        res = (
            Permissions.objects.filter(
                client_id=client_id, component_order__gte=territory_component_order
            )
            .exclude(permission_id__in=permission_ids)
            .update(component_order=F("component_order") + 1)
        )
        if res > 0:
            counters["component_orders_updated"] += res
            print(
                f"Updated component order for {res} permissions for client {client_id}"
            )


def process_client_permissions(
    client: Client, permission_data_template: list, write: bool, counters: dict
):
    """Process permissions and roles for a single client."""
    client_id = client.client_id
    print(f"\nSTARTING FOR CLIENT {client_id}")
    existing_permission_ids = fetch_existing_permission_ids(client_id)

    # Get the dynamic component order for territory plans (after queries)
    territory_component_order = get_territory_plans_component_order(client_id)
    print(
        f"Determined territory plans component order as {territory_component_order} for client {client_id}"
    )

    # Update the template permissions with the correct component order
    permission_data = []
    for perm in permission_data_template:
        perm_copy = perm.copy()
        perm_copy["component_order"] = territory_component_order
        permission_data.append(perm_copy)

    permission_ids = [p["permission_id"] for p in permission_data]

    permissions_added = False
    for permission in permission_data:
        if add_permission_if_not_exists(
            client_id, permission, existing_permission_ids, write, counters
        ):
            permissions_added = True

    update_power_admin_role_permissions(client_id, permission_ids, write, counters)

    # Only update component order if permissions were added for this client
    if permissions_added:
        update_component_order(
            client_id, permission_ids, territory_component_order, write, counters
        )

    print(f"COMPLETED PROCESSING FOR CLIENT {client_id}")


def update_client_permissions(write: bool):
    """Main function to update permissions for all clients."""
    clients = Client.objects.filter(is_deleted=False)

    if write:
        print("Taking backup for Permissions and RolePermissions tables")
        backup_permissions_and_roles(clients)
        print("Backup completed")

    # Define the new permissions template (without component_order which will be set dynamically)
    permission_data_template = [
        {
            "permission_id": "view:territoryplans",
            "permission_name": "View plans",
            "component_system_name": "territory_plans",
            "component_display_name": "Planning",
            "show_data_permissions": False,
            # component_order will be set dynamically for each client
            "permission_description": "Users can view plans",
            "parent_id": None,
            "show_to_user": True,
        },
        {
            "permission_id": "explore:territoryplans",
            "permission_name": "Explore plans",
            "component_system_name": "territory_plans",
            "component_display_name": "Planning",
            "show_data_permissions": False,
            # component_order will be set dynamically for each client
            "parent_id": "view:territoryplans",
            "permission_description": "Users can explore plans",
            "show_to_user": True,
        },
    ]

    # Initialize counters
    counters = {
        "permissions_added": 0,
        "roles_updated": 0,
        "component_orders_updated": 0,
    }

    for client in clients:
        process_client_permissions(client, permission_data_template, write, counters)

    # Display summary
    print("\n==== SUMMARY ====")
    print(f"Total permissions added: {counters['permissions_added']}")
    print(f"Total roles updated: {counters['roles_updated']}")
    print(f"Total component orders updated: {counters['component_orders_updated']}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Update RBAC permissions for Territory Plans."
    )
    parser.add_argument(
        "--write",
        action="store_true",
        help="Apply changes to the database. Without this flag, the script runs in dry-run mode.",
    )
    args = parser.parse_args()

    print("\n==== BEGIN ====")
    update_client_permissions(write=args.write)
    print("\n==== END ====")
