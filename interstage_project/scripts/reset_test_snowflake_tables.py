import logging
import os
import time
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Tuple

import snowflake.connector

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_connection_params(database: str) -> Dict[str, Any]:
    """Get Snowflake connection parameters."""
    return {
        "user": os.getenv("SNOWFLAKE_PASSWORD"),
        "password": os.getenv("SNOWFLAKE_PASSWORD"),
        "account": os.getenv("SNOWFLAKE_ACCOUNT"),
        "warehouse": "EVERSTAGE_TEST_ETL",
        "database": database,
        "schema": "ACCOUNT_USAGE",
        "role": "ACCOUNTADMIN",
        "CLIENT_SESSION_KEEP_ALIVE": True,
    }


def validate_timestamp(timestamp: datetime) -> datetime:
    """Validate that the timestamp is in the past."""
    if timestamp > datetime.now(timezone.utc):
        raise ValueError("Timestamp cannot be in the future")
    return timestamp


def print_execution_summary(
    start_time: float,
    total_tables: int,
    successful_tables: int,
    failed_tables: int,
    total_tables_for_client: int,
    tables_skipped: int,
) -> None:
    """Print a summary of the execution statistics."""
    end_time = time.time()
    total_time = end_time - start_time

    logger.info("\n=== Execution Summary ===")
    logger.info(f"Total execution time: {total_time:.2f} seconds")
    logger.info(f"Total tables processed: {total_tables}")
    logger.info(f"Successful tables: {successful_tables}")
    logger.info(f"Failed tables: {failed_tables}")
    logger.info(f"Success rate: {(successful_tables/total_tables_for_client)*100:.2f}%")
    logger.info(f"Total tables for client: {total_tables_for_client}")
    logger.info(f"Tables skipped: {tables_skipped}")
    logger.info("======================\n")


def get_client_tables(
    cur: snowflake.connector.cursor.SnowflakeCursor, client_id: str, database: str
) -> List[Tuple[str, str]]:
    """Get all tables for a specific client."""
    cur.execute(
        """
        SELECT table_schema, table_name
        FROM information_schema.tables
        WHERE table_type = 'BASE TABLE'
        AND table_catalog = %s
        AND table_schema != 'INFORMATION_SCHEMA'
        AND table_name LIKE %s;
        """,
        (database, f"%{client_id}%"),
    )
    return cur.fetchall()


def process_time_travel(
    cur: snowflake.connector.cursor.SnowflakeCursor,
    schema: str,
    table: str,
    timestamp: str,
) -> bool:
    """Process time travel for a single table."""
    try:
        # nosec B608 - SQL query is safe as schema and table names are validated
        rollback_sql = f"""
            CREATE OR REPLACE TABLE "{schema}"."{table}" AS
            SELECT * FROM "{schema}"."{table}"
            AT (TIMESTAMP => '{timestamp}');
        """
        logger.info(f"Running time travel for {schema}.{table}")
        cur.execute(rollback_sql)
        return True
    except snowflake.connector.errors.ProgrammingError as e:
        logger.error(f"Error processing table {schema}.{table}: {str(e)}")
        return False


def reset_snowflake_db(test_instance_db_name, reset_clients, reset_timestamp):
    try:
        start_time = time.time()
        total_tables = 0
        successful_tables = 0
        failed_tables = 0
        total_tables_for_client = 0
        tables_skipped = 0

        conn = snowflake.connector.connect(
            **get_connection_params(test_instance_db_name)
        )
        cur = conn.cursor()
        cur.execute(
            "ALTER SESSION SET QUERY_TAG = 'test_time_travel_tag_30_apr_10038';"
        )

        timestamp_str = reset_timestamp.strftime("%Y-%m-%d %H:%M:%S.%f")

        clients_list = reset_clients.split(",")

        for client_id in clients_list:
            logger.info(f"Processing client ID: {client_id}")
            tables = get_client_tables(cur, client_id, test_instance_db_name)
            total_tables += len(tables)

            for schema, table in tables:
                if client_id in table:
                    total_tables_for_client += 1
                    if process_time_travel(cur, schema, table, timestamp_str):
                        successful_tables += 1
                    else:
                        failed_tables += 1
                else:
                    tables_skipped += 1

        print_execution_summary(
            start_time,
            total_tables,
            successful_tables,
            failed_tables,
            total_tables_for_client,
            tables_skipped,
        )

    except Exception as e:
        logger.exception("An error occurred during execution")
        raise
    finally:
        if "cur" in locals():
            cur.close()
        if "conn" in locals():
            conn.close()
