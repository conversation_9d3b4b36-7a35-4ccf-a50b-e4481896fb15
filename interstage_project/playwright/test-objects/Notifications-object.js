import { expect } from "@playwright/test";
// User Page Objects
const usersCount = ".text-xs.text-ever-base-content-mid";
// Notification Page Objects
const notificationPageToggleBtn = "button.ant-switch";
const notificationUserNames =
  ".ag-cell[col-id='fullName']>div>div>span.text-base";
const configureNotificationBtn = ".ant-btn.ant-btn-text";
const userStatusBtn = ".ag-pinned-right-cols-container>div>div>div>button";
const userNameEle = "div[col-id='fullName'] span[title].text-base";
const filterBtn = ".ant-btn-dashed[type='button']";
const filterComboBox = "div>.ant-select-selection-search-input";
// Configure Notifications Page
const notificationPreferencesEle =
  ".ant-drawer-body>div>div>div.flex>div>div.ant-row";
const configureNotificationscloseBtn = "button.ant-drawer-close";
const totalRowsSelector =
  'div[data-testid="pt-row-count"] > span:nth-of-type(5)';

class Notification {
  constructor(page) {
    this.page = page;
  }

  /**
   *
   * @function : To Check whether User List is found in the Notification Page
   * @returns True if User List is found in the Webpage , False Otherwise
   */
  async userList() {
    await this.page.waitForSelector(userNameEle);
    return await this.page.isVisible(userNameEle);
  }

  /**
   *
   * @function : To check whether the user should see all the 23 Notifications along with "set preference" CTA
   * @returns True if  user is able to see all the 23 Notifications along with "set preference" CTA , False Otherwise
   */
  async validateNotificationPreferencesCount() {
    await this.page.locator(configureNotificationBtn).click();
    const notificationPreferences = await this.page
      .locator(notificationPreferencesEle)
      .all();
    let status = true;
    if (!notificationPreferences.length === 23) status = false;
    await this.page.locator(configureNotificationscloseBtn).click();
    return status;
  }

  /**
   *
   * @function : Creating a New role in Settings/Roles Page
   * @param {String} roleName
   */
  async createRole(roleName) {
    await this.page.getByText("New Role").click();
    await this.page.getByPlaceholder("Enter a role name").fill(roleName);
    await this.page
      .getByPlaceholder("Add optional description")
      .fill("Playwright Desc");
    await this.page.getByRole("button", { name: "Create Role" }).click();
    await this.page.getByText("Dashboard", { exact: true }).click();
    await this.page.getByLabel("View dashboards page").check();
    await this.page.getByText("Databooks", { exact: true }).click();
    await this.page.getByLabel("View databooks page").check();
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  /**
   *
   * @function : Deleting a Role in Setting/Roles Page
   * @param {String} roleName
   */
  async deleteRole(roleName) {
    await this.page.getByText(roleName).click();
    await this.page.locator(".ant-space-item>svg").click();
    await this.page.getByText("Delete", { exact: true }).click();
    await this.page.getByText("Yes, Confirm").click();
  }

  /**
   *
   * @function : Turn ON/OFF Enable User Notification toggle in Notifications page
   * @param {String} enable
   */
  async toggleNotification(enable) {
    await this.page.waitForSelector(notificationPageToggleBtn);
    const toggleButtons = await this.page
      .locator(notificationPageToggleBtn)
      .all();
    if (enable == true) {
      const ariaChecked = await toggleButtons[1].getAttribute("aria-checked");
      if (ariaChecked === "false") {
        await toggleButtons[1].click();
        await this.page
          .getByText("Notifications enabled successfully")
          .waitFor({ state: "visible" });
      }
    } else {
      const ariaChecked = await toggleButtons[1].getAttribute("aria-checked");
      if (ariaChecked === "true") {
        await toggleButtons[1].click();
        await this.page
          .getByText("Notifications disabled successfully")
          .waitFor({ state: "visible" });
      }
    }
  }

  /**
   *
   * @function : To get Sorted User Names from User Page
   * @returns {Array} Sorted User Names from User Page
   */
  async getUserNames() {
    let ActiveUserNames = [];
    await this.page.locator('//div[@data-testid="ever-select"]').first().click();
    await this.page.locator('//div[@title="100"]').click();

    // Wait for total rows selector and get the count
    await this.page.waitForSelector(totalRowsSelector);
    const totalRowsText = await this.page
      .locator(totalRowsSelector)
      .textContent();
    const totalRows = parseInt(totalRowsText, 10);
    console.log(
      "totalRows in users page by 100 users per page view",
      totalRows
    );
    // Wait for user count elements to load
    await this.page.waitForSelector(usersCount);

    // Iterate over the number of rows
    for (let i = 0; i < totalRows; i++) {
      const element = this.page.locator(
        `//div[@row-index="${i}"]//div[@col-id="fullName"]//span[contains(@class, "text-xs")]`
      );
      // Wait for the element to be visible before accessing it
      await element.waitFor();
      const text = await element.innerText();
      // Get the status for the corresponding row
      const status = await this.page
        .locator(
          `[row-id='${text}']>div[col-id='status']>div>span>div>div>span`
        )
        .innerText();
      if (status !== "Inactive") {
        // Get the username for active users
        const userName = await this.page
          .locator(
            `//div[@row-index="${i}"]//div[@col-id="fullName"]//span[contains(@class, "text-base")]`
          )
          .innerText();
        ActiveUserNames.push(userName);
      }
      // Scroll down by 50 pixels after processing each row
      await this.page.evaluate(() => {
        const container = document.querySelector(".ag-body-viewport");
        if (container) {
          container.scrollTop += 50; // Scroll down by 50 pixels
        }
      });
      await this.page.waitForTimeout(100);
    }
    return ActiveUserNames.sort();
  }

  /**
   *
   * @function : To get user names available in the Notificaiton Page
   * @returns {Array} userName in Notification Page
   */
  async getNotificationUserNames() {
    let notificationUserNames = [];
    await this.page
      .locator(".ag-header-cell[col-id='checkbox']>div>label>span>input")
      .check({ timeout: 5000 });
    const totalRowsText = await this.page
      .locator(
        "(//button[.//span[text()='Turn on notifications']]/ancestor::div[1]/preceding-sibling::div//span)[3]"
      )
      .textContent();
    const totalRows = parseInt(totalRowsText, 10);
    console.log("totalRows in notifications page", totalRows);
    // Iterate over the number of rows
    for (let i = 0; i < totalRows; i++) {
      const element = this.page.locator(
        `//div[@row-index="${i}"]//div[@col-id="fullName"]//div[2]/span[1]`
      );
      // Wait for the element to be visible before accessing it
      await element.waitFor();
      const userName = await element.innerText();
      notificationUserNames.push(userName);
      // Scroll down by 50 pixels after processing each row
      await this.page.evaluate(() => {
        const container = document.querySelector(".ag-body-viewport");
        if (container) {
          container.scrollTop += 50; // Scroll down by 50 pixels
        }
      });
      await this.page.waitForTimeout(100);
    }
    return notificationUserNames;
  }

  /**
   *
   * @function : To check whether Buttons are disabled when Enable User Notification is OFF
   * @returns True if button is enabled , False otherwise
   */
  async getButtonStatus(flag) {
    await this.page.waitForTimeout(2000);
    if (flag)
      await expect(
        await this.page.locator(configureNotificationBtn)
      ).toBeEnabled();
    else
      await expect(
        await this.page.locator(configureNotificationBtn)
      ).toBeDisabled();
    for (const userstatus of await this.page.locator(userStatusBtn).all()) {
      if (flag) await expect(userstatus).toBeEnabled();
      else await expect(userstatus).toBeDisabled();
    }
    if (flag) return true;
    else return false;
  }

  /**
   *
   * @function : To check whether Buttons for Notification Preferences given in ExpectedNotificationName is Enabled
   * @returns True if button is enabled , False otherwise
   */
  async validateConfigureNotifications() {
    await this.page.locator(configureNotificationBtn).click();
    let status = true;
    const notificationPreferences = await this.page
      .locator(notificationPreferencesEle)
      .all();
    const ExpectedNotificationName = [
      "Commission and Quota attainment status",
      "When a payee hit milestones in commissions (25%,50%, 75%, 100%)",
      "When a payee hit milestones in quota attainment (25%,50%, 75%, 100%)",
      "When Statements is locked",
      "When Statements are unlocked",
      "When payment is invalidated",
      "Daily update of queries status (open, unassigned)",
      "When query category is updated",
      "When a query is assigned to a user",
      "When a comment is added to a query involving a user",
      "When a user is added to a query thread",
      "When a query involving a user is resolved",
    ];
    for (let num = 0; num < ExpectedNotificationName.length; num++) {
      for (const notificationPreference of notificationPreferences) {
        if (
          (await notificationPreference
            .locator("div:nth-child(1)>label>span")
            .textContent()) === ExpectedNotificationName[num]
        ) {
          const isDisabled = await notificationPreference
            .locator("div:nth-child(6)>div>div>button")
            .isDisabled();
          if (isDisabled) status = false;
          break;
        }
      }
    }
    await this.page.locator(configureNotificationscloseBtn).click();
    return status;
  }

  /**
   *
   * @function : Navigate to Filter Section and Open Role Dropdown
   */
  async openRoleDropdown() {
    await this.page.locator(filterBtn).click();
    const waits = await this.page.locator(filterComboBox).all();
    await waits[0].click();
  }

  /**
   *
   * @function : Navigate to Filter Section and Open Reporting Manage Dropdown
   */
  async openReportingManagerDropdown() {
    await this.page.locator(filterBtn).click();
    const waits = await this.page.locator(filterComboBox).all();
    await waits[1].click();
  }

  /**
   *
   * @function : Navigate to Filter Section and Open Country Dropdown
   */
  async openCountryDropdown() {
    await this.page.locator(filterBtn).click();
    const waits = await this.page.locator(filterComboBox).all();
    await waits[2].click();
  }

  /**
   *
   * @function : Navigate to Filter Section and Open User Group Dropdown
   */
  async openUserGroupDropdown() {
    await this.page.locator(filterBtn).click();
    const waits = await this.page.locator(filterComboBox).all();
    await waits[3].click();
  }

  /**
   *
   * @function : Navigate to Filter Section and Open Notification Dropdown
   */
  async openNotificationDropdown() {
    await this.page.locator(filterBtn).click();
    const notificationDropdown = await this.page
      .locator("span>.ant-select-selection-search-input")
      .all();
    await notificationDropdown[1].click();
  }

  /**
   *
   * @param {String} userEmail
   * @param {String}currentRoleName
   * @param {String} updatedRoleName
   * @function : Assign Role to User
   */
  async assignRole(userEmail, currentRoleName, updatedRoleName) {
    await this.page
      .locator(`[data-testid='${userEmail} users dd button']`)
      .click();
    await this.page.getByText("Edit", { exact: true }).click();
    await this.page.getByTitle(currentRoleName, { exact: true }).click();
    await this.page.locator(`span[title='${updatedRoleName}']`).click();
    await this.page.getByRole("button", { name: "Update User" }).click();
  }

  /**
   *
   * @function : Select Value in Filters dropdown
   * @param {String} RoleName
   */
  async validateUserNewRole(RoleName) {
    await this.page
      .locator(`[title='${RoleName}'][label='${RoleName}']`)
      .click();
    await this.page.getByText("Apply").click();
    await this.page.waitForSelector("[col-id='checkbox']>div>div>svg", {
      state: "hidden",
    });
  }

  /**
   *
   * @function : Validate whether Notification dropdown filter is working as expected
   * @returns True if it works, False otherwise
   */
  async validateNotificationFilterFunctionality() {
    let isPass = true;
    // Validating Notification Combo box filter - Notification On
    await this.page.getByText("Notification On").click();
    await this.page.getByText("Apply").click();
    await this.page.waitForSelector("[col-id='checkbox']>div>div>svg", {
      state: "hidden",
    });
    let actualRoles = await this.page.$$eval(userStatusBtn, (elements) =>
      elements.filter((el) => el.getAttribute("aria-checked").trim() !== "true")
    );
    if (actualRoles.length > 0) isPass = false;
    await this.page.getByText("clear all").click();
    await this.page.waitForSelector("[col-id='checkbox']>div>div>svg", {
      state: "hidden",
    });
    // Validating Notification Combo box filter - Notification OFF
    this.openNotificationDropdown();
    await this.page.getByText("Notification Off").click();
    await this.page.getByText("Apply").click();
    await this.page.waitForSelector("[col-id='checkbox']>div>div>svg", {
      state: "hidden",
    });
    actualRoles = await this.page.$$eval(userStatusBtn, (elements) =>
      elements.filter(
        (el) => el.getAttribute("aria-checked").trim() !== "false"
      )
    );
    if (actualRoles.length > 0) isPass = false;
    await this.page.getByText("clear all").click();
    await this.page.waitForSelector("[col-id='checkbox']>div>div>svg", {
      state: "hidden",
    });
    return isPass;
  }

  /**
   * Validate Country Filter dropdown in Notification Page
   *
   * @returns True if Country filter worked as expected, False otherwise
   */
  async validateCountryFilterFunctionality() {
    let isStatus = true;
    await this.page.getByText("India").click();
    await this.page.getByText("Apply").click();
    await this.page.waitForSelector("[col-id='checkbox']>div>div>svg", {
      state: "hidden",
    });
    const actualValues = await this.page
      .locator(".ag-row>[col-id='employmentCountry']")
      .allTextContents();
    for (const value of actualValues) {
      if (value !== "IND") isStatus = false;
    }
    return isStatus;
  }

  /**
   *
   * @function : To Check whether the user should able to search the payees list by user name from the search box
   * @param {String} userName
   * @returns True if it display the expected User List
   */
  async validateSearchBox(userName) {
    await this.page.waitForSelector(userNameEle);
    let userNames = await this.page.$$eval(userNameEle, (elements) =>
      elements.map((el) => el.textContent.trim())
    );
    let filteredArray = userNames.filter((item) =>
      item.toLowerCase().includes(userName)
    );
    const expectedCount = filteredArray.length;
    await this.page.getByPlaceholder("Search by user name").fill(userName);
    await this.page.waitForSelector("[col-id='checkbox']>div>div>svg", {
      state: "hidden",
    });
    userNames = await this.page.$$eval(userNameEle, (elements) =>
      elements.map((el) => el.textContent.trim())
    );
    filteredArray = userNames.filter((item) =>
      item.toLowerCase().includes(userName)
    );
    const actualCount = filteredArray.length;
    if (actualCount === expectedCount) return true;
    else return false;
  }

  /**
   *
   * @function : To check Bulk Selecting Users and Turn ON/OFF Notification Status
   * @returns True if it is working as expected, False Otherwise
   */
  async validateBulkSelection() {
    let status = true;
    await this.page.getByText("Turn off notifications").click();
    await this.page
      .getByText("Notification status updated successfully")
      .waitFor({ state: "visible" });
    let actualStatus = await this.page.$$eval(userStatusBtn, (elements) =>
      elements.filter(
        (el) => el.getAttribute("aria-checked").trim() !== "false"
      )
    );
    if (actualStatus > 0) status = false;
    await this.page
      .locator(".ag-header-cell[col-id='checkbox']>div>label>span>input")
      .check();
    await this.page.getByText("Turn on notifications").click();
    await this.page
      .getByText("Notification status updated successfully")
      .waitFor({ state: "visible" });
    actualStatus = await this.page.$$eval(userStatusBtn, (elements) =>
      elements.filter((el) => el.getAttribute("aria-checked").trim() !== "true")
    );
    if (actualStatus > 0) status = false;
    await this.page
      .getByPlaceholder("Search by user name")
      .fill("<EMAIL>");
    await this.page
      .locator("[row-id='<EMAIL>']>div>div>button")
      .first()
      .click();
    await this.page.waitForTimeout(2000);
    await this.page
      .getByText("Notification status updated successfully")
      .waitFor({ state: "visible" });
    await this.page
      .getByPlaceholder("Search by user name")
      .fill("<EMAIL>");
    await this.page
      .locator("[row-id='<EMAIL>']>div>div>button")
      .first()
      .click();
    await this.page
      .getByText("Notification status updated successfully")
      .waitFor({ state: "visible" });
    return status;
  }

  /**
   *
   * @function : To return group count
   * @returns Group count
   */
  async getGroupCount() {
    const elementsCount = await this.page
      .locator("[data-test-id*=group]")
      .all();
    return elementsCount.length;
  }

  /**
   *
   * @function : Delete a group based on Group Name
   * @param {String} groupName
   */
  async deleteGroup(groupName) {
    await this.page.locator(`[title="${groupName}"]`).click();
    await this.page
      .locator('//button[ .//div[text()="Edit"]]/following-sibling::button')
      .click();
    await this.page.getByText("Delete", { exact: true }).click();
  }

  /**
   *
   * @function : Selecting Group Name in User Group Dropdown
   * @param {String} groupName
   */
  async validateUserNewGroup(groupName) {
    await this.page
      .locator(`[title='${groupName}'][label='${groupName}']`)
      .click();
    await this.page.keyboard.press("Escape");
    await this.page.getByText("Apply").click();
    await this.page.waitForSelector("[col-id='checkbox']>div>div>svg", {
      state: "hidden",
    });
  }

  /**
   *
   * @function : Clicking Clear All Button
   */
  async clearAllFilter() {
    await this.page.getByText("clear All").click();
    await this.page.waitForSelector("[col-id='checkbox']>div>div>svg", {
      state: "hidden",
    });
  }

  /**
   *
   * @function : Selecting Empty Group Option in User Group Filter Dropdown
   */
  async validateEmptyGroup() {
    await this.page.getByText("Empty Group", { exact: true }).click();
    await this.page.keyboard.press("Tab");
    await this.page.getByText("Apply").click();
    await this.page.waitForSelector("[col-id='checkbox']>div>div>svg", {
      state: "hidden",
    });
  }

  /**
   *
   * @function : Remove user from group
   * @param {String} groupName
   * @param {String} userName
   */
  async removeUserFromGroup(groupName, userName) {
    await this.page.locator(`span[title='${groupName}']`).click();
    await this.page.getByText("Edit").click();
    await this.page.hover(`span[title="${userName}"]`);
    await this.page.locator("span.ant-tag-close-icon").click();
    await this.page.getByText("Update", { exact: true }).click();
  }

  /**
   * Apply filters based on dropdown values and click the apply button.
   *
   * @param {Object} filters - An object containing dropdown values.
   * @param {Array} filters.Roles - Array of roles to select.
   * @param {Array} filters.ReportingManager - Array of reporting managers to select.
   * @param {Array} filters.Countries - Array of countries to select.
   * @param {Array} filters.NotificaitonStatus
   * @param {Array} filters.UserGroup - Array of user groups to select.
   */
  async applyFilters(filters) {
    await this.page.getByRole("button", { name: "Filters" }).click();
    const {
      Roles,
      ReportingManager,
      Countries,
      NotificationStatus,
      UserGroup,
    } = filters;
    const selectDropdownValues = async (dropdownSelector, values) => {
      await dropdownSelector.click();
      for (const value of values) {
        await this.page.locator(`[title='${value}'][label='${value}']`).click();
      }
      await this.page.keyboard.press("Escape");
    };
    const waits = await this.page.locator(filterComboBox).all();
    const notificationDropdown = await this.page
      .locator("span>.ant-select-selection-search-input")
      .all();
    const dropdownSelectors = {
      Roles: waits[0],
      ReportingManager: waits[1],
      Countries: waits[2],
      NotificationStatus: notificationDropdown[1],
      UserGroup: waits[3],
    };
    if (Roles && Roles.length > 0) {
      await selectDropdownValues(dropdownSelectors.Roles, Roles);
    }
    if (ReportingManager && ReportingManager.length > 0) {
      await selectDropdownValues(
        dropdownSelectors.ReportingManager,
        ReportingManager
      );
    }
    if (Countries && Countries.length > 0) {
      await selectDropdownValues(dropdownSelectors.Countries, Countries);
    }
    if (UserGroup && UserGroup.length > 0) {
      await selectDropdownValues(dropdownSelectors.UserGroup, UserGroup);
    }
    if (NotificationStatus && NotificationStatus.length > 0) {
      await dropdownSelectors.NotificationStatus.click();
      await this.page
        .locator(
          `[title='${NotificationStatus[0]}'][label='${NotificationStatus[0]}']`
        )
        .click();
    }
    await this.page.getByText("Apply", { exact: true }).click();
    await this.page
      .locator("[col-id='checkbox']>div>div>svg")
      .waitFor({ state: "hidden" });
  }

  /**
   * Remove Filter based on topic
   *
   * @param {Array} removeFilter
   */
  async removeFilter(removeFilter) {
    for (const filter of removeFilter) {
      const divs = await this.page
        .locator("div.text-xs.base-content-mid")
        .all();
      for (const div of divs) {
        if (
          (await div.locator("div>span:nth-child(1)").textContent()) === filter
        ) {
          await div.locator("svg").click();
          await this.page.waitForSelector("[col-id='checkbox']>div>div>svg", {
            state: "hidden",
          });
          break;
        }
      }
    }
    await this.page
      .locator("[col-id='checkbox']>div>div>svg")
      .waitFor({ state: "hidden" });
  }

  /**
   *@function : Get Reporting manager values from Reporting Manager Dropdown
   */
  async getReportingManagerValues() {
    const elements = await this.page
      .locator("[col-id='reportingManagerFullName']>span")
      .all();
    const textValues = [];
    for (const element of elements) {
      const text = await element.innerText();
      if (text.trim() !== "") {
        textValues.push(text.trim());
      }
    }
    // Remove duplicate values and return unique, non-blank text values
    return [...new Set(textValues)];
  }

  /**
   * Get Filter dropdown values
   *
   * @param {String} selector
   *
   * @returns
   */
  async getdropdownValues(selector) {
    const elements = await this.page.locator(selector).all();
    const textValues = [];

    for (const element of elements) {
      const text = await element.innerText();
      if (text.trim() !== "") {
        textValues.push(text.trim());
      }
    }
    return textValues;
  }

  /**
   * Compare Actual value with Expected Dropdown values
   *
   * @param {String} expected
   * @param {String} actual
   * @returns
   */
  async dropdownvalidation(expected, actual) {
    const sortAndDeduplicate = (arr) => {
      return arr
        .slice()
        .sort()
        .filter((value, index, self) => self.indexOf(value) === index);
    };
    const sortedAndDeduplicatedArr1 = sortAndDeduplicate(expected);
    const sortedAndDeduplicatedArr2 = sortAndDeduplicate(actual);
    return (
      JSON.stringify(sortedAndDeduplicatedArr1) ===
      JSON.stringify(sortedAndDeduplicatedArr2)
    );
  }

  async changeManager(userEmail, currentManager, updateManager) {
    await this.page
      .locator(
        `[row-id='${userEmail}']>div[col-id='actions']>div>span>div>div>button`
      )
      .click();
    await this.page.getByRole("button", { name: "next" }).click();
    await this.page.locator("button.ant-switch").click();
    await this.page.getByRole("button", { name: "okay" }).click();
    await this.page.locator(`span[title='${currentManager}']`).click();
    await this.page.locator(`span[title="${updateManager}"]`).click();
    await this.page.getByRole("button", { name: "Save Changes" }).click();
  }

  async turnPayeeNotification(enable) {
    await this.page
      .getByText("Configure notifications", { exact: true })
      .click();
    const buttons = await this.page
      .locator(".ant-row>div:nth-child(6)>div>div>button")
      .all();
    if (enable === true) {
      const ariaChecked = await buttons[6].getAttribute("aria-checked");
      if (ariaChecked === "false") {
        await buttons[6].click();
        await this.page.getByRole("button", { name: "save" }).click();
        await this.page
          .getByText("Notification preferences saved successfully")
          .waitFor({ state: "visible" });
      }
    } else {
      const ariaChecked = await buttons[6].getAttribute("aria-checked");
      if (ariaChecked === "true") {
        await buttons[6].click();
        await this.page.getByRole("button", { name: "save" }).click();
        await this.page
          .getByText("Notification preferences saved successfully")
          .waitFor({ state: "visible" });
      }
    }
  }

  /**
   * Apply filters and Validating Filtered Section Users count
   * @param {Array} filterArray
   * @param {Number} expectedCount
   *
   */
  async validateFilteredUsersCount(
    filterArray,
    expectedCount,
    actualUserCount
  ) {
    await this.applyFilters(filterArray);
    await expect(this.page.locator("[col-id='fullName'].ag-cell")).toHaveCount(
      expectedCount
    );
    await this.clearAllFilter();
    const totalCount = await this.page
      .locator(
        "(//button[.//span[text()='Turn on notifications']]/ancestor::div[1]/preceding-sibling::div//span)[3]"
      )
      .textContent();
    expect(parseInt(totalCount, 10)).toBe(actualUserCount);
  }

  /**
   * Apply filters, remove filter and Validating Filtered Section Users count
   * @param {Array} filterArray
   * @param {Number} expectedCount
   *
   */
  async validateRemovedFilteredUsersCount(
    filterArray,
    removeFilterArray,
    beforeFilterCount,
    AfterFilterCount,
    actualUserCount
  ) {
    await this.applyFilters(filterArray);
    await expect(this.page.locator("[col-id='fullName'].ag-cell")).toHaveCount(
      beforeFilterCount
    );
    await this.removeFilter(removeFilterArray);
    await expect(this.page.locator("[col-id='fullName'].ag-cell")).toHaveCount(
      AfterFilterCount
    );
    await this.clearAllFilter();
    const totalCount = await this.page
      .locator(
        "(//button[.//span[text()='Turn on notifications']]/ancestor::div[1]/preceding-sibling::div//span)[3]"
      )
      .textContent();
    expect(parseInt(totalCount, 10)).toBe(actualUserCount);
  }

  async checkNotificationExist(
    request,
    token,
    email_id,
    client_id,
    notification_name,
    channels,
    dateTime
  ) {
    const allResponses = []; 
  
    for (const channel of channels) {
      await this.page.waitForTimeout(2000);
  
      const requestBody = {
        email_id: email_id,
        notification_name: notification_name,
        channel: channel,
        client_id: client_id,
        triggered_at: dateTime,
      };
  
      const response = await request.post(
        "https://qa.everstage.com/notification_audit/check_notification_exist",
        {
          data: requestBody,
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
  
      console.log("Channel:", channel);
      console.log("Request body:", requestBody);
  
      const jsonResponse = await response.json();
      console.log("Response:", jsonResponse);
  
      expect(jsonResponse.message).toBe(true);
      expect(response.status()).toBe(200);
  
      allResponses.push(jsonResponse); 
    }
  
    return allResponses; 
  }
  

  async getCurrentDateTime() {
    const now = new Date();

    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hour = String(now.getHours()).padStart(2, "0");
    const minute = String(now.getMinutes()).padStart(2, "0");

    return `${year}${month}${day}${hour}${minute}`;
  }
}

export default Notification;
