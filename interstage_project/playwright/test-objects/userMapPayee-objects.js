class UserMapPayeePage {
  constructor(page) {
    this.page = page;
  }

  async updateDesignation(value) {
    await this.page.locator("#map-payee_designation").fill(value);
  }

  async updateCrystalAccess(value) {
    await this.page.locator("#map-payee_payeeRole").click();
    await this.page
      .locator(`div[role="listitem"] span[title="${value}"]`)
      .click();
  }

  async updateBasePay(value) {
    await this.page.locator("#map-payee_fixedPay").fill(value);
  }

  async updateVariablePay(value) {
    await this.page.locator("#map-payee_variablePay").fill(value);
  }

  async toggleCF(id) {
    await this.page.locator(`span:has(> input#${id})`).click();
  }

  async updateCFEmergencyContact(value) {
    await this.page
      .locator("#map-payee_cf_10448_emergency_contact")
      .fill(value);
  }

  async fillCFEmergencyEmail(value) {
    await this.page.locator("#map-payee_cf_10448_emergency_email").fill(value);
  }

  async chooseGender(value) {
    await this.page.locator("#map-payee_cf_10448_pronounced_as").click();
    await this.page.locator(`span[title="${value}"]`).click();
  }

  async fillCFstartedon(date) {
    const locator = this.page.locator(
      "#map-payee_cf_10448_freelance_started_om"
    );

    await locator.click();
    await locator.fill(date);
    await locator.press("Enter");
  }

  async updatePayoutFrequency(value) {
    await this.page
      .locator(
        '//input[@id="map-payee_payoutFrequency"]/parent::span/following-sibling::span'
      )
      .click();
    await this.page
      .locator(`div[role="listitem"] span[title="${value}"]`)
      .click();
  }

  async updateCountry(value) {
    await this.page
      .locator(
        '//input[@id="map-payee_employmentCountry"]/parent::span/following-sibling::span'
      )
      .click();
    await this.page
      .locator(`div[role="listitem"] span[title="${value}"]`)
      .click();
  }

  async updateFutureDetails(overwrite = true, date = null) {
    if (!overwrite) {
      await this.page.locator("input[value='CHOOSE_FUTURE_DATES']").click();
      await this.page
        .getByLabel("Update User Details")
        .getByRole("button", { name: "Next" })
        .click();
      await this.page.locator("#effective-date-step-2 input").first().click();
      await this.updateFieldByID("ALL_FIELDS", date);
      await this.clickBtn("Save");
    } else {
      await this.page.locator('input[value="OVERWRITE"]').click();
      await this.clickBtn("Save");
    }
  }

  async verifyToast(msg) {
    await this.page.getByText(msg).waitFor({ state: "visible" });
    await this.page.getByText(msg).waitFor({ state: "hidden" });
  }

  async clickBtn(btn) {
    await this.page
      .getByRole("button", { name: btn, exact: true })
      .last()
      .click();
  }

  async verifyRecord(date, value) {
    return await this.page
      .locator(
        `//span[contains(text(), "${date}:")]/following-sibling::span/*[contains(text(), "${value}")]`
      )
      .last();
  }

  async verifyRecordDoesNotExist(date, value) {
    const locator = this.page
      .locator(
        `//span[contains(text(), "${date}")]/following-sibling::span[contains(text(), "${value}")]`
      )
      .last();

    // Check if the element count is zero
    const count = await locator.count();
    if (count === 0) {
      console.log(`No element found for date: ${date} and value: ${value}`);
      return true; // Locator does not exist
    } else {
      console.log(`Element found for date: ${date} and value: ${value}`);
      return false; // Locator exists
    }
  }

  async clickRecord(value) {
    await this.page
      .getByRole("button")
      .locator("span", { hasText: value })
      .first()
      .click({ timeout: 5000 });
  }

  /**
   * Verifies the record details for a given date and field name.
   *
   * This function retrieves the record period, such as start date and end date,
   * and verifies the specified field name and its corresponding value.
   *
   * @param {string} recordDate - The date of the record to verify (e.g., "31 Dec").
   * @param {string} fieldName - The name of the field to verify (e.g., "Designation").
   * @returns {string} - The value of the field.
   */
  async verifyRecordDetails(recordDate, fieldName) {
    const locator = await this.page
      .locator(
        `//div[@role='button'][.//span[contains(text(),'${recordDate}')]]/following-sibling::div//span[text()='${fieldName}']/ancestor::div[2]/div[3]/span`
      )
      .last();
    return await locator.textContent();
  }

  async clickMoreOptions(date) {
    await this.page
      .locator(
        `//div[@role="button"][.//span[contains(text(), '${date}')]]//button`
      )
      .last()
      .click();
  }

  async editRecord() {
    await this.page
      .getByRole("menuitem")
      .locator("span", { hasText: "Edit" })
      .last()
      .click();
  }

  async deleteRecord() {
    await this.page
      .getByRole("menuitem")
      .locator("span", { hasText: "Delete" })
      .last()
      .click();
  }

  async verifyStringsPresent(stringsList) {
    for (const text of stringsList) {
      const isVisible = await this.page
        .locator(
          `//div[contains(@class, 'ant-modal-content')]//span[text()='${text}']`
        )
        .isVisible({ timeout: 5000 }); // Check if the text is visible
      if (!isVisible) {
        console.error(`String not found: "${text}"`);
        return false; // Return false immediately if any string is not found
      }
    }
    console.log("All strings are present on the screen.");
    return true; // Return true if all strings are found
  }

  async getTextWithLineThrough() {
    // Find all elements with the 'line-through' class
    const elements = await this.page.locator(".line-through").allTextContents();
    return elements; // Return the list of text content
  }

  async updateFieldByID(selectorId, value) {
    await this.page.locator(`#${selectorId}`).click();
    await this.page.locator(`#${selectorId}`).fill(value);
    await this.page.locator(`#${selectorId}`).press("Enter");
  }

  async verifyRecordPopup(heading, text) {
    // Wait for heading and modal to be visible
    await this.page.getByText(heading).waitFor({ state: "visible" });
    await this.page
      .locator(".ant-modal-body")
      .last()
      .waitFor({ state: "visible" });

    // Get modal text
    const modalText = await this.page
      .locator(".ant-modal-body")
      .last()
      .innerText();

    // Normalize modalText and expected text
    const normalizedModalText = modalText.replace(/\s+/g, " ").trim();
    const normalizedExpectedText = text.replace(/\s+/g, " ").trim();
    // Check if the normalized modal text contains the expected text
    if (!normalizedModalText.includes(normalizedExpectedText)) {
      throw new Error(
        `Expected text not found in the modal body.\nModal Text: ${normalizedModalText}\nExpected Text: ${normalizedExpectedText}`
      );
    }
  }

  async goToTab(tabName) {
    await this.page.getByRole("tab", { name: tabName }).click();
  }

  async verifyAnchorTag() {
    const anchorTag = await this.page.locator(
      `a[href="/settings/commissions-and-data-sync"]`
    );

    await anchorTag.waitFor({ state: "visible", timeout: 5000 });
    const textContent = await anchorTag.innerText();

    if (textContent !== "Settings → Commission & Data Sync") {
      throw new Error("Expected text not found for the anchor tag.");
    }
  }

  async updateReportingManager(newManager, id) {
    const locator = `#${id}`;
    await this.page
      .locator(`//div[@data-testid="ever-select"][.//input[@id='${id}']]`)
      .click({ timeout: 5000 });
    await this.page.locator(locator).fill(newManager);
    await this.page
      .locator(`div[role="listitem"] span[title="${newManager}"]`)
      .click();
  }

  async reportingManagerMoreOptions(date) {
    await this.page
      .getByRole("row", { name: date })
      .getByRole("button")
      .click({ timeout: 10000 });
  }

  async switch() {
    await this.page.getByRole("switch").click();
    await this.clickBtn("Okay");
  }

  async verifyManagerNames(names) {
    for (const name of names) {
      const isVisible = await this.page
        .locator(`div.ant-table span:has-text("${name}")`)
        .last()
        .isVisible({ timeout: 10000 });

      if (!isVisible) {
        console.error(`Manager name "${name}" not found.`);
        return false;
      }
    }

    return true; // All names found
  }

  async closeModal(byX = true) {
    if (byX) {
      await this.page
        .locator(".ant-modal-close")
        .last()
        .click({ timeout: 5000 });
    } else {
      await this.clickBtn("Cancel");
    }
    await this.page
      .locator(".ant-modal-body")
      .last()
      .waitFor({ state: "hidden", timeout: 5000 });
  }
}

export default UserMapPayeePage;
