const CommonUtils = require("./common-utils-objects");
class LockUser {
  constructor(page) {
    this.page = page;
    this.searchUserLocator = page.getByPlaceholder("Search by name or email", {
      exact: true,
    });
  }

  async navigate(locator) {
    await this.page.goto(locator, { waitUntil: "networkidle" });
  }

  async selectDate(date) {
    await new CommonUtils(this.page).setCustomCalendarDate(date);
    await this.page.getByPlaceholder("Select date").click();
    await this.page.getByPlaceholder("Select date").fill("");
    await this.page.getByPlaceholder("Select date").fill(date);
    const formattedDate = new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
    await this.page.getByPlaceholder("Select date").fill(formattedDate);
    await this.page.keyboard.press("Enter");
    await this.page.keyboard.press("Escape");
  }

  async searchByNameOrEmail(nameOrEmail) {
    await this.searchUserLocator.click();
    await this.page
      .getByPlaceholder("Search by name or email", { exact: true })
      .fill(nameOrEmail);
    await this.page.waitForTimeout(5000);
  }

  async searchByUsernameOrEmail(usernameOrEmail) {
    await this.searchUserLocator.click();
    await this.page
      .getByPlaceholder("Search by name or email", { exact: true })
      .fill(usernameOrEmail);
    await this.page.waitForTimeout(5000);
  }

  async invalidatePayment() {
    await this.page.locator(".ag-group-contracted > .ag-icon").first().click();
    await this.page.getByRole("button", { name: "Invalidate" }).click();
    await this.page
      .getByText("Payment invalidated")
      .waitFor({ state: "visible", timeout: 10000 });
  }

  async lockStatements(userEmail) {
    // await this.page.locator("button[data-testid*='users dd button']").click();
    await this.page.waitForTimeout(2000);
    await this.page.getByTestId(`${userEmail}-actions-dd`).click();
    await this.page.getByRole("menuitem", { name: "Lock Statements" }).click();
    await this.page
      .getByText("Lock status updated successfully")
      .waitFor({ state: "visible", timeout: 20000 });
  }

  async unlockStatements(userEmail) {
    // await this.page.locator("button[data-testid*='users dd button']").click();
    await this.page.waitForTimeout(2000);
    await this.page.getByTestId(`${userEmail}-actions-dd`).click();
    await this.page
      .getByRole("menuitem", { name: "Unlock Statements" })
      .click();
    await this.page
      .getByText("Lock status updated successfully")
      .waitFor({ state: "visible", timeout: 10000 });
  }

  async initiateExit(exitDate) {
    await this.page.locator("button[data-testid*='users dd button']").click();
    await this.page.getByRole("button", { name: "Initiate Exit" }).click();
    await this.page.getByPlaceholder("Select date").click();
    await this.page.getByPlaceholder("Select date").fill(exitDate);
    await this.page.getByPlaceholder("Select date").press("Enter");
    await this.page.getByRole("button", { name: "Validate" }).click();
  }

  async getErrorMessage() {
    return await this.page
      .locator("(//div[@class='ant-alert-message'])[1]")
      .textContent();
  }

  async getWarningMessageWithError() {
    return await this.page
      .locator("(//div[@class='ant-alert-message'])[3]")
      .textContent();
  }

  async getWarningMessage() {
    return await this.page
      .locator("(//div[@class='ant-alert-message'])[2]")
      .textContent();
  }

  async isConfirmButtonDisabled() {
    const confirmButton = await this.page.getByRole("button", {
      name: "Confirm",
    });
    return await confirmButton.isDisabled();
  }

  async validateSuccessMessage() {
    await this.page
      .getByText("Validation Successful!")
      .waitFor({ state: "visible", timeout: 10000 });
  }

  async isConfirmButtonEnabled() {
    const confirmButton = await this.page.getByRole("button", {
      name: "Confirm",
    });
    return await confirmButton.isEnabled();
  }

  async getResultsifOne() {
    const results = [];
    const elements = await this.page.locator("(//div[@class='flex'])[1]");
    for (let i = 0; i < (await elements.count()); i++) {
      const text = await elements.nth(i).textContent();
      results.push(text);
    }
    return results;
  }

  async getResultsifMany() {
    const results = [];
    const elements = await this.page.locator(
      "div.ant-alert-description div.flex-col"
    );
    for (let i = 0; i < (await elements.count()); i++) {
      const text = await elements.nth(i).textContent();
      results.push(text);
    }
    return results;
  }

  async getResults(selector) {
    const results = [];
    const elements = await this.page.locator(selector);
    for (let i = 0; i < (await elements.count()); i++) {
      const text = await elements.nth(i).textContent();
      results.push(text);
    }
    return results;
  }

  async registerFullPayment() {
    await this.page
      .locator("button.primary-ring div.gap-2 div.h-5,w-5")
      .click();
    await this.page.getByPlaceholder("Add your comments").click();
    await this.page.getByPlaceholder("Add your comments").fill("Full Payment");
    await this.page.getByRole("button", { name: "Register" }).click();
    await this.page
      .getByText("Payment registered successfully")
      .waitFor({ state: "visible", timeout: 15000 });
  }

  async registerPartialPayment(Label) {
    await this.page
      .locator("button.primary-ring div.gap-2 div.h-5,w-5")
      .click();
    await this.page.getByLabel(Label).locator('input[type="text"]').click();
    await this.page.getByLabel(Label).locator('input[type="text"]').fill("");
    await this.page
      .getByLabel(Label)
      .locator('input[type="text"]')
      .fill("5000");
    await this.page.getByPlaceholder("Add your comments").click();
    await this.page
      .getByPlaceholder("Add your comments")
      .fill("Partial Payment");
    await this.page.getByRole("button", { name: "Register" }).click();
    await this.page
      .getByText("Payment registered successfully")
      .waitFor({ state: "visible", timeout: 10000 });
  }

  async clickMonth(monthText) {
    const [newPage] = await Promise.all([
      this.page.context().waitForEvent("page"),
      this.page.getByText(monthText).click(),
    ]);
    await newPage.waitForLoadState("load");
    return newPage;
  }

  async openArrearsView() {
    await this.page.getByRole("button", { name: "Arrears View" }).click();
  }

  async moveToArrear(Name) {
    await this.searchUserLocator.click();
    await this.searchUserLocator.fill(Name);
    await this.page.waitForTimeout(5000);
    await this.page
      .locator("div.ag-row-even div.css-bw5a7v button.primary-ring")
      .click();
    await this.page.getByRole("button", { name: "Move" }).click();
  }

  async openPayoutsView() {
    await this.page.locator("div.ant-dropdown-trigger").click();
    await this.page
      .getByRole("menuitem", { name: "Payouts View" })
      .locator("div")
      .nth(1)
      .click();
  }
}

export default LockUser;
