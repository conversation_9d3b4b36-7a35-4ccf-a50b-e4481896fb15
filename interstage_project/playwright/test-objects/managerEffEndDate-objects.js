import { expect } from "@playwright/test";
const CommonUtils = require("./common-utils-objects");
class ManagerEffectiveEndDate {
  constructor(page) {
    this.page = page;
    this.searchUserLocator = page.getByPlaceholder("Search by name or email", {
      exact: true,
    });
  }

  // async effenddate() {
  //   await this.page.getByRole("button", { name: "Payee 1" }).click();
  //   await this.page.getByRole("button", { name: "Next" }).click();
  //   // await expect(
  //   //   page.locator("label").filter({ hasText: "Effective End Date" }).first()
  //   // ).toBeVisible();
  // }

  async navigate(url) {
    await this.page.goto(url, { waitUntil: "networkidle" });
  }

  async selectMonthInStatement(month) {
    const monthNames = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    const currentMonth = monthNames[new Date().getMonth()];

    // Click the dropdown option based on the current month
    await this.page.getByTestId("ever-select").getByText(currentMonth).click();
    // await this.page.getByTestId("ever-select").getByText("October").click();
    await this.page.getByPlaceholder("Search Period").click();
    await this.page.getByPlaceholder("Search Period").fill(month);
    await this.page.getByText(month).click();
  }

  async openDatabook(bookname) {
    await this.page.getByText(bookname).click();
  }

  async opensheetInDatabook(sheetname) {
    await this.page.getByRole("tab", { name: sheetname, exact: true }).click();
  }

  async openDatasheet() {
    // await this.page.locator("//span[normalize-space()='quota attainment']");
    await this.page
      .getByRole("tab", { name: "quota attainment", exact: true })
      .click();
    // await this.page.getByText("quota attainment", { exact: true }).click();
  }

  async updateDatasheet() {
    await this.page
      .getByText("The source for this datasheet")
      .waitFor({ state: "visible", timeout: 10000 });
    const staleness = this.page.getByText("The source for this datasheet");
    const isStalenessVisible = await staleness.isVisible({
      timeout: 2000,
    });
    if (isStalenessVisible) {
      await expect(
        this.page.getByRole("button", { name: "Update Data", exact: true })
      ).toBeVisible({
        timeout: 200000,
      });
      // If the "Update" button is visible, click it
      await this.page
        .getByRole("button", { name: "Update Data", exact: true })
        .click();
      await expect(
        this.page.getByText("Datasheet has been generated")
      ).toBeVisible({ timeout: 200000 });
      console.log("Datasheet Updated");
    } else {
      // If the "Update" button is not visible, continue without Updating
      console.log("Update button not found, skipping Update.");
    }
  }

  async openPlan(planname) {
    await this.page.getByText(planname).click();
  }

  async openPlanComponent(componentname) {
    await this.page.getByText(componentname).click();
  }

  async simulatePlanComponent() {
    await this.page.getByRole("button", { name: "Simulate" }).click();
  }

  async setStartDateandEndDateinSimulate(startdate, enddate) {
    await this.page.getByPlaceholder("Start date").click();
    await this.page.getByPlaceholder("Start date").fill("");
    await this.page.getByPlaceholder("Start date").fill(startdate);
    await this.page.getByPlaceholder("Start date").press("Enter");
    await this.page.getByPlaceholder("End date").click();
    await this.page.getByPlaceholder("End date").fill("");
    await this.page.getByPlaceholder("End date").fill(enddate);
    await this.page.getByPlaceholder("End date").press("Enter");
  }

  async clickRun(payee) {
    await this.page.getByRole("button", { name: "Run" }).click();
  }

  async clickExitCanvas(payee) {
    await this.page.getByRole("button", { name: "Exit Canvas" }).click();
  }

  async searchPayeeinPayouts(payee) {
    await this.searchUserLocator.click();
    await this.searchUserLocator.fill(payee);
    await this.page
      .getByText(payee)
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async clickPayeeinPayouts(payee) {
    await this.page.getByRole("link", { name: payee }).click();
  }

  async setPayoutsDate(payoutDate) {
    await new CommonUtils(this.page).setCustomCalendarDate(payoutDate);
    const dateInput = await this.page.getByPlaceholder("Select date");
    await dateInput.click();
    await dateInput.fill(payoutDate);
    await this.page.keyboard.press("Enter");
    await this.page.keyboard.press("Escape");
  }

  async setFutureYearPayoutsDate(payoutDate) {
    const dateInput = await this.page.getByPlaceholder("Select date");
    await dateInput.click();
    await this.page.locator(".ant-picker-header-super-next-btn").dblclick();
    await this.page.locator(".ant-picker-header-super-next-btn").dblclick();
    await dateInput.fill("");
    await this.page.waitForTimeout(3000);
    await dateInput.fill(payoutDate);
    await this.page.keyboard.press("Enter");
    await this.page.keyboard.press("Escape");
  }

  async clickregisterPayment(email) {
    // await this.page.locator("div.flex.text-inherit.h-5.w-5 svg").click();
    await this.page.waitForTimeout(2000);
    await this.page.getByTestId(`${email}-register-payment`).click();
    // await page.getByTestId('<EMAIL>-register-payment').click();
  }

  async clickOptions() {
    // await this.page.waitForTimeout(3000);
    await this.page.locator("button[data-testid*='users dd button']").click();
  }

  async navigateToManagerScreen(payeename) {
    await this.page.goto("/users", { waitUntil: "networkidle" });
    await this.searchUserLocator.click();
    await this.searchUserLocator.fill(payeename);
    await this.page.waitForTimeout(3000);
    await this.clickOptions();
    await this.page
      .getByRole("menuitem", { name: "Map Payee" })
      .getByRole("button")
      .click();
    await this.page.getByRole("button", { name: "Next" }).click();
  }

  async overWriteCurrentDetails() {
    await this.page.getByRole("switch").click();
    await this.page.getByRole("button", { name: "Okay" }).click();
  }

  async savechangesbutton() {
    await this.page.getByRole("button", { name: "Save Changes" });
  }

  async setEffEndDateFromStartDate() {
    const startdateField = this.page.locator(
      "#reporting-mgr_reportingEffectiveStartDate"
    );
    // Get the title attribute and save it in a constant
    const titleValue = await startdateField.getAttribute("title");
    console.log("Title value:", titleValue);
    await this.page.getByLabel("Effective End Date").click();
    await this.page.getByLabel("Effective End Date").fill(titleValue);
    await this.page.keyboard.press("Enter");
  }

  async setStartDate(startdate) {
    const startdateField = this.page.locator(
      "#reporting-mgr_reportingEffectiveStartDate"
    );
    await startdateField.click();
    await startdateField.fill(startdate);
    await this.page.keyboard.press("Enter");
  }

  async setEndDate(enddate) {
    const endDateField = this.page.getByLabel("Effective End Date");
    await endDateField.click();
    await endDateField.fill(enddate);
    await this.page.keyboard.press("Enter");
  }

  async clickReset() {
    await this.page.getByRole("button", { name: "Reset" }).click();
  }

  async addFilterinDatasheet(columnname, operatorname, payeemail) {
    await this.page.getByRole("button", { name: "Add Filter" }).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Column$/ })
      .nth(3)
      .click();
    await this.page
      .locator("[data-testid='ever-select'] div span input")
      .first()
      .fill(columnname);
    await this.page.getByRole("listitem").getByText(columnname).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Operator$/ })
      .nth(3)
      .click();
    await this.page.getByText(operatorname, { exact: true }).click();
    await this.page.getByRole("textbox").nth(1).click();
    await this.page.getByRole("textbox").nth(1).fill(payeemail);
    await this.page.getByRole("button", { name: "Apply", exact: true }).click();
  }

  async addAnotherConditioninFilter(columnname, operatorname) {
    await this.page.getByText("Edit filter").click();
    await this.page.getByRole("button", { name: "Add Condition" }).click();
    // await this.page.locator("#rc_select_108").click();
    await this.page
      .locator("input.ant-select-selection-search-input")
      .nth(6)
      .click();
    await this.page
      .locator("input.ant-select-selection-search-input")
      .nth(6)
      .fill(columnname);
    await this.page.locator("span").filter({ hasText: columnname }).click();
    // await this.page.locator("#rc_select_109").click();
    await this.page
      .locator("input.ant-select-selection-search-input")
      .nth(7)
      .click();
    await this.page
      .locator("input.ant-select-selection-search-input")
      .nth(7)
      .fill(operatorname);
    await this.page.getByText(operatorname).click();
    await this.page.getByRole("button", { name: "Apply", exact: true }).click();
  }

  async ValidateUserReport(columnname, operatorname) {
    await this.page.getByText("User Report").click();
    await this.page.getByRole("button", { name: "Add Filter" }).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Column$/ })
      .nth(3)
      .click();
    await this.page
      .locator("[data-testid='ever-select'] div span input")
      .first()
      .fill(columnname);
    await this.page
      .getByRole("listitem")
      .getByText("Employee Email Id")
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Operator$/ })
      .nth(3)
      .click();
    await this.page.getByText(operatorname, { exact: true }).click();
    await this.page.getByRole("textbox").nth(1).click();
    await this.page.getByRole("textbox").nth(1).fill("<EMAIL>");
    await this.page.getByRole("button", { name: "Apply", exact: true }).click();
  }

  async registerPayment(paymentdate, amount) {
    // change locator
    await this.page
      .getByLabel(`Register payment${paymentdate}`)
      .locator('input[type="text"]')
      .click();
    await this.page
      .getByLabel(`Register payment${paymentdate}`)
      .locator('input[type="text"]')
      .fill("");
    await this.page
      // .getByLabel("Register payment30 Sep")
      .getByLabel(`Register payment${paymentdate}`)
      .locator('input[type="text"]')
      .fill(amount);
    await this.page.getByRole("button", { name: "Register" }).click();
  }

  async clickPlusbuttoninQuotas() {
    await this.page
      .locator(".ant-tree-switcher.ant-tree-switcher_close")
      .first()
      .click();
  }

  async clickExpandbuttoninQuotas(manager) {
    if (manager === "manager1") {
      // Click the first locator for manager1
      await this.page
        .locator(".ant-tree-switcher.ant-tree-switcher_close")
        .first()
        .click();
    } else if (manager === "manager2") {
      // Click the second locator for manager2
      await this.page
        .locator(".ant-tree-switcher.ant-tree-switcher_close")
        .nth(1) // Selects the second locator
        .click();
    } else {
      throw new Error(
        `Invalid index: ${manager}. Expected 'manager1' or 'manager2'.`
      );
    }
  }

  async clickPayee(payee) {
    await this.page.getByText(payee).click();
  }

  async clickEditQuotabutton(payee) {
    await this.page.locator(".rounded-lg > .w-4").click();
  }

  async editAnnaulQuotavalue(amount) {
    await this.page
      .getByRole("row", { name: "Quota Increase Value Decrease Value" })
      .getByRole("spinbutton")
      .click();
    // .getByRole("cell", { name: "Increase Value Decrease Value 20000" })
    // .click();
    await this.page
      .getByRole("row", { name: "Quota Increase Value Decrease Value" })
      .getByRole("spinbutton")
      .fill("");
    // .getByRole("cell", { name: "Increase Value Decrease Value 20000" })
    // .fill("");
    await this.page
      .getByRole("row", { name: "Quota Increase Value Decrease Value" })
      .getByRole("spinbutton")
      .fill(amount);
    // .getByRole("cell", { name: "Increase Value Decrease Value 20000" })
    // .fill(amount);
  }

  async editMonthlyQuotavalue(amount, monthplusone) {
    await this.page
      .locator(
        `td:nth-child(${monthplusone}) > .ant-input-number > .ant-input-number-input-wrap > .ant-input-number-input`
      )
      .first()
      .click();
    await this.page
      .locator(
        // "td:nth-child(13) > .ant-input-number > .ant-input-number-input-wrap > .ant-input-number-input"
        `td:nth-child(${monthplusone}) > .ant-input-number > .ant-input-number-input-wrap > .ant-input-number-input`
      )
      .first()
      .fill("");
    // locator(".ant-input-number !p-0").nth(3).click();
    await this.page
      .locator(
        `td:nth-child(${monthplusone}) > .ant-input-number > .ant-input-number-input-wrap > .ant-input-number-input`
      )
      .first()
      .fill(amount);
  }

  async editQuarterlyyQuotavalue(amount, quarter) {
    // quarter starts from 0 eg: jan to mar is 0
    await this.page
      .getByRole("row", { name: "Quota Increase Value Decrease" })
      .getByRole("spinbutton")
      .nth(quarter)
      .click();
    await this.page
      .getByRole("row", { name: "Quota Increase Value Decrease" })
      .getByRole("spinbutton")
      .nth(quarter)
      .fill("");
    await this.page
      .getByRole("row", { name: "Quota Increase Value Decrease" })
      .getByRole("spinbutton")
      .nth(quarter)
      .fill(amount);
  }

  async editHalfYearlyQuotaValue(amount) {
    // await this.page
    //   .locator("(//div[@class='ant-input-number-input-wrap'])[2]")
    //   .click();
    await this.page
      .locator(
        "(//div[@class='ant-input-number-input-wrap'])[2]//input[@class='ant-input-number-input']"
      )
      .click();
    await this.page
      .locator(
        "(//div[@class='ant-input-number-input-wrap'])[2]//input[@class='ant-input-number-input']"
      )
      .fill("");
    await this.page
      .locator(
        "(//div[@class='ant-input-number-input-wrap'])[2]//input[@class='ant-input-number-input']"
      )
      .fill(amount);
    // await this.page
    //   .getByRole("cell", {
    //     name: `Increase Value Decrease Value ${existingamount}`,
    //   })
    //   .getByRole("spinbutton")
    //   .click();
    // await this.page
    //   .getByRole("cell", {
    //     name: `Increase Value Decrease Value ${existingamount}`,
    //   })
    //   .getByRole("spinbutton")
    //   .fill("");
    // await this.page
    //   .getByRole("cell", {
    //     name: `Increase Value Decrease Value ${""}`,
    //   })
    //   .getByRole("spinbutton")
    //   .fill(newamount);
  }

  async lockOrUnlockstatement() {
    await this.page.getByTestId("lock-unlock-button").click();
  }

  // async waitForCommissionsSuccess() {
  //   const successSelector = this.page.getByText(
  //     "Commission Calculations Completed"
  //   );
  //   const partialFailureSelector = this.page.getByText(
  //     "Commission Calculations Failed Partially"
  //   );

  //   const isSuccess = await successSelector.isVisible({ timeout: 300000 });

  //   if (isSuccess) {
  //     console.log("Commission Calculations Completed");
  //   } else {
  //     const isPartialFailure = await partialFailureSelector.isVisible({
  //       timeout: 300000,
  //     });

  //     if (isPartialFailure) {
  //       console.error("Commission Calculations Failed Partially");
  //     } else {
  //       console.error("Neither success nor partial failure message appeared.");
  //       throw new Error("Commission process did not complete successfully.");
  //     }
  //   }
  // }

  async clickPlusbuttoninDraws() {
    await this.page
      .locator(".ant-tree-switcher.ant-tree-switcher_close")
      .first()
      .click();
  }

  async checkImpersonationandExit() {
    if (
      await this.page.getByText("Logged in as").isVisible({ timeout: 10000 })
    ) {
      try {
        await this.page.getByRole("button", { name: "Exit" }).click();
        console.log("Exited impersonation successfully");
        await this.page.waitForTimeout(5000);
      } catch {
        console.log("Unable to Exit Logged in user");
      }
    }
  }
}

export default ManagerEffectiveEndDate;
