import { expect } from "@playwright/test";
const path = require("path");
const fs = require("fs");

class TsarPage {
  constructor(page) {
    this.page = page;
  }
  async verifyClientName() {
    const CLIENT_NAME = await this.page.getByText("breakglass Metrics").first();
    await CLIENT_NAME.waitFor({ state: "visible", timeout: 10000 });
  }

  async supportMembership() {
    const MEMBERSHIP_TEXT = await this.page.locator(
      "//div[text()='Support membership ends in']"
    );
    await MEMBERSHIP_TEXT.waitFor({ state: "visible", timeout: 10000 });
  }

  async verifyAdminName() {
    const ADMIN_NAME = await this.page
      .getByText("Hi, everstage admin!")
      .first();
    await ADMIN_NAME.waitFor({ state: "visible", timeout: 10000 });
  }

  async closeMappayee() {
    await this.page.getByRole("button", { name: "Cancel" }).click();
  }

  async nextButton() {
    await this.page.getByRole("button", { name: "Next" }).click();
  }

  async logout() {
    await this.page.getByRole("button", { name: "Log out" }).click();
  }

  async navigateAuditLogs() {
    await this.page.waitForTimeout(10000);
    await this.page.goto("settings/commissions-and-data-sync", {
      timeout: 40000,
      waitUntil: "load",
    });
    const COMMISSION_TEXT = await this.page
      .locator("//span[text()='show past activities']")
      .first();
    await COMMISSION_TEXT.waitFor({ state: "visible", timeout: 50000 });
    await this.page.goto("/settings/audit-logs", {
      timeout: 40000,
      waitUntil: "load",
    });
    await this.page.waitForTimeout(5000);
    await this.page
      .getByRole("button", {
        name: "Export as CSV",
      })
      .waitFor({ state: "visible", timeout: 60000 });
  }

  async navigateAuditLogs1() {
    await this.page.waitForTimeout(10000);
    await this.page.goto("/settings/audit-logs", { waitUntil: "networkidle" });
  }

  async verifyAuditAction(action_param) {
    const audit_actual = [];
    const audit_user = [];
    let matchingIndex = -1;

    const AUDIT_ACTIONS = this.page.locator(
      "div [col-id='eventTypeName'] span"
    );
    const AUDIT_USERS = this.page.locator(
      "div [col-id='updatedByName'] span[class*='text-xs']"
    );

    // Get the count of audit actions
    const actionsCount = await AUDIT_ACTIONS.count();
    const usersCount = await AUDIT_USERS.count();

    for (let q = 0; q < actionsCount; q++) {
      const audit_value = await AUDIT_ACTIONS.nth(q).textContent();
      audit_actual.push(audit_value);

      // Check if the current action is the passing param
      if (audit_value === action_param) {
        matchingIndex = q;
        for (let k = 0; k < usersCount; k++) {
          const audit_user_value = await AUDIT_USERS.nth(k).textContent();
          audit_user.push(audit_user_value);
        }
      }
    }
    console.log("index", matchingIndex);
    return { audit_actual, audit_user, matchingIndex };
  }

  async createNewuser(email3, firstName, lastName, role) {
    await this.page.getByRole("button", { name: "New User" }).click();
    await this.page.getByPlaceholder("Enter Email").click();
    await this.page.getByPlaceholder("Enter Email").fill(email3);
    await this.page.getByPlaceholder("Enter First Name").click();
    await this.page.getByPlaceholder("Enter First Name").fill(firstName);
    await this.page.getByPlaceholder("Enter Last Name").click();
    await this.page.getByPlaceholder("Enter Last Name").fill(lastName);
    await this.page.getByLabel("Role*").click();
    await this.page.getByTestId(role).locator("div").first().click();
    await this.page.getByRole("button", { name: "Add User" }).click();
  }

  async verifyAdminLogin() {
    const ACCESS_DENIED = await this.page.getByText("Access Denied");
    await ACCESS_DENIED.waitFor({ state: "visible", timeout: 10000 });
  }

  /**
   * Navigate to Settings component
   *
   * @param {String} pageURL
   */
  async navigateToSettings(pageURL) {
    await this.page.goto(`/settings${pageURL}`, { waitUntil: "networkidle" });
  }

  /**
   * Add revenue leader in Basic Settings module
   */
  async addRevenueLeader() {
    await this.page.getByPlaceholder("Effective start date").click();
    const date = await this.getDate();
    await this.page.locator(`td[title='${date}']`).click();
    await this.page.getByTestId("ever-select").first().click();
    await this.page.getByText("edwina b", { exact: true }).click();
    await this.page.getByRole("button", { name: "Add" }).first().click();
    await this.page.locator("tr.ant-table-row").waitFor({ state: "visible" });
  }

  /**
   * Add FxRates in Basic Settings module
   */
  async addFxRates() {
    await this.page.getByTestId("ever-select").nth(1).click();
    await this.page.getByText("INR", { exact: true }).click();
    await this.page.getByPlaceholder("Start month").click();
    await this.page.locator('td[title="2024-01"]').click();
    await this.page.locator('td[title="2024-12"]').click();
    await this.page.getByRole("spinbutton").fill("10");
    await this.page.getByRole("button", { name: "Update" }).nth(1).click();
    await this.page
      .getByText("Updated Successfully!!", { exact: true })
      .first()
      .waitFor({ state: "visible" });
  }

  /**
   * Generate Tomorrow's Date
   *
   * @returns {String}
   */
  async getDate() {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    const year = tomorrow.getFullYear();
    const month = String(tomorrow.getMonth() + 1).padStart(2, "0");
    const day = String(tomorrow.getDate()).padStart(2, "0");
    const formattedDate = `${year}-${month}-${day}`;
    return formattedDate;
  }

  /**
   * Validate Audit Log's UserName and Description based on Action
   *
   * @param {String} userName
   * @param {String} Action
   * @param {String} Description
   */
  async validAuditLog(userName, Action, Description) {
    await this.page.waitForSelector("[col-id='summary'] div span");
    const AUDIT_ACTIONS = this.page.locator(
      "div [col-id='eventTypeName'] span"
    );
    const AUDIT_USERS = this.page.locator(
      "div [col-id='updatedByName'] span[class*='text-xs']"
    );
    const AUDIT_DESC = this.page.locator("[col-id='summary'] div span");
    const count = await AUDIT_ACTIONS.count();
    for (let i = 0; i < count; i++) {
      await AUDIT_ACTIONS.nth(i).textContent({ timeout: 100000 });
      if ((await AUDIT_ACTIONS.nth(i).textContent()) === Action) {
        expect(await AUDIT_USERS.nth(i).textContent()).toBe(userName);
        expect(await AUDIT_DESC.nth(i).textContent()).toBe(Description);
        console.log(await AUDIT_USERS.nth(i).textContent());
        console.log(await AUDIT_DESC.nth(i).textContent());
        break;
      }
    }
  }

  /**
   * Change Profile Card Section
   *
   */
  async changeProfileCard() {
    await this.page.locator("input[value='employee_id']").check();
    await this.page.waitForTimeout(10000);
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page
      .getByText("Updated successfully")
      .first()
      .waitFor({ state: "visible", timeout: 10000 });
  }

  /**
   * Create,Edit and Deleted Role in Role Settings Module
   *
   * @param {String} roleName
   */
  async createRole(roleName) {
    await this.page.getByText("New Role").click();
    await this.page.getByPlaceholder("Enter a role name").fill(roleName);
    await this.page
      .getByPlaceholder("Add optional description")
      .fill("Playwright Desc");
    await this.page.getByRole("button", { name: "Create Role" }).click();
    await this.page.getByText("Dashboard", { exact: true }).click();
    await this.page.getByLabel("View dashboards page").check();
    await this.page.getByText("Databooks", { exact: true }).click();
    await this.page.getByLabel("View databooks page").check();
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page.locator("svg.ant-dropdown-trigger").click();
    await this.page.getByRole("menuitem", { name: "delete" }).click();
    await this.page.getByRole("button", { name: "Yes, Confirm" }).click();
  }

  /**
   * Create,Edit and Deleted Custom Field in Custom Fields Settings Module
   *
   * @param {String} fieldName
   */
  async createCustomField(fieldName) {
    await this.page.getByRole("button", { name: "Add new field" }).click();
    await this.page.getByPlaceholder("Type label name").fill(fieldName);
    await this.page.locator("input[type='search']").click();
    await this.page.locator('span[title="Email"]').click();
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page.getByTestId("Alternate Email cf edit button").click();
    await this.page.getByLabel("Mark as mandatory").check();
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page.getByTestId("Alternate Email cf dots button").click();
    await this.page.getByRole("menuitem", { name: "Delete" }).click();
    await this.page.getByRole("button", { name: "OK" }).click();
    await this.page
      .getByText("Custom field deleted")
      .first()
      .waitFor({ state: "visible" });
  }

  /**
   * Modify Quyeries in Queries Settings module
   */
  async modifyQueries() {
    await this.page.getByRole("switch").uncheck();
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  /**
   * Create Approval Workflow in Approval Workflow Settings Module
   *
   * @param {String} flowName
   */
  async createWorkFlow(flowName) {
    await this.page.getByRole("button", { name: "Create Workflow" }).click();
    await this.page.getByPlaceholder("Enter workflow name").fill(flowName);
    await this.page.getByTestId("ever-select").click();
    await this.page
      .locator(".ant-select-item-option-content")
      .getByText("Payouts")
      .click();
    await this.page.getByPlaceholder("Choose approvers").click();
    await this.page.locator('span[title="Payee"]').click();
    await this.page
      .getByPlaceholder("Choose who needs to be notified")
      .first()
      .click();
    await this.page
      .locator("span[title]")
      .filter({ hasText: "All approvers" })
      .click();
    await this.page.getByRole("button", { name: "Save Workflow" }).click();
    await this.page
      .getByText("Workflow created successfully")
      .first()
      .waitFor({ state: "visible" });
  }

  async reloadPage() {
    await this.page.reload({ waitUntil: "networkidle" });
  }

  /**
   * Edit Workflow in Approval Workflow Settings Module
   *
   * @param {String} flowName
   */
  async editWorkflow(flowName) {
    const parentLocator = this.page
      .locator(`div > div > span`)
      .filter({ hasText: flowName })
      .locator("..")
      .locator("..");
    const editButton = await parentLocator
      .locator("+div>button>div")
      .filter({ hasText: "Edit" });
    await editButton.click();
    await this.page.getByPlaceholder("Enter description").fill("test");
    await this.page.getByRole("button", { name: "Save Workflow" }).click();
    await this.page
      .getByText("Workflow updated successfully")
      .first()
      .waitFor({ state: "visible" });
  }

  /**
   * Delete Workflow based on flowName in Apporval Workflow Settings Module
   *
   * @param {String} flowName
   */
  async deleteWorkflow(flowName) {
    const parentLocator = this.page
      .locator(`div > div > span`)
      .filter({ hasText: flowName })
      .locator("..")
      .locator("..");
    const deleteButton = await parentLocator.locator("+div>button>div>svg");
    await deleteButton.click();
    await this.page.getByText("Delete").click();
    await this.page.getByRole("button", { name: "Yes, Confirm" }).click();
    await this.page
      .getByText("Workflow deleted successfully")
      .first()
      .waitFor({ state: "visible" });
  }

  /**
   * Turn off Apporval in Approval Workflow Settings
   */
  async turnOffApproval() {
    await this.page.getByRole("tab", { name: "Settings" }).click();
    await this.page.getByRole("switch").first().click();
    await this.page.getByRole("button", { name: "Yes, change" }).click();
  }

  /**
   * Add Commission Adjustment in Adjustments Settings module
   *
   */
  async addCommissionAdjustment() {
    await this.page.getByRole("button", { name: "Add Adjustment" }).click();
    await this.page.getByRole("menuitem", { name: "Commission" }).click();
    await this.page.getByLabel("Payee").click();
    await this.page.getByTitle("chandler b").first().click();
    await this.page.getByLabel("Effective Period").click();
    await this.page.getByText("June 2024").first().click();
    await this.page.getByRole("spinbutton").fill("1000");
    await this.page.getByLabel("Commission Plan").click();
    await this.page
      .getByTitle("sample plan")
      .getByText("sample plan", { exact: true })
      .click();
    await this.page.getByRole("button", { name: "Submit" }).click();
    await this.page
      .getByText("Adjustment saved successfully")
      .first()
      .waitFor({ state: "visible" });
  }

  /**
   * Add Draw Adjustment in Adjustments Settings module
   *
   */
  async addDrawAdjustment() {
    await this.page.getByRole("button", { name: "Add Adjustment" }).click();
    await this.page.getByRole("menuitem", { name: "Recover Draw" }).click();
    await this.page.getByLabel("Payee").click();
    await this.page.getByTitle("chandler b").first().click();
    await this.page.getByLabel("Fiscal Year").click();
    await this.page.locator("div[label='2024']").click();
    await this.page.getByLabel("Period").click();
    await this.page.locator("div[label='Jan']").click();
    await this.page.getByLabel("Amount*").fill("10");
    await this.page.getByRole("button", { name: "Submit" }).click();
    await this.page
      .getByText("Adjustment saved successfully")
      .first()
      .waitFor({ state: "visible" });
  }

  /**
   * Add Connectors in Connectors module
   *
   * @param {String} connectorName
   */
  async addConnectors(connectorName) {
    await this.page
      .getByRole("button", { name: "Add new object", exact: true })
      .click();
    await this.page.getByText("From scratch").click();
    await this.page.locator("svg:left-of(:text('Edit'))").first().click();
    await this.page.getByPlaceholder("Enter name").fill(connectorName);
    await this.page.keyboard.press("Enter");
    await this.page.getByText("Email", { exact: true }).click();
    await this.page.getByPlaceholder("Enter field name").fill("email address");
    await this.page.locator("div.ant-dropdown-trigger").click();
    await this.page
      .getByRole("menuitem", { name: "Both Primary & Snapshot Key" })
      .click();
    await this.page.getByText("Number", { exact: true }).click();
    await this.page.getByPlaceholder("Enter field name").nth(1).fill("Amount");
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page.getByText(connectorName).click();
    await this.page.locator("[data-test-id='delete-field']").click();
    await this.page.getByRole("button", { name: "Cancel" }).click();
  }

  /**
   * Edit Connectors in Connector Module
   *
   * @param {String} connectorName
   */
  async editConnectors(connectorName) {
    await this.page.getByText(connectorName).click();
    await this.page.getByRole("button", { name: "Add more fields" }).click();
    await this.page.getByText("Number", { exact: true }).click();
    await this.page.getByPlaceholder("Enter field name").fill("Amount");
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  /**
   * Create,update,delete Data based on input variable {data}
   *
   * @param {String} data
   */
  async manageData(data) {
    let filepath;
    let taskName;

    switch (data) {
      case "Create":
        await this.page.waitForTimeout(5000);
        await this.page.getByText("Create new data", { exact: true }).click();
        filepath = "Create Data - Breakglass.csv";
        taskName = "Create New Records";
        break;
      case "Update":
        await this.page
          .getByText("Update existing data", { exact: true })
          .click();
        filepath = "Update Data - Breakglass.csv";
        taskName = "Update Records";
        break;
      case "Delete":
        await this.page.getByText("Delete data", { exact: true }).click();
        filepath = "Delete Data - Break glass.csv";
        taskName = "Delete Records";
        break;
      default:
        await this.page
          .getByText("Create new and update existing data", { exact: true })
          .click();
        filepath = "Create and update data - Breakglass.csv";
        taskName = "Create or Update Records";
        break;
    }

    await this.page.locator("span").filter({ hasText: "sales-obj" }).click();
    await this.page.getByRole("button", { name: "Next" }).click();
    const fileChooserPromise = this.page.waitForEvent("filechooser");
    await this.page.getByText("Browse").click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(
      path.join(__dirname, `/../upload-files/manageData-breakglass/${filepath}`)
    );
    await this.page.getByRole("button", { name: "Next" }).click();
    if (data === "Delete") {
      await this.page.getByRole("button", { name: "Next" }).click();
      await this.page.getByRole("button", { name: "Validate" }).click();
      await this.page.getByRole("button", { name: "Delete" }).click();
      await this.page.getByRole("button", { name: "Confirm" }).click();
    } else {
      await this.page.getByRole("button", { name: "Next" }).click();
      await this.page
        .locator("span[title='(DD-MMM-YYYY)']")
        .waitFor({ state: "visible", timeout: 5000 });
      await this.page.getByRole("button", { name: "Next" }).click();
      await this.page.getByRole("button", { name: "Validate" }).click();
      await this.page
        .getByText(`${filepath} file read successfully.`)
        .first()
        .waitFor({ state: "visible", timeout: 5000 });
      await this.page.getByRole("button", { name: "Import" }).click();
    }

    await this.page.getByPlaceholder("Email").fill("<EMAIL>");
    await this.page.getByRole("button", { name: "Submit" }).click();
    await this.page
      .getByText("We'll notify you via email once the import is complete.")
      .waitFor({ state: "visible" });
    await this.page.getByRole("button", { name: "Got it" }).click();
    await this.navigateToSettings("/manage-data");
    try {
      await this.page
        .locator("[row-id='0']>[col-id='taskName']")
        .filter({ hasText: taskName })
        .waitFor({ state: "visible", timeout: 200000 });
    } catch (error) {
      console.log("running Error Block");
      await this.page.reload();
      await this.page
        .locator("[row-id='0']>[col-id='taskName']")
        .filter({ hasText: taskName })
        .waitFor({ state: "visible", timeout: 200000 });
    }
    await this.page
      .locator("[row-id='0']>[col-id='object']")
      .filter({ hasText: "sales-obj" })
      .waitFor({ state: "visible", timeout: 200000 });
    try {
      await this.page
        .locator("[row-id='0']>[col-id='status']>div>div")
        .filter({ hasText: "Completed" })
        .waitFor({ state: "visible", timeout: 200000 });
    } catch (error) {
      console.log("running Error Block");
      await this.page.reload();
      await this.page
        .locator("[row-id='0']>[col-id='status']>div>div")
        .filter({ hasText: "Completed" })
        .waitFor({ state: "visible", timeout: 200000 });
    }
  }

  /**
   * Connect Contracts in Contracts Settings module
   */
  async connectContracts() {
    const [newTab] = await Promise.all([
      this.page.waitForEvent("popup"),
      this.page.getByRole("button", { name: "Connect" }).click(),
    ]);
    await newTab.waitForLoadState();
    await newTab.getByPlaceholder("Enter email").fill("<EMAIL>");
    await newTab.getByRole("button", { name: "Next" }).click();
    await newTab.getByPlaceholder("Enter password").fill("Applegirl2!");
    await newTab.getByRole("button", { name: "Log in" }).click();
    await newTab
      .locator("img~button>div")
      .filter({ hasText: "Connected" })
      .waitFor({ state: "visible" });
  }

  /**
   * Disconnect contract in Contracts Settings module
   */
  async disconnectContracts() {
    // await this.page.getByRole("button", { name: "Connected" }).click();
    // // await this.page.getByText("Document disconnected successfully").waitFor({state : "visible"});
    // await this.page
    //   .locator("img~button>div")
    //   .filter({ hasText: { text: "Connect", exact: true } })
    //   .waitFor({ state: "visible", timeout: 10000 });
    const ConnectButton2 = await this.page.getByRole("button", {
      name: "Connected",
    });
    await ConnectButton2.waitFor({ state: "visible", timeout: 60000 });

    const page2Promise = this.page.waitForEvent("popup");
    await ConnectButton2.click();

    const page2 = await page2Promise;
    await page2.waitForLoadState("networkidle");

    const connectbtn = await page2.getByRole("button", { name: "Connect" });
    await connectbtn.waitFor({ state: "visible", timeout: 70000 });
  }

  /**
   * Navigate to Draws
   */
  async navigateToDraws() {
    await this.page.goto("/draws", { waitUntil: "networkidle" });
  }

  async addDrawSchedule(userName) {
    await this.page.locator("span.ant-tree-switcher_close").click();
    await this.page.getByTitle(userName).getByText(userName).click();
    await this.page.getByRole("button", { name: "Add Draw Schedule" }).click();
    await this.page.getByTestId("ever-select").first().click();
    await this.page.getByTitle("Jan").getByText("Jan").click();
    await this.page.getByTestId("ever-select").nth(1).click();
    // await this.page.getByTitle('Jan').getByText('Non-recoverable',{exact : true}).click();
    await this.page
      .locator("div[role='listitem']>div>span[title='Non-recoverable']")
      .click();
    await this.page.getByPlaceholder("Enter Amount").fill("1000");
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page
      .getByText("Draws Added Successfully")
      .first()
      .waitFor({ state: "visible" });
  }

  async modifyDraws(userName) {
    await this.page.locator("span.ant-tree-switcher_close").click();
    await this.page.getByTitle(userName).getByText(userName).click();
    await this.page.getByRole("button", { name: "Modify" }).click();
    await this.page.locator("button.link>div>div>svg").first().click();
    await this.page.getByRole("button", { name: "Yes, remove" }).click();
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page
      .getByText("Draws Added Successfully")
      .first()
      .waitFor({ state: "visible" });
  }
  /**
   * Validate Audit log for super admin with support user access
   *
   * @param {String} userName
   * @param {String} userEmail
   * @param {String} Action
   * @param {String} Description
   */
  async validAuditLogSupport(userName, userEmail, Action, Description) {
    // await this.page.waitForSelector("[col-id='summary'] div span");
    const AUDIT_ACTIONS = this.page.locator(
      "div [col-id='eventTypeName'] span"
    );
    const AUDIT_EMAIL = this.page.locator(
      "div [col-id='updatedByName'] span[class*='text-xs']"
    );
    const AUDIT_USERS = this.page.locator(
      "div [col-id='updatedByName'] span[class*='text-base']"
    );
    const AUDIT_DESC = this.page.locator("[col-id='summary'] div span");
    const count = await AUDIT_ACTIONS.count();
    for (let i = 0; i < count; i++) {
      await AUDIT_ACTIONS.nth(i).textContent({ timeout: 100000 });
      if ((await AUDIT_ACTIONS.nth(i).textContent()) === Action) {
        expect(await AUDIT_USERS.nth(i).textContent()).toBe(userName);
        expect(await AUDIT_EMAIL.nth(i).textContent()).toBe(userEmail);
        expect(await AUDIT_DESC.nth(i).textContent()).toBe(Description);
        console.log(await AUDIT_USERS.nth(i).textContent());
        console.log(await AUDIT_DESC.nth(i).textContent());
        break;
      }
    }
  }

  /**
   * Import/Ignore HRIS Users
   *
   */
  async importHRISUsers() {
    await this.page
      .getByText("No User Updates to Review", { exact: true })
      .waitFor({ state: "visible" });
    await this.page.getByText("New users", { exact: true }).click();
    await this.page.waitForLoadState("networkidle");
    await this.page.locator("input[type='checkbox']").first().click();
    await this.page.getByRole("button", { name: "Import 1 Record(s)" }).click();
    await this.page.getByRole("button", { name: "Yes, proceed" }).click();
    await this.page.getByPlaceholder("Email").fill("<EMAIL>");
    await this.page.getByRole("button", { name: "Submit" }).click();
    await this.page
      .getByText("Data Imported successfully", { exact: true })
      .first()
      .waitFor({ state: "visible" });
  }
  async ignoreHRISUsers() {
    await this.page
      .getByText("No User Updates to Review", { exact: true })
      .waitFor({ state: "visible" });
    await this.page.getByText("New users", { exact: true }).click();
    await this.page.waitForLoadState("networkidle");
    await this.page.locator("input[type='checkbox']").first().click();
    await this.page.getByRole("button", { name: "Ignore 1 Record(s)" }).click();
    await this.page.getByRole("button", { name: "Yes, proceed" }).click();
    await this.page
      .getByText("Data Imported successfully", { exact: true })
      .first()
      .waitFor({ state: "visible" });
  }

  async pageClose() {
    await this.page.close();
  }

  async explictWait() {
    await this.page.waitForTimeout(10000);
  }
}

export default TsarPage;
