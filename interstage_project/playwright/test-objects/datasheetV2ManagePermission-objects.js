class DatasheetV2ManagePermission {
  constructor(page) {
    this.page = page;
  }

  async goToRoles() {
    await this.page.goto("/settings", { waitUntil: "networkidle" });
    await this.page.locator('a:has-text("Roles")').click();
  }

  async clickRole(roleName) {
    await this.page
      .locator(`//div[@role='listitem'][.//span[text()='${roleName}']]`)
      .click();
  }

  async clickEditRoles() {
    await this.page.getByRole("button", { name: "Edit" }).click();
  }

  async enableCheckBox(labelName) {
    const checkboxLocator = this.page.locator(
      `label:has-text("${labelName}") input[type='checkbox']`
    );
    const isChecked = await checkboxLocator.isChecked();
    if (!isChecked) {
      await checkboxLocator.click();
    }
  }

  async disableCheckBox(labelName) {
    const checkboxLocator = this.page.locator(
      `label:has-text("${labelName}") input[type='checkbox']`
    );
    const isChecked = await checkboxLocator.isChecked();
    if (isChecked) {
      await checkboxLocator.click();
    }
  }

  async isCheckBoxChecked(labelName) {
    const checkboxLocator = this.page.locator(
      `label:has-text("${labelName}") input[type='checkbox']`
    );
    return await checkboxLocator.isChecked();
  }

  async clickRoleModule(moduleName) {
    await this.page
      .locator(`//div[@role='listitem'][.//span[text()='${moduleName}']]`)
      .click();
  }

  async saveRoles() {
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async loginAsUser(userEmail) {
    await this.page
      .locator(`button[data-testid='${userEmail} users dd button']`)
      .click();
    await this.page.locator(`button:has-text("Login as user")`).click();
    await this.page.waitForLoadState("networkidle");
  }

  async exitLoginAsUser() {
    await this.page.getByRole("Button", { name: "Exit" }).click();
    await this.page.waitForTimeout(10000);
  }

  async addPermissionSet(
    index,
    userType,
    userEmail,
    columnTitle,
    addAnotherPermissionSet = false
  ) {
    await this.page.waitForTimeout(500);
    await this.page
      .locator(
        '//div[@data-testid="ever-select" and .//span[text()="Select by users and groups..."]]'
      )
      .last()
      .click();
    await this.page
      .locator(`//div[@role='tab' and contains(text(), '${userType}')]`)
      .last()
      .click();
    if (userType === "Users") {
      await this.page.getByPlaceholder("Search").last().click();
      await this.page.getByPlaceholder("Search").last().fill(userEmail);
      await this.page.locator(`li:has-text("${userEmail}")`).click();
    } else if (userType === "Groups") {
      await this.page.locator(`span:has-text("${userEmail}")`).click();
    }
    // await this.page.locator(`li:has-text("${userEmail}")`).click();
    await this.page.click("body", { position: { x: 0, y: 0 } });
    await this.page
      .locator(
        `//div[@data-testid="ever-select" and .//span[text()="Select columns to hide"]]`
      )
      .click();
    await this.page.locator(`div[title="${columnTitle}"]`).last().click();
    await this.page.click("body", { position: { x: 0, y: 0 } });
    if (addAnotherPermissionSet) {
      await this.page.getByText("Add New Permission Set").click();
    }
  }

  async editPermissionName(index, newName) {
    await this.page
      .locator(
        `//div[@class='ml-2 flex items-center justify-center cursor-pointer']`
      )
      .nth(index)
      .click();
    const inputField = await this.page
      .locator(`.ant-collapse-header input`)
      .last();
    await inputField.fill("");
    await inputField.fill(newName);
    await this.page
      .locator(
        `//div[@class='ml-2 flex   items-center justify-center cursor-pointer']`
      )
      .nth(0)
      .click();
  }

  async deletePermissionSet(index) {
    await this.page.locator('//div[@class="ml-auto"]').nth(index).click();
    await this.page
      .locator(
        `.ant-dropdown-menu li.ant-dropdown-menu-item span:has-text("Delete")`
      )
      .nth(index + 1)
      .click();
  }

  async clonePermissionSet(index) {
    await this.page.locator('//div[@class="ml-auto"]').nth(index).click();
    await this.page
      .locator(
        `.ant-dropdown-menu li.ant-dropdown-menu-item span:has-text("Clone")`
      )
      .nth(index + 1)
      .click();
  }

  async validateImpersonation(payee_name) {
    await this.page.waitForLoadState("networkidle");
    await this.page.waitForTimeout(5000);
    return this.page.getByText("Logged in as " + payee_name + "").count();
  }

  async rowPermissionColumn(ColumnName) {
    await this.page
      .locator(`span[role="img"].anticon.anticon-close-circle`)
      .nth(1)
      .click();
    await this.page
      .locator(`div[data-testid="ever-select"]:has(span:text-is("Column"))`)
      .click();
    await this.page
      .locator(`div.overflow-hidden > span[title="${ColumnName}"]`)
      .nth(1)
      .click();
  }

  async rowPermissionOperator(Operator) {
    await this.page
      .locator(`span[role="img"].anticon.anticon-close-circle`)
      .nth(2)
      .click();
    await this.page
      .locator(`div[data-testid="ever-select"]:has(span:text-is("Operator"))`)
      .click();
    await this.page
      .locator(`div.overflow-hidden > span[title="${Operator}"]`)
      .nth(0)
      .click();
  }

  async performRowPermissionOperators(operators) {
    for (const operator of operators) {
      await this.rowPermissionOperator(operator);
    }
  }
}

export default DatasheetV2ManagePermission;
