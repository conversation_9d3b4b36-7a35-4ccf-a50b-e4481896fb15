import { expect } from "@playwright/test";
const path = require("path");

// Share Page objects

// Commission Plan Page Objects
class planperiodexclusion {
  constructor(page) {
    this.page = page;
  }

  /**
   * Navigate to plans / users and Exit if logged in as Impersonate user when given true
   *
   * @param {Bo<PERSON>an} flag
   */
  async navigate(flag, path) {
    await Promise.all([
      this.page.waitForNavigation({ waitUntil: "networkidle" }),
      this.page.goto(path, { waitUntil: "networkidle" }),
    ]);
  }

  async navigation(path) {
    await this.page.goto(path);
  }

  async excludePeriod() {
    const planExclusion = await this.page.locator(
      "//div[@row-id='0']//div[@col-id='planExclusion']"
    );
    await planExclusion.hover();
    await planExclusion.locator("svg").nth(0).click();
    await this.page.getByRole("button", { name: "Exit Canvas" }).click();
  }

  async runCommissionSync() {
    await this.page.getByLabel("Payees in Commission Plan").check();
    await this.page.getByTestId("ever-select").locator("div").nth(1).click();
    await this.page.getByText("Commission Plan", { exact: true }).click();
    await this.page.getByPlaceholder("Select date").click();
    await this.page.getByPlaceholder("Select date").fill("Feb 01, 2024");
    await this.page.keyboard.press("Enter");
    await this.page.getByLabel("Run commission sync for the").check();
    await this.page.getByRole("button", { name: "Run", exact: true }).click();
    await this.page.getByRole("button", { name: "Skip & Run" }).click();
  }

  async setPayoutDate(Date) {
    await this.page.getByTitle(Date).locator("div").click();
  }

  async getIntostatements(payeeName) {
    await this.page
      .getByTestId(payeeName)
      .getByRole("link", { name: "Payee" })
      .click();
  }

  async earnedCommissionsComponent() {
    await this.page.getByText("Commission Summary").click();
    await this.page.getByText("Earned Commissions").click();
    await this.page.getByText("Commission Plan").click();
    await this.page.getByRole("button", { name: "Quota" }).click();
  }

  async payoutSummaryComponent() {
    await this.page.getByText("Payout Summary").click();
    await this.page
      .getByRole("row", { name: " Payout from current period $" })
      .click();
    await this.page
      .getByRole("gridcell", { name: " Commission Plan" })
      .click();
    await this.page.getByRole("button", { name: "Quota" }).click();
  }
}
/**
 * Share the plan to all users and validate whether the added users are displaying in the shared screen
 *
 * @param {String} planName
 * @returns Total Number of users in Share Screen
 */

module.exports = planperiodexclusion;
