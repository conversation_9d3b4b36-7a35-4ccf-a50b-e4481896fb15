/* eslint-disable playwright/no-wait-for-timeout */
import { expect } from "@playwright/test";
import BasePage from "./basePage.js";

const testData = JSON.parse(
  JSON.stringify(require("../../testData/testData.json"))
);

class ProductPage extends BasePage {
  constructor(
    page,
    {
      productNames = [
        "Connector Charges",
        "Everstage Platform",
        "Standard White Glove Support",
        "One-time Implementation",
        "Premium White Glove Supporter",
      ],
      pricePointOpt = "Per Unit - USD - Monthly",
      colHead = "product_name",
      rowHeads = [
        "CONNECTOR",
        "PLATFORM",
        "SUPPORT",
        "IMPLEMENTATION",
        "PREMIUMSUPPORT",
      ],
    } = {}
  ) {
    super(page);
    this.selectProduct = "//div[text()='Select Product']";
    this.addProductButton = page.getByRole("button", { name: "Add Product" });
    this.productNames = productNames;
    this.products = productNames.map((name) =>
      page.getByText(name, { exact: true })
    );
    // select all products in product page
    this.selectAll = "input[name='selectAll']";
    this.bulkDiscountButton = "button:has-text('Discount')";
    this.percentageField = "input[placeholder='In Percentage']";
    this.add = ".ant-modal-footer button:has-text('Add Product')";
    this.durationField =
      "//span[text()='Duration']/following::button[@aria-haspopup='dialog']";
    this.quantity = ".ant-input-number-input[name='quantity']";
    this.listUnitPrice = ".ant-input-number-input[name='listUnitPrice']";
    this.netUnitPrice = ".ant-input-number-input[name='netUnitPrice']";
    this.discountPercent = ".ant-input-number-input[name='discountPercent']";
    this.updateButton = "//div[normalize-space()='Update']";
    this.pricePoint = "div[name='pricePoint'] div[class='ant-select-selector']";
    this.pricePointOption = `span[title='${pricePointOpt}']`;
    this.publishButton = page.getByRole("button", { name: "Publish" });
    this.exitButton = page.getByRole("button", { name: "Exit" });
    this.errorMsg = "//span[contains(text(), 'Please fill missing info')]";
    this.productTable = "div[col-id='product_name']";
    this.approvalModal = "span:has-text('Approval Required')";
    this.sendApproval = page.getByRole("button", { name: "Send for approval" });
    // all products in product page including table header and 'add product' row
    this.productParentSelector = `div[col-id='${colHead}']`;
    this.productRow = rowHeads.map((rowHead) => `div[row-id='${rowHead}']`);
    this.removeButton = page.getByRole("button", { name: "Remove" });
    this.yesRemoveButton = page.getByRole("button", { name: "Yes, remove" });
    this.cancelButton = page.getByRole("button", { name: "Cancel" });
    this.editBtn = page.getByRole("button", { name: "Edit" });
    this.addPhaseBtn = page.getByRole("button", {
      name: "Add subscription phase",
    });
  }

  // Select product
  async clickSelectProduct() {
    await this.click(this.selectProduct);
  }

  //
  // add product
  async addProduct() {
    await this.waitForElement(this.addProductButton, 20000);
    await this.click(this.addProductButton);
  }

  // add product from product table 'add product'
  async addProductFromRow() {
    const addProduct = await this.page.locator("div[col-id='product_name'] > div", {
      hasText: "Add Product",
    });
    await addProduct.click();
  }

  // Method to select a products to add
  async selectProducts() {
    for (const product of this.products) {
      await this.click(product);
    }
  }

  // Method to add selected products
  async clickAdd() {
    await this.click(this.add);
    await this.wait(3000);   
  
  }

  async clickDuration() {
    await this.click(this.durationField);
  }

  /**
   * Set the duration
   * @param {String} fromDurationType - the 'duration' already set
   *  @param {String} toDurationType - the 'duration' you want to
   */
  async setSubscriptionPeriod(fromDurationType, toDurationType) {
    const from = this.page.locator(
      `div[class='ant-select-selector'] span[title='${fromDurationType}']`
    );
    const to = this.page.locator(
      `div[class='ant-select-selector'] span[title='${toDurationType}']`
    );
    await this.click(from);
    await this.click(to);
  }

  /**
   * Set the duration
   * @param {String} durationType - In years/In months
   *  @param {integer} value - duration value
   */
  async setPeriodValue(durationType, value) {
    const input = `input[placeholder='${durationType}']`;
    await this.fill(input, value);
    await this.keyPress(input, "Enter");
  }

  // Delete product
  async deleteProduct() {
    const row = this.page.locator(this.productRow, { exact: true });
    const checkbox = await row.locator(
      "div[col-id='checkbox'] input[type='checkbox']"
    );
    await this.click(checkbox);
    await this.click(this.removeButton);
    await this.click(this.yesRemoveButton);
  }

  async doubleClickPublish() {
    await this.doubleClick(this.publishButton);
  }

  async updateBulkDiscount(percentage) {
    await this.click(this.selectAll);
    await this.click(this.bulkDiscountButton);
    await this.fill(this.percentageField, percentage);
    await this.keyPress(this.percentageField, "Enter");
    await this.wait(10000);
  }

  async validateDiscountValue() {
    const row = await this.page.locator(this.productRow);
    const updatedDiscount = await row.locator(
      "div[col-id='net_unit_price'] div > div > span + span"
    );

    const updatedDiscountText = await updatedDiscount.textContent();
    const numericValue = parseFloat(
      updatedDiscountText.replace(/[^\d.-]/g, "")
    );
    return Math.abs(numericValue);
  }

  async getDurationForAddedProducts() {
    const durations = [];

    for (const rowSelector of this.productRow) {
      const row = this.page.locator(rowSelector);
      const duration = await row.locator("div[col-id='duration'] div");
      const durationText = await duration.textContent();
      durations.push(durationText);
    }
    return durations;
  }

  async updateListUnitPrice(price) {
    await this.click(this.listUnitPrice);
    await this.click(this.listUnitPrice, price);
  }

  async selectPricePoint(title) {
    // const pricePointOption = `span[title='${title}']`;
    this.click(this.pricePoint);
    this.click(pricePointOption);
  }

  async validateAddedProducts() {
    const productParentElements = await this.page.locator(
      this.productParentSelector
    );
    const products = [];

    for (const productName of this.productNames) {
      const childDiv = await productParentElements.locator('div', {
        hasText: new RegExp(`^${productName}$`)
      });

      // Get the text content of the child div
      const product = await childDiv.textContent();
      // Trim the product name and check if it matches any product in the productNames array
      const trimmedProductName = product.trim();
      // push it to the products array
      if (this.productNames.includes(trimmedProductName)) {
        products.push(trimmedProductName);
      }
    }
    const expectedProducts = this.productNames; // Use the productNames array as expected
    return { products, expectedProducts };
  }

  async updateProductDetails(value, dataType = "initial") {
    await this.wait(3000);
    for (const productName of this.productNames) {
      const productLocator = await this.products[
        this.productNames.indexOf(productName)
      ];
      await this.click(productLocator);
      // If the product is "Everstage Platform", select the price point
      if (productName === "Everstage Platform") {
        await this.click(this.pricePoint); // Open dropdown
        await this.click(this.pricePointOption); // Select option
      }
      const { quantity, discount, price } = value[productName][dataType];
      // Update fields inside modal
      await this.fill(this.quantity, quantity);
      await this.fill(this.discountPercent, discount);
      await this.fill(this.listUnitPrice, price);
      // Click update button
      await this.click(this.updateButton);
    }
  }

  // Subtotal of phase
  async validateSubTotal() {
    await this.wait(3000);
    const subTotalElements = await this.page
      .locator("//span[text()='Sub Total:']/following-sibling::span")
      .all();
    const subTotalValues = await Promise.all(
      subTotalElements.map(async (el) => {
        const text = await el.textContent();
        return text ? text.trim() : "Not Found";
      })
    );

    console.log("Phase 1 Sub Total:", subTotalValues[0]);
    console.log("Phase 2 Sub Total:", subTotalValues[1]);
  }

  async validateTotalValueDisplayed(keyName) {
    // Locate the key using its text content
    const value = await this.page.locator(`span:has-text("${keyName}") + span`);
    const valueText = await value.textContent();
    console.log(`${keyName}: ${valueText.trim()}`);
  }

  // Get units, list and net price
  async getProductValues(colHeads) {
    const result = [];

    for (const rowSelector of this.productRow) {
      const row = await this.page.locator(rowSelector);
      const rowData = {};

      for (const colHead of colHeads) {
        if (colHead === "quantity") {
          try {
            const unitLocator = await row.locator(
              `div[col-id='${colHead}'] > div`
            );
            const unitValues = await unitLocator.textContent();
            rowData[colHead] = { units: unitValues };
          } catch (error) {
            console.error(
              `Error fetching 'quantity' for row: ${rowSelector} - ${error.message}`
            );
            rowData[colHead] = { units: "Error fetching data" };
          }
          continue;
        } else if (colHead === "list_unit_price") {
          try {
            const colLocator = await row.locator(
              `div[col-id='${colHead}'] > div > div`
            );
            await colLocator.waitFor({ state: "visible", timeout: 10000 });
            const divValue = await colLocator.textContent();
            rowData[colHead] = { listUnit: divValue };
          } catch (error) {
            console.error(
              `Error fetching 'list_unit_price' for row: ${rowSelector} - ${error.message}`
            );
            rowData[colHead] = { listUnit: "Error fetching data" };
          }
          continue;
        } else {
          try {
            const colLocator = await row.locator(
              `div[col-id='${colHead}'] > div > div`
            );
            await colLocator.waitFor({ state: "visible", timeout: 10000 });
            const divValue = await colLocator.textContent();
            rowData[colHead] = { netUnitPrice: divValue };
          } catch (error) {
            console.error(
              `Error fetching 'net_unit_price' for row: ${rowSelector} - ${error.message}`
            );
            rowData[colHead] = { netUnitPrice: "Error fetching data" };
          }
          continue;
        }
      }
      result.push(rowData);
    }

    return result;
  }

  async clickUpdate() {
    await this.click(this.updateButton);
  }

  async clickPublish() {
    await this.click(this.publishButton);
    await this.wait(2000);
    // if approval required
    if (await this.page.locator(this.approvalModal).isVisible()) {
      await this.click(this.sendApproval);
    }
  }

  async clickExit() {
    // await this.waitForElement(this.exitButton, 5000);
    await this.click(this.exitButton);
  }

  async verifyErrorMessage() {
    await this.waitForElement(this.errorMsg, 10000);
    const actualText = await this.page.locator(this.errorMsg).textContent(); // Extract text
    console.log("Error Message:", actualText.trim());
    expect(actualText).toContain(testData.errorMessagePriceNotSet);
  }

  // Select duration (Years / months / Forever)
  async selectSubscriptionPeriod(period) {
    await this.page
      .locator('button:right-of(:text("Duration"))')
      .first()
      .click();
    await this.page
      .locator("[role='dialog'] [data-testid='ever-select']")
      .click();
    await this.page
      .locator("span.inline-block.w-full.truncate", { hasText: period })
      .click({ waitUntil: "networkidle" });
    await this.page.waitForTimeout(1000);
    await this.page.keyboard.press("Enter");
  }

  // give exact duration for validation (eg : 1 Year [exact match case needed])
  async validateSubsPeriod(period) {
    const button = this.page
      .locator('button:right-of(:text("Duration"))')
      .first();
    await expect(button).toHaveText(period);
  }

  // select duration period 1 & switch to duration period 2 (eg : Years to Months)
  async switchPeriods(period1, period2) {
    await this.selectSubscriptionPeriod(period1);
    await this.page.waitForLoadState("networkidle");
    await this.selectSubscriptionPeriod(period2);
    await this.page.waitForLoadState("networkidle");
  }

  // Use only for Years/Months (Changes the period & number)
  async changeDuration(period, noOfPeriods) {
    await this.page
      .locator('button:right-of(:text("Duration"))')
      .first()
      .click();
    await this.page
      .locator("[role='dialog'] [data-testid='ever-select']")
      .click();
    await this.page
      .locator("span.inline-block.w-full.truncate", { hasText: period })
      .click({ waitUntil: "networkidle" });
    if (period === "Months") {
      await this.page
        .locator('input[placeholder="In months"]')
        .fill(noOfPeriods);
    } else {
      await this.page
        .locator('input[placeholder="In years"]')
        .fill(noOfPeriods);
    }
    await this.page.keyboard.press("Enter");
  }

  // Enter Existing date to locate & change it to new date (changes subscription starting date only)
  async changeSubscriptionStartDate(date) {
    await this.page
      .locator("(//input[@placeholder='Select date'])[2]")
      .first()
      .click();
    await this.page.waitForTimeout(5000);
    await this.page
      .locator("(//input[@placeholder='Select date'])[2]")
      .first()
      .fill(date);
    await this.page.waitForTimeout(5000);
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(5000);
  }

  // validates the change in subscription start period
  async validateSubStartPeriod(date) {
    const dateInput = this.page
      .locator("(//input[@placeholder='Select date'])[2]")
      .first();

    await expect(dateInput).toHaveAttribute("title", date);
    await this.page.waitForTimeout(1000);
  }

  // add +2 to the phase number you want to change
  async changeSubscriptionPhaseDate(phase, date) {
    await this.page
      .locator(`(//input[@placeholder='Select date'])[${phase}]`)
      .first()
      .click();
    await this.page.waitForTimeout(3000);
    await this.page
      .locator(`(//input[@placeholder='Select date'])[${phase}]`)
      .first()
      .fill(date);
    await this.page.keyboard.press("Enter");
    await this.page.waitForLoadState("networkidle");
  }

  // validates the change in subscription phase
  async validateSubPhaseDateChange(phase, date) {
    const Datebutton = this.page
      .locator(`(//input[@placeholder='Select date'])[${phase}]`)
      .first();
    await expect(Datebutton).toHaveAttribute("title", date);
    await this.page.waitForTimeout(1000);
  }

  // To edit an approved plan
  async clickEditBtn() {
    await this.click(this.editBtn);
    await this.page.waitForLoadState("networkidle");
  }

  // Use to exit a quote without publishing the changes
  async clickExitBtnInPublish() {
    await this.page
      .getByRole("button", { name: "Exit" })
      .click({ waitForLoadState: "networkidle" });
    await this.page
      .getByRole("button", { name: "Yes, exit" })
      .click({ waitForLoadState: "networkidle" });
  }

  // Add a phase with the + button in product page
  async addSubscriptionPhase() {
    await this.click(this.addPhaseBtn);
  }

  // validates that a new phase has been added after using addSusbscriptionPhase()
  async validatePhaseAddition(phaseName) {
    await expect(
      this.page.locator(`//div[div/span[text() = '${phaseName}']]`)
    ).toBeVisible();
  }

  // delete a phase with the particular name given to the phase
  async deleteSubscriptionPhase(phaseName) {
    const tripleDotsButton = this.page.locator(
      `//div[div/div/span[text() = '${phaseName}']]//button`
    );
    await tripleDotsButton.waitFor({ state: "visible" });
    await tripleDotsButton.click();
    await this.page.getByText("Remove").click();
    await this.page.getByRole("button", { name: "Yes, remove" }).click();
  }

  // Enter the deleted phase name to validate
  async validatePhaseDelete(phaseName) {
    await expect(
      this.page.locator(`//span[text() = '${phaseName}']`)
    ).toHaveCount(0);
  }

  // checks for the count in select product page
  async checkProductCount(productName, expCount) {
    await expect(
      this.page.locator(`//div/div[text() = '${productName}']`)
    ).toHaveCount(expCount);
  }
}
export default ProductPage;
