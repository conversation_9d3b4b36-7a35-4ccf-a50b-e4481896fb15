class ManualUploadRevamp {
  constructor(page) {
    this.page = page;
  }

  async navigate(URL) {
    await this.page.goto(URL, { waitUntil: "networkidle" });
  }

  async selectUploadMode(mode) {
    await this.page.locator(`//span[.='${mode}']`).click();
  }

  async selectObject(objectName) {
    await this.page.locator(`//span[.='${objectName}']`).click();
  }

  async clickNextButton() {
    await this.page.getByRole("button", { name: "Next" }).click();
  }

  async clickBrowse() {
    await this.page.getByText("Browse").click();
  }

  async selectColumnField(fieldName) {
    await this.page.getByTitle(fieldName).click();
  }

  async selectDropdownValue(dropdownValue) {
    await this.page
      .locator(".ant-select-dropdown:not(.ant-select-dropdown-hidden) span")
      .getByText(dropdownValue)
      .click();
  }

  async clickValidate() {
    await this.page.getByRole("button", { name: "<PERSON>idate" }).click();
  }

  async selectDateFormatDropdownEmpty() {
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select date format" })
      .click();
  }

  async selectDateFormatDropdownPrefilled(format) {
    await this.page.getByText(format).click();
  }

  async selectDateFormat(format) {
    await this.page.getByText(format).click();
  }

  async getErrorMessage() {
    return await this.page
      .locator("div[col-id='issue'] div.text-ever-error")
      .innerText();
  }

  async getAllErrors() {
    return await this.page
      .locator("div[col-id='issue'] div.text-ever-error")
      .allInnerTexts();
  }

  async getIgnored() {
    return await this.page
      .locator("span:right-of(span:text('Ignored'))")
      .first()
      .innerText();
  }

  async clickDownloadTemplate() {
    await this.page.getByRole("button", { name: "Download template" }).click();
  }

  async closeUploadWizard() {
    await this.page.locator("button.ant-drawer-close").click();
    await this.page.getByRole("button", { name: "Confirm" }).click();
  }

  async switchObjectType(objectType) {
    await this.page.getByText(objectType).click();
  }

  async closeValidationModal() {
    await this.page.locator("button").filter({ hasText: "Close" }).click();
  }

  async clickViewDetails() {
    await this.page
      .locator("(//div[contains(text(),'View details')])[1]")
      .click();
  }

  async clickimport() {
    await this.page.getByRole("button", { name: "Import" }).click();
    await this.page.getByPlaceholder("Email").fill("<EMAIL>");
    await this.page.getByRole("button", { name: "Submit" }).click();
    await this.page.getByRole("button", { name: "Got it" }).click();
    await this.page.getByRole("gridcell", { name: "Running" }).first().waitFor({
      state: "visible",
      timeout: 20000,
    });
    await this.page.getByRole("gridcell", { name: "Running" }).first().waitFor({
      state: "hidden",
      timeout: 20000,
    });
    await this.page
      .getByRole("gridcell", { name: "Completed" })
      .first()
      .waitFor({
        state: "visible",
        timeout: 20000,
      });
  }
}

export default ManualUploadRevamp;
