import CommonUtils from "./common-utils-objects";

class QuotaAnnualED {
  constructor(page) {
    this.page = page;
  }

  async navigate(URL) {
    await this.page.goto(URL, { waitUntil: "networkidle" });
  }

  async setSplitAnnualQuota(
    managerName,
    payeeName,
    splitMonth,
    oldValue,
    splitedValue
  ) {
    await this.page
      .locator(`div.employeeTreeLevel_${managerName} span.ant-tree-switcher`)
      .click();
    await this.page.getByText(payeeName, { exact: true }).click();
    await this.page
      .locator("(//*[name()='svg'][@class='w-4 h-4 hover:cursor-pointer'])[1]")
      .click();
    await this.page
      .getByRole("button", { name: "Update Quota for a New Date" })
      .click();
    const commonPage = new CommonUtils(this.page);
    await commonPage.setSelectMonthComboBox(splitMonth);
    await this.page.locator(`(//input[@value='${oldValue}'])[3]`).click();
    await this.page
      .locator(`(//input[@value='${oldValue}'])[3]`)
      .fill(splitedValue);
  }

  async setSplitAnnualQuotachangedSF(splitMonth, oldValue, splitedValue) {
    await this.page
      .getByRole("button", { name: "Update Quota for a New Date" })
      .click();
    const commonPage = new CommonUtils(this.page);
    await commonPage.setSelectMonthComboBox(splitMonth);
    await this.page.locator(`(//input[@value='${oldValue}'])[3]`).click();
    await this.page
      .locator(`(//input[@value='${oldValue}'])[3]`)
      .fill(splitedValue);
  }

  async selectPayeeQuota(managerName, payeeName) {
    await this.page
      .locator(`div.employeeTreeLevel_${managerName} span.ant-tree-switcher`)
      .click();
    await this.page.getByText(payeeName, { exact: true }).click();
  }

  async saveQuota() {
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page
      .getByText("Added/Modified Quota Successfully")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async getQuotaErrosion() {
    return await this.page
      .locator(
        "(//span[@class='text-base !font-[IBM Plex Sans] self-end font-medium text-ever-base-content-mid'])[1]"
      )
      .innerText();
  }

  async unlockStatements() {
    await this.page.getByTestId("lock-unlock-button").click();
    await this.page
      .getByText("Statement unlocked successfully")
      .waitFor({ state: "visible", timeout: 5000 });
  }

  async switchperiod(currentPeriod, nextPeriod) {
    await this.page.getByTestId("ever-select").getByText(currentPeriod).click();
    await this.page.getByText(nextPeriod).click();
  }

  async clickCriteria(criteriaName) {
    await this.page
      .locator("span")
      .filter({ hasText: criteriaName })
      .first()
      .click();
  }

  async clickSimulateBtn() {
    await this.page.getByRole("button", { name: "Simulate" }).click();
  }

  async simulateCommissions(startDate, endDate) {
    await this.page.getByPlaceholder("Start date").click();
    await this.page.getByPlaceholder("Start date").fill(startDate);
    await this.page.getByPlaceholder("Start date").press("Enter");
    await this.page.getByPlaceholder("End date").fill(endDate);
    await this.page.getByPlaceholder("End date").press("Enter");
    await this.page.getByRole("button", { name: "Run" }).click();
  }

  async clickViewProfile() {
    await this.page.getByText("View profile").click();
  }

  async switchTab(tabName) {
    await this.page.getByRole("tab", { name: tabName }).click();
  }

  async getAllTiers() {
    return await this.page
      .locator("div[col-id='originalTierName'] span")
      .allInnerTexts();
  }

  async openQuotaSection(periodOrder) {
    await this.page
      .locator(`(//span[@class='ag-icon ag-icon-tree-closed'])[${periodOrder}]`)
      .click();
  }

  async closeQuotaSection(periodOrder) {
    await this.page
      .locator(`(//span[@class='ag-icon ag-icon-tree-open'])[${periodOrder}]`)
      .click();
  }

  async getQuotaValueViewProfile() {
    return await this.page
      .locator("div[row-id='0'] div[col-id='quota']")
      .innerText();
  }

  async clickEditQuota() {
    await this.page
      .locator("(//*[name()='svg'][@class='w-4 h-4 hover:cursor-pointer'])[1]")
      .click();
  }

  async changeScheduleFrequency(prevPeriod, newPeriod) {
    await this.page.getByText(prevPeriod).click();
    await this.page.locator(`div[label='${newPeriod}']`).click();
  }

  async setQuotaValue(value) {
    await this.page
      .getByRole("row", { name: "Quota Increase Value Decrease Value" })
      .getByRole("spinbutton")
      .click();
    await this.page
      .getByRole("row", { name: "Quota Increase Value Decrease Value" })
      .getByRole("spinbutton")
      .fill(value);
  }

  async getQuotaRange() {
    return await this.page
      .locator(
        "tr[class='ant-table-row ant-table-row-level-0'] td:nth-child(2)"
      )
      .innerText();
  }

  async clickTracebyLineItemID(id) {
    await this.page.getByTestId(`pt-${id}-trace-icon`).click();
  }

  async getQuotaReportObjectValues() {
    return await this.page
      .locator(
        "//div[@class='ag-center-cols-container'] //div[contains(@col-id,'fully_ramped_quota_value')]"
      )
      .allInnerTexts();
  }

  async switchDatasheet(datasheetName) {
    await this.page.getByText(datasheetName).click();
  }

  async getQuotaAttnReportObjectValues() {
    return await this.page
      .locator(
        "//div[@class='ag-center-cols-container'] //div[contains(@col-id,'target')]"
      )
      .allInnerTexts();
  }
}

export default QuotaAnnualED;
