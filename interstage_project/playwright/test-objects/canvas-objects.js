import { expect } from "@playwright/test";

class CanvasCommission {
  constructor(page) {
    this.page = page;
  }

  // CREATE MAIN COMMISSION PLAN

  async goToPlans(locator) {
    await this.page.goto(locator, { waitUntil: "networkidle" });
  }

  async clickBuildNewPlanButton() {
    await this.page.getByRole("button", { name: "Build a new Plan" }).click();
  }

  async enterPlanName(name) {
    const placeholder = await this.page.getByPlaceholder("Enter name");
    await placeholder.click();
    await placeholder.fill(name);
  }

  async enterDate(date, placeholder) {
    const input = await this.page.getByPlaceholder(placeholder);
    await input.click();
    await this.page.waitForTimeout(2000);
    await input.fill(date);
    await this.page.keyboard.press("Enter");
  }

  async enterDateTo(date, placeholder) {
    await this.page.waitForTimeout(2000);
    const input = await this.page.getByPlaceholder(placeholder);

    await input.fill(date);
    await this.page.keyboard.press("Enter");
  }

  async selectPayoutFrequency(frequency) {
    await this.page.getByLabel("Payout Frequency*").click();
    await this.page.getByTitle(frequency).locator("div").first().click();
  }

  async clickBuildPlanButton() {
    await this.page
      .locator("#create_plan")
      .getByRole("button", { name: "Build Plan" })
      .click();
  }

  async clickBuildPlanButtonSettlement() {
    await this.page.getByRole("button", { name: "Build Plan" }).click();
  }

  async checkP1Payee() {
    await this.page.getByLabel("P1Payee").check();
  }

  async clickAddPayeesButton() {
    await this.page.getByRole("button", { name: "Add Payees" }).click();
  }

  async clickSaveButton() {
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async waitForTimeout(time) {
    await this.page.waitForTimeout(time);
  }

  async clickAddComponentButton() {
    await this.page.getByRole("button", { name: "Add Component" }).click();
  }

  async getCurrentURL() {
    const currentURL = await this.page.url();
    console.log("Current URL:", currentURL);
    return currentURL;
  }

  async getPlanIdFromURL(url) {
    const searchParams = new URLSearchParams(url.split("?")[1]);
    return searchParams.get("plan_id");
  }

  async clickGetStartedButton() {
    await this.page.getByRole("button", { name: "Get Started" }).click();
  }

  async enterComponentName(name) {
    const placeholder = await this.page.getByPlaceholder("Enter name");
    await placeholder.click();
    await placeholder.fill(name);
  }

  async clickNextButton() {
    await this.page.getByRole("button", { name: "Next" }).click();
  }

  async clickStep2SelectData() {
    await this.page.getByText("STEP 2Select data").click();
  }

  async selectDataBook(bookTitle) {
    await this.page.getByLabel("Databook*").click();
    await this.page.getByTitle(bookTitle).locator("div").first().click();
  }

  async selectDataSheet(sheetTitle) {
    await this.page.getByLabel("Datasheet*").click();
    await this.page.getByTitle(sheetTitle).locator("div").first().click();
  }

  async selectEmailField(fieldTitle) {
    await this.page.getByLabel("Email field*").click();
    await this.page.getByTitle(fieldTitle).locator("div").first().click();
  }

  async selectDateField(fieldTitle) {
    await this.page.getByLabel("Date field*").click();
    await this.page.getByTitle(fieldTitle).locator("div").first().click();
  }

  async clickCreateButton() {
    await this.page.getByRole("button", { name: "Create" }).click();
  }

  async selectAutoSuggestionItem(itemText) {
    await this.page.getByPlaceholder("Press Ctrl + H for help").click();
    await this.page.waitForTimeout(1000);
    await this.page
      .getByTestId("auto-suggestion-view")
      .locator("div")
      .filter({ hasText: itemText })
      .nth(3)
      .click();
  }

  async enterPercentage(percentage) {
    await this.page
      .locator("li")
      .filter({ hasText: "*" })
      .getByRole("listitem")
      .click();
    const textbox = await this.page.getByRole("textbox").nth(3);
    await textbox.click();
    await this.page.keyboard.type(percentage);
    await textbox.press("Enter");
  }

  async waitForSVGVisible() {
    await this.page
      .locator("svg.text-ever-success")
      .waitFor({ state: "visible", timeout: 18000 });
  }

  async waitForErrorIconVisible() {
    await this.page
      .locator("svg.text-ever-error")
      .waitFor({ state: "visible", timeout: 18000 });
  }

  async clickSaveComponentButton() {
    const btnLocator = await this.page.getByRole("button", { name: "Save" });
    await expect(btnLocator).toBeEnabled();
    await btnLocator.click();
  }

  async waitForComponentSavedMessage(timeoutvalue) {
    await this.page
      .getByText("Plan Component details saved successfully")
      .waitFor({ state: "visible", timeout: timeoutvalue });
  }

  async assertDateRetained(expectedDate, locator) {
    const endDateElement = await this.page.locator(locator);
    console.log(endDateElement);
    const endDateValue = await endDateElement.evaluate(
      (endDateElement) => endDateElement.value
    );
    if (endDateValue.trim() === expectedDate) {
      console.log(
        "The change in date is being retained at the criteria level."
      );
    } else {
      console.log("The changed date is not reflecting in the criteria level");
    }
  }

  async selectYear(year) {
    await this.page.getByTestId("pt-fiscal-year-select").click();
    await this.page.locator(`div[title="${year}"]`).last().click();
  }

  // READ THE CREATED COMMISSION PLAN
  async clickCommissionPlan(name) {
    const planName = this.page.getByText(name, { exact: true });
    await planName.waitFor({ state: "visible" });
    await planName.first().click();
  }

  // UPDATE THE COMMIISION PLAN
  async updateCommissionPlan() {
    await this.page
      .locator("div")
      .filter({ hasText: /^DraftQuarterly$/ })
      .getByRole("button")
      .click();
    await this.page.getByPlaceholder("Enter name").click();
    await this.page.getByPlaceholder("Enter name").fill("");
    await this.page
      .getByPlaceholder("Enter name")
      .fill("Automation_Commission_Plan_edited");
    await this.page.getByRole("button", { name: "Update" }).click();
    await this.page.reload();
    const expected = await this.page
      .locator("div.whitespace-nowrap span.text-lg")
      .innerText();
    if (expected === "Automation_Commission_Plan_edited") {
      console.log("Update is successful and you can proceed");
    } else {
      console.log("Update Failed");
    }
  }

  async addComponentAndSave() {
    await this.page.getByRole("button", { name: "Add Component" }).click();
    await this.page.getByRole("button", { name: "Get Started" }).click();
    await this.page.getByLabel("TierChoose the Tier template").check();
    await this.page.getByPlaceholder("Enter name").click();
    await this.page.getByPlaceholder("Enter name").fill("Tier_Rule");
    await this.page.getByRole("button", { name: "Next" }).click();
    await this.page.getByLabel("Databook*").click();
    await this.page.getByTitle("REG_Book").locator("div").first().click();
    await this.page.getByLabel("Datasheet*").click();
    await this.page.getByTitle("Reg_sheet").locator("div").first().click();
    await this.page.getByLabel("Email field*").click();
    await this.page.getByTitle("email").locator("div").first().click();
    await this.page.getByLabel("Date field*").click();
    await this.page.getByTitle("DateOfSale").locator("div").first().click();
    await this.page.getByRole("button", { name: "Create" }).click();
    await this.page.getByPlaceholder("Press Ctrl + H for help").click();
    await this.page
      .getByTestId("auto-suggestion-view")
      .locator("div")
      .filter({ hasText: "amount" })
      .nth(3)
      .click();
    await this.page
      .locator("span")
      .filter({ hasText: "Tier_Rule" })
      .first()
      .click();
    await this.page.getByRole("spinbutton").click();
    await this.page.getByRole("spinbutton").fill("10000");
    await this.page.getByTestId("expression-input-box").nth(1).click();
    await this.page.keyboard.type("20%");
    await this.page.keyboard.press("Enter");
    await this.page.keyboard.press("Escape");
    await this.page
      .locator("div")
      .filter({ hasText: /^SimpleConditional TieredValue\(\) \* 20%$/ })
      .getByRole("img")
      .locator("path")
      .waitFor({ state: "visible", timeout: 20000 });
    await this.page.getByText("TieredValue() *", { exact: true }).click();
    await this.page.keyboard.type("10%");
    await this.page.keyboard.press("Enter");
    await this.page.keyboard.press("Escape");
    await this.page
      .locator("div")
      .filter({ hasText: /^SimpleConditional TieredValue\(\) \* 10%$/ })
      .getByRole("img")
      .locator("path")
      .waitFor({ state: "visible", timeout: 20000 });
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page
      .getByText("Plan Component details saved successfully")
      .waitFor({ state: "visible", timeout: 20000 });
  }

  // PUBLISH THE COMMISSION PLAN
  async publishCommissionPlan() {
    await this.page
      .getByRole("button", { name: "Publish", exact: true })
      .click();
    await this.page
      .getByText(
        "Are you sure you want to publish Automation_Commission_Plan_edited?Once"
      )
      .click();
    await this.page
      .getByRole("dialog")
      .getByRole("button", { name: "Publish" })
      .click();
    await this.page.waitForTimeout(3000);
    await this.page.locator("button.ant-modal-close").click();
  }

  async publishCommissionPlanV2(commissionPlanName) {
    await this.page
      .locator("//button[ ./div[text()='Publish']]")
      .last()
      .click();
    const textPresence = await this.page.getByText(
      `Are you sure you want to publish ${commissionPlanName}?`
    );
    await textPresence.waitFor({ state: "visible" });
    await this.page
      .locator("//button[ ./div[text()='Publish']]")
      .last()
      .click();
    await this.page.waitForTimeout(3000);
    await this.page.locator("button.ant-modal-close").click();
  }

  async exitCanvas() {
    await this.page.waitForTimeout(2000);
    await this.page.getByRole("button", { name: "Exit Canvas" }).click();
  }

  // CLONE THE COMMISSION PLAN AS WELL AS THE CRITERIA
  async clickCloneMenu() {
    await this.page.locator(".absolute > .ant-btn").first().click();
    await this.page.getByRole("menuitem", { name: "With Payees" }).click();
  }

  async hoverAndCloneCriteria(oldCriteria, newCriteria) {
    await this.page.getByText(oldCriteria).hover();
    await this.page
      .locator("div")
      .filter({ hasText: new RegExp(`^${oldCriteria}${newCriteria}$`) })
      .getByRole("button")
      .click();
    await this.page.getByRole("menuitem", { name: "Clone" }).click();
    const newCriteriaElement = await this.page
      .locator("span")
      .filter({ hasText: `${oldCriteria} (Copy)` })
      .first();
    const newCriteriaText = await newCriteriaElement.innerText();
    const expected = `${oldCriteria} (Copy)`;
    if (newCriteriaText.trim().includes(expected.trim())) {
      console.log("Cloned the Criteria successfully");
    } else {
      console.log("Cloning Failed");
    }
  }

  // ADD AN INVALID CRITERIA
  async clickComponentByText(text) {
    await this.page.locator("span").filter({ hasText: text }).first().click();
  }

  async clearComponentTextbox() {
    await this.page.getByRole("textbox").nth(4).click();
    await this.page.getByRole("textbox").nth(3).clear();
  }

  async hoverAndCloneInvalidCriteria(criterianame) {
    await this.page.waitForTimeout(3000);
    await this.page
      .locator(`(//span[contains(text(),'${criterianame}')])`)
      .hover();
    await this.page
      .locator(`//span[text()='${criterianame}']/ancestor::div[3]//div[2]`)
      .click();
    await this.page.getByRole("menuitem", { name: "Clone" }).click();
    await this.page
      .getByText("Cannot clone an invalid component")
      .waitFor({ state: "visible", timeout: 18000 });
    const errormsg = await this.page
      .getByText("Cannot clone an invalid component")
      .innerText();
    if (errormsg.trim().includes("Cannot clone an invalid component")) {
      console.log(
        "Cloning of an invalid component is not possible; you can proceed."
      );
    } else {
      console.log("Failed");
    }
  }

  async addComponentWithInvalidCriteria(componentName) {
    await this.page.getByLabel("Import an existing").check();
    await this.page.getByRole("button", { name: "Get Started" }).click();
    await this.page.getByText("STEP 1Import Existing").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select fiscal year$/ })
      .nth(2)
      .click();
    await this.page.getByRole("listitem").getByText("2024").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select commission plan$/ })
      .nth(2)
      .click();
    await this.page
      .locator("span")
      .filter({ hasText: "Automation_Commission_Plan_edited_CopyDraft" })
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Choose component$/ })
      .nth(2)
      .click();
    await this.page.getByText("Simple_Rule (Copy)Simple").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Next$/ })
      .first()
      .click();
    await this.page.getByRole("button", { name: "Next" }).click();
    await this.page.getByPlaceholder("Enter component name").click();
    await this.page.getByPlaceholder("Enter component name").fill("");
    await this.page
      .getByPlaceholder("Enter component name")
      .fill(componentName);
    await this.page.getByText("BackCreate").click();
    await this.page.getByText("Create").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Save$/ })
      .nth(2)
      .click();
    await this.page.getByRole("button", { name: "Save" }).click();
    const newCriteriaElement = await this.page
      .locator("span")
      .filter({ hasText: componentName })
      .first();
    const newCriteriaText = await newCriteriaElement.innerText();
    const expected = componentName;
    if (newCriteriaText.trim().includes(expected.trim())) {
      console.log(
        "Successfully added an Invalid Criteria to the drafted Commission plan"
      );
    } else {
      console.log("Importing of an Invalid Criteria failed");
    }
  }

  // CREATE A SPIFF PLAN
  async fillPlanDetails(planName, fromDate, toDate, payoutFrequency) {
    await this.page.getByRole("button", { name: "Build Plan" }).click();
    await this.page.getByPlaceholder("Enter name").click();
    await this.page.waitForTimeout(2000);
    await this.page.getByPlaceholder("Enter name").fill(planName);
    await this.page.getByPlaceholder("From").click();
    await this.page.getByPlaceholder("From").fill(fromDate);
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(2000);
    await this.page.waitForTimeout(2000);
    await this.page.getByPlaceholder("To").fill(toDate);
    await this.page.keyboard.press("Enter");
    await this.page.getByLabel("Payout Frequency*").click();
    await this.page.getByTitle(payoutFrequency).locator("div").first().click();
    await this.page
      .locator('[id="create_plan_spiff\\ Plan"]')
      .getByRole("switch")
      .click();
  }

  async selectConditional() {
    await this.page.getByLabel("ConditionalChoose the").check();
  }

  async fillComponentDetails(componentName) {
    await this.page.getByPlaceholder("Enter name").click();
    await this.page.getByPlaceholder("Enter name").fill(componentName);
    await this.page.getByRole("button", { name: "Next" }).click();
  }

  async createComponentRule() {
    await this.page.getByRole("button", { name: "Create" }).click();
    await this.page.getByPlaceholder("Press Ctrl + H for help").first().click();
    await this.page
      .getByTestId("auto-suggestion-view")
      .locator("div")
      .filter({ hasText: "amount" })
      .nth(3)
      .click();
    await this.page
      .locator("li")
      .filter({ hasText: ">=" })
      .getByRole("listitem")
      .click();
    await this.page.getByText("amount >=").click();
    await this.page.keyboard.type("10000");
    await this.page.keyboard.press("Enter");
    await this.page.getByText("THEN").click();
    await this.page.getByTestId("expression-input-box").nth(1).click();
    await this.page
      .getByTestId("auto-suggestion-view")
      .locator("div")
      .filter({ hasText: "amount" })
      .nth(3)
      .click();
    await this.page
      .locator("li")
      .filter({ hasText: "*" })
      .getByRole("listitem")
      .click();
    await this.page.keyboard.type("20%");
    await this.page.keyboard.press("Enter");
    await this.page.getByText("ELSE").click();
    await this.page.getByTestId("expression-input-box").nth(2).click();
    await this.page
      .getByTestId("auto-suggestion-view")
      .locator("div")
      .filter({ hasText: "amount" })
      .nth(3)
      .click();
    await this.page
      .locator("(//*[name()='path'][@fill-rule='evenodd'])[9]")
      .waitFor({ state: "visible", timeout: 15000 });
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page
      .getByText("Plan Component details saved successfully")
      .waitFor({ state: "visible", timeout: 18000 });
  }

  async clickManagePayees() {
    await this.page.getByText("Manage Payees", { exact: true }).click();
    await this.page.getByLabel("P1Payee").check();
  }

  async savePayees() {
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page
      .getByText("Successfully saved 1 payees")
      .waitFor({ state: "visible", timeout: 20000 });
    await this.page
      .getByText(
        "Automation_SPIFF_PlanDraftSPIFFQuarterlyTime MachinePublishExit Canvas"
      )
      .click();
  }

  // CREATE A SETTLEMENT PLAN
  async fillDateInput(placeholder, date) {
    await this.page.getByPlaceholder(placeholder).click();
    await this.page.getByPlaceholder(placeholder).fill(date);
    await this.page.keyboard.press("Enter");
  }

  async fillDateOutput(placeholder, date) {
    await this.page.getByPlaceholder(placeholder).fill(date);
    await this.page.keyboard.press("Enter");
  }

  async toggleSwitch() {
    await this.page.getByRole("switch").nth(1).click();
  }

  async selectSettlementDate(option, placeholder) {
    await this.page.getByPlaceholder(placeholder).click();
    await this.page.getByPlaceholder(placeholder).fill(option);
  }

  async checkCheckbox(label) {
    await this.page.getByLabel(label).check();
  }

  async clickmodal() {
    await this.page.getByText("STEP 1Import Existing").click();
  }

  async addComponentWithvalidCriteria(year, commission, component) {
    await this.page
      .locator("div")
      .filter({ hasText: /^Select fiscal year$/ })
      .nth(2)
      .click();
    await this.page.getByRole("listitem").getByText(year).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select commission plan$/ })
      .nth(2)
      .click();
    await this.page.locator("span").filter({ hasText: commission }).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Choose component$/ })
      .nth(2)
      .click();
    await this.page.getByText(component).click();
  }

  async fillSettlementName(placeholder, name) {
    await this.page.getByPlaceholder(placeholder).click();
    await this.page.getByPlaceholder(placeholder).fill(name);
  }

  async selectSettlementComponent(component) {
    await this.page.getByLabel("Select Component(s)*").click();
    await this.page.getByTitle(component).locator("div").first().click();
  }

  async selectTriggerbasedpayouts(trigger) {
    await this.page.getByLabel("Trigger payouts based on*").click();
    await this.page.getByTitle(trigger).locator("div").first().click();
  }

  async selectForCommissioncomponents(id) {
    await this.page.getByLabel("For commission component(s)*").click();
    await this.page
      .getByTitle(id, { exact: true })
      .locator("div")
      .first()
      .click();
  }

  async searchPlan(searchPlan) {
    await this.page.waitForTimeout(10000);
    const searchBar = await this.page.getByPlaceholder("Search by plan name");
    await searchBar.waitFor({ state: "visible", timeout: 10000 });
    await this.page.waitForTimeout(10000);
    await this.page.getByPlaceholder("Search by plan name").fill(searchPlan);
    await this.page.waitForTimeout(5000);
    const plan = await this.page.getByText(searchPlan, { exact: true });
    await plan.waitFor({ state: "visible", timeout: 10000 });
  }

  async editPlanenddate(planName, date) {
    await this.page.getByText(planName, { exact: true }).click();
    await this.page.getByRole("button", { name: "Edit" }).click();
    await this.page.getByPlaceholder("End date").click();
    await this.page.getByTitle(date).locator("div").click();
    await this.page.getByRole("button", { name: "Apply" }).click();
    await this.page.getByRole("button", { name: "Done" }).click();
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.page.getByRole("button", { name: "Exit Canvas" }).click();
  }

  async navigatetoPlan() {
    await this.page.goto("/plans", { waitUntil: "networkidle" });
  }

  async planMenu(planName) {
    await this.page.getByTestId(`pt-actions-${planName}`).click();
  }

  async selectTriggerPayouts(id) {
    await this.page.getByLabel("For triggering payouts*").click();
    await this.page.getByText(id, { exact: true }).nth(2).click();
    await this.page
      .locator("#data-source-form div")
      .filter({ hasText: "STEP 1Component name:" })
      .nth(1)
      .click();
  }

  async selectSettlementvalues(placeholder, value) {
    await this.page
      .locator("div")
      .filter({
        hasText: /^Trigger commission payout only when this condition is true$/,
      })
      .getByPlaceholder(placeholder)
      .click();
    await this.page.getByPlaceholder(placeholder).first().click();
    await this.page.waitForTimeout(3000);
    await this.page.getByPlaceholder(placeholder).first().fill(value);
    await this.page.waitForTimeout(3000);
    await this.page
      .getByTestId("auto-suggestion-view")
      .locator("div")
      .filter({ hasText: "Boolean" })
      .nth(3)
      .click();
    await this.page.getByPlaceholder(placeholder).first().press("Enter");
    await this.page.getByText("Z_Automation_Settlement_Plan").nth(1).click();
    await this.page
      .locator("(//*[name()='svg'][@class='text-ever-success w-6 h-6'])[2]")
      .waitFor({ state: "visible", timeout: 15000 });
    await this.page.getByText("Trigger commission payout").click();
  }

  async selectSettlementvalues2(Id, value) {
    await this.page.getByRole("button", { name: "Conditional" }).click();
    await this.page.getByRole("button", { name: "Simple" }).click();
    await this.page.getByPlaceholder("Press Ctrl + H for help").click();
    await this.page
      .getByTestId(Id)
      .locator("div")
      .filter({ hasText: value })
      .nth(3)
      .click();
  }

  async waitForSettlementSuccess() {
    await this.page
      .locator("(//*[name()='svg'][@class='text-ever-success w-6 h-6'])[2]")
      .waitFor({ state: "visible", timeout: 15000 });
  }

  async ExitFull() {
    await this.page.getByRole("button", { name: "Exit", exact: true }).click();
  }

  async hoverAndCloneInvalidSettlementCriteria(criterianame) {
    await this.page.getByText(criterianame).first().hover();
    await this.page
      .locator("div")
      .filter({
        hasText: new RegExp(
          `^Simple_Rule1 linked settlement rules${criterianame}$`
        ),
      })
      .getByRole("button")
      .click();
    await this.page.getByRole("menuitem", { name: "Clone" }).click();
    await this.page.getByText(`${criterianame} (Copy)`).first().click();
    await this.page.getByRole("textbox").nth(2).click();
    await this.page.getByRole("textbox").nth(2).clear();
    await this.page.getByText("Trigger commission payout").last().click();
    await this.page.getByText(`${criterianame} (Copy)`).first().hover();
    await this.page
      .locator(
        `//span[text()='${criterianame} (Copy)']/ancestor::div[3]//div[2]`
      )
      .click();
    await this.page.getByRole("menuitem", { name: "Clone" }).click();
    await this.page
      .getByText("Cannot clone an invalid settlement rule")
      .waitFor({ state: "visible", timeout: 18000 });
    const errormsg = await this.page
      .getByText("Cannot clone an invalid settlement rule")
      .innerText();
    if (errormsg.trim().includes("Cannot clone an invalid settlement rule")) {
      console.log(
        "Cloning of an invalid settlement rule is not possible; you can proceed."
      );
    } else {
      console.log("Failed");
    }
  }

  // BUILD AND SIMULATE CONSISTENCY
  async clickComponent(componentName) {
    await this.page
      .locator("div")
      .filter({ hasText: new RegExp("^" + componentName + "$") })
      .nth(4)
      .click();
  }

  async clickSimulateButton() {
    await this.page.getByRole("button", { name: "Simulate" }).click();
  }

  async fillEndDate(endDate) {
    await this.page.getByPlaceholder("End date").click();
    await this.page.waitForTimeout(5000);
    await this.page.getByPlaceholder("End date").fill("");
    await this.page.getByPlaceholder("End date").fill(endDate);
    await this.page.keyboard.press("Enter");
  }

  // GUIDE RULES
  async clickGuidesButton() {
    await this.page.getByRole("button", { name: "Guides" }).click();
    await this.page.waitForTimeout(5000);
  }

  async getAllGuideElementsText() {
    const Guideelements = [];
    const guidelist = await this.page.locator(
      "div.absolute div.h-full span.text-base"
    );
    for (let i = 0; i < (await guidelist.count(i)); i++) {
      const elem = await guidelist.nth(i).textContent();
      Guideelements.push(elem);
    }
    return Guideelements;
  }

  // SORT, FILTER CTA
  async hoverViewSettings() {
    await this.page.locator("button.ant-btn-ghost").hover();
  }

  async toggleShowSpiffPlans() {
    await this.page
      .getByRole("menuitem", { name: "Show Spiff Plans" })
      .getByRole("switch")
      .click();
  }

  async toggleShowActivePlans() {
    await this.page
      .getByRole("menuitem", { name: "Show Active Plans" })
      .getByRole("switch")
      .click();
  }

  async clickSortAscendingButton() {
    await this.page
      .getByRole("button", { name: "Sort by Ascending (A-Z) right" })
      .click();
    await this.page.getByLabel("Descending (Z-A)").check();
  }

  async sharePlan() {
    await this.page.getByText("Share").click();
  }

  async emailtoShare(role) {
    await this.page.getByTestId("ever-select").locator("div").nth(1).click();
    await this.page.getByText(role).click();
    await this.page.getByText("Share Plan").click();
    await this.page.getByRole("button", { name: "Add" }).click();
    await this.page.getByText("Successfully added members to").first().click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Share Plan$/ })
      .getByRole("button")
      .click();
  }

  async clickSortDescendingButton() {
    await this.page
      .getByRole("button", { name: "Sort by Descending (Z-A) right" })
      .click();
    await this.page.getByLabel("Ascending (A-Z)").check();
  }

  async clickMainPage() {
    await this.page.locator("(//div[@class='h-full px-4 py-3'])[1]").click();
  }

  async checkTextContent(locator) {
    const results = [];
    const elements = await this.page.locator(locator);
    for (let i = 0; i < (await elements.count()); i++) {
      const text = await elements.nth(i).textContent();
      results.push(text);
    }
    return results;
  }

  // ADD A PLAN DOCUMENT
  async clickDraftQuarterlyButton() {
    await this.page
      .locator("div")
      .filter({ hasText: /^DraftQuarterly$/ })
      .getByRole("button")
      .click();
  }

  async clickUploadButton() {
    await this.page.getByRole("button", { name: "Upload" }).click();
  }

  async setInputFiles(filePath) {
    const fileInput = await this.page.locator('input[type="file"]').first();
    await fileInput.setInputFiles(filePath);
  }

  async clickUpdateButton() {
    await this.page.getByRole("button", { name: "Update" }).click();
    await this.page.waitForTimeout(10000);
  }

  // REMOVE A PLAN DOCUMENT
  async clickRemovePlanDocumentButton() {
    await this.page
      .locator(
        "(//*[name()='svg'][@class='!w-4 !h-4 m-auto text-ever-base-content-mid h-full w-full'])[1]"
      )
      .click();
  }

  // DELETE A COMMISSION PLAN
  async clickclonedelete(planName) {
    await this.page.locator(`[data-testid="pt-actions-${planName}"]`).click();
  }

  async ClickCloneMenuDelete() {
    await this.page.getByRole("menuitem", { name: "With Payees" }).click();
  }

  async clickDeleteMenuItem() {
    await this.page.getByText("Delete").click();
  }

  async clickConfirmDeleteButton() {
    await this.page.getByRole("button", { name: "Yes, delete" }).click();
  }

  async publishCommissionsPlan(planName) {
    await this.page
      .getByRole("button", { name: "Publish", exact: true })
      .click();
    const PLAN_POPUP = await this.page.getByText(
      "Are you sure you want to publish " + planName + "?"
    );
    await PLAN_POPUP.waitFor({ state: "visible" });
    await this.page.waitForTimeout(1000);
    await this.page
      .getByRole("dialog")
      .getByRole("button", { name: "Publish" })
      .click();
    await this.page.getByLabel("Close", { exact: true }).first().click();
  }

  async clickCommissionComponent(component) {
    const componentLocator = await this.page.locator(
      `//div[.//span[text()="Components"]]/following-sibling::div//span[text()="${component}"]`
    );
    await componentLocator.waitFor({ state: "visible" });
    await componentLocator.click();
  }

  /**
   * Clicks a button on the page with the specified name and optional wait behavior
   *
   * @param {string} buttonName - The name/text of the button to click
   * @param {Object} options - Optional configuration object
   * @param {boolean} options.wait - Whether to wait for network idle after click (default: false)
   * @param {number} options.timeout - Timeout in ms for network idle wait (default: 10000)
   * @returns {Promise<void>}
   * @example
   * // Click button without waiting
   * await clickButton('Save')
   * // Click button and wait for network idle with custom timeout
   * await clickButton('Submit', { wait: true, timeout: 5000 })
   */
  async clickButton(buttonName, options = {}) {
    const { wait = false, timeout = 10000 } = options;
    await this.page.getByRole("button", { name: buttonName }).click();
    if (wait) {
      await this.page.waitForLoadState("networkidle", { timeout });
    }
  }

  /**
   * @returns {Page} EditViewPage - Page object for Edit View
   */
  async switchToEditViewByClickingRenameFields() {
    const [editViewPage] = await Promise.all([
      this.page.context().waitForEvent("page"),
      this.page.getByText("Rename fields").click(),
    ]);
    await editViewPage.waitForLoadState("networkidle");
    return editViewPage;
  }

  /**
   * Simulates a scenario using the Time Machine feature for a given component, date range, and simulation target.
   
   * @param {string} componentName - The name of the component to simulate.
   * @param {string} startDate - The start date for the simulation period.
   * @param {string} endDate - The end date for the simulation period.
   * @param {string} simulateFor - The target to simulate for.
   * @returns {Promise<void>}
   */
  async SimulateByTimeMachine(componentName, startDate, endDate, simulateFor) {
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Criteria$/ })
      .nth(2)
      .click();
    await this.page
      .getByText(`${componentName}`, { exact: true })
      .nth(1)
      .click();

    const startDateLocator = await this.page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("Start date");
    await startDateLocator.click();
    await startDateLocator.fill(`${startDate}`);
    await startDateLocator.press("Enter");
    const endDateLocator = await this.page
      .locator("div")
      .filter({ hasText: /^Period$/ })
      .getByPlaceholder("End date");
    await endDateLocator.click();
    await endDateLocator.fill(`${endDate}`);
    await endDateLocator.press("Enter");

    await this.page.getByText("Simulate for").click();
    await this.page.getByText("Simulate for Select payee").click();
    await this.page.getByRole("listitem").getByText(`${simulateFor}`).click();
    await this.page.getByRole("button", { name: "Simulate" }).click();
  }

  /**
   * Clicks a user from the simulation results based on their text name.
   
   * @param {string} textName - The text name of the user to click.
   * @returns {Promise<void>}
   */
  async clickUserFromSimulationByText(textName) {
    await this.page
      .locator(`(//div[@role='row']//descendant::span[.='${textName}'])[2]`)
      .click();
  }

  /**
   * Clicks the simulate columns button.
   
   * @returns {Promise<void>}
   */
  async clickSimulateColumns() {
    await this.page
      .locator("//button[.='Export as .CSV']//preceding-sibling::button")
      .click();
  }
}

export default CanvasCommission;
