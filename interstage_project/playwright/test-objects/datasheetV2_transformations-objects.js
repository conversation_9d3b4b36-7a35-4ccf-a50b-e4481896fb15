import DatasheetV2EditViewPage from "./datasheet-v2-editView-objects";
import { expect } from "@playwright/test";

class V2TransformationsPage {
  constructor(page, adminPage) {
    this.page = page;
    this.dsV2EditPage = new DatasheetV2EditViewPage(this.page);
  }

  async navigateToDatasheetPage() {
    await this.page.goto("/datasheet", { waitUntil: "networkidle" });
  }

  async verifyText(text) {
    await expect(this.page.getByText(text)).toBeVisible();
  }

  async verifyTextIsHidden(text) {
    await expect(this.page.getByText(text)).toBeHidden();
  }

  async error(message) {
    await expect(this.page.getByText(message)).toBeVisible();
  }

  async validateErrorMessage(mes) {
    const text = await this.page.locator("div[class*='go']").textContent();
    console.log(text);
    expect(text).toContain(mes);
  }

  async generateSheet() {
    await expect(
      this.page.getByRole("button", { name: "Load data into this sheet" })
    ).toBeVisible();
    await this.page.waitForTimeout(2000);
    await this.page
      .getByRole("button", { name: "Load data into this sheet" })
      .click();
  }

  async verifyDataSheetGeneration() {
    const generateText = await this.page.getByText(
      "Refreshing this sheet with latest data from all sources"
    );
    await generateText.waitFor({ state: "visible", timeout: 10000 });
    await generateText.waitFor({ state: "hidden", timeout: 180000 });
    await this.page.waitForTimeout(5000);
  }

  async addDatainTransformation(LHSField, RHSField) {
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Datasheet$/ })
      .nth(2)
      .click();
    await this.page.locator(".ant-select-item-option-content").first().click();
    await this.page
      .locator(
        "form > div > div:nth-child(2) > div > .relative > .ant-select > .ant-select-selector > .ant-select-selection-overflow"
      )
      .first()
      .click();
    await this.page.getByText(LHSField, { exact: true }).nth(4).click();
    await this.page
      .locator(
        "form > div > div:nth-child(2) > div:nth-child(2) > .relative > .ant-select > .ant-select-selector"
      )
      .click();
    await this.page.getByText(RHSField).nth(1).click();
  }

  async getUserProperty(email, dateSelection, d1, d2) {
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Email Column$/ })
      .nth(2)
      .click();
    await this.page.keyboard.type(email);
    await this.page.keyboard.press("Enter");

    await this.page
      .locator("div")
      .filter({ hasText: /^Select Date Column$/ })
      .nth(2)
      .click();
    await this.page.keyboard.type(dateSelection);
    await this.page.keyboard.press("Enter");
    // await this.page.locator("span").filter({ hasText: dateSelection }).click();

    await this.page
      .locator("form div")
      .filter({ hasText: "Select User Fields" })
      .nth(4)
      .click();

    // Check if d1 is visible before clicking
    const isD1Visible = await this.page
      .getByRole("listitem")
      .filter({ hasText: d1 })
      .locator("span")
      .isVisible();

    if (isD1Visible) {
      await this.page
        .getByRole("listitem")
        .filter({ hasText: d1 })
        .locator("span")
        .click();
      // Select d2 (assuming it should always be selected)
      await this.page
        .getByRole("listitem")
        .filter({ hasText: d2 })
        .locator("span")
        .click();
    } else {
      console.log(`⚠️ Skipping selection: '${d1}' is not visible.`);
    }

    // Click "Data Transformation"
    await this.page.getByText("Data Transformation").click();
  }

  async flattenTransformation(fieldName, outputDataType) {
    await this.page.locator("//div[@name='hierarchyField']").click();
    await this.page.keyboard.type(fieldName);
    await this.page.keyboard.press("Enter");
    //  if (outputDataType == "String") {
    //   await this.page
    //     .locator("div")
    //     .filter({ hasText: /^Output data type\*Email$/ })
    //     .locator("div")
    //     .nth(2)
    //     .click();
    //   await this.page
    //     .locator("span")
    //     .filter({ hasText: /^String$/ })
    //     .click();
    // }

    const emailElement = this.page.locator(
      "//div[@name='outputType']//span[@title='Email']"
    );
    if (await emailElement.isVisible()) {
      await emailElement.click();

      // Proceed only if outputDataType is "String"
      if (outputDataType === "String") {
        await this.page
          .locator("div")
          .filter({ hasText: /^Output data type\*Email$/ })
          .locator("div")
          .nth(2)
          .click();
        await this.page
          .locator("span")
          .filter({ hasText: /^String$/ })
          .click();
      }
    }
    await this.page.getByText("Data Transformation").click();
  }

  async flattenTransformation(fieldName, outputDataType) {
    await this.page.locator("//div[@name='hierarchyField']").click();
    await this.page.keyboard.type(fieldName);
    await this.page.keyboard.press("Enter");
    //  if (outputDataType == "String") {
    //   await this.page
    //     .locator("div")
    //     .filter({ hasText: /^Output data type\*Email$/ })
    //     .locator("div")
    //     .nth(2)
    //     .click();
    //   await this.page
    //     .locator("span")
    //     .filter({ hasText: /^String$/ })
    //     .click();
    // }

    const emailElement = this.page.locator(
      "//div[@name='outputType']//span[@title='Email']"
    );
    if (await emailElement.isVisible()) {
      await emailElement.click();

      // Proceed only if outputDataType is "String"
      if (outputDataType === "String") {
        await this.page
          .locator("span")
          .filter({ hasText: /^String$/ })
          .click();
      }
    }
    await this.page.getByText("Data Transformation").click();
  }

  async secondFilter(vname, operator, text, ddtext) {
    await this.page
      .getByRole("textbox", { name: "Press Ctrl + H for help" })
      .nth(1)
      .click();
    await this.page
      .getByTestId("auto-suggestion-view")
      .getByTitle(vname)
      .locator("span")
      .first()
      .click();
    await this.page.getByText(operator).click();
    await this.page.getByRole("textbox").nth(3).press("CapsLock");
    await this.page.getByRole("textbox").nth(3).fill(text);
    await this.page.getByText(ddtext).click();
    await this.page.getByText("Data Transformation").click();
  }

  async selectAllDDOptions() {
    await this.page
      .locator("form div")
      .filter({ hasText: "Select User Fields" })
      .nth(4)
      .click();

    // Loop through the first 10 items
    for (let i = 0; i < 22; i++) {
      await this.page.keyboard.press("ArrowDown"); // Move to the next option
      await this.page.keyboard.press("Enter"); // Select the highlighted option
    }
  }

  async validateDDoptions(d2) {
    await this.page
      .locator("form div")
      .filter({ hasText: "Select User Fields" })
      .nth(4)
      .click();
    await this.page
      .getByRole("listitem")
      .filter({ hasText: d2 })
      .locator("span")
      .isHidden();
  }

  async secondUpTransformation(email, dateSelection, d1, d2) {
    await this.page.locator("(//div[@name='emailColumn'])[2]").click();
    await this.page.getByText(email).nth(3).click();
    await this.page.locator("(//div[@name='dateColumn'])[2]").click();
    await this.page.locator("span").filter({ hasText: dateSelection }).click();
    await this.page
      .locator("form div")
      .filter({ hasText: "Select User Fields" })
      .nth(4)
      .click();
    await this.page
      .getByRole("listitem")
      .filter({ hasText: d1 })
      .locator("span")
      .click();
    await this.page
      .getByRole("listitem")
      .filter({ hasText: d2 })
      .locator("span")
      .click();
    await this.page.getByText("Data Transformation").click();
  }

  async editUp(dd, newVariable) {
    await this.page.locator("form").getByText(dd).click();
    await this.page.keyboard.press("Backspace");
    await this.page.keyboard.press("Backspace");

    if (newVariable) {
      // Only execute if newVariable has a value
      await this.page.getByText(newVariable, { exact: true }).click();
    }

    await this.page.getByText("Data Transformation").click();
  }

  async verifyFilterButton() {
    const generateText = await this.page.getByRole("button", {
      name: "Filter",
    });
    await generateText.waitFor({ state: "visible", timeout: 10000 });
  }

  async createFilter(
    variablename,
    operator,
    value,
    dropdownvalue,
    endvalue,
    enddropdownvalue
  ) {
    await this.page.getByPlaceholder("Press Ctrl + H for help").last().click();
    await this.page
      .getByTitle(variablename, { exact: true })
      .locator("span")
      .first()
      .click();
    await this.page
      .getByTitle(operator, { exact: true })
      .locator("span")
      .first()
      .click();

    await this.page.getByRole("textbox").nth(2).fill(value);
    await this.page.getByText(dropdownvalue).click();
    if (
      operator === "/" ||
      operator === "*" ||
      operator === "+" ||
      operator === "-"
    ) {
      await this.page.getByText("==").click();
      await this.page.getByRole("textbox").nth(4).fill(endvalue);
      await this.page.getByText(enddropdownvalue).click();
    }

    await this.page.getByText("Data Transformation").click();
  }

  async createFilterAndORFunction(
    variablename,
    operator,
    value,
    dropdownvalue,
    operator1,
    columnname1,
    endvalue,
    enddropdownvalue
  ) {
    await this.page.getByPlaceholder("Press Ctrl + H for help").click();
    await this.page.getByTitle(variablename).locator("span").first().click();
    await this.page
      .getByTestId("auto-suggestion-view")
      .getByText(operator)
      .click();

    await this.page.getByRole("textbox").nth(2).fill(value);
    await this.page.getByText(dropdownvalue).click();
    await this.page.getByText(operator1, { exact: true }).click();
    await this.page.getByTitle(columnname1).locator("span").first().click();
    await this.page
      .getByTestId("auto-suggestion-view")
      .getByText(operator)
      .click();
    await this.page.getByRole("textbox").nth(6).fill(endvalue);
    await this.page.getByText(enddropdownvalue).click();

    await this.page.getByText("Data Transformation").click();
  }

  async createFilterwithFunctions(
    functionname,
    variablename,
    findtext,
    operator,
    constant,
    dropdownvalue
  ) {
    await this.page.getByPlaceholder("Press Ctrl + H for help").click();
    await this.page.getByTitle(functionname).locator("span").first().click();

    if (functionname === "Find") {
      // Code for FIND function

      await this.dsV2EditPage.selectDropDown("Text field", variablename);
      await this.page.locator('div[data-state="open"] input').nth(-1).click();
      await this.page
        .locator('div[data-state="open"] input')
        .nth(-1)
        .fill(findtext);
      await this.page.getByRole("button", { name: "Apply" }).click();
      await this.page.getByText(operator, { exact: true }).click();
      await this.page.getByRole("textbox").nth(2).fill(constant);
      await this.page.getByText(dropdownvalue).click();
      await this.page.getByText("Data Transformation").click();
    } else if (
      functionname === "Round" ||
      functionname === "RoundUp" ||
      functionname === "RoundDown"
    ) {
      // Common logic for these functions
      // await this.dsV2EditPage.selectDropDown(
      //   "Select Number Column",
      //   variablename
      // );
      await this.dsV2EditPage.clickDropdown("Select Number Column");

      await this.dsV2EditPage.selectDropDown(
        "Select Number Column",
        variablename
      );

      await this.page
        .getByRole("spinbutton", { name: "Decimal Places" })
        .click();
      await this.page
        .getByRole("spinbutton", { name: "Decimal Places" })
        .fill(findtext);
      await this.page.getByRole("button", { name: "Apply" }).click();
      await this.page.getByText("==", { exact: true }).click();
      await this.page.getByRole("textbox").nth(2).fill(constant);
      await this.page.getByText(dropdownvalue).click();
      await this.page.getByText("Data Transformation").click();
    } else if (functionname === "Contains" || functionname === "NotContains") {
      // Code for CONTAINS and NOT CONTAINS function
      await this.dsV2EditPage.selectDropDown("Text field", variablename);
      await this.page.locator('div[data-state="open"] input').nth(-1).click();
      await this.page
        .locator('div[data-state="open"] input')
        .nth(-1)
        .fill(findtext);
      await this.page.getByRole("button", { name: "Apply" }).click();
      await this.page.getByText("Data Transformation").click();
    } else if (functionname === "IsEmpty" || functionname === "IsNotEmpty") {
      // Common logic for these functions
      await this.page
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Please Select" })
        .click();
      await this.page.getByText(variablename).nth(2).click();

      await this.page.getByRole("button", { name: "Apply" }).click();
      await this.page.getByText("Data Transformation").click();
    } else if (functionname === "LEN" || functionname === "Lower") {
      // Code for LEN function
      await this.page
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Please Select" })
        .click();
      await this.page.getByText(variablename).nth(2).click();
      await this.page.getByRole("button", { name: "Apply" }).click();
      await this.page.getByText(operator, { exact: true }).click();
      await this.page.getByRole("textbox").nth(2).fill(constant);
      await this.page.getByText(dropdownvalue).click();
      await this.page.getByText("Data Transformation").click();
    } else {
      // Default or fallback behavior if needed
      throw new Error(`Unsupported function name: ${functionname}`);
    }

    // Common actions (apply, set operator, etc.)
  }

  async dateAdd(
    functionname,
    variablename,
    unit,
    findtext,
    constant,
    dropdownvalue
  ) {
    await this.page.getByPlaceholder("Press Ctrl + H for help").click();
    await this.page.getByTitle(functionname).locator("span").first().click();
    await this.dsV2EditPage.selectDropDown("Select Date Column", variablename);

    if (unit === "HH:mm") {
      await this.page
        .locator("(//span[@class='ant-select-selection-item'])[3]")
        .click();
      await this.page.getByText(unit, { exact: true }).click();
      await this.page.getByRole("textbox", { name: "Select time" }).click();
      await this.page.getByText(findtext).nth(1).click();
      await this.page.locator(".ant-picker-ok button").click();
    } else if (unit !== "") {
      await this.page
        .locator("(//span[@class='ant-select-selection-item'])[3]")
        .click();
      await this.page.getByText(unit, { exact: true }).click();
      await this.page.locator('div[data-state="open"] input').nth(-1).click();
      await this.page
        .locator('div[data-state="open"] input')
        .nth(-1)
        .fill(findtext);
    } else {
      await this.page.locator('div[data-state="open"] input').nth(-1).click();
      await this.page
        .locator('div[data-state="open"] input')
        .nth(-1)
        .fill(findtext);
    }

    await this.page.getByRole("button", { name: "Apply" }).click();
    await this.page.getByText("==", { exact: true }).click();
    await this.page.getByRole("textbox").nth(2).fill(constant);
    await this.page.getByText(dropdownvalue).click();
    await this.page.getByText("Data Transformation").click();
  }

  async dateAddDatasheetField(
    functionname,
    variablename,
    unit,
    datasheetField,
    operator,
    constant,
    dropdownvalue
  ) {
    await this.page.getByPlaceholder("Press Ctrl + H for help").click();
    await this.page.getByTitle(functionname).locator("span").first().click();
    await this.dsV2EditPage.selectDropDown("Select Date Column", variablename);

    if (unit !== "") {
      await this.page
        .locator("(//span[@class='ant-select-selection-item'])[3]")
        .click();
      await this.page.getByText(unit, { exact: true }).click();
    }
    await this.page
      .locator(
        "//span[@class='ant-select-selection-item' and @title='Constant']"
      )
      .click();
    await this.page.getByText("Datasheet Field").click();
    await this.dsV2EditPage.selectDropDown(
      "Select Numeric Column",
      datasheetField
    );

    await this.page.getByRole("button", { name: "Apply" }).click();
    await this.page.getByText(operator, { exact: true }).click();
    await this.page.getByRole("textbox").nth(2).fill(constant);
    await this.page.getByText(dropdownvalue).click();
    await this.page.getByText("Data Transformation").click();
  }

  async dateDiff(
    functionname,
    variablename,
    secondvariablename,
    unit,
    period,
    constant,
    dropdownvalue
  ) {
    await this.page.getByPlaceholder("Press Ctrl + H for help").click();
    await this.page.getByTitle(functionname).locator("span").first().click();

    await this.dsV2EditPage.selectDropDown("Start Date Column", variablename);
    await this.dsV2EditPage.selectDropDown(
      "End Date Column",
      secondvariablename
    );
    await this.dsV2EditPage.selectDropDown("Select Unit", unit);

    if (
      unit === "QUARTER" ||
      unit === "HALF-YEAR" ||
      (unit === "YEAR" && period === "CALENDAR")
    ) {
      await this.dsV2EditPage.selectDropDown("FISCAL", period);
      await this.page.getByRole("button", { name: "Apply" }).click();
      await this.page.getByText("==", { exact: true }).click();
      await this.page.getByRole("textbox").nth(2).fill(constant);
      await this.page.getByText(dropdownvalue).click();
      await this.page.getByText("Data Transformation").click();
    } else if (
      unit === "QUARTER" ||
      unit === "HALF-YEAR" ||
      (unit === "YEAR" && period === "FISCAL")
    ) {
      await this.dsV2EditPage.selectDropDown("FISCAL", period);
      await this.page.getByRole("button", { name: "Apply" }).click();
      await this.page.getByText("==", { exact: true }).click();
      await this.page.getByRole("textbox").nth(2).fill(constant);
      await this.page.getByText(dropdownvalue).click();
      await this.page.getByText("Data Transformation").click();
    } else if (unit === "DAYS" || unit === "MONTH") {
      await this.page.getByRole("button", { name: "Apply" }).click();
      await this.page.getByText("==", { exact: true }).click();
      await this.page.getByRole("textbox").nth(2).fill(constant);
      await this.page.getByText(dropdownvalue).click();
      await this.page.getByText("Data Transformation").click();
    }
  }

  async validate(isPass = false) {
    await this.page.getByRole("button", { name: "Validate" }).click();
    await this.page.waitForTimeout(500);
    if (isPass) {
      await this.page
        .getByRole("button", { name: "Add Transformation" })
        .waitFor({ state: "visible" });
    }
  }

  async validateAndSave() {
    await this.page.getByRole("button", { name: "Validate" }).click();
    // await expect(this.page.getByRole('button', { name: 'Save' })).toBeEnabled();
    await this.page
      .getByRole("button", { name: "Add Transformation" })
      .waitFor({ state: "visible" });
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async saveOnly() {
    await this.page.getByRole("button", { name: "Save" }).click();
  }

  async closethisPage() {
    await this.page.getByRole("button", { name: "Close this page" }).click();
  }

  async addTransformation(transformation) {
    await this.page.waitForTimeout(3000);
    await expect(
      this.page.getByRole("button", { name: "Add Transformation" })
    ).toBeEnabled();
    await expect(
      this.page.getByRole("button", { name: "Add Transformation" })
    ).toBeVisible();
    await this.page.getByRole("button", { name: "Add Transformation" }).click();
    const menuItem = this.page.getByRole("menuitem", { name: transformation });

    if (await menuItem.isVisible()) {
      await menuItem.click();
    } else {
      console.log(`Menu item "${transformation}" is not visible.`);
    }
    await this.page.waitForTimeout(2000);
  }

  async deleteTransformation(number) {
    if (number === "1") {
      await this.page.locator('div[class="flex"]>svg').first().click();
      await this.page
        .getByRole("button", { name: "Yes, Delete", exact: true })
        .click();
    } else if (number === "2") {
      await this.page.locator('div[class="flex"]>svg').last().click();
      await this.page
        .getByRole("button", { name: "Yes, Delete", exact: true })
        .click();
    }
  }

  async editUserProperty() {
    await this.page
      .locator(
        "form > div > div:nth-child(2) > div > .ant-select > .ant-select-selector"
      )
      .click();
  }

  async deletingField() {
    await this.page.keyboard.press("Backspace");
  }

  async exitTransformation() {
    await this.page.getByText("Data Transformation").click();
  }

  async createSheetWithUnion(databook) {
    if (
      (await this.page
        .getByRole("button", { name: databook })
        .getAttribute("aria-expanded")) === "false"
    ) {
      await this.page.getByRole("button", { name: databook }).click();
    }
    await this.page
      .locator(
        `//button//span[contains(text(),'${databook}')]/ancestor::div[2]/following-sibling::div/div[1]`
      )
      .click();

    await expect(this.page.getByText("Create New Datasheet")).toBeVisible();
    await this.page.getByPlaceholder("Enter sheet name").click();
    await this.page.getByPlaceholder("Enter sheet name").fill("Union");
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select Object" })
      .click();
    await this.page.getByText("Transform Obj A").click();
    await this.page.getByRole("button", { name: "Create" }).click();

    await this.addTransformation("Union");
    await this.deleteTransformation("1");
    await this.addTransformation("Union");
    await this.page.locator("form").getByTestId("ever-select").first().click();
    await this.page.waitForTimeout(2000);
    await this.page.locator(`div[title='${databook}']`).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Datasheet$/ })
      .nth(2)
      .click();
    await this.page.getByText("Cars").nth(1).click();

    await this.page.locator(".ant-select-selection-overflow").first().click();
    await this.page.getByText("ID").nth(4).click();
    await this.page
      .locator(
        "div:nth-child(2) > .relative > .ant-select > .ant-select-selector > .ant-select-selection-overflow"
      )
      .click();
    await this.page.getByText("Description").click();
    await this.page.keyboard.press("Escape");
    await expect(
      this.page.getByRole("button", { name: "Validate" })
    ).toBeVisible();

    // To validate error message when the datatypes of the selected fields are different
    await this.page.getByRole("button", { name: "Validate" }).click();
    await expect(
      this.page.getByText(
        "Data types of ID and Description should be same in union"
      )
    ).toBeVisible();

    await this.page
      .locator(
        "form > div > div:nth-child(2) > div > div > .ant-select > .ant-select-selector"
      )
      .first()
      .click();
    await this.page.getByText("Summary").nth(1).click();
    await this.page.keyboard.press("Escape");
    await this.page.getByRole("button", { name: "Validate" }).click();
    await expect(
      this.page.getByText(
        "Number of look up columns on lhs and rhs should be same in union transformation"
      )
    ).toBeVisible();
    await this.page
      .locator(
        "form > div > div:nth-child(2) > div > div > .ant-select > .ant-select-selector"
      )
      .first()
      .click();
    await this.page.getByText("ID", { exact: true }).nth(2).click();
    await this.page.keyboard.press("Escape");
    await this.validateAndSave();
    await this.generateSheet();
    await this.verifyDataSheetGeneration();
    await expect(
      this.page.getByRole("button", { name: "Filter", exact: true })
    ).toBeEnabled({ timeout: 10000 });
  }

  async createSheetWithJoin(databook, joinType) {
    if (
      (await this.page
        .getByRole("button", { name: databook })
        .getAttribute("aria-expanded")) === "false"
    ) {
      await this.page.getByRole("button", { name: databook }).click();
    }
    await this.page
      .locator(
        `//button//span[contains(text(),'${databook}')]/ancestor::div[2]/following-sibling::div/div[1]`
      )
      .click();

    await expect(this.page.getByText("Create New Datasheet")).toBeVisible();
    await this.page.getByPlaceholder("Enter sheet name").click();
    await this.page
      .getByPlaceholder("Enter sheet name")
      .fill(joinType + " Join");
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select Object" })
      .click();
    await this.page.getByText("Transform Obj A").click();
    await this.page.getByRole("button", { name: "Create" }).click();

    await this.addTransformation("Join");
    await this.deleteTransformation("1");
    await this.addTransformation("Join");

    if (joinType !== "Left") {
      await this.page.getByLabel(joinType).click();
    }

    await this.joinWith("Transformation Book", "Cars", "1");
    await this.clickLookupColumns("lhs", "1");
    await this.selectOptions(["Summary"]);
    await this.clickLookupColumns("rhs", "1");
    await this.selectOptions(["Payee email"]);
    // To validate error message when the datatypes of the selected fields are different
    await this.page.getByRole("button", { name: "Validate" }).click();
    // Data types of Summary and Payee email should be same in join
    await expect(
      this.page.getByText(
        "Columns used to join sheets must have the same datatypes"
      )
    ).toBeVisible();
    await this.clickLookupColumns("rhs", "1");
    await this.page.keyboard.press("Backspace");
    await this.selectOptions(["Description"]);
    await this.clickOutputColumns("rhs", "1");
    await this.selectAllOptionsInDropdown();

    //To verify whether the Primary tag is displayed near the PK field in dropdown
    await expect(
      this.page.locator('//div[@title="ID"]//span[text()="Primary"]')
    ).toHaveCount(3);
    //To verify colour code of Primary Keys
    const elements = [
      " //div[@name='lhsCols']//div//div//span[span[text()='ID']]/span",
      "//div[span[text()='Data Transformation']]/div[1]//div[@name='rhsCols']//div//div//span[span[text()='ID']]/span",
    ];

    for (let element of elements) {
      await this.verifyColourCode(element, "#41098B");
    }
    await this.validateAndSave();
    await this.generateSheet();
    await this.verifyDataSheetGeneration();
    await expect(
      this.page.getByRole("button", { name: "Filter", exact: true })
    ).toBeEnabled({ timeout: 10000 });
  }

  async createSheetWithFilter(databook) {
    if (
      (await this.page
        .getByRole("button", { name: databook })
        .getAttribute("aria-expanded")) === "false"
    ) {
      await this.page.getByRole("button", { name: databook }).click();
    }
    await this.page
      .locator(
        `//button//span[contains(text(),'${databook}')]/ancestor::div[2]/following-sibling::div/div[1]`
      )
      .click();

    await expect(this.page.getByText("Create New Datasheet")).toBeVisible();
    await this.page.getByPlaceholder("Enter sheet name").click();
    await this.page.getByPlaceholder("Enter sheet name").fill("Filter");
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select Object" })
      .click();
    await this.page.getByText("Transform Obj B").click();
    await this.page.getByRole("button", { name: "Create" }).click();

    await this.addTransformation("Filter");
    await this.deleteTransformation("1");
    await this.addTransformation("Filter");

    // await this.page.locator('div').filter({ hasText: /^Data TransformationFilterValidateAdd Transformation$/ }).getByPlaceholder('Press Ctrl + H for help').click();
    await this.page.getByPlaceholder("Press Ctrl + H for help").click();
    await this.page.locator("span[title='ID']").click();
    await this.page
      .locator("li")
      .filter({ hasText: /^>$/ })
      .getByRole("listitem")
      .click();
    await this.page.getByRole("textbox").nth(2).fill("25");
    await this.page.waitForTimeout(2000);
    await this.page.getByText("25Integer").click();
    await this.page.keyboard.press("Escape");

    await this.validateAndSave();
    await this.generateSheet();
    await this.verifyDataSheetGeneration();
    await expect(
      this.page.getByRole("button", { name: "Filter", exact: true })
    ).toBeEnabled({ timeout: 10000 });
  }

  async createSheetWithGetUserProperties(databook) {
    if (
      (await this.page
        .getByRole("button", { name: databook })
        .getAttribute("aria-expanded")) === "false"
    ) {
      await this.page.getByRole("button", { name: databook }).click();
    }
    await this.page
      .locator(
        `//button//span[contains(text(),'${databook}')]/ancestor::div[2]/following-sibling::div/div[1]`
      )
      .click();

    await expect(this.page.getByText("Create New Datasheet")).toBeVisible();
    await this.page.getByPlaceholder("Enter sheet name").click();
    await this.page
      .getByPlaceholder("Enter sheet name")
      .fill("Get User Properties");
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select Object" })
      .click();
    await this.page.getByText("Transform Obj A").click();
    await this.page.getByRole("button", { name: "Create" }).click();

    await this.addTransformation("Get User Properties");
    await this.deleteTransformation("1");
    await this.addTransformation("Get User Properties");

    await this.page
      .locator("div")
      .filter({ hasText: /^Select Email Column$/ })
      .nth(2)
      .click();
    await this.page.locator(".ant-select-item-option-content").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Date Column$/ })
      .nth(2)
      .click();
    await this.page.locator("span").filter({ hasText: "CURRENT_DATE" }).click();
    await this.page
      .locator("form div")
      .filter({ hasText: "Select User Fields" })
      .nth(4)
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^First Name$/ })
      .nth(1)
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Last Name$/ })
      .nth(1)
      .click();
    await this.page.keyboard.press("Escape");

    await this.page.getByRole("button", { name: "Validate" }).click();
    await expect(
      this.page.getByRole("button", { name: "Add Transformation" })
    ).toBeEnabled();
    await expect(this.page.getByRole("button", { name: "Save" })).toBeEnabled();
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.generateSheet();
    await this.verifyDataSheetGeneration();
    await expect(
      this.page.getByRole("button", { name: "Filter", exact: true })
    ).toBeEnabled({ timeout: 10000 });
  }

  async createSheetWithTemporalSplice(databook) {
    if (
      (await this.page
        .getByRole("button", { name: databook })
        .getAttribute("aria-expanded")) === "false"
    ) {
      await this.page.getByRole("button", { name: databook }).click();
    }
    await this.page
      .locator(
        `//button//span[contains(text(),'${databook}')]/ancestor::div[2]/following-sibling::div/div[1]`
      )
      .click();

    await expect(this.page.getByText("Create New Datasheet")).toBeVisible();
    await this.page.getByPlaceholder("Enter sheet name").click();
    await this.page
      .getByPlaceholder("Enter sheet name")
      .fill("Temporal Splice");
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select Object" })
      .click();
    await this.page.getByText("Payroll Obj").click();
    await this.page.getByRole("button", { name: "Create" }).click();

    await this.addTransformation("Temporal Splice");
    await this.deleteTransformation("1");
    await this.addTransformation("Temporal Splice");

    await this.page
      .locator("div")
      .filter({ hasText: /^Select$/ })
      .nth(2)
      .click();
    await this.page.getByText("Email").nth(3).click();
    await this.page.getByRole("switch").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Start date columnSelect$/ })
      .locator("div")
      .nth(2)
      .click();
    await this.page.getByText("ESD").nth(1).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select$/ })
      .nth(2)
      .click();
    await this.page.getByText("Auto-generate").nth(2).click();
    await this.page
      .getByRole("button", { name: "Add new data source" })
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Source typeSelect$/ })
      .locator("div")
      .nth(2)
      .click();
    await this.page.getByText("Object", { exact: true }).nth(2).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Data sourceSelect$/ })
      .locator("div")
      .nth(2)
      .click();
    await this.page.getByText("Employee Obj").click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select$/ })
      .nth(2)
      .click();
    await this.page.getByText("email", { exact: true }).click();
    await this.page.getByRole("switch").nth(1).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Start date columnSelect$/ })
      .locator("div")
      .nth(2)
      .click();
    await this.page
      .locator("span")
      .filter({ hasText: "Auto-generate" })
      .nth(3)
      .click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select$/ })
      .nth(2)
      .click();
    await this.page.getByText("EED", { exact: true }).nth(4).click();
    await this.validateAndSave();
    await this.generateSheet();
    await this.verifyDataSheetGeneration();
    await expect(
      this.page.getByRole("button", { name: "Filter", exact: true })
    ).toBeEnabled({ timeout: 10000 });
  }

  async createSheetWithGroupBy(databook) {
    if (
      (await this.page
        .getByRole("button", { name: databook })
        .getAttribute("aria-expanded")) === "false"
    ) {
      await this.page.getByRole("button", { name: databook }).click();
    }
    await this.page
      .locator(
        `//button//span[contains(text(),'${databook}')]/ancestor::div[2]/following-sibling::div/div[1]`
      )
      .click();

    await expect(this.page.getByText("Create New Datasheet")).toBeVisible();
    await this.page.getByPlaceholder("Enter sheet name").click();
    await this.page.getByPlaceholder("Enter sheet name").fill("Group By");
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select Object" })
      .click();
    await this.page.getByText("Transform Obj B").click();
    await this.page.getByRole("button", { name: "Create" }).click();

    await this.addTransformation("Group by");
    await this.deleteTransformation("1");
    await this.addTransformation("Group by");
    await this.page.locator(".ant-select-selection-overflow").click();
    await this.page.getByText("Payee email").nth(1).click();
    await this.page.keyboard.press("Escape");
    await this.page.getByRole("button", { name: "Add Aggregations" }).click();
    await this.page.getByRole("menuitem", { name: "AVG" }).click();
    await this.page
      .locator(
        "div:nth-child(2) > .relative > .ant-select > .ant-select-selector"
      )
      .click();
    await this.page.getByText("ID", { exact: true }).nth(2).click();
    await this.page.keyboard.press("Escape");

    await this.validateAndSave();
    await this.generateSheet();
    await this.verifyDataSheetGeneration();
    await expect(
      this.page.getByRole("button", { name: "Filter", exact: true })
    ).toBeEnabled({ timeout: 10000 });
  }

  async createSheetWithFlattenHierarchy(databook, outputDataType) {
    if (
      (await this.page
        .getByRole("button", { name: databook })
        .getAttribute("aria-expanded")) === "false"
    ) {
      await this.page.getByRole("button", { name: databook }).click();
    }
    await this.page
      .locator(
        `//button//span[contains(text(),'${databook}')]/ancestor::div[2]/following-sibling::div/div[1]`
      )
      .click();

    await expect(this.page.getByText("Create New Datasheet")).toBeVisible();
    await this.page.getByPlaceholder("Enter sheet name").click();
    await this.page
      .getByPlaceholder("Enter sheet name")
      .fill("Flatten Hierarchy_" + outputDataType);
    await this.page.getByLabel("Datasheet").check();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Databook$/ })
      .nth(2)
      .click();
    await this.page.getByText("Transformation Book").nth(1).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Datasheet$/ })
      .nth(2)
      .click();
    await this.page
      .getByTestId("ever-select")
      .filter({ hasText: "Select Datasheet" })
      .locator("input")
      .fill("Hier");
    await this.page.locator(`//span[@title="${"Hierarchy"}"]`).click();
    await this.page.getByRole("button", { name: "Create" }).click();

    await this.addTransformation("Flatten Hierarchy");
    await this.deleteTransformation("1");
    await this.addTransformation("Flatten Hierarchy");
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Column$/ })
      .nth(2)
      .click();
    await this.page.getByText("Calc Field Automation").nth(1).click();

    if (outputDataType == "String") {
      await this.page
        .locator("div")
        .filter({ hasText: /^Output data type\*Email$/ })
        .locator("div")
        .nth(2)
        .click();
      await this.page
        .locator("span")
        .filter({ hasText: /^String$/ })
        .click();
    }

    await this.validateAndSave();
    await this.generateSheet();
    await this.verifyDataSheetGeneration();
    await expect(
      this.page.getByRole("button", { name: "Filter", exact: true })
    ).toBeEnabled({ timeout: 10000 });
  }

  async createSheetWithJoinAndFilter(databook, joinType) {
    if (
      (await this.page
        .getByRole("button", { name: databook })
        .getAttribute("aria-expanded")) === "false"
    ) {
      await this.page.getByRole("button", { name: databook }).click();
    }
    await this.page
      .locator(
        `//button//span[contains(text(),'${databook}')]/ancestor::div[2]/following-sibling::div/div[1]`
      )
      .click();

    await expect(this.page.getByText("Create New Datasheet")).toBeVisible();
    await this.page.getByPlaceholder("Enter sheet name").click();
    await this.page
      .getByPlaceholder("Enter sheet name")
      .fill(joinType + " Join & Filter");
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select Object" })
      .click();
    await this.page.getByText("Transform Obj A").click();
    await this.page.getByRole("button", { name: "Create" }).click();

    await this.addTransformation("Join");
    await this.deleteTransformation("1");
    await this.addTransformation("Join");

    if (joinType != "Left") {
      await this.page.getByLabel(joinType).click();
    }
    await this.page
      .locator(
        "div:nth-child(2) > div > div > .ant-select > .ant-select-selector > .ant-select-selection-item"
      )
      .first()
      .click();
    await this.page.getByText("Transformation Book").nth(1).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Datasheet$/ })
      .nth(2)
      .click();
    await this.page.getByText("Cars").nth(1).click();

    await this.page.locator(".ant-select-selection-overflow").first().click();
    await this.page
      .locator("div.ant-select-item-option-content>span[title='Summary']")
      .click();
    await this.page.keyboard.press("Escape");
    await this.page
      .locator(
        "div:nth-child(2) > .relative > .ant-select > .ant-select-selector > .ant-select-selection-overflow"
      )
      .first()
      .click();
    await this.page
      .locator("div.ant-select-item-option-content>span[title='Payee email']")
      .click();
    await this.page.keyboard.press("Escape");
    // To validate error message when the datatypes of the selected fields are different
    await this.page.getByRole("button", { name: "Validate" }).click();
    // Data types of Summary and Payee email should be same in join
    await expect(
      this.page.getByText(
        "Columns used to join sheets must have the same datatypes"
      )
    ).toBeVisible();
    await this.page
      .locator(
        "div:nth-child(2) > div > .ant-select > .ant-select-selector > .ant-select-selection-overflow"
      )
      .first()
      .click();
    await this.page.keyboard.press("Backspace");
    await this.page
      .locator("div.ant-select-item-option-content>span[title='Description']")
      .click();
    await this.page.keyboard.press("Escape");
    await this.page.locator('div[name="rhsCols"] input').click();
    await this.page
      .locator(
        "//div[contains(@class, 'ant-select-dropdown')]//span[text()='Select All']"
      )
      .click();
    await this.page.getByRole("button", { name: "Validate" }).click();
    await expect(
      this.page.getByRole("button", { name: "Add Transformation" })
    ).toBeEnabled();

    // Filter
    await this.addTransformation("Filter");
    await this.deleteTransformation("2");
    await this.addTransformation("Filter");

    await this.page.getByTestId("expression-input-box").click();
    await this.page
      .getByRole("listitem")
      .locator("span[title='ID']")
      .first()
      .click();
    await this.page
      .locator("li")
      .filter({ hasText: /^>$/ })
      .getByRole("listitem")
      .click();
    await this.page.getByRole("textbox").nth(2).fill("14");
    await this.page.waitForTimeout(2000);
    await this.page.getByText("14Integer").click();
    await this.page.keyboard.press("Escape");

    await this.page.getByRole("button", { name: "Validate" }).click();
    await expect(
      this.page.getByRole("button", { name: "Add Transformation" })
    ).toBeEnabled();
    await expect(this.page.getByRole("button", { name: "Save" })).toBeEnabled();
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.generateSheet();
    await this.verifyDataSheetGeneration();
    await expect(
      this.page.getByRole("button", { name: "Filter", exact: true })
    ).toBeEnabled({ timeout: 10000 });
  }

  async createSheetWithJoinAndGroupBy(databook, joinType) {
    if (
      (await this.page
        .getByRole("button", { name: databook })
        .getAttribute("aria-expanded")) === "false"
    ) {
      await this.page.getByRole("button", { name: databook }).click();
    }
    await this.page
      .locator(
        `//button//span[contains(text(),'${databook}')]/ancestor::div[2]/following-sibling::div/div[1]`
      )
      .click();

    await expect(this.page.getByText("Create New Datasheet")).toBeVisible();
    await this.page.getByPlaceholder("Enter sheet name").click();
    await this.page
      .getByPlaceholder("Enter sheet name")
      .fill(joinType + " Join & Group By");
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select Object" })
      .click();
    await this.page.getByText("Transform Obj A").click();
    await this.page.getByRole("button", { name: "Create" }).click();

    await this.addTransformation("Join");
    await this.deleteTransformation("1");
    await this.addTransformation("Join");

    if (joinType != "Left") {
      await this.page.getByLabel(joinType).click();
    }
    await this.page
      .locator(
        "div:nth-child(2) > div > div > .ant-select > .ant-select-selector > .ant-select-selection-item"
      )
      .first()
      .click();
    await this.page.getByText("Transformation Book").nth(1).click();
    await this.page
      .locator("div")
      .filter({ hasText: /^Select Datasheet$/ })
      .nth(2)
      .click();
    await this.page.getByText("Cars").nth(1).click();

    await this.page.locator(".ant-select-selection-overflow").first().click();
    await this.page
      .locator("div.ant-select-item-option-content>span[title='Summary']")
      .click();
    await this.page.keyboard.press("Escape");
    await this.page
      .locator(
        "div:nth-child(2) > .relative > .ant-select > .ant-select-selector > .ant-select-selection-overflow"
      )
      .first()
      .click();
    await this.page
      .locator("div.ant-select-item-option-content>span[title='Payee email']")
      .click();
    await this.page.keyboard.press("Escape");
    // To validate error message when the datatypes of the selected fields are different
    await this.page.getByRole("button", { name: "Validate" }).click();
    // Data types of Summary and Payee email should be same in join
    await expect(
      this.page.getByText(
        "Columns used to join sheets must have the same datatypes"
      )
    ).toBeVisible();
    await this.page
      .locator(
        "div:nth-child(2) > div > .ant-select > .ant-select-selector > .ant-select-selection-overflow"
      )
      .first()
      .click();
    await this.page.keyboard.press("Backspace");
    await this.page
      .locator("div.ant-select-item-option-content>span[title='Description']")
      .click();
    await this.page.keyboard.press("Escape");
    await this.page.locator('div[name="rhsCols"] input').click();
    await this.page
      .locator(
        "//div[contains(@class, 'ant-select-dropdown')]//span[text()='Select All']"
      )
      .click();
    await this.page.getByRole("button", { name: "Validate" }).click();
    await expect(
      this.page.getByRole("button", { name: "Add Transformation" })
    ).toBeEnabled();

    // Group By
    await this.addTransformation("Group by");
    await this.deleteTransformation("2");
    await this.addTransformation("Group by");
    await this.page
      .locator(".ant-col > .relative > .ant-select > .ant-select-selector")
      .click();
    await this.page
      .getByTestId("ever-select")
      .filter({ hasText: "Group by" })
      .locator("input")
      .fill("Payee email");
    await this.page.waitForTimeout(2000);
    await this.page
      .locator(".ant-select-item[title='Payee email']")
      .last()
      .click();

    await this.page.getByRole("button", { name: "Add Aggregations" }).click();
    await this.page.getByRole("menuitem", { name: "AVG" }).click();
    await this.page.waitForTimeout(5000);
    await this.page
      .locator(
        ".ant-row > div:nth-child(2) > .relative > .ant-select > .ant-select-selector"
      )
      .click();
    await this.page.waitForTimeout(2000);
    await this.page.locator("div.ant-select-item[title='ID']").last().click();
    await this.page.keyboard.press("Escape");

    await this.page.getByRole("button", { name: "Validate" }).click();
    await expect(
      this.page.getByRole("button", { name: "Add Transformation" })
    ).toBeEnabled();
    await expect(this.page.getByRole("button", { name: "Save" })).toBeEnabled();
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.generateSheet();
    await this.verifyDataSheetGeneration();
    await expect(
      this.page.getByRole("button", { name: "Filter", exact: true })
    ).toBeEnabled({ timeout: 10000 });
  }

  async createSheetWithFilterAndGroupBy(databook) {
    if (
      (await this.page
        .getByRole("button", { name: databook })
        .getAttribute("aria-expanded")) === "false"
    ) {
      await this.page.getByRole("button", { name: databook }).click();
    }
    await this.page
      .locator(
        `//button//span[contains(text(),'${databook}')]/ancestor::div[2]/following-sibling::div/div[1]`
      )
      .click();

    await expect(this.page.getByText("Create New Datasheet")).toBeVisible();
    await this.page.getByPlaceholder("Enter sheet name").click();
    await this.page
      .getByPlaceholder("Enter sheet name")
      .fill("Filter & Group By");
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select Object" })
      .click();
    await this.page.getByText("Transform Obj A").click();
    await this.page.getByRole("button", { name: "Create" }).click();

    await this.addTransformation("Filter");
    await this.deleteTransformation("1");
    await this.addTransformation("Filter");
    await this.page.getByPlaceholder("Press Ctrl + H for help").fill("ID");
    await this.page.locator("span[title='ID']").first().click();
    await this.page
      .locator("li")
      .filter({ hasText: /^>$/ })
      .getByRole("listitem")
      .click();
    await this.page.getByRole("textbox").nth(2).fill("9");
    await this.page.getByText("9Integer").click();
    await this.page
      .locator("li")
      .filter({ hasText: "AND" })
      .getByRole("listitem")
      .click();
    await this.page.locator("span[title='ID']").click();
    await this.page
      .locator("li")
      .filter({ hasText: /^<$/ })
      .getByRole("listitem")
      .click();
    await this.page
      .getByTestId("expression-input-box")
      .getByText("ID > 9 AND ID <")
      .click();
    await this.page.locator("input:nth-child(19)").fill("15");
    await this.page.getByText("15Integer").click();
    await this.page.keyboard.press("Escape");

    await this.page.getByRole("button", { name: "Validate" }).click();

    await this.addTransformation("Group by");
    await this.deleteTransformation("2");
    await this.addTransformation("Group by");
    await this.page
      .locator(".ant-col > .relative > .ant-select > .ant-select-selector")
      .click();
    await this.page.locator("div.ant-select-item[title='Email']").click();
    await this.page.keyboard.press("Escape");
    await expect(
      this.page.getByRole("button", { name: "Add Aggregations" })
    ).toBeVisible();
    await this.page.getByRole("button", { name: "Add Aggregations" }).click();
    await this.page.getByRole("menuitem", { name: "SUM" }).click();
    await this.page
      .locator(
        "div:nth-child(2) > .relative > .ant-select > .ant-select-selector"
      )
      .click();
    await this.page.getByTitle("ID").nth(3).click();
    await this.page.keyboard.press("Escape");

    await this.page.getByRole("button", { name: "Validate" }).click();
    await expect(
      this.page.getByRole("button", { name: "Add Transformation" })
    ).toBeEnabled();
    await this.page.getByRole("button", { name: "Save" }).click();
    await this.generateSheet();
    await this.verifyDataSheetGeneration();
    await expect(
      this.page.getByRole("button", { name: "Filter", exact: true })
    ).toBeEnabled({ timeout: 10000 });
  }

  async verifyResultcreateSheetWithFilterAndGroupBy() {
    await expect(this.page.getByRole("gridcell", { name: "23" })).toBeVisible();
    await expect(
      this.page.getByRole("gridcell", { name: "<EMAIL>" })
    ).toBeVisible();
  }

  async verifyResultcreateSheetWithJoinAndGroupBy() {
    await expect(
      this.page.getByRole("gridcell", { name: "22.5" })
    ).toBeVisible();
    await expect(
      this.page.getByRole("gridcell", { name: "<EMAIL>" })
    ).toBeVisible();
  }

  async verifyResultcreateSheetWithJoinAndFilter() {
    await expect(
      this.page.locator("[col-id='lhs_co_1_id']").filter({ hasText: "15" })
    ).toBeVisible();
    await expect(
      this.page.locator("[col-id='lhs_co_1_id']").filter({ hasText: "16" })
    ).toBeVisible();
    await expect(this.page.getByRole("gridcell", { name: "13" })).toBeHidden();
    await expect(this.page.getByRole("gridcell", { name: "11" })).toBeHidden();
  }

  async verifyResultcreateSheetWithFlattenHierarchyString() {
    await expect(
      this.page.getByRole("columnheader", {
        name: "Calc Field Automation",
        exact: true,
      })
    ).toBeVisible();
    await expect(
      this.page.getByRole("columnheader", {
        name: "Calc Field Automation as String-flattened",
      })
    ).toBeVisible();
  }

  async verifyResultcreateSheetWithFlattenHierarchyEmail() {
    await expect(
      this.page.getByRole("columnheader", {
        name: "Calc Field Automation",
        exact: true,
      })
    ).toBeVisible();
    await expect(
      this.page.getByRole("columnheader", {
        name: "Calc Field Automation as Email-flattened",
      })
    ).toBeVisible();
  }

  async verifyResultcreateSheetWithGetUserProperties() {
    await expect(
      this.page.getByRole("columnheader", { name: "Last Name" })
    ).toBeVisible();
    await expect(
      this.page.getByRole("columnheader", { name: "First Name" })
    ).toBeVisible();
    await expect(
      this.page.getByRole("gridcell", { name: "Rijo", exact: true })
    ).toHaveCount(2);
    await expect(
      this.page.getByRole("gridcell", { name: "Varghese", exact: true })
    ).toHaveCount(2);
  }

  async verifyResultcreateSheetWithTemporalSplice() {
    await expect(
      this.page.getByRole("gridcell", { name: "<EMAIL>" })
    ).toHaveCount(5);

    console.log("Verifying column values of: TS::Effective Start Date");
    const valuesReceived = await this.getColumnValues(
      "TS::Effective Start Date",
      "ts_effective_start_date"
    );
    const valuesExpected = [
      "Oct 21, 1677",
      "Dec 04, 2022",
      "May 03, 2023",
      "May 05, 2023",
      "Sep 08, 2023",
    ];
    expect(valuesReceived).toEqual(valuesExpected);

    console.log("Verifying column values of: TS::Effective End Date");
    const valuesReceived2 = await this.getColumnValues(
      "TS::Effective End Date",
      "ts_effective_end_date"
    );
    const valuesExpected2 = [
      "Dec 03, 2022",
      "May 02, 2023",
      "May 04, 2023",
      "Sep 07, 2023",
      "",
    ];
    expect(valuesReceived2).toEqual(valuesExpected2);
  }

  async verifyResultcreateSheetWithGroupBy() {
    await expect(
      this.page.getByRole("gridcell", { name: "22.50" })
    ).toBeVisible();
  }

  async verifyResultcreateSheetWithUnion() {
    await expect(
      this.page.getByRole("columnheader", { name: "Summary" })
    ).toBeVisible();
    await expect(
      this.page.getByRole("columnheader", { name: "Email" })
    ).toBeHidden();
    await expect(
      this.page.getByRole("columnheader", { name: "ID" })
    ).toBeHidden();
    await expect(
      this.page.getByRole("columnheader", { name: "Date" })
    ).toBeHidden();
    await expect(
      this.page.getByRole("columnheader", { name: "Payee email" })
    ).toBeHidden();
    await expect(
      this.page.getByRole("columnheader", { name: "Start date" })
    ).toBeHidden();
  }

  async verifyResultcreateSheetWithJoinLeft() {
    const results = ["Beans", "Carrot", "Apple", "Orange"];
    for (const num of results) {
      await expect(this.page.getByText(num, { exact: true })).toHaveCount(2);
    }
    const results2 = ["Pineapple", "Blueberry", "Peas"];
    for (const num of results2) {
      await expect(this.page.getByRole("gridcell", { name: num })).toHaveCount(
        1
      );
    }
  }

  async verifyResultcreateSheetWithJoinRight() {
    const results = ["Beans", "Carrot", "Apple", "Orange"];
    for (const num of results) {
      await expect(this.page.getByText(num, { exact: true })).toHaveCount(2);
    }
    const results2 = ["Volvo", "Benz", "Drumstick"];
    for (const num of results2) {
      await expect(this.page.getByRole("gridcell", { name: num })).toHaveCount(
        1
      );
    }
  }

  async verifyResultcreateSheetWithJoinInner() {
    const results = ["Beans", "Carrot", "Apple", "Orange"];
    for (const num of results) {
      await expect(this.page.getByText(num, { exact: true })).toHaveCount(2);
    }
    const results2 = [
      "Volvo",
      "Benz",
      "Drumstick",
      "Pineapple",
      "Blueberry",
      "Peas",
    ];
    for (const num of results2) {
      await expect(this.page.getByRole("gridcell", { name: num })).toHaveCount(
        0
      );
    }
  }

  async verifyResultcreateSheetWithJoinFull() {
    const results = ["Beans", "Carrot", "Apple", "Orange"];
    for (const num of results) {
      await expect(this.page.getByText(num, { exact: true })).toHaveCount(2);
    }
    const results2 = [
      "Volvo",
      "Benz",
      "Drumstick",
      "Pineapple",
      "Blueberry",
      "Peas",
    ];
    for (const num of results2) {
      await expect(this.page.getByRole("gridcell", { name: num })).toHaveCount(
        1
      );
    }
  }

  async joinWith(databook, datasheet, indexOfTransformation) {
    const joinWithDatabook = await this.page
      .locator(
        `//div[span[text()='Data Transformation']]/div[${indexOfTransformation}]//span[contains(text(), 'Join With')]/../following-sibling::div/div/div`
      )
      .nth(0);
    await joinWithDatabook.click();
    await this.page.keyboard.type(databook);
    await this.page.keyboard.press("Enter");

    const joinWithDatasheet = await this.page
      .locator(
        `//div[span[text()='Data Transformation']]/div[${indexOfTransformation}]//span[contains(text(), 'Join With')]/../following-sibling::div/div/div`
      )
      .nth(1);
    await joinWithDatasheet.click();
    await this.page.keyboard.type(datasheet);
    await this.page.keyboard.press("Enter");
  }

  async unionWith(databook, datasheet, indexOfTransformation) {
    const unionWithDatabook = await this.page
      .locator(
        `//div[span[text()='Data Transformation']]/div[${indexOfTransformation}]//span[contains(text(), 'Union With')]/../following-sibling::div/div/div`
      )
      .nth(0);
    await unionWithDatabook.click();
    await this.page.keyboard.type(databook);
    await this.page.keyboard.press("Enter");

    const unionWithDatasheet = await this.page
      .locator(
        `//div[span[text()='Data Transformation']]/div[${indexOfTransformation}]//span[contains(text(), 'Union With')]/../following-sibling::div/div/div`
      )
      .nth(1);
    await unionWithDatasheet.click();
    await this.page.keyboard.type(datasheet);
    await this.page.keyboard.press("Enter");
  }

  async clickLookupColumnsforUnion(side, indexOfTransformation) {
    if (side === "lhs") {
      const lhsDropdown = await this.page.locator(
        `//div[span[text()='Data Transformation']]/div[${indexOfTransformation}]//div[@name='sourceColumns']`
      );
      await lhsDropdown.click();
    } else if (side === "rhs") {
      const rhsDropdown = await this.page.locator(
        `//div[span[text()='Data Transformation']]/div[${indexOfTransformation}]//div[@name='unionSheetColumns']`
      );
      await rhsDropdown.click();
    }
  }

  async clickLookupColumns(side, indexOfTransformation) {
    if (side === "lhs") {
      const lhsDropdown = await this.page.locator(
        `//div[span[text()='Data Transformation']]/div[${indexOfTransformation}]//div[@name='lhsLookupCols']`
      );
      await lhsDropdown.click();
    } else if (side === "rhs") {
      const rhsDropdown = await this.page.locator(
        `//div[span[text()='Data Transformation']]/div[${indexOfTransformation}]//div[@name='rhsLookupCols']`
      );
      await rhsDropdown.click();
    }
  }

  async clickOutputColumns(side, indexOfTransformation) {
    if (side === "lhs") {
      const lhsDropdown = await this.page.locator(
        `//div[span[text()='Data Transformation']]/div[${indexOfTransformation}]//div[@name='lhsCols']`
      );
      await lhsDropdown.click();
    } else if (side === "rhs") {
      const lhsDropdown = await this.page.locator(
        `//div[span[text()='Data Transformation']]/div[${indexOfTransformation}]//div[@name='rhsCols']`
      );
      await lhsDropdown.click();
    }
  }

  async clickTemporalSpliceToggle() {
    await this.page.getByRole("switch").click();
  }

  async clickEmailDropdownTemporalSplice() {
    await this.page.locator('div[name="emailIdColumn"]').click();
  }

  async clickStartDateDropdownTemporalSplice() {
    await this.page
      .locator("div")
      .filter({ hasText: /^Start date columnSelect$/ })
      .locator("div")
      .nth(2)
      .click();
  }

  async clickEndDateDropdownTemporalSplice() {
    await this.page
      .locator("div")
      .filter({ hasText: /^Select$/ })
      .nth(2)
      .click();
  }

  async selectOptions(values = []) {
    for (const value of values) {
      await this.page.keyboard.type(value);
      await this.delay(2000);
      await this.page.keyboard.press("Enter");
    }
    await this.page.keyboard.press("Escape");
  }

  async delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async validateOnly() {
    await this.page.getByRole("button", { name: "Validate" }).click();
  }

  async selectAllOptionsInDropdown() {
    await this.page
      .locator(
        "//div[contains(@class, 'ant-select-dropdown')]//span[text()='Select All']"
      )
      .click();
  }

  async verifyColourCode(locator, colourCode) {
    const element = this.page.locator(locator);

    // Get the computed color
    const rgbColor = await element.evaluate((el) => getComputedStyle(el).color);
    console.log("Computed RGB Color:", rgbColor);

    // Convert RGB to Hex
    function rgbToHex(rgb) {
      const match = rgb.match(/\d+/g);
      return match
        ? `#${match
            .map((x) => Number(x).toString(16).padStart(2, "0"))
            .join("")}`.toUpperCase()
        : null;
    }

    const hexColor = rgbToHex(rgbColor);

    if (hexColor === "#41098B") {
      console.log("Color matches #41098B");
    } else {
      console.log(`Color does not match. Found: ${hexColor}`);
      throw new Error(
        `Color mismatch! Expected: ${expectedHexColor}, Found: ${hexColor}`
      );
    }
  }

  async fillJoin(
    joinType,
    JoinWithDB,
    JoinWithDS,
    currentLookupColumn,
    JoinWithLookupColumn
  ) {
    if (joinType != "Left") {
      await this.page.getByLabel(joinType).click();
    }
    await this.page
      .locator("form")
      .last()
      .getByTestId("ever-select")
      .first()
      .click();
    await this.page.getByText(`${JoinWithDB}`, { exact: true }).nth(1).click();
    const datasheetLocator = this.page.locator(
      "//div[div/div/span[text() = 'Select Datasheet']]//input"
    );
    await datasheetLocator.click();
    await datasheetLocator.fill(`${JoinWithDS}`);
    await this.page.getByText(`${JoinWithDS}`, { exact: true }).last().click();
    await this.page.locator(".ant-select-selection-overflow").first().click();
    await this.page
      .locator(
        `div.ant-select-item-option-content>span[title='${currentLookupColumn}']`
      )
      .last()
      .click();
    await this.page.keyboard.press("Escape");
    await this.page
      .locator(
        "div:nth-child(2) > .relative > .ant-select > .ant-select-selector > .ant-select-selection-overflow"
      )
      .first()
      .click();
    await this.page
      .locator(
        `div.ant-select-item-option-content>span[title='${JoinWithLookupColumn}']`
      )
      .last()
      .click();
    await this.page.keyboard.press("Escape");
    await this.page.waitForTimeout(1000);
  }

  async fillUnion(
    UnionWithDB,
    UnionWithDS,
    currentLookupColumn,
    UnionWithLookupColumn
  ) {
    await this.page
      .locator("form")
      .last()
      .getByTestId("ever-select")
      .first()
      .click();

    await this.page.getByText(`${UnionWithDB}`, { exact: true }).last().click();
    await this.page
      .locator(
        "//div[@data-testid='ever-select'][.//span[text()='Select Datasheet']]"
      )
      .last()
      .click();
    await this.page
      .locator(
        '//div[@data-testid="ever-select"][.//span[text()="Select Datasheet"]]//input'
      )
      .last()
      .fill(UnionWithDS);
    await this.page.locator(`span[title="${UnionWithDS}"]`).last().click();
    await this.page.locator('[name="sourceColumns"]').last().click();
    await this.page
      .locator('div[name="sourceColumns"] input')
      .last()
      .fill(currentLookupColumn);
    await this.page
      .locator(`span[title='${currentLookupColumn}']`)
      .last()
      .click();
    await this.page.keyboard.press("Escape");
    await this.page.locator('div[name="unionSheetColumns"]').last().click();
    await this.page
      .locator('div[name="unionSheetColumns"] input')
      .last()
      .fill(UnionWithLookupColumn);
    await this.page
      .locator(`span[title='${UnionWithLookupColumn}']`)
      .last()
      .click();
    await this.page.keyboard.press("Escape");
    await this.page.waitForTimeout(1000);
  }

  async closeButton() {
    await this.page.getByRole("button", { name: "Close" }).click();
  }

  async closePopupValidateandsave() {
    await this.page.getByRole("button", { name: "Close this page" }).click();
  }

  async letColumnsLoad(colName) {
    const locator = this.page
      .locator(`//div[@role='columnheader']//div[text()="${colName}"]`)
      .last();
    try {
      await locator.waitFor({ state: "visible", timeout: 180000 });
    } catch (error) {
      await this.page.reload();
      await locator.waitFor({ state: "visible", timeout: 180000 });
    }
    await this.delay(5000);
  }

  async getColumnValues(colName, locatorName) {
    await this.letColumnsLoad(colName);
    await this.delay(5000);
    const colmValuesLocator = this.page.locator(
      `//div[contains(@class, 'ant-tabs-tabpane-active')]//div[contains(@col-id, '${locatorName}')]`
    );
    const count = await colmValuesLocator.count();
    const values = [];
    for (let i = 0; i < count; i++) {
      const value = await colmValuesLocator.nth(i).textContent();
      values.push(value);
    }
    return values.slice(1);
  }

  async createDatasheet(databookName, objName, dsName) {
    if (
      (await this.page
        .getByRole("button", { name: databookName })
        .getAttribute("aria-expanded")) === "false"
    ) {
      await this.page.getByRole("button", { name: databookName }).click();
    }
    await this.page
      .locator(
        `//button//span[contains(text(),'${databookName}')]/ancestor::div[2]/following-sibling::div/div[1]`
      )
      .click();

    await expect(this.page.getByText("Create New Datasheet")).toBeVisible();
    await this.page.getByPlaceholder("Enter sheet name").click();
    await this.page.getByPlaceholder("Enter sheet name").fill(`${dsName}`);
    await this.page
      .getByTestId("ever-select")
      .locator("div")
      .filter({ hasText: "Select Object" })
      .click();
    await this.page.getByText(`${objName}`).click();
    await this.page.getByRole("button", { name: "Create" }).click();
  }
}
export default V2TransformationsPage;
