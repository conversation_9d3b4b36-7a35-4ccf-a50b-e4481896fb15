import { expect } from "@playwright/test";

class Mappayeeobjs {
  constructor(page) {
    this.page = page;
  }

  async clickMapPayee() {
    try {
      await this.page.getByRole("button", { name: "Map Payee" }).click();

      // Try clicking the first element
    } catch (error) {
      console.warn("First 'Map Payee' click failed, trying the second one...");
      try {
        await this.page.locator('span:text("Map Payee")').nth(1).click(); // Try clicking the second element
      } catch (error) {
        console.error("Both 'Map Payee' clicks failed:", error);
      }
    }
  }

  async clickaRecordOption(index) {
    // await this.page
    //   .locator("!h-4 !w-4 text-ever-base-content-mid h-full w-full")
    //   .nth(index)
    //   .click();
    // await this.page
    //   .locator(
    //     '//body//div//div[@bodystyle="[object Object]"]//div//div//div[2]//div[1]//div[1]//div[1]//div[3]//button[1]//div[1]//*[name()="svg"]'
    //   )
    //   .click();
    // await this.page
    //   .locator("flex text-inherit items-center justify-center h-5 w-5")
    //   .locator("!h-4 !w-4 text-ever-base-content-mid h-full w-full")
    //   .click();
    await this.page
      .locator("button.ant-btn-text.ant-dropdown-trigger div.text-inherit")
      .nth(index)
      .click();
  }

  async clickNext() {
    await this.page.getByText("Next", { exact: true }).click();
  }

  async clickPrevious() {
    await this.page.getByText("Previous", { exact: true }).click();
  }

  async selectManager(oldmanager, newmanager) {
    await this.page.getByTitle(oldmanager).last().click();
    await this.page.getByTitle(newmanager).last().click();
  }

  async clickEdit() {
    // await this.page.getByText("Edit", { exact: true }).click();
    await this.page.getByRole("menuitem", { name: "Edit" }).click();
  }

  async clickDelete() {
    await this.page.getByRole("menuitem", { name: "Delete" }).click();
    await expect(
      this.page.getByText("Are you sure you want to delete this entry?")
    ).toBeVisible();
    await this.page.getByRole("button", { name: "Delete" }).click();
  }

  async clickCustomFieldHistory() {
    await this.page.getByText("Custom Field History").click();
  }

  async clickBasicPayrollHistory() {
    await this.page.getByText("Basic & Payroll History").click();
  }

  async clickSplit() {
    // await this.page.locator("span.text-base.!font-[IBM Plex Sans]").click();
    await this.page.locator('//span[text()="Split"]').click();
  }

  async changeEffStartDate(newdatefromolddateindays) {
    // Get the current date from the input field
    const currentDate = await this.page
      .locator("#effectiveStartDate")
      .inputValue();
    // Add days
    const date = new Date(currentDate);
    date.setDate(date.getDate() + newdatefromolddateindays);
    const formattedDate = new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
    console.log("old and new dates are", currentDate, formattedDate);
    // Click the date picker and select the new date
    await this.page.locator("#effectiveStartDate").click();
    await this.page.locator("#effectiveStartDate").fill(`${formattedDate}`);
    await this.page.keyboard.press("Enter");
  }

  async inputEmployeeId(id) {
    await this.page.locator('//input[@id="employeeId"]').click();
    await this.page.locator('//input[@id="employeeId"]').fill(id);
    await this.page.keyboard.press("Enter");
  }

  async inputVariablePay(pay) {
    await this.page.locator("#variablePay").click();
    await this.page.locator("#variablePay").fill(pay);
    await this.page.keyboard.press("Enter");
  }

  async getDateValue() {
    const dateValue = await this.page
      .locator("#effectiveStartDate")
      .inputValue();
    console.log(dateValue);
  }

  async selectdropdownoptioninCF() {
    // await this.page.locator("#cf_10459_status").locator("xpath=..").click();
    // await this.page.getByRole("option", { name: "Yes" }).click();
    await this.page.getByLabel("Split and Insert").getByText("Yes").click();
    await this.page.getByTitle("No").nth(1).click(); // Clicks the second match
  }

  async clickProceed() {
    await this.page.getByText("Proceed").click();
  }

  // async verifyFieldsUpdated(noOfFields) {
  //   await expect(
  //     this.page.getByText(`${noOfFields} field updated`)
  //   ).toBeVisible();
  // }

  async verifyFieldsUpdated(noOfFields) {
    const text = `${noOfFields} field${noOfFields > 1 ? "s" : ""} updated`;
    await expect(this.page.getByText(text)).toBeVisible();
  }

  // async verifyFieldsUpdated(noOfFields) {
  //   await expect(
  //     this.page.getByText(new RegExp(`${noOfFields} fields updated`, "i"))
  //   ).toBeVisible();
  // }

  async clickApplyChangesandverifyRunCommissionPopup() {
    await this.page.getByText("Apply changes").click();
    await this.page.waitForTimeout(10000);
    await expect(
      this.page.getByText(
        "If the updated fields are part of a commission plan, please remember to run the commission sync from Settings → Commission & Data Sync to ensure the changes are reflected."
      )
    ).toBeVisible();
    await this.page.getByText("Got it").click();
  }

  async clickApplyChangesinEdit() {
    await this.page.getByText("Apply changes").click();
    await expect(
      this.page.getByText(
        "Please run the commission sync from Settings → Commission & Data Sync for the impacted user(s) to ensure the changes are applied."
      )
    ).toBeVisible();
    // Click "Got it" once after verifying the message
    await this.page.getByText("Got it").click();
  }

  async clickBackToEdit() {
    await this.page.getByText("Back to edit").click();
  }

  async clickApply() {
    await this.page.getByText("Apply changes").click();
  }

  async clickMerge() {
    await this.page
      .getByRole("button", { name: "Merge" })
      .waitFor({ state: "visible" });
    await this.page.getByRole("button", { name: "Merge" }).click();
    await expect(
      this.page.getByText(
        "Please run the commission sync from Settings → Commission & Data Sync for the impacted user(s) to ensure the changes are applied."
      )
    ).toBeVisible();
    await this.page.getByText("Got it").click();
  }

  async verifySplitDate(startdate, enddate) {
    await expect(
      this.page.locator('span:text("Start Date:") + span')
    ).toHaveText(`${startdate}`);
    await expect(this.page.locator('span:text("End Date:") + span'))
      .toHaveText(`
        ${enddate}`);
  }

  async verifytext(startdate) {
    await expect(this.page.getByText(startdate)).toBeVisible();
  }
}

export default Mappayeeobjs;
