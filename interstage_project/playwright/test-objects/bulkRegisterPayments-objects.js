class BulkRegisterPayments {
  constructor(page) {
    this.page = page;
  }

  async navigate(URL) {
    await this.page.goto(URL, { waitUntil: "networkidle" });
    await this.page.waitForTimeout(2000);
  }

  async initiatePayment(payeeEmail, period, amount) {
    await this.page.getByTestId(`pt-${payeeEmail}-register-payment`).click();
    const inputField = this.page
      .getByLabel(`Register payment${period}`)
      .locator('input[type="text"]');
    await inputField.click();
    await inputField.fill(amount);
    await this.page.waitForTimeout(2000);
  }

  async clickBulkRegister() {
    await this.page.getByRole("button", { name: "Register" }).click();
  }

  getBulkRegisterButton() {
    return this.page.getByRole("button", { name: "Register" });
  }

  async getFailedPaymentTitle() {
    return await this.page.locator("span.tracking-tight").innerText();
  }

  async getFailedPaymentMessage() {
    return await this.page
      .locator("div.ant-modal-body span.text-center")
      .innerText();
  }

  async getPaymentSuccessToast() {
    return await this.page
      .locator("span.text-ever-base-content-black")
      .innerText();
  }

  async selectPayees(payees) {
    for (const payee of payees) {
      await this.page.getByTestId(payee).check();
    }
  }

  async selectAllPayees() {
    await this.page.locator("input[name$='selectAll']").dblclick();
    await this.page.getByRole("button", { name: "Select All" }).click();
  }

  async clickbulkRegisterPayment() {
    await this.page.getByText("Register Payment").click();
  }

  async fillPaymentValues(paymentValues) {
    for (const [originalValue, newValue] of Object.entries(paymentValues)) {
      await this.page.locator(`input[value='${originalValue}']`).fill(newValue);
    }
  }

  async clickBulkLockStatements() {
    await this.page.getByText("Lock", { exact: true }).click();
  }

  async clickBulkUnlockStatements() {
    await this.page.getByText("Unlock", { exact: true }).click();
  }

  async performBulkLockingStatements() {
    await this.page.getByRole("button", { name: "Lock" }).click();
  }

  async performBulkUnLockingStatements() {
    await this.page.getByRole("button", { name: "Unlock" }).click();
  }

  async lockValidation() {
    await this.page
      .getByText("Lock status updated successfully")
      .waitFor({ state: "visible", timeout: 20000 });
  }

  async clickRequestApproval(approvalWorkflow) {
    await this.page.getByText("Request Approval").click();
    await this.page
      .getByRole("button", { name: "Ok, request approval" })
      .click();
    await this.page.getByText(approvalWorkflow).click();
  }

  async clickRevokeApproval() {
    await this.page.getByRole("button", { name: "Revoke" }).click();
    await this.page
      .getByText("Bulk approval revoking task submitted.")
      .waitFor({ state: "visible", timeout: 5000 });
    await this.page.waitForTimeout(4000);
  }

  async sendApproval() {
    await this.page
      .getByRole("button", { name: "Send Approval Request" })
      .click();
    await this.page
      .getByText("Bulk approval creation task")
      .waitFor({ state: "visible", timeout: 10000 });
  }

  async getApprovalStatus() {
    return await this.page
      .locator("div[col-id='approval_status'] div.text-xs div")
      .first()
      .innerText();
  }

  async sendRevokeApproval() {
    await this.page.getByText("Revoke Approval").click();
  }
}

export default BulkRegisterPayments;
