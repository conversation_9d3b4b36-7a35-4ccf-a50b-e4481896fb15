class PlanPage {
  constructor(page) {
    this.page = page;
  }

  async searchPlan(search_plan) {
    const search_bar = await this.page.getByPlaceholder("Search by plan name");
    await search_bar.waitFor({ state: "visible", timeout: 10000 });
    await this.page.waitForTimeout(10000);
    await this.page.getByPlaceholder("Search by plan name").fill(search_plan);
    await this.page.waitForTimeout(5000);
    const plan = await this.page.getByText(search_plan, { exact: true });
    await plan.waitFor({ state: "visible", timeout: 10000 });
  }

  async navigatetoPlan() {
    await this.page.goto("/plans", { waitUntil: "networkidle" });
  }

  async planMenu() {
    await this.page
      .locator("div.relative button.ant-dropdown-trigger")
      .first()
      .click();
  }

  async selectPlan(planName) {
    await this.page.getByText(planName, { exact: true }).click(); // Click the first occurrence
    const secondPlanElement = this.page.getByRole("button", {
      name: "Exit Canvas",
    });

    // Wait for up to 5 seconds for the element to become visible
    try {
      await secondPlanElement.waitFor({ state: "visible", timeout: 5000 });
      return true;
    } catch (error) {
      console.error(`Element "Exit Canvas" not visible within timeout.`);
      return false;
    }
  }

  async selectCriteria(criteria_name) {
    await this.page
      .locator("span")
      .filter({ hasText: criteria_name, exact: true })
      .first()
      .click();
  }

  async simulateCriteria() {
    await this.page.getByRole("button", { name: "Simulate" }).click();
  }

  async simulatePeriod(old_date, new_date, date) {
    await this.page.getByPlaceholder("Start date").click();
    await this.page.getByRole("button", { name: old_date }).click();
    await this.page.getByText(new_date, { exact: true }).click();
    await this.page.getByTitle(date).locator("div").click();
  }

  async runSimulation() {
    await this.page.getByRole("button", { name: "Run" }).click();
  }

  async verifyCommissionNumber(commission_amount) {
    const element = this.page.getByText(commission_amount);
    try {
      await element.waitFor({ state: "visible", timeout: 5000 }); // Adjust timeout as needed
      await element.click();
      return true;
    } catch (error) {
      console.error(
        `Element with text "${commission_amount}" not found or not visible.`
      );
      return false;
    }
  }
}
export default PlanPage;
