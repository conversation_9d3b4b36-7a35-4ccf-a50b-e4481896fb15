{"info": {"_postman_id": "78f2c7d5-e22b-4bf4-926b-90a364391686", "name": "Public Read API (Simple filter With Multiple Iteration)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "39993535"}, "item": [{"name": "Retrieve a Datasheet Data -Simple", "event": [{"listen": "prerequest", "script": {"exec": ["// Iteration data ", "let serialNumber = pm.iterationData.get(\"Sno\");", "", "let testcase_name = pm.iterationData.get(\"Testcase Name\");", "", "let expected_status = pm.iterationData.get(\"Status Code\");", "", "let columnData =pm.iterationData.get(\"Column\");", "", "let rawValue= pm.iterationData.get(\"Value\");", "", "let operator = pm.iterationData.get(\"Operation\");", "", "// collection data ", "let cIntegerData=pm.collectionVariables.get(\"integerColumnData\");", "", "let cPercentageData=pm.collectionVariables.get(\"percentageData\");", "", "let cDateData=pm.collectionVariables.get(\"dateData\");", "", "let cStringData=pm.collectionVariables.get(\"stringData\");", "", "let cHierarchyData=pm.collectionVariables.get(\"hierarchyData\");", "", "let cBooleanData=pm.collectionVariables.get(\"booleanData\");", "", "let cEmailData=pm.collectionVariables.get(\"emailData\");", "", "", "let column;", "", "let columnDatas = {", "    integer: cIntegerData,", "    percentage: cPercentageData, ", "    date: cDateData,", "    string: c<PERSON><PERSON><PERSON><PERSON>,", "    email: cEmailData,", "    hierarchy: cHierarchyData,", "    boolean: cBooleanData", "}", "", "", "if (columnData === 'Integer'){", "    column=columnDatas.integer;", "}else if(columnData === 'Percentage'){", "    column=columnDatas.percentage;", "}", "else if(columnData === 'Date'){", "    column=columnDatas.date;", "}", "else if(columnData === 'String'){", "    column=columnDatas.string;", "}", "else if(columnData === 'Email'){", "    column=columnDatas.email;", "}", "else if(columnData === 'Hierarchy'){", "    column=columnDatas.hierarchy;", "}", "else if(columnData === 'Boolean'){", "    column=columnDatas.boolean;", "}", "else{", "    console.log(\"Error:Unexpected Data, Check .csv file\")", "}", " ", "let parsedValue;", "let normalized = String(rawValue).trim().toLowerCase();", "", "if (normalized === \"null\") {", "    parsedValue = null;", "} else if (normalized === \"true\") {", "    parsedValue = true;", "} else if (normalized === \"false\") {", "    parsedValue = false;", "} else if (!isNaN(normalized) && normalized !== \"\") {", "    parsedValue = Number(normalized);", "} else {", "    parsedValue = `\"${rawValue}\"`;", "}", "", "", "let requestBody = `", "{", "  \"filter_params\": {", "    \"column\": \"${column}\",", "    \"operator\": \"${operator}\",", "    \"value\": ${parsedValue}", "  }", "}`;", "", "pm.variables.set(\"serialNumber\", serialNumber);", "pm.variables.set(\"testcase_name\", testcase_name);", "pm.variables.set(\"expected_status\", expected_status);", "pm.variables.set(\"dynamic_body\", requestBody);", "", "", "// Delay (0.5 Secs)", "let start = Date.now();", "while (Date.now() - start < 500) {", "}", ""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["", "let sNO=pm.variables.get(\"serialNumber\");", "let testcase_name =pm.variables.get(\"testcase_name\");", "let testcaseSummary = sNO+\" : \"+testcase_name;", "const expectedStatus = pm.variables.get(\"expected_status\");", "", "//Iterative Tests", "pm.test(testcaseSummary, () => {", "  pm.response.to.have.status(Number(expectedStatus));", "});", "", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-api-key", "value": "wArpKx1ZVUzZGrdaWOSB8dOcRJNJHYgg", "type": "text"}], "body": {"mode": "raw", "raw": "{{dynamic_body}}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL_PublicReadAPI}}/v1/datasheets/{{datasheetID}}", "host": ["{{baseURL_PublicReadAPI}}"], "path": ["v1", "datasheets", "{{datasheetID}}"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "datasheetID", "value": "88abf4ca-84bd-40e2-b345-6db3de5efcf4", "type": "string"}, {"key": "integerColumnData", "value": "amount_payee_currency", "type": "default"}, {"key": "percentageData", "value": "cf_test__1", "type": "default"}, {"key": "dateData", "value": "period_end_date", "type": "default"}, {"key": "stringData", "value": "databook_name", "type": "default"}, {"key": "emailData", "value": "payee_email_id", "type": "default"}, {"key": "hierarchyData", "value": "cf_hier_cf", "type": "default"}, {"key": "booleanData", "value": "is_locked", "type": "default"}, {"key": "baseURL_PublicReadAPI", "value": "https://api-qa.everstage.com", "type": "string"}]}