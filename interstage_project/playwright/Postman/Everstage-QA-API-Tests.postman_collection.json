{"info": {"_postman_id": "ba18ac42-5b30-4677-a517-7b1766dbf5f6", "name": "Everstage-QA-API Tests", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "39993535"}, "item": [{"name": "TSAR_Freshservice APIS", "item": [{"name": "user have support access - Time window available", "item": [{"name": "manage support access - 30m", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.expect(pm.response.code).to.equal(200);", "});", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  pm.expect(responseData.message).to.exist.and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('Membership created successfully.');", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"30m\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"<EMAIL>\",\n    \"description\":\"Test\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}, {"name": "Get Membership ID", "event": [{"listen": "test", "script": {"exec": ["responseJson = pm.response.json();", "console.log(responseJson);", "if (responseJson && responseJson.memberships && responseJson.memberships.rows) {", "responseJson.memberships.rows.forEach(function(row) {", "    if (row[10] && row[10].includes(\"<EMAIL>\")) {", "    let membershipId = row[0];  // 'membershipId' is at index 0 in the row", "    console.log(\"Found Membership <NAME_EMAIL>:\", membershipId);        ", "    pm.collectionVariables.set(\"membershipId\", membershipId);", "    }", "    });", "    } else {", "        console.log(\"No memberships found in the response.\");", "    }"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://qa.everstage.com/everstage_admin/tsar/desktop-web/memberships?quick_filter=live&search", "protocol": "https", "host": ["qa", "everstage", "com"], "path": ["everstage_admin", "tsar", "desktop-web", "memberships"], "query": [{"key": "quick_filter", "value": "live"}, {"key": "search", "value": null}]}}, "response": []}, {"name": "Revoke Membership", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\n    \"memberships\": [\n    \"{{membershipId}}\"\n    ],\n    \"revokeAll\": \"false\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://qa.everstage.com/everstage_admin/tsar/desktop-web/memberships", "protocol": "https", "host": ["qa", "everstage", "com"], "path": ["everstage_admin", "tsar", "desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - 4hr", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.expect(pm.response.code).to.equal(200);", "});", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  pm.expect(responseData.message).to.exist.and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('Membership created successfully.');", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"4h\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"<EMAIL>\",\n    \"description\":\"Test\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}, {"name": "Get Membership ID", "event": [{"listen": "test", "script": {"exec": ["responseJson = pm.response.json();", "console.log(responseJson);", "if (responseJson && responseJson.memberships && responseJson.memberships.rows) {", "responseJson.memberships.rows.forEach(function(row) {", "    if (row[10] && row[10].includes(\"<EMAIL>\")) {", "    let membershipId = row[0];  // 'membershipId' is at index 0 in the row", "    console.log(\"Found Membership <NAME_EMAIL>:\", membershipId);        ", "    pm.collectionVariables.set(\"membershipId\", membershipId);", "    }", "    });", "    } else {", "        console.log(\"No memberships found in the response.\");", "    }"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://qa.everstage.com/everstage_admin/tsar/desktop-web/memberships?quick_filter=live&search", "protocol": "https", "host": ["qa", "everstage", "com"], "path": ["everstage_admin", "tsar", "desktop-web", "memberships"], "query": [{"key": "quick_filter", "value": "live"}, {"key": "search", "value": null}]}}, "response": []}, {"name": "Revoke Membership", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\n    \"memberships\": [\n    \"{{membershipId}}\"\n    ],\n    \"revokeAll\": \"false\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://qa.everstage.com/everstage_admin/tsar/desktop-web/memberships", "protocol": "https", "host": ["qa", "everstage", "com"], "path": ["everstage_admin", "tsar", "desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - 1d", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.expect(pm.response.code).to.equal(200);", "});", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  pm.expect(responseData.message).to.exist.and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('Membership created successfully.');", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"1d\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"<EMAIL>\",\n    \"description\":\"Test\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}, {"name": "Get Membership ID", "event": [{"listen": "test", "script": {"exec": ["responseJson = pm.response.json();", "console.log(responseJson);", "if (responseJson && responseJson.memberships && responseJson.memberships.rows) {", "responseJson.memberships.rows.forEach(function(row) {", "    if (row[10] && row[10].includes(\"<EMAIL>\")) {", "    let membershipId = row[0];  // 'membershipId' is at index 0 in the row", "    console.log(\"Found Membership <NAME_EMAIL>:\", membershipId);        ", "    pm.collectionVariables.set(\"membershipId\", membershipId);", "    }", "    });", "    } else {", "        console.log(\"No memberships found in the response.\");", "    }"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://qa.everstage.com/everstage_admin/tsar/desktop-web/memberships?quick_filter=live&search", "protocol": "https", "host": ["qa", "everstage", "com"], "path": ["everstage_admin", "tsar", "desktop-web", "memberships"], "query": [{"key": "quick_filter", "value": "live"}, {"key": "search", "value": null}]}}, "response": []}, {"name": "Revoke Membership", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\n    \"memberships\": [\n    \"{{membershipId}}\"\n    ],\n    \"revokeAll\": \"false\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://qa.everstage.com/everstage_admin/tsar/desktop-web/memberships", "protocol": "https", "host": ["qa", "everstage", "com"], "path": ["everstage_admin", "tsar", "desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - 1W", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.expect(pm.response.code).to.equal(200);", "});", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  pm.expect(responseData.message).to.exist.and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('Membership created successfully.');", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"1w\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"<EMAIL>\",\n    \"description\":\"Test\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}, {"name": "Get Membership ID", "event": [{"listen": "test", "script": {"exec": ["responseJson = pm.response.json();", "console.log(responseJson);", "if (responseJson && responseJson.memberships && responseJson.memberships.rows) {", "responseJson.memberships.rows.forEach(function(row) {", "    if (row[10] && row[10].includes(\"<EMAIL>\")) {", "    let membershipId = row[0];  // 'membershipId' is at index 0 in the row", "    console.log(\"Found Membership <NAME_EMAIL>:\", membershipId);        ", "    pm.collectionVariables.set(\"membershipId\", membershipId);", "    }", "    });", "    } else {", "        console.log(\"No memberships found in the response.\");", "    }"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://qa.everstage.com/everstage_admin/tsar/desktop-web/memberships?quick_filter=live&search", "protocol": "https", "host": ["qa", "everstage", "com"], "path": ["everstage_admin", "tsar", "desktop-web", "memberships"], "query": [{"key": "quick_filter", "value": "live"}, {"key": "search", "value": null}]}}, "response": []}, {"name": "Revoke Membership", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\n    \"memberships\": [\n    \"{{membershipId}}\"\n    ],\n    \"revokeAll\": \"false\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://qa.everstage.com/everstage_admin/tsar/desktop-web/memberships", "protocol": "https", "host": ["qa", "everstage", "com"], "path": ["everstage_admin", "tsar", "desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - 30 days", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.expect(pm.response.code).to.equal(200);", "});", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  pm.expect(responseData.message).to.exist.and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('Membership created successfully.');", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"30d\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"<EMAIL>\",\n     \"description\":\"Test\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}, {"name": "Get Membership ID", "event": [{"listen": "test", "script": {"exec": ["responseJson = pm.response.json();", "console.log(responseJson);", "if (responseJson && responseJson.memberships && responseJson.memberships.rows) {", "responseJson.memberships.rows.forEach(function(row) {", "    if (row[10] && row[10].includes(\"<EMAIL>\")) {", "    let membershipId = row[0];  // 'membershipId' is at index 0 in the row", "    console.log(\"Found Membership <NAME_EMAIL>:\", membershipId);        ", "    pm.collectionVariables.set(\"membershipId\", membershipId);", "    }", "    });", "    } else {", "        console.log(\"No memberships found in the response.\");", "    }"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://qa.everstage.com/everstage_admin/tsar/desktop-web/memberships?quick_filter=live&search", "protocol": "https", "host": ["qa", "everstage", "com"], "path": ["everstage_admin", "tsar", "desktop-web", "memberships"], "query": [{"key": "quick_filter", "value": "live"}, {"key": "search", "value": null}]}}, "response": []}, {"name": "Revoke Membership", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\n    \"memberships\": [\n    \"{{membershipId}}\"\n    ],\n    \"revokeAll\": \"false\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://qa.everstage.com/everstage_admin/tsar/desktop-web/memberships", "protocol": "https", "host": ["qa", "everstage", "com"], "path": ["everstage_admin", "tsar", "desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - Custom", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", function () {", "  pm.expect(pm.response.code).to.equal(200);", "});", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  pm.expect(responseData.message).to.exist.and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('Membership created successfully.');", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"custom\", \n    \"startsAt\": \"2025-10-16 13:51\", \n    \"endsAt\": \"2025-10-30 13:51\",\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"<EMAIL>\",\n     \"description\":\"Test\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}, {"name": "Get Membership ID", "event": [{"listen": "test", "script": {"exec": ["responseJson = pm.response.json();", "console.log(responseJson);", "if (responseJson && responseJson.memberships && responseJson.memberships.rows) {", "responseJson.memberships.rows.forEach(function(row) {", "    if (row[10] && row[10].includes(\"<EMAIL>\")) {", "    let membershipId = row[0];  // 'membershipId' is at index 0 in the row", "    console.log(\"Found Membership <NAME_EMAIL>:\", membershipId);        ", "    pm.collectionVariables.set(\"membershipId\", membershipId);", "    }", "    });", "    } else {", "        console.log(\"No memberships found in the response.\");", "    }"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://qa.everstage.com/everstage_admin/tsar/desktop-web/memberships?quick_filter=scheduled&search", "protocol": "https", "host": ["qa", "everstage", "com"], "path": ["everstage_admin", "tsar", "desktop-web", "memberships"], "query": [{"key": "quick_filter", "value": "scheduled"}, {"key": "search", "value": null}]}}, "response": []}, {"name": "Revoke Membership", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\n    \"memberships\": [\n    \"{{membershipId}}\"\n    ],\n    \"revokeAll\": \"false\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://qa.everstage.com/everstage_admin/tsar/desktop-web/memberships", "protocol": "https", "host": ["qa", "everstage", "com"], "path": ["everstage_admin", "tsar", "desktop-web", "memberships"]}}, "response": []}]}]}, {"name": "user dont have support access", "item": [{"name": "manage support access - 30m", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 403\", function () {", "  pm.response.to.have.status(403);", "});", "", "", "pm.test(\"Response message is valid\", function () {", "  var responseData = pm.response.json();", "  pm.expect(responseData.message).to.be.a('string').and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('You must login to access this resource or you do not have permission to access this resource');", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"30m\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - 4hr", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 403\", function () {", "  pm.response.to.have.status(403);", "});", "", "", "pm.test(\"Response message is valid\", function () {", "  var responseData = pm.response.json();", "  pm.expect(responseData.message).to.be.a('string').and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('You must login to access this resource or you do not have permission to access this resource');", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"4h\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - 1d", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 403\", function () {", "  pm.response.to.have.status(403);", "});", "", "", "pm.test(\"Response message is valid\", function () {", "  var responseData = pm.response.json();", "  pm.expect(responseData.message).to.be.a('string').and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('You must login to access this resource or you do not have permission to access this resource');", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"1d\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - 1W", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 403\", function () {", "  pm.response.to.have.status(403);", "});", "", "", "pm.test(\"Response message is valid\", function () {", "  var responseData = pm.response.json();", "  pm.expect(responseData.message).to.be.a('string').and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('You must login to access this resource or you do not have permission to access this resource');", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"1w\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - 30 days", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 403\", function () {", "  pm.response.to.have.status(403);", "});", "", "", "pm.test(\"Response message is valid\", function () {", "  var responseData = pm.response.json();", "  pm.expect(responseData.message).to.be.a('string').and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('You must login to access this resource or you do not have permission to access this resource');", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"30d\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - Custom", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 403\", function () {", "  pm.response.to.have.status(403);", "});", "", "", "pm.test(\"Response message is valid\", function () {", "  var responseData = pm.response.json();", "  pm.expect(responseData.message).to.be.a('string').and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('You must login to access this resource or you do not have permission to access this resource');", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"custom\", \n    \"startsAt\": \"2025-10-16 13:51\", \n    \"endsAt\": \"2025-12-16 13:51\",\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}]}]}, {"name": "user have support access - Time window not available", "item": [{"name": "manage support access - 30m", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(400);", "});", "", "// Add a test to check the response schema", "pm.test(\"Response has the required schema\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      message: { type: \"string\" },", "      errorDetails: { type: \"string\" }", "    },", "    required: [\"message\", \"errorDetails\"]", "  };", "", "  pm.expect(tv4.validate(pm.response.json(), schema)).to.be.true;", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "<EMAIL>", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10009,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"30m\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - 4hr", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(400);", "});", "", "// Add a test to check the response schema", "pm.test(\"Response has the required schema\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      message: { type: \"string\" },", "      errorDetails: { type: \"string\" }", "    },", "    required: [\"message\", \"errorDetails\"]", "  };", "", "  pm.expect(tv4.validate(pm.response.json(), schema)).to.be.true;", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "<EMAIL>", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10009,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"4h\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - 1d", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(400);", "});", "", "// Add a test to check the response schema", "pm.test(\"Response has the required schema\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      message: { type: \"string\" },", "      errorDetails: { type: \"string\" }", "    },", "    required: [\"message\", \"errorDetails\"]", "  };", "", "  pm.expect(tv4.validate(pm.response.json(), schema)).to.be.true;", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "<EMAIL>", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10009,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"1d\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - 1W", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(400);", "});", "", "// Add a test to check the response schema", "pm.test(\"Response has the required schema\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      message: { type: \"string\" },", "      errorDetails: { type: \"string\" }", "    },", "    required: [\"message\", \"errorDetails\"]", "  };", "", "  pm.expect(tv4.validate(pm.response.json(), schema)).to.be.true;", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "<EMAIL>", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10009,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"1w\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - 30 days", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(400);", "});", "", "// Add a test to check the response schema", "pm.test(\"Response has the required schema\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      message: { type: \"string\" },", "      errorDetails: { type: \"string\" }", "    },", "    required: [\"message\", \"errorDetails\"]", "  };", "", "  pm.expect(tv4.validate(pm.response.json(), schema)).to.be.true;", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "<EMAIL>", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10009,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"30d\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}]}, {"name": "manage support access - Custom", "item": [{"name": "manage support admin access", "event": [{"listen": "test", "script": {"exec": ["", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(400);", "});", "", "// Add a test to check the response schema", "pm.test(\"Response has the required schema\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      message: { type: \"string\" },", "      errorDetails: { type: \"string\" }", "    },", "    required: [\"message\", \"errorDetails\"]", "  };", "", "  pm.expect(tv4.validate(pm.response.json(), schema)).to.be.true;", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "<EMAIL>", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10009,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"custom\", \n    \"startsAt\": \"2024-10-16 13:51\", \n    \"endsAt\": \"2025-12-16 13:51\",\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}]}]}, {"name": "Negative Scenarios", "item": [{"name": "Requested At and Requested By field in API", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 500\", function () {", "  pm.expect(pm.response.code).to.equal(500);", "});", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  pm.expect(responseData.error).to.eql('Internal Server Error');", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"30m\", \n    \"startsAt\": null, \n    \"endsAt\": null\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}, {"name": "Add member without everstage.com domain", "event": [{"listen": "test", "script": {"exec": ["", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(400);", "});", "", "// Add a test to check the response schema", "pm.test(\"Response has the required schema\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      message: { type: \"string\" },", "      errorDetails: { type: \"string\" }", "    },", "    required: [\"message\", \"errorDetails\"]", "  };", "", "  pm.expect(tv4.validate(pm.response.json(), schema)).to.be.true;", "});", "", "", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(400);", "});", "", "// Add a test to check the response schema", "pm.test(\"Response has the required schema\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      message: { type: \"string\" },", "      errorDetails: { type: \"string\" }", "    },", "    required: [\"message\", \"errorDetails\"]", "  };", "", "  pm.expect(tv4.validate(pm.response.json(), schema)).to.be.true;", "", "});", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  pm.expect(responseData.message).to.exist.and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('Validation Error(s) in creating membership.');", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"30m\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}, {"name": "support time less than 30 minutes", "event": [{"listen": "test", "script": {"exec": ["", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(400);", "});", "", "// Add a test to check the response schema", "pm.test(\"Response has the required schema\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      message: { type: \"string\" },", "      errorDetails: { type: \"string\" }", "    },", "    required: [\"message\", \"errorDetails\"]", "  };", "", "  pm.expect(tv4.validate(pm.response.json(), schema)).to.be.true;", "", "});", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  pm.expect(responseData.message).to.exist.and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('Validation Error(s) in creating membership.');", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10009,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"custom\", \n    \"startsAt\": \"2025-10-16 13:51\", \n    \"endsAt\": \"2025-10-16 13:55\",\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}, {"name": "support time more than 1000 days", "event": [{"listen": "test", "script": {"exec": ["", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(400);", "});", "", "// Add a test to check the response schema", "pm.test(\"Response has the required schema\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      message: { type: \"string\" },", "      errorDetails: { type: \"string\" }", "    },", "    required: [\"message\", \"errorDetails\"]", "  };", "", "  pm.expect(tv4.validate(pm.response.json(), schema)).to.be.true;", "", "});", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  pm.expect(responseData.message).to.exist.and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('Validation Error(s) in creating membership.');", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10009,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"custom\", \n    \"startsAt\": \"2025-10-16 13:51\", \n    \"endsAt\": \"2029-10-16 13:55\",\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}, {"name": "Client ID TSAR Not enabled", "event": [{"listen": "test", "script": {"exec": ["", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(400);", "});", "", "// Add a test to check the response schema", "pm.test(\"Response has the required schema\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      message: { type: \"string\" },", "      errorDetails: { type: \"string\" }", "    },", "    required: [\"message\", \"errorDetails\"]", "  };", "", "  pm.expect(tv4.validate(pm.response.json(), schema)).to.be.true;", "", "});", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  pm.expect(responseData.message).to.exist.and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('Validation Error(s) in creating membership.');", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 3038,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"custom\", \n    \"startsAt\": \"2025-10-16 13:51\", \n    \"endsAt\": \"2025-12-16 13:55\",\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}, {"name": "Invalid Freshwork Service key", "event": [{"listen": "test", "script": {"exec": ["", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(401);", "});", "", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "", "  pm.expect(responseData.message).to.exist.and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('Invalid Freshservice API Key.');", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}123", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10009,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"custom\", \n    \"startsAt\": \"2025-10-16 13:51\", \n    \"endsAt\": \"2025-12-16 13:55\",\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}, {"name": "member already have access", "event": [{"listen": "test", "script": {"exec": ["", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(400);", "});", "", "// Add a test to check the response schema", "pm.test(\"Response has the required schema\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      message: { type: \"string\" },", "      errorDetails: { type: \"string\" }", "    },", "    required: [\"message\", \"errorDetails\"]", "  };", "", "  pm.expect(tv4.validate(pm.response.json(), schema)).to.be.true;", "", "});", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "  ", "  pm.expect(responseData).to.be.an('object');", "  pm.expect(responseData.message).to.exist.and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('Validation Error(s) in creating membership.');", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10009,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"30m\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}, {"name": "support access email without everstage domain", "event": [{"listen": "test", "script": {"exec": ["", "// Add a test to compare the response against a saved value", "pm.test(\"Validate response status code\", function () {", "    pm.response.to.have.status(403);", "});", "", "", "pm.test(\"Message field is present and not empty\", function () {", "  const responseData = pm.response.json();", "", "  pm.expect(responseData.message).to.exist.and.to.not.be.empty;", "  pm.expect(responseData.message).to.eql('You must login to access this resource or you do not have permission to access this resource');", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "<EMAIL>", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"clientId\": 10010,\n    \"members\": [\n        \"<EMAIL>\"\n    ],\n    \"relativeDuration\": \"30m\", \n    \"startsAt\": null, \n    \"endsAt\": null,\n    \"requestedAt\": \"\",\n    \"requestedBy\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL}}/desktop-web/memberships", "host": ["{{baseURL}}"], "path": ["desktop-web", "memberships"]}}, "response": []}]}, {"name": "Get Client Details", "item": [{"name": "everstage domain mail with support member access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response has valid structure\", function () {", "    var jsonData = pm.response.json();", "    console.log(jsonData);", "    pm.expect(jsonData.clients).to.be.an('array');", "    jsonData.clients.forEach(function(client) {", "        pm.expect(client.clientId).to.be.a('number');", "        pm.expect(client.name).to.be.a('string');", "        pm.expect(client.logoUrl).to.be.a('string');", "        pm.expect(client.logoUrl).to.match(/^https?:\\/\\/.+/);", "    });", "});", "", "pm.test(\"Validate message is null\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.be.null;", "});", "", "pm.test(\"Validate clients array has 10 items\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.clients).to.be.an('array').that.has.lengthOf(10);", "});", "", "pm.test(\"Validate clients' data\", function () {", "    var jsonData = pm.response.json();", "    var expectedClients =  [", "        {", "            \"clientId\": 8,", "            \"name\": \"Adjustments\",", "            \"logoUrl\": \"https://dmdghj697ifd2.cloudfront.net/Logos/Adjustments\"", "        },", "        {", "            \"clientId\": 3005,", "            \"name\": \"apple\",", "            \"logoUrl\": \"https://d177w6gi8jmdvz.cloudfront.net/Logos/apple\"", "        },", "        {", "            \"clientId\": 3028,", "            \"name\": \"Forecast\",", "            \"logoUrl\": \"https://d177w6gi8jmdvz.cloudfront.net/Logos/Forecast\"", "        },", "        {", "            \"clientId\": 10079,", "            \"name\": \"Karthik - Everstage Inc\",", "            \"logoUrl\": \"https://dmdghj697ifd2.cloudfront.net/Logos/Ka<PERSON><PERSON>_-_Everstage_Inc\"", "        },", "        {", "            \"clientId\": 9878,", "            \"name\": \"PMM - Everstage Inc\",", "            \"logoUrl\": \"http://du4a3cteiciwm.cloudfront.net/Logos/Everstage..\"", "        },", "        {", "            \"clientId\": 9879,", "            \"name\": \"Product Design - Everstage Inc\",", "            \"logoUrl\": \"http://du4a3cteiciwm.cloudfront.net/Logos/Everstage..\"", "        },", "        {", "            \"clientId\": 9880,", "            \"name\": \"Product Manager - Everstage Inc\\r\\n\",", "            \"logoUrl\": \"http://du4a3cteiciwm.cloudfront.net/Logos/Everstage..\"", "        },", "        {", "            \"clientId\": 10010,", "            \"name\": \"QA-<PERSON><PERSON>-<PERSON><PERSON>\",", "            \"logoUrl\": \"https://d177w6gi8jmdvz.cloudfront.net/Logos/QA-Sanity-Jeeva\"", "        },", "        {", "            \"clientId\": 10009,", "            \"name\": \"sample client\",", "            \"logoUrl\": \"https://d177w6gi8jmdvz.cloudfront.net/Logos/sample_client\"", "        },", "        {", "            \"clientId\": 10004,", "            \"name\": \"Sud-kojima\",", "            \"logoUrl\": \"https://d177w6gi8jmdvz.cloudfront.net/Logos/Sud-kojima\"", "        }", "    ]", "        jsonData.clients.forEach(function(client, index) {", "        var expectedClient = expectedClients[index];", "        pm.expect(client.clientId).to.eql(expectedClient.clientId);", "        pm.expect(client.name).to.eql(expectedClient.name);", "        pm.expect(client.logoUrl).to.eql(expectedClient.logoUrl);", "    });", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/clients", "host": ["{{baseURL}}"], "path": ["clients"]}}, "response": []}, {"name": "everstage domain mail without support member access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response has valid structure\", function () {", "    var jsonData = pm.response.json();", "    console.log(jsonData);", "    pm.expect(jsonData.clients).to.be.an('array');", "    jsonData.clients.forEach(function(client) {", "        pm.expect(client.clientId).to.be.a('number');", "        pm.expect(client.name).to.be.a('string');", "        pm.expect(client.logoUrl).to.be.a('string');", "        pm.expect(client.logoUrl).to.match(/^https?:\\/\\/.+/);", "    });", "});", "", "pm.test(\"Validate message is null\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.be.null;", "});", "", "pm.test(\"Validate clients array has 10 items\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.clients).to.be.an('array').that.has.lengthOf(10);", "});", "", "pm.test(\"Validate clients' data\", function () {", "    var jsonData = pm.response.json();", "    var expectedClients = [", "        {", "            \"clientId\": 8,", "            \"name\": \"Adjustments\",", "            \"logoUrl\": \"https://dmdghj697ifd2.cloudfront.net/Logos/Adjustments\"", "        },", "        {", "            \"clientId\": 3005,", "            \"name\": \"apple\",", "            \"logoUrl\": \"https://d177w6gi8jmdvz.cloudfront.net/Logos/apple\"", "        },", "        {", "            \"clientId\": 3028,", "            \"name\": \"Forecast\",", "            \"logoUrl\": \"https://d177w6gi8jmdvz.cloudfront.net/Logos/Forecast\"", "        },", "        {", "            \"clientId\": 10079,", "            \"name\": \"Karthik - Everstage Inc\",", "            \"logoUrl\": \"https://dmdghj697ifd2.cloudfront.net/Logos/Ka<PERSON><PERSON>_-_Everstage_Inc\"", "        },", "        {", "            \"clientId\": 9878,", "            \"name\": \"PMM - Everstage Inc\",", "            \"logoUrl\": \"http://du4a3cteiciwm.cloudfront.net/Logos/Everstage..\"", "        },", "        {", "            \"clientId\": 9879,", "            \"name\": \"Product Design - Everstage Inc\",", "            \"logoUrl\": \"http://du4a3cteiciwm.cloudfront.net/Logos/Everstage..\"", "        },", "        {", "            \"clientId\": 9880,", "            \"name\": \"Product Manager - Everstage Inc\\r\\n\",", "            \"logoUrl\": \"http://du4a3cteiciwm.cloudfront.net/Logos/Everstage..\"", "        },", "        {", "            \"clientId\": 10010,", "            \"name\": \"QA-<PERSON><PERSON>-<PERSON><PERSON>\",", "            \"logoUrl\": \"https://d177w6gi8jmdvz.cloudfront.net/Logos/QA-Sanity-Jeeva\"", "        },", "        {", "            \"clientId\": 10009,", "            \"name\": \"sample client\",", "            \"logoUrl\": \"https://d177w6gi8jmdvz.cloudfront.net/Logos/sample_client\"", "        },", "        {", "            \"clientId\": 10004,", "            \"name\": \"Sud-kojima\",", "            \"logoUrl\": \"https://d177w6gi8jmdvz.cloudfront.net/Logos/Sud-kojima\"", "        }", "    ]", "        jsonData.clients.forEach(function(client, index) {", "        var expectedClient = expectedClients[index];", "        pm.expect(client.clientId).to.eql(expectedClient.clientId);", "        pm.expect(client.name).to.eql(expectedClient.name);", "        pm.expect(client.logoUrl).to.eql(expectedClient.logoUrl);", "    });", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/clients", "host": ["{{baseURL}}"], "path": ["clients"]}}, "response": []}, {"name": "Get Client details without everstage domain", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response has valid structure\", function () {", "    var jsonData = pm.response.json();", "    console.log(jsonData);", "    pm.expect(jsonData.message).to.be.a('string');", "});", "", "pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql('You must login to access this resource or you do not have permission to access this resource');", "});", "", "pm.test(\"Response status code is 403\", function () {", "  pm.response.to.have.status(403);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "<EMAIL>", "type": "text"}], "url": {"raw": "{{baseURL}}/clients", "host": ["{{baseURL}}"], "path": ["clients"]}}, "response": []}, {"name": "Get Client details with Invalid FreshService API Key", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response has valid structure\", function () {", "    var jsonData = pm.response.json();", "    console.log(jsonData);", "    pm.expect(jsonData.message).to.be.a('string');", "});", "", "pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql('Invalid Freshservice API Key.');", "});", "", "pm.test(\"Response status code is 401\", function () {", "  pm.response.to.have.status(401);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}tets", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "<EMAIL>", "type": "text"}], "url": {"raw": "{{baseURL}}/clients", "host": ["{{baseURL}}"], "path": ["clients"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken_TSAR_APIs}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Check Window Availablility", "item": [{"name": "mail with everstage.com( time window available)", "item": [{"name": "mail with support access - 30m", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=30m&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "30m"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail without support access - 30m", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{alternate_email}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=30m&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "30m"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail with support access - 1h", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=1h&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "1h"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail without support access - 1h", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{alternate_email}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=1h&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "1h"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail with support access - 4h", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=4h&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "4h"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail without support access - 4h", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{alternate_email}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=4h&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "4h"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail with support access - 1d", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=1d&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "1d"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail without support access - 1d", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{alternate_email}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=1d&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "1d"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail with support access - 1W", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=1w&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "1w"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail without support access - 1W", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{alternate_email}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=1w&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "1w"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail with support access - 30d", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=30d&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "30d"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail without support access - 30d", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{alternate_email}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=30d&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "30d"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail with support access - Custom", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=custom&client_id=10009&starts_at=2025-09-30 11:51&ends_at=2025-10-30 11:51", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "custom"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "2025-09-30 11:51"}, {"key": "ends_at", "value": "2025-10-30 11:51"}]}}, "response": []}, {"name": "mail without support access - Custom", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    pm.expect(pm.response.json().message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{alternate_email}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=custom&client_id=10009&starts_at=2025-09-30 11:51&ends_at=2025-10-30 11:51", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "custom"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "2025-09-30 11:51"}, {"key": "ends_at", "value": "2025-10-30 11:51"}]}}, "response": []}]}, {"name": "mail with everstage.com( time window not available)", "item": [{"name": "mail without support access - 30m", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(false);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=30m&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "30m"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail without support access - 1h", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(false);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=1h&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "1h"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail without support access - 4h", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(false);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=4h&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "4h"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail without support access - 1d", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(false);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=1d&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "1d"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail without support access - 1W", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(false);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=1w&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "1w"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail without support access - 30d", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(false);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=30d&client_id=10009&starts_at=null&ends_at=null", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "30d"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "null"}, {"key": "ends_at", "value": "null"}]}}, "response": []}, {"name": "mail without support access - Custom", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    pm.expect(pm.response.json().message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(false);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_without_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=custom&client_id=10009&starts_at=2024-11-30 11:51&ends_at=2024-12-10 11:51", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "custom"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "2024-11-30 11:51"}, {"key": "ends_at", "value": "2024-12-10 11:51"}]}}, "response": []}], "description": "Sending API Request with mail that has everstage domain but Support member time is not available for that user"}, {"name": "Negative Scenarios", "item": [{"name": "Both Relative Date and Custom is given", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(true);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=30m&client_id=10009&starts_at=2024-12-10 11:51&ends_at=2024-12-11 11:51", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "30m"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "2024-12-10 11:51"}, {"key": "ends_at", "value": "2024-12-11 11:51"}]}}, "response": []}, {"name": "Custom time less than 30 mins", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(false);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=custom&client_id=10009&starts_at=2024-12-10 11:51&ends_at=2024-12-10 11:59", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "custom"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "2024-12-10 11:51"}, {"key": "ends_at", "value": "2024-12-10 11:59"}]}}, "response": []}, {"name": "Custom time greater than 1000 days", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(false);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=custom&client_id=10009&starts_at=2024-12-10 11:51&ends_at=2028-12-10 11:59", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "custom"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "2024-12-10 11:51"}, {"key": "ends_at", "value": "2028-12-10 11:59"}]}}, "response": []}, {"name": "End Date Greater than Start Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(false);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=custom&client_id=10009&starts_at=2024-12-12 11:51&ends_at=2028-12-10 11:59", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "custom"}, {"key": "client_id", "value": "10009"}, {"key": "starts_at", "value": "2024-12-12 11:51"}, {"key": "ends_at", "value": "2028-12-10 11:59"}]}}, "response": []}, {"name": "Invalid Client ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql(null);", "});", "", "pm.test(\"Validate isTimeWindowAvailable\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.isTimeWindowAvailable).to.eql(false);", "});", "", "pm.test(\"Response status code is 200\", function () {", "  pm.response.to.have.status(200);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=custom&client_id=3038&starts_at=2024-12-10 11:51&ends_at=2028-12-12 11:59", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "custom"}, {"key": "client_id", "value": "3038"}, {"key": "starts_at", "value": "2024-12-10 11:51"}, {"key": "ends_at", "value": "2028-12-12 11:59"}]}}, "response": []}, {"name": "Invalid Freshwork Service Key", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql('Invalid Freshservice API Key.');", "});", "", "", "pm.test(\"Response status code is 401\", function () {", "  pm.response.to.have.status(401);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}12", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "{{email_support_access}}", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=custom&client_id=3038&starts_at=2024-12-10 11:51&ends_at=2028-12-12 11:59", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "custom"}, {"key": "client_id", "value": "3038"}, {"key": "starts_at", "value": "2024-12-10 11:51"}, {"key": "ends_at", "value": "2028-12-12 11:59"}]}}, "response": []}, {"name": "Without everstage domain", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.eql('You must login to access this resource or you do not have permission to access this resource');", "});", "", "", "pm.test(\"Response status code is 403\", function () {", "  pm.response.to.have.status(403);", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Freshservice-Api-Key", "value": "{{API_KEY}}", "type": "text"}, {"key": "Freshservice-Action-User-Email", "value": "<EMAIL>", "type": "text"}], "url": {"raw": "{{baseURL}}/desktop-web/check-time-window-availability?relative_duration=custom&client_id=3038&starts_at=2024-12-10 11:51&ends_at=2028-12-12 11:59", "host": ["{{baseURL}}"], "path": ["desktop-web", "check-time-window-availability"], "query": [{"key": "relative_duration", "value": "custom"}, {"key": "client_id", "value": "3038"}, {"key": "starts_at", "value": "2024-12-10 11:51"}, {"key": "ends_at", "value": "2028-12-12 11:59"}]}}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken_TSAR_APIs}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken_TSAR_APIs}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "Z-Access Control APIs", "item": [{"name": "payroll  (Statements page-power admin)", "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query payroll($payeeEmail: String!, $component: String, $effectiveDate: String, $secondaryKdAware: Boolean) {\n  employeePayForAll(\n    emailId: $payeeEmail\n    component: $component\n    effectiveDate: $effectiveDate\n    secondaryKdAware: $secondaryKdAware\n  ) {\n    employeeEmailId\n    firstName\n    lastName\n    profilePicture\n    employeePayroll {\n      variablePay\n      payoutFrequency\n      effectiveStartDate\n      effectiveEndDate\n      designation\n      employeeId\n      __typename\n    }\n    employeeHierarchy {\n      reportingManagerEmailId\n      effectiveStartDate\n      effectiveEndDate\n      managerDetails {\n        firstName\n        lastName\n        __typename\n      }\n      __typename\n    }\n    employeePlanDetails {\n      planName\n      planId\n      planCriterias {\n        criteriaId\n        criteriaName\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n}", "variables": "{\n  \"payeeEmail\": \"<EMAIL>\",\n  \"component\": \"payouts_statements\",\n  \"effectiveDate\": \"2024-02-29\",\n  \"secondaryKdAware\": true\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "allPublishedPlans (Statements page)", "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "{\n  allPublishedPlans {\n    planId\n    planName\n    __typename\n  }\n  allManagers {\n    managerDetails {\n      employeeEmailId\n      firstName\n      lastName\n      __typename\n    }\n    __typename\n  }\n  allActiveCountries {\n    countryCode\n    countryName\n    currencyCode\n    __typename\n  }\n  activeCustomFieldsByClient {\n    systemName\n    displayName\n    fieldType\n    options\n    __typename\n  }\n  periodLabelList {\n    label\n    value\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "allTeamsOfMemberUsersModule", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allTeamsOfMemberUsersModule).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "{\n  allTeamsOfMemberUsersModule(\n    teamType: \"team\"\n    memberEmailId: \"<EMAIL>\"\n  ) {\n    teamName\n    teamId\n    memberships {\n      teamId\n      groupMemberEmailId\n      effectiveStartDate\n      effectiveEndDate\n      __typename\n    }\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "RootHierarchy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.rootHierarchy).to.be.null", "})    "], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query RootHierarchy {\n  rootHierarchy {\n    employeeEmailId\n    fullName\n    hasReportee\n    exitDate\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "EmployeeDraws", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"have permission to access this data\");", "", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.employeeDraws[0]).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query EmployeeDraws($employeeEmailId: String!) {\n  employeeDraws(employeeEmailId: $employeeEmailId) {\n    drawYear\n    draws {\n      drawPeriod\n      drawAmount\n      drawTypeName\n      __typename\n    }\n    employeeEmailId\n    employee {\n      employeeEmailId\n      firstName\n      lastName\n      employeePayroll {\n        payoutFrequency\n        joiningDate\n        effectiveEndDate\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n}", "variables": "{\n  \"employeeEmailId\": \"<EMAIL>\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "EmployeeQuota", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"qc_secondary\");", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query EmployeeQuota($emailId: String!) {\n  employeeQuota(emailId: $emailId) {\n    quotaCategoryName\n    displayName\n    quotaYear\n    isTeamQuota\n    __typename\n  }\n}", "variables": "{\n  \"emailId\": \"<EMAIL>\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "EmployeeQuotaAndReporteesQuota", "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query EmployeeQuotaAndReporteesQuota($emailId: String!, $component: String) {\n  employeeQuota(emailId: $emailId) {\n    quotaCategoryName\n    displayName\n    quotaYear\n    quotaType\n    quotaScheduleType\n    isTeamQuota\n    teamType\n    effectiveStartDate\n    effectiveEndDate\n    quotaCurrencySymbol\n    scheduleQuota {\n      ramp\n      quota\n      rampedQuota\n      __typename\n    }\n    __typename\n  }\n  hasReporteesGivenComponent(emailId: $emailId, component: $component)\n  employeeNameDetail(emailId: $emailId) {\n    fullName\n    __typename\n  }\n}", "variables": "{\n  \"emailId\": \"<EMAIL>\",\n  \"component\": \"quotas_draws\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllQuotaCategoriesWithPrimary", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allQuotaCategoriesWithPrimary).to.be.null", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllQuotaCategoriesWithPrimary {\n  allQuotaCategoriesWithPrimary {\n    displayName\n    quotaCategoryName\n    quotaCurrencyType\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllPayoutCurrenciesAndBaseCurrency", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllPayoutCurrenciesAndBaseCurrency {\n  allPayoutCurrencies\n  myClient {\n    baseCurrency\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllEmployeesQuota", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.employeesByQuotaPaginated).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllEmployeesQuota($quotaCategoryName: String, $fiscalYear: String, $searchTerm: String, $offsetValue: Int, $limitValue: Int) {\n  employeesByQuotaPaginated(\n    quotaCategoryName: $quotaCategoryName\n    fiscalYear: $fiscalYear\n    searchTerm: $searchTerm\n    offsetValue: $offsetValue\n    limitValue: $limitValue\n  ) {\n    employeeEmailId\n    firstName\n    lastName\n    profilePicture\n    __typename\n  }\n}", "variables": "{\n  \"searchTerm\": null,\n  \"offsetValue\": 0,\n  \"limitValue\": 20,\n  \"quotaCategoryName\": \"Primary\",\n  \"fiscalYear\": \"2024\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "CountEmployees<PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.countEmployeesQuotaCategory).to.be.null;", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query CountEmployeesQuota($quotaCategoryName: String, $fiscalYear: String) {\n  countEmployeesQuotaCategory(\n    quotaCategoryName: $quotaCategoryName\n    fiscalYear: $fiscalYear\n  )\n}", "variables": "{\n  \"quotaCategoryName\": \"Primary\",\n  \"fiscalYear\": \"2024\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "ConnectedObjects", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.connectedObjects).to.be.null", "})"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query ConnectedObjects {\n  connectedObjects(shouldIncludeDisabledObjects: false, serviceName: \"salesforce\") {\n    integrationId\n    name\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "MyClient", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.myClient.baseCurrency).to.eql(\"USD\")", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query MyClient {\n  myClient {\n    fiscalStartMonthZero\n    baseCurrency\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllEmployeesAndRevenueLeader", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response 1\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.employeeRoleDetails).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllEmployeesAndRevenueLeader {\n  employeeRoleDetails{\n    employeeEmailId\n    firstName\n    lastName\n    canUserManageAdmins\n    __typename\n  }\n  revenueLeader {\n    effectiveStartDate\n    effectiveEndDate\n    value\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "ClientFxRatesAndAllActiveCountries", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response 1\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.clientFxRates).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query ClientFxRatesAndAllActiveCountries {\n  clientFxRates {\n    fxRatesByCurrency {\n      currency\n      fxRatesByDate {\n        date\n        fxRate\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n  allActiveCountries {\n    currencyCode\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AdminNotifications", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.adminNotifications).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AdminNotifications {\n  adminNotifications\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "GetEmailNotificationInsights", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response 1\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.emailNotificationInsights).to.be.null", "});", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query GetEmailNotificationInsights {\n  emailNotificationInsights\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "NotificationIntegrations", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"slack\");", "    pm.expect(pm.response.text()).to.include(\"ms_teams\");", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query NotificationIntegrations {\n  notificationIntegrations\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "MyClientConfig", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"showApprovalFeature\");", "    pm.expect(pm.response.text()).to.include(\"hideCategories\");", "    pm.expect(pm.response.text()).to.include(\"statementsExportInLockEmail\");", "    pm.expect(pm.response.text()).to.include(\"freezeDate\");", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query MyClientConfig {\n  myClient {\n    clientFeatures {\n      showApprovalFeature\n      hideCategories\n      statementsExportInLockEmail\n      freezeDate\n      __typename\n    }\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "ActiveCustomFieldsByClient", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"systemName\");", "    pm.expect(pm.response.text()).to.include(\"displayName\");", "    pm.expect(pm.response.text()).to.include(\"activeCustomFieldsByClient\");", "    pm.expect(pm.response.text()).to.include(\"clientProfileFields\");", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query ActiveCustomFieldsByClient {\n  activeCustomFieldsByClient {\n    systemName\n    displayName\n    __typename\n  }\n  clientProfileFields\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllSpiffPublishedPlansAscDisplayOrder", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "", "    pm.test(\"Validate JSON Response 2\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allSpiffPublishedPlansAscDisplayOrder).to.be.null", "});", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllSpiffPublishedPlansAscDisplayOrder {\n  allSpiffPublishedPlansAscDisplayOrder {\n    planId\n    planName\n    planDisplayOrder\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllCustomFieldsByClient", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allCustomFieldsByClient).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllCustomFieldsByClient {\n  allCustomFieldsByClient {\n    systemName\n    displayName\n    displayOrder\n    isArchived\n    isMandatory\n    options\n    fieldType\n    isEffectiveDated\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "allCommissionReportEnrichment", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query allCommissionReportEnrichment($filters: CommissionReportEnrichmentFiltersInput!, $searchTerm: String!, $offsetValue: Int!, $limitValue: Int!) {\n  allCommissionReportEnrichment(\n    filters: $filters\n    searchTerm: $searchTerm\n    offsetValue: $offsetValue\n    limitValue: $limitValue\n  ) {\n    commissionPlan {\n      planId\n      planName\n      __typename\n    }\n    criteria {\n      criteriaName\n      criteriaId\n      __typename\n    }\n    databook {\n      name\n      databookId\n      __typename\n    }\n    datasheet {\n      datasheetId\n      name\n      __typename\n    }\n    dataType {\n      id\n      __typename\n    }\n    systemName\n    displayName\n    reportSystemName\n    reportType\n    temporalId\n    hasDependency\n    dependency\n    __typename\n  }\n  totalRows(\n    filters: $filters\n    searchTerm: $searchTerm\n    offsetValue: $offsetValue\n    limitValue: $limitValue\n  )\n}", "variables": "{\n  \"filters\": {\n    \"commissionPlanId\": [],\n    \"criteriaId\": [],\n    \"databookId\": [],\n    \"datasheetId\": []\n  },\n  \"searchTerm\": \"\",\n  \"offsetValue\": 0,\n  \"limitValue\": 10\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "allCommissionReportEnrichmentFilters", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allCommissionReportEnrichmentFilters).to.be.null", "    ", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query allCommissionReportEnrichmentFilters {\n  allCommissionReportEnrichmentFilters {\n    commissionPlans {\n      id\n      name\n      __typename\n    }\n    criterias {\n      id\n      name\n      __typename\n    }\n    databooks {\n      id\n      name\n      __typename\n    }\n    datasheets {\n      id\n      name\n      __typename\n    }\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "CriteriaDetails", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query CriteriaDetails($planId: UUID!) {\n  criteriaDetails(planId: $planId) {\n    criteriaId\n    criteriaName\n    criteriaLevel\n    criteriaData {\n      databookId\n      datasheetId\n      __typename\n    }\n    criteriaConfig {\n      intermediateOnly\n      __typename\n    }\n    __typename\n  }\n}", "variables": "{\n  \"planId\": \"2a2ef6b8-2f94-488f-a4cd-9f52ed3873c7\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "DatabookDatasheetDetails", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.databookDetails).to.be.null", "    pm.expect(jsonData.data.datasheet).to.be.null", "});", "", "pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query DatabookDatasheetDetails($databookId: String!, $datasheetId: String!) {\n  databookDetails(databookId: $databookId) {\n    databookId\n    name\n    __typename\n  }\n  datasheet(databookId: $databookId, datasheetId: $datasheetId) {\n    datasheetId\n    name\n    variables {\n      systemName\n      displayName\n      fieldOrder\n      dataType {\n        id\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n}", "variables": "{\n  \"databookId\": \"084e7103-6df9-4de1-90ab-18fff3740eb4\",\n  \"datasheetId\": \"b57a1015-9fdc-4f49-ba45-7fc42d1555eb\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "UserGroupsQuery", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.userGroups).to.be.null", "});", "", "pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query UserGroupsQuery {\n  userGroups {\n    userGroupId\n    userGroupName\n    allMembers {\n      firstName\n      __typename\n    }\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllApprovalTemplates", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t", "", "pm.test(\"Validate JSON Response 2\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allApprovalTemplates).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllApprovalTemplates($excludeInactive: Boolean!) {\n  allApprovalTemplates(excludeInactive: $excludeInactive) {\n    templateId\n    templateName\n    templateDescription\n    knowledgeBeginDate\n    entityType\n    __typename\n  }\n}", "variables": "{\n  \"excludeInactive\": true\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "clientQuerySetting", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"clientQuerySetting\");", "    pm.expect(pm.response.text()).to.include(\"query_assignee_type\");", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query clientQuerySetting {\n  clientQuerySetting\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllEmployees-(Queries)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allEmployeeNames[0].employeeEmailId).to.include(\".com\")", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllEmployees($userStatus: String, $searchTerm: String, $offsetValue: Int, $limitValue: Int, $configUsersOnly: Bo<PERSON>an) {\n  allEmployeeNames(\n    userStatus: $userStatus\n    searchTerm: $searchTerm\n    offsetValue: $offsetValue\n    limitValue: $limitValue\n    configUsersOnly: $configUsersOnly\n  ) {\n    employeeEmailId\n    fullName\n    canUserManageAdmins\n    __typename\n  }\n}", "variables": "{\n  \"searchTerm\": null,\n  \"userStatus\": \"Active\",\n  \"configUsersOnly\": false,\n  \"offsetValue\": 0,\n  \"limitValue\": 100\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllEmployees-(Audit logs)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllEmployees($component: String) {\n  employeeNameDetails(component: $component) {\n    employeeEmailId\n    firstName\n    lastName\n    __typename\n  }\n}", "variables": "{\n  \"component\": \"settings\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllCommissionAdjustments", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allCommissionAdjustmentsV2).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllCommissionAdjustments($limitValue: Int!, $offsetValue: Int!, $searchTerm: String!, $status: String!, $ped: String!, $planId: String!) {\n  allCommissionAdjustmentsV2(\n    limitValue: $limitValue\n    offsetValue: $offsetValue\n    searchTerm: $searchTerm\n    status: $status\n    ped: $ped\n    planId: $planId\n  ) {\n    adjustmentId\n    adjustmentType\n    periodStartDate\n    periodEndDate\n    employeeEmailId\n    employeeName\n    currency\n    amount\n    planId\n    planName\n    criteriaId\n    criteriaName\n    lineItemId\n    reasonCategory\n    reason\n    commissionLocked\n    approvalStatus\n    templateId\n    isApprovalSkipped\n    skipApprovalReason\n    isAutoApproved\n    canEdit\n    canDelete\n    profilePicture\n    statementUrl\n    __typename\n  }\n  commissionAdjustmentsCount(\n    limitValue: $limitValue\n    offsetValue: $offsetValue\n    searchTerm: $searchTerm\n    status: $status\n    planId: $planId\n    ped: $ped\n  ) {\n    pending\n    all\n    __typename\n  }\n  allCurrencyCodeSymbolMap\n}", "variables": "{\n  \"limitValue\": 20,\n  \"offsetValue\": 0,\n  \"searchTerm\": \"\",\n  \"status\": \"all\",\n  \"ped\": \"all\",\n  \"planId\": \"\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "periodLabelList", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.periodLabelList[0].label).to.be.not.null", "    pm.expect(jsonData.data.periodLabelList[0].value).to.be.not.null", "})", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "{\n  periodLabelList {\n    label\n    value\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllEmployees-(Adjustments)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allEmployeesWithLimit).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllEmployees($userStatus: String, $searchTerm: String, $offsetValue: Int, $limitValue: Int, $component: String) {\n  allEmployeesWithLimit(\n    userStatus: $userStatus\n    searchTerm: $searchTerm\n    offsetValue: $offsetValue\n    limitValue: $limitValue\n    component: $component\n  ) {\n    employeeEmailId\n    firstName\n    lastName\n    userRole\n    canUserManageAdmins\n    employeePayroll {\n      payoutFrequency\n      joiningDate\n      effectiveEndDate\n      __typename\n    }\n    employeeDraw {\n      drawYear\n      draws\n      __typename\n    }\n    employeePlanDetails {\n      planId\n      planName\n      planCriterias {\n        criteriaId\n        criteriaName\n        __typename\n      }\n      __typename\n    }\n    employeeSpiffPlanDetails {\n      planId\n      planName\n      planCriterias {\n        criteriaId\n        criteriaName\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n}", "variables": "{\n  \"searchTerm\": null,\n  \"userStatus\": \"All\",\n  \"component\": \"payouts_statements\",\n  \"offsetValue\": 0,\n  \"limitValue\": 100\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllDrawAdjustments", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allDrawAdjustments).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllDrawAdjustments {\n  allDrawAdjustments {\n    adjustmentId\n    adjustmentType\n    payeeId\n    fiscalYear\n    period\n    recoverableBalance\n    amount\n    comments\n    payee {\n      employeeEmailId\n      firstName\n      lastName\n      __typename\n    }\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllDrawAdjustmentsOfPayee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allDrawAdjustmentsOfPayee).to.be.empty;", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllDrawAdjustmentsOfPayee($payeeId: String!) {\n  allDrawAdjustmentsOfPayee(payeeId: $payeeId) {\n    payeeId\n    fiscalYear\n    period\n    recoverableBalance\n    amount\n    __typename\n  }\n}", "variables": "{\n  \"payeeId\": \"<EMAIL>\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "CustomObjectsAndClientVariables", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t", "", "pm.test(\"Validate JSON Response 2\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.customObjects).to.be.null", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query CustomObjectsAndClientVariables {\n  customObjects {\n    knowledgeBeginDate\n    customObjectId\n    name\n    primaryKey\n    snapshotKey\n    createdBy\n    createdAt\n    accessTokenConfigId\n    orderedColumns\n    totalCustomObjectVariables\n    customObjectVariableLastUpdatedAt\n    __typename\n  }\n  clientVariables {\n    variableId\n    name\n    tags\n    meta {\n      knowledgeBeginDate\n      systemName\n      category\n      dataTypeId\n      model\n      tags\n      applicablePrimaryObjects\n      variableType\n      __typename\n    }\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "ReportObjects", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.reportObjectsForIntegration).to.be.null", "})   "], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query ReportObjects {\n  reportObjectsForIntegration {\n    knowledgeBeginDate\n    customObjectId\n    name\n    primaryKey\n    snapshotKey\n    createdBy\n    createdAt\n    accessTokenConfigId\n    orderedColumns\n    customObjectVariables {\n      knowledgeBeginDate\n      customObjectId\n      systemName\n      displayName\n      dataType {\n        id\n        dataType\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AccessTokenConfigs", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t", "", "pm.test(\"Validate JSON Response 2\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.accessTokenConfigs).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AccessTokenConfigs {\n  accessTokenConfigs {\n    accessTokenConfigId\n    accessType\n    serviceName\n    connectionName\n    connectionType\n    accessRequestBody\n    apiAccessKey\n    connectionStatus\n    createdOn\n    knowledgeBeginDate\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "ConnectedObjects(Settings)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "     pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.connectedObjects).to.be.null", "    ", "", "})    "], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query ConnectedObjects {\n  connectedObjects(shouldIncludeDisabledObjects: true) {\n    integrationId\n    name\n    serviceName\n    logoUrl\n    destinationObjectId\n    objectId\n    extractionEndTime\n    hasHardDeleteSync\n    isDisabled\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "GetTransformationConfigsForCustomObjectId", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.transformationConfigsForCustomObjectId).to.be.null", "    pm.expect(jsonData.data.hyperlinkedFieldForCustomObject).to.be.null", "});", "", "pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query GetTransformationConfigsForCustomObjectId($customObjectId: Int!) {\n  transformationConfigsForCustomObjectId(customObjectId: $customObjectId) {\n    id\n    sourceObjectId\n    destinationObjectId\n    sourceField\n    destinationField\n    fieldType\n    integrationId\n    __typename\n  }\n  hyperlinkedFieldForCustomObject(customObjectId: $customObjectId)\n}", "variables": "{\n  \"customObjectId\": 9\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "CustomObjectVariables", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.customObjectVariablesById).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query CustomObjectVariables($customObjectId: Int!) {\n  customObjectVariablesById(customObjectId: $customObjectId) {\n    knowledgeBeginDate\n    customObjectId\n    systemName\n    displayName\n    dataType {\n      id\n      dataType\n      __typename\n    }\n    __typename\n  }\n}", "variables": "{\n  \"customObjectId\": 9\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "GetAllMappedAndUnmappedFieldsForIntegration", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.mappedAndUnmappedFieldsForIntegration).to.be.null", "    pm.expect(jsonData.data.objectMetaInfo).to.be.null", "    ", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query GetAllMappedAndUnmappedFieldsForIntegration($serviceName: String!, $accessTokenConfigId: String!, $objectId: String!, $integrationId: String!) {\n  mappedAndUnmappedFieldsForIntegration(\n    serviceName: $serviceName\n    accessTokenConfigId: $accessTokenConfigId\n    objectId: $objectId\n    integrationId: $integrationId\n  ) {\n    unmappedFields {\n      label\n      name\n      type\n      __typename\n    }\n    mappedFields {\n      label\n      name\n      type\n      isAssociation\n      functionName\n      __typename\n    }\n    associations {\n      sourceField\n      objectId\n      __typename\n    }\n    hasHardDeleteSync\n    __typename\n  }\n  objectMetaInfo(\n    serviceName: $serviceName\n    accessTokenConfigId: $accessTokenConfigId\n    objectId: $objectId\n  ) {\n    label\n    name\n    __typename\n  }\n}", "variables": "{\n  \"serviceName\": \"hubspot\",\n  \"accessTokenConfigId\": \"59\",\n  \"objectId\": \"companies\",\n  \"integrationId\": \"892f0862-717e-4246-bba1-e1f7f726de20\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "HyperlinkDataForCustomObject", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.hyperlinkDataForCustomObject).to.be.null", "});", "", "pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query HyperlinkDataForCustomObject($customObjectId: String!) {\n  hyperlinkDataForCustomObject(customObjectId: $customObjectId) {\n    hyperlinkField\n    url\n    __typename\n  }\n}", "variables": "{\n  \"customObjectId\": \"1\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "RecentDataSyncStatus", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.recentDataSyncStatus).to.be.null", "});", "", "pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query RecentDataSyncStatus {\n  recentDataSyncStatus {\n    e2eSyncRunId\n    task\n    syncStatus\n    syncStartTime\n    syncEndTime\n    updatedBy\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "DetailedSyncStatus", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.detailedSyncStatus).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query DetailedSyncStatus {\n  detailedSyncStatus\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllPublishedPlansWithLimit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allPublishedPlansWithLimit).to.be.null", "});", "", "pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllPublishedPlansWithLimit($searchTerm: String, $offsetValue: Int, $limitValue: Int) {\n  allPublishedPlansWithLimit: allPublishedPlansWithLimit(\n    searchTerm: $searchTerm\n    offsetValue: $offsetValue\n    limitValue: $limitValue\n  ) {\n    planName\n    planId\n    __typename\n  }\n}", "variables": "{\n  \"searchTerm\": null,\n  \"offsetValue\": 0,\n  \"limitValue\": 100\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllDatabooksAndEmployeesAndReportObjects", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "     pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "    pm.expect(jsonData.data.allReportObjects).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllDatabooksAndEmployeesAndReportObjects {\n  allReportObjects {\n    id\n    name\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "EtlStatusCountAndEtlStatusHistory", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.etlStatusCount).to.be.null", "    pm.expect(jsonData.data.etlStatusHistory).to.be.null", "});", "", "pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query EtlStatusCountAndEtlStatusHistory($pageNo: Int, $pageSize: Int) {\n  etlStatusCount\n  etlStatusHistory(pageNo: $pageNo, pageSize: $pageSize) {\n    updatedBy\n    userName\n    syncStatus\n    task\n    e2eSyncRunId\n    syncStartTime\n    syncEndTime\n    syncPeriod\n    __typename\n  }\n}", "variables": "{\n  \"pageNo\": 1,\n  \"pageSize\": 30\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllDrs (Queries)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allDrs).not.to.be.empty", "", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllDrs {\n  allDrs {\n    drsId\n    logger\n    assignee\n    subject\n    status\n    category\n    loggedTime\n    involvedUsers\n    sequenceNumber\n    loggerDetails {\n      employeeEmailId\n      firstName\n      lastName\n      __typename\n    }\n    assigneeDetails {\n      employeeEmailId\n      firstName\n      lastName\n      __typename\n    }\n    drsUpdates {\n      drsId\n      meta\n      message\n      updatedBy\n      updatedTime\n      __typename\n    }\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllEmployeeNames (Queries)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allEmployeeNames[0].employeeEmailId).to.include(\".com\")", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllEmployeeNames {\n  allEmployeeNames {\n    employeeEmailId\n    firstName\n    lastName\n    canUserManageAdmins\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllUpdates", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allUpdates[0]).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllUpdates($drsId: String!) {\n  allUpdates(drsId: $drsId) {\n    drsId\n    logger\n    assignee\n    subject\n    status\n    category\n    loggedTime\n    involvedUsers\n    loggerDetails {\n      employeeEmailId\n      firstName\n      lastName\n      __typename\n    }\n    assigneeDetails {\n      employeeEmailId\n      firstName\n      lastName\n      __typename\n    }\n    drsUpdates {\n      drsId\n      meta\n      message\n      updatedBy\n      updatedTime\n      __typename\n    }\n    __typename\n  }\n}", "variables": "{\n  \"drsId\": \"bc213228-288e-4eb0-8cd3-44c7ad4edfc7\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "allApprovalRequests", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.not.include(\"error\");", "});\t", "", "pm.test(\"Validate JSON Response 2\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allApprovalRequests.requests).to.not.be.empty", "    pm.expect(jsonData.data.allApprovalRequests.requests).to.not.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query allApprovalRequests($status: String, $period: String!, $searchTerm: String, $offsetValue: Int, $limitValue: Int) {\n  allApprovalRequests(\n    status: $status\n    period: $period\n    searchTerm: $searchTerm\n    offsetValue: $offsetValue\n    limitValue: $limitValue\n  ) {\n    requests\n    requestsCount\n    __typename\n  }\n}", "variables": "{\n  \"status\": \"requested\",\n  \"period\": \"all\",\n  \"offsetValue\": 0,\n  \"limitValue\": 20,\n  \"searchTerm\": \"\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "PayeeDetails", "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query PayeeDetails {\n  payeeRole\n  hasReportee\n  hasReporteeQuota\n  hasReporteeDraws\n  hasOwnQuota\n  hasOwnDraws\n  everPartOfPlan\n  employeeBasicDetail {\n    firstName\n    lastName\n    canUserManageAdmins\n    userRoleDetails {\n      rolePermissionId\n      displayName\n      __typename\n    }\n    timeZone\n    __typename\n  }\n  getEmployeeConfig\n  hasDocusignIntegration\n  docusignLogoutUrl\n  pendingApprovalRequestCount\n  pendingCommAdjRequestCount\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllPublishedPlansAndCurrentCommissionPayouts", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allPublishedPlans).to.be.null", "    pm.expect(jsonData.data.currentCommissionPayouts).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllPublishedPlansAndCurrentCommissionPayouts($planIds: [String]!) {\n  allPublishedPlans {\n    planId\n    planName\n    __typename\n  }\n  currentCommissionPayouts(planIds: $planIds)\n}", "variables": "{\n  \"planIds\": []\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllManagersWithLimit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "     pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allManagersWithLimit).to.be.null", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllManagersWithLimit($limitValue: Int!, $firstName: String, $email: String, $searchTerm: String) {\n  allManagersWithLimit(\n    limitValue: $limitValue\n    firstName: $firstName\n    email: $email\n    searchTerm: $searchTerm\n  ) {\n    headers\n    data\n    __typename\n  }\n}", "variables": "{\n  \"limitValue\": 100\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "PayeeStatusCount", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.payeeStatusCount).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query PayeeStatusCount {\n  payeeStatusCount\n}", "variables": "{}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "DrsCounts", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.drsCounts).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query DrsCounts($payee_email: String!) {\n  drsCounts(email: $payee_email)\n}", "variables": "{\n  \"payee_email\": \"<EMAIL>\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "RevenueLeader", "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query RevenueLeader {\n  revenueLeader {\n    effectiveStartDate\n    effectiveEndDate\n    value\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllPublishedPlansAndCommissionSummaryPayoutsCount", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allPublishedPlans).to.be.null", "    pm.expect(jsonData.data.commissionSummaryPayoutsCount).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllPublishedPlansAndCommissionSummaryPayoutsCount($planIds: [String]!, $year: Int!) {\n  allPublishedPlans {\n    planId\n    planName\n    __typename\n  }\n  commissionSummaryPayoutsCount(planIds: $planIds, year: $year)\n}", "variables": "{\n  \"planIds\": [],\n  \"year\": 2024\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllQuotaCategoryTypes", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allQuotaCategoryTypes[0].label).to.eql(\"Primary Quota\")", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllQuotaCategoryTypes($year: Int!) {\n  allQuotaCategoryTypes(year: $year) {\n    label\n    value\n    __typename\n  }\n}", "variables": "{\n  \"year\": 2024\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "DynamicTeamDirectMembers", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.dynamicTeamDirectMembers).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query DynamicTeamDirectMembers($quotaYear: String, $quotaCategory: String, $quotaSchedule: String, $managerEmails: [String], $searchText: String, $limitValue: Int, $offsetValue: Int) {\n  dynamicTeamDirectMembers(\n    quotaYear: $quotaYear\n    quotaCategory: $quotaCategory\n    quotaSchedule: $quotaSchedule\n    managerEmails: $managerEmails\n    searchText: $searchText\n    limitValue: $limitValue\n    offsetValue: $offsetValue\n  )\n}", "variables": "{\n  \"managerEmails\": [\n    \"<EMAIL>\"\n  ],\n  \"quotaYear\": \"2024\",\n  \"quotaCategory\": \"qc_extra_category\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "QuotaAttainmentsCategoryForTeam", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.quotaAttainmentsCategoryForTeam.quotas).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query QuotaAttainmentsCategoryForTeam($teamOwnerId: String!, $year: Int!, $category: String) {\n  quotaAttainmentsCategoryForTeam(\n    teamOwnerId: $teamOwnerId\n    year: $year\n    category: $category\n  ) {\n    quotas\n    payeesInfo\n    quotaCategoryNames\n    displayNames {\n      label\n      value\n      __typename\n    }\n    __typename\n  }\n}", "variables": "{\n  \"teamOwnerId\": \"<EMAIL>\",\n  \"year\": 2024\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "ManagersWithLimit", "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query ManagersWithLimit($limitValue: Int!, $offsetFullName: String, $offsetEmail: String, $searchTerm: String) {\n  managersWithLimit(\n    limitValue: $limitValue\n    offsetFullName: $offsetFullName\n    offsetEmail: $offsetEmail\n    searchTerm: $searchTerm\n  ) {\n    headers\n    data\n    __typename\n  }\n}", "variables": "{\n  \"limitValue\": 100\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "Plan - Foreacst", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.plan).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query Plan($planId: UUID!, $component: String) {\n  plan: forecastPlanBasicDetails(planId: $planId, component: $component) {\n    planId\n    planName\n    planType\n    isDraft\n    planStartDate\n    planEndDate\n    isSettlementEndDate\n    settlementEndDate\n    payoutFrequency\n    totalPayeesInPlan\n    planDoc {\n      fileName\n      doc\n      __typename\n    }\n    __typename\n  }\n}", "variables": "{\n  \"planId\": \"598d9b7f-f547-45a3-b04b-995d13ff2f89\",\n  \"component\": \"payouts_statements\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllPlans - Forecast", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allPlans).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllPlans($component: String, $fiscalYear: Int, $planStatus: String, $searchTerm: String, $limitValue: Int, $offsetValue: Int, $payeesLimit: Int, $sortBy: String) {\n  allPlans: allForecastPlans(\n    component: $component\n    fiscalYear: $fiscalYear\n    planStatus: $planStatus\n    searchTerm: $searchTerm\n    limitValue: $limitValue\n    offsetValue: $offsetValue\n    sortBy: $sortBy\n  ) {\n    planId\n    planName\n    isDraft\n    planStartDate\n    planEndDate\n    payeesInPlan(limitValue: $payeesLimit) {\n      employee {\n        firstName\n        lastName\n        employeeEmailId\n        profilePicture\n        __typename\n      }\n      __typename\n    }\n    totalPayeesInPlan\n    __typename\n  }\n  planCountInFiscalYear: forecastPlanCountInFiscalYear(\n    fiscalYear: $fiscalYear\n    searchTerm: $searchTerm\n  ) {\n    allPlansCount\n    draftPlansCount\n    publishedPlansCount\n    __typename\n  }\n}", "variables": "{\n  \"searchTerm\": \"\",\n  \"component\": \"commission_plans\",\n  \"fiscalYear\": 2024,\n  \"planStatus\": \"all\",\n  \"offsetValue\": 0,\n  \"limitValue\": 12,\n  \"sortBy\": \"asc\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllPlans - Commission plan", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allPlans).to.be.null", "    pm.expect(jsonData.data.planCountInFiscalYear).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllPlans($component: String, $fiscalYear: Int, $planStatus: String, $searchTerm: String, $limitValue: Int, $offsetValue: Int, $payeesLimit: Int, $sortBy: String, $showActivePlans: Boolean, $showSpiffPlans: Boolean) {\n  allPlans: allCommissionPlans(\n    component: $component\n    fiscalYear: $fiscalYear\n    planStatus: $planStatus\n    searchTerm: $searchTerm\n    limitValue: $limitValue\n    offsetValue: $offsetValue\n    sortBy: $sortBy\n    showActivePlans: $showActivePlans\n    showSpiffPlans: $showSpiffPlans\n  ) {\n    planId\n    planName\n    isDraft\n    planStartDate\n    planEndDate\n    planType\n    payeesInPlan(limitValue: $payeesLimit) {\n      employee {\n        firstName\n        lastName\n        employeeEmailId\n        profilePicture\n        __typename\n      }\n      __typename\n    }\n    totalPayeesInPlan\n    __typename\n  }\n  planCountInFiscalYear: planCountInFiscalYear(\n    fiscalYear: $fiscalYear\n    searchTerm: $searchTerm\n    showActivePlans: $showActivePlans\n    showSpiffPlans: $showSpiffPlans\n  ) {\n    allPlansCount\n    draftPlansCount\n    publishedPlansCount\n    __typename\n  }\n}", "variables": "{\n  \"searchTerm\": \"\",\n  \"component\": \"commission_plans\",\n  \"fiscalYear\": 2024,\n  \"planStatus\": \"all\",\n  \"offsetValue\": 12,\n  \"limitValue\": 12,\n  \"showActivePlans\": false,\n  \"showSpiffPlans\": false,\n  \"sortBy\": \"asc\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "AllPlansFiscalYears - Forecast", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allPlansFiscalYears).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query AllPlansFiscalYears {\n  allPlansFiscalYears: allForecastPlansFiscalYears\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "plan - Forecast", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "     pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.plan).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query Plan($planId: UUID!, $component: String, $expressionVersion: String) {\n  plan: forecastPlanCriteriaBasicDetails(\n    planId: $planId\n    component: $component\n    expressionVersion: $expressionVersion\n  ) {\n    planCriterias {\n      criteriaId\n      criteriaName\n      criteriaType\n      criteriaDescription\n      criteriaDisplayOrder\n      criteriaData\n      criteriaColumn\n      simulateColumn\n      criteriaConfig {\n        showDoNothing\n        criteriaIsHidden\n        intermediateOnly\n        traceEnabled\n        sortCols\n        __typename\n      }\n      criteriaLevel\n      __typename\n    }\n    settlementRules\n    __typename\n  }\n}", "variables": "{\n  \"planId\": \"598d9b7f-f547-45a3-b04b-995d13ff2f89\",\n  \"component\": \"payouts_statements\",\n  \"expressionVersion\": \"v2\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "PlanDetails - Forcast", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.allPlans).to.be.null", "});", "", "pm.test(\"Validate JSON Response 2\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "});\t"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query PlanDetails($component: String, $fiscalYear: Int, $limitValue: Int, $offsetValue: Int) {\n  allPlans: allForecastPlans(\n    component: $component\n    fiscalYear: $fiscalYear\n    limitValue: $limitValue\n    offsetValue: $offsetValue\n  ) {\n    planId\n    planName\n    isDraft\n    __typename\n  }\n}", "variables": "{\n  \"component\": \"commission_plans\",\n  \"fiscalYear\": 2024\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "PlanCriteria - Forcast", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Validate JSON Response\", function () {", "    pm.expect(pm.response.text()).to.include(\"You must login to access this resource or you do not have permission to access this resource\");", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.plan).to.be.null", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "query PlanCriteria($planId: UUID!, $expressionVersion: String) {\n  plan: forecastPlanCriteriaBasicDetails(\n    planId: $planId\n    expressionVersion: $expressionVersion\n  ) {\n    planCriterias {\n      criteriaId\n      criteriaName\n      criteriaDescription\n      criteriaType\n      criteriaData\n      __typename\n    }\n    __typename\n  }\n}", "variables": "{\n  \"planId\": \"598d9b7f-f547-45a3-b04b-995d13ff2f89\",\n  \"expressionVersion\": \"v2\"\n}"}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}, {"name": "allPublishedPlans (Statements)", "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "{\n  allPublishedPlans {\n    planId\n    planName\n    __typename\n  }\n  allManagers {\n    managerDetails {\n      employeeEmailId\n      firstName\n      lastName\n      __typename\n    }\n    __typename\n  }\n  allActiveCountries {\n    countryCode\n    countryName\n    currencyCode\n    __typename\n  }\n  activeCustomFieldsByClient {\n    systemName\n    displayName\n    fieldType\n    options\n    __typename\n  }\n  periodLabelList {\n    label\n    value\n    __typename\n  }\n}", "variables": ""}}, "url": {"raw": "{{url}}/graphql", "host": ["{{url}}"], "path": ["graphql"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken_Z_Access_APIs}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "", "/* pm.test(\"Response include the specific keywords\", function () {", "   // pm.expect(pm.response.text()).to.include(\"invalid\");", "    const value = pm.response.text();", "    ", "    if(value.includes(\"you do not have permission\")||value.includes(\"null\")){", "        console.log(\"Includes Value\")", "        pm.expect(\"true\").to.include(\"true\")", "    }", "    else{", "        console.log(\"Does not include value\")", "        pm.expect(\"true\").to.include(\"false\")", "    }", "}); */", "", ""]}}]}, {"name": "Public Read APIs", "item": [{"name": "Datasheets", "item": [{"name": "List Datasheets", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", ()=> {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "wArpKx1ZVUzZGrdaWOSB8dOcRJNJHYgg", "type": "text"}], "url": {"raw": "{{baseURL_PublicReadAPI}}/v1/datasheets?page_number=1&page_size=100", "host": ["{{baseURL_PublicReadAPI}}"], "path": ["v1", "datasheets"], "query": [{"key": "page_number", "value": "1"}, {"key": "page_size", "value": "100"}]}}, "response": []}, {"name": "Retrieve Datasheet Schema", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", ()=> {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "wArpKx1ZVUzZGrdaWOSB8dOcRJNJHYgg", "type": "text"}], "url": {"raw": "{{baseURL_PublicReadAPI}}/v1/datasheets/{{datasheetID}}/schema", "host": ["{{baseURL_PublicReadAPI}}"], "path": ["v1", "datasheets", "{{datasheetID}}", "schema"]}}, "response": []}, {"name": "Retrieve a Datasheet Data", "event": [{"listen": "prerequest", "script": {"exec": ["if (pm.info.iteration > 0 && pm.info.requestName !== \"Retrieve a Datasheet Data-Simple\") {", "    console.log(\"Redirecting to target on iteration\", pm.info.iteration);", "    pm.execution.setNextRequest(\"Retrieve a Datasheet Data-Simple\");", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", ()=> {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "x-api-key", "value": "wArpKx1ZVUzZGrdaWOSB8dOcRJNJHYgg", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL_PublicReadAPI}}/v1/datasheets/{{datasheetID}}", "host": ["{{baseURL_PublicReadAPI}}"], "path": ["v1", "datasheets", "{{datasheetID}}"]}}, "response": []}, {"name": "Retrieve a Datasheet Data-Group-OR", "event": [{"listen": "prerequest", "script": {"exec": ["if (pm.info.iteration > 0 && pm.info.requestName !== \"Retrieve a Datasheet Data-Simple\") {", "    console.log(\"Redirecting to target on iteration\", pm.info.iteration);", "    pm.execution.setNextRequest(\"Retrieve a Datasheet Data-Simple\");", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", ()=> {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "x-api-key", "value": "wArpKx1ZVUzZGrdaWOSB8dOcRJNJHYgg", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"filter_params\": {\n        \"condition\": \"OR\",\n        \"filters\": [\n            {\n                \"column\": \"amount_payee_currency\",\n                \"operator\": \"=\",\n                \"value\": 27.41\n            },\n            {\n                \"column\": \"cf_test__1\",\n                \"operator\": \"=\",\n                \"value\": 0.2\n            },\n            {\n                \"column\": \"period_end_date\",\n                \"operator\": \"=\",\n                \"value\": \"2024-12-31\"\n            },\n            {\n                \"column\": \"databook_name\",\n                \"operator\": \"=\",\n                \"value\": \"ComPlanBook\"\n            },\n            {\n                \"column\": \"payee_email_id\",\n                \"operator\": \"=\",\n                \"value\": \"<EMAIL>\"\n            },\n            {\n                \"column\": \"is_locked\",\n                \"operator\": \"IS\",\n                \"value\": false\n            },\n            {\n                \"column\": \"cf_hier_cf\",\n                \"operator\": \"exact_match\",\n                \"value\": \"3e092676-cffb-46a4-a90c-db230ee66e8a\"\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL_PublicReadAPI}}/v1/datasheets/{{datasheetID}}?page_number=1&page_size=100", "host": ["{{baseURL_PublicReadAPI}}"], "path": ["v1", "datasheets", "{{datasheetID}}"], "query": [{"key": "page_number", "value": "1"}, {"key": "page_size", "value": "100"}]}}, "response": []}, {"name": "Retrieve a Datasheet Data-Group-AND", "event": [{"listen": "prerequest", "script": {"exec": ["if (pm.info.iteration > 0 && pm.info.requestName !== \"Retrieve a Datasheet Data-Simple\") {", "    console.log(\"Redirecting to target on iteration\", pm.info.iteration);", "    pm.execution.setNextRequest(\"Retrieve a Datasheet Data-Simple\");", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", ()=> {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "x-api-key", "value": "wArpKx1ZVUzZGrdaWOSB8dOcRJNJHYgg", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"filter_params\": {\n        \"condition\": \"AND\",\n        \"filters\": [\n            {\n                \"column\": \"tier_id\",\n                \"operator\":\"=\",\n                \"value\": \"\"\n            },\n            {\n                \"column\": \"quota_erosion\",\n                \"operator\":\"=\",\n                \"value\":null\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL_PublicReadAPI}}/v1/datasheets/{{datasheetID}}", "host": ["{{baseURL_PublicReadAPI}}"], "path": ["v1", "datasheets", "{{datasheetID}}"]}}, "response": []}, {"name": "Retrieve a Datasheet Data-Complex", "event": [{"listen": "prerequest", "script": {"exec": ["if (pm.info.iteration > 0 && pm.info.requestName !== \"Retrieve a Datasheet Data-Simple\") {", "    console.log(\"Redirecting to target on iteration\", pm.info.iteration);", "    pm.execution.setNextRequest(\"Retrieve a Datasheet Data-Simple\");", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", ()=> {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "x-api-key", "value": "wArpKx1ZVUzZGrdaWOSB8dOcRJNJHYgg", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"filter_params\": {\n        \"condition\": \"OR\",\n        \"groups\": [\n            {\n                \"condition\": \"AND\",\n                \"filters\": [\n                    {\n                        \"column\": \"amount_payee_currency\",\n                        \"operator\": \"=\",\n                        \"value\": 27.41\n                    },\n                    {\n                        \"column\": \"cf_test__1\",\n                        \"operator\": \">=\",\n                        \"value\": 0.2\n                    },\n                    {\n                        \"column\": \"period_end_date\",\n                        \"operator\": \"=\",\n                        \"value\": \"2024-12-31\"\n                    }\n                ]\n            },\n            {\n                \"column\": \"is_locked\",\n                \"operator\": \"IS\",\n                \"value\": true\n            },\n            {\n                \"column\": \"cf_hier_cf\",\n                \"operator\": \"!=\",\n                \"value\": \"3e092676-cffb-46a4-a90c-db230ee66e8a\"\n            },\n            {\n                \"condition\": \"AND\",\n                \"groups\": [\n                    {\n                        \"column\": \"payee_email_id\",\n                        \"operator\": \"=\",\n                        \"value\": \"<EMAIL>\"\n                    },\n                    {\n                        \"condition\": \"OR\",\n                        \"filters\": [\n                            {\n                                \"column\": \"databook_name\",\n                                \"operator\": \"=\",\n                                \"value\": \"ComPlanBook\"\n                            }\n                        ]\n                    }\n                ]\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseURL_PublicReadAPI}}/v1/datasheets/{{datasheetID}}", "host": ["{{baseURL_PublicReadAPI}}"], "path": ["v1", "datasheets", "{{datasheetID}}"]}}, "response": []}, {"name": "Retrieve a Changes in Datasheet Data", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", ()=> {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["if (pm.info.iteration > 0 && pm.info.requestName !== \"Retrieve a Datasheet Data-Simple\") {", "    console.log(\"Redirecting to target on iteration\", pm.info.iteration);", "    pm.execution.setNextRequest(\"Retrieve a Datasheet Data-Simple\");", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-api-key", "value": "wArpKx1ZVUzZGrdaWOSB8dOcRJNJHYgg", "type": "text"}], "url": {"raw": "{{baseURL_PublicReadAPI}}/v1/datasheets/{{datasheetID}}?2024-01-30&2024-05-30&page_number=1&page_size=100", "host": ["{{baseURL_PublicReadAPI}}"], "path": ["v1", "datasheets", "{{datasheetID}}"], "query": [{"key": "2024-01-30", "value": null}, {"key": "2024-05-30", "value": null}, {"key": "page_number", "value": "1"}, {"key": "page_size", "value": "100"}]}}, "response": []}]}, {"name": "Users", "item": [{"name": "List users", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response status code is 200\", ()=> {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["if (pm.info.iteration > 0 && pm.info.requestName !== \"Retrieve a Datasheet Data-Simple\") {", "    console.log(\"Redirecting to target on iteration\", pm.info.iteration);", "    pm.execution.setNextRequest(\"Retrieve a Datasheet Data-Simple\");", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "wArpKx1ZVUzZGrdaWOSB8dOcRJNJHYgg", "type": "text"}], "url": {"raw": "{{baseURL_PublicReadAPI}}/v1/users?page_number=1&page_size=100", "host": ["{{baseURL_PublicReadAPI}}"], "path": ["v1", "users"], "query": [{"key": "page_number", "value": "1"}, {"key": "page_size", "value": "100"}]}}, "response": []}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "url", "value": "https://qa.everstage.com", "type": "string"}, {"key": "email_id", "value": "<EMAIL>", "type": "string"}, {"key": "accessToken_Z_Access_APIs", "value": "", "type": "string"}, {"key": "baseURL", "value": "https://qa.everstage.com/freshservice/tsar", "type": "string"}, {"key": "API_KEY", "value": "b00446bf-ad60-4b93-969e-b69fd3db44fb", "type": "string"}, {"key": "membershipId", "value": "", "type": "string"}, {"key": "email_support_access", "value": "<EMAIL>", "type": "string"}, {"key": "email_without_support_access", "value": "<EMAIL>", "type": "string"}, {"key": "alternate_email", "value": "<EMAIL>", "type": "string"}, {"key": "accessToken_TSAR_APIs", "value": "", "type": "string"}, {"key": "baseURL_PublicReadAPI", "value": "https://api-qa.everstage.com", "type": "string"}, {"key": "datasheetID", "value": "88abf4ca-84bd-40e2-b345-6db3de5efcf4", "type": "string"}]}