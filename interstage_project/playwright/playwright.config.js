import { readdirSync } from "fs";
import { devices } from "@playwright/test";
const path = require("path");
const API_TEST_FOLDER_NAME = "api-tests";

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
require("dotenv").config();

// Project explcitly to refresh auth credentials so that rest of the tests run well
const authRefreshProject = {
  name: "auth-refresh",
  testDir: "./tests/auth-refresh/",
  use: {
    ...devices["Desktop Chrome"],
    baseURL: "http://localhost:3000",
    // We need to set this to the specific project for which credentials should be refreshed
    env: "localhost",
    headless: true,
  },
};

// Temporary project to run qa-debt related tests alone
const qaDebtProject = {
  name: "qa-debt",
  testDir: "./tests/qa-debt/",
  use: {
    ...devices["Desktop Chrome"],
    baseURL: "http://localhost:3000",
    // We need to set this to the specific project for which credentials should be refreshed
    env: "localhost",
    headless: true,
  },
};

const infraProject = {
  name: "infra",
  testDir: "./tests/infra/",
  use: {
    ...devices["Desktop Chrome"],
    baseURL: process.env.BASE_URL,
    // We need to set this to the specific project for which credentials should be refreshed
    env: "infra",
    headless: true,
  },
};

/**
 * @see https://playwright.dev/docs/test-configuration
 * @type {import('@playwright/test').PlaywrightTestConfig}
 */
const config = {
  testDir: "./tests/",
  testMatch: process.env.SMOKE_ONLY ? /.*\.smoke\.spec\.js/ : /.*\.spec\.js/,
  /* Maximum time one test can run for. */
  timeout: 180 * 1000,
  /* snapshots saved path definition */
  snapshotPathTemplate:
    "{testDir}/{testFileDir}/{testFileName}-snapshots/{arg}{ext}",
  expect: {
    /**
     * Maximum time expect() should wait for the condition to be met.
     * For example in `await expect(locator).toHaveText();`
     */
    timeout: 20000,
  },
  /* Run tests in files in parallel */
  fullyParallel: true,
  // Exit after set failures since its most likely to be an infra issue
  maxFailures: process.env.CI ? 100 : undefined,
  // maxFailures: 10,
  /*  Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : 1,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 1 : 0,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [["html"], ["blob"]],
  // Customize allure report to include run tag in suite level.
  globalTeardown: path.resolve(__dirname, "customizeAllure.js"),
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Maximum time each action such as `click()` can take. */
    actionTimeout: 60 * 1000,
    navigationTimeout: 180 * 1000,
    // To prevent excessive resource usage
    globalTimeout: 30 * 60 * 1000,
    screenshot: "on",
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: process.env.BASE_URL || "https://staging.everstage.com",

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: "retain-on-failure",

    extraHTTPHeaders: process.env.API_TEST_ONLY
      ? {
          // Add authorization token to all requests.
          Authorization: `Bearer ${process.env.API_TOKEN}`,
        }
      : {},
  },

  /**
   * Configure projects for different environments
   * These values can be accessed directly in fixtures
   */
  projects: [...getProjects(), authRefreshProject, qaDebtProject, infraProject],

  /* Folder for test artifacts such as screenshots, videos, traces, etc. */
  // outputDir: 'test-results/',
};

/**
 * This function creates a list of folders that are to be ignored
 * except the one that is provided in `notThis`
 * @param {array} folders e.g. ['staging', 'qa', 'localhost']
 * @param {string} notThis e.g. 'staging'
 * @returns {array} e.g. [ '**\/localhost/**', '**\/qa/**' ]
 */
function getIgnoreList(folders, notThis) {
  const list = [
    ...folders.filter((f) => f !== notThis).map((f) => `**/${f}/**`),
  ];
  if (!process.env.API_TEST_ONLY) {
    list.push(`**/${notThis}/${API_TEST_FOLDER_NAME}/**`);
  }
  return list;
}

export default config;

function getProjects() {
  const testFolder = path.join(__dirname, "./tests/specific/");
  // Get only the visible folders and eliminate the hidden folders.
  const folders = readdirSync(testFolder).filter(
    (file) => !file.startsWith(".")
  );
  return folders.map((f) => {
    const loginCreds = require(`${testFolder}${f}/loginCreds.js`);
    const ignoreList = getIgnoreList(folders, f);
    return {
      name: f,
      testDir: process.env.API_TEST_ONLY
        ? `${testFolder}${f}/${API_TEST_FOLDER_NAME}`
        : `${testFolder}${f}/`,
      testIgnore: ignoreList,
      use: {
        ...devices["Desktop Chrome"],
        baseURL: loginCreds.baseUrl || process.env.BASE_URL,
        env: f,
        headless: true,
      },
    };
  });
}
