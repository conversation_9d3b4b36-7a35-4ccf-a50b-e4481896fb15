import AdjustementsV2Page from "../../../../test-objects/adjustments-v2-objects";
import Notification from "../../../../test-objects/Notifications-object";
import CommissionRBAC from "../../../../test-objects/commisionRBAC-objects";
import QueriesLineItem from "../../../../test-objects/querieslineitems-objects";
import QueriesPage from "../../../../test-objects/queries-objects";
import UserPage from "../../../../test-objects/user-objects";
import ApprovalPage from "../../../../test-objects/approval-objects";
import CommonUtils from "../../../../test-objects/common-utils-objects";
import UserMapPayeePage from "../../../../test-objects/userMapPayee-objects";
import { setupGraphQLRouteInterceptor } from "../../../bearerToken";

const {
  notificationAuditFixtures: { test, expect },
} = require("../../../fixtures");

const emailId = "<EMAIL>";
const clientId = 10047;
const channels = ["ms_teams", "slack", "email"];
let dateTime = "";
let notificationName = "";
let token = "";

test.describe(
  "Validate Notification Audit Table for Notification V2",
  { tag: ["@notifications", "@regression", "@repconnect-2"] },
  () => {
    test.beforeEach(async ({ adminPage }, testInfo) => {
      testInfo.setTimeout(testInfo.timeout + 120000);
      const page = adminPage.page;
      const notification = new Notification(page);
      dateTime = await notification.getCurrentDateTime();
      token = await setupGraphQLRouteInterceptor(page);
    });

    test(
      "Validate whether notification is triggered for payee when statement is locked/unlocked and payment is registered/invalidated",
      {
        annotation: [
          {
            type: "TestID",
            description: "",
          },
          {
            type: "Description",
            description:
              "Validate notification_audit table for payee when statement is locked/unlocked and payment is registered/invalidated",
          },
          { type: "Precondition", description: "Users" },
          {
            type: "Expected Behaviour",
            description:
              "Line item should be added in notification_audit table when statement is locked/unlocked and payment is registered/invalidated ",
          },
        ],
      },
      async ({ adminPage, request }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const notification = new Notification(page);
        const commonPage = new CommonUtils(page);

        try {
          await adjustmentV2.navigate("/commissions");
          await commonPage.setStorageCommValue("September-2024");
          await adjustmentV2.invalidatePayment(emailId);
        } catch {
          console.log("Invalidate user failed");
        }
        await adjustmentV2.navigate("/commissions");
        await commonPage.setStorageCommValue("September-2024");
        await adjustmentV2.unlockStatements(emailId);
        notificationName = "COMMISSION_UNFROZEN_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
        await adjustmentV2.lockStatements(emailId);
        notificationName = "STATEMENTS_LOCKED_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
        notificationName = "COMMISSION_FROZEN_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
        await adjustmentV2.registerPayment(emailId);
        notificationName = "COMMISSION_MARKED_AS_PAID_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
        notificationName = "PAYOUT_INITIATED_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
        await adjustmentV2.invalidatePayment(emailId);
        notificationName = "COMMISSION_MARKED_AS_UNPAID_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
      }
    );

    test(
      "Validate whether notification is triggered for payee when query involving payee is initiated",
      {
        annotation: [
          {
            type: "TestID",
            description: "",
          },
          {
            type: "Description",
            description:
              "Validate notification_audit table for payee when query involved payee",
          },
          { type: "Precondition", description: "Users" },
          {
            type: "Expected Behaviour",
            description:
              "Line item should be added in notification_audit table when query involved payee is modified",
          },
        ],
      },
      async ({ adminPage, request }) => {
        const page = adminPage.page;
        const userqueriespage = new QueriesLineItem(page);
        const queriespage = new QueriesPage(page);
        const adjustmentV2 = new AdjustementsV2Page(page);
        const notification = new Notification(page);
        await adjustmentV2.navigate("/queries/allTickets");
        await userqueriespage.clickRaiseQueryBtnModule();
        await queriespage.createQuery("Test");
        await queriespage.assignQuery("Jeeva V");
        await queriespage.selectCategory("General query", "General query");
        await queriespage.enterSingleDescription("This is Test QA");
        await queriespage.summitQuery();
        notificationName = "QUERY_ASSIGNED_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
        await queriespage.selectQuery("Test");
        await queriespage.assignCC_existingQuery("Jeeva V");
        await queriespage.escapeBtn();
        await queriespage.updateQuery();
        notificationName = "CC_IN_QUERY_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
        await queriespage.updateCategory("Commission Calculation");
        await queriespage.updateQuery();
        notificationName = "QUERY_CATEGORY_CHANGE_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
        await queriespage.updateDescription("Test QA");
        await queriespage.updateQuery();
        notificationName = "QUERY_COMMENT_ADDED_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
        await queriespage.resolveQuery();
        notificationName = "QUERY_CLOSED_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
      }
    );

    test(
      "Validate whether notification is triggered for payee when payee is added to a commission plan",
      {
        annotation: [
          {
            type: "TestID",
            description: "",
          },
          {
            type: "Description",
            description:
              "Validate notification_audit table for payee payee is added to commission plan",
          },
          { type: "Precondition", description: "Users" },
          {
            type: "Expected Behaviour",
            description:
              "Line item should be added in notification_audit table when payee is added to commission plan",
          },
        ],
      },
      async ({ adminPage, request }) => {
        const page = adminPage.page;
        const commissionPlan = new CommissionRBAC(page);
        const notification = new Notification(page);
        await commissionPlan.navigate(true, "/plans");
        const user = "Admin Viewer";
        await page.locator('div[data-testid="pt-fiscal-year-select"]').click();
        await page.locator('span[title="2024"]').last().click();
        const clonedPlanName = await commissionPlan.cloneCommissionPlan("Plan", user);
        await commissionPlan.DeletePlan(clonedPlanName);
        notificationName = "PLAN_UPDATED_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
      }
    );

    test(
      "Validate whether notification is triggered for payee when payee reporting manager changes",
      {
        annotation: [
          {
            type: "TestID",
            description: "INTER-T19178",
          },
          {
            type: "Description",
            description:
              "Validate notification_audit table when payee reporting manager changes",
          },
          { type: "Precondition", description: "Users" },
          {
            type: "Expected Behaviour",
            description:
              "Line item should be added in notification_audit table when payee reporting manager changes",
          },
        ],
      },
      async ({ adminPage, request }) => {
        const page = adminPage.page;
        const userPage = new UserPage(page);
        const notification = new Notification(page);
        const mapPayeePage = new UserMapPayeePage(page);
        await userPage.navigateToUser();
        await userPage.fillSearch("jeeva");
        await userPage.waitForSearchResults();
        await userPage.mapPayeeByEmail(emailId);
        await userPage.nextAction();
        try {
          const today = new Date()
            .toLocaleDateString("en-US", { month: "short", day: "2-digit", year: "numeric" })
            .replace(/(\d{2}) (\w{3}) (\d{4})/, "$2 $1, $3");
          console.log(today);
          await mapPayeePage.reportingManagerMoreOptions(today);
          await mapPayeePage.deleteRecord();
          await mapPayeePage.clickBtn("Delete");
          await mapPayeePage.verifyToast(
            "Reporting manager deleted successfully"
          );
        } catch (error) {
          console.error("Error while deleting hierarchy:", error.message);
          // Continue with the test
        }
        await userPage.setNewmanager("payee notification");
        await userPage.cancelEndDate();
        await userPage.changeDate();
        await userPage.updateHierarchychanges();
        notificationName = "REPORTING_MANAGER_UPDATED_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
        const today = new Date()
            .toLocaleDateString("en-US", { month: "short", day: "2-digit", year: "numeric" })
            .replace(/(\d{2}) (\w{3}) (\d{4})/, "$2 $1, $3");
        await mapPayeePage.reportingManagerMoreOptions(today);
        await mapPayeePage.deleteRecord();
        await mapPayeePage.clickBtn("Delete");
        await mapPayeePage.verifyToast(
          "Reporting manager deleted successfully"
        );
        await userPage.setNewmanager("payee notification");
        await userPage.changeDate();
        await userPage.cancelEndDate();
        await userPage.updateHierarchychanges();
        notificationName = "REPORTING_MANAGER_UPDATED_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
      }
    );

    test(
      "Validate whether notification is triggered for payee when there is a new approval request",
      {
        annotation: [
          {
            type: "TestID",
            description: "",
          },
          {
            type: "Description",
            description:
              "Validate notification_audit table when there is a new approval request",
          },
          {
            type: "Expected Behaviour",
            description:
              "Line item should be added in notification_audit table when there is a new approval request",
          },
        ],
      },
      async ({ adminPage, request }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const notification = new Notification(page);
        const commonPage = new CommonUtils(page);
        const approvalpage = new ApprovalPage(page);

        await adjustmentV2.navigate("/commissions");
        await commonPage.setStorageCommValue("September-2024");
        await approvalpage.requestApprovalByEmail(
          "<EMAIL>",
          "Workflow QA"
        );
        await approvalpage.revokeApprovalByEmail("<EMAIL>");
        notificationName = "NEW_APPROVAL_REQUEST_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime
        );
      }
    );
  }
);
