import { setupGraphQLRouteInterceptor } from "../../../bearerToken";
import AdjustmentV2 from "../../../../test-objects/adjustments-v2-objects";
import Notification from "../../../../test-objects/Notifications-object";

require("dotenv").config();
let token = "";

const {
    emailStatementsFixtures: { test},
  } = require("../../../fixtures");


  test.beforeEach(async ({ adminPage }) => {
    const page = adminPage.page;
    await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
  });


  test.describe.skip(
    "commission adjustment custom category validation check",
    { tag: ["@statement", "@regression", "@repconnect-2"] },
    () => {
        test.beforeEach(async ({ adminPage }, testInfo) => {
            testInfo.setTimeout(testInfo.timeout + 120000);
            const page = adminPage.page;
            const notification = new Notification(page);
            dateTime = await notification.getCurrentDateTime();
            token = await setupGraphQLRouteInterceptor(page);
          });
      
      test("Email validation for commission adjustment custom category", async ({
        adminPage,
        request,
      }) => {
        test.info().annotations.push(
          {
            type: "Test ID",
            description: "",
          },
          {
            type: "Description",
            description:
              "Email validation for commission adjustment custom category",
          },
          {
            type: "Precondition",
            description:
              "Email validation for commission adjustment custom category",
          },
          {
            type: "Expected Behaviour",
            description:
              "Email validation for commission adjustment custom category",
          }
        );
        const page = adminPage.page;
        token = await setupGraphQLRouteInterceptor(page);
        const adjustmentV2 = new AdjustmentV2(page);
        const notification = new Notification(page);
        await adjustmentV2.navigateToAdjustments();
        const notificationName = "NEW_APPROVAL_REQUEST_NOTIFICATION";
        await notification.checkNotificationExist(
          request,
          token,
          emailId,
          clientId,
          notificationName,
          channels,
          dateTime  
        );
      });
    }  
  );
  
