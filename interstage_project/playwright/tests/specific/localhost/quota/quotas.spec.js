import CommissionsSyncPrevPeriod from "../../../../test-objects/commissionSyncPrevPeriod-objects";
import CommissionSync from "../../../../test-objects/commissionSync-objects";

const {
  quotaFixtures: { test, expect },
  quotaPayeeFixtures: { test: test1, expect: expect1 },
  quotaFunctionFixtures: { test: test2, expect: expect2 },
} = require("../../../fixtures");

test.describe(
  "Quotas",
  { tag: ["@quota", "@regression", "@primelogic-4"] },
  () => {
    test("Check all hierarchy and non hierarchy, exited users present", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page.getByLabel("plus-square").locator("svg").click();
      await expect(page.getByText("super quota").first()).toBeVisible();
      await expect(page.getByText("Payee test").first()).toBeVisible();
      await expect(
        page.getByText("Admin test", { exact: true }).first()
      ).toBeVisible();
      await expect(page.getByText("super quota").first()).toBeVisible();
      await expect(page.getByText("annual quota").first()).toBeVisible();
      await expect(page.getByText("payee quota").first()).toBeVisible();
      await expect(page.getByText("Super Admin test").first()).toBeVisible();
      await expect(page.getByText("full freq").first()).toBeVisible();
      const searchInput = page.getByPlaceholder("Search by name or email");
      await searchInput.click();
      await searchInput.fill("Admin test");
      await expect(
        page.getByText("Admin test", { exact: true }).first()
      ).toBeVisible();
    });

    test("Updated hieracrhy check", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/users", {
        waitUntil: "networkidle",
      });
      await page
        .getByPlaceholder("Search by name or email", { exact: true })
        .fill("super admin test");
      await page.waitForTimeout(1000);
      await page.getByText("Super Admin testsuperadmin.").click();
      await page.waitForTimeout(1000);
      await page.getByRole("button", { name: "Next" }).click();
      await page.waitForTimeout(1000);
      await page.getByLabel("Reporting Manager*").click();
      await page.getByText("payee quota").last().click();
      await page.getByRole("button", { name: "Save Changes" }).click();
      await page.getByLabel("Close", { exact: true }).click();
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page.getByPlaceholder("Search by name or email").click();
      await page
        .getByPlaceholder("Search by name or email")
        .fill("payee quota");
      await page.getByRole("listitem").locator("div").click();
      await page.getByTitle("payee quota").first().click();
      await page.getByLabel("plus-square").locator("svg").click();
      await expect(page.getByText("Super Admin test").first()).toBeVisible();
    });

    test("Add quota and duplicate quota validations", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page.getByPlaceholder("Search by name or email").click();
      await page.getByPlaceholder("Search by name or email").fill("payee test");
      await page.getByText("Payee test").first().click();
      await page.getByRole("button", { name: "Add Quota" }).click();
      await page
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select Category" })
        .click();
      await page.locator(".ant-select-item-option-content").click();
      await page.getByPlaceholder("Select Year").click();
      await page.getByPlaceholder("Select Year").fill("2023");
      await page.getByPlaceholder("Select Year").press("Enter");
      await page.getByLabel("Enter new quota values").check();
      await page
        .locator("div")
        .filter({ hasText: /^Select Quota$/ })
        .nth(2)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Monthly$/ })
        .nth(2)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Schedule frequency$/ })
        .nth(1)
        .click();
      await page.locator(".ant-select-item-option-content").last().click();
      await page.locator(".ant-input-number-input").first().click();
      await page.locator(".ant-input-number-input").first().fill("100");
      await page
        .locator("div")
        .filter({ hasText: /^Use this quota for other payees$/ })
        .nth(2)
        .click();
      await page.getByRole("switch").click();
      await page
        .locator(".mt-8 > div > .relative > .ant-select > .ant-select-selector")
        .click();
      await page.getByText("super quota").nth(1).click();
      await page.getByRole("button", { name: "Validate" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByPlaceholder("Search by name or email").click();
      await page.getByPlaceholder("Search by name or email").fill("admin test");
      await page.getByRole("listitem").locator("div").first().click();
      await page.getByRole("button", { name: "Add Quota" }).click();
      await page
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select Category" })
        .click();
      await page.locator(".ant-select-item-option-content").click();
      await page.getByPlaceholder("Select Year").click();
      await page.getByPlaceholder("Select Year").fill("2023");
      await page.getByPlaceholder("Select Year").press("Enter");
      await page
        .locator("label")
        .filter({ hasText: "Duplicate quota from another" })
        .locator("span")
        .first()
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select payee$/ })
        .nth(3)
        .click();
      await page.getByText("super quota").first().click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Quota Category$/ })
        .nth(3)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^PrimaryPrimary Quota$/ })
        .nth(2)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Fiscal year$/ })
        .nth(3)
        .click();
      await page.getByRole("listitem").getByText("2023").click();
      await page.getByRole("switch").click();
      await page
        .locator(".flex > .relative > .ant-select > .ant-select-selector")
        .click();
      await page.getByText("Payee test").nth(1).click();
      await page.getByRole("button", { name: "Validate" }).click();
      await page.getByRole("button", { name: "Yes, update" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByPlaceholder("Search by name or email").fill("payee test");
      await page.getByText("Payee test").first().click();
      await page.getByRole("button", { name: "Add Quota" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Category$/ })
        .nth(3)
        .click();
      await page.getByRole("button", { name: "Add quota category" }).click();
      await page.getByPlaceholder("Enter category name").click();
      await page.getByPlaceholder("Enter category name").fill("club");
      await page
        .getByLabel("Add quota category")
        .getByRole("button", { name: "Save" })
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Category$/ })
        .nth(3)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^club$/ })
        .nth(1)
        .click();
      await page.getByPlaceholder("Select Year").click();
      await page.getByPlaceholder("Select Year").fill("2024");
      await page.getByText("2024").click();
      await page.getByLabel("Enter new quota values").check();
      await page
        .locator("div")
        .filter({ hasText: /^Select Quota$/ })
        .nth(2)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Monthly$/ })
        .nth(2)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Schedule frequency$/ })
        .nth(2)
        .click();
      await page.locator(".ant-select-item-option-content").last().click();
      await page.locator(".ant-input-number-input").first().click();
      await page.locator(".ant-input-number-input").first().fill("200.00");
      await page.getByRole("switch").click();
      await page
        .locator(".mt-8 > div > .relative > .ant-select > .ant-select-selector")
        .click();
      // await page.waitForTimeout(1000);
      await page.getByRole("listitem").getByText("super quota").click();
      await page.getByRole("button", { name: "Validate" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await page
        .locator("div")
        .filter({ hasText: "Added/Modified Quota" })
        .nth(3)
        .click();
      await page.getByRole("button", { name: "Add Quota" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Category$/ })
        .nth(3)
        .click();
      await page
        .locator("div:nth-child(2) > .ant-select-item-option-content")
        .click();
      await page.getByPlaceholder("Select Year").click();
      await page.getByPlaceholder("Select Year").fill("2023");
      await page.getByText("2023").last().click();
      await page.getByLabel("Enter new quota values").check();
      await page
        .locator("div")
        .filter({ hasText: /^Select Quota$/ })
        .nth(2)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Monthly$/ })
        .nth(2)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Schedule frequency$/ })
        .nth(2)
        .click();
      await page.locator(".ant-select-item-option-content").last().click();
      await page.locator(".ant-input-number-input").first().fill("100.00");
      await page.getByRole("switch").click();
      await page
        .locator(".flex > .relative > .ant-select > .ant-select-selector")
        .last()
        .click();
      await page.getByRole("listitem").getByText("super quota").click();
      await page.getByRole("button", { name: "Validate" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByText("Added/Modified Quota").first().click();
    });

    test("check reporting team and user group hierarchy", async ({
      payeePage,
    }) => {
      const page = payeePage.page;
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page.getByRole("tab", { name: "Reporting Team" }).click();
      await page.getByLabel("plus-square").locator("svg").click();
      await expect(page.getByText("super quota").first()).toBeVisible();
      await expect(page.getByText("Payee test").first()).toBeVisible();
      await page.getByRole("tab", { name: "User Groups" }).click();
      await page.getByLabel("plus-square").locator("svg").click();
      await expect(page.getByText("annual quota").first()).toBeVisible();
      await expect(page.getByTitle("super quota").last()).toBeVisible();
    });

    test("Edit quota for frozen period", async ({ adminPage }, testInfo) => {
      testInfo.setTimeout(testInfo.timeout + 100000);
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);
      const commSync = new CommissionSync(page);

      await csPrev.navigate("/quotas");

      await page.getByPlaceholder("Search by name or email").click();
      await page
        .getByPlaceholder("Search by name or email")
        .fill("annual quota");
      await page.getByText("annual quota").first().click();
      await page.getByRole("button", { name: "Settings" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^clubPrimary Quota$/ })
        .getByRole("button")
        .nth(1)
        .click();
      await page.getByRole("menuitem", { name: "Remove Payee" }).click();
      await page.getByPlaceholder("Select Year").click();
      await page.getByPlaceholder("Select Year").fill("2024");
      await page.getByPlaceholder("Select Year").press("Enter");
      await page
        .getByLabel("Remove Payee")
        .getByTestId("ever-select")
        .locator("div")
        .nth(1)
        .click();
      await page.getByPlaceholder("Search", { exact: true }).click();
      await page
        .getByPlaceholder("Search", { exact: true })
        .fill("annual quota");
      await page.getByLabel("AQannual quota").check();
      await page.getByRole("button", { name: "Validate & Save" }).click();
      await expect(
        page.getByText(
          "The payee is already associated with a plan in which the Quota category has been utilized."
        )
      ).toBeVisible();
      await page
        .locator("div[role='listitem']>div>span>button>div>svg")
        .click();
      await page.locator(".ant-modal-close").click();
      const key = "commission-view-period";
      const value = "31-December-2024";
      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await csPrev.navigate("/commissions");
      await page.locator('input[name="row-0"]').check();
      await page
        .locator("div")
        .filter({ hasText: /^Lock$/ })
        .click();
      await page.getByRole("button", { name: "Lock" }).click();
      await page.getByText("Lock status updated").click();
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page.getByPlaceholder("Search by name or email").click();
      await page
        .getByPlaceholder("Search by name or email")
        .fill("annual quota");
      await page.getByRole("listitem").locator("div").click();
      await page.locator(".ant-space-item > .rounded-lg > .w-4").click();
      await page
        .locator(
          "div:nth-child(3) > div > .ant-space > div:nth-child(2) > .relative > .ant-select > .ant-select-selector"
        )
        .first()
        .click();
      await page
        .locator(
          "div:nth-child(2) > .ant-space > div:nth-child(2) > .relative > .ant-select > .ant-select-selector"
        )
        .click();
      await page.getByLabel("Close", { exact: true }).click();
      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await csPrev.navigate("/commissions");
      await page
        .getByRole("row", { name: "Name" })
        .getByRole("columnheader")
        .first()
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Unlock$/ })
        .click();
      await page.getByRole("button", { name: "Unlock" }).click();
      await page.getByText("Lock status updated").click();
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page.getByPlaceholder("Search by name or email").click();
      await page.getByPlaceholder("Search by name or email").fill("annual");
      await page.getByRole("listitem").locator("div").click();
      await page.waitForTimeout(1000);
      await page.locator(".rounded-lg > .w-4").last().click();
      await page
        .getByRole("row", { name: "Quota Increase Value Decrease Value" })
        .getByLabel("Increase Value")
        .click({
          clickCount: 3,
        });
      await page.getByRole("button", { name: "Save" }).click();
      await csPrev.navigate("/settings/commissions-and-data-sync");
      await commSync.selectCriteria("selected-payees");
      await commSync.selectDropdown(["annual quota"]);
      await csPrev.selectDate("01 Dec 2024");
      await csPrev.runCommissions();
      await csPrev.clickSkipandRun();
      await csPrev.waitForCalculationMessage();
      await csPrev.waitForCommissionsSuccess();

      await csPrev.navigate("/plans");
      await page.locator(".px-2 > .bg-ever-base").last().click();
      await page.locator(".relative > .p-4").click();
      await page.getByRole("button", { name: "Configuration" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await page.evaluate(
        ({ key, value }) => {
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await csPrev.navigate("/commissions");
      await page.getByRole("link", { name: "annual quota" }).click();
      await page.getByText("quota_plan").click();
      await page.getByRole("button", { name: "quota" }).click();
      await page
        .getByRole("row", { name: "₹10,005.00" })
        .getByTestId("<EMAIL>-trace-icon")
        .click();
      await expect(page.getByText("1111.11%").first()).toBeVisible();
    });
  }
);

test1.describe(
  "Quotas own data",
  { tag: ["@regression", "@quota", "@primelogic-4"] },
  () => {
    test1("check own data permission hierarchy", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await expect(
        page.getByText("It's time for a little motivation")
      ).toBeVisible();
    });
  }
);

test2.describe(
  "Quota functions validation",
  { tag: ["@regression", "@quota", "@primelogic-4"] },
  () => {
    test2("Hidden quotavalidation", async ({ adminPage, payeePage }) => {
      const page = adminPage.page;
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page
        .getByPlaceholder("Search by name or email")
        .fill("hidden quota");
      await page.getByText("hidden quota").click();
      await expect2(page.getByText("hidden category")).toBeVisible();
      const page1 = payeePage.page;
      await page1.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await expect2(page1.getByText("hidden category")).toHaveCount(0);
    });
    test2("End quota validation", async ({ adminPage }) => {
      const page = adminPage.page;
      const key = "commission-view-period";
      const value = "30-September-2024";
      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await page.goto("/commissions", {
        waitUntil: "networkidle",
      });
      await page.getByPlaceholder("Search by name or email").click();
      await page
        .getByPlaceholder("Search by name or email")
        .fill("end manager");
      await page.waitForTimeout(1000);
      await page.locator('input[name="row-0"]').check();
      await page
        .locator("div")
        .filter({ hasText: /^Lock$/ })
        .click();
      await page.getByText("Selected payee(s) are already").click();
      await page.getByRole("button", { name: "OK" }).click();
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page.getByRole("button", { name: "Settings" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Primary Quota$/ })
        .last()
        .getByRole("button")
        .click();
      await page.getByRole("menuitem", { name: "Remove Payee" }).click();
      await page.getByPlaceholder("Select Year").click();
      await page.getByPlaceholder("Select Year").fill("2024");
      await page.getByPlaceholder("Select Year").press("Enter");
      await page
        .getByLabel("Remove Payee")
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select Payees" })
        .click();
      await page.getByPlaceholder("Search", { exact: true }).click();
      await page
        .getByPlaceholder("Search", { exact: true })
        .fill("end manager");
      await page.getByLabel("EMend manager").check();
      await page.getByRole("button", { name: "Validate & Save" }).click();
      await expect2(
        page.getByText(
          "The payee is already associated with a plan in which the Quota category has been utilized."
        )
      ).toBeVisible();
      await page.getByText("end manager").last().click();
      await page.locator("span.ant-modal-close-x").click();
      await page.getByLabel("Close").nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^end manager$/ })
        .first()
        .click();
      await page.locator(".rounded-lg > .w-4").first().click();
      await page.getByRole("switch").click();
      await page.getByText("Start this quota").click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Quota$/ })
        .nth(2)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Half-yearly$/ })
        .nth(1)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Schedule frequency$/ })
        .nth(2)
        .click();
      await page.getByText("Half-yearly").last().click();
      await page.getByRole("button", { name: "Save" }).isDisabled();
      await page.getByRole("button", { name: "Cancel" }).click();
      await page.locator(".rounded-lg > .w-4").first().click();
      await page.getByRole("switch").click();
      await page.getByText("Start this quota").click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Quota$/ })
        .nth(2)
        .click();
      await page.getByText("Quarterly", { exact: true }).last().click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Schedule frequency$/ })
        .nth(2)
        .click();
      await page.getByText("Quarterly", { exact: true }).last().click();
      await page.getByPlaceholder("Select start date").click();
      await page.getByPlaceholder("Select start date").fill("Oct 2024");
      await page.getByPlaceholder("Select start date").press("Enter");
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByText("Added/Modified Quota").first().click();
      await expect2(page.getByText("Active")).toHaveCount(2);
      await expect2(page.getByText("Not Available")).toHaveCount(0);
    });
    test2(
      "delete quota category and remove payee validation",
      async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/quotas", {
          waitUntil: "networkidle",
        });
        await page.getByText("monthly quota").click();
        await page.getByRole("button", { name: "Settings" }).click();
        await page.locator(".p-6 > div > div:nth-child(3)").click();
        await page
          .locator("div")
          .filter({ hasText: /^Primary Quota$/ })
          .getByRole("button")
          .last()
          .click();
        await page.getByText("Remove Payee", { exact: true }).click();
        await page.getByPlaceholder("Select Year").click();
        await page.getByPlaceholder("Select Year").fill("2024");
        await page.getByPlaceholder("Select Year").press("Enter");
        await page
          .getByLabel("Remove Payee")
          .getByTestId("ever-select")
          .locator("div")
          .nth(1)
          .click();
        await page.getByPlaceholder("Search", { exact: true }).click();
        await page
          .getByPlaceholder("Search", { exact: true })
          .fill("monthly quota");
        await page.waitForTimeout(2000);
        await page.getByLabel("MQmonthly quota").check();
        await page.getByRole("button", { name: "Validate & Save" }).click();
        await page
          .getByText(
            "The payee is already associated with a plan in which the Quota category has been utilized."
          )
          .click();
        await page.getByLabel("Remove Payee").click();
        await page.locator("span.ant-modal-close-x").click();
        await page
          .locator("div")
          .filter({ hasText: /^hidden categorynew_draftPrimary Quota$/ })
          .getByRole("button")
          .nth(2)
          .click();
        await page.getByLabel("Close").nth(1).click();
        await page.getByText("Super Admin test").click();
        await page.getByRole("button", { name: "Settings" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^hidden categorynew_draftPrimary Quota$/ })
          .getByRole("button")
          .nth(1)
          .click();
        await page.getByRole("menuitem", { name: "Delete" }).click();
        await page.getByRole("button", { name: "Yes, remove" }).click();
        await page
          .getByText(
            "The commission plan draft_plan utilizes this Quota category. To remove the Quota category, you should delete the plan"
          )
          .click();
        await page.getByRole("button", { name: "Cancel" }).click();
        await page.getByLabel("Close").nth(1).click();
        await page.getByRole("button", { name: "Add Quota" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Select Category$/ })
          .nth(3)
          .click();
        await page.getByRole("button", { name: "Add quota category" }).click();
        await page.getByPlaceholder("Enter category name").click();
        await page
          .getByPlaceholder("Enter category name")
          .fill("custom catgory");
        await page
          .getByLabel("Add quota category")
          .getByRole("button", { name: "Save" })
          .click();
        await page.getByText("custom catgory - Quota").click();
        await page
          .locator("div")
          .filter({ hasText: /^Select Category$/ })
          .nth(3)
          .click();
        await page.getByText("custom catgory", { exact: true }).click();
        await page.getByPlaceholder("Select Year").click();
        await page.getByPlaceholder("Select Year").fill("2025");
        await page.getByText("2025").click();
        await page.getByPlaceholder("Select Year").press("Enter");
        await page
          .locator("div")
          .filter({ hasText: /^Select payee$/ })
          .nth(3)
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Select payee$/ })
          .nth(3)
          .click();
        await page
          .getByText("Super Admin test", { exact: true })
          .nth(1)
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Select Quota Category$/ })
          .nth(3)
          .click();
        await page.getByRole("listitem").getByText("new_draft").click();
        await page
          .locator("div")
          .filter({ hasText: /^Select Fiscal year$/ })
          .nth(3)
          .click();
        await page.getByRole("listitem").getByText("2024").click();
        await page.waitForTimeout(2000);
        await page.getByRole("button", { name: "Save" }).click();
        // await page.getByRole("button", { name: "Save" }).click();
        await page
          .getByText("Added/Modified Quota")
          .first()
          .waitFor({ state: "visible" });
      }
    );
    test2("Quota schedule validation", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page.getByText("monthly quota").click();
      await page.getByRole("button", { name: "Add Quota" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Category$/ })
        .nth(3)
        .click();
      await page.getByText("Primary Quota").nth(1).click();
      await page.getByPlaceholder("Select Year").click();
      await page.getByPlaceholder("Select Year").fill("2025");
      await page.getByPlaceholder("Select Year").press("Enter");
      await page.getByLabel("Enter new quota values").check();
      await page
        .locator("div")
        .filter({ hasText: /^Select Quota$/ })
        .nth(2)
        .click();
      await page
        .locator("span")
        .filter({ hasText: /^Monthly$/ })
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Schedule frequency$/ })
        .nth(2)
        .click();
      await expect2(page.getByText("Monthly").last()).toBeVisible();
      await page.getByTitle("Monthly", { exact: true }).first().click();
      await page
        .locator("div")
        .filter({ hasText: /^Quarterly$/ })
        .nth(2)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Schedule frequency$/ })
        .nth(2)
        .click();
      await expect2(page.getByText("Monthly").last()).toBeVisible();
      await expect2(page.getByText("Quarterly").last()).toBeVisible();
      await page.getByText("Quarterly").first().click();
      await page
        .locator("div")
        .filter({ hasText: /^Half-yearly$/ })
        .nth(1)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Schedule frequency$/ })
        .nth(2)
        .click();
      await expect2(page.getByText("Monthly").last()).toBeVisible();
      await expect2(page.getByText("Quarterly").last()).toBeVisible();
      await expect2(page.getByText("Half-yearly").last()).toBeVisible();
      await page
        .locator("div")
        .filter({ hasText: /^Half-yearly$/ })
        .first()
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Annual$/ })
        .nth(2)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Schedule frequency$/ })
        .nth(2)
        .click();
      await expect2(page.getByText("Monthly").last()).toBeVisible();
      await expect2(page.getByText("Quarterly").last()).toBeVisible();
      await expect2(page.getByText("Half-yearly").last()).toBeVisible();
      await expect2(page.getByText("Annual").last()).toBeVisible();
    });
    test2("Bulk Add quota validation", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page.getByText("category user1").click();
      await page.getByRole("button", { name: "Bulk Quota Upload" }).click();
      await page.getByRole("menuitem", { name: "Bulk Quota Upload" }).click();
      await page
        .locator("span>input[type='file']")
        .setInputFiles("./upload-files/qaDebtBulkUpload.csv");
      await page
        .getByText("qaDebtBulkUpload.csv file read successfully.")
        .waitFor({ state: "visible", timeout: 5000 });
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "Validate" }).click();
      await page
        .getByLabel(
          "Proceed to import 7 records and email me the import status"
        )
        .check();
      await page.getByRole("button", { name: "Import" }).click();
      await page
        .getByLabel(
          "Enter the email address of the user who needs to be notified when the job is complete.*"
        )
        .fill("<EMAIL>");
      await page.getByRole("button", { name: "Submit" }).click();
      await page.getByText("Upload in progress...").click();
      await page.getByRole("button", { name: "Done" }).click();
      await page.getByText("category user1").click();
      await page.getByRole("button", { name: "Settings" }).click();
      await page
        .locator("div")
        .filter({
          hasText: /^Category1$/,
        })
        .getByRole("button")
        .first()
        .click();
      await page.getByRole("menuitem", { name: "Edit" }).click();
      await page.getByPlaceholder("Enter category name").click();
      await page.getByPlaceholder("Enter category name").fill("Category2");
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByLabel("Close", { exact: true }).last().click();
      await page.getByRole("button", { name: "Okay" }).click();
      await page.reload();
      await page.getByText("category user2").click();
      await expect2(page.getByText("Category2")).toBeVisible();
      await page.getByText("category user1").click();
      await expect2(page.getByText("Category2")).toBeVisible();
    });
    test2("Bulk Add quota error validation", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page.getByText("category user1").click();
      await page.getByRole("button", { name: "Bulk Quota Upload" }).click();
      await page.getByRole("menuitem", { name: "Bulk Quota Upload" }).click();
      await page
        .locator("span>input[type='file']")
        .setInputFiles("./upload-files/qaDebtBulkUploadError.csv");
      await page
        .getByText("qaDebtBulkUploadError.csv file read successfully.")
        .waitFor({ state: "visible", timeout: 5000 });
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "Validate" }).click();
      await page.getByRole("button", { name: "Revalidate" }).click();
      await expect2(
        page
          .getByText("Email not found. Create the user and import records.")
          .first()
      ).toBeVisible();
      await expect2(
        page.getByText(
          "Quota cannot be empty or contain special characters or letters."
        )
      ).toBeVisible();
      await expect2(
        page.getByText(
          "Commission Schedule cannot be Quarterly when quota period is Monthly."
        )
      ).toBeVisible();
      await expect2(page.getByText("Duplicate/Overwritten")).toBeVisible();
      await expect2(
        page.getByText(
          "Quotas cannot be created for a time-period that is already locked."
        )
      ).toBeVisible();
      await expect2(
        page
          .getByText(
            "Quotas across schedule should be equal or in increasing order."
          )
          .first()
      ).toBeVisible();
    });
    test2("Bulk Add frozen period validation", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/quotas", {
        waitUntil: "networkidle",
      });
      await page.getByText("category user1").click();
      await page.getByRole("button", { name: "Bulk Quota Upload" }).click();
      await page
        .getByRole("menuitem", { name: "Effective Date Quota" })
        .click();
      await page
        .locator("span>input[type='file']")
        .setInputFiles("./upload-files/qaDebtFrozenPeriodError.csv");
      await page
        .getByText("qaDebtFrozenPeriodError.csv file read successfully.")
        .waitFor({ state: "visible", timeout: 5000 });
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "Validate" }).click();
      await expect2(
        page.getByText(
          "Quotas cannot be created for a time-period that is already locked.Mid Quota period changes are not allowed"
        )
      ).toBeVisible();
    });
    test2(
      "Validate all freq effective end quota for quota,quota-attainemnt and quota erosion",
      async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/plans", {
          waitUntil: "networkidle",
        });
        await page.getByPlaceholder("Search by plan name").click();
        await page
          .getByPlaceholder("Search by plan name")
          .fill("effective_monthly");
        await page.getByText("effective_monthly").click();
        await page.getByText("simple").first().click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").click();
        await page.getByPlaceholder("Start date").fill("Jun 01, 2024");
        await page.getByPlaceholder("Start date").press("Enter");
        await page.getByPlaceholder("End date").click();
        await page.getByPlaceholder("End date").fill("Jun 30, 2024");
        await page.getByPlaceholder("End date").press("Enter");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$123.00").first().click();
        await page
          .locator("span")
          .filter({ hasText: "simple (Copy)" })
          .first()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(1).click();
        await page.getByPlaceholder("Start date").nth(1).fill("Jun 01, 2024");
        await page.getByPlaceholder("End date").nth(1).click();
        await page.getByPlaceholder("End date").nth(1).fill("Jun 30, 2024");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$5,456").first().click();
        await page
          .locator("span")
          .filter({ hasText: "simple (Copy) (Copy)" })
          .first()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(2).click();
        await page.getByPlaceholder("Start date").nth(2).fill("Jun 01, 2024");
        await page.getByPlaceholder("End date").nth(2).click();
        await page.getByPlaceholder("End date").nth(2).fill("Jun 30, 2024");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$44.36").first().click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();
        await page.getByLabel("close-circle").click();
        await page.getByPlaceholder("Search by plan name").click();
        await page
          .getByPlaceholder("Search by plan name")
          .fill("Effective_quar_plan");
        await page.getByText("Effective_quar_plan").click();
        await page.getByText("simple").first().click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").click();
        await page.getByPlaceholder("Start date").fill("Oct 01, 2024");
        await page.getByPlaceholder("Start date").press("Enter");
        await page.getByPlaceholder("End date").click();
        await page.getByPlaceholder("End date").fill("Dec 30, 2024");
        await page.getByPlaceholder("End date").press("Enter");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$30.00").first().click();
        await page
          .locator("span")
          .filter({ hasText: "simple (Copy)" })
          .first()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(1).click();
        await page.getByPlaceholder("Start date").nth(1).fill("Oct 01, 2024");
        await page.getByPlaceholder("End date").nth(1).click();
        await page.getByPlaceholder("End date").nth(1).fill("Dec 30, 2024");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$100.00").first().click();
        await page
          .locator("span")
          .filter({ hasText: "simple (Copy) (Copy)" })
          .first()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(2).click();
        await page.getByPlaceholder("Start date").nth(2).fill("Oct 01, 2024");
        await page.getByPlaceholder("End date").nth(2).click();
        await page.getByPlaceholder("End date").nth(2).fill("Oct 30, 2024");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$3,000.00").first().click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();
        await page.getByLabel("close-circle").click();
        await page.getByPlaceholder("Search by plan name").click();
        await page
          .getByPlaceholder("Search by plan name")
          .fill("all_effective_plan");
        await page.getByText("all_effective_plan").click();
        await page.getByText("cons").first().click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").click();
        await page.getByPlaceholder("Start date").fill("Jun 01, 2024");
        await page.getByPlaceholder("Start date").press("Enter");
        await page.getByPlaceholder("End date").click();
        await page.getByPlaceholder("End date").fill("Jun 30, 2024");
        await page.getByPlaceholder("End date").press("Enter");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$667.00").first().click();
        await page.locator("span").filter({ hasText: "tier" }).first().click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(1).click();
        await page.getByPlaceholder("Start date").nth(1).fill("Jun 01, 2024");
        await page.getByPlaceholder("End date").nth(1).click();
        await page.getByPlaceholder("End date").nth(1).fill("Jun 30, 2024");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$1,167.00").first().click();
        await page
          .locator("span")
          .filter({ hasText: "simple" })
          .first()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(2).click();
        await page.getByPlaceholder("Start date").nth(2).fill("Jun 01, 2024");
        await page.getByPlaceholder("End date").nth(2).click();
        await page.getByPlaceholder("End date").nth(2).fill("Jun 30, 2024");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$2,343.00").first().click();
        await page
          .locator("span")
          .filter({ hasText: "quota_desc" })
          .last()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(3).click();
        await page.getByPlaceholder("Start date").nth(3).fill("Oct 01, 2024");
        await page.getByPlaceholder("End date").nth(3).click();
        await page.getByPlaceholder("End date").nth(3).fill("Oct 30, 2024");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$1,834.00").first().click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();
      }
    );
    test2(
      "Validate quota erosion, quota, quota attainment for all frequecny for lookback period",
      async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/plans", {
          waitUntil: "networkidle",
        });
        await page.getByPlaceholder("Search by plan name").click();
        await page.getByPlaceholder("Search by plan name").fill("monthly_plan");
        await page.getByText("monthly_plan").click();
        await page.getByText("simple").first().click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").click();
        await page.getByPlaceholder("Start date").fill("Jun 01, 2024");
        await page.getByPlaceholder("Start date").press("Enter");
        await page.getByPlaceholder("End date").click();
        await page.getByPlaceholder("End date").fill("Jun 30, 2024");
        await page.getByPlaceholder("End date").press("Enter");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$667.00").first().click();
        await page
          .locator("span")
          .filter({ hasText: "simple (Copy)" })
          .first()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(1).click();
        await page.getByPlaceholder("Start date").nth(1).fill("Jun 01, 2024");
        await page.getByPlaceholder("End date").nth(1).click();
        await page.getByPlaceholder("End date").nth(1).fill("Jun 30, 2024");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$2,000.00").first().click();
        await page
          .locator("span")
          .filter({ hasText: "simple (Copy) (Copy)" })
          .first()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(2).click();
        await page.getByPlaceholder("Start date").nth(2).fill("Jun 01, 2024");
        await page.getByPlaceholder("End date").nth(2).click();
        await page.getByPlaceholder("End date").nth(2).fill("Jun 30, 2024");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$3.00").first().click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();
        await page.getByLabel("close-circle").click();
        await page.getByPlaceholder("Search by plan name").click();
        await page.getByPlaceholder("Search by plan name").fill("half_plan");
        await page.getByText("Half_plan").click();
        await page
          .locator("span")
          .filter({ hasText: /^quota$/ })
          .first()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").click();
        await page.getByPlaceholder("Start date").fill("Jan 01, 2024");
        await page.getByPlaceholder("End date").click();
        await page.getByPlaceholder("End date").fill("Jun 30, 2024");
        await page.getByPlaceholder("End date").press("Enter");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$7,657.00").first().click();
        await page
          .locator("span")
          .filter({ hasText: "quota (Copy)" })
          .first()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(1).click();
        await page.getByPlaceholder("Start date").nth(1).fill("Jan 01, 2024");
        await page.getByPlaceholder("End date").nth(1).click();
        await page.getByPlaceholder("End date").nth(1).fill("Jun 30, 2024");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$1,900.00").first().click();
        await page
          .locator("span")
          .filter({ hasText: "quota (Copy) (Copy)" })
          .first()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(2).click();
        await page.getByPlaceholder("Start date").nth(2).fill("Jan 01, 2024");
        await page.getByPlaceholder("End date").nth(2).click();
        await page.getByPlaceholder("End date").nth(2).fill("Jun 30, 2024");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$0.25").first().click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();
        await page.getByLabel("close-circle").click();
        await page.getByPlaceholder("Search by plan name").click();
        await page
          .getByPlaceholder("Search by plan name")
          .fill("quaterly_plan");
        await page.getByText("Quaterly_plan").click();
        await page
          .locator("div")
          .filter({ hasText: /^quota$/ })
          .nth(3)
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").click();
        await page.getByPlaceholder("Start date").fill("Jan 01, 2024");
        await page.getByPlaceholder("End date").click();
        await page.getByPlaceholder("End date").fill("Jun 30, 2024");
        await page.getByPlaceholder("End date").press("Enter");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$12,334.00").first().click();
        await page.getByText("quota (Copy)").first().click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(1).click();
        await page.getByPlaceholder("Start date").nth(1).fill("Jan 01, 2024");
        await page.getByPlaceholder("End date").nth(1).click();
        await page.getByPlaceholder("End date").nth(1).fill("Jun 30, 2024");
        await page.getByPlaceholder("End date").nth(1).press("Enter");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$0.00").first().click();
        await page.getByText("quota (Copy) (Copy)").first().click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(2).click();
        await page.getByPlaceholder("Start date").nth(2).fill("Jan 01, 2024");
        await page.getByPlaceholder("End date").nth(2).click();
        await page.getByPlaceholder("End date").nth(2).fill("Jun 30, 2024");
        await page.getByPlaceholder("End date").nth(2).press("Enter");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();
        await page.getByLabel("close-circle").click();
        await page.getByPlaceholder("Search by plan name").click();
        await page.getByPlaceholder("Search by plan name").fill("annual_plan");
        await page.waitForTimeout(4000);
        await page.getByText("Annual_plan").click();
        await page
          .locator("span")
          .filter({ hasText: /^simple$/ })
          .first()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").click();
        await page.getByPlaceholder("Start date").fill("Jan 01, 2024");
        await page.getByPlaceholder("End date").click();
        await page.getByPlaceholder("End date").fill("Dec 31, 2024");
        await page.getByPlaceholder("End date").press("Enter");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$900.00").first().click();
        await page
          .locator("span")
          .filter({ hasText: "simple (Copy)" })
          .first()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(1).click();
        await page.getByPlaceholder("Start date").nth(1).fill("Jan 01, 2024");
        await page.getByPlaceholder("End date").nth(1).click();
        await page.getByPlaceholder("End date").nth(1).fill("Dec 31, 2024");
        await page.getByPlaceholder("End date").nth(1).press("Enter");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$7,000.00").first().click();
        await page
          .locator("span")
          .filter({ hasText: "simple (Copy) (Copy)" })
          .first()
          .click();
        await page.getByRole("button", { name: "Simulate" }).click();
        await page.getByPlaceholder("Start date").nth(2).click();
        await page.getByPlaceholder("Start date").nth(2).fill("Jan 01, 2024");
        await page.getByPlaceholder("End date").nth(2).click();
        await page.getByPlaceholder("End date").nth(2).fill("Dec 31, 2024");
        await page.getByPlaceholder("End date").nth(2).press("Enter");
        await page.getByRole("button", { name: "Run" }).click();
        await page.getByText("$7.78").first().click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();
        await page.goto("/databook", {
          waitUntil: "networkidle",
        });
        await page.getByRole("link", { name: "Book", exact: true }).click();
        await page.getByText("inter_quota").click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page
          .locator("div:nth-child(3) > .ant-select-item-option-content")
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Contains$/ })
          .nth(1)
          .click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("half");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await page.getByRole("tab", { name: "Customize Columns" }).click();
        await page
          .getByLabel("Employee ID Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payee Name Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payee Email ID Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Designation Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Current Manager Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payee or Manager Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Individual or Team Quota")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Joining Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Exit Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Employment Country Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payout Currency Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payout Frequency Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Annual Variable Pay Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Variable Pay As Per Period")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Period Start Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Period End Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Is Hidden Quota Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Category Name Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Period Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Schedule Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Period Timeframe Column")
          .locator("div")
          .filter({ hasText: "Period Timeframe" })
          .click();
        await page
          .getByLabel("Quota Period Start Date Column")
          .locator("div")
          .filter({ hasText: "Quota Period Start Date" })
          .click();
        await page
          .getByLabel("Quota Schedule Start Date")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page.getByRole("gridcell", { name: "24.81" }).click();
        await page.getByRole("gridcell", { name: "10000" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^quota$/ })
          .first()
          .click();
        await page.getByRole("tab", { name: "Customize Columns" }).click();
        await page
          .getByLabel("Employee Email ID Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Fiscal Year Column")
          .locator("div")
          .filter({ hasText: "Quota Fiscal Year" })
          .click();
        await page
          .getByLabel("Quota Category Name Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota UI Hidden? Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Period Time Frame Column")
          .locator("div")
          .filter({ hasText: "Quota Period Time Frame" })
          .click();
        await page
          .getByLabel("Is Team Quota? Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Period Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Schedule Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Period Start Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Schedule Start Date")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Employee Email ID$/ })
          .nth(1)
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Contains$/ })
          .nth(1)
          .click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("half");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await page.getByRole("tab", { name: "Customize Columns" }).click();
        await page
          .getByLabel("Employee Email ID Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Fiscal Year Column")
          .locator("div")
          .filter({ hasText: "Quota Fiscal Year" })
          .click();
        await page
          .getByLabel("Quota Category Name Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota UI Hidden? Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Period Time Frame Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Is Team Quota? Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Period Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Schedule Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Period Start Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Schedule Start Date")
          .locator("div")
          .filter({ hasText: "Quota Schedule Start Date" })
          .click();
        await page.getByText("878").first().click();
        // await page.getByRole("tooltip", { name: "878" }).click();
        await page.getByText("190").first().click();
        await page.getByText("7,657").first().click();
        await page.getByText("7,656").first().click();
        await page.getByText("quota_attainment").click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page
          .locator("div:nth-child(3) > .ant-select-item-option-content")
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Contains$/ })
          .nth(1)
          .click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("half");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await page.getByRole("tab", { name: "Customize Columns" }).click();
        await page
          .getByLabel("Quota Period Start Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Schedule Start Date")
          .locator("div")
          .filter({ hasText: "Quota Schedule Start Date" })
          .click();
        await page
          .getByLabel("Target Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Employee ID Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payee Name Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payee Email ID Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Designation Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Current Manager Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payee or Manager Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Individual or Team Quota")
          .locator("div")
          .filter({ hasText: "Individual or Team Quota" })
          .click();
        await page
          .getByLabel("Joining Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Exit Date Column")
          .locator("div")
          .filter({ hasText: "Exit Date" })
          .click();
        await page
          .getByLabel("Employment Country Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payout Currency Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Payout Frequency Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Annual Variable Pay Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Variable Pay As Per Period")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Period Start Date Column")
          .locator("div")
          .filter({ hasText: "Period Start Date" })
          .click();
        await page
          .getByLabel("Period End Date Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Is Hidden Quota Column")
          .locator("div")
          .filter({ hasText: "Is Hidden Quota" })
          .click();
        await page
          .getByLabel("Quota Category Name Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Period Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Quota Schedule Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page
          .getByLabel("Period Timeframe Column")
          .getByLabel("Press SPACE to toggle")
          .uncheck();
        await page.getByRole("gridcell", { name: "24.81" }).click();
        await page.getByRole("gridcell", { name: "10000" }).click();
      }
    );
  }
);
