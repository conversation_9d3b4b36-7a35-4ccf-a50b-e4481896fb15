import CommissionsSyncPrevPeriod from "../../../../test-objects/commissionSyncPrevPeriod-objects";
import QuotaAnnualED from "../../../../test-objects/quotaAnnualEd-objects";
import AdjustementsV2Page from "../../../../test-objects/adjustments-v2-objects";
import CommonUtils from "../../../../test-objects/common-utils-objects";
import QueriesLineItem from "../../../../test-objects/querieslineitems-objects";

const {
  quotaAnnualEDFixtures: { test, expect },
} = require("../../../fixtures");

test.describe(
  "Effective dated annual quota - Automation",
  { tag: ["@quota", "@regression", "@primelogic-4"] },
  () => {
    test(
      "Split Annual Quota Validation over Locked Statement",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18560, INTER-T18561" },
          {
            type: "Description",
            description:
              "Validate whether the Annual Quota can be split into different effective-dated quota values upon locked statement",
          },
          { type: "Precondition", description: "Quotas" },
          {
            type: "Expected Behaviour",
            description:
              "The Annual Quota should be split into different effective-dated quota values when the statement is locked",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        testInfo.setTimeout(testInfo.timeout + 900000);
        const assert = require("assert");
        const csPrev = new CommissionsSyncPrevPeriod(page);
        const quotaED = new QuotaAnnualED(page);
        const queriesPage = new QueriesLineItem(page);

        await quotaED.navigate("/quotas");
        await quotaED.setSplitAnnualQuota(
          "bosspayee1",
          "Payee 1",
          "2024-12",
          "500,000.00",
          "100,0000"
        );
        await quotaED.saveQuota();

        await quotaED.navigate("/settings/commissions-and-data-sync");
        await csPrev.selectPayees("Payee 1");
        await csPrev.selectDate("31 Dec 2024");
        await csPrev.PrevPeriodCheck();
        await csPrev.runCommissions();
        await expect(page.getByText("Notify when complete")).toHaveCount(1);
        await csPrev.clickSkipandRun();
        await csPrev.waitForCalculationMessage();
        await csPrev.waitForCommissionsSuccess();

        await quotaED.navigate("/commissions");
        await queriesPage.selectDate("31 Dec 2024");
        await csPrev.clickUser("Payee 1");

        const quotaSplitBefore = await quotaED.getQuotaErrosion();
        await quotaED.unlockStatements();

        const quotaSplitDec = await quotaED.getQuotaErrosion();

        await quotaED.switchperiod("December", "November");
        await page.waitForTimeout(3000);

        await quotaED.unlockStatements();

        const quotaSplitNov = await quotaED.getQuotaErrosion();

        try {
          assert.strictEqual(
            quotaSplitBefore,
            "500,000.00",
            "The quotas for the locked period are not being reflected. Failed."
          );
          assert.strictEqual(
            quotaSplitNov,
            "500,000.00",
            "The quotas for the splitted period are not being reflected. Failed."
          );
          assert.strictEqual(
            quotaSplitDec,
            "1,000,000.00",
            "The quotas for the splitted period are not being reflected. Failed."
          );
          console.log(
            "Annual quotas are split successfully, and the values are updated on the statements only when the statement is unlocked, Passed"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Commission Plan Criteria level Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18570" },
          {
            type: "Description",
            description:
              "Validate whether the split quota values are reflected at the Commission Plan Criteria level",
          },
          { type: "Precondition", description: "Quotas" },
          {
            type: "Expected Behaviour",
            description:
              "The updated split quota values should be reflected properly in the Commission Plan criteria level",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const quotaED = new QuotaAnnualED(page);
        const csPrev = new CommissionsSyncPrevPeriod(page);

        await quotaED.navigate("/plans");
        await csPrev.clickPlan("Commission_Monthly_Quota_PLAN");
        await quotaED.clickCriteria("QUOTA_mon");

        await quotaED.clickSimulateBtn();
        await quotaED.simulateCommissions("Nov 01, 2024", "Dec 31, 2024");
        await page.waitForTimeout(5000);

        const tiers = await quotaED.getAllTiers();

        const tierCounts = tiers.reduce((acc, tier) => {
          acc[tier] = (acc[tier] || 0) + 1;
          return acc;
        }, {});

        console.log(tierCounts);

        try {
          assert.strictEqual(
            tierCounts["Tier 0"],
            5,
            "Mismatch in Tier 0 count"
          );
          assert.strictEqual(
            tierCounts["Tier 1"],
            3,
            "Mismatch in Tier 1 count"
          );
          console.log(
            "Quota values are correctly eroded, and the values are accurately simulated in the commission plan simulation., Passed"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "View Profile section of Simulate Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18566" },
          {
            type: "Description",
            description:
              "Validate whether the split quota values are reflected in the 'View Profile' section of Simulate",
          },
          { type: "Precondition", description: "Quotas" },
          {
            type: "Expected Behaviour",
            description:
              "The updated split quota values should be displayed in the 'Quotas' section of the View Profile.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const quotaED = new QuotaAnnualED(page);
        const csPrev = new CommissionsSyncPrevPeriod(page);

        await quotaED.navigate("/plans");
        await csPrev.clickPlan("Commission_Monthly_Quota_PLAN");
        await quotaED.clickCriteria("QUOTA_mon");

        await quotaED.clickSimulateBtn();
        await quotaED.clickViewProfile();
        await quotaED.switchTab("Quota");

        await quotaED.openQuotaSection(1);

        const janToNovQuota = await quotaED.getQuotaValueViewProfile();

        await quotaED.closeQuotaSection(1);
        await page.waitForTimeout(3000);

        await quotaED.openQuotaSection(2);

        const decQuota = await quotaED.getQuotaValueViewProfile();

        await quotaED.closeQuotaSection(2);

        try {
          assert.strictEqual(
            janToNovQuota,
            "500,000.00",
            "Quota for Period Jan - Nov aren't populated properly in the View Profile section"
          );
          assert.strictEqual(
            decQuota,
            "1,000,000.00",
            "Quota for Period Dec isn't populated properly in the View Profile section"
          );
          console.log(
            "Quota values are properly populated in the View profile section, Passed"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Uneditable Quotas when Annual Quota Effective dated is added",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18568" },
          {
            type: "Description",
            description:
              "Validate that when Annual Quota effective-dated values are selected, the schedule frequency is disabled and not editable",
          },
          { type: "Precondition", description: "Quotas" },
          {
            type: "Expected Behaviour",
            description:
              "The Schduele frequency should be uneditable when Annual Quota Effective dated values are selected",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const quotaED = new QuotaAnnualED(page);

        await quotaED.navigate("/quotas");
        await quotaED.selectPayeeQuota("bosspayee1", "Payee 1");

        await quotaED.clickEditQuota();

        const dropdown = page.locator("div.ant-select-disabled");
        await expect(dropdown).toHaveClass(/ant-select-disabled/);

        try {
          await dropdown.click();
          throw new Error("Dropdown is clickable, but it should not be!");
        } catch (error) {
          console.log("Dropdown is unclickable as expected.");
        }
      }
    );

    test(
      "Changing of Schedule Frequency",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18567" },
          {
            type: "Description",
            description:
              "Validate whether changing the schedule frequency and splitting the quota values reflects the updated values in the statements",
          },
          { type: "Precondition", description: "Quotas" },
          {
            type: "Expected Behaviour",
            description:
              "Upon changing the schedule frequency and splitting the quota values, the updated values should be displayed in the statements based on the locked/unlocked status",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        testInfo.setTimeout(testInfo.timeout + 900000);
        const assert = require("assert");
        const quotaED = new QuotaAnnualED(page);
        const csPrev = new CommissionsSyncPrevPeriod(page);
        const queriesPage = new QueriesLineItem(page);

        await quotaED.navigate("/quotas");
        await quotaED.selectPayeeQuota("bosspayeehalf", "Payee Half");

        await quotaED.clickEditQuota();
        await quotaED.changeScheduleFrequency("Half-Yearly", "Annual");
        await quotaED.setQuotaValue("50,0000");
        await quotaED.saveQuota();

        await quotaED.clickEditQuota();
        await quotaED.setSplitAnnualQuotachangedSF(
          "2024-07",
          "500,000.00",
          "100,0000"
        );
        await quotaED.saveQuota();

        await quotaED.navigate("/settings/commissions-and-data-sync");
        await csPrev.selectPayees("Payee Half");
        await csPrev.selectDate("31 Dec 2024");
        await csPrev.PrevPeriodCheck();
        await csPrev.runCommissions();
        await expect(page.getByText("Notify when complete")).toHaveCount(1);
        await csPrev.clickSkipandRun();
        await csPrev.waitForCalculationMessage();
        await csPrev.waitForCommissionsSuccess();

        await quotaED.navigate("/commissions");
        await queriesPage.selectDate("31 Dec 2024");
        await csPrev.clickUser("Payee Half");

        const quotaSplitBefore = await quotaED.getQuotaErrosion();
        await quotaED.unlockStatements();
        await page.waitForTimeout(3000);

        const quotaSplitH2 = await quotaED.getQuotaErrosion();

        try {
          assert.strictEqual(
            quotaSplitBefore,
            "500,000.00",
            "The quotas for the locked period are not being reflected. Failed."
          );
          assert.strictEqual(
            quotaSplitH2,
            "1,000,000.00",
            "The quotas for the splitted period are not being reflected. Failed."
          );
          console.log(
            "Schedule frequencies are changed successfully, and the values are updated on the statements only when the statement is unlocked, Passed"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Commission Trace Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18565" },
          {
            type: "Description",
            description:
              "Validate whether the split quota values are reflected in the Commission Trace",
          },
          { type: "Precondition", description: "Quotas" },
          {
            type: "Expected Behaviour",
            description:
              "The Quota Split should be reflected in the Commission Trace",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const quotaED = new QuotaAnnualED(page);
        const csPrev = new CommissionsSyncPrevPeriod(page);
        const queriesPage = new QueriesLineItem(page);

        await quotaED.navigate("/commissions");
        await queriesPage.selectDate("31 Dec 2024");
        await csPrev.clickUser("Payee 1");
        await csPrev.clickPlan("Commission_Monthly_Quota_PLAN");
        await csPrev.clickCriteria("QUOTA_mon");
        await quotaED.clickTracebyLineItemID("60");
        const quotaRange = await quotaED.getQuotaRange();

        try {
          assert.strictEqual(
            quotaRange,
            "Above 30000",
            "The Quota Split is not being reflected in the Commission Trace, Failed."
          );
          console.log(
            "The Quota Split is being reflected in the Commission Trace successfully, Passed"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Quota and Quota Attainment Report Object Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18562" },
          {
            type: "Description",
            description:
              "Validate whether the split quota values are reflected in the 'Quota' and 'Quota Attainment' report object datasheets",
          },
          { type: "Precondition", description: "Quotas" },
          {
            type: "Expected Behaviour",
            description:
              "The Report Objects 'Quota' and 'Quota Attainment' should be updated with the updated Quota values",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const quotaED = new QuotaAnnualED(page);
        const adjustmentV2 = new AdjustementsV2Page(page);
        const commonPage = new CommonUtils(page);
        const csPrevPage = new CommissionsSyncPrevPeriod(page);

        await quotaED.navigate("settings/commissions-and-data-sync");
        await commonPage.expandMenu("Report ETL Run ETL for report", "7");
        await commonPage.runETLReportforAllpayeesAndPeriod(
          "Quota Attainment",
          false
        );
        await csPrevPage.runCommissions();
        await csPrevPage.clickSkipandRun();
        await csPrevPage.waitForReportETLSuccess();
        await quotaED.navigate("databook/472002dd-3db4-42ff-aa6b-9ee891f1dc1c");

        await adjustmentV2.updateDatasheet();
        await adjustmentV2.generateDatasheet();
        await page.waitForTimeout(5000);

        const quotaValues = await quotaED.getQuotaReportObjectValues();
        const ChecksQuota = ["1,000,000", "500,000"];
        const allPresentQuota = ChecksQuota.every((item) =>
          quotaValues.includes(item)
        );

        await quotaED.switchDatasheet("Quota_attainment");
        await page.waitForTimeout(6000);

        await adjustmentV2.updateDatasheet();
        await adjustmentV2.generateDatasheet();
        await page.waitForTimeout(10000);

        const quotaAttainmentValues =
          await quotaED.getQuotaAttnReportObjectValues();
        const ChecksQuotaAttn = ["1,000,000", "500,000"];
        const allPresentQuotaAttn = ChecksQuotaAttn.every((item) =>
          quotaAttainmentValues.includes(item)
        );

        try {
          assert.strictEqual(
            allPresentQuota,
            true,
            "The 'Quota' Report Object not updating with the Annual Quota Effective dated changes, Failed."
          );
          assert.strictEqual(
            allPresentQuotaAttn,
            true,
            "The 'Quota Attainment' Report Object not updating with the Annual Quota Effective dated changes, Failed."
          );
          console.log(
            "The Report Objects 'Quota' and 'Quota Attainment' are updated successfully, Passed"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );
  }
);
