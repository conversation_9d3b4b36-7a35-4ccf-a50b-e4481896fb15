const {
  localplaywrightFixtures: { test, expect },
} = require("../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  if (await page.locator("text=Logged in as").isVisible({ timeout: 10000 })) {
    await page.getByRole("button", { name: "Exit" }).click();
    await page.waitForTimeout(10000);
  }

  await page.goto("/crystal", { waitUntil: "networkidle" });
  await expect(page.getByTestId("login-indicator")).toContainText(
    "New Simulator"
  );
});

test.describe(
  "Crystal",
  { tag: ["@regression", "@crystal", "@repconnect-1"] },
  () => {
    test("Create and Delete new simulator and table", async ({ adminPage }) => {
      const page = adminPage.page;
      const newSimulator = page.getByRole("button", { name: "New Simulator" });
      await newSimulator.waitFor({ state: "visible", timeout: 20000 });
      await newSimulator.click();
      await page.getByPlaceholder("Enter Crystal View").click();
      await page
        .getByPlaceholder("Enter Crystal View")
        .fill("Crystal test simulator");
      await page.locator(".ant-select-selection-overflow").click();
      await page.getByText("Crystal Payee").first().click();
      await page.getByText("Add Payees").click();
      await page.getByRole("button", { name: "Create" }).click();
      await page
        .getByText("Created Crystal View...")
        .waitFor({ state: "visible", timeout: 10000 });
      await page.getByRole("button", { name: "Add View" }).click();
      await page.waitForTimeout(1000);
      await page.getByPlaceholder("Enter the View name").click();
      await page.getByPlaceholder("Enter the View name").fill("Crystal table");
      await page
        .getByText(/Description.*./)
        .locator("//following::div/textarea")
        .fill("Playwright test for new simulator");
      await page
        .getByText(/Select Datasheet.*./)
        .locator("//following::div/span/input")
        .first()
        .click();
      await page.getByText("Plywright sheet", { exact: true }).click();
      await page.locator("#newTableForm_displayConditions_0_colName").click();
      await page
        .getByRole("listitem")
        .filter({ hasText: "Closed date" })
        .locator("div")
        .click();
      await page
        .locator("#newTableForm_displayConditions_0_operatorId")
        .click();
      await page.getByText("Is Not Empty").click();
      await page.locator("#newTableForm_successActions_0_columnName").click();
      await page.locator("div[title='Status']").last().click();
      await page.locator("#newTableForm_successActions_0_value").click();
      await page.locator("div[title='True']").last().click();
      await page.locator("#newTableForm_emailField").click();
      await page.getByText("Mail ID").nth(2).click();
      await page.locator("#newTableForm_dateField").click();
      await page.getByText("Closed date").nth(3).click();
      await page.locator("#newTableForm_rowName").click();
      await page.locator("div[title='Amount']").last().click();
      await page
        .getByText(/Amount.*Cell editing/)
        .getByRole("switch")
        .click();
      await page.getByRole("button", { name: "Confirm" }).click();
      await page
        .getByText("Table created successfully")
        .waitFor({ state: "visible", timeout: 10000 });
      await page
        .getByText(/Showing for.*.Crystal Payee/)
        .waitFor({ state: "visible", timeout: 10000 });

      // delete things...
      const table = page.getByText(/Delete table.*./);
      await page
        .getByRole("tab", { name: "Crystal table" })
        .locator("svg")
        .last()
        .click();
      await page.getByRole("menuitem", { name: "Delete" }).click();
      await page
        .getByText("Are you sure you want to permanently delete the table?")
        .waitFor();
      await page.getByRole("button", { name: "Confirm" }).click();
      await page
        .getByText("Crystal Table successfully deleted...")
        .waitFor({ state: "visible", timeout: 5000 });
      await table.waitFor({ state: "hidden", timeout: 5000 });
      await page.getByRole("link", { name: "Crystal" }).click();
      await page
        .locator("//span[text()='Crystal test simulator']/../../../../div")
        .click();
      await page
        .getByRole("menuitem", { name: "Delete Simulator" })
        .locator("span")
        .click();
      await page.getByRole("button", { name: "Delete" }).last().click();
      await page
        .getByText("Crystal View successfully deleted")
        .waitFor({ state: "visible", timeout: 5000 });
    });

    test("Add and remove payees from the simulator", async ({ adminPage }) => {
      const page = adminPage.page;
      const simulator = await page
        .locator("div.cursor-pointer")
        .filter({ hasText: "New1" });
      await simulator.waitFor({ state: "visible", timeout: 20000 });
      await simulator.click();
      await page.getByRole("button", { name: "Manage Payees" }).click();
      await page.locator(".ant-select-selection-overflow").click();
      await page.getByText("Crystal User").click();
      await page
        .getByRole("dialog", { name: "Manage Payees" })
        .getByRole("button", { name: "Add" })
        .click();
      await page
        .locator("//span[text()='Payee(s) added']")
        .first()
        .waitFor({ state: "visible", timeout: 5000 });
      await page
        .getByText("CUCrystal UserRemove")
        .getByRole("button", { name: "Remove" })
        .click();
      await page
        .locator("//span[text()='Payee(s) removed']")
        .first()
        .waitFor({ state: "visible", timeout: 5000 });
      await page
        .getByRole("listitem")
        .getByText(/Crystal User/)
        .waitFor({ state: "detached", timeout: 2000 });
      await page.getByRole("button", { name: "Close" }).click();
    });

    test("Delete Projection", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.waitForTimeout(1000);
      await page
        .locator("//span[text()='Crystal simulator']/../../../../div")
        .click();
      await page
        .getByRole("menuitem", { name: "Delete Simulator" })
        .locator("span")
        .click();
      await page.getByText("Delete", { exact: true }).click();
      await page
        .getByText("Crystal View successfully deleted")
        .waitFor({ state: "visible", timeout: 10000 });
    });

    test("Crystal Preview Payee - Projections", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "/crystal/preview/c3de8138-691c-4c65-bd44-719507c8b5c4/<EMAIL>",
        { waitUntil: "networkidle" }
      );
      await page
        .getByText("Current Payout")
        .first()
        .waitFor({ state: "visible", timeout: 20000 });
      await page
        .getByText("₹0.00")
        .first()
        .waitFor({ state: "visible", timeout: 5000 });
      await page
        .getByText("6,000")
        .waitFor({ state: "visible", timeout: 5000 });
      const selectOpportunities = page.getByRole("button", {
        name: "Select opportunities",
      });
      await selectOpportunities.waitFor({ state: "visible", timeout: 25000 });
      await selectOpportunities.click();
      await addFilter(page);
      await page.getByRole("button", { name: "Apply projections" }).click();
      const variablePayout = page
        .getByText(/₹.*. Variable Payout/)
        .getByText(/₹6,000/);
      await variablePayout.waitFor({ state: "visible", timeout: 10000 });
      await page
        .getByText("₹0.00")
        .first()
        .waitFor({ state: "visible", timeout: 15000 });
      // await expect(await page.getByText("₹100")).toHaveCount(5);
      // Validating Projected Payout before modifications
      await selectOpportunities.click();
      await addFilter(page);
      await page.locator("[col-id='co_1_amount']").last().dblclick();
      await page.getByRole("textbox", { name: "Input Editor" }).fill("500");
      await page.getByRole("textbox", { name: "Input Editor" }).press("Enter");
      await page.getByRole("button", { name: "Apply projections" }).click();
      await variablePayout.waitFor({ state: "visible", timeout: 10000 });
      await page
        .getByText("₹0.00")
        .first()
        .waitFor({ state: "visible", timeout: 15000 });
      // await expect(await page.getByText("₹500")).toHaveCount(5);
    });

    test("Publish View", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/crystal", { waitUntil: "networkidle" });
      await page.getByText("Crystal - Preview Payee").click();
      await page.getByRole("button", { name: "Publish" }).click();
      await page.getByRole("button", { name: "Yes, Publish" }).click();
      await page
        .getByText("Crystal View Updated")
        .first()
        .waitFor({ state: "visible", timeout: 15000 });
    });

    async function addFilter(page) {
      await page.getByRole("tablist").getByRole("button").click();
      await page
        .locator("#displayConditions_displayConditions_0_colName")
        .click();
      await page
        .locator("#filter-component-card div")
        .filter({ hasText: /^Rep ID$/ })
        .nth(1)
        .click();
      await page
        .locator("#displayConditions_displayConditions_0_operatorId")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Equal To$/ })
        .nth(1)
        .click();
      await page
        .locator("#displayConditions_displayConditions_0_value")
        .click();
      await page
        .locator("#displayConditions_displayConditions_0_value")
        .fill("202");
      await page.getByRole("button", { name: "Apply", exact: true }).click();
      await page.getByLabel("Press Space to toggle row").check();
    }
  }
);
