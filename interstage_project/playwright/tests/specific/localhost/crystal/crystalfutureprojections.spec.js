import { expect } from "@playwright/test";
import Crystalfuture from "../../../../test-objects/crystalfutureprojections-objects";
import CommonUtils from "../../../../test-objects/common-utils-objects";
const {
  localplaywrightFixtures: { test },
} = require("../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  if (await page.getByText("Logged in as").isVisible({ timeout: 10000 })) {
    try {
      await page.getByRole("button", { name: "Exit" }).click();
      await page.waitForTimeout(5000);
    } catch {
      console.log(
        "in before each, unable to click on Exit button for Logged in user"
      );
    }
  }

  await page.goto("/crystal", { waitUntil: "networkidle" });
});

test.describe("Crystal", { tag: ["@regression","@crystal","@rbac","@repconnect-1"] }, () => {
  test("To check the Date field for payee with with monthly frequency", async ({
    adminPage,
    request,
  }, testInfo) => {
    test.info().annotations.push(
      {
        type: "Test ID",
        description: "INTER-T16901, INTER-T16902, INTER-T16900",
      },
      {
        type: "Description",
        description:
          "Go to crystal, Go to a draft simulator, click preview as payee, check date field ",
      },
      {
        type: "Expected behaviour",
        description: "Should display the monthly period from current month",
      }
    );
    const page = adminPage.page;
    const crystalfuture = new Crystalfuture(page);
    await crystalfuture.navigateToPreviewasPayee(
      "Crystal monthly - raghul",
      "view monthly 2"
    );
    const page1Promise = page.waitForEvent("popup");
    await crystalfuture.clickPreviewAsPayee();
    const page1 = await page1Promise;
    await page1.waitForTimeout(3000);
    await crystalfuture.clickDateField(page1);
    const currentYear = new Date().getFullYear();
    await crystalfuture.logAndVerifyDropdownValues(
      page1,
      `December ${currentYear}`
    );
    await crystalfuture.logdatedowncount(page1);
  });

  test("To check the Date field for payee with with Quarterly frequency", async ({
    adminPage,
    request,
  }, testInfo) => {
    test.info().annotations.push(
      {
        type: "Test ID",
        description: "INTER-T16903 ",
      },
      {
        type: "Description",
        description:
          "Go to crystal, Go to a draft simulator, click preview as payee, check date drop down for quarterly",
      },
      {
        type: "Expected behaviour",
        description: "Should display the quarterly period from current quarter",
      }
    );
    const page = adminPage.page;
    const crystalfuture = new Crystalfuture(page);
    await crystalfuture.navigateToPreviewasPayee(
      "Crystal quarterly",
      "view quarterly"
    );
    const page1Promise = page.waitForEvent("popup");
    await crystalfuture.clickPreviewAsPayee();
    const page1 = await page1Promise;
    await page1.waitForTimeout(3000);
    await crystalfuture.clickDateField(page1);
    const currentYear = new Date().getFullYear();
    await crystalfuture.logAndVerifyDropdownValues(
      page1,
      `Q4 (Oct ${currentYear} - Dec ${currentYear})`
    );
    await crystalfuture.logdatedowncount(page1);
  });

  test("To check the Date field for payee with with Weekly frequency and Date field post increasing the  numbers in Number of future periods for Crystal projection - Custom Calendar in ADMIN UI ", async ({
    adminPage,
    request,
  }, testInfo) => {
    test.info().annotations.push(
      {
        type: "Test ID",
        description: "INTER-T16906, INTER-T16907 ",
      },
      {
        type: "Description",
        description:
          "Go to crystal, Go to a draft simulator, click preview as payee, check date drop down for weekly with 30 period in drop down ",
      },
      {
        type: "Expected behaviour",
        description: "Should display the weekly period with 30 future pertiod",
      }
    );
    const page = adminPage.page;
    const crystalfuture = new Crystalfuture(page);
    await crystalfuture.navigateToPreviewasPayee(
      "Crystal weekly",
      "view weekly"
    );
    const page1Promise = page.waitForEvent("popup");
    await crystalfuture.clickPreviewAsPayee();
    const page1 = await page1Promise;
    await page1.waitForTimeout(3000);
    await crystalfuture.clickDateField(page1);
    await crystalfuture.logandVerifyDateDropdownCount(page1, 30);
  });

  test("To check the Date field for payee with with Half yearly frequency", async ({
    adminPage,
    request,
  }, testInfo) => {
    test.info().annotations.push(
      {
        type: "Test ID",
        description: "INTER-T16904 ",
      },
      {
        type: "Description",
        description:
          "Go to crystal, Go to a draft simulator, click preview as payee, check date drop down for Halfyearly frequency",
      },
      {
        type: "Expected behaviour",
        description: "Should display the current half of the year",
      }
    );
    const page = adminPage.page;
    const crystalfuture = new Crystalfuture(page);
    await crystalfuture.navigateToPreviewasPayee(
      "Crystal halfyearly",
      "view halfyearly"
    );
    const page1Promise = page.waitForEvent("popup");
    await crystalfuture.clickPreviewAsPayee();
    const page1 = await page1Promise;
    await page1.waitForTimeout(3000);
    await crystalfuture.clickDateField(page1);
    const currentYear = new Date().getFullYear();
    await crystalfuture.logAndVerifyDropdownValues(
      page1,
      `H2 (Jul ${currentYear} - Dec ${currentYear})`
    );
    await crystalfuture.logdatedowncount(page1);
  });

  test("To check the Date field for payee with with Annual frequency", async ({
    adminPage,
    request,
  }, testInfo) => {
    test.info().annotations.push(
      {
        type: "Test ID",
        description: "INTER-T16905 ",
      },
      {
        type: "Description",
        description:
          "Go to crystal, Go to a draft simulator, click preview as payee, check date drop down for Halfyearly frequency",
      },
      {
        type: "Expected behaviour",
        description: "Should display the year",
      }
    );
    const page = adminPage.page;
    const crystalfuture = new Crystalfuture(page);
    await crystalfuture.navigateToPreviewasPayee(
      "Crystal annual",
      "view annual"
    );
    const page1Promise = page.waitForEvent("popup");
    await crystalfuture.clickPreviewAsPayee();
    const page1 = await page1Promise;
    await page1.waitForTimeout(3000);
    await crystalfuture.clickDateField(page1);
    // await crystalfuture.logdatedropdownvalues(page1);
    const currentYear = new Date().getFullYear();
    await crystalfuture.logAndVerifyDropdownValues(page1, `${currentYear}`);
    await crystalfuture.logdatedowncount(page1);
  });

  test("To check the date format in the date column and editing the date column in select opportunities", async ({
    adminPage,
    request,
  }, testInfo) => {
    test.info().annotations.push(
      {
        type: "Test ID",
        description: "INTER-T16908, INTER-T16909, INTER-T16910 ",
      },
      {
        type: "Description",
        description:
          "Go to crystal, Go to a draft simulator, click preview as payee, select opportunities edit the date column",
      },
      {
        type: "Expected behaviour",
        description: "Should display the older date below the new date",
      }
    );
    const page = adminPage.page;
    const crystalfuture = new Crystalfuture(page);
    await crystalfuture.navigateToPreviewasPayee(
      "Crystal weekly",
      "view weekly"
    );
    const page1Promise = page.waitForEvent("popup");
    await crystalfuture.clickPreviewAsPayee();
    const page1 = await page1Promise;
    await crystalfuture.clickSelectOpportunities(page1);
    await page1.waitForTimeout(2000);
    await crystalfuture.editDatecolumninopportunities(page1, "Nov 21,", "22");
    await expect(page1.getByText("Nov 22,")).toBeVisible();
    await crystalfuture.verifyAndLogVisibility("Nov 21,", "Date edited");
  });

  test("To check whether quota widget is displayed for payees with No quota", async ({
    adminPage,
    request,
  }, testInfo) => {
    test.info().annotations.push(
      {
        type: "Test ID",
        description: "INTER-T16911 ",
      },
      {
        type: "Description",
        description:
          "Go to crystal, Go to a draft simulator, click preview as payee, check for quota widget",
      },
      {
        type: "Expected behaviour",
        description: "Should not display quota widget",
      }
    );
    const page = adminPage.page;
    const crystalfuture = new Crystalfuture(page);
    await crystalfuture.navigateToPreviewasPayee(
      "Crystal annual",
      "view annual"
    );
    const page1Promise = page.waitForEvent("popup");
    await crystalfuture.clickPreviewAsPayee();
    const page1 = await page1Promise;
    await page1.waitForTimeout(3000);
    // const locator = page1.locator("text=Commission Payout");
    const locator = page1.locator("text=Quota Attainment");
    const isVisible = await locator.isVisible();
    console.log(`Is 'Quota Attainment' visible: ${isVisible}`);
    // Assert that the "Commission Payout" text is hidden
    await page1.waitForTimeout(3000);
    await expect(locator).toBeHidden();
  });

  // Quota Attainment
  test("To check whether quota widget is displayed for payees with quota", async ({
    adminPage,
    request,
  }, testInfo) => {
    test.info().annotations.push(
      {
        type: "Test ID",
        description: "INTER-T16912, INTER-T16920 ",
      },
      {
        type: "Description",
        description:
          "Go to crystal, Go to a draft simulator, click preview as payee, check for quota widget, check quota percentage in payouts",
      },
      {
        type: "Expected behaviour",
        description: "Should display the older date below the new date",
      }
    );
    const page = adminPage.page;
    const crystalfuture = new Crystalfuture(page);
    const commonPage = new CommonUtils(page);
    // await crystalfuture.navigateToPreviewasPayee(
    //   "Crystal halfyearly Draft Last",
    //   "view halfyearly"
    // );
    // const page1Promise = page.waitForEvent("popup");
    // await crystalfuture.clickPreviewAsPayee();
    // const page1 = await page1Promise;
    // await page1.waitForTimeout(3000);
    // await expect(page1.getByText("Quota Attainment")).toBeVisible();
    // await expect(page1.getByText("675.5", { exact: true })).toBeVisible();
    await crystalfuture.navigateToPayouts();
    await commonPage.setStorageCommValue("31-December-2024");
    await page.getByRole("link", { name: "crystal payee 4" }).click();
    await expect(page.getByText("Primary Quota").first()).toBeVisible();
    await expect(page.getByText("675.50%")).toBeVisible();
  });
});
