const {
  crystalBalaFixtures: { test, expect },
} = require("../../../fixtures");

test.describe(
  "Crystal Testcases",
  { tag: ["@regression","@crystal","@repconnect-1"] },
  () => {
    test("Payee Preview", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("http://localhost:3000/crystal", {
        waitUntil: "networkidle",
      });
      await page.getByRole("link", { name: "Crystal Sim" }).click();
      const page1Promise = page.waitForEvent("popup");
      await page.getByRole("button", { name: "Preview as payee" }).click();
      const page1 = await page1Promise;
      await page1.waitForLoadState("networkidle");
      await expect(page1.getByTestId("login-indicator")).toContainText(
        "payee crystal"
      );
      await expect(page1.getByText("Commission Payout")).toBeVisible();
      await expect(
        page1.getByRole("button", { name: "Select opportunities" })
      ).toBeVisible();
      await expect(page1.getByText("Payout Summary")).toBeVisible();
      const currentUrl = await page1.url();
      const expectedUrl =
        "http://localhost:3000/crystal/preview/97a211b2-e9ed-499d-862c-d4af0317e862/<EMAIL>";
      await expect(currentUrl).toBe(expectedUrl);
    });

    test("Create Draft", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("http://localhost:3000/crystal", {
        waitUntil: "networkidle",
      });
      await page.getByRole("button", { name: "New Simulator" }).click();
      await page.getByPlaceholder("Enter Crystal View").click();
      await page.getByPlaceholder("Enter Crystal View").fill("Draft");
      await page
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select Payee" })
        .click();
      await page.getByText("Payee Crystal").click();
      await page.getByText("Add Payees*").click();
      await page.getByRole("button", { name: "Create" }).click();
      await expect(page.getByText("Showing for")).toBeVisible();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^Draft$/ })
          .nth(1)
      ).toBeVisible();
      await page.getByRole("link", { name: "Crystal" }).click();
      await page.getByRole("button", { name: "Draft" }).click();
      await expect(
        page.getByRole("link", { name: "Draft Modified by Super Admin" })
      ).toBeVisible();
    });

    test("Published Add user and Delete Table", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/crystal/ef5875c3-bb9c-4048-8955-a8cc1b7d6b35",
        {
          waitUntil: "networkidle",
        }
      );
      await page.getByRole("button", { name: "Manage Payees" }).click();
      await page
        .getByLabel("Manage Payees")
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select Payee" })
        .click();
      await page.getByText("Payee2 Crystal").click();
      await page.getByRole("button", { name: "Add" }).click();
      await expect(page.getByText("Adding payee(s)")).toBeVisible();
      await expect(page.getByText("Payee(s) added")).toBeVisible();
      await page.getByText("Payee(s) added").click();
      await expect(page.getByText("Payee(s) added")).toBeVisible();
      await page.locator(".ant-modal-close-x").click();
      await page
        .getByRole("tab", { name: "Pub View" })
        .locator("svg")
        .first()
        .click();


      await page.getByRole("button", { name: "Edit" }).click();
      await expect(
        page.getByText(
          "You're now editing a published Crystal view. Once you're done, hit 'Update' to ensure your payees see the latest version of this Crystal view"
        )
      ).toBeVisible();
      await page
        .getByRole("tab", { name: "Pub View" })
        .locator("svg")
        .last()
        .click();
      await page.getByRole("menuitem", { name: "Delete" }).click();
      await expect(
        page.getByText("Are you sure you want to permanently delete the table?")
      ).toBeVisible();
      await page.getByRole("button", { name: "Confirm" }).click();
      await expect(
        page.getByText("Deleting the Crystal Table...")
      ).toBeVisible();
      await expect(page.getByText("Crystal Table successfully")).toBeVisible();
      await page.getByText("Crystal Table successfully").click();
      await page.getByRole("button", { name: "Update" }).click();
      await expect(
        page.getByText("Are you sure you want to make this update?")
      ).toBeVisible();
      await page.getByRole("button", { name: "Confirm" }).click();
      await expect(
        page.getByText("Updating Crystal Simulator...")
      ).toBeVisible();
      await expect(page.getByTestId("login-indicator")).toContainText("Draft");
    });

    test("Reportee Table", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/crystal/5df8d505-f80b-4f00-973a-4995de5c194b",
        {
          waitUntil: "networkidle",
        }
      );
      await expect(
        page.getByRole("gridcell", { name: "151010" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "151014" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "151011" })
      ).toBeVisible();
    });

    test.describe.skip("Waiting for kd to be enabled via url", () => {
      test("Payout Summary and Payout by View", async ({ adminPage }) => {
        const page1 = adminPage.page;
        await page1.goto(
          "http://localhost:3000/crystal/preview/97a211b2-e9ed-499d-862c-d4af0317e862/<EMAIL>?kd=2024-06-30",
          { waitUntil: "networkidle" }
        );
        await page1
          .getByRole("button", { name: "Select opportunities" })
          .click();
        await page1
          .getByRole("gridcell", {
            name: "Press Space to toggle row selection (unchecked)  151005",
          })
          .getByLabel("Press Space to toggle row")
          .check();
        await page1.getByRole("button", { name: "Apply projections" }).click();
        await expect(
          page1.locator("g:nth-child(3) > path").first()
        ).toBeVisible();
        await expect(
          page1.locator("g:nth-child(3) > path").first()
        ).toBeHidden();
        await page1.getByRole("button", { name: "Payout Summary" }).click();
        await expect(
          page1.getByRole("gridcell", { name: "₹220,000.00" }).nth(1)
        ).toBeVisible();
        await page1.getByRole("button", { name: "Payout By New" }).click();
        await expect(page1.getByRole("treegrid")).toContainText("₹10,000.00");
      });
      test("Currency", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/crystal/28927a37-02a5-4a6f-8810-5dfc9540b90e",
          {
            waitUntil: "networkidle",
          }
        );
        await page
          .locator("div")
          .filter({ hasText: /^PCPayee Crystal$/ })
          .nth(2)
          .click();
        await page.getByText("Payee3 Crystal").click();
        await expect(
          page.getByRole("gridcell", { name: "151005" })
        ).toBeHidden();
        await expect(
          page.getByRole("gridcell", { name: "151006" })
        ).toBeHidden();
        await expect(
          page.getByRole("gridcell", { name: "151009" })
        ).toBeHidden();
        await page.goto(
          "http://localhost:3000/crystal/preview/28927a37-02a5-4a6f-8810-5dfc9540b90e/<EMAIL>?kd=2024-06-30",
          {
            waitUntil: "networkidle",
          }
        );
        await expect(page.getByTestId("login-indicator")).toContainText(
          "$0.00"
        );
      });

      test("Remove Plan", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("http://localhost:3000/plans", {
          waitUntil: "networkidle",
        });
        await page.getByText("Payee Crystal").click();
        await page
          .getByTestId("<EMAIL> delete user icon")
          .click();
        await page.getByRole("button", { name: "Save" }).click();
        await expect(
          page.getByText("Successfully saved 1 payees")
        ).toBeVisible();
        await page.getByRole("button", { name: "Exit Canvas" }).click();

        await page.goto(
          "http://localhost:3000/crystal/preview/7453bd3e-b8f9-41e7-9ee8-fd3a436d8158/<EMAIL>?kd=2024-06-30",
          {
            waitUntil: "networkidle",
          }
        );

        await expect(page.getByText("Payee Crystal")).toBeVisible();
        await page
          .getByRole("button", { name: "Select opportunities" })
          .click();
        await page
          .getByRole("gridcell", {
            name: "Press Space to toggle row selection (unchecked)  151034",
          })
          .getByLabel("Press Space to toggle row")
          .check();
        await page.getByRole("button", { name: "Apply projections" }).click();
        await expect(
          page.locator("g:nth-child(3) > path").first()
        ).toBeVisible();
        await expect(
          page.locator("g:nth-child(3) > path").first()
        ).toBeHidden();
        await expect(page.getByText("Payee Crystal")).toBeHidden();
      });

      test("Quota Adjustment", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/crystal/preview/7453bd3e-b8f9-41e7-9ee8-fd3a436d8158/<EMAIL>?kd=2024-06-30",
          {
            waitUntil: "networkidle",
          }
        );

        await page
          .getByRole("button", { name: "Select opportunities" })
          .click();
        await page
          .getByRole("gridcell", {
            name: "Press Space to toggle row selection (unchecked)  151030",
          })
          .getByLabel("Press Space to toggle row")
          .check();
        await page.getByRole("button", { name: "Apply projections" }).click();
        await expect(
          page.locator("g:nth-child(3) > path").first()
        ).toBeVisible();
        await expect(
          page.locator("g:nth-child(3) > path").first()
        ).toBeHidden();
        await page
          .getByRole("button", { name: "Payout By Quota View" })
          .click();
        await expect(
          page.getByRole("gridcell", { name: "₹800.00" })
        ).toBeVisible();

        await page.goto("http://localhost:3000/quotas", {
          waitUntil: "networkidle",
        });

        await page.getByText("payee5 crystal", { exact: true }).click();
        await page.getByText("Custom").click();
        await page.getByText("Primary Quota").click();
        await page.locator(".rounded-lg > .w-4").click();
        await page
          .locator(
            "td:nth-child(7) > .ant-input-number > .ant-input-number-input-wrap > .ant-input-number-input"
          )
          .first()
          .fill("4000.00");
        await page.getByRole("button", { name: "Save" }).click();
        await expect(
          page.getByText("Added/Modified Quota Successfully")
        ).toBeVisible();
        await page.goto(
          "http://localhost:3000/crystal/preview/7453bd3e-b8f9-41e7-9ee8-fd3a436d8158/<EMAIL>?kd=2024-06-30",
          {
            waitUntil: "networkidle",
          }
        );

        await page
          .getByRole("button", { name: "Select opportunities" })
          .click();
        await page
          .getByRole("gridcell", {
            name: "Press Space to toggle row selection (unchecked)  151030",
          })
          .getByLabel("Press Space to toggle row")
          .check();
        await page.getByRole("button", { name: "Apply projections" }).click();
        await expect(
          page.locator("g:nth-child(3) > path").first()
        ).toBeVisible();
        await expect(
          page.locator("g:nth-child(3) > path").first()
        ).toBeHidden();
        await page
          .getByRole("button", { name: "Payout By Quota View" })
          .click();
        await expect(
          page.getByRole("gridcell", { name: "₹1,000.00" })
        ).toBeVisible();
      });

      test("Edit Column", async ({ adminPage }) => {
        const page = adminPage.page;

        await page.goto(
          "http://localhost:3000/crystal/preview/59a8f2ff-3abd-45b4-a239-731b06d2ba5a/<EMAIL>?kd=2024-06-30",
          {
            waitUntil: "networkidle",
          }
        );
        //   localhost:3000/crystal/preview/6dbaf5b3-15ea-441b-845f-c2bd7321e924/<EMAIL>?kd=2024-06-30

        await page
          .getByRole("button", { name: "Select opportunities" })
          .click();
        await expect(
          page.getByRole("gridcell", { name: "1,000" }).locator("div").first()
        ).toBeEditable();

        await page.getByText("1,000").dblclick();

        await page.getByLabel("Input Editor").fill("1500");
        await page.getByLabel("Input Editor").press("Enter");

        await expect(
          page.locator('[id="rc-tabs-1-panel-Edit\\ View"]')
        ).toContainText("1,0001500");

        await page
          .getByRole("gridcell", {
            name: "Press Space to toggle row selection (unchecked)  151030",
          })
          .getByLabel("Press Space to toggle row")
          .check();
        await page.getByRole("button", { name: "Apply projections" }).click();
        await expect(
          page.getByText("While we crunch the numbers,")
        ).toBeVisible();
        await expect(
          page.locator("g:nth-child(3) > path").first()
        ).toBeHidden();
        await page.getByRole("button", { name: "Payout By Edit View" }).click();
        await page.getByRole("button", { name: "Payout Summary" }).click();
        await page
          .getByRole("button", { name: "Select opportunities" })
          .click();
        await expect(
          page.getByLabel("Press Space to toggle row selection (checked)")
        ).toBeChecked();
      });
    });

    test("Edit View", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/crystal/519e9db8-91b2-426d-866d-17612c99b693",
        {
          waitUntil: "networkidle",
        }
      );
      await expect(
        page.getByRole("gridcell", { name: "151005" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "151006" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "151009" })
      ).toBeVisible();
      await expect(page.getByRole("gridcell", { name: "151007" })).toBeHidden();
      await expect(page.getByRole("gridcell", { name: "151008" })).toBeHidden();

      await page.getByRole("button", { name: "Edit" }).click();
      await expect(page.getByTestId("login-indicator")).toContainText(
        "You're now editing a published Crystal view. Once you're done, hit 'Update' to ensure your payees see the latest version of this Crystal view"
      );
      await page
        .getByRole("tab", { name: "Pub View" })
        .locator("svg")
        .last()
        .click();
      await expect(page.getByRole("menuitem", { name: "Edit" })).toBeEnabled();

      await page.getByRole("button", { name: "Add tab" }).click();
      await page.getByRole("button", { name: "Confirm" }).click();
      await expect(page.getByText("Please enter the view name")).toBeVisible();
      await expect(page.getByText("Please enter description")).toBeVisible();
      await expect(page.getByText("Please select source")).toBeVisible();
      await expect(page.getByText("Please select type")).toBeVisible();
      await expect(page.getByText("Please select operator")).toBeVisible();
      await expect(page.getByText("Please select field")).toBeVisible();
      await expect(page.getByText("Please enter value")).toBeVisible();
      await expect(page.getByText("Please select email field")).toBeVisible();
      await expect(page.getByText("Please select date field")).toBeVisible();
      await expect(page.getByText("Please set row name")).toBeVisible();

      await page.locator("#newTableForm_crystalTableDescription").click();
      await page
        .locator("#newTableForm_crystalTableDescription")
        .fill("published view");
      await page.locator("#newTableForm_sourceId").click();
      await page.getByText("New DS").click();

      // From here
      await page.locator("#newTableForm_displayConditions_0_colName").click();
      await page
        .locator("div:nth-child(6) > .ant-select-item-option-content")
        .click();
      await page
        .locator("#newTableForm_displayConditions_0_operatorId")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Equal To$/ })
        .nth(1)
        .click();
      await page.locator("#newTableForm_displayConditions_0_value").click();
      await page
        .locator("div")
        .filter({ hasText: /^True$/ })
        .nth(2)
        .click();
      await page.locator("#newTableForm_successActions_0_columnName").click();
      await page
        .locator(
          ".ant-space-item > .ant-row > .ant-col > .ant-form-item-control-input > .ant-form-item-control-input-content > div > .ant-select > div:nth-child(3) > div > .ant-select-dropdown > div > .rc-virtual-list > .rc-virtual-list-holder > div > .rc-virtual-list-holder-inner > div:nth-child(6) > .ant-select-item-option-content"
        )
        .click();
      await page.locator("#newTableForm_successActions_0_value").click();
      await page
        .locator(
          ".ant-space > div:nth-child(3) > .ant-row > .ant-col > .ant-form-item-control-input > .ant-form-item-control-input-content > div > .ant-select > div:nth-child(3) > div > .ant-select-dropdown > div > .rc-virtual-list > .rc-virtual-list-holder > div > .rc-virtual-list-holder-inner > div:nth-child(2) > .ant-select-item-option-content"
        )
        .click();

      await page.locator("#newTableForm_emailField").click();
      await page.getByRole("listitem").getByText("email").first().click();
      await page.locator("#newTableForm_dateField").click();
      await page
        .locator("#newTableForm")
        .getByText("end", { exact: true })
        .nth(2)
        .click();
      await page.locator("#newTableForm_rowName").click();
      await page
        .locator("#newTableForm")
        .getByText("id", { exact: true })
        .nth(2)
        .click();
      await page.getByPlaceholder("Enter the View name").click();
      await page.getByPlaceholder("Enter the View name").fill("Pub View");
      await page.getByRole("button", { name: "Confirm" }).click();
      await expect(
        page.getByText("A Crystal Table with the same name exists")
      ).toBeVisible();

      await page.getByPlaceholder("Enter the View name").click();
      await page.getByPlaceholder("Enter the View name").fill("Pubs");
      await page.getByRole("button", { name: "Confirm" }).click();
      await expect(page.getByText("Table created successfully")).toBeVisible();

      await page.getByRole("button", { name: "Update" }).click();
      await expect(page.getByRole("dialog")).toContainText(
        "Do you want to update the changes?"
      );
      await expect(page.getByRole("alert")).toContainText(
        "Before updating the changes, we recommend that you 'Preview as Payee' to ensure the simulator works as intended for all payees."
      );
      await page.getByRole("button", { name: "Confirm" }).click();
      await expect(page.getByText("Updating Crystal Simulator")).toBeVisible();

      await expect(page.locator("g > path").first()).toBeVisible();
      await page
        .locator("g > path")
        .first()
        .waitFor({ state: "hidden", timeout: 10000 });

      await page.getByRole("tab", { name: "Pubs" }).locator("span").first().click();
      await expect(
        page.getByRole("gridcell", { name: "151007" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "151008" })
      ).toBeVisible();
    });

    test("Publish popup", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/crystal/6321a2cc-c09e-4659-81c0-e8fcc24e807e",
        {
          waitUntil: "networkidle",
        }
      );
      await page.getByRole("button", { name: "Publish" }).click();
      await expect(page.getByRole("dialog")).toContainText(
        "1 out of 1 payees are already added to other views. If you chose to continue to publish, the user will not be added to this view. Please remove the user from other views first."
      );
      await expect(
        page.getByRole("button", { name: "Yes, Publish" })
      ).toBeDisabled();
      await page.getByRole("button", { name: "Cancel" }).click();
      await page.getByRole("button", { name: "Manage Payees" }).click();
      await page
        .getByLabel("Manage Payees")
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select Payee" })
        .click();
      await page.getByText("Payee2 Crystal").click();
      await page.getByRole("button", { name: "Add", exact: true }).click();
      await expect(page.getByText("Payee(s) added")).toBeVisible();
      await page.locator(".ant-modal-close-x").click();
      await page.getByRole("button", { name: "Publish" }).click();
      await expect(page.getByRole("dialog")).toContainText(
        "1 out of 2 payees are already added to other views. If you chose to continue to publish, the user will not be added to this view. Please remove the user from other views first."
      );
      await expect(
        page.getByRole("button", { name: "Yes, Publish" })
      ).toBeEnabled();
    });

    test("Tabs", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("http://localhost:3000/crystal", {
        waitUntil: "networkidle",
      });
      await page.getByRole("button", { name: "Draft" }).click();
      await expect(page.getByTestId("crystal-published")).toBeHidden();
      await page.getByRole("button", { name: "Published" }).click();
      await expect(page.getByTestId("crystal-draft")).toBeHidden();
    });

    test("Remove Payee", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/crystal/5df8d505-f80b-4f00-973a-4995de5c194b",
        {
          waitUntil: "networkidle",
        }
      );
      await page.getByRole("button", { name: "Manage Payees" }).click();
      await expect(
        page.getByRole("button", { name: "Remove", exact: true })
      ).toBeDisabled();
    });
  }
);
