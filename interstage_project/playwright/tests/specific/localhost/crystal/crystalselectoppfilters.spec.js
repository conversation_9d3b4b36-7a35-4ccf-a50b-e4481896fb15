import CrystalPage from "../../../../test-objects/crystalselectoppfilters-objects";
const {
  localplaywrightFixtures: { test, expect },
} = require("../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  if (await page.getByText("Logged in as").isVisible({ timeout: 10000 })) {
    try {
      await page.getByRole("button", { name: "Exit" }).click();
      await page.waitForTimeout(5000);
    } catch {
      console.log(
        "in before each, unable to click on Exit button for Logged in user"
      );
    }
  }
  await page.goto("/crystal", { waitUntil: "networkidle" });
});

test.describe(
  "Crystalselectopp",
  { tag: ["@regression", "@crystal", "@repconnect-1"] },
  () => {
    test("Go to crystal, Go to a draft simulator, click preview as payee, click select opportunities, click filters, Add all necessary data, click apply button", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T15064, INTER-T15065, INTER-T15066, INTER-T15067, INTER-T15069, INTER-T15063, INTER-T15076, INTER-T15077,INTER-T15078 ",
        },
        {
          type: "Description",
          description:
            "Go to crystal, Go to a draft simulator, click preview as payee, click select opportunities, click filters, Add all necessary data, click apply button",
        },
        {
          type: "Expected behaviour",
          description: "All the actions should performed",
        }
      );
      const page = adminPage.page;
      const crystalPage = new CrystalPage(page);
      await crystalPage.navigateToCrystalDraft();
      const page1 = await crystalPage.openDraftSimulator();
      await crystalPage.selectOpportunities(page1);
      await crystalPage.loader();
      await crystalPage.clickOpenFilters(page1);
      await crystalPage.applyFilter(page1, "Deal owner email", "Contains", "f");
      await crystalPage.addAnotherFilter(
        page1,
        "Deal name",
        "Starts With",
        "D"
      );
      await crystalPage.apply(page1);
      // Assert that the filtered email is visible on the page
      await expect(page1.getByText("<EMAIL>")).toBeVisible();
    });

    test("clicking apply button without adding all data in required fields", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T15068 ",
        },
        {
          type: "Description",
          description:
            "clicking apply button without adding all data in required fields",
        },
        {
          type: "Expected Behaviour",
          description:
            "Application should display a error message below the unfilled field",
        }
      );
      const page = adminPage.page;
      const crystalPage = new CrystalPage(page);
      await crystalPage.navigateToCrystalDraft();
      const page1 = await crystalPage.openDraftSimulator();
      await crystalPage.selectOpportunities(page1);
      await crystalPage.loader();
      await crystalPage.clickOpenFilters(page1);
      await crystalPage.apply(page1);
      await expect(page1.getByText("Please select type")).toBeVisible();
    });

    test("removing a filter using circled minus button", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T15068 ",
        },
        {
          type: "Description",
          description:
            "removing a saved filter using circled minus button in the filter box",
        },
        {
          type: "Expected Behaviour",
          description: "The filter should be removed",
        }
      );
      const page = adminPage.page;
      const crystalPage = new CrystalPage(page);
      await crystalPage.navigateToCrystalDraft();
      const page1 = await crystalPage.openDraftSimulator();
      await crystalPage.selectOpportunities(page1);
      await crystalPage.loader();
      await crystalPage.clickOpenFilters(page1);
      await crystalPage.applyFilter(page1, "Deal name", "Equal To", "DabZ");
      await crystalPage.apply(page1);
      await crystalPage.clickOpenFilters(page1);
      await page1.waitForTimeout(2000);
      await crystalPage.clickMinusButton(page1);
      await crystalPage.apply(page1);
      await expect(
        page1.getByRole("gridcell", { name: "Skyvu" })
      ).toBeVisible();
    });

    test("Two filter are added 2. Apply 3. one filter removed 4. Apply 5.Clear all", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T15074, INTER-T15076,",
        },
        {
          type: "Description",
          description:
            "Two filter are added 2. Apply 3. one filter removed 4. Apply 5.Clear all",
        },
        {
          type: "Expected Behaviour",
          description:
            "Filters are added and clear button should work properly",
        }
      );
      const page = adminPage.page;
      const crystalPage = new CrystalPage(page);
      await crystalPage.navigateToCrystalDraft();
      const page1 = await crystalPage.openDraftSimulator();
      await crystalPage.selectOpportunities(page1);
      await crystalPage.loader();
      await crystalPage.clickOpenFilters(page1);
      await crystalPage.applyFilter(
        page1,
        "Deal id",
        "Greater than or Equal to",
        "30"
      );
      await crystalPage.addAnotherFilter1(page1, "Amount", "Equal To", "6400");
      await crystalPage.apply(page1);
      await expect(
        page1.getByRole("gridcell", { name: "Topdrive" })
      ).toBeVisible();
      await crystalPage.removeFilter2(page1);
      await expect(
        page1.getByRole("gridcell", { name: "Voonte" })
      ).toBeVisible();
      await crystalPage.clearAllFilters(page1);
    });

    test("To check operator drop down with datatype Number(Amount) in select type", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T15079",
        },
        {
          type: "Description",
          description:
            " To check operator drop down with datatype Number(Amount) in select type ",
        },
        {
          type: "Expected Behaviour",
          description: "The dropdown values should match the expected values",
        }
      );
      const page = adminPage.page;
      const crystalPage = new CrystalPage(page);
      await crystalPage.loader();
      await crystalPage.verifyOperatorDropdownoptions("amount", [
        "Equal To",
        "Not Equal To",
        "In",
        "Not In",
        "Less than or Equal to",
        "Greater than or Equal to",
        "Less Than",
        "Greater Than",
        "Is Empty",
        "Is Not Empty",
      ]);
    });

    test("To check operator drop down with datatype Date(Date) in select type", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T15080",
        },
        {
          type: "Description",
          description:
            "To check operator drop down with datatype Date(Date) in select type",
        },
        {
          type: "Expected Behaviour",
          description: "The dropdown values should match the expected values",
        }
      );
      const page = adminPage.page;
      const crystalPage = new CrystalPage(page);
      await crystalPage.loader();
      await crystalPage.verifyOperatorDropdownoptions("date", [
        "Is Empty",
        "Is Not Empty",
        "Is in Current Month",
        "Is in Last Month",
        "Is in Current Fiscal Quarter",
        "Is in Last Fiscal Quarter",
        "Is in Current Fiscal Halfyear",
        "Is in Last Fiscal Halfyear",
        "Is in Current Fiscal year",
        "Is in Last Fiscal year",
        "Is on or Before",
        "Is on or After",
        "Is Before",
        "Is After",
      ]);
    });

    test("To check operator drop down with datatype String in select type", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T15081",
        },
        {
          type: "Description",
          description:
            "To check operator drop down with datatype String in select type",
        },
        {
          type: "Expected Behaviour",
          description: "The dropdown values should match the expected values",
        }
      );
      const page = adminPage.page;
      const crystalPage = new CrystalPage(page);
      await crystalPage.loader();
      await crystalPage.verifyOperatorDropdownoptions("string", [
        "Equal To",
        "Not Equal To",
        "In",
        "Not In",
        "Is Empty",
        "Is Not Empty",
        "Starts With",
        "Ends With",
        "Contains",
        "Does not Contain",
      ]);
    });

    test("To check operator drop down with datatype Boolean in select type", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T15083",
        },
        {
          type: "Description",
          description:
            "To check operator drop down with datatype Boolean in select type",
        },
        {
          type: "Expected Behaviour",
          description: "The dropdown values should match the expected values",
        }
      );
      const page = adminPage.page;
      const crystalPage = new CrystalPage(page);
      await crystalPage.loader();
      await crystalPage.verifyOperatorDropdownoptions("boolean", [
        "Equal To",
        "Not Equal To",
        "Is Empty",
        "Is Not Empty",
      ]);
    });

    test("To check operator drop down with datatype Email in select type", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T15082",
        },
        {
          type: "Description",
          description:
            "To check operator drop down with datatype Email in select type",
        },
        {
          type: "Expected Behaviour",
          description: "The dropdown values should match the expected values",
        }
      );
      const page = adminPage.page;
      const crystalPage = new CrystalPage(page);
      await crystalPage.loader();
      await crystalPage.verifyOperatorDropdownoptions("email", [
        "Equal To",
        "Not Equal To",
        "Is Empty",
        "Is Not Empty",
        "Starts With",
        "Ends With",
        "Contains",
        "Does not Contain",
      ]);
    });

    test("To check whether filter is applied for all the pages. if rows exceeds more than one page", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T15085",
        },
        {
          type: "Description",
          description:
            "To check whether filter is applied for all the pages. if rows exceeds more than one page",
        },
        {
          type: "Expected Behaviour",
          description: "The filters should be applied properly in all pages",
        }
      );
      const page = adminPage.page;
      const crystalPage = new CrystalPage(page);
      await crystalPage.navigateToCrystalDraft();
      const page1 = await crystalPage.openDraftSimulator();
      await crystalPage.selectOpportunities(page1);
      await crystalPage.loader();
      await crystalPage.clickOpenFilters(page1);
      await crystalPage.selectAmountFilter(page1);
      await crystalPage.selectOperatorField(page1, "Greater Than");
      await page.waitForTimeout(1000);
      await crystalPage.enterValue(page1, "2000");
      await page.waitForTimeout(1000);
      await crystalPage.apply(page1);
      await page1.getByLabel("test").getByRole("button").nth(3).click();
      await expect(page1.getByRole("gridcell", { name: "Latz" })).toBeVisible();
    });

    test("To check the filter when impersonated", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T15095",
        },
        {
          type: "Description",
          description: "To check the filter when impersonated",
        },
        {
          type: "Expected Behaviour",
          description:
            "The selected projection count should be displayed  correctly ",
        }
      );
      const page = adminPage.page;
      const crystalPage = new CrystalPage(page);
      await crystalPage.navigateTosettings();
      await crystalPage.selectRole("Payee");
      await crystalPage.clickEditRole();
      await crystalPage.manageCrystalViews();
      await crystalPage.impersonateUser("<EMAIL>");
      await crystalPage.navigateToCrystal();
      await crystalPage.navigateToCrystalDraft();
      const page1Promise = page.waitForEvent("popup");
      await page.getByRole("button", { name: "Preview as payee" }).click();
      const page1 = await page1Promise;
      await page1.getByRole("button", { name: "Select opportunities" }).click();
      await crystalPage.loader1(page1);
      await page1.getByRole("tablist").getByRole("button").click();
      await crystalPage.selectDealnameFilter(page1);
      await crystalPage.selectOperatorField(page1, "Equal To");
      await crystalPage.enterValue(page1, "6");
      await crystalPage.apply(page1);
      await page.waitForTimeout(3000);
      // verify the filter resultnn
      await expect(
        page1.getByRole("gridcell", { name: "Agivu" })
      ).toBeVisible();
      await page1.getByLabel("Close").click();
      await page.getByRole("button", { name: "Exit" }).click();
    });

    test("To check by selecting the filtered rows and apply projections and check the number of records selected on the projections page", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T15087,INTER-T15099",
        },
        {
          type: "Description",
          description:
            "To check by selecting the filtered rows and apply projections and check the number of records selected on the projections page",
        },
        {
          type: "Expected Behaviour",
          description:
            "The selected projection count should be displayed  correctly ",
        }
      );
      const page = adminPage.page;
      const crystalPage = new CrystalPage(page);
      await crystalPage.navigateToCrystalDraft();
      const page1 = await crystalPage.openDraftSimulator();
      await crystalPage.selectOpportunities(page1);
      await crystalPage.loader();
      await page1
        .getByRole("gridcell", {
          name: "Press Space to toggle row selection (unchecked)  55",
        })
        .getByLabel("Press Space to toggle row selection (unchecked)")
        .check();
      await page1
        .getByRole("gridcell", {
          name: "Press Space to toggle row selection (unchecked)  56",
        })
        .getByLabel("Press Space to toggle row selection (unchecked)")
        .check();
      await page1
        .getByRole("gridcell", {
          name: "Press Space to toggle row selection (unchecked)  6",
        })
        .getByLabel("Press Space to toggle row selection (unchecked)")
        .check();
      await crystalPage.applyProjections(page1);
      await page1.waitForTimeout(3000);
      await expect(page1.locator("text=3 Records Selected")).toBeVisible();
    });

    test("To check applied filters are retained when switching between views", async ({
      adminPage,
      request,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T15074, INTER-T15076,",
        },
        {
          type: "Description",
          description:
            "To check applied filters are retained when switching between views",
        },
        {
          type: "Expected Behaviour",
          description:
            "The filters should be retained when switched back to default view from another view",
        }
      );
      const page = adminPage.page;
      const crystalPage = new CrystalPage(page);
      await crystalPage.navigateToCrystalDraft();
      const page1 = await crystalPage.openDraftSimulator();
      await crystalPage.selectOpportunities(page1);
      await crystalPage.loader1(page1);
      await crystalPage.clickOpenFilters(page1);
      await crystalPage.selectstringFilter(page1);
      await crystalPage.selectOperatorField(page1, "Equal To");
      await crystalPage.enterValue(page1, "Agivu");
      await crystalPage.apply(page1);
      await crystalPage.switchView(page1, "view 2");
      await crystalPage.switchView(page1, "test");
      await page1.waitForTimeout(2000);
      // verify Deal status
      await expect(
        page1.getByLabel("test").getByText("<EMAIL>")
      ).toBeVisible();
      await page1.waitForTimeout(2000);
      // verify Deal available status
      await expect(
        page1.getByRole("gridcell", { name: "false" })
      ).toBeVisible();
    });
  }
);
