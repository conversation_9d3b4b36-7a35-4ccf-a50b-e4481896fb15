import QueriesLineItem from "../../../../test-objects/querieslineitems-objects";
const {
  queriesLineItemsFixtures: { test, expect },
} = require("../../../fixtures");

// Function to generate a random word
function getRandomWord() {
  const letters = "abcdefghijklmnopqrstuvwxyz";
  const wordLength = Math.floor(Math.random() * 10) + 1; // Word length between 1 and 10
  let word = "";
  for (let i = 0; i < wordLength; i++) {
    word += letters.charAt(Math.floor(Math.random() * letters.length));
  }
  return word;
}

// Function to generate a string with 1000 random words
function generateText(wordCount) {
  let text = "";
  for (let i = 0; i < wordCount; i++) {
    text += getRandomWord() + " ";
  }
  return text.trim();
}

// To clear the cache so that all the steps can be performed in parallel mode
test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.addInitScript(() => {
    localStorage.clear();
    sessionStorage.clear();
  });
});

test.describe(
  "Queries - Line Items with Context Automation",
  { tag: ["@regression", "@queries", "@repconnect-1"] },
  () => {
    test(
      "Check 'Raise Query' button in Statements Page",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate that when the 'Raise Query' button is selected, the query modal opens in Statements Page",
          },
          { type: "Precondition", description: "Commission Plan and Payouts" },
          {
            type: "Expected Behaviour",
            description:
              "Selecting the 'Raise Query' button on the Statements Page opens the query modal.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickQueriesBtnStatements();

        const ModalTitle = await QueriesLI.getModalTitle();
        if (ModalTitle === "Create Query") {
          console.log("The Raise Query Modal is opening from Statements Page");
        } else {
          console.log(
            "The Raise Query Modal is not opening from Statements Page"
          );
          assert.fail(
            "The Raise Query Modal is not opening from Statements Page"
          );
        }
      }
    );

    test(
      "Raise query button under all the Line Items in the Statements for row-level criteria",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether the Raise Query button is present under all the Line Items in the Statements for row-level criteria",
          },
          { type: "Precondition", description: "Commission Plan and Payouts" },
          {
            type: "Expected Behaviour",
            description:
              "Create Query button should be present under each line item",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickPlan("Queries_RO_plan");
        await QueriesLI.clickCriteria("SR");

        await page.waitForTimeout(6000);

        const countOfButtons = await QueriesLI.countBtns();
        const expectedCount = 6;

        try {
          assert.strictEqual(
            countOfButtons,
            expectedCount,
            "Raise Query button isn't present under each Line Item for row level criteria"
          );
          console.log(
            "Raise Query button is present under each Line Item for row level criteria"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Validate whether the Raise Query button is present under all the Line Items in the Statements for overall sum criteria",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether the Raise Query button is present under all the Line Items in the Statements for overall sum criteria",
          },
          {
            type: "Precondition",
            description:
              "Commission Plan with Overall Sum criteria and Payouts",
          },
          {
            type: "Expected Behaviour",
            description:
              "Create Query button should be present under each line item",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Mar 2024");
        await QueriesLI.clickUser("Payee 18");
        await QueriesLI.clickPeriod("Pyt from current period");
        await QueriesLI.clickPlan("Queries_Overall_Sum");
        await QueriesLI.clickCriteria("Overall_Sum_Criteria");

        await page.waitForTimeout(8000);

        const countOfButtons = await QueriesLI.countBtns();
        const expectedCount = 2;

        try {
          assert.strictEqual(
            countOfButtons,
            expectedCount,
            "Raise Query button isn't present under each Line Item for overall level criteria"
          );
          console.log(
            "Raise Query button is present under each Line Item for overall level criteria"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Add a description longer than 1000 words",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether the user can add a description longer than 1000 words",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "The modal should accept description with more than 1000 words",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const generatedText = generateText(1001);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickQueriesBtnStatements();
        await QueriesLI.raiseQuery(
          "General query",
          "Peter Super Admin",
          "Joker Admin",
          generatedText
        );

        const button = await QueriesLI.createQueryBtn();
        const isEnabled = await button.isEnabled();

        try {
          assert.strictEqual(
            isEnabled,
            true,
            "The user cannot raise a query with a description longer than 1000 words, i.e. Create button is disabled"
          );
          console.log(
            "The user can raise a query with a description longer than 1000 words, i.e. Create button is enabled"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Check for all the fields in the Query Modal of Statements",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate that the modal asks for the following fields to be filled: Subject, Description, Assigned To, Category, and CC in the statement level modal. Also, check if the Subject and Description fields are prefilled according to the Periods and Commission Plan in Statements Page",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "The Query modal should ask for fields like Subject, Description, Assigned To, Category and CC. Also, the Subject and Description should be prefilled",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickQueriesBtnStatements();

        const requiredFields = [
          "Subject",
          "Description",
          "Category",
          "CC",
          "AssignedTo",
        ];

        const payeeEmail = await QueriesLI.getQueryDescTableValue(
          "Payee Email Id"
        );
        const period = await QueriesLI.getQueryDescTableValue("Period");
        const linkToStatement = await page
          .getByRole("link", { name: "Link" })
          .getAttribute("href");

        try {
          await expect(QueriesLI.getFieldLocator("Subject")).toHaveValue(
            "July 2024 Statement"
          );
          assert.strictEqual(
            linkToStatement,
            "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwZXRlckBjcmljay5jb20iLCJwc2QiOiIyMDI0LTA3LTAxIiwicGVkIjoiMjAyNC0wNy0zMSJ9",
            "Link to statement is incorrect"
          );
          assert.strictEqual(period.trim(), "July 2024", "Period is incorrect");
          assert.strictEqual(
            payeeEmail.trim(),
            "<EMAIL>",
            "Payee Email Id is incorrect"
          );

          for (const fieldName of requiredFields) {
            const fieldIsVisible = await QueriesLI.isFieldVisible(fieldName);
            assert.strictEqual(
              fieldIsVisible,
              true,
              `${fieldName} field is missing in Statement Level Query Modal`
            );
            console.log(
              `${fieldName} field is present in Statement Level Query Modal.`
            );
          }
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Check without filling in the Assigned To, CC, and Category fields whether the query can be raised from the statement level modal",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether the user can raise a query without filling in the Assigned To, CC, and Category fields in the statement level modal",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "User shouldn't be able to raise queries without filling all the mandatory fields",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Mar 2024");
        await QueriesLI.clickUser("Payee 18");
        await QueriesLI.clickQueriesBtnStatements();

        const button = await QueriesLI.createQueryBtn();
        const isDisabled = await button.isDisabled();

        try {
          assert.strictEqual(
            isDisabled,
            true,
            "The user can raise a query despite the Assigned To, CC, and Category fields being empty, i.e. Create button is enabled"
          );
          console.log(
            "The user cannot raise a query because the Assigned To, CC, and Category fields are empty, i.e. Create button is disabled"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Change the prefilled Subject and Description from the Statements level modal",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether the prefilled Subject and Description can be changed in the Raise Query modal in the Statements level modal",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description: "The Subject and Description should be editable",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Mar 2024");
        await QueriesLI.clickUser("Payee 18");
        await QueriesLI.clickQueriesBtnStatements();

        const newSubject = "Statement for March";
        const newDescription = "This is an updated Description";

        await QueriesLI.fillField("Subject", newSubject);
        // await QueriesLI.fillField("Description", newDescription);
        await page
          .locator("[data-placeholder ='Provide an update here']")
          .fill(newDescription);

        const updatedSubject = await QueriesLI.getFieldValue("Subject");
        // const updatedDescription = await QueriesLI.getFieldValue("Description");
        const updatedDescription = await page
          .locator("[data-placeholder ='Provide an update here']")
          .textContent();

        try {
          assert.strictEqual(
            updatedSubject,
            newSubject,
            "Failed to change the Subject field"
          );
          assert.strictEqual(
            updatedDescription,
            newDescription,
            "Failed to change the Description field"
          );
          console.log(
            "The Subject and Description fields have been successfully changed."
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Check post query creation, the message 'Query created successfully' appears or not from the statement level modal",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether, after the query has been raised, the message 'Query created successfully' appears along with the action: 'View Query' from the statement level modal",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "After raising the query, a pop up message should be displayed saying 'Query created successfully', along with 'View Query' button",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickQueriesBtnStatements();
        await QueriesLI.raiseQuery(
          "General query",
          "Peter Super Admin",
          "Joker Admin",
          "Commission Amount = 9,000"
        );
        await QueriesLI.clickcreateQueryBtn();
        await page.waitForSelector(
          "div.go4109123758 div.w-full div.text-ever-base-content",
          { state: "visible" }
        );
        const viewbtn = await QueriesLI.getViewAndDismissButtonsText();
        expect(viewbtn[0]).toBe("View Query");

        await page.waitForTimeout(2000);

        const SuccessMsg = await QueriesLI.getSuccessMessageQuery();
        try {
          assert.strictEqual(
            SuccessMsg,
            "Query Saved Successfully",
            "Failed to raise query from the statements page"
          );
          console.log(
            "Query has been raised successfully from the statements page"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Create Query for all available categories, including General Category, Commission Calculations, and Approvals from the Statements Screen",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether the user can create queries for all available categories, including General Category, Commission Calculations, and Approvals from the Statements Screen",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "Users should be able to create queries in all three categories: General Category, Commission Calculations, and Approvals",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("28 Feb 2023");
        await QueriesLI.clickUser("Peter Super Admin");

        await QueriesLI.clickQueriesBtnStatements();
        await QueriesLI.raiseQuery(
          "General query",
          "Peter Super Admin",
          "Joker Admin",
          "This is a General Query"
        );
        await QueriesLI.clickcreateQueryBtn();
        const SuccessMsgGeneral = await QueriesLI.getSuccessMessageQuery();

        await QueriesLI.clickQueriesBtnStatements();
        await QueriesLI.raiseQuery(
          "Commission Calculation",
          "Peter Super Admin",
          "Joker Admin",
          "This is a Commission Calculation Query"
        );
        await QueriesLI.clickcreateQueryBtn();
        const SuccessMsgCC = await QueriesLI.getSuccessMessageQuery();

        await QueriesLI.clickQueriesBtnStatements();
        await QueriesLI.raiseQuery(
          "Approvals",
          "Peter Super Admin",
          "Joker Admin",
          "This is a Approvals Query"
        );
        await QueriesLI.clickcreateQueryBtn();
        const SuccessMsgApv = await QueriesLI.getSuccessMessageQuery();
        await page.waitForTimeout(2000);

        if (
          SuccessMsgGeneral === "Query Saved Successfully" &&
          SuccessMsgCC === "Query Saved Successfully" &&
          SuccessMsgApv === "Query Saved Successfully"
        ) {
          console.log(
            "Queries with the category General, Commission Calculation and Approvals are successfully created"
          );
        } else {
          console.log(
            "Queries with the category General, Commission Calculation and Approvals are not created, Failed"
          );
          assert.fail(
            "Queries with the category General, Commission Calculation and Approvals are not created, Failed"
          );
        }
      }
    );

    test(
      "Click the 'View Query' option from the statement-level modal success message",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether clicking the 'View Query' option navigates the user to the Queries module in a new tab from the statement-level modal",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "Once the query is created, a success popup will appear. Clicking 'View Query' on the popup should navigate to the Queries Module",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("28 Feb 2023");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickQueriesBtnStatements();

        // Validation of clicking the 'View Query' button navigates to Queries Module
        await QueriesLI.raiseQuery(
          "General query",
          "Peter Super Admin",
          "Joker Admin",
          "Commission Amount = ₹370,000.00"
        );
        await QueriesLI.clickcreateQueryBtn();
        await page.waitForSelector(
          "div.go4109123758 div.w-full div.text-ever-base-content",
          { state: "visible" }
        );
        await QueriesLI.clickViewQuery();
        await page.waitForTimeout(3000);
        const Qrylist = await QueriesLI.getQueriesList();
        const isPresent = Qrylist.includes("February 2023 Statement");

        const currentURL = await page.url();

        try {
          assert.strictEqual(
            currentURL,
            "http://localhost:3000/queries/allTickets",
            "On clicking the 'View Query' button, did not navigate to the Queries Module."
          );
          assert.strictEqual(
            isPresent,
            true,
            "Query not listed in the Queries Module"
          );
          console.log(
            "On clicking the 'View Query' button, navigated to the Queries Module."
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Check for all the fields in the Query Modal of Line Item Level",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate that the modal asks for the following fields to be filled: Subject, Description, Assigned To, Category, and CC in the line-item level modal. Also, check if the Subject and Description fields are prefilled according to the Periods and Commission Plan in Line Item Level",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "The Query modal should ask for fields like Subject, Description, Assigned To, Category and CC. Also, the Subject and Description should be prefilled",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickPlan("Queries_RO_plan");
        await QueriesLI.clickCriteria("SR");

        await page.waitForTimeout(2000);

        await QueriesLI.clickQueryBtnLineItem("₹30.00");

        const requiredFields = [
          "Subject",
          "Description",
          "Category",
          "CC",
          "AssignedTo",
        ];

        const payeeEmail = await QueriesLI.getQueryDescTableValue("Email");
        const period = await QueriesLI.getQueryDescTableValue("Period");
        const amount = await QueriesLI.getQueryDescTableValue("Amount");
        const linkToStatement = await page
          .getByRole("link", { name: "Link" })
          .getAttribute("href");
        try {
          await expect(QueriesLI.getFieldLocator("Subject")).toHaveValue(
            "July 2024 - Queries_RO_plan"
          );
          assert.strictEqual(
            linkToStatement,
            "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwZXRlckBjcmljay5jb20iLCJwc2QiOiIyMDI0LTA3LTAxIiwicGVkIjoiMjAyNC0wNy0zMSJ9",
            "Link to statement is incorrect"
          );
          assert.strictEqual(period.trim(), "July 2024", "Period is incorrect");
          assert.strictEqual(
            payeeEmail.trim(),
            "<EMAIL>",
            "Payee Email Id is incorrect"
          );
          assert.strictEqual(amount.trim(), "1", "Amount is incorrect");

          for (const fieldName of requiredFields) {
            const fieldIsVisible = await QueriesLI.isFieldVisible(fieldName);
            assert.strictEqual(
              fieldIsVisible,
              true,
              `${fieldName} field is missing in Statement Level Query Modal`
            );
            console.log(
              `${fieldName} field is present in Statement Level Query Modal.`
            );
          }
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Change the prefilled Subject and Description from the Line Item level modal",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether the prefilled Subject and Description can be changed in the Raise Query modal in the line-item level modal",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description: "The Subject and Description should be editable",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("28 Feb 2023");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickPlan("InterPlan");
        await QueriesLI.clickCriteria("simple");

        await page.waitForTimeout(3000);

        await QueriesLI.clickQueryBtnLineItem("₹");

        const newSubject = "Statement for February 2023";
        const newDescription = "This is an updated Description";

        await QueriesLI.fillField("Subject", newSubject);
        // await QueriesLI.fillField("Description", newDescription);
        await page
          .locator("[data-placeholder ='Provide an update here']")
          .fill(newDescription);

        const updatedSubject = await QueriesLI.getFieldValue("Subject");
        // const updatedDescription = await QueriesLI.getFieldValue("Description");
        const updatedDescription = await page
          .locator("[data-placeholder ='Provide an update here']")
          .textContent();

        try {
          assert.strictEqual(
            updatedSubject,
            newSubject,
            "Failed to change the Subject field"
          );
          assert.strictEqual(
            updatedDescription,
            newDescription,
            "Failed to change the Description field"
          );
          console.log(
            "The Subject and Description fields have been successfully changed."
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Check without filling in the Assigned To, CC, and Category fields whether the query can be raised from the line item level modal",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether the user can raise a query without filling in the Assigned To, CC, and Category fields in the line-item level modal",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "User shouldn't be able to raise queries without filling all the mandatory fields",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jan 2023");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickPlan("InterPlan");
        await QueriesLI.clickCriteria("simple");

        await page.waitForTimeout(3000);

        await QueriesLI.clickQueryBtnLineItem("₹316.15");

        const button = await QueriesLI.createQueryBtn();
        const isDisabled = await button.isDisabled();

        try {
          assert.strictEqual(
            isDisabled,
            true,
            "The user can raise a query despite the Assigned To, CC, and Category fields being empty from Line Item Level, i.e. Create button is enabled"
          );
          console.log(
            "The user cannot raise a query because the Assigned To, CC, and Category fields are empty from Line Item Level, i.e. Create button is disabled"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Check post query creation, the message 'Query created successfully' appears or not from the line item level level modal",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether, after the query has been raised, the message 'Query created successfully' appears along with the action: 'View Query' from the line-item level modal",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "After raising the query, a pop up message should be displayed saying 'Query created successfully'",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickPlan("Queries_RO_plan");
        await QueriesLI.clickCriteria("SR");

        await page.waitForTimeout(3000);

        await QueriesLI.clickQueryBtnLineItem("₹30.00");
        await QueriesLI.raiseQuery(
          "General query",
          "Peter Super Admin",
          "Joker Admin",
          "Commission Amount = ₹9,000"
        );
        await QueriesLI.clickcreateQueryBtn();
        await page.waitForSelector(
          "div.go4109123758 div.w-full div.text-ever-base-content",
          { state: "visible" }
        );
        const viewbtn = await QueriesLI.getViewAndDismissButtonsText();
        expect(viewbtn[0]).toBe("View Query");

        const SuccessMsg = await QueriesLI.getSuccessMessageQuery();
        try {
          assert.strictEqual(
            SuccessMsg,
            "Query Saved Successfully",
            "Failed to raise query from the Line Item"
          );
          console.log("Query has been raised successfully from the Line Item");
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Click the 'View Query' option from the line-item-level modal success message",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether clicking the 'View Query' option navigates the user to the Queries module in a new tab from the line-item level modal",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "Once the query is created, a success popup will appear. Clicking 'View Query' on the popup should navigate to the Queries Module",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickPlan("Queries_RO_Plan");
        await QueriesLI.clickCriteria("SR");

        await page.waitForTimeout(2000);

        await QueriesLI.clickQueryBtnLineItem("₹8,970.00");

        // Validation of clicking the 'View Query' button navigates to Queries Module
        await QueriesLI.raiseQuery(
          "Commission Calculation",
          "Peter Super Admin",
          "Joker Admin",
          "Commission Amount = ₹8,970.00"
        );
        await QueriesLI.clickcreateQueryBtn();
        await page.waitForSelector(
          "div.go4109123758 div.w-full div.text-ever-base-content",
          { state: "visible" }
        );
        await QueriesLI.clickViewQuery();
        await page.waitForTimeout(3000);

        const currentURL = await page.url();

        try {
          assert.strictEqual(
            currentURL,
            "http://localhost:3000/queries/allTickets",
            "On clicking the 'View Query' button, did not navigate to the Queries Module."
          );
          console.log(
            "On clicking the 'View Query' button, navigated to the Queries Module."
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Check for Query modal dropdown whether the Payee can assign the query only to the current / past managers or the Admins",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether the Payee can assign the query only to the current / past managers or the Admins (Check in Queries Module, Statement level modal and line-item level modal)",
          },
          {
            type: "Precondition",
            description: "Current / Past Managers Details",
          },
          {
            type: "Expected Behaviour",
            description:
              "The dropdown should only contain the current / past managers or the admins",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);

        await QueriesLI.navigate("/settings/user-roles");
        await QueriesLI.selectRole("VIEW_CREATE_AND_EDIT_QUERIES");
        await QueriesLI.editRBACbtn();
        await QueriesLI.editRBACpermissions("Users can see all data");
        await QueriesLI.saveRBACchanges();

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Peter Super Admin");

        await QueriesLI.clickQueriesBtnStatements();
        await QueriesLI.clickAssignedTo();

        const AssignedToList = await QueriesLI.getAssignedToList();
        expect(AssignedToList[0]).toBe("Joker Admin");
        expect(AssignedToList[1]).toBe("Payee 18");
        expect(AssignedToList[2]).toBe("Payee 3");
        expect(AssignedToList[3]).toBe("Peter Super Admin");

        await QueriesLI.clickCC();

        const CCList = await QueriesLI.getCCList();
        expect(CCList[0]).toBe("Joker Admin");
        expect(CCList[1]).toBe("Payee 18");
        expect(CCList[2]).toBe("Payee 3");
        expect(CCList[3]).toBe("Peter Super Admin");
      }
    );

    test(
      "Validate that adding a new line / updating item updates the statements screen and enables the Raise Query button",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate that adding or updating a line item is reflected on the Statements Page and that the Query button is present.",
          },
          { type: "Precondition", description: "Databook and Datasheet" },
          {
            type: "Expected Behaviour",
            description:
              "The newly added line-item / updated line-item should also contain the Raise Query button",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        testInfo.setTimeout(testInfo.timeout + 120000);
        const QueriesLI = new QueriesLineItem(page);

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/settings/commissions-and-data-sync");
        await QueriesLI.clickUserforCommissions("Bob");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.runCommissions();
        await expect(page.getByText("Notify when complete")).toHaveCount(1);
        await QueriesLI.clickSkipandRun();

        await QueriesLI.waitForCalculationMessage();
        await QueriesLI.waitForCommissionsSuccess();

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Bob");

        await QueriesLI.clickPlan("Queries_RBAC");
        await QueriesLI.clickCriteria("Simple_Rule");
        await page.waitForTimeout(3000);

        await QueriesLI.clickQueryBtnLineItem("₹1,750.00");

        const ExtractedAmount = await QueriesLI.validateDesc("5,000");

        expect(ExtractedAmount).toBe(true);

        await QueriesLI.closeQuerypopUpCommission();
        await page.waitForTimeout(3000);

        await QueriesLI.clickQueryBtnLineItem("₹11,200.00");

        const ExtractedAmountNew = await QueriesLI.validateDesc("32,000");
        expect(ExtractedAmountNew).toBe(true);
      }
    );

    test(
      "Check Custom Terminology",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate that the Custom Terminology reflects in the Description of Query Modal",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "The Custom Terminology should be reflected in the Description of Query Modal",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickPlan("Queries_RO_plan");
        await QueriesLI.clickCriteria("SR");

        await QueriesLI.clickQueryBtnLineItem("₹8,970.00");

        // const ExtractedSection = await QueriesLI.getDescription("Section");
        const section = await QueriesLI.validateDesc("Pyt Summary");

        try {
          assert.strictEqual(
            section,
            true,
            "Custom Terminology is applied to the Modal's description"
          );
          console.log(
            "Custom Terminology is applied to the Modal's description"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Check Empty Column check",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate that clicking the Query button on a line item with empty field values functions correctly.",
          },
          {
            type: "Precondition",
            description: "Any field column should be empty",
          },
          {
            type: "Expected Behaviour",
            description: "The Modal should open for Empty valued line item",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickPlan("Queries_RO_plan");
        await QueriesLI.clickCriteria("SR");

        await QueriesLI.clickQueryBtnLineItem("₹0.00");

        // const ExtractedCommision = await QueriesLI.getDescription("Commission");
        const ExtractedCommision = await QueriesLI.validateDesc("₹0.00");

        try {
          assert.strictEqual(
            ExtractedCommision,
            true,
            "The Modal is not opening for the commissions with ₹0.00"
          );
          console.log("The Modal is opening for the commissions with ₹0.00");
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Check for Previously Deferred Commissions",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether clicking the Query button from Previously Deferred Commissions opens the modal and hierarchy is correct",
          },
          { type: "Precondition", description: "Settlement Data" },
          {
            type: "Expected Behaviour",
            description:
              "The Modal should open for Previously Deferred Commissions and the hierarchy should be correct",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Mar 2024");
        await QueriesLI.clickUser("Payee 18");
        await QueriesLI.clickPeriod("Pyt from previously deferred");
        await QueriesLI.clickPlan("Queries_Settlement");
        await QueriesLI.clickCriteria("Simple_Rule");

        await QueriesLI.clickQueryBtnLineItem("₹10,000.00");

        // const ExtractedSection = await QueriesLI.getDescription("Section");
        const ExtractedSection = await QueriesLI.validateDesc(
          "Pyt Summary -> Previous Deferred Commission"
        );
        // const ExtractedCommission = await QueriesLI.getDescription("Commission");
        const ExtractedCommission = await QueriesLI.validateDesc("₹10,000.00");
        // const ExtractedAmount = await QueriesLI.getDescription("amount");
        const ExtractedAmount = await QueriesLI.validateDesc("20,000");

        try {
          assert.strictEqual(
            ExtractedSection,
            true,
            "Breadcrumb order is not correct"
          );
          assert.strictEqual(
            ExtractedCommission,
            true,
            "Commission Value is not matching, Failed"
          );
          assert.strictEqual(
            ExtractedAmount,
            true,
            "Amount Value is not matching, Failed"
          );

          console.log(
            "The fields Section, Amount and Commission are correctly populated in the Line Item level's Query Modal for Previously Deferred Commissions"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Check for Earned Deferred Commissions",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether clicking the Query button from Earned Commissions opens the modal and hierarchy is correct",
          },
          { type: "Precondition", description: "Settlement Data" },
          {
            type: "Expected Behaviour",
            description:
              "The Modal should open for Earned Commissions and the hierarchy should be correct",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        const assert = require("assert");

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Mar 2024");
        await QueriesLI.clickUser("Payee 18");
        await QueriesLI.switchTabsStatements();
        await QueriesLI.clickPeriod("Earned Commissions");
        await QueriesLI.clickPlan("Queries_Settlement");
        await QueriesLI.clickCriteria("Simple_Rule");

        await QueriesLI.clickQueryBtnLineItem("₹9,000.00");

        // const ExtractedSection = await QueriesLI.getDescription("Section");
        const ExtractedSection = await QueriesLI.validateDesc(
          "Commission Summary -> Earned Commission"
        );
        const ExtractedCommission = await QueriesLI.validateDesc("₹9,000.00");
        // const ExtractedCommission = await QueriesLI.getDescription("Commission");
        // const ExtractedAmount = await QueriesLI.getDescription("amount");
        const ExtractedAmount = await QueriesLI.validateDesc("18,000");

        try {
          assert.strictEqual(
            ExtractedSection,
            true,
            "Breadcrumb order is not correct"
          );
          assert.strictEqual(
            ExtractedCommission,
            true,
            "Commission Value is not matching, Failed"
          );
          assert.strictEqual(
            ExtractedAmount,
            true,
            "Amount Value is not matching, Failed"
          );

          console.log(
            "The fields Section, Amount and Commission are correctly populated in the Line Item level's Query Modal for Earned Deferred Commissions"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Disable the 'Allow CC while raising queries' toggle and check in all areas",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether disabling the 'Allow CC while raising queries' toggle in Queries is reflected in the Query Module, Statement level, and Line item level",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "The CC option should not be present in all the places",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);

        await QueriesLI.navigate("/settings/queries");
        await QueriesLI.toggleAllowCC();
        await QueriesLI.waitFortoggleCCSuccess();

        // Queries Module
        await QueriesLI.navigate("/queries/allTickets");
        await QueriesLI.clickRaiseQueryBtnModule();

        const ccbtnQueryModule = await QueriesLI.ccButtonCount();
        expect(ccbtnQueryModule).toBe(0);

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Mar 2024");
        await QueriesLI.clickUser("Payee 18");
        await QueriesLI.clickPeriod("Pyt from current period");
        await QueriesLI.clickPlan("Queries_Overall_Sum");
        await QueriesLI.clickCriteria("Overall_Sum_Criteria");

        await page.waitForTimeout(6000);

        // Line Item Level
        await QueriesLI.clickQueryBtnLineItem("₹5,00,000.00");
        const ccbtnLineItem = await QueriesLI.ccButtonCount();
        expect(ccbtnLineItem).toBe(0);

        await QueriesLI.closeQueryModal();
        await QueriesLI.closeStatements();

        // Statements Level
        await QueriesLI.clickQueriesBtnStatements();
        const ccbtnStatement = await QueriesLI.ccButtonCount();
        expect(ccbtnStatement).toBe(0);

        await QueriesLI.navigate("/settings/queries");
        await QueriesLI.toggleAllowCC();
        await QueriesLI.waitFortoggleCCSuccess();
      }
    );
  }
);

test.describe(
  "Queries - Line items with context - RBAC",
  {
    tag: ["@queries", "@regression", "@statements","@repconnect-1"],
  },
  () => {
    test(
      "VIEW_QUERIES with 'Users can only see their data' RBAC permission",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether Payees/Admins with VIEW_QUERIES and 'Users can only see their data' RBAC permissions can only view the queries they have raised, queries assigned to them, and queries they are CC'd on, and confirm that they cannot raise new queries",
          },
          { type: "Precondition", description: "RBAC" },
          {
            type: "Expected Behaviour",
            description: "The Raise Query Button shouldn't be visible",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        testInfo.setTimeout(testInfo.timeout + 25000);
        const assert = require("assert");

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/settings/user-roles");
        await QueriesLI.selectRole("VIEW_QUERIES_ONLY");
        await QueriesLI.editRBACbtn();
        await QueriesLI.editRBACpermissions("Users can only see their data");
        await QueriesLI.saveRBACchanges();

        await QueriesLI.navigate("/users");
        await QueriesLI.searchUser("<EMAIL>");
        await QueriesLI.Impersonate();

        await QueriesLI.navigate("/queries/allTickets");
        await page.waitForTimeout(5000);

        await expect(await page.getByRole("button",{name : "Raise Query"})).toBeHidden();

        const Qrylist = await QueriesLI.getQueriesList();
        const Checks = [
          "July 2024 Statement - (Users data from their reporting team)",
          "RBAC - 2024 - (Users can see all data)",
        ];
        const allPresent = Checks.every((item) => Qrylist.includes(item));

        try {
          assert.strictEqual(
            allPresent,
            true,
            "Not all expected queries are visible. (VIEW_QUERIES with 'Users can only see their data')"
          );
          console.log(
            "The 'Raise Query' Button is not visible because of 'VIEW_QUERIES' and 'Users can see their Data' RBAC"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }

        await QueriesLI.ExitImpersonation();
      }
    );

    test(
      "VIEW_QUERIES with 'Users can see all data' RBAC permission",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether Payee / Admins with VIEW_QUERIES and 'Users can see all data' RBAC permissions can view all the queries raised and are not able to raise new queries",
          },
          { type: "Precondition", description: "RBAC" },
          {
            type: "Expected Behaviour",
            description: "The Raise Query Button shouldn't be visible",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        testInfo.setTimeout(testInfo.timeout + 25000);
        const assert = require("assert");

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/settings/user-roles");
        await QueriesLI.selectRole("VIEW_QUERIES_ONLY");
        await QueriesLI.editRBACbtn();
        await QueriesLI.editRBACpermissions("Users can see all data");
        await QueriesLI.saveRBACchanges();

        await QueriesLI.navigate("/users");
        await QueriesLI.searchUser("<EMAIL>");
        await QueriesLI.Impersonate();

        await QueriesLI.navigate("/queries/allTickets");
        await page.waitForTimeout(5000);

        await expect(await page.getByRole("button",{name : "Raise Query"})).toBeHidden();

        const Qrylist = await QueriesLI.getQueriesList();
        const Checks = ["RBAC - 2024 - (Users can see all data)"];
        const allPresent = Checks.every((item) => Qrylist.includes(item));

        try {
          assert.strictEqual(
            allPresent,
            true,
            "Not all expected queries are visible. (VIEW_QUERIES with 'Users can see all data')"
          );
          console.log(
            "The 'Raise Query' Button is not visible because of 'VIEW_QUERIES' and 'Users can see all Data' RBAC"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }

        await QueriesLI.ExitImpersonation();
      }
    );

    test(
      "VIEW_QUERIES with 'Users can see their data and their team's data' (data from their reporting team) RBAC permission",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether Payees/Admins with VIEW_QUERIES and 'Users can see their data and their team's data' (data from their reporting team) RBAC permissions can only view the queries raised by the reporting team, the queries assigned to them, and the queries they are CC'd on, and confirm that they cannot raise new queries",
          },
          { type: "Precondition", description: "RBAC" },
          {
            type: "Expected Behaviour",
            description: "The Raise Query Button shouldn't be visible",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        testInfo.setTimeout(testInfo.timeout + 25000);
        const assert = require("assert");

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/settings/user-roles");
        await QueriesLI.selectRole("VIEW_QUERIES_ONLY");
        await QueriesLI.editRBACbtn();
        await QueriesLI.editRBACpermissions("Users can see their data and");
        await QueriesLI.saveRBACchanges();

        await QueriesLI.navigate("/users");
        await QueriesLI.searchUser("<EMAIL>");
        await QueriesLI.Impersonate();

        await QueriesLI.navigate("/queries/allTickets");
        await page.waitForTimeout(5000);

        await expect(await page.getByRole("button",{name : "Raise Query"})).toBeHidden();

        const Qrylist = await QueriesLI.getQueriesList();
        const Checks = [
          "July 2024 Statement - (Users data from their reporting team)",
        ];
        const allPresent = Checks.every((item) => Qrylist.includes(item));

        try {
          assert.strictEqual(
            allPresent,
            true,
            "Not all expected queries are visible. (VIEW_QUERIES with 'Users can see their data and their team's data')"
          );
          console.log(
            "The 'Raise Query' Button is not visible because of 'VIEW_QUERIES' and 'Users can see their data and their team's data' RBAC"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }

        await QueriesLI.ExitImpersonation();
      }
    );

    test(
      "VIEW_QUERIES with 'Users can see their data and their team's data' (Data from specific groups) (with some members without VIEW_QUERIES permission) RBAC permission",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate the Payee / Admins with VIEW_QUERIES and 'Users can see their data and their team's data' (Data from specific groups) (with some members without VIEW_QUERIES permission) RBAC permissions can only view the queries raised by the specific groups, the queries assigned to them, and the queries they are CC'd on",
          },
          { type: "Precondition", description: "RBAC" },
          {
            type: "Expected Behaviour",
            description: "The Raise Query Button shouldn't be  visible",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        testInfo.setTimeout(testInfo.timeout + 25000);
        const assert = require("assert");

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/settings/user-roles");
        await QueriesLI.selectRole("VIEW_QUERIES_ONLY");
        await QueriesLI.editRBACbtn();
        await QueriesLI.editRBACpermissions("Users can see their data and");
        await QueriesLI.editRBACpermissions("Users can only see their data");
        await QueriesLI.editRBACpermissions("Users can see their data and");
        await QueriesLI.SpecificGroupRBAC();
        await QueriesLI.editRBACpermissions("Data from specific groups");
        await QueriesLI.selectSpecificGroup("Queries_Group_View");
        await QueriesLI.saveRBACchanges();

        await QueriesLI.navigate("/users");
        await QueriesLI.searchUser("<EMAIL>");
        await QueriesLI.Impersonate();

        await QueriesLI.navigate("/queries/allTickets");
        await page.waitForTimeout(5000);

        await expect(await page.getByRole("button",{name : "Raise Query"})).toBeHidden();

        const Qrylist = await QueriesLI.getQueriesList();
        const Checks = [
          "July 2024 Statement - (Users can see their data) - VCE",
          "July 2024 Statement - (Users data from their reporting team)",
          "July 2024 - Queries_RBAC - (Users data from their reporting team)",
        ];
        const allPresent = Checks.every((item) => Qrylist.includes(item));

        try {
          assert.strictEqual(
            allPresent,
            true,
            "Not all expected queries are visible. (VIEW_QUERIES with 'Users can see their data and their team's data')"
          );
          console.log(
            "The 'Raise Query' Button is not visible because of 'VIEW_QUERIES' and Users can see their data and their team's data' (Data from specific groups) & (with some members without VIEW_QUERIES permission) RBAC"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }

        await QueriesLI.ExitImpersonation();
      }
    );

    test(
      "VIEW_QUERIES (CREATE_QUERY) with 'Users can only see their data' RBAC permission",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether Payee / Admins with VIEW_QUERIES (CREATE_QUERY) and 'Users can only see their data' RBAC permissions can only view the queries they have raised, queries assigned to them, and queries they are CC'd on the queries raised by them and are also able to raise new queries",
          },
          { type: "Precondition", description: "RBAC" },
          {
            type: "Expected Behaviour",
            description: "The Raise Query Button should be visible",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        testInfo.setTimeout(testInfo.timeout + 40000);
        const assert = require("assert");

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/settings/user-roles");
        await QueriesLI.selectRole("VIEW_AND_CREATE_QUERIES");
        await QueriesLI.editRBACbtn();
        await QueriesLI.editRBACpermissions("Users can only see their data");
        await QueriesLI.saveRBACchanges();

        await QueriesLI.navigate("/users");
        await QueriesLI.searchUser("<EMAIL>");
        await QueriesLI.Impersonate();

        await QueriesLI.navigate("/queries/allTickets");
        await page.waitForTimeout(5000);

        await expect(await page.getByRole("button",{name : "Raise Query"})).toBeVisible();

        const Qrylist = await QueriesLI.getQueriesList();
        const Checks = [
          "July 2024 - Queries_RBAC - (Users can see their data and all data) - VC",
        ];
        const allPresent = Checks.every((item) => Qrylist.includes(item));

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Peter Super Admin");

        await QueriesLI.clickQueriesBtnStatementsIMP();

        const ModalTitle = await QueriesLI.getModalTitle();
        await page.waitForTimeout(3000);

        await QueriesLI.closeQueryModalStatements();
        await QueriesLI.clickPlan("Queries_RO_plan");
        await QueriesLI.clickCriteria("SR");

        await page.waitForTimeout(6000);

        const button = "div.ag-cell button.ant-btn-ghost";
        const buttons = await page.locator(button);
        const countOfButtons = await buttons.count();
        const expectedCount = 6;

        await QueriesLI.closeQueryModalLineItem();

        try {
          assert.strictEqual(
            ModalTitle,
            "Create Query",
            "The modal title is not 'Create Query', expected it to be."
          );
          assert.strictEqual(
            countOfButtons,
            expectedCount,
            `The number of 'Raise Query' buttons is ${countOfButtons}, expected ${expectedCount}.`
          );
          assert.strictEqual(
            allPresent,
            true,
            "Not all expected queries are visible. (VIEW_QUERIES (CREATE_QUERY) with 'Users can only see their data')"
          );
          console.log(
            "The 'Raise Query' Button is visible in the Queries Module because of 'VIEW_QUERIES (CREATE_QUERY)' and 'Users can see their Data' RBAC"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }

        await QueriesLI.ExitImpersonation();
      }
    );

    test(
      "VIEW_QUERIES (CREATE_QUERY) with 'Users can see all data' RBAC permission",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether Payee / Admins with VIEW_QUERIES (CREATE_QUERY) and 'Users can see all data' RBAC permissions can view all the queries raised and  also are able to raise new queries",
          },
          { type: "Precondition", description: "RBAC" },
          {
            type: "Expected Behaviour",
            description: "The Raise Query Button should be visible",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        testInfo.setTimeout(testInfo.timeout + 25000);
        const assert = require("assert");

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/settings/user-roles");
        await QueriesLI.selectRole("VIEW_AND_CREATE_QUERIES");
        await QueriesLI.editRBACbtn();
        await QueriesLI.editRBACpermissions("Users can see all data");
        await QueriesLI.saveRBACchanges();

        await QueriesLI.navigate("/users");
        await QueriesLI.searchUser("<EMAIL>");
        await QueriesLI.Impersonate();

        await QueriesLI.navigate("/queries/allTickets");
        await page.waitForTimeout(6000);

        await expect(await page.getByRole("button",{name : "Raise Query"})).toBeVisible();

        const Qrylist = await QueriesLI.getQueriesList();
        const Checks = ["RBAC - 2024 - (Users can see all data)"];
        const allPresent = Checks.every((item) => Qrylist.includes(item));

        try {
          assert.strictEqual(
            allPresent,
            true,
            "Not all expected queries are visible. (VIEW_QUERIES (CREATE_QUERY) with 'Users can see all data')"
          );
          console.log(
            "The 'Raise Query' Button is visible in the Queries Module because of 'VIEW_QUERIES (CREATE_QUERY)' and 'Users can see all data' RBAC"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }

        await QueriesLI.ExitImpersonation();
      }
    );

    test(
      "VIEW_QUERIES (CREATE_QUERY) with 'Users can see their data and their team's data'(Data from their reporting team) RBAC permission",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether Payee / Admins with VIEW_QUERIES (CREATE_QUERY) and 'Users can see all data' RBAC permissions can view all the queries raised and  also are able to raise new queries",
          },
          { type: "Precondition", description: "RBAC" },
          {
            type: "Expected Behaviour",
            description: "The Raise Query Button should be visible",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        testInfo.setTimeout(testInfo.timeout + 25000);
        const assert = require("assert");

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/settings/user-roles");
        await QueriesLI.selectRole("VIEW_AND_CREATE_QUERIES");
        await QueriesLI.editRBACbtn();
        await QueriesLI.editRBACpermissions("Users can see their data and");
        await QueriesLI.saveRBACchanges();

        await QueriesLI.navigate("/users");
        await QueriesLI.searchUser("<EMAIL>");
        await QueriesLI.Impersonate();

        await QueriesLI.navigate("/queries/allTickets");
        await page.waitForTimeout(6000);

        await expect(await page.getByRole("button",{name : "Raise Query"})).toBeVisible();

        const Qrylist = await QueriesLI.getQueriesList();
        const Checks = [
          "July 2024 - Queries_RBAC - (Users can see their data and all data) - VC",
        ];
        const allPresent = Checks.every((item) => Qrylist.includes(item));

        try {
          assert.strictEqual(
            allPresent,
            true,
            "Not all expected queries are visible. (VIEW_QUERIES (CREATE_QUERY) with 'Users can see their data and their team's data')"
          );
          console.log(
            "The 'Raise Query' Button is visible in the Queries Module because of 'VIEW_QUERIES (CREATE_QUERY)' and 'Users can see their data and their team's data'(Data from their reporting team) RBAC"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }

        await QueriesLI.ExitImpersonation();
      }
    );

    test(
      "VIEW_QUERIES (CREATE_QUERY) with 'Users can see their data and their team's data'(Data from specific groups) (with some members without VIEW_QUERIES permission) RBAC permission",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate the Payee / Admins with VIEW_QUERIES (CREATE_QUERY) and 'Users can see their data and their team's data'(Data from specific groups) (with some members without VIEW_QUERIES permission) RBAC permissions can only view the queries raised by the specific groups, the queries assigned to them, and the queries they are CC'd on",
          },
          { type: "Precondition", description: "RBAC" },
          {
            type: "Expected Behaviour",
            description: "The Raise Query Button should be visible",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        testInfo.setTimeout(testInfo.timeout + 25000);
        const assert = require("assert");

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/settings/user-roles");
        await QueriesLI.selectRole("VIEW_AND_CREATE_QUERIES");
        await QueriesLI.editRBACbtn();
        await QueriesLI.editRBACpermissions("Users can see their data and");
        await QueriesLI.editRBACpermissions("Users can only see their data");
        await QueriesLI.editRBACpermissions("Users can see their data and");
        await QueriesLI.SpecificGroupRBAC();
        await QueriesLI.editRBACpermissions("Data from specific groups");
        await QueriesLI.selectSpecificGroup("Queries_Group_VC");
        await QueriesLI.saveRBACchanges();

        await QueriesLI.navigate("/users");
        await QueriesLI.searchUser("<EMAIL>");
        await QueriesLI.Impersonate();

        await QueriesLI.navigate("/queries/allTickets");
        await page.waitForTimeout(8000);

        await expect(await page.getByRole("button",{name : "Raise Query"})).toBeVisible();

        const Qrylist = await QueriesLI.getQueriesList();
        const Checks = [
          "July 2024 - Queries_RBAC - (Users data from their reporting team)",
          "July 2024 - Queries_RBAC - (Users can see their data and all data) - VC",
        ];
        const allPresent = Checks.every((item) => Qrylist.includes(item));

        try {
          assert.strictEqual(
            allPresent,
            true,
            "Not all expected queries are visible. (VIEW_QUERIES (CREATE_QUERY) with 'Users can see their data and their team's data')"
          );
          console.log(
            "The 'Raise Query' Button is visible in the Queries Module because of 'VIEW_QUERIES (CREATE_QUERY)' and 'Users can see their data and their team's data'(Data from specific groups) RBAC"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }

        await QueriesLI.ExitImpersonation();
      }
    );

    test(
      "VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY) with 'Users can only see their data' RBAC permission",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether Payee / Admins with VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY) and 'Users can only see their data' RBAC permissions can only view the queries they have raised, queries assigned to them, and queries they are CC'd on the queries raised by them and are also able to raise and edit new queries",
          },
          { type: "Precondition", description: "RBAC" },
          {
            type: "Expected Behaviour",
            description: "The Raise Query Button should be visible",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        testInfo.setTimeout(testInfo.timeout + 25000);
        const assert = require("assert");

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/settings/user-roles");
        await QueriesLI.selectRole("VIEW_CREATE_AND_EDIT_QUERIES");
        await QueriesLI.editRBACbtn();
        await QueriesLI.editRBACpermissions("Users can only see their data");
        await QueriesLI.saveRBACchanges();

        await QueriesLI.navigate("/users");
        await QueriesLI.searchUser("<EMAIL>");
        await QueriesLI.Impersonate();

        await QueriesLI.navigate("/queries/allTickets");
        await page.waitForTimeout(7000);

        await expect(await page.getByRole("button",{name : "Raise Query"})).toBeVisible();

        const Qrylist = await QueriesLI.getQueriesList();
        const Checks = [
          "July 2024 Statement - (Users can see their data) - VCE",
        ];
        const allPresent = Checks.every((item) => Qrylist.includes(item));

        await QueriesLI.UpdateDescriptionQueryMod(
          "July 2024 Statement - (Users can see their data) - VCE",
          "This is an updated Description"
        );

        try {
          assert.strictEqual(
            allPresent,
            true,
            "Not all expected queries are visible. (VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY) with 'Users can only see their data')"
          );
          console.log(
            "The 'Raise Query' Button is visible in the Queries Module because of 'VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY)' and 'Users can only see their data' RBAC"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }

        await QueriesLI.ExitImpersonation();
      }
    );

    test(
      "VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY) with 'Users can see all data' RBAC permission",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether Payee / Admins with VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY) and 'Users can see all data' RBAC permissions can view all the queries raised and also are able to raise and edit new queries",
          },
          { type: "Precondition", description: "RBAC" },
          {
            type: "Expected Behaviour",
            description: "The Raise Query Button should be visible",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        testInfo.setTimeout(testInfo.timeout + 25000);
        const assert = require("assert");

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/settings/user-roles");
        await QueriesLI.selectRole("VIEW_CREATE_AND_EDIT_QUERIES");
        await QueriesLI.editRBACbtn();
        await QueriesLI.editRBACpermissions("Users can see all data");
        await QueriesLI.saveRBACchanges();

        await QueriesLI.navigate("/users");
        await QueriesLI.searchUser("<EMAIL>");
        await QueriesLI.Impersonate();

        await QueriesLI.navigate("/queries/allTickets");
        await page.waitForTimeout(6000);

        await expect(await page.getByRole("button",{name : "Raise Query"})).toBeVisible();

        const Qrylist = await QueriesLI.getQueriesList();
        const Checks = ["RBAC - 2024 - (Users can see all data)"];
        const allPresent = Checks.every((item) => Qrylist.includes(item));

        try {
          assert.strictEqual(
            allPresent,
            true,
            "Not all expected queries are visible. (VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY) with 'Users can see all data')"
          );
          console.log(
            "The 'Raise Query' Button is visible in the Queries Module because of 'VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY)' and 'Users can see all data' RBAC"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }

        await QueriesLI.ExitImpersonation();
      }
    );

    test(
      "VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY) with 'Users can see their data and their team's data' (Data from their reporting team) RBAC permission",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether Payee / Admins with VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY) and 'Users can see their data and their team's data' (Data from their reporting team) RBAC permissions can only view the queries raised by the reporting team, the queries assigned to them, and the queries they are CC'd on and are also able to raise and edit new queries",
          },
          { type: "Precondition", description: "RBAC" },
          {
            type: "Expected Behaviour",
            description: "The Raise Query Button should be visible",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        testInfo.setTimeout(testInfo.timeout + 25000);
        const assert = require("assert");

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/settings/user-roles");
        await QueriesLI.selectRole("VIEW_CREATE_AND_EDIT_QUERIES");
        await QueriesLI.editRBACbtn();
        await QueriesLI.editRBACpermissions("Users can see their data and");
        await QueriesLI.saveRBACchanges();

        await QueriesLI.navigate("/users");
        await QueriesLI.searchUser("<EMAIL>");
        await QueriesLI.Impersonate();

        await QueriesLI.navigate("/queries/allTickets");
        await page.waitForTimeout(5000);

        await expect(await page.getByRole("button",{name : "Raise Query"})).toBeVisible();

        const Qrylist = await QueriesLI.getQueriesList();
        const Checks = [
          "July 2024 Statement - (Users can see their data) - VCE",
        ];
        const allPresent = Checks.every((item) => Qrylist.includes(item));

        try {
          assert.strictEqual(
            allPresent,
            true,
            "Not all expected queries are visible. (VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY) with 'Users can see their data and their team's data')"
          );
          console.log(
            "The 'Raise Query' Button is visible in the Queries Module because of 'VEIW_QUERIES (CREATE_QUERY and EDIT_QUERY)' and 'Users can see their data and their team's data' (Data from their reporting team) RBAC"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }

        await QueriesLI.ExitImpersonation();
      }
    );

    test(
      "VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY) with 'Users can see their data and their team's data' (Data from specific groups) (with some members without VIEW_QUERIES permission) RBAC permission",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate the Payee / Admins with VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY) and 'Users can see their data and their team's data' (Data from specific groups) (with some members without VIEW_QUERIES permission) RBAC permissions can only view the queries raised by the specific groups, the queries assigned to them, and the queries they are CC'd on",
          },
          { type: "Precondition", description: "RBAC" },
          {
            type: "Expected Behaviour",
            description: "The Raise Query Button should be visible",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);
        testInfo.setTimeout(testInfo.timeout + 25000);
        const assert = require("assert");

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/settings/user-roles");
        await QueriesLI.selectRole("VIEW_CREATE_AND_EDIT_QUERIES");
        await QueriesLI.editRBACbtn();
        await QueriesLI.editRBACpermissions("Users can see their data and");
        await QueriesLI.editRBACpermissions("Users can only see their data");
        await QueriesLI.editRBACpermissions("Users can see their data and");
        await QueriesLI.SpecificGroupRBAC();
        await QueriesLI.editRBACpermissions("Data from specific groups");
        await QueriesLI.selectSpecificGroup("Queries_Group_VCE");
        await QueriesLI.saveRBACchanges();

        await QueriesLI.navigate("/users");
        await QueriesLI.searchUser("<EMAIL>");
        await QueriesLI.Impersonate();

        await QueriesLI.navigate("/queries/allTickets");
        await page.waitForTimeout(5000);

        await expect(await page.getByRole("button",{name : "Raise Query"})).toBeVisible();

        const Qrylist = await QueriesLI.getQueriesList();
        const Checks = [
          "July 2024 Statement - (Users can see their data) - VCE",
          "July 2024 Statement - (Users data from their reporting team)",
          "July 2024 - Queries_RBAC - (Users data from their reporting team)",
        ];
        const allPresent = Checks.every((item) => Qrylist.includes(item));

        try {
          assert.strictEqual(
            allPresent,
            true,
            "Not all expected queries are visible. (VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY) with 'Users can see their data and their team's data')"
          );
          console.log(
            "The 'Raise Query' Button is visible in the Queries Module because of 'VIEW_QUERIES (CREATE_QUERY and EDIT_QUERY)' and 'Users can see their data and their team's data' (Data from specific groups) (with some members without VIEW_QUERIES permission) RBAC"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }

        await QueriesLI.ExitImpersonation();
      }
    );

    test(
      "Check for Query modal dropdown for Overwriting Managers whether the Payee can assign the query only to the current / past managers or the Admins",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8982" },
          {
            type: "Description",
            description:
              "Validate whether the Payee can assign the query only to the current / past managers or the Admins (Check in Statement level modal and line-item level modal). Also check the assignee with respect to Overwriting of Managers / Exited manager",
          },
          {
            type: "Precondition",
            description: "Current / Past Managers Details",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Queries should be listed based on the changed managers",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const QueriesLI = new QueriesLineItem(page);

        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.navigate("/users");
        await QueriesLI.ExitImpersonationCheck();
        await QueriesLI.searchUser("<EMAIL>");
        await QueriesLI.Impersonate();

        await QueriesLI.navigate("/commissions");
        await QueriesLI.selectDate("31 Jul 2024");
        await QueriesLI.clickUser("Peter Super Admin");
        await QueriesLI.clickQueriesBtnStatementsIMP();

        await QueriesLI.clickAssignedTo();

        const AssignedToList = await QueriesLI.getAssignedToList();
        expect(AssignedToList[0]).toBe("Boss Payee 10");

        await QueriesLI.closeQueryModalStatements();

        await QueriesLI.ExitImpersonation();
      }
    );
  }
);
