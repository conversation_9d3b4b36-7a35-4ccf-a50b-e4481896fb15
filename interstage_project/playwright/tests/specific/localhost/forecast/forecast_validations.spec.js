/* eslint-disable playwright/no-wait-for-timeout */
import CommissionsSyncPrevPeriod from "../../../../test-objects/commissionSyncPrevPeriod-objects";
import CommissionSync from "../../../../test-objects/commissionSync-objects";

const {
  forecastFixtures: { test, expect },
} = require("../../../fixtures");

test.describe(
  "Forecast plans and forecast sync validations ",
  { tag: ["@forecast", "@regression", "@primelogic-4"] },
  () => {
    test("Forecast_clone_from_plan", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/plans", { waitUntil: "networkidle" });
      await page.locator("div.ant-select-selector").click();
      await page.getByRole("listitem").getByText("2023").click();
      const placeholder = await page.getByPlaceholder("Search by plan name");
      await placeholder.waitFor({ state: "visible", timeout: 10000 });
      await page.waitForTimeout(3000);
      await page.getByPlaceholder("Search by plan name").fill("Config-plan");
      await page.getByTestId("pt-actions-Config-plan").click();
      await page.getByRole("menuitem", { name: "Add to Forecast" }).click();
      const page1Promise = page.waitForEvent("popup");
      await page.getByRole("link", { name: "Take me there" }).click();
      const page1 = await page1Promise;
      await page1
        .locator("div")
        .filter({ hasText: /^Compensation Forecasting$/ })
        .nth(1)
        .waitFor({ state: "visible", timeout: 20000 });
      await page1.waitForTimeout(3000);
      await page1.locator("div.ant-select-selector").click();
      await page1.getByRole("listitem").getByText("2023").click();
      await page1
        .getByPlaceholder("Search by plan name")
        .fill("Config-plan_Copy");
      // check whether the config values is also gets cloned
      await page1.getByText("Config-plan_Copy", { exact: true }).click();
      await page1
        .locator("span")
        .filter({ hasText: "config-simple" })
        .first()
        .click();
      await expect(
        page1.locator(".anticon > .text-ever-success > path")
      ).toHaveCount(1);
      await page1.getByRole("button", { name: "Dynamic fields" }).click();
      await expect(
        page1
          .getByLabel(
            "Dynamic fieldsAssign distinct field values for each sales rep when calculating commissions."
          )
          .getByText("con-amount")
      ).toHaveCount(1);
      await page1.getByRole("button", { name: "Cancel" }).click();
      await page1.getByText("Planupload.pdf").click();
      await expect(
        page1.getByLabel("Planupload.pdf").getByText("Planupload.pdf")
      ).toHaveCount(1);
      await page1.getByRole("button", { name: "Cancel" }).click();
      await page1.getByRole("button", { name: "Exit Canvas" }).click();
      await page1.close();
      await expect(page.getByText("Compensation Plans")).toHaveCount(1);
    });

    test("settlement_alert", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/plans", { waitUntil: "networkidle" });
      await page.locator("div.ant-select-selector").click();
      await page.getByRole("listitem").getByText("2023").click();
      const placeholder1 = await page.getByPlaceholder("Search by plan name");
      await placeholder1.waitFor({ state: "visible", timeout: 10000 });
      await page.getByPlaceholder("Search by plan name").fill("Collection");
      await page.getByTestId("pt-actions-collection-plan").click();
      await page.getByText("Add to Forecast").click();
      const page1Promise = page.waitForEvent("popup");
      await page.getByRole("link", { name: "Take me there" }).click();
      const page1 = await page1Promise;
      await page1
        .locator("div")
        .filter({ hasText: /^Compensation Forecasting$/ })
        .nth(1)
        .waitFor({ state: "visible", timeout: 20000 });
      await page1.locator("div.ant-select-selector").click();
      await page1.getByRole("listitem").getByText("2023").click();
      const placeholder = await page1.getByPlaceholder("Search by plan name");
      await placeholder.waitFor({ state: "visible", timeout: 10000 });
      await page1
        .getByPlaceholder("Search by plan name")
        .fill("collection-plan_Copy");
      await page1.getByText("collection-plan_Copy", { exact: true }).click();
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await page1.waitForTimeout(2000);
      const ele = await page1.locator("div[draggable='true']").count();
      expect(ele).toBe(1);
      await page1.getByRole("button", { name: "Exit Canvas" }).click();
      await page1.close();
      await expect(page.getByText("Compensation Plans")).toHaveCount(1);
    });

    test("forecast_clone & delete", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/forecasts", { waitUntil: "networkidle" });
      await page.locator("div.ant-select-selector").click();
      await page.getByRole("listitem").getByText("2023").click();
      const placeholder = await page.getByPlaceholder("Search by plan name");
      await placeholder.waitFor({ state: "visible", timeout: 10000 });
      await page
        .getByPlaceholder("Search by plan name")
        .fill("Forecast-enrichment");
      await page.getByTestId("pt-actions-Forecast-enrichment").click();
      await page
        .getByRole("menuitem", { name: "Clone Forecast" })
        .locator("div")
        .click();
      await expect(
        page
          .getByTestId("login-indicator")
          .getByText("Forecast-enrichment_Copy")
      ).toHaveCount(1);
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await page
        .getByPlaceholder("Search by plan name")
        .fill("Forecast-enrichment_Copy");
      await page.waitForTimeout(1500);
      await page.getByTestId("pt-actions-Forecast-enrichment_Copy").click();
      await page.getByText("Delete").click();
      await expect(
        page.getByText(
          'Are you sure you want to delete "Forecast-enrichment_Copy" compensation plan?'
        )
      ).toHaveCount(1);
      await page.getByRole("button", { name: "Yes, delete" }).click();
      await expect(
        page.getByText("Forecast-enrichment_Copy deleted successfully!")
      ).toHaveCount(1);
    });

    test("no_settlement_component", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/plans", { waitUntil: "networkidle" });
      await page.locator("div.ant-select-selector").click();
      await page.getByRole("listitem").getByText("2023").click();
      const placeholder = await page.getByPlaceholder("Search by plan name");
      await placeholder.waitFor({ state: "visible", timeout: 10000 });
      await page.getByText("Collection-plan").click();
      await expect(page.getByText("Settlement end date :")).toHaveCount(1);
      await page.goto("/forecasts", { waitUntil: "networkidle" });
      await page.getByText("Simple-plan", { exact: true }).click();
      await expect(page.getByText("Settlement end date :")).toHaveCount(0);
      await page.getByRole("button", { name: "Exit Canvas" }).click();
    });

    test("hide component", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/plans", { waitUntil: "networkidle" });
      await page.locator("div.ant-select-selector").click();
      await page.getByRole("listitem").getByText("2023").click();
      const placeholder = await page.getByPlaceholder("Search by plan name");
      await placeholder.waitFor({ state: "visible", timeout: 10000 });
      await page.getByText("Simple-plan", { exact: true }).click();
      // await page.locator("span").filter({ hasText: "Sample-playwright" }).click();
      await page
        .locator("span")
        .filter({ hasText: "Sample-playwright" })
        .first()
        .click();
      await page.getByRole("button", { name: "Configuration" }).click();
      await expect(page.getByText("Hide this component")).toHaveCount(1);
      await page.goto("/forecasts", { waitUntil: "networkidle" });
      await page.getByText("Simple-plan", { exact: true }).click();
      await page
        .locator("span")
        .filter({ hasText: "Sample-playwright" })
        .first()
        .click();
      await page.getByRole("button", { name: "Configuration" }).click();
      await expect(page.getByText("Hide this component")).toHaveCount(0);
      await expect(page.getByText("Show commission trace")).toHaveCount(0);
      await page.getByRole("button", { name: "Exit Canvas" }).click();
    });

    test("Export do nothing records", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/forecasts", { waitUntil: "networkidle" });
      await page.locator("div.ant-select-selector").click();
      await page.getByRole("listitem").getByText("2023").click();
      const placeholder = await page.getByPlaceholder("Search by plan name");
      await placeholder.waitFor({ state: "visible", timeout: 10000 });
      await page.getByText("Simple-plan_Copy", { exact: true }).click();
      await page
        .locator("span")
        .filter({ hasText: "conditional-playwright" })
        .first()
        .click();
      await page.getByRole("button", { name: "Simulate" }).click();
      await page.getByPlaceholder("Start date").click();
      await page.getByRole("button", { name: "Dec" }).click();
      await page.getByText("Jul").click();
      await page
        .getByRole("row", { name: "25 26 27 28 29 30 1" })
        .getByText("1")
        .click();
      await page.getByPlaceholder("End date").click();
      await page.getByRole("button", { name: "Dec" }).click();
      await page.getByText("Jul").click();
      await page
        .locator("tr:nth-child(6) > td:nth-child(2) > .ant-picker-cell-inner")
        .first()
        .click();
      await page.getByRole("button", { name: "Run" }).click();
      await page.waitForTimeout(2000);
      await page.locator(".mt-4 > div:nth-child(2) > button").first().click();
      await page.getByLabel("Select all").first().check();
      await page.getByLabel("Select all").nth(1).check();
      await page.waitForTimeout(1000);
      await page
        .getByLabel(
          "Simulate columnsChoose the columns you want to display in the Simulate table view"
        )
        .getByRole("button", { name: "Apply" })
        .click();
      // await expect(
      //   page.getByText("Simulate columns updated successfully")
      // ).toHaveCount(1);
      await page.getByRole("button", { name: "Export" }).click();
      await expect(
        page.getByText(
          "Exported simulated data successfully for harry crick in conditional-playwright c"
        )
      ).toHaveCount(1);

      //check whether the localisation is applied
      await expect(
        page.getByRole("treegrid").getByText("Compensation")
      ).toHaveCount(1);
      await page
        .locator("div")
        .filter({ hasText: /^Include 'Do nothing' recordsExport$/ })
        .getByRole("button")
        .first()
        .click();
      await expect(page.getByText("demo bool").nth(3)).toHaveCount(1);
      await page
        .locator("label")
        .filter({ hasText: "demo bool" })
        .locator("span")
        .first()
        .click();
      await expect(page.getByText("demo bool").nth(3)).toHaveCount(0);
      await page
        .getByLabel(
          "Simulate columnsChoose the columns you want to display in the Simulate table view"
        )
        .getByRole("button", { name: "Apply" })
        .click();
    });

    test("view uploaded doc", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/forecasts", { waitUntil: "networkidle" });
      await page.locator("div.ant-select-selector").click();
      await page.getByRole("listitem").getByText("2023").click();
      const placeholder = await page.getByPlaceholder("Search by plan name");
      await placeholder.waitFor({ state: "visible", timeout: 10000 });
      await page.getByText("Simple-plan", { exact: true }).click();
      await page.getByText("Forecast.pdf").click();
      await expect(
        page.getByLabel("Forecast.pdf").getByText("Forecast.pdf")
      ).toHaveCount(1);
      await page.getByRole("button", { name: "Cancel" }).click();
      await page.getByRole("button", { name: "Exit Canvas" }).click();
    });

    test("forecast_commission_validation", async ({ adminPage }, testInfo) => {
      testInfo.setTimeout(testInfo.timeout + 900000);

      const page = adminPage.page;
      await page.goto("/forecasts", { waitUntil: "networkidle" });
      await page.waitForTimeout(2000);
      const commSyncPage = new CommissionSync(page);
      const csPrev = new CommissionsSyncPrevPeriod(page);

      await csPrev.navigate("/settings/commissions-and-data-sync");

      // sync commission for the month of july
      // Its a check for validating whether other Sync tabs are closed or not
      await page
        .getByRole("button", {
          name: "Calculate Commissions Run",
        })
        .click();

      await page
        .getByRole("button", { name: "Compensation Forecasting Run" })
        .click();
      await commSyncPage.selectCriteria("payeee-in-plan");
      await commSyncPage.selectDropdown(["Forecast-enrichment"]);
      await page.getByRole("textbox", { name: "Select month" }).click();
      await page.locator("button").nth(1).click();
      await page.locator("button").nth(1).click();
      await page.getByText("Jul").click();
      await page
        .getByLabel("Refresh databooks & then calculate compensation forecast")
        .check();
      await csPrev.runCommissions();
      await csPrev.clickSkipandRun();
      await expect(page.getByText("Preparing to start...")).toBeVisible();
      await page
        .getByText("Calculating Commissions forecast...")
        .waitFor({ state: "visible", timeout: 60000 });
      await page
        .getByText("Compensation Forecast Calculations Completed")
        .waitFor({ state: "visible", timeout: 300000 });
    });
  }
);
