import CommonUtils from "../../../../test-objects/common-utils-objects.js";
import CommissionSync from "../../../../test-objects/commissionSync-objects.js";
import CommissionSycnPrevPeriod from "../../../../test-objects/commissionSyncPrevPeriod-objects.js";
import { bearerTokenGenerator } from "../../../bearerToken.js";
const {
  playwrightCommPlanForecastFixtures: { test, expect },
  playwrightCommPlanForecastFixtures2: { test: test2, expect: expect2 },
  playwrightCommPlanForecastFixtures3: { test: test3, expect: expect3 },
  playwrightCommPlanForecastFixturesSa: { test: testSa, expect: expectSa },
} = require("../../../fixtures.js");
const requestBodies = require("./forecastApi.js").default;
const creds = require("..//apiCreds.js");

const fs = require("fs");
const path = require("path");
const csv = require("csv-parser");
let token = "";

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  if (await page.getByText("Logged in as").isVisible({ timeout: 10000 })) {
    try {
      await page.getByRole("button", { name: "Exit" }).click();
      await page.waitForTimeout(5000);
    } catch {
      console.log(
        "in before each, unable to click on Exit button for Logged in user"
      );
    }
  }
  await page.goto("/forecasts", { waitUntil: "networkidle" });
});

test.describe(
  "Forecast Tests",
  { tag: ["@forecast", "@regression", "@primelogic-4"] },
  () => {
    test("Test to check for disabled publish button", async ({ adminPage }) => {
      const page = adminPage.page;
      // Plan without criteria - disable check
      await page.getByText("Plan with no components").click();
      const publishButton = page
        .getByRole("button", { name: "Publish" })
        .nth(1);
      await expect(publishButton).toBeDisabled();
      await page.getByRole("button", { name: "Exit Canvas" }).click();

      // Plan without payees - disable check
      await page.getByText("Plan with no payees").click();
      await page.getByText("Add payees to this plan");
      await expect(publishButton).toBeDisabled();
      await page.getByRole("button", { name: "Exit Canvas" }).click();

      // Plan with unsaved changes - not allow publish check
      await page.getByText("Plan 8", { exact: true }).click();
      await page.getByText("Simple").first().click();
      await page.waitForTimeout(2000);
      await page.getByTestId("expression-input-box").click();
      await page.getByRole("textbox").nth(4).fill("*");
      await page.getByTestId("expression-input-box").click();
      await page.getByText("*").click();
      await expect(publishButton).toBeDisabled();
      await page
        .getByTestId("expression-input-box")
        .getByRole("textbox")
        .nth(4)
        .fill("5");
      await page.getByText("5Integer").click();
      await page.getByRole("button", { name: "Publish", exact: true }).click();
      await page
        .getByRole("dialog")
        .getByRole("button", { name: "Publish" })
        .click();
      await expect(page.getByText("Publish could not be done")).toBeVisible();
      await expect(
        page.getByText("You have pending unsaved changes in Components").first()
      ).toBeVisible();
      await page.getByLabel("Close").nth(2).click();
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await page
        .getByRole("button", { name: "Exit", exact: true })
        .nth(-1)
        .click();
    });

    test("Test to check filters for users in manage payees section", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      // Reporting manager filter
      await page.getByText("Quarterly Plan").click();
      await page
        .locator(".h-\\[calc\\(100\\%-57px\\)\\] > div > button")
        .click();
      await page.locator(".ant-select-selection-overflow").first().click();
      await page.getByText("User 10 Test 10").nth(1).click();
      await page.getByRole("button", { name: "Apply filter" }).click();
      await expect(page.getByText("Filtered Payees: 1")).toBeVisible();
      await expect(page.getByText("User 11 Test 11")).toBeVisible();
      await page.getByRole("button", { name: "Exit Canvas" }).click();

      // Other filters for payees
      await page.getByText("Plan 5", { exact: true }).click();
      await page
        .locator(".h-\\[calc\\(100\\%-57px\\)\\] > div > button")
        .click();
      await page
        .locator(
          "div:nth-child(2) > .relative > .ant-select > .ant-select-selector > .ant-select-selection-overflow"
        )
        .click();
      await page.getByText("Group 1").click();
      await page.getByText("All Payees", { exact: true }).click();
      await page.getByText("Group 1All Payees").click();
      await page
        .locator("div")
        .filter({ hasText: /^Select date function$/ })
        .nth(3)
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^After$/ })
        .nth(1)
        .click();
      await page.getByPlaceholder("Select date").click();
      await page.getByPlaceholder("Select date").fill("Apr 01, 2023");
      await page.getByPlaceholder("Select date").press("Enter");
      await page
        .locator(
          "div:nth-child(4) > .relative > .ant-select > .ant-select-selector > .ant-select-selection-overflow"
        )
        .click();
      await page
        .locator("span")
        .filter({ hasText: /^Payee$/ })
        .click();
      await page
        .locator("span")
        .filter({ hasText: /^Manager$/ })
        .click();
      await page.locator("label").filter({ hasText: "Designation" }).click();
      await page.getByRole("button", { name: "Add more filters" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select field$/ })
        .nth(3)
        .click();
      await page.getByText("Employment Country").click();
      await page
        .locator("div")
        .filter({ hasText: /^Select operator$/ })
        .nth(3)
        .click();
      await page.getByText("In", { exact: true }).click();
      await page
        .locator(
          "div:nth-child(3) > .relative > .ant-select > .ant-select-selector"
        )
        .click();
      await page.getByText("India").click();
      await page.getByText("United States Of America").click();
      await page.getByText("Canada").click();
      await page.getByText("France").click();
      await page.getByText("IndiaUnited States Of AmericaCanadaFrance").click();
      await page.getByRole("button", { name: "Add more filters" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select field$/ })
        .nth(3)
        .click();
      await page.getByText("Payee Currency").nth(1).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select operator$/ })
        .nth(3)
        .click();
      await page.getByText("In", { exact: true }).nth(2).click();
      await page
        .locator(
          "div:nth-child(2) > .ant-row > div:nth-child(3) > .relative > .ant-select > .ant-select-selector"
        )
        .click();
      await page.locator("span").filter({ hasText: "INR" }).click();
      await page.locator("span").filter({ hasText: "USD" }).click();
      await page.locator("span").filter({ hasText: "CAD" }).click();
      await page.locator("span").filter({ hasText: "EUR" }).click();
      await page.locator("label").filter({ hasText: "Designation" }).click();
      await page.getByRole("button", { name: "Apply filter" }).click();
      await expect(page.getByText("Filtered Payees: 5")).toBeVisible();
    });

    test("Test to check bulk operations for user updates in manage payees", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      // Bulk add users to plan
      await page.getByPlaceholder("Search by plan name").click();
      await page.getByPlaceholder("Search by plan name").fill("Plan 7");
      await page.getByText("Plan 7").click();
      await page.getByLabel("U3User 3 Test 3").check();
      await page.getByLabel("U4User 4 Test 4").check();
      await page.getByLabel("U5User 5 Test 5").check();
      await page.getByLabel("U6User 6 Test 6").check();
      await page.getByLabel("U7User 7 Test 7").check();
      await expect(page.getByText("5payee selected")).toBeVisible();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await page.getByTitle("Hide panel").locator("svg").click();

      // Bulk update plan period
      // await page.locator('input[name="row-0"]').check();
      // await page.locator('input[name="row-1"]').check();
      // await page.locator('input[name="row-2"]').check();
      // await page.locator('input[name="row-3"]').check();
      await page.getByTestId("User 3 Test 3").click();
      await page.getByTestId("User 4 Test 4").click();
      await page.getByTestId("User 5 Test 5").click();
      await page.getByTestId("User 6 Test 6").click();
      await page.getByText("Bulk actions").click();
      await page.getByRole("menuitem", { name: "Update plan period" }).click();
      await page.getByPlaceholder("Start date").click();
      await page.getByPlaceholder("Start date").fill("May 22, 2024");
      await page.getByPlaceholder("Start date").press("Enter");
      await page.getByTitle("-06-30").locator("div").click();
      await page.getByRole("button", { name: "Update" }).click();
      await expect(page.getByText("Limited period4")).toHaveCount(1);

      // Bulk remove users from plan
      await page.locator(".w-max > .w-4 > path").click();
      await page.locator('input[name="row-3"]').check();
      await page.locator('input[name="row-4"]').check();
      await page.getByText("Bulk actions").click();
      await page.getByRole("menuitem", { name: "Remove payees" }).click();
      await expect(page.getByText("Are you sure to remove 2")).toBeVisible();
      await page.getByRole("button", { name: "Remove" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        page.getByText("Successfully saved 5 payees").first()
      ).toBeVisible();

      // Download csv
      await page.locator('input[name="row-0"]').check();
      await page.locator('input[name="row-1"]').check();
      await page.locator('input[name="row-2"]').check();
      await page.getByText("Bulk actions").click();
      const downloadPromise = page.waitForEvent("download");
      await page.getByRole("menuitem", { name: "Download as CSV" }).click();
      const download = await downloadPromise;
      await expect(
        page.getByText("Successfully Exported").first()
      ).toBeVisible();
    });

    test("Manage payee - payee period tests", async ({ adminPage }) => {
      const page = adminPage.page;

      await page.getByPlaceholder("Search by plan name").click();
      await page.getByPlaceholder("Search by plan name").fill("Plan 9");
      await page.getByText("Plan 9").click();
      await page.getByLabel("U1User 1 Test 1").check();
      await page.getByLabel("U2User 2 Test 2").check();
      await page.getByLabel("U3User 3 Test 3").check();
      await page.getByLabel("U4User 4 Test 4").check();
      await page.getByRole("button", { name: "Add Payees" }).click();

      // Update payee plan start date less than plan start date - Its restricted and no longer allowed so deleted

      // Update payee plan start date lesser than joining date of the payee
      await page.getByRole("button", { name: "Edit" }).click();
      await page.getByPlaceholder("End date").click();
      await page.getByPlaceholder("End date").fill("Jun 30, 2024");
      await page.getByPlaceholder("End date").press("Enter");
      await page.getByRole("button", { name: "Apply" }).click();
      await page.getByTestId("pt-end date").click();
      await page.getByRole("button", { name: "Done" }).click();
      await page.getByLabel("U1User 14 Test 14").check();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await page.getByTitle("Hide panel").locator("svg").click();
      await page.getByPlaceholder("Search payees").click();
      await page.getByPlaceholder("Search payees").fill("User 14 Test 14");
      await page.waitForTimeout(2000);
      await expect(
        page
          .getByText(
            "Effective start date should be greater than oldest payroll date: 02-Jun-"
          )
          .first()
      ).toBeVisible();
      await expect(
        page
          .getByRole("treegrid")
          .getByText(
            "Effective start date should be greater than joining date: 02-Jun-"
          )
      ).toBeVisible();
      await page
        .getByRole("gridcell", { name: "Apr 01, 2024 - Jun 30, 2024" })
        .locator("svg")
        .click();
      await page.getByPlaceholder("Start date").click();
      await page.getByPlaceholder("Start date").fill("Jun 05, 2024");
      await page.getByPlaceholder("Start date").press("Enter");
      await expect(page.getByText("Jun 05, 2024 - Jun 30, 2024")).toHaveCount(
        1
      );
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await expect(
        page.getByText("Review unsaved payee updates").first()
      ).toBeVisible();
      await page
        .getByRole("button", { name: "Exit", exact: true })
        .last()
        .click();

      // Update plan start date and end date for all payees
      // Plan start date before actually set plan start date
      await page.getByText("Plan 9").click();
      await page.getByLabel("U1User 1 Test 1").check();
      await page.getByLabel("U2User 2 Test 2").check();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await page.getByRole("button", { name: "Edit" }).click();
      await page.getByPlaceholder("Start date").click();
      await page.getByPlaceholder("Start date").fill("Jan 01, 2024");
      await page.getByPlaceholder("Start date").press("Enter");
      await page.getByPlaceholder("End date").fill("Feb 28, 2025");
      await page.getByPlaceholder("End date").press("Enter");
      await page.getByRole("button", { name: "Apply" }).click();
      await page.getByTestId("pt-start date").click();
      await page.getByTestId("pt-end date").click();
      await page.getByRole("button", { name: "Done" }).click();
      await expect(page.getByText("Jan 01, 2024 - Feb 28, 2025")).toHaveCount(
        4
      );
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await expect(
        page.getByText("Review unsaved payee updates").first()
      ).toBeVisible();
      await page
        .getByRole("button", { name: "Exit", exact: true })
        .last()
        .click();

      // Update plan start date and end date for all payees
      // Plan start date after actually set plan start date
      await page.getByText("Plan 9").click();
      await page.getByLabel("U1User 1 Test 1").check();
      await page.getByLabel("U2User 2 Test 2").check();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await page.getByRole("button", { name: "Edit" }).click();
      await page.getByPlaceholder("Start date").click();
      await page.getByPlaceholder("Start date").fill("Apr 21, 2024");
      await page.getByPlaceholder("Start date").press("Enter");
      await page.getByPlaceholder("End date").fill("May 28, 2024");
      await page.getByPlaceholder("End date").press("Enter");
      await page.getByRole("button", { name: "Apply" }).click();
      await page.getByTestId("pt-end date").click();
      await page.getByRole("button", { name: "Done" }).click();
      await expect(page.getByText("Apr 21, 2024 - May 28, 2024")).toHaveCount(
        4
      );
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await expect(
        page.getByText("Review unsaved payee updates").first()
      ).toBeVisible();
      await page
        .getByRole("button", { name: "Exit", exact: true })
        .last()
        .click();
    });

    // 5 different sorting test cases
    const sortingTestCases = [
      {
        sort: "asc",
        expectedResult: [
          {
            planId: "b64e6231-f805-4470-9302-02b7c19a6f9d",
            planName: "Plan 1",
            isDraft: true,
            planStartDate: "2023-01-01T00:00:00+00:00",
            planEndDate: "2023-12-31T23:59:59.999999+00:00",
            planType: "MAIN",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 1",
                  lastName: "Test 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
          {
            planId: "458bb1b2-b2eb-4a19-8ae3-7c1cc1372c4b",
            planName: "Plan 2",
            isDraft: true,
            planStartDate: "2023-07-01T00:00:00+00:00",
            planEndDate: "2023-09-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 1",
                  lastName: "Test 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 2",
                  lastName: "Test 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 3",
                  lastName: "Test 3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "edbf071d-0d63-43e9-bedc-2ba21652253c",
            planName: "Plan 3",
            isDraft: true,
            planStartDate: "2023-08-01T00:00:00+00:00",
            planEndDate: "2023-12-31T23:59:59.999999+00:00",
            planType: "MAIN",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 10",
                  lastName: "Test 10",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 11",
                  lastName: "Test 11",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "20e27d49-6e6f-42d1-b2a3-d1eced19c2d6",
            planName: "Plan 4",
            isDraft: true,
            planStartDate: "2023-03-01T00:00:00+00:00",
            planEndDate: "2023-05-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 8",
                  lastName: "Test 8",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
        ],
      },
      {
        sort: "desc",
        expectedResult: [
          {
            planId: "20e27d49-6e6f-42d1-b2a3-d1eced19c2d6",
            planName: "Plan 4",
            isDraft: true,
            planStartDate: "2023-03-01T00:00:00+00:00",
            planEndDate: "2023-05-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 8",
                  lastName: "Test 8",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
          {
            planId: "edbf071d-0d63-43e9-bedc-2ba21652253c",
            planName: "Plan 3",
            isDraft: true,
            planStartDate: "2023-08-01T00:00:00+00:00",
            planEndDate: "2023-12-31T23:59:59.999999+00:00",
            planType: "MAIN",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 10",
                  lastName: "Test 10",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 11",
                  lastName: "Test 11",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "458bb1b2-b2eb-4a19-8ae3-7c1cc1372c4b",
            planName: "Plan 2",
            isDraft: true,
            planStartDate: "2023-07-01T00:00:00+00:00",
            planEndDate: "2023-09-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 1",
                  lastName: "Test 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 2",
                  lastName: "Test 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 3",
                  lastName: "Test 3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "b64e6231-f805-4470-9302-02b7c19a6f9d",
            planName: "Plan 1",
            isDraft: true,
            planStartDate: "2023-01-01T00:00:00+00:00",
            planEndDate: "2023-12-31T23:59:59.999999+00:00",
            planType: "MAIN",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 1",
                  lastName: "Test 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
        ],
      },
      {
        sort: "modified_date",
        expectedResult: [
          {
            planId: "20e27d49-6e6f-42d1-b2a3-d1eced19c2d6",
            planName: "Plan 4",
            isDraft: true,
            planStartDate: "2023-03-01T00:00:00+00:00",
            planEndDate: "2023-05-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 8",
                  lastName: "Test 8",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
          {
            planId: "edbf071d-0d63-43e9-bedc-2ba21652253c",
            planName: "Plan 3",
            isDraft: true,
            planStartDate: "2023-08-01T00:00:00+00:00",
            planEndDate: "2023-12-31T23:59:59.999999+00:00",
            planType: "MAIN",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 10",
                  lastName: "Test 10",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 11",
                  lastName: "Test 11",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "458bb1b2-b2eb-4a19-8ae3-7c1cc1372c4b",
            planName: "Plan 2",
            isDraft: true,
            planStartDate: "2023-07-01T00:00:00+00:00",
            planEndDate: "2023-09-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 1",
                  lastName: "Test 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 2",
                  lastName: "Test 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 3",
                  lastName: "Test 3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "b64e6231-f805-4470-9302-02b7c19a6f9d",
            planName: "Plan 1",
            isDraft: true,
            planStartDate: "2023-01-01T00:00:00+00:00",
            planEndDate: "2023-12-31T23:59:59.999999+00:00",
            planType: "MAIN",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 1",
                  lastName: "Test 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
        ],
      },
      {
        sort: "created_date",
        expectedResult: [
          {
            planId: "20e27d49-6e6f-42d1-b2a3-d1eced19c2d6",
            planName: "Plan 4",
            isDraft: true,
            planStartDate: "2023-03-01T00:00:00+00:00",
            planEndDate: "2023-05-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 8",
                  lastName: "Test 8",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
          {
            planId: "edbf071d-0d63-43e9-bedc-2ba21652253c",
            planName: "Plan 3",
            isDraft: true,
            planStartDate: "2023-08-01T00:00:00+00:00",
            planEndDate: "2023-12-31T23:59:59.999999+00:00",
            planType: "MAIN",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 10",
                  lastName: "Test 10",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 11",
                  lastName: "Test 11",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "458bb1b2-b2eb-4a19-8ae3-7c1cc1372c4b",
            planName: "Plan 2",
            isDraft: true,
            planStartDate: "2023-07-01T00:00:00+00:00",
            planEndDate: "2023-09-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 1",
                  lastName: "Test 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 2",
                  lastName: "Test 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 3",
                  lastName: "Test 3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "b64e6231-f805-4470-9302-02b7c19a6f9d",
            planName: "Plan 1",
            isDraft: true,
            planStartDate: "2023-01-01T00:00:00+00:00",
            planEndDate: "2023-12-31T23:59:59.999999+00:00",
            planType: "MAIN",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 1",
                  lastName: "Test 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
        ],
      },
      {
        sort: "plan_start_date",
        expectedResult: [
          {
            planId: "edbf071d-0d63-43e9-bedc-2ba21652253c",
            planName: "Plan 3",
            isDraft: true,
            planStartDate: "2023-08-01T00:00:00+00:00",
            planEndDate: "2023-12-31T23:59:59.999999+00:00",
            planType: "MAIN",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 10",
                  lastName: "Test 10",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 11",
                  lastName: "Test 11",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 2,
          },
          {
            planId: "458bb1b2-b2eb-4a19-8ae3-7c1cc1372c4b",
            planName: "Plan 2",
            isDraft: true,
            planStartDate: "2023-07-01T00:00:00+00:00",
            planEndDate: "2023-09-30T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 1",
                  lastName: "Test 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 2",
                  lastName: "Test 2",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
              {
                employee: {
                  firstName: "User 3",
                  lastName: "Test 3",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 3,
          },
          {
            planId: "20e27d49-6e6f-42d1-b2a3-d1eced19c2d6",
            planName: "Plan 4",
            isDraft: true,
            planStartDate: "2023-03-01T00:00:00+00:00",
            planEndDate: "2023-05-31T23:59:59.999999+00:00",
            planType: "SPIFF",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 8",
                  lastName: "Test 8",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
          {
            planId: "b64e6231-f805-4470-9302-02b7c19a6f9d",
            planName: "Plan 1",
            isDraft: true,
            planStartDate: "2023-01-01T00:00:00+00:00",
            planEndDate: "2023-12-31T23:59:59.999999+00:00",
            planType: "MAIN",
            payeesInPlan: [
              {
                employee: {
                  firstName: "User 1",
                  lastName: "Test 1",
                  employeeEmailId: "<EMAIL>",
                  profilePicture: null,
                },
              },
            ],
            totalPayeesInPlan: 1,
          },
        ],
      },
    ];

    // Changing this graphql to rest api - post that will be fixed
    test.describe.skip(
      "Sorting functionality in forecast plans display",
      () => {
        test.beforeAll(async ({ request }) => {
          token = await bearerTokenGenerator(request, creds.QAFORECAST_CREDS);
        });

        sortingTestCases.forEach(({ sort, expectedResult }) => {
          test(`Sorts by ${sort}`, async ({ request }) => {
            console.log(sort);
            console.log(token);
            const response = await request.post("/graphql", {
              data: {
                query: requestBodies.allPlans,
                variables: {
                  searchTerm: "",
                  component: "commission_plans",
                  fiscalYear: 2023,
                  planStatus: "all",
                  payeesLimit: 9,
                  offsetValue: 0,
                  limitValue: 12,
                  showActivePlans: false,
                  showSpiffPlans: false,
                  sortBy: sort,
                },
              },
              headers: {
                Authorization: token,
              },
            });
            const resp = await response.json();
            console.log(resp.data);
            expect(resp.data.allPlans.length).toBe(4);
            expect(resp.data.allPlans).toEqual(expectedResult);
          });
        });
      }
    );

    test("Test to check basic plan based operations", async ({ adminPage }) => {
      const page = adminPage.page;
      // Clone plan
      await page.getByPlaceholder("Search by plan name").click();
      await page.getByPlaceholder("Search by plan name").fill("Plan 5");
      await page.getByTestId("pt-actions-Plan 5").click();
      await page.getByText("Clone Forecast").click();
      await expect(page.getByText("Plan 5_Copy").nth(1)).toBeVisible();
      await expect(page.getByText("2payee saved")).toBeVisible();
      await page.getByRole("button", { name: "Exit Canvas" }).click();

      // Search for a plan
      await page.getByPlaceholder("Search by plan name").click();
      await page.getByPlaceholder("Search by plan name").fill("quarterly");
      await expect(page.getByText("Quarterly Plan")).toBeVisible();
      await page.getByLabel("close-circle").click();

      // Fiscal year filter check
      await page.getByTestId("pt-fiscal-year-select").click();
      await page.locator("span").filter({ hasText: "2023" }).click();
      await page.waitForTimeout(2000);
      await expect(page.getByText("All04Published00Draft04")).toBeVisible();
      await page.getByTestId("pt-fiscal-year-select").click();
      await page.locator("span").filter({ hasText: "2024" }).click();

      // Spiff plan count check
      await page.locator("button.ant-btn-ghost").click();
      await page
        .getByRole("menuitem", { name: "Show Spiff Plans" })
        .getByRole("switch")
        .click();
      await expect(page.getByText("All04Published00Draft04")).toBeVisible();

      // Active plan count check
      await page
        .getByRole("menuitem", { name: "Show Spiff Plans" })
        .getByRole("switch")
        .click();
      await page.waitForTimeout(2000);
      await page
        .getByRole("menuitem", { name: "Show Active Plans" })
        .getByRole("switch")
        .click();

      await expect(page.getByText("All00Published00Draft00")).toBeVisible({
        timeout: 15000,
      });
      await page
        .getByRole("menuitem", { name: "Show Active Plans" })
        .getByRole("switch")
        .click();
      // Delete plan
      await page.getByPlaceholder("Search by plan name").click();
      await page.getByPlaceholder("Search by plan name").fill("Delete plan 1");
      await page.getByTestId("pt-actions-Delete plan 1").click();
      await page.getByText("Delete", { exact: true }).click();
      await expect(
        page.getByText("Are you sure you want to delete")
      ).toBeVisible();
      await page.getByRole("button", { name: "Yes, delete" }).click();
      await expect(
        page.getByText("Delete plan 1 deleted successfully!").first()
      ).toBeVisible();
    });

    test("Test to create plan with plan document", async ({ adminPage }) => {
      const page = adminPage.page;
      // Plan creation
      await page.getByRole("button", { name: "Build Plan" }).click();
      await page.getByPlaceholder("Enter name").click();
      await page.getByPlaceholder("Enter name").fill("New forecast plan 1");
      await page.getByPlaceholder("From").click();
      await page.getByPlaceholder("From").fill("Aug 01, 2024");
      await page.getByPlaceholder("From").press("Enter");
      await page.getByPlaceholder("To").fill("Aug 31, 2024");
      await page.getByPlaceholder("To").press("Enter");
      await page.getByLabel("Payout Frequency*").click();
      await page.locator("span").filter({ hasText: "Monthly" }).click();

      const fileChooserPromise = page.waitForEvent("filechooser");
      await page.getByText("Upload", { exact: true }).click();
      const fileChooser = await fileChooserPromise;
      await fileChooser.setFiles(
        path.join(__dirname, "../../../../upload-files/playwright_contract.pdf")
      );
      await page
        .locator("#create_plan")
        .getByRole("button", { name: "Build Plan" })
        .click();
      await expect(page.getByText("playwright_contract.pdf")).toHaveCount(1);

      // Checking payees section
      await page.waitForTimeout(2000);
      await page.getByPlaceholder("Search payees").first().click();
      await page.getByPlaceholder("Search payees").first().fill("new");
      await page.waitForTimeout(2000);
      await page.getByText("New User 1 Test").click();
      await page.getByText("New User 2 Test").click();
      await expect(page.getByText("Filtered payees: 3")).toBeVisible();
      // await page.getByRole("button", { name: "close-circle" }).click();
      await page.locator('//span[@aria-label="close-circle"]').nth(1).click();

      // Adding exited user
      await page.getByPlaceholder("Search payees").first().click();
      await page.getByPlaceholder("Search payees").first().fill("exit");
      await page.getByLabel("EUExit User").check();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await expect(
        page.getByRole("treegrid").getByText("User has already been exited")
      ).toBeVisible();

      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await page
        .getByRole("button", { name: "Exit", exact: true })
        .nth(-1)
        .click();
      await page.waitForTimeout(2000);
      // Active plan count check
      await page.locator("button.ant-btn-ghost").click();
      await page
        .getByRole("menuitem", { name: "Show Spiff Plans" })
        .getByRole("switch")
        .click();
      await page.waitForTimeout(2000);
      await page
        .getByRole("menuitem", { name: "Show Active Plans" })
        .getByRole("switch")
        .click();
      await expect(page.getByText("Nothing to show")).toBeVisible();

      await page.locator("button.ant-btn-ghost").click();
      await page
        .getByRole("menuitem", { name: "Show Spiff Plans" })
        .getByRole("switch")
        .click();
      await page
        .getByRole("menuitem", { name: "Show Active Plans" })
        .getByRole("switch")
        .click();
      await page.getByText("New forecast plan 1", { exact: true }).click();
      await page
        .locator("div.ant-drawer-body>div>div>div>div>div>div>button>div>svg")
        .click();
      await page.locator("#edit_plan_planDocument").locator("span>svg").click();
      await page.getByRole("button", { name: "Update" }).click();
      // // Upload plan document and remove it
      // await page.getByRole("button", { name: "Upload" }).click();
      // await page
      //   .locator("span>input[type='file']")
      //   .setInputFiles("./upload-files/playwright_contract.pdf");
      // await page.locator("#create_plan_planDocument svg").click();
      // await page
      //   .locator("#create_plan")
      //   .getByRole("button", { name: "Build Plan" })
      //   .click();
      await expect(page.getByText("playwright_contract.pdf")).toHaveCount(0);

      // Add all payees and checking pagination and user export csv
      await page.getByLabel("Select All").check();
      await page.getByRole("button", { name: "Add Payees" }).click();
      await page.getByTitle("Hide panel").locator("svg").click();
      await expect(page.getByText("1 - 10of16rows")).toBeVisible();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^1 - 10of16rowsPage of2$/ })
          .getByRole("button")
          .nth(2)
      ).toBeEnabled();
      await page
        .locator("div")
        .filter({ hasText: /^1 - 10of16rowsPage of2$/ })
        .getByRole("button")
        .nth(2)
        .click();
      await expect(page.getByText("11 - 16of16rows")).toBeVisible();
      await expect(
        page
          .locator("div")
          .filter({ hasText: /^11 - 16of16rowsPage of2$/ })
          .getByRole("button")
          .nth(1)
      ).toBeEnabled();
      await expect(
        await page
          .locator("div")
          .filter({ hasText: /^11 - 16of16rowsPage of2$/ })
          .getByRole("button")
          .nth(1)
      ).toBeEnabled();
      await page
        .locator("div")
        .filter({ hasText: /^11 - 16of16rowsPage of2$/ })
        .getByRole("button")
        .nth(1)
        .click();

      // Export csv for users
      await page.getByText("Bulk actions").click();
      const downloadPromise = page.waitForEvent("download");
      await page.getByRole("menuitem", { name: "Download as CSV" }).click();
      const download = await downloadPromise;
      const downloadPath = path.join(
        __dirname,
        "downloads",
        download.suggestedFilename()
      );
      await download.saveAs(downloadPath);
      const results = [];
      fs.createReadStream(downloadPath)
        .pipe(csv())
        .on("data", (data) => {
          const trimmedData = {};
          for (const key in data) {
            trimmedData[key.trim()] = data[key];
          }
          results.push(trimmedData);
        })
        .on("end", () => {
          expect(results.length).toBe(16);
          expect(Object.keys(results[0]).length).toBe(6);
        })
        .on("error", (err) => {
          throw err;
        });
      await expect(
        page.getByText("Successfully Exported").first()
      ).toBeVisible();
      await page.getByTestId("User 14 Test 14").check();
      await page.getByText("Bulk actions").click();
      await page.getByRole("menuitem", { name: "Remove payees" }).click();
      await expect(
        page.getByText("Are you sure to remove 1 payees")
      ).toBeVisible();
      await page.getByRole("button", { name: "Remove" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        page.getByText("Successfully saved 15 payees").first()
      ).toBeVisible();

      // Import valid component
      await page.getByRole("button", { name: "Add Component" }).click();
      await page.getByLabel("Import an existing").check();
      await page.getByRole("button", { name: "Get Started" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select fiscal year$/ })
        .nth(2)
        .click();
      await page.getByText("2024", { exact: true }).nth(2).click();
      await page
        .getByTestId("import-criteria-select-plan")
        .locator("div")
        .filter({ hasText: "Select commission plan" })
        .click();
      await page
        .getByTestId("import-criteria-select-plan")
        .locator(".ant-select-selection-search-input")
        .fill("plan 6");
      await page
        .locator("span")
        .filter({ hasText: "Plan 6" })
        .locator("span")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Choose component$/ })
        .nth(2)
        .click();
      await page.getByText("Simple").nth(1).click();
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "Create" }).click();
      await expect(
        page.getByText("Component imported successfully").first()
      ).toBeVisible();
      await page.getByRole("button", { name: "Exit Canvas" }).click();
    });

    test("Test to check creation of all types of criterias", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      // Plan creation
      await page.getByRole("button", { name: "Build Plan" }).click();
      await page.getByPlaceholder("Enter name").click();
      await page.getByPlaceholder("Enter name").fill("New Forecast Plan 3");
      await page.getByPlaceholder("From").click();
      await page.getByPlaceholder("From").fill("Jun 01, 2024");
      await page.getByPlaceholder("From").press("Enter");
      await page.getByPlaceholder("To").fill("Jun 30, 2024");
      await page.getByPlaceholder("To").press("Enter");
      await page.getByLabel("Payout Frequency*").click();
      await page.locator("span").filter({ hasText: "Monthly" }).click();
      await page.getByRole("switch").click();
      await page
        .locator("#create_plan")
        .getByRole("button", { name: "Build Plan" })
        .click();

      // Adding simple criteria
      await page.getByRole("button", { name: "Add Component" }).click();
      await page.getByLabel("Create new componentCraft").check();
      await page.getByRole("button", { name: "Get Started" }).click();
      await page.getByLabel("Simple").check();
      await page.getByPlaceholder("Enter name").click();
      await page.getByPlaceholder("Enter name").fill("Simple");
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByLabel("Databook*").click();
      await page.getByText("Deals").click();
      await page.getByLabel("Datasheet*").click();
      await page.getByText("ds 1").click();
      await page.getByLabel("Email field*").click();
      await page.getByText("Email", { exact: true }).click();
      await page.getByLabel("Date field*").click();
      await page.getByText("Deal date").click();
      await page.getByRole("button", { name: "Create" }).click();
      await expect(
        page.getByText("Component created successfully").first()
      ).toBeVisible();
      await page.getByPlaceholder("Press Ctrl + H for help").click();
      await page.getByText("Amount").click();
      await page.getByTestId("auto-suggestion-view").getByText("+").click();
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .last()
        .fill("10");
      await page
        .locator("div[role='listitem']")
        .filter({ hasText: "10" })
        .first()
        .click();

      // Adding conditional criteria
      await page.getByRole("button", { name: "Add Component" }).click();
      await page.getByLabel("Create new componentCraft").check();
      await page.getByRole("button", { name: "Get Started" }).click();
      await page.getByLabel("Conditional").check();
      await page.getByPlaceholder("Enter name").click();
      await page.getByPlaceholder("Enter name").fill("Conditional");
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByLabel("Databook*").click();
      await page.getByText("Deals").click();
      await page.getByLabel("Datasheet*").click();
      await page.getByText("ds 1").click();
      await page.getByLabel("Email field*").click();
      await page.getByText("Email", { exact: true }).click();
      await page.getByLabel("Date field*").click();
      await page.getByText("Deal date").click();
      await page.locator("#data-source-form").getByRole("switch").click();
      await page.locator("#data-source-form_teamType").click();
      await page.getByRole("listitem").getByText("Pod").click();
      await page.getByRole("button", { name: "Create" }).click();
      await expect(
        page.getByText("Component created successfully").first()
      ).toBeVisible();
      await page.getByPlaceholder("Press Ctrl + H for help").first().click();
      await page.getByText("IsNotEmpty").click();
      await page
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Please Select" })
        .click();
      await page.locator("//div[@label='Email']").click();
      await page.getByRole("button", { name: "Apply" }).click();
      await page.getByText("THEN").click();
      await page.getByPlaceholder("Press Ctrl + H for help").first().click();
      await page
        .getByTestId("auto-suggestion-view")
        .getByText("Amount")
        .click();
      await page.getByText("ELSE").click();
      await page.getByPlaceholder("Press Ctrl + H for help").click();
      await page
        .getByTestId("auto-suggestion-view")
        .getByText("Amount")
        .click();
      await page.getByTestId("auto-suggestion-view").getByText("-").click();
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .last()
        .fill("10");
      await page
        .locator("div[role='listitem']")
        .filter({ hasText: "10" })
        .first()
        .click();
      await page.getByText("ELSE").click();

      // Adding tier criteria
      await page.getByRole("button", { name: "Add Component" }).click();
      await page.getByLabel("Create new componentCraft").check();
      await page.getByRole("button", { name: "Get Started" }).click();
      await page.getByLabel("TierChoose the Tier template").check();
      await page.getByPlaceholder("Enter name").click();
      await page.getByPlaceholder("Enter name").fill("Tier");
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByLabel("Databook*").click();
      await page.getByText("Deals").click();
      await page.getByLabel("Datasheet*").click();
      await page.getByText("ds 1").click();
      await page.getByLabel("Email field*").click();
      await page.getByRole("listitem").getByText("Email").click();
      await page.getByLabel("Date field*").click();
      await page.getByText("Deal date").click();
      await page.getByRole("button", { name: "Create" }).click();
      await expect(
        page.getByText("Component created successfully").first()
      ).toBeVisible();
      await page.getByPlaceholder("Press Ctrl + H for help").click();
      await page
        .getByTestId("auto-suggestion-view")
        .getByText("Amount")
        .click();
      await page.getByText("Enter formula to compare").click();
      await page.getByRole("spinbutton").click();
      await page.getByRole("spinbutton").fill("20");
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-2)
        .click();
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-2)
        .fill("2");
      await page
        .getByTestId("auto-suggestion-view")
        .getByText("2")
        .first()
        .click();
      await page.getByText("Enter tiered slabs").click();
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .last()
        .click();
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .last()
        .fill("10");
      await page.getByText("10Integer").click();
      await page.getByText("Enter tiered slabs").click();

      // Adding quota criteria
      await page.getByRole("button", { name: "Add Component" }).click();
      await page.getByLabel("Create new componentCraft").check();
      await page.getByRole("button", { name: "Get Started" }).click();
      await page.getByLabel("Quota").check();
      await page.getByPlaceholder("Enter name").click();
      await page.getByPlaceholder("Enter name").fill("Quota");
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByLabel("Databook*").click();
      await page.getByText("Deals").click();
      await page.getByLabel("Datasheet*").click();
      await page.getByText("ds 1").click();
      await page.getByLabel("Email field*").click();
      await page.getByRole("listitem").getByText("Email").click();
      await page.getByLabel("Date field*").click();
      await page.getByText("Deal date").click();
      await page.getByLabel("Quota Category*").click();
      await page.getByText("Primary Quota").click();
      await page.getByRole("button", { name: "Create" }).click();
      await expect(
        page.getByText("Component created successfully").first()
      ).toBeVisible();
      await page.getByPlaceholder("Press Ctrl + H for help").click();
      await page
        .getByTestId("auto-suggestion-view")
        .getByText("Amount")
        .click();
      await page.getByText("Enter formula to compare").nth(1).click();
      await page.getByRole("spinbutton").click();
      await page.getByRole("spinbutton").press("ArrowLeft");
      await page.getByRole("spinbutton").fill("100%");
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-2)
        .click();
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-2)
        .fill("5");
      await page
        .getByTestId("auto-suggestion-view")
        .getByText("5")
        .first()
        .click();
      await page.getByText("Enter tiered slabs").nth(1).click();
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-1)
        .click();
      await page
        .locator('div[data-testid="expression-input-box"] input[placeholder]')
        .nth(-1)
        .fill("10");
      await page
        .getByTestId("auto-suggestion-view")
        .getByText("10")
        .first()
        .click();
      await page.getByText("Enter tiered slabs").nth(1).click();
      // Saving all components
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        page.getByText("Plan Component details saved successfully").first()
      ).toBeVisible();

      // Checking for settlement criteria to not be present
      await page.getByRole("button", { name: "Add Component" }).click();
      await page.getByLabel("Create new componentCraft").check();
      await page.getByRole("button", { name: "Get Started" }).click();
      await expect(page.getByLabel("Settlement")).toHaveCount(0);
      await page.getByLabel("Close", { exact: true }).click();
      await page.getByRole("button", { name: "Exit Canvas" }).click();
    });

    test("Test to check forecast plans not visible under these sections", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      // Checking in report enrichment section
      await page.goto("/settings", { waitUntil: "networkidle" });
      await page
        .getByRole("link", { name: "Report Enrichment Enrich the" })
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Commission Plan$/ })
        .nth(2)
        .click();
      // Commission plan is visible
      await expect(page.getByText("Canvas plan 1")).toBeVisible();
      // Forecast plan is not visible
      await expect(page.getByText("Plan 6")).toBeHidden();
      await page
        .locator("div")
        .filter({ hasText: /^Select Commission Plan$/ })
        .nth(2)
        .click();

      // Checking in plans section
      await page.goto("/plans", { waitUntil: "networkidle" });
      await expect(page.getByText("Canvas plan 1")).toBeVisible();
      await expect(page.getByText("Plan 6")).toBeHidden();

      // Checking in dashboards section
      await page.goto("/dashboards?type=all", { waitUntil: "networkidle" });
      await page

        .getByText("playwright-test-plan-2 Metrics")
        .click({ waitUntil: "networkdile" });
      await page.waitForTimeout(10000);
      await page
        .locator('//span[text()="Payouts"]')
        .last()
        .waitFor({ state: "visible" });
      await page
        .locator(
          ".mb-8 > div:nth-child(2) > div > .ant-select > .ant-select-selector"
        )
        .first()
        .click();
      await expect(page.getByText("All Commission Plans").nth(1)).toBeVisible();
      await expect(page.getByText("Canvas plan 1")).toBeVisible();
      await expect(page.getByText("Plan 6")).toBeHidden();
      await page
        .locator(
          ".bg-ever-base > div > div:nth-child(2) > div > div > .ant-select > .ant-select-selector"
        )
        .first()
        .click();
      await expect(page.getByText("All Commission Plans").nth(3)).toBeVisible();
      await expect(page.getByText("Canvas plan 1").nth(1)).toBeVisible();
      await expect(page.getByText("Plan 6")).toBeHidden();

      // Checking super admin user's dashboard
      await page.goto("/users", { waitUntil: "networkidle" });
      await page
        .getByTestId("<EMAIL> users dd button")
        .click();
      await page.getByRole("button", { name: "Login as user" }).click();
      await page.waitForTimeout(5000);
      await page
        .locator('//span[text()="Payouts"]')
        .last()
        .waitFor({ state: "visible" });
      const locator = page.locator('span[title="All Commission Plans"]');
      await expect(locator).toHaveCount(2, { timeout: 10000 });
      await page
        .locator(
          ".mb-8 > div:nth-child(2) > div > .ant-select > .ant-select-selector"
        )
        .first()
        .click();
      await expect(page.getByText("All Commission Plans").nth(1)).toBeVisible();
      await expect(page.getByText("Canvas plan 1")).toBeVisible();
      await expect(page.getByText("Plan 6")).toBeHidden();
      await page
        .locator(
          ".flex > div > div > .ant-select > .ant-select-selector > .ant-select-selection-overflow"
        )
        .click();
      await expect(page.getByText("All Commission Plans").nth(2)).toBeVisible();
      await expect(page.getByText("Canvas plan 1").nth(1)).toBeVisible();
      await expect(page.getByText("Plan 6")).toBeHidden();
      await page
        .locator(
          ".bg-ever-base > div > div:nth-child(2) > div > div > .ant-select > .ant-select-selector"
        )
        .first()
        .click();
      await page.getByRole("button", { name: "Exit" }).click();
    });

    test("Test to clone from commission plan to forecast and check operations", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      // Create draft commission plan and clone it to forecast
      await page.goto("/plans", { waitUntil: "networkidle" });
      await page.getByTestId("pt-actions-Canvas plan 1").click();
      await page.getByText("With Payees").click();
      await page
        .locator("div")
        .filter({ hasText: /^DraftMonthly$/ })
        .getByRole("button")
        .click();
      await page.getByPlaceholder("Enter name").click();
      await page.getByPlaceholder("Enter name").click();
      await page.getByPlaceholder("Enter name").fill("Canvas plan 2");
      await page.getByRole("button", { name: "Update" }).click();
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await page.getByPlaceholder("Search by plan name").click();
      await page.getByPlaceholder("Search by plan name").fill("Canvas plan 2");
      await page.waitForTimeout(2000);
      await page.getByTestId("pt-actions-Canvas plan 2").click();
      await page.getByText("Add to Forecast").click();
      await expect(
        page.getByText("Canvas plan 2_Copy successfully added").first()
      ).toBeVisible();
      const page1Promise = page.waitForEvent("popup");
      await page.getByRole("link", { name: "Take me there" }).click();
      const page1 = await page1Promise;

      // Clone forecast plan
      await page1.getByPlaceholder("Search by plan name").click();
      await page1.getByPlaceholder("Search by plan name").fill("canvas");
      await expect(page1.getByText("Canvas plan 2")).toBeVisible();
      await page.waitForTimeout(4000);
      await page1.getByTestId("pt-actions-Canvas plan 2_Copy").click();
      await page1.getByText("Clone Forecast").click();
      await expect(
        page1.getByText("Canvas plan 2_Copy_Copy").nth(1)
      ).toBeVisible();
      await page1.getByRole("button", { name: "Exit Canvas" }).click();

      // Delete cloned forecast plan
      await page1.getByPlaceholder("Search by plan name").click();
      await page1
        .getByPlaceholder("Search by plan name")
        .fill("canvas plan 2_Copy_Copy");
      await page.waitForTimeout(2000);
      await page1.getByTestId("pt-actions-Canvas plan 2_Copy_Copy").click();
      await page1.getByText("Delete").click();
      await expect(
        page1.getByText(
          'Are you sure you want to delete "Canvas plan 2_Copy_Copy" commission plan?'
        )
      ).toBeVisible();
      await page1.getByRole("button", { name: "Yes, delete" }).click();
      await expect(
        page1.getByText("Canvas plan 2_Copy_Copy deleted successfully!").first()
      ).toBeVisible();
      await page1.getByLabel("close-circle").click();

      // Delete cloned commission plan but that shouldnt delete forecast plan
      await page1.goto("/plans", { waitUntil: "networkidle" });
      await page1.getByPlaceholder("Search by plan name").click();
      await page1.getByPlaceholder("Search by plan name").fill("canvas plan 2");
      await page.waitForTimeout(2000);
      await page1.getByTestId("pt-actions-Canvas plan 2").click();
      await page1.getByText("Delete").click();
      await expect(
        page1.getByText(
          'Are you sure you want to delete "Canvas plan 2" commission plan?'
        )
      ).toBeVisible();
      await page1.getByRole("button", { name: "Yes, delete" }).click();
      await expect(
        page1.getByText("Canvas plan 2 deleted successfully!").first()
      ).toBeVisible();
      await page1.getByLabel("close-circle").click();
      await expect(page1.getByText("Canvas plan 2")).toHaveCount(0);
      await page1.goto("/forecasts", { waitUntil: "networkidle" });
      await page1.getByPlaceholder("Search by plan name").click();
      await page1.getByPlaceholder("Search by plan name").fill("canvas");
      await expect(page1.getByText("Canvas plan 2")).toHaveCount(1);
    });

    test("Test to check permissions for power admin in cloning and deleting plans", async ({
      adminPage,
    }) => {
      // Power Admin plan cloning
      const page = adminPage.page;

      async function deletePlan() {
        await page
          .getByTestId("pt-actions-Plan Intermediate only components_Copy")
          .click();
        await page.getByText("Delete").click();
        await expect(
          page.getByText(
            'Are you sure you want to delete "Plan Intermediate only components_Copy" commission plan?'
          )
        ).toBeVisible();
        await page.getByRole("button", { name: "Yes, delete" }).click();
        await expect(
          page.getByText(
            "Plan Intermediate only components_Copy deleted successfully!"
          )
        ).toBeVisible();
        await page.getByLabel("close-circle").click();
      }

      await page.getByPlaceholder("Search by plan name").click();
      await page
        .getByPlaceholder("Search by plan name")
        .fill("Plan Intermediate only components");
      await expect(
        page
          .getByText("Plan Intermediate only components", { exact: true })
          .first()
      ).toBeVisible();
      await page.waitForTimeout(5000);
      if (
        await page
          .getByText("Plan Intermediate only components_Copy", { exact: true })
          .isVisible()
      ) {
        await deletePlan();
      } else {
        await page
          .getByTestId("pt-actions-Plan Intermediate only components")
          .click();
        await page.getByText("Clone Forecast").click();
        await expect(
          page
            .getByText("Plan Intermediate only components_Copy", {
              exact: true,
            })
            .nth(-1)
        ).toBeVisible();
        await page.getByRole("button", { name: "Exit Canvas" }).click();

        // Power Admin delete cloned forecast plan
        await page.getByPlaceholder("Search by plan name").click();
        await page
          .getByPlaceholder("Search by plan name")
          .fill("Plan Intermediate only components_Copy");
        await page.waitForTimeout(3000);
        await deletePlan();
      }

      await page.getByPlaceholder("Search by plan name").click();
      await page
        .getByPlaceholder("Search by plan name")
        .fill("Plan Intermediate only components");
      await expect(
        page.getByText("Plan Intermediate only components")
      ).toHaveCount(1);
    });

    test("Test to check if forecast option is present - 10305", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      const commIcon = await page.locator("#Commission");
      await commIcon.hover();
      await expect(page.getByRole("link", { name: "Forecasts" })).toBeVisible();
    });

    test.describe("Sync based tests", () => {
      // test.describe.configure({ retries: 2 });
      test("Forecast sync tests for selected plan", async ({ adminPage }) => {
        const page = adminPage.page;
        const commonPage = new CommonUtils(page);
        const commSyncPage = new CommissionSync(page);
        const csPrev = new CommissionSycnPrevPeriod(page);
        await csPrev.navigate("/settings/commissions-and-data-sync");

        // Its a check for validating whether other Sync tabs are closed or not
        while (true) {
          try {
            const runTab = page.getByRole("button", {
              name: "Calculate Commissions Run",
            });
            if (await runTab.isVisible()) {
              await runTab.click(); // Close the tab
              // Add any confirmation/animation wait if needed here
            }
            await csPrev.clickCommissionForecastingRun(); // Try clicking after closing
            break;
          } catch (error) {
            // Optional: wait before retrying to avoid rapid loop
            await page.waitForTimeout(1000);
          }
        }

        await commSyncPage.selectCriteria("payeee-in-plan");
        await commSyncPage.selectDropdown(["Plan 6"]);

        await expect(
          page.locator("span").filter({ hasText: "All Payees" }).nth(2)
        ).toBeVisible();
        await expect(
          page
            .locator("span")
            .filter({ hasText: /^Selected Payees$/ })
            .nth(2)
        ).toBeVisible();
        await expect(
          page
            .locator("span")
            .filter({ hasText: "Payees in Forecast Plan" })
            .first()
        ).toBeVisible();

        await csPrev.selectDate("01 Jun 2024");
        await csPrev.refreshDatabookForecastCheck();
        await csPrev.runCommissions();
        await csPrev.clickSkipandRun();
        await expect(page.getByText("Preparing to start...")).toBeVisible();
        await csPrev.waitForForecastCalculationMessage();
        await csPrev.waitForForecastSuccess();

        await csPrev.navigate("/commissions");
        await commonPage.setStorageCommValue("30-June-2024");
        await page.getByRole("link", { name: "Payee test" }).click();
        await expect(page.getByText("Canvas plan 1")).toBeVisible();
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "AU$0.00"
        );
        await expect(page.getByText("Plan 6")).toHaveCount(0);
      });

      test("Forecast sync tests for selected payees", async ({ adminPage }) => {
        const page = adminPage.page;
        const commonPage = new CommonUtils(page);
        const commSyncPage = new CommissionSync(page);
        const csPrev = new CommissionSycnPrevPeriod(page);
        await csPrev.navigate("/settings/commissions-and-data-sync");

        // Its a check for validating whether other Sync tabs are closed or not
        await page
          .getByRole("button", {
            name: "Calculate Commissions Run",
          })
          .click();

        await commSyncPage.selectCriteria("selected-payees");
        await commSyncPage.selectPayeeSelectedPayeesOption("User 1 Test 1");

        await csPrev.selectDate("01 Jun 2024");
        await csPrev.refreshDatabookForecastCheck();
        await csPrev.runCommissions();
        await csPrev.clickSkipandRun();
        await expect(page.getByText("Preparing to start...")).toBeVisible();
        await csPrev.waitForForecastCalculationMessage();
        await csPrev.waitForForecastSuccess();

        await csPrev.navigate("/commissions");
        await commonPage.setStorageCommValue("30-June-2024");
        await expect(page.getByText("User 1 Test 1")).toHaveCount(0);

        await csPrev.navigate("databook/aba43bb0-2c1e-4bbd-a21d-4fa03ce0b5e0");
        await page.getByText("ds 1 forecast").click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Contains", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("user1");
        await page.getByRole("button", { name: "Add Condition" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.getByText("Commission Plan").nth(2).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To").nth(2).click();
        await page.getByRole("textbox").nth(2).click();
        await page.getByRole("textbox").nth(2).fill("Plan 6");
        await page.getByRole("button", { name: "Add Condition" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        // await page.getByText("Period", { exact: true }).nth(3).click();
        await page.locator('//div[@title="Period"]').last().click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).nth(3).click();
        await page.getByRole("textbox").nth(3).click();
        await page.getByRole("textbox").nth(3).fill("June 2024");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        // await expect(page.getByLabel("ds 1 forecast")).toContainText(
        //   "Period Period Start Date Period End Date Payee Payee Email Payout Frequency Commission Plan Commission Type Record Type Criteria Line Item Id Tier Id Tier Quota Retirement Commission Amount (Org Currency) Payee Currency Currency Conversion Rate Commission Amount (Payout Currency) Databook Datasheet Updated at Plan Id Criteria Id1June 202401 Jun 202430 Jun 2024User 1 Test <EMAIL> 6MAINCommissionTier21Tier 1100EUR1100Dealsds 111 Jul 2024919589a8-da16-4d99-a905-ae29fbe6480e3246217d-433a-451e-b5cd-9d0d0321ccbe2June 202401 Jun 202430 Jun 2024User 1 Test <EMAIL> 6MAINCommissionQuota10Tier 01,0000EUR10Dealsds 111 Jul 2024919589a8-da16-4d99-a905-ae29fbe6480e4931f34c-b236-45f4-b59d-2ecf7769fb4b3June 202401 Jun 202430 Jun 2024User 1 Test <EMAIL> 6MAINCommissionQuota20Tier 02,0000EUR10Dealsds 111 Jul 2024919589a8-da16-4d99-a905-ae29fbe6480e4931f34c-b236-45f4-b59d-2ecf7769fb4b4June 202401 Jun 202430 Jun 2024User 1 Test <EMAIL> 6MAINCommissionSimple11,010EUR11,010Dealsds 111 Jul 2024919589a8-da16-4d99-a905-ae29fbe6480eece8facd-ff1a-42f0-b972-fc1f1807196e5June 202401 Jun 202430 Jun 2024User 1 Test <EMAIL> 6MAINCommissionTier20Tier 050EUR150Dealsds 111 Jul 2024919589a8-da16-4d99-a905-ae29fbe6480e3246217d-433a-451e-b5cd-9d0d0321ccbe6June 202401 Jun 202430 Jun 2024User 1 Test <EMAIL> 6MAINCommissionTier10Tier 050EUR150Dealsds 111 Jul 2024919589a8-da16-4d99-a905-ae29fbe6480e3246217d-433a-451e-b5cd-9d0d0321ccbe7June 202401 Jun 202430 Jun 2024User 1 Test <EMAIL> 6MAINCommissionConditional22,000EUR12,000Dealsds 111 Jul 2024919589a8-da16-4d99-a905-ae29fbe6480ee0f05d5e-655a-4dbc-a83b-a2da96ea86548June 202401 Jun 202430 Jun 2024User 1 Test <EMAIL> 6MAINCommissionConditional11,000EUR11,000Dealsds 111 Jul 2024919589a8-da16-4d99-a905-ae29fbe6480ee0f05d5e-655a-4dbc-a83b-a2da96ea86549June 202401 Jun 202430 Jun 2024User 1 Test <EMAIL> 6MAINCommissionSimple22,010EUR12,010Dealsds 111 Jul 2024919589a8-da16-4d99-a905-ae29fbe6480eece8facd-ff1a-42f0-b972-fc1f1807196e "
        // );
        await expect(page.getByLabel("ds 1 forecast")).toContainText(
          "1 - 9of9rows"
        );
        await expect(page.getByRole("treegrid")).toContainText(
          "<EMAIL>"
        );
      });

      test("Inter commission flag on - only inter forecast object to be updated test", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        const commSyncPage = new CommissionSync(page);
        const csPrev = new CommissionSycnPrevPeriod(page);

        await csPrev.navigate("/settings/commissions-and-data-sync");

        // Its a check for validating whether other Sync tabs are closed or not
        await page
          .getByRole("button", {
            name: "Calculate Commissions Run",
          })
          .click();

        await commSyncPage.selectCriteria("payeee-in-plan");
        await commSyncPage.selectDropdown([
          "Plan Intermediate only components",
        ]);

        await csPrev.selectDate("01 Jun 2024");
        await csPrev.refreshDatabookForecastCheck();
        await csPrev.runCommissions();
        await csPrev.clickSkipandRun();
        await expect(page.getByText("Preparing to start...")).toBeVisible();
        await csPrev.waitForForecastCalculationMessage();
        await csPrev.waitForForecastSuccess();

        await csPrev.navigate("databook/aba43bb0-2c1e-4bbd-a21d-4fa03ce0b5e0");
        await page.getByText("ds 2 inter forecast").click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page
          .getByRole("textbox")
          .nth(1)
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(
          page
            .getByRole("gridcell", { name: "<EMAIL>" })
            .first()
        ).toBeVisible();
        await page.getByText("ds 1 forecast").click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page
          .getByRole("textbox")
          .nth(1)
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(page.locator(".ag-center-cols-viewport")).toBeVisible();
      });

      test("Inter commission flag off - forecast and inter forecast objects to be updated test", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        const commSyncPage = new CommissionSync(page);
        const csPrev = new CommissionSycnPrevPeriod(page);

        await csPrev.navigate("/settings/commissions-and-data-sync");

        // Its a check for validating whether other Sync tabs are closed or not
        await page
          .getByRole("button", {
            name: "Calculate Commissions Run",
          })
          .click();

        await commSyncPage.selectCriteria("payeee-in-plan");
        await commSyncPage.selectDropdown(["Plan 6"]);

        await csPrev.selectDate("01 Jun 2024");
        await csPrev.refreshDatabookForecastCheck();
        await csPrev.runCommissions();
        await csPrev.clickSkipandRun();
        await expect(page.getByText("Preparing to start...")).toBeVisible();
        await csPrev.waitForForecastCalculationMessage();
        await csPrev.waitForForecastSuccess();

        await csPrev.navigate("databook/aba43bb0-2c1e-4bbd-a21d-4fa03ce0b5e0");
        await page.getByText("ds 2 inter forecast").click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page
          .getByRole("textbox")
          .nth(1)
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(
          page
            .getByRole("gridcell", { name: "<EMAIL>" })
            .first()
        ).toBeVisible();
        await page.getByText("ds 1 forecast").click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page
          .getByRole("textbox")
          .nth(1)
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(
          page
            .getByRole("gridcell", { name: "<EMAIL>" })
            .first()
        ).toBeVisible();
      });
    });
  }
);

/*
    test.describe.only("Sync based tests", () => {
      
      test("Forecast sync tests for selected plan", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/settings", { waitUntil: "networkidle" });
        await page
          .getByRole("link", { name: "Commission & Data Sync (On-demand)" })
          .click();

        // Export csv for users
        await page.getByText("Bulk actions").click();
        const downloadPromise = page.waitForEvent("download");
        await page.getByRole("menuitem", { name: "Download as CSV" }).click();
        const download = await downloadPromise;
        const downloadPath = path.join(
          __dirname,
          "downloads",
          download.suggestedFilename()
        );
        await download.saveAs(downloadPath);
        const results = [];
        fs.createReadStream(downloadPath)
          .pipe(csv())
          .on("data", (data) => {
            const trimmedData = {};
            for (const key in data) {
              trimmedData[key.trim()] = data[key];
            }
            results.push(trimmedData);
          })
          .on("end", () => {
            expect(results.length).toBe(16);
            expect(Object.keys(results[0]).length).toBe(6);
          })
          .on("error", (err) => {
            throw err;
          });
        await expect(page.getByText("Successfully Exported")).toBeVisible();
        await page.getByTestId("User 14 Test 14").check();
        await page.getByText("Bulk actions").click();
        await page.getByRole("menuitem", { name: "Remove payees" }).click();
        await expect(
          page.getByText("Are you sure to remove 1 payees")
        ).toBeVisible();
        await page.getByRole("button", { name: "Remove" }).click();
        await page.getByRole("button", { name: "Save" }).click();
        await expect(
          page.getByText("Successfully saved 15 payees")
        ).toBeVisible();

        // Import valid component
        await page.getByRole("button", { name: "Add Component" }).click();
        await page.getByLabel("Import an existing").check();
        await page.getByRole("button", { name: "Get Started" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Select fiscal year$/ })
          .nth(2)
          .click();
        await page.getByText("2024", { exact: true }).nth(2).click();
        await page
          .getByTestId("import-criteria-select-plan")
          .locator("div")
          .filter({ hasText: "Select commission plan" })
          .click();
        await page
          .getByTestId("import-criteria-select-plan")
          .locator(".ant-select-selection-search-input")
          .fill("plan 6");
        await page
          .locator("span")
          .filter({ hasText: "Plan 6" })
          .locator("span")
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Choose component$/ })
          .nth(2)
          .click();
        await page.getByText("Simple").nth(1).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Create" }).click();
        await expect(
          page.getByText("Component imported successfully")
        ).toBeVisible();
        await page.getByRole("button", { name: "Exit Canvas" }).click();
      });

      test("Test to check creation of all types of criterias", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        // Plan creation
        await page.getByRole("button", { name: "Build Plan" }).click();
        await page.getByPlaceholder("Enter name").click();
        await page.getByPlaceholder("Enter name").fill("New Forecast Plan 3");
        await page.getByPlaceholder("From").click();
        await page.getByPlaceholder("From").fill("Jun 01, 2024");
        await page.getByPlaceholder("From").press("Enter");
        await page.getByPlaceholder("To").fill("Jun 30, 2024");
        await page.getByPlaceholder("To").press("Enter");
        await page.getByLabel("Payout Frequency*").click();
        await page.locator("span").filter({ hasText: "Monthly" }).click();
        await page.getByRole("switch").click();
        await page
          .locator("#create_plan")
          .getByRole("button", { name: "Build Plan" })
          .click();

        // Adding simple criteria
        await page.getByRole("button", { name: "Add Component" }).click();
        await page.getByLabel("Create new componentCraft").check();
        await page.getByRole("button", { name: "Get Started" }).click();
        await page.getByLabel("Simple").check();
        await page.getByPlaceholder("Enter name").click();
        await page.getByPlaceholder("Enter name").fill("Simple");
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByLabel("Databook*").click();
        await page.getByText("Deals").click();
        await page.getByLabel("Datasheet*").click();
        await page.getByText("ds 1").click();
        await page.getByLabel("Email field*").click();
        await page.getByText("Email", { exact: true }).click();
        await page.getByLabel("Date field*").click();
        await page.getByText("Deal date").click();
        await page.getByRole("button", { name: "Create" }).click();
        await expect(
          page.getByText("Component created successfully")
        ).toBeVisible();
        await page.getByPlaceholder("Press Ctrl + H for help").click();
        await page.getByText("Amount").click();
        await page.getByTestId("auto-suggestion-view").getByText("+").click();
        await page.getByRole("textbox").nth(3).fill("10");
        await page.getByText("10").first().click();

        // Adding conditional criteria
        await page.getByRole("button", { name: "Add Component" }).click();
        await page.getByLabel("Create new componentCraft").check();
        await page.getByRole("button", { name: "Get Started" }).click();
        await page.getByLabel("Conditional").check();
        await page.getByPlaceholder("Enter name").click();
        await page.getByPlaceholder("Enter name").fill("Conditional");
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByLabel("Databook*").click();
        await page.getByText("Deals").click();
        await page.getByLabel("Datasheet*").click();
        await page.getByText("ds 1").click();
        await page.getByLabel("Email field*").click();
        await page.getByText("Email", { exact: true }).click();
        await page.getByLabel("Date field*").click();
        await page.getByText("Deal date").click();
        await page.locator("#data-source-form").getByRole("switch").click();
        await page.locator("#data-source-form_teamType").click();
        await page.getByRole("listitem").getByText("Pod").click();
        await page.getByRole("button", { name: "Create" }).click();
        await expect(
          page.getByText("Component created successfully")
        ).toBeVisible();
        await page.getByPlaceholder("Press Ctrl + H for help").first().click();
        await page.getByText("IsNotEmpty").click();
        await page
          .locator("#radix-2")
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Please Select" })
          .click();
        await page.locator("span").filter({ hasText: "Email" }).click();
        await page.getByRole("button", { name: "Apply" }).click();
        await page.getByText("THEN").click();
        await page.getByPlaceholder("Press Ctrl + H for help").first().click();
        await page
          .getByTestId("auto-suggestion-view")
          .getByText("Amount")
          .click();
        await page.getByText("ELSE").click();
        await page.getByPlaceholder("Press Ctrl + H for help").click();
        await page
          .getByTestId("auto-suggestion-view")
          .getByText("Amount")
          .click();
        await page.getByTestId("auto-suggestion-view").getByText("-").click();
        await page
          .locator(
            ".flex > div:nth-child(2) > div:nth-child(2) > div > .bg-ever-base > .w-\\[calc\\(100\\%-25px\\)\\] > input:nth-child(5)"
          )
          .fill("10");
        await page.getByText("10").nth(1).click();
        await page.getByText("ELSE").click();

        // Adding tier criteria
        await page.getByRole("button", { name: "Add Component" }).click();
        await page.getByLabel("Create new componentCraft").check();
        await page.getByRole("button", { name: "Get Started" }).click();
        await page.getByLabel("TierChoose the Tier template").check();
        await page.getByPlaceholder("Enter name").click();
        await page.getByPlaceholder("Enter name").fill("Tier");
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByLabel("Databook*").click();
        await page.getByText("Deals").click();
        await page.getByLabel("Datasheet*").click();
        await page.getByText("ds 1").click();
        await page.getByLabel("Email field*").click();
        await page.getByRole("listitem").getByText("Email").click();
        await page.getByLabel("Date field*").click();
        await page.getByText("Deal date").click();
        await page.getByRole("button", { name: "Create" }).click();
        await expect(
          page.getByText("Component created successfully")
        ).toBeVisible();
        await page.getByPlaceholder("Press Ctrl + H for help").click();
        await page
          .getByTestId("auto-suggestion-view")
          .getByText("Amount")
          .click();
        await page.getByText("Enter formula to compare").click();
        await page.getByRole("spinbutton").click();
        await page.getByRole("spinbutton").fill("20");
        await page
          .locator(
            "div:nth-child(3) > div:nth-child(2) > div:nth-child(2) > div > .bg-ever-base > .w-\\[calc\\(100\\%-25px\\)\\] > input:nth-child(5)"
          )
          .first()
          .click();
        await page
          .locator(
            "div:nth-child(3) > div:nth-child(2) > div:nth-child(2) > div > .bg-ever-base > .w-\\[calc\\(100\\%-25px\\)\\] > input:nth-child(5)"
          )
          .first()
          .fill("2");
        await page
          .getByTestId("auto-suggestion-view")
          .getByText("2")
          .first()
          .click();
        await page.getByText("Enter tiered slabs").click();
        await page
          .locator(
            "div:nth-child(3) > div:nth-child(3) > div:nth-child(2) > div:nth-child(2) > div > .bg-ever-base > .w-\\[calc\\(100\\%-25px\\)\\] > input:nth-child(5)"
          )
          .click();
        await page
          .locator(
            "div:nth-child(3) > div:nth-child(3) > div:nth-child(2) > div:nth-child(2) > div > .bg-ever-base > .w-\\[calc\\(100\\%-25px\\)\\] > input:nth-child(5)"
          )
          .fill("10");
        await page.getByText("10").nth(2).click();
        await page.getByText("Enter tiered slabs").click();

        // Adding quota criteria
        await page.getByRole("button", { name: "Add Component" }).click();
        await page.getByLabel("Create new componentCraft").check();
        await page.getByRole("button", { name: "Get Started" }).click();
        await page.getByLabel("Quota").check();
        await page.getByPlaceholder("Enter name").click();
        await page.getByPlaceholder("Enter name").fill("Quota");
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByLabel("Databook*").click();
        await page.getByText("Deals").click();
        await page.getByLabel("Datasheet*").click();
        await page.getByText("ds 1").click();
        await page.getByLabel("Email field*").click();
        await page.getByRole("listitem").getByText("Email").click();
        await page.getByLabel("Date field*").click();
        await page.getByText("Deal date").click();
        await page.getByLabel("Quota Category*").click();
        await page.getByText("Primary Quota").click();
        await page.getByRole("button", { name: "Create" }).click();
        await expect(
          page.getByText("Component created successfully")
        ).toBeVisible();
        await page.getByPlaceholder("Press Ctrl + H for help").click();
        await page
          .getByTestId("auto-suggestion-view")
          .getByText("Amount")
          .click();
        await page.getByText("Enter formula to compare").nth(1).click();
        await page.getByRole("spinbutton").click();
        await page.getByRole("spinbutton").press("ArrowLeft");
        await page.getByRole("spinbutton").fill("100%");
        await page
          .locator(
            "div:nth-child(4) > div > div:nth-child(2) > div > div > div > div > .py-5 > div > .h-auto > div:nth-child(3) > div:nth-child(2) > div:nth-child(2) > div > .bg-ever-base > .w-\\[calc\\(100\\%-25px\\)\\] > input:nth-child(5)"
          )
          .click();
        await page
          .locator(
            "div:nth-child(4) > div > div:nth-child(2) > div > div > div > div > .py-5 > div > .h-auto > div:nth-child(3) > div:nth-child(2) > div:nth-child(2) > div > .bg-ever-base > .w-\\[calc\\(100\\%-25px\\)\\] > input:nth-child(5)"
          )
          .fill("5");
        await page
          .getByTestId("auto-suggestion-view")
          .getByText("5")
          .first()
          .click();
        await page.getByText("Enter tiered slabs").nth(1).click();
        await page
          .locator(
            "div:nth-child(4) > div > div:nth-child(2) > div > div > div > div > .py-5 > div:nth-child(3) > div:nth-child(3) > div:nth-child(2) > div:nth-child(2) > div > .bg-ever-base > .w-\\[calc\\(100\\%-25px\\)\\] > input:nth-child(5)"
          )
          .click();
        await page
          .locator(
            "div:nth-child(4) > div > div:nth-child(2) > div > div > div > div > .py-5 > div:nth-child(3) > div:nth-child(3) > div:nth-child(2) > div:nth-child(2) > div > .bg-ever-base > .w-\\[calc\\(100\\%-25px\\)\\] > input:nth-child(5)"
          )
          .fill("10");
        await page
          .getByTestId("auto-suggestion-view")
          .getByText("10")
          .first()
          .click();
        await page.getByText("Enter tiered slabs").nth(1).click();

        // Saving all components
        await page.getByRole("button", { name: "Save" }).click();
        await expect(
          page.getByText("Plan Component details saved successfully")
        ).toBeVisible();

        // Checking for settlement criteria to not be present
        await page.getByRole("button", { name: "Add Component" }).click();
        await page.getByLabel("Create new componentCraft").check();
        await page.getByRole("button", { name: "Get Started" }).click();
        await expect(page.getByLabel("Settlement")).toHaveCount(0);
        await page
          .getByLabel("Close", { exact: true })
          .getByRole("button")
          .click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();
      });
      

      test("Test to check forecast plans not visible under these sections", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        // Checking in report enrichment section
        await page.goto("/settings", { waitUntil: "networkidle" });
        await page
          .getByRole("link", { name: "Report Enrichment Enrich the" })
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Select Commission Plan$/ })
          .nth(2)
          .click();
        // Commission plan is visible
        await expect(page.getByText("Canvas plan 1")).toBeVisible();
        // Forecast plan is not visible
        await expect(page.getByText("Plan 6")).toBeHidden();
        await page
          .locator("div")
          .filter({ hasText: /^Select Commission Plan$/ })
          .nth(2)
          .click();

        // Checking in plans section
        await page.goto("/plans", { waitUntil: "networkidle" });
        await expect(page.getByText("Canvas plan 1")).toBeVisible();
        await expect(page.getByText("Plan 6")).toBeHidden();

        // Checking in dashboards section
        await page.goto("/dashboards?type=all", { waitUntil: "networkidle" });
        await page
         
          .getByText("playwright-test-plan-2 Metrics")
          .click();
        await page.waitForTimeout(3000);
        await page
          .locator(".flex > div > .ant-select > .ant-select-selector")
          .first()
          .click();

        await expect(
          page.getByText("All Commission Plans").nth(1)
        ).toBeVisible();
        await expect(page.getByText("Canvas plan 1")).toBeVisible();
        await expect(page.getByText("Plan 6")).toBeHidden();
        await page
          .locator(
            ".bg-ever-base > div > div:nth-child(2) > div > div > .ant-select > .ant-select-selector"
          )
          .first()
          .click();
        await expect(
          page.getByText("All Commission Plans").nth(3)
        ).toBeVisible();
        await expect(page.getByText("Canvas plan 1").nth(1)).toBeVisible();
        await expect(page.getByText("Plan 6")).toBeHidden();

        // Checking super admin user's dashboard
        await page.goto("/users", { waitUntil: "networkidle" });
        await page
          .getByTestId(
            "<EMAIL> users dd button"
          )
          .click();
        await page.getByRole("button", { name: "Login as user" }).click();
        await page.waitForTimeout(2000);
        await page.waitForSelector("text=Payouts", { visible: true });
        await page
          .locator(".flex > div > .ant-select > .ant-select-selector")
          .first()
          .click();
        await expect(
          page.getByText("All Commission Plans").nth(1)
        ).toBeVisible();
        await expect(page.getByText("Canvas plan 1")).toBeVisible();
        await expect(page.getByText("Plan 6")).toBeHidden();
        await page
          .locator(
            ".bg-ever-base > div > div:nth-child(2) > div > div > .ant-select > .ant-select-selector"
          )
          .first()
          .click();
        await expect(
          page.getByText("All Commission Plans").nth(3)
        ).toBeVisible();
        await expect(page.getByText("Canvas plan 1").nth(1)).toBeVisible();
        await expect(page.getByText("Plan 6")).toBeHidden();
        await page
          .locator(
            ".bg-ever-base > div > div:nth-child(2) > div > div > .ant-select > .ant-select-selector"
          )
          .first()
          .click();
        await page.getByRole("button", { name: "Exit" }).click();
      });

      test("Test to clone from commission plan to forecast and check operations", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        // Create draft commission plan and clone it to forecast
        await page.goto("/plans", { waitUntil: "networkidle" });
        await page.locator(".absolute > .ant-btn").click();
        await page.getByText("With Payees").click();
        await page
          .locator("div")
          .filter({ hasText: /^DraftMonthly$/ })
          .getByRole("button")
          .click();
        await page.getByPlaceholder("Enter name").click();
        await page.getByPlaceholder("Enter name").click();
        await page.getByPlaceholder("Enter name").fill("Canvas plan 2");
        await page.getByRole("button", { name: "Update" }).click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();
        await page.getByPlaceholder("Search by plan name").click();
        await page
          .getByPlaceholder("Search by plan name")
          .fill("Canvas plan 2");
        await page.waitForTimeout(2000);
        await page.locator(".absolute > .ant-btn").click();
        await page.getByText("Add to Forecast").click();
        await expect(
          page.getByText("Canvas plan 2_Copy successfully added")
        ).toBeVisible();
        const page1Promise = page.waitForEvent("popup");
        await page.getByRole("link", { name: "Take me there" }).click();
        const page1 = await page1Promise;

        // Clone forecast plan
        await page1.getByPlaceholder("Search by plan name").click();
        await page1.getByPlaceholder("Search by plan name").fill("canvas");
        await expect(page1.getByText("Canvas plan 2")).toBeVisible();
        await page.waitForTimeout(4000);
        await page1.locator(".absolute > .ant-btn").click();
        await page1.getByText("Clone Forecast").click();
        await expect(
          page1.getByText("Canvas plan 2_Copy_Copy").nth(1)
        ).toBeVisible();
        await page1.getByRole("button", { name: "Exit Canvas" }).click();

        // Delete cloned forecast plan
        await page1.getByPlaceholder("Search by plan name").click();
        await page1
          .getByPlaceholder("Search by plan name")
          .fill("canvas plan 2_Copy_Copy");
        await page.waitForTimeout(2000);
        await page1.locator(".absolute > .ant-btn").click();
        await page1.getByText("Delete").click();
        await expect(
          page1.getByText(
            'Are you sure you want to delete "Canvas plan 2_Copy_Copy" commission plan?'
          )
        ).toBeVisible();
        await page1.getByRole("button", { name: "Yes, delete" }).click();
        await expect(
          page1.getByText("Canvas plan 2_Copy_Copy deleted successfully!")
        ).toBeVisible();
        await page1.getByLabel("close-circle").click();

        // Delete cloned commission plan but that shouldnt delete forecast plan
        await page1.goto("/plans", { waitUntil: "networkidle" });
        await page1.getByPlaceholder("Search by plan name").click();
        await page1
          .getByPlaceholder("Search by plan name")
          .fill("canvas plan 2");
        await page.waitForTimeout(2000);
        await page1.locator(".absolute > .ant-btn").click();
        await page1.getByText("Delete").click();
        await expect(
          page1.getByText(
            'Are you sure you want to delete "Canvas plan 2" commission plan?'
          )
        ).toBeVisible();
        await page1.getByRole("button", { name: "Yes, delete" }).click();
        await expect(
          page1.getByText("Canvas plan 2 deleted successfully!").first()
        ).toBeVisible();
        await page1.getByLabel("close-circle").click();
        await expect(page1.getByText("Canvas plan 2")).toHaveCount(0);
        await page1.goto("/forecasts", { waitUntil: "networkidle" });
        await page1.getByPlaceholder("Search by plan name").click();
        await page1.getByPlaceholder("Search by plan name").fill("canvas");
        await expect(page1.getByText("Canvas plan 2")).toHaveCount(1);
      });
      

      
  }
);
*/

test2(
  "Test to check if forecast option is absent - 10306",
  { tag: ["@forecast", "@regression", "@primelogic-4"] },
  async ({ adminPage }) => {
    const page = adminPage.page;
    await page.goto("/plans", { waitUntil: "networkidle" });
    const commIcon = await page.locator("#Commission");
    await commIcon.hover();
    await expect2(page.getByRole("link", { name: "Forecasts" })).toBeHidden();
  }
);

test3(
  "Test to check forecast is in canvas mode even if plans version is not canvas",
  { tag: ["@forecast", "@regression", "@primelogic-4"] },
  async ({ adminPage }) => {
    const page = adminPage.page;
    await expect3(await page.locator("button.ant-btn-ghost")).toBeVisible();
    await page.goto("/plans", { waitUntil: "networkidle" });
    await expect3(await page.locator("button.ant-btn-ghost")).toBeHidden();
  }
);

testSa(
  "Test to check permissions for super admin in cloning and deleting plans",
  { tag: ["@forecast", "@regression", "@primelogic-4"] },
  async ({ adminPage }) => {
    // Super Admin plan cloning
    const superAdminPage = adminPage.page;
    await superAdminPage.goto("/forecasts", { waitUntil: "networkidle" });
    await superAdminPage.getByPlaceholder("Search by plan name").click();
    await superAdminPage
      .getByPlaceholder("Search by plan name")
      .fill("intermediate");
    await expectSa(
      superAdminPage.getByText("Plan Intermediate only components")
    ).toBeVisible();
    await superAdminPage.waitForTimeout(2000);
    await superAdminPage
      .getByTestId("pt-actions-Plan Intermediate only components")
      .click();
    await superAdminPage.getByText("Clone Forecast").click();
    await expectSa(
      superAdminPage.getByText("Plan Intermediate only components_Copy").nth(1)
    ).toBeVisible();
    await superAdminPage.getByRole("button", { name: "Exit Canvas" }).click();

    // Admin delete published forecast plan
    await superAdminPage.getByPlaceholder("Search by plan name").click();
    await superAdminPage
      .getByPlaceholder("Search by plan name")
      .fill("Plan Intermediate only components");
    await superAdminPage.waitForTimeout(2000);
    await superAdminPage
      .getByTestId("pt-actions-Plan Intermediate only components")
      .click();
    await expectSa(superAdminPage.locator('text="Delete"')).toBeHidden();
    await superAdminPage.getByPlaceholder("Search by plan name").click();
    await superAdminPage
      .getByPlaceholder("Search by plan name")
      .fill("Intermediate");
    await expectSa(
      superAdminPage.getByText("Plan Intermediate only components")
    ).toHaveCount(2);
  }
);
