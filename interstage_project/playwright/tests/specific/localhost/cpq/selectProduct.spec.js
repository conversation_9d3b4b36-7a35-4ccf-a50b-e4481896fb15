import { expect } from "@playwright/test";
import OpportunityPage from "../../../../test-objects/cpq/opportunitiesPage-objects.js";
import QuoteListPage from "../../../../test-objects/cpq/quotesListPage-objects.js";
import ProductPage from "../../../../test-objects/cpq/selectProductPage-objects.js";
import SettingsPage from "../../../../test-objects/cpq/settingsPage-objects.js";

const testData = JSON.parse(
  JSON.stringify(require("../../../../testData/testData.json"))
);

const {
  cpqTestFixtures: { test },
} = require("../../../fixtures.js");

test.beforeAll(async ({ adminPage }) => {
  const quoteListPage = new QuoteListPage(adminPage.page);
  await quoteListPage.openApp("/cpq/quotes");
  const settingsPage = new SettingsPage(adminPage.page);
  await settingsPage.refreshDatasheets();
});

test.describe(
  "CPQ - Select product",
  { tag: ["@regression", "@cpq-1"] },
  () => {
    test("Validate user is able to add only selected products", async ({
      adminPage,
    }, testInfo) => {
      testInfo.annotations.push(
        { type: "Test ID", description: "INTER-T25564, INTER-T25690" },
        {
          type: "Description",
          description: "Validate user is able to add selected products",
        },
        {
          type: "Precondition",
          description: "None",
        },
        {
          type: "Expected Behaviour",
          description: "User should be able to add only selected products",
        }
      );
      const quoteListPage = new QuoteListPage(adminPage.page);
      testInfo.setTimeout(testInfo.timeout + 800000);
      await quoteListPage.openApp("/cpq/quotes");
      await quoteListPage.clickCreate();
      await quoteListPage.clickNewquote();
      await quoteListPage.selectForm();
      await quoteListPage.clickContinue();
      const opportunityPage = new OpportunityPage(adminPage.page);
      await opportunityPage.verifySuccessMessage(
        testData.successMessageCreateQuote
      );
      await opportunityPage.searchAndSelectQuote("Facebook");

      const productPage = new ProductPage(adminPage.page, {
        productNames: ["Connector Charges", "Everstage Platform"],
        pricePointOpt:
          testData["Everstage Platform"]["initial"].pricePointOption,
        colHead: "product_name",
        rowHeads: [testData.rowHeads.CONNECTOR, testData.rowHeads.PLATFORM],
      });
      await productPage.clickSelectProduct();
      await productPage.addProduct();
      await productPage.selectProducts();
      await productPage.clickAdd();
      const { products: initialProducts, expectedProducts } =
        await productPage.validateAddedProducts();
      console.log(`Added products: ${initialProducts}`);
      expect(initialProducts).toEqual(expectedProducts);
      console.log(
        `Order of product before updating the quantity: ${initialProducts}`
      );
      // Update details for each product
      await productPage.updateProductDetails(testData);
      const { products: updatedProducts } =
        await productPage.validateAddedProducts();
      console.log(
        `Order of product after updating the quantity: ${updatedProducts}`
      );
      const duration = await productPage.getDurationForAddedProducts();
      console.log(`Duration for added products: ${duration}`);
    });

    test("Validate total value and user is able to add values in bulk discount option", async ({
      adminPage,
    }, testInfo) => {
      testInfo.annotations.push(
        {
          type: "Test ID",
          description: "INTER-T25563, INTER-T25686, INTER-T25582, INTER-T26671",
        },
        {
          type: "Description",
          description: "Add discount value via 'Bulk Discount' option",
        },
        {
          type: "Precondition",
          description: "None",
        },
        {
          type: "Expected Behaviour",
          description: "User should be able to input discount",
        }
      );
      const quoteListPage = new QuoteListPage(adminPage.page);
      testInfo.setTimeout(testInfo.timeout + 800000);
      await quoteListPage.openApp("/cpq/quotes");
      await quoteListPage.clickCreate();
      await quoteListPage.clickNewquote();
      await quoteListPage.selectForm();
      await quoteListPage.clickContinue();
      const opportunityPage = new OpportunityPage(adminPage.page);
      await opportunityPage.verifySuccessMessage(
        testData.successMessageCreateQuote
      );
      await opportunityPage.searchAndSelectQuote("pepsico");
      const productPage = new ProductPage(adminPage.page, {
        productNames: [
          "Connector Charges",
          "Everstage Platform",
          "Standard White Glove Support",
          "One-time Implementation",
          "Premium White Glove Supporter",
        ],
        pricePointOpt:
          testData["Everstage Platform"]["initial"].pricePointOption,
        colHead: "net_unit_price",
        rowHeads: [testData.rowHeads.CONNECTOR],
      });
      await productPage.clickSelectProduct();
      await productPage.addProduct();
      await productPage.selectProducts();
      await productPage.clickAdd();
      await productPage.updateProductDetails(testData);
      for (const discount of testData["bulk discount"]) {
        await productPage.updateBulkDiscount(discount);
        const updatedDiscount = await productPage.validateDiscountValue();
        expect(updatedDiscount).toBe(discount);
        console.log("Total quote value for different discount price: ");
        for (const value of testData["Quote value"]) {
          await productPage.validateTotalValueDisplayed(value);
        }
        console.log("********");
      }
    });

    test("Validate not be able to publish the quote with no products", async ({
      adminPage,
    }, testInfo) => {
      testInfo.annotations.push(
        {
          type: "Test ID",
          description: "INTER-T25584",
        },
        {
          type: "Description",
          description: "Create and publish quote without adding products",
        },
        {
          type: "Expected Behaviour",
          description: "User should not be able to publish the quote",
        }
      );
      const quoteListPage = new QuoteListPage(adminPage.page);
      testInfo.setTimeout(testInfo.timeout + 800000);
      await quoteListPage.openApp("/cpq/quotes");
      await quoteListPage.clickCreate();
      await quoteListPage.clickNewquote();
      await quoteListPage.selectForm();
      await quoteListPage.clickContinue();
      const opportunityPage = new OpportunityPage(adminPage.page);
      await opportunityPage.verifySuccessMessage(
        testData.successMessageCreateQuote
      );
      await opportunityPage.searchAndSelectQuote("hp");
      await opportunityPage.clickPublishButton();
      await opportunityPage.isRedAlertVisible();
      console.log("Red alert appeared as expected.");
    });

    test("Validate : List unit price, net unit price and duration", async ({
      adminPage,
    }, testInfo) => {
      testInfo.annotations.push(
        {
          type: "Test ID",
          description: "INTER-T25583, INTER-T25687",
        },
        {
          type: "Description",
          description: "Add products and validate columns",
        },
        {
          type: "Expected Behaviour",
          description:
            "List unit price, net unit price and duration should be displayed for each line item",
        }
      );
      const quoteListPage = new QuoteListPage(adminPage.page);
      testInfo.setTimeout(testInfo.timeout + 800000);
      await quoteListPage.openApp("/cpq/quotes");
      await quoteListPage.clickCreate();
      await quoteListPage.clickNewquote();
      await quoteListPage.selectForm();
      await quoteListPage.clickContinue();
      const opportunityPage = new OpportunityPage(adminPage.page);
      await opportunityPage.verifySuccessMessage(
        testData.successMessageCreateQuote
      );
      const generatedQuoteName = await opportunityPage.createNewQuote();

      const productPage = new ProductPage(adminPage.page, {
        productNames: [
          "Connector Charges",
          "Everstage Platform",
          "Standard White Glove Support",
          "One-time Implementation",
          "Premium White Glove Supporter",
        ],
        pricePointOpt:
          testData["Everstage Platform"]["initial"].pricePointOption,
        colHead: "net_unit_price",
        rowHeads: [
          testData.rowHeads.CONNECTOR,
          testData.rowHeads.PLATFORM,
          testData.rowHeads.SUPPORT,
          testData.rowHeads.IMPLEMENTATION,
          testData.rowHeads.PREMIUMSUPPORT,
        ],
      });

      await productPage.clickSelectProduct();
      await productPage.addProduct();
      await productPage.selectProducts();
      await productPage.clickAdd();
      await productPage.updateProductDetails(testData);
      const productValues = await productPage.getProductValues([
        "quantity",
        "list_unit_price",
        "net_unit_price",
      ]);
      console.log(productValues);
      const duration = await productPage.getDurationForAddedProducts();
      console.log(`Duration for added products: ${duration}`);
    });

    test("Validate delete product and add same product", async ({
      adminPage,
    }, testInfo) => {
      testInfo.annotations.push(
        {
          type: "Test ID",
          description: "INTER-T25565, INTER-T25574",
        },
        {
          type: "Description",
          description: "Create and publish quote without adding products",
        },
        {
          type: "Expected Behaviour",
          description: "User should not be able to publish the quote",
        }
      );
      const quoteListPage = new QuoteListPage(adminPage.page);
      testInfo.setTimeout(testInfo.timeout + 800000);
      await quoteListPage.openApp("/cpq/quotes");
      await quoteListPage.clickCreate();
      await quoteListPage.clickNewquote();
      await quoteListPage.selectForm();
      await quoteListPage.clickContinue();
      const opportunityPage = new OpportunityPage(adminPage.page);
      await opportunityPage.verifySuccessMessage(
        testData.successMessageCreateQuote
      );
     await opportunityPage.createNewQuote();

      const productPage = new ProductPage(adminPage.page, {
        productNames: ["Connector Charges", "Everstage Platform"],
        pricePointOpt:
          testData["Everstage Platform"]["initial"].pricePointOption,
        colHead: "product_name",
        rowHeads: [testData.rowHeads.CONNECTOR],
      });
      await productPage.clickSelectProduct();
      await productPage.addProduct();
      await productPage.selectProducts();
      await productPage.clickAdd();
      const { products: addedProducts1 } =
        await productPage.validateAddedProducts();
      console.log(`Added products: ${addedProducts1}`);
      await productPage.deleteProduct();
      console.log(`Products avilable after deleting a product`);
      // create another obj to add same product
      const productPage3 = new ProductPage(adminPage.page, {
        productNames: ["Connector Charges"],
        pricePointOpt:
          testData["Everstage Platform"]["initial"].pricePointOption,
        colHead: "product_name",
        rowHeads: [testData.rowHeads.CONNECTOR],
      });
      const { products: addedProducts2 } =
        await productPage3.validateAddedProducts();
      console.log(`Added products: ${addedProducts2}`);

      // create another obj to add same product
      const productPage1 = new ProductPage(adminPage.page, {
        productNames: ["Connector Charges"],
        pricePointOpt:
          testData["Everstage Platform"]["initial"].pricePointOption,
        colHead: "product_name",
        rowHeads: [testData.rowHeads.CONNECTOR],
      });
      await productPage1.addProductFromRow();
      await productPage1.selectProducts();
      await productPage1.clickAdd();
      // Validate whether deleted product has been added
      const productPage2 = new ProductPage(adminPage.page, {
        productNames: ["Connector Charges", "Everstage Platform"],
        pricePointOpt:
          testData["Everstage Platform"]["initial"].pricePointOption,
        colHead: "product_name",
        rowHeads: [testData.rowHeads.CONNECTOR],
      });
      const { products: addedProducts, expectedProducts } =
        await productPage2.validateAddedProducts();
      console.log(`Products avilable after adding same product`);
      console.log(`Added products: ${addedProducts}`);
      expect(addedProducts).toEqual(expectedProducts);
    });

    test("Validate user is able to edit product quantity and price", async ({
      adminPage,
    }, testInfo) => {
      testInfo.annotations.push(
        {
          type: "Test ID",
          description: "INTER-T25570, INTER-T25569, INTER-T25580",
        },
        {
          type: "Description",
          description: "Add products and validate columns",
        },
        {
          type: "Expected Behaviour",
          description: "User should be able to edit the price and quantity",
        }
      );
      const quoteListPage = new QuoteListPage(adminPage.page);
      testInfo.setTimeout(testInfo.timeout + 800000);
      await quoteListPage.openApp("/cpq/quotes");
      await quoteListPage.clickCreate();
      await quoteListPage.clickNewquote();
      await quoteListPage.selectForm();
      await quoteListPage.clickContinue();
      const opportunityPage = new OpportunityPage(adminPage.page);
      await opportunityPage.verifySuccessMessage(
        testData.successMessageCreateQuote
      );
      const generatedQuoteName = await opportunityPage.createNewQuote();

      const productPage = new ProductPage(adminPage.page, {
        productNames: [
          "Connector Charges",
          "Everstage Platform",
          "Standard White Glove Support",
          "One-time Implementation",
          "Premium White Glove Supporter",
        ],
        pricePointOpt:
          testData["Everstage Platform"]["initial"].pricePointOption,
        colHead: "net_unit_price",
        rowHeads: [
          testData.rowHeads.CONNECTOR,
          testData.rowHeads.PLATFORM,
          testData.rowHeads.SUPPORT,
          testData.rowHeads.IMPLEMENTATION,
          testData.rowHeads.PREMIUMSUPPORT,
        ],
      });
      await productPage.clickSelectProduct();
      await productPage.addProduct();
      await productPage.selectProducts();
      await productPage.clickAdd();
      await productPage.updateProductDetails(testData);
      const productValues = await productPage.getProductValues([
        "quantity",
        "list_unit_price",
        "net_unit_price",
      ]);
      console.log("Initial values: ");
      console.log(productValues);
      // Edit price and quantity
      await productPage.updateProductDetails(testData, "updated");
      const updatedValues = await productPage.getProductValues([
        "quantity",
        "list_unit_price",
        "net_unit_price",
      ]);
      console.log("Updated values: ");
      console.log(updatedValues);
    });

    test("Validate the quote value under each phase", async ({
      adminPage,
    }, testInfo) => {
      testInfo.annotations.push(
        { type: "Test ID", description: "INTER-T25581" },
        {
          type: "Description",
          description: "Validate the quote value under each phase",
        },
        {
          type: "Precondition",
          description: "Login to an CPQ Client",
        },
        {
          type: "Expected Behaviour",
          description:
            "Total quote value of the phase should be present under each phase",
        }
      );
      const quoteListPage = new QuoteListPage(adminPage.page);
      testInfo.setTimeout(testInfo.timeout + 800000);
      await quoteListPage.openApp("/cpq/quotes");
      await quoteListPage.clickCreate();
      await quoteListPage.clickNewquote();
      await quoteListPage.selectForm();
      await quoteListPage.clickContinue();
      const opportunityPage = new OpportunityPage(adminPage.page);
      await opportunityPage.verifySuccessMessage(
        testData.successMessageCreateQuote
      );
      await opportunityPage.fillQuoteName("Test_Quote");
      await opportunityPage.searchAndSelectQuote(testData.searchOpp);
      const productPage = new ProductPage(adminPage.page, {
        productNames: [
          "Connector Charges",
          "Everstage Platform",
          "Standard White Glove Support",
          "One-time Implementation",
          "Premium White Glove Supporter",
        ],
        pricePointOpt:
          testData["Everstage Platform"]["initial"].pricePointOption,
        colHead: "product_name",
        rowHeads: [
          testData.rowHeads.CONNECTOR,
          testData.rowHeads.PLATFORM,
          testData.rowHeads.SUPPORT,
          testData.rowHeads.IMPLEMENTATION,
          testData.rowHeads.PREMIUMSUPPORT,
        ],
      });
      await productPage.clickSelectProduct();
      await productPage.addProduct();
      await productPage.selectProducts();
      await productPage.clickAdd();
      await productPage.updateProductDetails(testData);

      await quoteListPage.openApp("/cpq/quotes");
      await quoteListPage.searchQuote("Test_Quote");
      await quoteListPage.clickOnQuote("Test_Quote");

      await productPage.clickSelectProduct();
      await productPage.clickDuration();
      await productPage.setSubscriptionPeriod(
        testData.t25581.from,
        testData.t25581.to
      );
      await productPage.setPeriodValue(
        testData.t25581.durationType,
        testData.t25581.value
      );
      await productPage.validateSubTotal();
    });
  }
);
