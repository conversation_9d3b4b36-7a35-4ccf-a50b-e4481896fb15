import CommissionsSyncPrevPeriod from "../../../../../test-objects/commissionSyncPrevPeriod-objects";
import AdjustementsV2Page from "../../../../../test-objects/adjustments-v2-objects";
import csvParse from "csv-parse/sync";

const fs = require("fs");
const path = require("path");

const {
  adjustmentv2Fixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.addInitScript(() => {
    localStorage.clear();
    sessionStorage.clear();
  });
  const exitButton = await page.getByRole("button", {
    name: "Exit",
    exact: true,
  });
  if (await exitButton.isVisible()) {
    await exitButton.click();
  }
});
test.describe(
  "Adjustments V2 Automation",
  { tag: ["@commissionAdjustments", "@regression", "@primelogic-3"] },
  () => {
    test(
      "Commission Adjustment V2 Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T17819, INTER-T17821, INTER-T24571",
          },
          {
            type: "Description",
            description:
              "Validate whether the user is able to make an adjustment in Commission Adjustment V2 and also check whether the adjustment is reflected in statements",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "Users should be able to make an adjustments in Commission Adjustment V2, and it should be reflected on the statements screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");
        // Checking whether the total number of adjustments are displayed in the listing page (Empty Adjustments)
        expect(
          await page
            .locator("span.text-xs:near(:text('Commission'))")
            .last()
            .innerText()
        ).toBe("00");
        await adjustmentV2.addCommissionAdjustment(
          "Payee 1",
          "January 2024",
          "5000",
          "Commission_plan_Payee_1(2024)",
          "Simple",
          "Calculation Issue",
          "10",
          "This is a test reason"
        );
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Jan 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Adjustments");

        const amountADV = await adjustmentV2.getAdjustedAmount();

        try {
          assert.strictEqual(
            amountADV,
            "₹5,000.00",
            "Adjustment isn't created and and reflected in the statements"
          );
          console.log(
            "Adjustment is created successfully and is also reflected in the statements"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Mandatory Fields Validation in Commission Adjustment Modal",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17825" },
          {
            type: "Description",
            description:
              "Validate whether commission adjustments can be made without filling in the mandatory fields",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "On clicking the Submit button without filling mandatory fields, the un-filled data should be populated with a warning",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickCommissionAdjustmentBtn();
        await adjustmentV2.clickSubmitBtn();
        await page.waitForTimeout(3000);

        const allAlerts = await adjustmentV2.getErrorMessages();

        const Checks = [
          "Please select a payee!",
          "Please select a period!",
          "Please select a currency",
          "Amount is required",
        ];
        const allPresent = Checks.every((item) => allAlerts.includes(item));

        try {
          assert.strictEqual(
            allPresent,
            true,
            "User is able to create Adjustments without filling all mandatory data"
          );
          console.log(
            "On clicking the Submit button without filling mandatory fields, the un-filled data should be populated with a warning "
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "All payout periods (Monthly, Quarterly, Half-Yearly, Annually and Custom-Calendar) selection validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17828" },
          {
            type: "Description",
            description:
              "Validate whether the user is able to select all payout periods, including custom calendar periods, from the Select Period date picker in Commission Adjustement V2",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to select all the payout periods from the Select Period Date picker in the Adjustments page",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");

        // Quarterly Period
        await adjustmentV2.selectPeriodAdjustment("31 Oct 2024");

        // Custom Calendar Period
        await adjustmentV2.selectPeriodAdjustment("08 Jan 2024");

        // Half Yearly Period
        await adjustmentV2.selectPeriodAdjustment("30 Jun 2024");

        // Annual Period
        await adjustmentV2.selectPeriodAdjustment("31 Dec 2024");
      }
    );

    test(
      "Custom Calendar Adjustment Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17826" },
          {
            type: "Description",
            description:
              "Validate whether the user is able to make adjustments for Custom Calendar periods in Commission Adjustments V2",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "The adjustment should be reflected for payees with a Custom Calendar",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.addCommissionAdjustment(
          "Payee Week",
          "Jan 02, 2024 - Jan 08, 2024",
          "4500.765",
          "Custom_Cal_Period_Plan ( Jan 02, 24 - Jan 08, 24)",
          "Simple_CR",
          "CRM Issue",
          "4",
          "Weekly Commission Error"
        );
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "08 Jan 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Adjustments");

        const amountADVCust = await adjustmentV2.getAdjustedAmount();

        try {
          assert.strictEqual(
            amountADVCust,
            "£4,500.77",
            "Adjustment from Custom Calendar Period isn't created and and reflected in the statements"
          );
          console.log(
            "Adjustment from Custom Calendar Period is created successfully and is also reflected in the statements"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Commission Adjustments for all types of Effective Periods (Monthly, Quarterly, Half-Yearly, and Annually) Validation, and No data",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T17823,INTER-T22527,INTER-22534",
          },
          {
            type: "Description",
            description:
              "Validate the ability to make adjustments for all types of Effective Periods (Monthly, Quarterly, Half-Yearly, and Annually) in V2, and also check if it remains empty if the payee has no statement periods",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "After filling all the mandatory details, user should be able to create Adjustment / Draw Adjustment in V2 for Monthly/Quarterly/Half-Yearly/Annual Payout Frequency Payee, and display 'No data' if there is none",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");

        // Quarterly Period Adjustment
        await adjustmentV2.addCommissionAdjustment(
          "Payee Quar",
          "Q4 (Oct 2024 - Dec 2024)",
          "3529.99",
          "Quarterly_plan",
          "Simple_criteria",
          "Others",
          "2",
          "Quarterly Adjustment"
        );
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        // Half-Yearly Period Adjustment
        await adjustmentV2.addCommissionAdjustment(
          "Payee Half",
          "H1 (Jan 2024 - Jun 2024)",
          "5678.90",
          "Half_Yearly_Plan (Jan to Jun)",
          "Simple_Rule",
          "Calculation Issue",
          "12",
          "Half-Yearly Period Adjustment"
        );
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        // Annual Period Adjustment
        await adjustmentV2.addCommissionAdjustment(
          "Payee Annual",
          "2024",
          "100000",
          "Commission_Annual_plan",
          "Conditional_Criteria",
          "Calculation Issue",
          "5",
          "Annual Period Adjustment"
        );
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        await page.waitForTimeout(4000);

        const allAdjs = await adjustmentV2.getAllAdjustments();

        const Checks = ["Payee Half", "Payee Quar", "Payee Annual"];
        const allPresent = Checks.every((item) => allAdjs.includes(item));

        try {
          assert.strictEqual(
            allPresent,
            true,
            "User is not able to create Adjustments of all Periods (Quarterly, Half-Yearly and Annually)"
          );
          console.log(
            "User is able to create Adjsutments of all Periods (Quarterly, Half-Yearly and Annually)"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
        // check for No period, No Component(Only for component before selecting payee and after selecting payee)
        await adjustmentV2.clickCommissionAdjustmentBtn();
        await adjustmentV2.clickComponentDropdown();
        await expect(
          page
            .locator('[id="commission\\ form_criteriaId_list"]')
            .getByText("No Data")
        ).toBeVisible();
        await adjustmentV2.addPayee("Boss Payee Half");
        await adjustmentV2.clickEffectivePeriodDropdown();
        await expect(
          page
            .locator('[id="commission\\ form_effectivePeriod_list"]')
            .getByText("No Data")
        ).toBeVisible();
        await adjustmentV2.clickComponentDropdown();
        await expect(
          page
            .locator('[id="commission\\ form_criteriaId_list"]')
            .getByText("No Data")
        ).toBeVisible();
      }
    );

    test(
      "Payee currency Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T17837,INTER-T22528,INTER-22538",
          },
          {
            type: "Description",
            description:
              "Validate whether the payee's currency is correctly reflected for adjustments on the statements page, and amount drop down remains empty when no payee is selected",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "The payee currency should be reflected accurately in the adjustements, and 'No data' should be displayed when no payee is selected",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.addCommissionAdjustment(
          "Sample Payee E2E",
          "July 2024",
          "1250",
          "Settlement_Plan",
          "Simple_Criteria",
          "CRM Issue",
          "3",
          "Payee Currency Check"
        );
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Jul 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Adjustments");

        const amountADV = await adjustmentV2.getAdjustedAmountNonSettlement();

        try {
          assert.strictEqual(
            amountADV,
            "€1.250,00",
            "Payee currency formatting is not applied to adjustments created and displayed in the statements"
          );
          console.log(
            "Payee currency formatting is correctly applied to adjustments created and displayed in the statements"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
        // Check Amount dropdown remains empty when no payee is selected
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickCommissionAdjustmentBtn();
        await page.locator('[id="commission\\ form_amount_currency"]').click();
        await page.waitForTimeout(1000);
        await expect(page.getByText("No Data")).toBeVisible();
        // Check change from Payee currency to global currency
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickCommissionAdjustmentBtn();
        await adjustmentV2.addPayee("Payee 1");
        await adjustmentV2.selectEffectivePeriod("January 2024");
        await page.waitForTimeout(1000);
        await page.getByTitle("INR").click();
        await page.locator("span").filter({ hasText: "USD" }).click();
        await expect(
          page.getByLabel("Add Adjustment").getByTitle("USD")
        ).toBeVisible();
      }
    );

    test(
      "Adding and Editing Adjustments on a locked statement Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17834" },
          {
            type: "Description",
            description:
              "Validate whether adjustments can be added and edited, and confirm if the updated config is displayed in the statements in V2 when the statement is locked",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "The user should be able to add adjustments on a locked statement and edited or updated configuration should be reflected in the statements screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Oct 2024");
        await adjustmentV2.lockStatements("<EMAIL>");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.addCommissionAdjustment(
          "Payee 1",
          "October 2024 (commission locked)",
          "8999.90",
          "Commission_plan_Payee_1(2024)",
          "Simple",
          "CRM Issue",
          "7",
          "This is an Editable Adjustment"
        );
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Oct 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Adjustments");

        const amountBeforeEdit =
          await adjustmentV2.getAdjustedAmountNonSettlement();

        await adjustmentV2.navigate("/settings/adjustments");

        await adjustmentV2.filterByCommissionAmount("8999.90");
        await page.waitForTimeout(3000);

        await adjustmentV2.clickEditAdjustments();
        await adjustmentV2.editAmount("8999.999000");
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentUpdateValidation();

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Oct 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Adjustments");

        const amountAfterEdit =
          await adjustmentV2.getAdjustedAmountNonSettlement();

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Oct 2024");
        await adjustmentV2.unlockStatements("<EMAIL>");

        try {
          assert.strictEqual(
            amountBeforeEdit,
            "₹8,999.90",
            "Adjustment can't be created when the statement is locked"
          );
          assert.strictEqual(
            amountAfterEdit,
            "₹9,000.00",
            "Adjustment can't be edited when the statement is locked"
          );
          console.log(
            "The adjustment is created and edited successfully when the statement is locked."
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Deletion of Adjustments on the locked statement Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T17835, INTER-T17860, INTER-T24572,INTER-22515,INTER-22542",
          },
          {
            type: "Description",
            description:
              "Validate whether adjustments can be deleted and are removed from the listing when the statement is locked/unlocked. Also validate that negative adjustments are displayed in red, positive adjustment are displayed in green on both the Adjustments and Statements screens",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "The user should be able to delete adjustments on a locked/unlocked statement and edited or updated configuration should be reflected in the statements screen. And negative Adjustments should be displayed in red color, positive should be displayed in green in the Statements as well as in the Adjustments screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Mar 2024");
        await adjustmentV2.lockStatements("<EMAIL>");

        await adjustmentV2.navigate("/settings/adjustments");
        // negative adjustment
        await adjustmentV2.addCommissionAdjustment(
          "Payee Quar",
          "Q1 (Jan 2024 - Mar 2024) (commission locked)",
          "-2500",
          "Quarterly_plan",
          "Simple_criteria",
          "Calculation Issue",
          "1",
          "Deletable Adjustment"
        );
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        // positive adjustment
        await adjustmentV2.addCommissionAdjustment(
          "payee 3",
          "March 2024",
          "2499",
          "payee 3&4 plan",
          "simple",
          "Calculation Issue",
          "1",
          "Deletable Adjustment"
        );
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();
        // Test for negative adjustment being Red(in adjustments)
        await adjustmentV2.searchAdjustments("Payee Quar");
        await expect(
          page.locator("div.text-ever-error-content-lite")
        ).toHaveCSS("color", "rgb(185, 28, 28)");

        await adjustmentV2.navigate("/settings/adjustments");
        // Test for positive adjustment being Green(in adjustments)
        await adjustmentV2.searchAdjustments("payee 3");
        await expect(
          page.locator("div.text-ever-success-content-lite")
        ).toHaveCSS("color", "rgb(21, 128, 61)");
        // Test for negative adjustment being Red(in statements)
        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Mar 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Adjustments");
        await expect(page.locator("div.text-ever-error")).toHaveCSS(
          "color",
          "rgb(220, 38, 38)"
        );
        // Test for positive adjustment being green(in statements)
        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Mar 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Adjustments");
        await expect(page.locator("div.text-ever-success-hover")).toHaveCSS(
          "color",
          "rgb(22, 162, 74)"
        );
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.filterByCommissionAmount("-2500");
        await adjustmentV2.clickDeleteAdjustments();
        await adjustmentV2.deleteAdjustmentValidation();
        await adjustmentV2.clearFilter();
        await adjustmentV2.filterByCommissionAmount("2499");
        await adjustmentV2.clickDeleteAdjustments();
        await adjustmentV2.deleteAdjustmentValidation();
        await adjustmentV2.navigate("/settings/adjustments");

        await page.waitForTimeout(2000);
        // Checking whether the total number of adjustments are displayed in the listing page (On Deletion)
        expect(
          await page
            .locator("span.text-xs:near(:text('Commission'))")
            .last()
            .innerText()
        ).toBe("07");

        const allAmounts = await adjustmentV2.getAllAdjustmentAmounts();

        const checks = ["-₹2,500.00", "₹2,499.00"];
        const deletedAdj = checks.some((item) => !allAmounts.includes(item));

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Mar 2024");
        await adjustmentV2.unlockStatements("<EMAIL>");

        try {
          assert.strictEqual(
            deletedAdj,
            true,
            "Adjustment can't be deleted when the statement is locked"
          );
          console.log(
            "The adjustment is deleted successfully when the statement is locked"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Auto Approval Workflow Validation in Adjustments V2 (Within and Beyond Thresholds)",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17839, INTER-T17840" },
          {
            type: "Description",
            description:
              "Validate whether the approvals were automatically approved by the Auto Approval feature within the specified threshold. Also validate whether the approvals were automatically approved by the Auto Approval feature beyond the specified threshold",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments and Approvals",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Adjustment created should be in the Approved status by default when the adjustment is within the threshold and user should not be allowed to select the auto-approval workflow when the amount is beyond the threshold",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.ToggleApprovalsAdj();
        await adjustmentV2.setApprovalThreshold("1000", "2999");
        await adjustmentV2.thresholdSetValidation();

        await adjustmentV2.navigate("/settings/adjustments");

        // Within Threshold
        await adjustmentV2.addCommissionAdjustment(
          "Payee 1",
          "September 2024",
          "2999",
          "Commission_plan_Payee_1(2024)",
          "Simple",
          "Calculation Issue",
          "8",
          "Auto Approved Adjustment"
        );
        await expect(page.locator("(//input[@type='radio'])[1]")).toBeEnabled();
        await adjustmentV2.selectAutoApproval();
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.filterByCommissionAmount("2999");
        await page.waitForTimeout(3000);

        const empName = await adjustmentV2.getAllEmployees();

        await adjustmentV2.clearAllBtn();

        // Beyond Threshold
        await adjustmentV2.addCommissionAdjustment(
          "Payee 1",
          "August 2024",
          "3000",
          "Commission_plan_Payee_1(2024)",
          "Simple",
          "Calculation Issue",
          "8",
          "Auto Approved Adjustment"
        );
        await expect(
          page.locator("(//input[@type='radio'])[1]")
        ).toBeDisabled();
        await adjustmentV2.closeAdjustmentModal();
        await adjustmentV2.ToggleApprovalsAdj();
        await adjustmentV2.clickConfirmToggleOff();

        try {
          assert.strictEqual(
            empName,
            "Payee 1",
            "The Adjustment aren't auto-approved within the given threshold"
          );
          console.log(
            "The Adjustments are auto-approved within the given threshold"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Changing of adjustment's reason, reason category, or line item ID on a locked statement Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17846" },
          {
            type: "Description",
            description:
              "Validate whether changes to the adjustment's reason, reason category, or line item ID are reflected on a locked statement",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "The change in reason, reason category and line Item ID should be reflected in the locked statement",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "30 Apr 2024");
        await adjustmentV2.lockStatements("<EMAIL>");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.addCommissionAdjustment(
          "Payee 1",
          "April 2024 (commission locked)",
          "7899.90",
          "Commission_plan_Payee_1(2024)",
          "Simple",
          "Others",
          "9",
          "The reason before the Edit"
        );
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "30 Apr 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Adjustments");

        await page.waitForTimeout(2000);

        const reasonBefore = await adjustmentV2.getReason();

        const reasonCategoryBefore = await adjustmentV2.getReasonCategory();

        const lineItemIdBefore = await adjustmentV2.getLineItemId();

        expect(reasonBefore).toBe("The reason before the Edit");
        expect(reasonCategoryBefore).toBe("Others");
        expect(lineItemIdBefore).toBe("9");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.filterByCommissionAmount("7899.90");

        await page.waitForTimeout(3000);
        await adjustmentV2.clickEditAdjustments();
        await adjustmentV2.editLineItemId("6");
        await adjustmentV2.editReasonCategory("Others", "Calculation Issue");
        await adjustmentV2.editReason("The reason after the edit");
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentUpdateValidation();

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "30 Apr 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Adjustments");

        await page.waitForTimeout(2000);

        const reasonAfter = await adjustmentV2.getReason();

        const reasonCategoryAfter = await adjustmentV2.getReasonCategory();

        const lineItemIdAfter = await adjustmentV2.getLineItemId();

        expect(reasonAfter).toBe("The reason after the edit");
        expect(reasonCategoryAfter).toBe("Calculation Issue");
        expect(lineItemIdAfter).toBe("6");

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "30 Apr 2024");
        await adjustmentV2.unlockStatements("<EMAIL>");
      }
    );

    test(
      "Approval Status filter Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17831" },
          {
            type: "Description",
            description:
              "Validate whether the Approval Status filter is working as expected",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments and Aprrovals",
          },
          {
            type: "Expected Behaviour",
            description:
              "The change in reason, reason category and line Item ID should be reflected in the locked statementThe Approval Status should filter the adjustement correctly based on the Approval status (PENDING & ALL)",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.ToggleApprovalsAdj();

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.addCommissionAdjustment(
          "Sample Payee E2E",
          "June 2024",
          "14567",
          "Settlement_Plan",
          "Simple_Criteria",
          "Calculation Issue",
          "10",
          "Approval status Filter Check"
        );
        await adjustmentV2.selectApprovalWorkflow();
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        await adjustmentV2.changeApprovalsFilter();

        const empNamePending = await adjustmentV2.getAllEmployees();
        expect(empNamePending).toBe("Sample Payee E2E");

        await adjustmentV2.ToggleApprovalsAdj();
        await adjustmentV2.clickConfirmToggleOff();

        await adjustmentV2.navigate("/settings/adjustments");

        const allAmounts = await adjustmentV2.getAllAdjustmentAmounts();

        const checks = ["€14,567.00"];
        const pendingADJ = checks.some((item) => !allAmounts.includes(item));

        try {
          assert.strictEqual(
            pendingADJ,
            true,
            "Adjustment Approval status is not working properly"
          );
          console.log("Adjustment Approval status is working as expected");
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Recoverable and Recoverable guarantee draws Adjustments Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17847" },
          {
            type: "Description",
            description:
              "Validate whether the recoverable and recoverable guarantee draws adjustments are reflected in the statements",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments and Draws",
          },
          {
            type: "Expected Behaviour",
            description: "Both the draws should be reflected in the Statements",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/draws");
        await adjustmentV2.addDraws(
          "Payee 1",
          "Jan",
          "2024",
          "Recoverable",
          "2500"
        );
        await adjustmentV2.drawsAddedValidation();

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.addDrawsAdjustment("Payee 1", "2024", "Jan", "1250");
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Jan 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Draw Adjustments");

        const recoverableDrawsAmount = await adjustmentV2.getDrawsAmount();

        await adjustmentV2.navigate("/draws");
        await adjustmentV2.modifyDraws(
          "2024",
          "Payee 1",
          "Recoverable",
          "Recoverable Guarantee"
        );
        await adjustmentV2.drawsAddedValidation();

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.deleteDrawsAdjustment("<EMAIL>", "1250");
        await adjustmentV2.deleteDrawsAdjustmentValidation();

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.addDrawsAdjustment("Payee 1", "2024", "Jan", "1000");
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Jan 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Draw Adjustments");

        const recoverableGuarateeDrawsAmount =
          await adjustmentV2.getDrawsAmount();

        try {
          assert.strictEqual(
            recoverableDrawsAmount,
            "₹1,250.00",
            "Recoverable draws can't be adjusted"
          );
          assert.strictEqual(
            recoverableGuarateeDrawsAmount,
            "₹1,000.00",
            "Recoverable Guarantee draws can't be adjusted"
          );
          console.log(
            "Recoverable and Recoverable guarantee draws adjustments are reflected in the statements succussfully"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Payee currency change Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17838" },
          {
            type: "Description",
            description:
              "Validate the change of payee currency and ensure that the currency is updated for adjustments",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments",
          },
          {
            type: "Expected Behaviour",
            description:
              "The updated payee currency should be reflected in the adjustments screen",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const assert = require("assert");
        testInfo.setTimeout(testInfo.timeout + 900000);

        const csPrev = new CommissionsSyncPrevPeriod(page);
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/users");
        await adjustmentV2.searchUser("Payee Currency Change");
        await adjustmentV2.changePayeeCurrency("EUR", "ALL");

        await csPrev.navigate("/settings/commissions-and-data-sync");
        await csPrev.selectPayees("Payee Currency Change");
        await csPrev.selectDate("31 Jul 2024");
        await csPrev.PrevPeriodCheck();
        await csPrev.runCommissions();
        await expect(page.getByText("Notify when complete")).toHaveCount(1);
        await csPrev.clickSkipandRun();
        await csPrev.waitForCalculationMessage();
        await csPrev.waitForCommissionsSuccess();

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.checkCurrency("Payee Currency Change", "June 2024");

        const getChangedCurrency = await adjustmentV2.getCurrencyValue("ALL");

        try {
          assert.strictEqual(
            getChangedCurrency,
            "ALL",
            "Currency Change is not working"
          );
          console.log("Currency Change is reflected in the Adjustments");
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Multiple currencies in Adjustments and Statements Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17841, INTER-T17842" },
          {
            type: "Description",
            description:
              "Validate that multiple currencies are present and accurately reflected in both the Adjustments and Statements",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments",
          },
          {
            type: "Expected Behaviour",
            description:
              "Post Sync, the user should able to see the multiple currencies in the Adjustment section of statements as well as in the Adjustment Modal",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        testInfo.setTimeout(testInfo.timeout + 900000);
        const csPrev = new CommissionsSyncPrevPeriod(page);
        const adjustmentV2 = new AdjustementsV2Page(page);

        await csPrev.navigate("/settings/commissions-and-data-sync");
        await csPrev.selectPayees("Payee Multiple");
        await csPrev.selectDate("30 Apr 2024");
        await csPrev.PrevPeriodCheck();
        await csPrev.runCommissions();
        await expect(page.getByText("Notify when complete")).toHaveCount(1);
        await csPrev.clickSkipandRun();
        await csPrev.waitForCalculationMessage();
        await csPrev.waitForCommissionsSuccess();

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.checkCurrency("Payee Multiple", "April 2024");

        const getCurrency1 = await adjustmentV2.getCurrencyValue("ALL");
        expect(getCurrency1).toBe("ALL");

        await page.waitForTimeout(2000);
        await adjustmentV2.changeAdjPeriod("April", "March 2024");

        const getCurrency2 = await adjustmentV2.getCurrencyValue("CAD");
        expect(getCurrency2).toBe("CAD");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.addCommissionAdjustment(
          "Payee Multiple",
          "March 2024",
          "8889",
          "Multiple_cuurency_plan",
          "simple_rule",
          "CRM Issue",
          "19",
          "March CAD"
        );
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        await adjustmentV2.addCommissionAdjustment(
          "Payee Multiple",
          "April 2024",
          "5543",
          "Multiple_cuurency_plan",
          "simple_rule",
          "Calculation Issue",
          "19",
          "April ALL"
        );
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "30 Apr 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Adjustments");

        const amountMultipleApr = await adjustmentV2.getAdjustedAmount();
        expect(amountMultipleApr).toBe("AL$5,543.00");

        await adjustmentV2.changeStatementPeriod("April", "March");
        await adjustmentV2.clickComponent("Adjustments");

        const amountMultipleMarch = await adjustmentV2.getAdjustedAmount();
        expect(amountMultipleMarch).toBe("CA$8,889.00");
      }
    );

    test(
      "Change of approval status on a locked statement Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17845" },
          {
            type: "Description",
            description:
              "Validate whether changing the approval status is reflected on a locked statement",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments and Approvals",
          },
          {
            type: "Expected Behaviour",
            description:
              "When the approval status of the adjustment is changed to Pending (Not approved), the adjustment should be removed from the locked statement",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "29 Feb 2024");
        await adjustmentV2.lockStatements("<EMAIL>");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.ToggleApprovalsAdj();
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.addCommissionAdjustment(
          "Payee 1",
          "February 2024 (commission locked)",
          "15500.8989",
          "Commission_plan_Payee_1(2024)",
          "Simple",
          "CRM Issue",
          "11",
          "Pending Approval Adjustment"
        );
        await adjustmentV2.selectApprovalWorkflow();
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();

        await adjustmentV2.navigate("/users");
        await adjustmentV2.impersonatePayee("<EMAIL>");
        await adjustmentV2.navigate(
          "/approvals/commission-adjustments?status=all"
        );
        await adjustmentV2.approveCommissionAdj("Pending Approval Adjustment");
        await adjustmentV2.exitImpersonation();

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "29 Feb 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Adjustments");

        const adjAmount = await adjustmentV2.getAdjustedAmount();
        console.log(adjAmount);

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "29 Feb 2024");
        await adjustmentV2.unlockStatements("<EMAIL>");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.ToggleApprovalsAdj();
        await adjustmentV2.clickConfirmToggleOff();

        try {
          assert.strictEqual(
            adjAmount,
            "₹15,500.90",
            "Upon changing the Adjustment Approval status is not reflected in the statements"
          );
          console.log(
            "Upon changing the Adjustment Approval status is being reflected in the statements"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Commission Report Validation (and whether it becomes stale after the adjustment)",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17848" },
          {
            type: "Description",
            description:
              "Validate whether the Commission Report shows updated Commission amount after making an adjustment (and whether it becomes stale after the adjustment)",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments and Report Object",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Commission sheet should become stale in any form of update (add or update adjustment)",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate(
          "databook/ca5accaf-5fa8-4245-8fd5-28d930fb811d"
        );

        await adjustmentV2.updateDatasheet();
        await adjustmentV2.generateDatasheet();

        const commissionAmount = await adjustmentV2.getvalueDatasheet(
          "amount_payee_currency",
          "15500.8989"
        );

        try {
          assert.strictEqual(
            commissionAmount,
            "15,500.90",
            "The Commission Report is not updated with the Commission value with adjustment"
          );
          console.log(
            "The Commission Report is updated with the Commission value with adjustment"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Commission Summary Report Validation (and whether it becomes stale after the adjustment)",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17849" },
          {
            type: "Description",
            description:
              "Validate whether the Commission Summary Report shows updated Adjustment amount / Draw Adjustment amount after making an adjustment (and whether it becomes stale after the adjustment)",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments and Report Object",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Commission Summary sheet should become stale in any form of update (add or update adjustment)",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate(
          "databook/ca5accaf-5fa8-4245-8fd5-28d930fb811d"
        );
        await adjustmentV2.switchDatasheet("Commission_Summary");
        await adjustmentV2.updateDatasheet();
        await adjustmentV2.generateDatasheet();
        const adjAmount = await adjustmentV2.getvalueDatasheet(
          "comm_adj_for_plan",
          "5000"
        );
        const drawAmount = await adjustmentV2.getvalueDatasheet(
          "draw_adjustment_amount",
          "-1000"
        );

        try {
          assert.strictEqual(
            adjAmount,
            "5,000",
            "The Commission Summary Report is not updated with the Commission value with adjustment"
          );
          assert.strictEqual(
            drawAmount,
            "-1,000",
            "The Commission Summary Report is not updated with the draw adjustment"
          );
          console.log(
            "The Commission Summary Report is updated with the Commission value with adjustments and the draw adjustments"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Total number of adjustments Validation",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Validate that the total number of adjustments updates correctly with the addition of adjustments",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Commission Adjustment numbers should be updated with respect to the addition of an adjustment",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await page.waitForTimeout(2000);

        // Checking whether the total number of adjustments are displayed in the listing page (On Addition)
        expect(
          await page
            .locator("span.text-xs:near(:text('Commission'))")
            .last()
            .innerText()
        ).toBe("12");
      }
    );

    test(
      "Export File Validtion",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T24573, INTER-T24574" },
          {
            type: "Description",
            description:
              "Validate that the exported file contains the Statement Period column along with the Period Start Date and Period End Date",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Exported file should contain Period Start Date, Period End Date as well as the Statement Period column",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await page.getByRole("button", { name: "Import / Export" }).click();

        const downloadPromise = page.waitForEvent("download");
        await page
          .getByRole("button", { name: "Export Adjustments You can" })
          .click();
        const download = await downloadPromise;

        const downloadsPath = path.join(__dirname, "downloads");
        if (!fs.existsSync(downloadsPath)) {
          fs.mkdirSync(downloadsPath);
        }

        const customFileName = "exported_adjustments.csv";
        const filePath = path.join(downloadsPath, customFileName);
        await download.saveAs(filePath);

        // Verify the file exists
        if (!fs.existsSync(filePath)) {
          throw new Error(`File not found at: ${filePath}`);
        }
        console.log("File downloaded successfully");

        const csvData = fs.readFileSync(filePath, "utf-8");
        const records = csvParse.parse(csvData, {
          columns: true,
          skip_empty_lines: true,
        });

        // Validate required headers
        const requiredHeaders = [
          "Period Start Date",
          "Period End Date",
          "Statement Period",
        ];
        const fileHeaders = Object.keys(records[0]);
        requiredHeaders.forEach((header) => {
          if (!fileHeaders.includes(header)) {
            throw new Error(`Missing required column: ${header}`);
          }
        });
        console.log("All required headers are present");

        // Helper function to format dates
        const formatDate = (dateString, formatType) => {
          const date = new Date(dateString);
          const options = { year: "numeric", month: "short", day: "2-digit" };

          switch (formatType) {
            case "long":
              return date
                .toLocaleDateString("en-US", options)
                .replace(/(\d{2}) (\w{3})/, "$2 $1,"); // Jan 02, 2024
            case "short":
              return date.toLocaleDateString("en-US", {
                month: "long",
                year: "numeric",
              }); // January 2024
            case "short-abbr":
              return date.toLocaleDateString("en-US", {
                month: "short",
                year: "numeric",
              }); // Jan 2024
            case "year":
              return date.getFullYear().toString(); // 2024
            default:
              return date.toISOString().split("T")[0]; // Fallback: YYYY-MM-DD
          }
        };

        // Iterate through each row and validate the Statement Period
        for (const {
          "Period Start Date": startDate,
          "Period End Date": endDate,
          "Statement Period": statementPeriod,
        } of records) {
          const formattedStart = formatDate(startDate, "long");
          const formattedEnd = formatDate(endDate, "long");
          const expectedRange = `${formattedStart} - ${formattedEnd}`;
          const expectedMonthYear = formatDate(startDate, "short");
          const expectedYear = formatDate(startDate, "year");

          // Half-Year & Quarter Calculation
          const startMonth = new Date(startDate).getMonth() + 1;
          const endMonth = new Date(endDate).getMonth() + 1;
          const halfYearLabel = startMonth === 1 ? "H1" : "H2";
          const expectedHalfYear =
            (startMonth === 1 && endMonth === 6) ||
            (startMonth === 7 && endMonth === 12)
              ? `${halfYearLabel} (${formatDate(
                  startDate,
                  "short-abbr"
                )} - ${formatDate(endDate, "short-abbr")})`
              : null;

          const expectedQuarter = `Q${
            Math.floor((startMonth - 1) / 3) + 1
          } (${formatDate(startDate, "short-abbr")} - ${formatDate(
            endDate,
            "short-abbr"
          )})`;

          // Valid Periods List and Validation
          if (
            ![
              expectedRange,
              expectedMonthYear,
              expectedYear,
              expectedHalfYear,
              expectedQuarter,
            ]
              .filter(Boolean)
              .includes(statementPeriod)
          ) {
            throw new Error(
              `Validation failed for Statement Period: ${statementPeriod}. Expected one of: ${[
                expectedRange,
                expectedMonthYear,
                expectedYear,
                expectedHalfYear,
                expectedQuarter,
              ]
                .filter(Boolean)
                .join(", ")}.`
            );
          }
        }
        console.log("Statement Period validation passed.");
      }
    );
  }
);
