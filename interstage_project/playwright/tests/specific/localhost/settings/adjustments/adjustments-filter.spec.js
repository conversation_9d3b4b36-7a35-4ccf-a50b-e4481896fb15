import AdjustementsV2Page from "../../../../../test-objects/adjustments-v2-objects";

const {
  commissionAdjustmentFilterFixtures: { test },
} = require("../../../../fixtures");

const {
  commissionAdjustmentFilterFixtures: { test: test1, expect: expect1 },
} = require("../../../../fixtures");

const fs = require("fs");
const path = require("path");
const csv = require("csv-parser");

test.beforeEach(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 400000);
  const page = adminPage.page;
  await page.addInitScript(() => {
    localStorage.clear();
    sessionStorage.clear();
  });
  const exitButton = await page.getByRole("button", {
    name: "Exit",
    exact: true,
  });
  if (await exitButton.isVisible()) {
    await exitButton.click();
  }
});

// The Test scripts are blocked due to the below bugs
// https://interstage.atlassian.net/browse/ICM-3030
// https://interstage.atlassian.net/browse/ICM-3029

test.describe(
  "Adjustments V2 Filter Automation",
  { tag: ["@commissionAdjustments", "@regression", "@primelogic-3"] },
  () => {
    test.skip(
      "Commission Adjustment V2 Payees Filter Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T22546" },
          {
            type: "Description",
            description:
              "Validate whether the Adjustment Filter for Payees works as expected (for both 'IN' and 'NOT IN' conditions).",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments with Payees available",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Adjustment Filter for Payees should correctly filter adjustments based on 'IN' and 'NOT IN' conditions.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        await page.waitForTimeout(7000);
        const totalCount = await adjustmentV2.adjustmentsCount();
        console.log("Total Adjustments Count:", totalCount);
        const payees = ["1 .", "2 ."];
        for (const payee of payees) {
          console.log(`\nValidating "IN" and "NOT IN" for: ${payee}`);
          await adjustmentV2.openFilter();
          await adjustmentV2.selectFilter("payees", "In", payee);
          await adjustmentV2.applyFilter();
          const inCount = await adjustmentV2.adjustmentsCount();
          console.log(`"IN" ${payee} Count:`, inCount);
          await adjustmentV2.clearFilter();
          await adjustmentV2.openFilter();
          await adjustmentV2.selectFilter("payees", "Not In", payee);
          await adjustmentV2.applyFilter();
          const notInCount = await adjustmentV2.adjustmentsCount();
          console.log(`"NOT IN" ${payee} Count:`, notInCount);
          await adjustmentV2.clearFilter();
          assert.strictEqual(
            inCount + notInCount,
            totalCount,
            `Validation failed for: ${payee}. IN (${inCount}) + NOT IN (${notInCount}) Total (${totalCount})`
          );
          console.log(
            `Validation Passed: "IN" ${payee} + "NOT IN" ${payee} = Total`
          );
        }
        console.log("\nValidating Double Filters:");
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter("payees", "In", "1 .");
        await adjustmentV2.selectFilter("payees", "In", "2 .");
        await adjustmentV2.applyFilter();
        const doubleInCount = await adjustmentV2.adjustmentsCount();
        console.log(`Double "IN" Count (1 . & 2 .):`, doubleInCount);
        await adjustmentV2.clearFilter();
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter("payees", "Not In", "1 .");
        await adjustmentV2.selectFilter("payees", "Not In", "2 .");
        await adjustmentV2.applyFilter();
        const doubleNotInCount = await adjustmentV2.adjustmentsCount();
        console.log(`Double "NOT IN" Count (1 . & 2 .):`, doubleNotInCount);
        await adjustmentV2.clearFilter();
        assert.strictEqual(
          doubleInCount + doubleNotInCount,
          totalCount,
          `Validation failed for double filters. IN (${doubleInCount}) + NOT IN (${doubleNotInCount}) Total (${totalCount})`
        );
        console.log(
          `Validation Passed: Double "IN" + Double "NOT IN" = Total Count`
        );
      }
    );

    test.skip(
      "Commission Adjustment V2 Statement Period Filter Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T22547" },
          {
            type: "Description",
            description:
              "Validate whether the Adjustment Filter for Statement Period works as expected (for both 'IN' and 'NOT IN' conditions).",
          },
          {
            type: "Precondition",
            description:
              "Commission Adjustments with Statement Period data available",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Adjustment Filter for Statement Period should correctly filter adjustments based on 'IN' and 'NOT IN' conditions.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        await page.waitForTimeout(7000);
        const totalCount = await adjustmentV2.adjustmentsCount();
        console.log("Total Adjustments Count:", totalCount);
        const filterItems = ["2025", "June 2025"];
        for (const period of filterItems) {
          console.log(`\nValidating "IN" and "NOT IN" for: ${period}`);
          await adjustmentV2.openFilter();
          await adjustmentV2.selectFilter("statementPeriod", "In", period);
          await adjustmentV2.applyFilter();
          const inCount = await adjustmentV2.adjustmentsCount();
          console.log(`"IN" ${period} Count:`, inCount);
          await adjustmentV2.clearFilter();
          await adjustmentV2.openFilter();
          await adjustmentV2.selectFilter("statementPeriod", "Not In", period);
          await adjustmentV2.applyFilter();
          const notInCount = await adjustmentV2.adjustmentsCount();
          console.log(`"NOT IN" ${period} Count:`, notInCount);
          await adjustmentV2.clearFilter();
          assert.strictEqual(
            inCount + notInCount,
            totalCount,
            `Validation failed for: ${period}. IN (${inCount}) + NOT IN (${notInCount}) Total (${totalCount})`
          );
          console.log(
            `Validation Passed: "IN" ${period} + "NOT IN" ${period} = Total`
          );
        }
        console.log("\nValidating Double Filters:");
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter("statementPeriod", "In", "2025");
        await adjustmentV2.selectFilter("statementPeriod", "In", "June 2025");
        await adjustmentV2.applyFilter();
        const doubleInCount = await adjustmentV2.adjustmentsCount();
        console.log(`Double "IN" Count:`, doubleInCount);
        await adjustmentV2.clearFilter();
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter("statementPeriod", "Not In", "2025");
        await adjustmentV2.selectFilter(
          "statementPeriod",
          "Not In",
          "June 2025"
        );
        await adjustmentV2.applyFilter();
        const doubleNotInCount = await adjustmentV2.adjustmentsCount();
        console.log(`Double "NOT IN" Count:`, doubleNotInCount);
        await adjustmentV2.clearFilter();
        assert.strictEqual(
          doubleInCount + doubleNotInCount,
          totalCount,
          `Validation failed for double filters. IN (${doubleInCount}) + NOT IN (${doubleNotInCount}) Total (${totalCount})`
        );
        console.log(
          `Validation Passed: Double "IN" + Double "NOT IN" = Total Count`
        );
      }
    );

    test.skip(
      "Commission Adjustment V2 Commission Amount Currency Filter Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T22550" },
          {
            type: "Description",
            description:
              "Validate whether the Adjustment Filter for Commission Amount Currency works as expected (for both 'IN' and 'NOT IN' conditions).",
          },
          {
            type: "Precondition",
            description:
              "Commission Adjustments with different Commission Amount Currencies available",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Adjustment Filter for Commission Amount Currency should correctly filter adjustments based on 'IN' and 'NOT IN' conditions.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        await page.waitForTimeout(7000);
        const totalCount = await adjustmentV2.adjustmentsCount();
        const filterItems = ["INR", "USD"];
        for (const currency of filterItems) {
          await adjustmentV2.openFilter();
          await adjustmentV2.selectFilter("adjustmentCurrency", "In", currency);
          await adjustmentV2.applyFilter();
          const inCount = await adjustmentV2.adjustmentsCount();
          await adjustmentV2.clearFilter();
          await adjustmentV2.openFilter();
          await adjustmentV2.selectFilter(
            "adjustmentCurrency",
            "Not In",
            currency
          );
          await adjustmentV2.applyFilter();
          const notInCount = await adjustmentV2.adjustmentsCount();
          await adjustmentV2.clearFilter();
          assert.strictEqual(
            inCount + notInCount,
            totalCount,
            `Validation failed for: ${currency}. IN (${inCount}) + NOT IN (${notInCount}) ≠ Total (${totalCount})`
          );
        }
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter("adjustmentCurrency", "In", "CAD");
        await adjustmentV2.selectFilter("adjustmentCurrency", "In", "AUD");
        await adjustmentV2.applyFilter();
        const doubleInCount = await adjustmentV2.adjustmentsCount();
        await adjustmentV2.clearFilter();
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter("adjustmentCurrency", "Not In", "CAD");
        await adjustmentV2.selectFilter("adjustmentCurrency", "Not In", "AUD");
        await adjustmentV2.applyFilter();
        const doubleNotInCount = await adjustmentV2.adjustmentsCount();
        await adjustmentV2.clearFilter();
        assert.strictEqual(
          doubleInCount + doubleNotInCount,
          totalCount,
          `Validation failed for double filters. IN (${doubleInCount}) + NOT IN (${doubleNotInCount}) ≠ Total (${totalCount})`
        );
      }
    );

    test.skip(
      "Commission Adjustment V2 Reason Category Filter Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T22551" },
          {
            type: "Description",
            description:
              "Validate whether the Adjustment Filter for Reason Category works as expected for 'In', 'Not In', 'Is Empty', and 'Is Not Empty' conditions.",
          },
          {
            type: "Precondition",
            description:
              "Commission Adjustments with different Reason Categories available",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Adjustment Filter for Reason Category should correctly filter adjustments based on 'In', 'Not In', 'Is Empty', and 'Is Not Empty' conditions.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        await page.waitForTimeout(7000);
        const totalCount = await adjustmentV2.adjustmentsCount();
        console.log("Total Adjustments Count:", totalCount);
        const filterItems = [
          "Calculation Issue",
          "CRM Issue",
          "Custom Category",
        ];
        console.log("\nValidating 'IS EMPTY' and 'IS NOT EMPTY'");
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter("reasonCategory", "Is Empty", null);
        await adjustmentV2.applyFilter();
        const isEmptyCount = await adjustmentV2.adjustmentsCount();
        console.log("'IS EMPTY' Count:", isEmptyCount);
        await adjustmentV2.clearFilter();
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter("reasonCategory", "Is Not Empty", null);
        await adjustmentV2.applyFilter();
        const isNotEmptyCount = await adjustmentV2.adjustmentsCount();
        console.log("'IS NOT EMPTY' Count:", isNotEmptyCount);
        await adjustmentV2.clearFilter();
        assert.strictEqual(
          isEmptyCount + isNotEmptyCount,
          totalCount,
          `Validation failed for: IS EMPTY (${isEmptyCount}) + IS NOT EMPTY (${isNotEmptyCount}) == Total (${totalCount})`
        );
        console.log(`Validation Passed: "IS EMPTY" + "IS NOT EMPTY" = Total`);
        for (const category of filterItems) {
          console.log(
            `\nValidating "IN", "NOT IN" and "IS EMPTY" for: ${category}`
          );
          await adjustmentV2.openFilter();
          await adjustmentV2.selectFilter("reasonCategory", "In", category);
          await adjustmentV2.applyFilter();
          const inCount = await adjustmentV2.adjustmentsCount();
          console.log(`"IN" ${category} Count:`, inCount);
          await adjustmentV2.clearFilter();
          await adjustmentV2.openFilter();
          await adjustmentV2.selectFilter("reasonCategory", "Not In", category);
          await adjustmentV2.applyFilter();
          const notInCount = await adjustmentV2.adjustmentsCount();
          console.log(`"NOT IN" ${category} Count:`, notInCount);
          await adjustmentV2.clearFilter();
          assert.strictEqual(
            inCount + notInCount + isEmptyCount,
            totalCount,
            `Validation failed for: ${category}. IN (${inCount}) + NOT IN (${notInCount}) + IS EMPTY (${isEmptyCount}) == Total (${totalCount})`
          );
          console.log(
            `Validation Passed: "IN" ${category} + "NOT IN" ${category} + "IS EMPTY" == Total`
          );
        }
        console.log("\nValidating Double Filters:");
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter(
          "reasonCategory",
          "In",
          "Calculation Issue"
        );
        await adjustmentV2.selectFilter("reasonCategory", "In", "CRM Issue");
        await adjustmentV2.applyFilter();
        const doubleInCount = await adjustmentV2.adjustmentsCount();
        console.log(`Double "IN" Count:`, doubleInCount);
        await adjustmentV2.clearFilter();
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter(
          "reasonCategory",
          "Not In",
          "Calculation Issue"
        );
        await adjustmentV2.selectFilter(
          "reasonCategory",
          "Not In",
          "CRM Issue"
        );
        await adjustmentV2.applyFilter();
        const doubleNotInCount = await adjustmentV2.adjustmentsCount();
        console.log(`Double "NOT IN" Count:`, doubleNotInCount);
        await adjustmentV2.clearFilter();
        assert.strictEqual(
          doubleInCount + doubleNotInCount + isEmptyCount,
          totalCount,
          `Validation failed for double filters. IN (${doubleInCount}) + NOT IN (${doubleNotInCount}) + Is Empty (${isEmptyCount}) Total (${totalCount})`
        );
        console.log(
          "Validation Passed: Double 'IN' + 'NOT IN' filters applied successfully."
        );
      }
    );

    test.skip(
      "Commission Adjustment V2 Adjustment Added By Filter Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T22552" },
          {
            type: "Description",
            description:
              "Validate whether the Adjustment Filter for Adjustment Added By works as expected (for both 'IN' and 'NOT IN' conditions).",
          },
          {
            type: "Precondition",
            description:
              "Commission Adjustments with different users who added adjustments available",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Adjustment Filter for Adjustment Added By should correctly filter adjustments based on 'IN' and 'NOT IN' conditions.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        await page.waitForTimeout(7000);
        const totalCount = await adjustmentV2.adjustmentsCount();
        console.log("Total Adjustments Count:", totalCount);
        const addedByUsers = ["everstage admin", "manager M"];
        for (const user of addedByUsers) {
          console.log(`\nValidating "IN" and "NOT IN" for: ${user}`);
          await adjustmentV2.openFilter();
          await adjustmentV2.selectFilter("adjustmentAddedBy", "In", user);
          await adjustmentV2.applyFilter();
          const inCount = await adjustmentV2.adjustmentsCount();
          console.log(`"IN" ${user} Count:`, inCount);
          await adjustmentV2.clearFilter();
          await adjustmentV2.openFilter();
          await adjustmentV2.selectFilter("adjustmentAddedBy", "Not In", user);
          await adjustmentV2.applyFilter();
          const notInCount = await adjustmentV2.adjustmentsCount();
          console.log(`"NOT IN" ${user} Count:`, notInCount);
          await adjustmentV2.clearFilter();
          assert.strictEqual(
            inCount + notInCount,
            totalCount,
            `Validation failed for: ${user}. IN (${inCount}) + NOT IN (${notInCount}) Total (${totalCount})`
          );
          console.log(
            `Validation Passed: "IN" ${user} + "NOT IN" ${user} = Total`
          );
        }
        console.log("\nValidating Double Filters:");
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter(
          "adjustmentAddedBy",
          "In",
          "everstage admin"
        );
        await adjustmentV2.selectFilter("adjustmentAddedBy", "In", "manager M");
        await adjustmentV2.applyFilter();
        const doubleInCount = await adjustmentV2.adjustmentsCount();
        console.log(
          `Double "IN" Count (everstage admin & manager M):`,
          doubleInCount
        );
        await adjustmentV2.clearFilter();
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter(
          "adjustmentAddedBy",
          "Not In",
          "everstage admin"
        );
        await adjustmentV2.selectFilter(
          "adjustmentAddedBy",
          "Not In",
          "manager M"
        );
        await adjustmentV2.applyFilter();
        const doubleNotInCount = await adjustmentV2.adjustmentsCount();
        console.log(
          `Double "NOT IN" Count (everstage admin & manager M):`,
          doubleNotInCount
        );
        await adjustmentV2.clearFilter();
        assert.strictEqual(
          doubleInCount + doubleNotInCount,
          totalCount,
          `Validation failed for double filters. IN (${doubleInCount}) + NOT IN (${doubleNotInCount}) Total (${totalCount})`
        );
        console.log(
          `Validation Passed: Double "IN" + Double "NOT IN" = Total Count`
        );
      }
    );

    test.skip(
      "Commission Adjustment V2 Commission Plan Filter Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T22553" },
          {
            type: "Description",
            description:
              "Validate whether the Adjustment Filter for Commission Plans works as expected for 'In', 'Not In', 'Is Empty', and 'Is Not Empty' conditions.",
          },
          {
            type: "Precondition",
            description:
              "Commission Adjustments with different Commission Plans available",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Adjustment Filter for Commission Plans should correctly filter adjustments based on 'In', 'Not In', 'Is Empty', and 'Is Not Empty' conditions.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        await page.waitForTimeout(7000);
        const totalCount = await adjustmentV2.adjustmentsCount();
        console.log("Total Adjustments Count:", totalCount);
        console.log("\nValidating 'IS EMPTY' and 'IS NOT EMPTY'");
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter("commissionPlan", "Is Empty", null);
        await adjustmentV2.applyFilter();
        const isEmptyCount = await adjustmentV2.adjustmentsCount();
        console.log("'IS EMPTY' Count:", isEmptyCount);
        await adjustmentV2.clearFilter();
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter("commissionPlan", "Is Not Empty", null);
        await adjustmentV2.applyFilter();
        const isNotEmptyCount = await adjustmentV2.adjustmentsCount();
        console.log("'IS NOT EMPTY' Count:", isNotEmptyCount);
        await adjustmentV2.clearFilter();
        assert.strictEqual(
          isEmptyCount + isNotEmptyCount,
          totalCount,
          `Validation failed for: IS EMPTY (${isEmptyCount}) + IS NOT EMPTY (${isNotEmptyCount}) == Total (${totalCount})`
        );
        console.log(`Validation Passed: "IS EMPTY" + "IS NOT EMPTY" = Total`);
        const filterItems = ["Settlement", "Annual"];
        for (const plan of filterItems) {
          console.log(
            `\nValidating "IN", "NOT IN" and "IS EMPTY" for: ${plan}`
          );
          await adjustmentV2.openFilter();
          await adjustmentV2.selectFilter("commissionPlan", "In", plan);
          await adjustmentV2.applyFilter();
          const inCount = await adjustmentV2.adjustmentsCount();
          console.log(`"IN" ${plan} Count:`, inCount);
          await adjustmentV2.clearFilter();
          await adjustmentV2.openFilter();
          await adjustmentV2.selectFilter("commissionPlan", "Not In", plan);
          await adjustmentV2.applyFilter();
          const notInCount = await adjustmentV2.adjustmentsCount();
          console.log(`"NOT IN" ${plan} Count:`, notInCount);
          await adjustmentV2.clearFilter();
          assert.strictEqual(
            inCount + notInCount + isEmptyCount,
            totalCount,
            `Validation failed for: ${plan}. IN (${inCount}) + NOT IN (${notInCount}) + IS EMPTY (${isEmptyCount}) Total (${totalCount})`
          );
          console.log(
            `Validation Passed: "IN" ${plan} + "NOT IN" ${plan} + "IS EMPTY" = Total`
          );
        }
        console.log("\nValidating Double Filters:");
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter("commissionPlan", "In", "Monthly");
        await adjustmentV2.selectFilter("commissionPlan", "In", "Half_Yearly");
        await adjustmentV2.applyFilter();
        const doubleInCount = await adjustmentV2.adjustmentsCount();
        console.log(`Double "IN" Count:`, doubleInCount);
        await adjustmentV2.clearFilter();
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter("commissionPlan", "Not In", "Monthly");
        await adjustmentV2.selectFilter(
          "commissionPlan",
          "Not In",
          "Half_Yearly"
        );
        await adjustmentV2.applyFilter();
        const doubleNotInCount = await adjustmentV2.adjustmentsCount();
        console.log(`Double "NOT IN" Count:`, doubleNotInCount);
        await adjustmentV2.clearFilter();
        assert.strictEqual(
          doubleInCount + doubleNotInCount + isEmptyCount,
          totalCount,
          `Validation failed for double filters. IN (${doubleInCount}) + NOT IN (${doubleNotInCount}) + IS EMPTY (${isEmptyCount}) Total (${totalCount})`
        );
        console.log(
          "Validation Passed: Double 'IN' + 'NOT IN' filters applied successfully."
        );
      }
    );

    test.skip(
      "Commission Adjustment V2 Adjustment Amount Filter Validation (Positive and Negative)",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T22548,INTER-T22549" },
          {
            type: "Description",
            description:
              "Validate whether the Adjustment Filter for Adjustment Amount works as expected with various conditions.",
          },
          {
            type: "Precondition",
            description:
              "Commission Adjustments with different Adjustment Amount values available",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Adjustment Filter for Adjustment Amount should correctly filter adjustments based on various conditions.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        await page.waitForTimeout(7000);
        const totalCount = await adjustmentV2.adjustmentsCount();
        console.log("Total Adjustments Count:", totalCount);
        const filterOptions = [
          { option: "Equal To", type: "number", values: ["740"] },
          { option: "Not Equal To", type: "number", values: ["740"] },
          { option: "Greater Than", type: "number", values: ["-790"] },
          {
            option: "Greater than or Equal to",
            type: "number",
            values: ["740"],
          },
          { option: "Less Than", type: "number", values: ["740"] },
          { option: "Less than or Equal to", type: "number", values: ["-790"] },
          { option: "Is Empty", type: null, values: [] },
          { option: "Is Not Empty", type: null, values: [] },
          { option: "In Between", values: ["-5380", "740"] },
          { option: "In", type: "dropdown", values: ["740", "-790"] },
          { option: "Not In", type: "dropdown", values: ["740", "-790"] },
        ];
        let filterCounts = {};
        for (const filter of filterOptions) {
          console.log(`\nValidating filter: ${filter.option}`);
          await adjustmentV2.openFilter();
          await adjustmentV2.filterAdjustmentAmount(
            filter.option,
            filter.type,
            filter.values
          );
          const filteredCount = await adjustmentV2.adjustmentsCount();
          console.log(`${filter.option} Count:`, filteredCount);
          await adjustmentV2.clearFilter();
          filterCounts[filter.option] = filteredCount;
        }
        assert.strictEqual(
          filterCounts["Equal To"] + filterCounts["Not Equal To"],
          totalCount,
          "Validation failed: Equal To + Not Equal To ≠ Total"
        );
        assert.strictEqual(
          filterCounts["Less Than"] + filterCounts["Greater than or Equal to"],
          totalCount,
          "Validation failed: Less Than + Greater than or Equal to ≠ Total"
        );
        assert.strictEqual(
          filterCounts["Greater Than"] + filterCounts["Less than or Equal to"],
          totalCount,
          "Validation failed: Greater Than + Less than or Equal to ≠ Total"
        );
        assert.strictEqual(filterCounts["In"], 2, "Validation failed: IN");
        assert.strictEqual(
          filterCounts["Not In"],
          179,
          "Validation failed: IN"
        );
        console.log("All filter validations passed successfully.");
      }
    );

    test.skip(
      "Commission Adjustment V2 Line Item Filter Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T22554" },
          {
            type: "Description",
            description:
              "Validate whether the Adjustment Filter for Line Item works as expected for various filter conditions.",
          },
          {
            type: "Precondition",
            description:
              "Commission Adjustments with various Line Items available",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Adjustment Filter for Line Item should correctly filter adjustments based on specified conditions.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        await page.waitForTimeout(7000);
        const totalCount = await adjustmentV2.adjustmentsCount();
        console.log("Total Adjustments Count:", totalCount);
        const filterOptions = [
          { option: "Contains", type: "text", values: ["1"] },
          { option: "Does not Contain", type: "text", values: ["1"] },
          { option: "Starts With", type: "text", values: ["1"] },
          { option: "Ends With", type: "text", values: ["8"] },
          { option: "Equal To", type: "text", values: ["1"] },
          { option: "Not Equal To", type: "text", values: ["1"] },
          { option: "In", type: "dropdown", values: ["2", "19"] },
          { option: "Not In", type: "dropdown", values: ["2", "19"] },
          { option: "Is Empty", type: null, values: [] },
          { option: "Is Not Empty", type: null, values: [] },
        ];
        let filterCounts = {};
        for (const filter of filterOptions) {
          console.log(`\nValidating filter: ${filter.option}`);
          await adjustmentV2.openFilter();
          await adjustmentV2.filterLineItem(
            filter.option,
            filter.type,
            filter.values
          );
          const filteredCount = await adjustmentV2.adjustmentsCount();
          console.log(`${filter.option} Count:`, filteredCount);
          await adjustmentV2.clearFilter();
          filterCounts[filter.option] = filteredCount;
        }
        assert.strictEqual(
          filterCounts["Equal To"] +
            filterCounts["Not Equal To"] +
            filterCounts["Is Empty"],
          totalCount,
          "Validation failed: Equal To + Not Equal To ≠ Total"
        );
        assert.strictEqual(
          filterCounts["Contains"] +
            filterCounts["Does not Contain"] +
            filterCounts["Is Empty"],
          totalCount,
          "Validation failed: Contains + Does not Contain + Is Empty ≠ Total"
        );
        assert.strictEqual(
          filterCounts["In"] +
            filterCounts["Not In"] +
            filterCounts["Is Empty"],
          totalCount,
          "Validation failed: In + Not In + Is Empty ≠ Total"
        );
        assert.strictEqual(
          filterCounts["Is Empty"] + filterCounts["Is Not Empty"],
          totalCount,
          "Validation failed: Is Empty + Is Not Empty ≠ Total"
        );
        console.log("All filter validations passed successfully.");
      }
    );

    test.skip(
      "Commission Adjustment V2 Export With Filter Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T22555" },
          {
            type: "Description",
            description:
              "Validate that when the filter is applied, clicking the Export button results in only the filtered data being included in the exported CSV.",
          },
          {
            type: "Precondition",
            description: "Data is filtered based on the selected criteria",
          },
          {
            type: "Expected Behaviour",
            description:
              "When the filter is applied, the exported CSV should contain only the data that matches the filter criteria.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const downloadsPath = path.join(__dirname, "downloads");
        if (!fs.existsSync(downloadsPath)) {
          fs.mkdirSync(downloadsPath);
        }
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.openFilter();
        await adjustmentV2.selectFilter("payees", "In", "1 .");
        await adjustmentV2.selectFilter("payees", "In", "2 .");
        await adjustmentV2.applyFilter();
        const downloadPromise = page.waitForEvent("download");
        await adjustmentV2.clickBulkImport("Export Adjustments");
        const download = await downloadPromise;
        const downloadPath = path.join(
          downloadsPath,
          "FilteredAdjustments.csv"
        );
        await download.saveAs(downloadPath);
        if (!fs.existsSync(downloadPath)) {
          throw new Error(`File not found at: ${downloadPath}`);
        }
        const existingFilePath = path.join(
          downloadsPath,
          "AdjustmentFilter(Payee1,Payee2).csv"
        );
        const readCsv = (filePath) => {
          return new Promise((resolve, reject) => {
            const results = [];
            fs.createReadStream(filePath)
              .pipe(csv())
              .on("data", (data) => results.push(data))
              .on("end", () => resolve(results))
              .on("error", reject);
          });
        };
        const existingData = await readCsv(existingFilePath);
        const newData = await readCsv(downloadPath);

        // 🔹 Compare row counts
        console.log(`✅ Expected Rows: ${existingData.length}`);
        console.log(`✅ Downloaded Rows: ${newData.length}`);

        if (existingData.length !== newData.length) {
          throw new Error(
            `Row count mismatch! Expected: ${existingData.length}, Got: ${newData.length}`
          );
        }
      }
    );
  }
);
test1.describe(
  "Export Button disappeared on Serach",
  { tag: ["@commissionAdjustments", "@regression", "@primelogic-3"] },
  () => {
    const csv = require("csv-parser");
    test1.skip(
      "Validate that the export button remains visible",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T25853,INTER-T25854" },
          {
            type: "Description",
            description:
              "Validate that the export button remains visible after performing a search and does not disappear after clearing the search",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments with Payees available",
          },
          {
            type: "Expected Behaviour",
            description:
              "The export and add adjustments buttons should be visible",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        const totalCount = await adjustmentV2.adjustmentsCount();
        expect1(
          await page
            .locator("div.border-ever-chartColors-2 div")
            .last()
            .innerText()
        ).toBe("181 adjustments");
        await adjustmentV2.searchAdjustments("1 .");
        expect1(
          await page
            .locator("div.border-ever-chartColors-2 div")
            .last()
            .innerText()
        ).toBe("20 adjustments");
        await expect1(
          page.getByRole("button", { name: "Import / Export" })
        ).toBeVisible();
        await expect1(
          page.getByRole("button", { name: "Add Adjustment" })
        ).toBeVisible();
        console.log("Both buttons are visible!");
        const closeButton = page.locator(
          "input[placeholder='Search by name or email'] ~ span > svg"
        );
        await closeButton.click();
        await page.waitForTimeout(1000);
        expect1(
          await page
            .locator("div.border-ever-chartColors-2 div")
            .last()
            .innerText()
        ).toBe("181 adjustments");
        await expect1(
          page.getByRole("button", { name: "Import / Export" })
        ).toBeVisible();
        await expect1(
          page.getByRole("button", { name: "Add Adjustment" })
        ).toBeVisible();
        console.log("Both buttons are visible!");
      }
    );

    test1.skip(
      "Validate the Exported file contains search query filtered records only",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T25856" },
          {
            type: "Description",
            description:
              "Validate that performing a search and then exporting works correctly",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments with Payees available",
          },
          {
            type: "Expected Behaviour",
            description: "The export operation must be successful",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const downloadsPath = path.join(__dirname, "upload files");
        if (!fs.existsSync(downloadsPath)) {
          fs.mkdirSync(downloadsPath);
        }
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.searchAdjustments("1 .");
        const downloadPromise = page.waitForEvent("download");
        await adjustmentV2.clickBulkImport("Export Adjustments");
        const download = await downloadPromise;
        const downloadPath = path.join(downloadsPath, "SearchExport.csv");
        await download.saveAs(downloadPath);
        if (!fs.existsSync(downloadPath)) {
          throw new Error(`File not found at: ${downloadPath}`);
        }
        const existingFilePath = path.join(
          downloadsPath,
          "SearchExport(Payee1).csv"
        );
        const readCsv = (filePath) => {
          return new Promise((resolve, reject) => {
            const results = [];
            fs.createReadStream(filePath)
              .pipe(csv())
              .on("data", (data) => results.push(data))
              .on("end", () => resolve(results))
              .on("error", reject);
          });
        };
        const existingData = await readCsv(existingFilePath);
        const newData = await readCsv(downloadPath);
        assert.deepStrictEqual(
          newData,
          existingData,
          "The CSV data does not match the expected data."
        );
      }
    );
  }
);
