/* eslint-disable playwright/no-wait-for-timeout */
const CommonUtils = require("../../../../../test-objects/common-utils-objects");
const {
  drawsAdjustmentsFixtures: { test, expect },
} = require("../../../../fixtures");

async function setPayoutsPeriod(page, period) {
  await page.goto("/commissions", { waitUntil: "networkidle" });
  await page.waitForTimeout(5000);
  const value = await page
    .getByTestId("period-select")
    .locator("div")
    .first()
    .innerText();
  if (value !== period) {
    await page.getByTestId("period-select").locator("div").first().click();
    await page.getByText(period).click();
  }
}
test.beforeEach(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 1800000);
});

test.describe(
  "Draw Adjustments",
  {
    tag: [
      "@commissionAdjustments",
      "@regression",
      "@draws",
      "@drawAdjustments",
      "@primelogic-3",
    ],
  },
  () => {
    test("Check basic draw adjustment flow", async ({ adminPage }) => {
      // Remove added adjustment for payee test user
      const page = adminPage.page;

      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Recover Draw" }).click();
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page
        .getByRole("menuitem", { name: "Recover Draw" })
        .locator("span")
        .click();
      await page.getByLabel("Payee*").click();
      await page
        .locator("div")
        .filter({ hasText: /^Payee test$/ })
        .nth(1)
        .click();
      await page.getByLabel("Fiscal Year*").click();
      await page.locator("span").filter({ hasText: "2023" }).click();
      await page.getByLabel("Period*").click();
      await page.locator("div").filter({ hasText: /^Apr$/ }).nth(2).click();
      const recoverableBalanceValue = await page
        .getByTestId("pt-recoverable-balance")
        .getAttribute("value");
      expect(recoverableBalanceValue).toBe("1500");
      await page.getByLabel("Amount*").click();
      await page.getByLabel("Amount*").fill("2000");
      await expect(
        await page.getByText("Entered amount should be")
      ).toBeVisible();
      await page.getByLabel("Amount*").dblclick();
      await page.getByLabel("Amount*").fill("500");
      await page.getByLabel("Comments").click();
      await page.getByLabel("Comments").fill("This is test draw adj");
      await page.getByRole("button", { name: "Submit" }).click();
      await expect(
        await page.getByText("Adjustment saved successfully")
      ).toBeVisible();
    });

    test("Draw adjustments after changing FX should reflect latest value as per FX", async ({
      adminPage,
    }) => {
      // Remove fx for CAD feb 2023
      // Delete addded draw
      const page = adminPage.page;

      await page.goto("/settings/basic", { waitUntil: "networkidle" });
      await page
        .locator("div")
        .filter({ hasText: /^Select currency$/ })
        .nth(3)
        .click();
      await page.locator("span").filter({ hasText: "CAD" }).click();
      await page.getByPlaceholder("Start month").click();
      await page.getByPlaceholder("Start month").fill("Feb-2023");
      await page.getByPlaceholder("Start month").press("Enter");
      await page.getByPlaceholder("End month").click();
      await page.getByPlaceholder("End month").fill("Feb-2023");
      await page.getByPlaceholder("End month").press("Enter");
      await page.getByRole("spinbutton").click();
      await page.getByRole("spinbutton").fill("2.5");
      await page.getByRole("button", { name: "Update" }).click();
      await expect(
        await page.getByText("Updated Successfully!!")
      ).toBeVisible();
      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Recover Draw" }).click();
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page.getByRole("menuitem", { name: "Recover Draw" }).click();
      await page.getByLabel("Payee*").click();
      await page.getByText("Draw User1").click();
      await page.getByLabel("Fiscal Year*").click();
      await page.getByRole("listitem").getByText("2023").click();
      await page.getByLabel("Period*").click();
      await page.locator("span").filter({ hasText: "Feb" }).click();
      await page.getByLabel("Amount*").click();
      await page.getByLabel("Amount*").fill("150");
      await page.getByRole("button", { name: "Submit" }).click();
      await expect(
        await page.getByText("Adjustment saved successfully")
      ).toBeVisible();
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page.getByRole("menuitem", { name: "Recover Draw" }).click();
      await expect(
        await page.getByLabel("Add Adjustment").getByText("Add Adjustment")
      ).toBeVisible();
      await page.locator("span.ant-modal-close-x").click();
      await expect(
        await page.getByLabel("Add Adjustment").getByText("Add Adjustment")
      ).toBeHidden();
    });

    test("User should be able to edit/delete the added draw adjustment add adjustments for inactive users with comments", async ({
      adminPage,
    }) => {
      // Add already added adjustment for a draw user2
      // Delete addded draw
      const page = adminPage.page;

      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Recover Draw" }).click();
      await page
        .locator("[data-testid*='<EMAIL>']")
        .locator("svg")
        .click();
      await page.getByRole("button", { name: "Yes" }).click();
      await expect(await page.getByText("Adjustment deleted")).toBeVisible();
      await page.reload();
      await page.getByRole("button", { name: "Recover Draw" }).click();
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page.getByRole("menuitem", { name: "Recover Draw" }).click();
      await page.getByLabel("Payee*").click();
      await page.getByText("Inactive User").click();
      await page.getByLabel("Fiscal Year*").click();
      await page.getByRole("listitem").getByText("2024").click();
      await page.getByLabel("Period*").click();
      await page.locator("span").filter({ hasText: "Jan" }).click();
      await page.getByLabel("Amount*").click();
      await page.getByLabel("Amount*").fill("2456");
      await page.getByLabel("Comments").click();
      await page.getByLabel("Comments").fill("Adding adj for inactive user");
      await page.getByRole("button", { name: "Submit" }).click();
      await expect(
        await page.getByText("Adjustment saved successfully")
      ).toBeVisible();
    });

    test("Recovered value should get reflected in statements after sync run.", async ({
      adminPage,
    }, testInfo) => {
      // Delete addded draw and adjustment for draw user 3, Jan 2024
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 600000);
      await page.goto("/draws", { waitUntil: "networkidle" });
      await page.getByText("Draw User3").click();
      await page.getByPlaceholder("Select Year").click();
      await page.getByPlaceholder("Select Year").fill("2024");
      await page.getByPlaceholder("Select Year").press("Enter");
      await page.getByRole("button", { name: "Modify" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Period$/ })
        .nth(2)
        .click();
      await page.locator("span").filter({ hasText: "Jan" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Type$/ })
        .nth(2)
        .click();
      await page.getByText("Recoverable", { exact: true }).click();
      await page.getByPlaceholder("Enter Amount").click();
      await page.getByPlaceholder("Enter Amount").fill("5000");
      await page.getByRole("button", { name: "Save" }).click();
      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page
        .getByRole("menuitem", { name: "Recover Draw" })
        .locator("span")
        .click();
      await page.getByLabel("Payee*").click();
      await page.getByText("Draw User3").click();
      await page.getByLabel("Fiscal Year*").click();
      await page.getByRole("listitem").getByText("2024").click();
      await page.getByLabel("Period*").click();
      await page.locator("span").filter({ hasText: "Jan" }).click();
      await page.getByLabel("Amount*").click();
      await page.getByLabel("Amount*").fill("250");
      await page.getByRole("button", { name: "Submit" }).click();
      await expect(
        await page.getByText("Adjustment saved successfully")
      ).toBeVisible();
      await page.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await page
        .locator("span")
        .filter({ hasText: "Selected Payees" })
        .nth(1)
        .click();
      await page.getByTestId("ever-select").locator("div").nth(1).click();
      await page.getByText("Draw User3").click();
      await page.getByPlaceholder("Select month").click();
      await page.getByText("Jan").click();
      await page.getByRole("button", { name: "Run", exact: true }).click();
      await page.getByRole("button", { name: "Skip & Run" }).click();
      await expect(
        await page.getByText("Calculating Commissions...")
      ).toBeVisible();
      await expect(
        await page.getByText("Commission Calculations Completed")
      ).toBeVisible({
        timeout: 240000,
      });
      // Commission Calculations Failed Partially
      await setPayoutsPeriod(page, "January 2024");
      await page.getByRole("link", { name: "Draw User3" }).click();
      await page.getByText("Draw Adjustments").click();
      await expect(
        await page.getByRole("gridcell", { name: "£5,000.00" })
      ).toBeVisible();
      await expect(
        await page.getByRole("gridcell", { name: "£250.00" })
      ).toBeVisible();
    });

    test("When user edits value in draw screen values should relfect in adjustment screen", async ({
      adminPage,
    }) => {
      // Reset draw value for Draw User1 to 2500 in jan 2023
      const page = adminPage.page;

      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Recover Draw" }).click();
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page.getByRole("menuitem", { name: "Recover Draw" }).click();
      await page.getByLabel("Payee*").click();
      // await page.getByText("Draw User1").nth(1).click();
      await page.getByText("Draw User1", { exact: true }).nth(1).click();
      await page.getByLabel("Fiscal Year*").click();
      await page.getByRole("listitem").getByText("2023").click();
      await page.getByLabel("Period*").click();
      await page.getByText("Feb").nth(1).click();
      await page.waitForTimeout(1000);
      const recoverableBalanceValue = await page
        .getByTestId("pt-recoverable-balance")
        .getAttribute("value");
      expect(recoverableBalanceValue).toBe("4350");
      await page.goto("/draws", { waitUntil: "networkidle" });
      await page.getByText("Draw User1").click();
      await page.getByPlaceholder("Select Year").click();
      await page.getByText("2023").click();
      await page.getByRole("button", { name: "Modify" }).click();
      await page.getByPlaceholder("Enter Amount").first().click();
      await page.getByPlaceholder("Enter Amount").first().fill("2501");
      await page.getByPlaceholder("Enter Amount").first().press("Enter");
      await page.getByRole("button", { name: "Save" }).click();
      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page.getByRole("menuitem", { name: "Recover Draw" }).click();
      await page.getByLabel("Payee*").click();
      await page.getByText("Draw User1").click();
      await page.getByLabel("Fiscal Year*").click();
      await page.getByRole("listitem").getByText("2023").click();
      await page.getByLabel("Period*").click();
      await page.locator("span").filter({ hasText: "Feb" }).click();
      const recoverableBalanceValueNew = await page
        .getByTestId("pt-recoverable-balance")
        .getAttribute("value");
      expect(recoverableBalanceValueNew).toBe("4351");
    });

    test("Recoverable Draw amount should be displayed in recoverable amount & Non Recoverable Draw amount is displayed as 0", async ({
      adminPage,
    }) => {
      // Remove draws for drawUser1 in 2024
      const page = adminPage.page;

      await page.goto("/draws", { waitUntil: "networkidle" });
      await page.getByText("Draw User1").click();
      await page.getByPlaceholder("Select Year").click();
      await page.getByText("2024").click();
      await page.getByRole("button", { name: "Modify" }).click();
      await page.getByText("Period*Select Period").click();
      await page.locator("span").filter({ hasText: "Jan" }).click();
      await page.getByText("Draw type*Select Type").click();
      await page.getByText("Non-recoverable", { exact: true }).click();
      await page.getByPlaceholder("Enter Amount").click();
      await page.getByPlaceholder("Enter Amount").fill("1500");
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        await page.getByText("Draws Added Successfully")
      ).toBeVisible();
      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page.getByRole("menuitem", { name: "Recover Draw" }).click();
      await page.getByLabel("Payee*").click();
      await page.getByText("Draw User1").click();
      await page.getByLabel("Fiscal Year*").click();
      await page.locator("span").filter({ hasText: "2024" }).click();
      await page.getByLabel("Period*").click();
      await page.locator("span").filter({ hasText: "Jan" }).click();
      const recoverableBalanceValueNew = await page
        .getByTestId("pt-recoverable-balance")
        .getAttribute("value");
      expect(recoverableBalanceValueNew).toBe("0");
      await page.goto("/draws", { waitUntil: "networkidle" });
      await page.getByText("Draw User1").click();
      await page.getByPlaceholder("Select Year").click();
      await page.getByPlaceholder("Select Year").fill("2024");
      await page.getByPlaceholder("Select Year").press("Enter");
      await page.getByRole("button", { name: "Modify" }).click();
      await page.getByRole("button", { name: "Add Schedule" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Select Period$/ })
        .nth(2)
        .click();
      await page.locator("span").filter({ hasText: "Feb" }).click();
      await page.getByText("Draw type*Select Type").click();
      await page.getByText("Recoverable", { exact: true }).click();
      await page.getByPlaceholder("Enter Amount").nth(1).click();
      await page.getByPlaceholder("Enter Amount").nth(1).fill("500");
      await page.getByPlaceholder("Enter Amount").nth(1).press("Enter");
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        await page.getByText("Draws Added Successfully")
      ).toBeVisible();
      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page.getByRole("menuitem", { name: "Recover Draw" }).click();
      await page.getByLabel("Payee*").click();
      await page.getByText("Draw User1").click();
      await page.getByLabel("Fiscal Year*").click();
      await page.locator("span").filter({ hasText: "2024" }).click();
      await page.getByLabel("Period*").click();
      await page.locator("span").filter({ hasText: "Feb" }).click();
      const recoverableBalanceValue = await page
        .getByTestId("pt-recoverable-balance")
        .getAttribute("value");
      expect(recoverableBalanceValue).toBe("500");
    });

    test("Adding draw adjustments when statements are locked and unlocked - Flow check", async ({
      adminPage,
    }, testInfo) => {
      testInfo.setTimeout(testInfo.timeout + 280000);
      // Draw user 6 should be unlocked and should not have any adjustments for june 2024
      const page = adminPage.page;

      await setPayoutsPeriod(page, "June 2024");
      await page.getByRole("link", { name: "Draw User6" }).click();
      await page.getByTestId("lock-unlock-button").click();
      await expect(
        await page.getByText("Statement locked successfully")
      ).toBeVisible();
      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page
        .getByRole("menuitem", { name: "Recover Draw" })
        .locator("span")
        .click();
      await page.getByLabel("Payee*").click();
      await page.getByText("Draw User6").click();
      await page.getByLabel("Fiscal Year*").click();
      await page.getByRole("listitem").getByText("2024").click();
      await page.getByLabel("Period*").click();
      await page.getByText("Jun").click();
      await page.getByLabel("Amount*").click();
      await page.getByLabel("Amount*").fill("1500");
      await page.getByRole("button", { name: "Submit" }).click();
      await page.getByText("Adjustment saved successfully").click();
      await page.goto("/databook", { waitUntil: "networkidle" });
      await page.getByRole("link", { name: "Report" }).click();
      await page.getByRole("button", { name: "Update Data" }).click();
      await expect(
        await page.getByText("Datasheet sync request has")
      ).toBeVisible();
      await expect(
        await page.getByText("Datasheet has been generated")
      ).toBeVisible({ timeout: 60000 });
      await page.getByRole("button", { name: "Add Filter" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Column$/ })
        .nth(3)
        .click();
      await page.getByText("Payee Email").nth(2).click();
      await page
        .locator("div")
        .filter({ hasText: /^Operator$/ })
        .nth(3)
        .click();
      await page.getByText("Equal To", { exact: true }).click();
      await page.getByRole("textbox").nth(1).click();
      await page.getByRole("textbox").nth(1).fill("<EMAIL>");
      await page.getByRole("button", { name: "Apply", exact: true }).click();
      await expect(await page.getByTestId("pt-9000").first()).toBeVisible();
      await expect(await page.getByTestId("pt-1000").first()).toBeVisible();
      await setPayoutsPeriod(page, "June 2024");
      await page.getByRole("link", { name: "Draw User6" }).click();
      await page.getByTestId("lock-unlock-button").click();
      await expect(
        await page.getByText("Statement unlocked successfully")
      ).toBeVisible();
      await page.goto("/databook", { waitUntil: "networkidle" });
      await page.getByRole("link", { name: "Comm Summary" }).click();
      await page.getByRole("button", { name: "Update Data" }).click();
      await expect(
        await page.getByText("Datasheet has been generated")
      ).toBeVisible({ timeout: 90000 });
      await page.getByRole("button", { name: "Add Filter" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Column$/ })
        .nth(3)
        .click();
      await page.locator("span").filter({ hasText: "Payee Email" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Operator$/ })
        .nth(3)
        .click();
      await page.getByText("Equal To", { exact: true }).click();
      await page.getByRole("textbox").nth(1).click();
      await page.getByRole("textbox").nth(1).fill("<EMAIL>");
      await page.getByRole("button", { name: "Apply", exact: true }).click();
      await page.getByTestId("pt-1000").click();
      await page.getByTestId("pt-7500").click();
      await page.getByRole("link", { name: "Databook" }).click();
      await page.getByRole("link", { name: "Report" }).click();
      // await page.getByRole("button", { name: "Update Data" }).click();
      // await page.getByText("Datasheet sync request has").click();
      await page.getByRole("button", { name: "Add Filter" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Column$/ })
        .nth(3)
        .click();
      await page.getByText("Payee Email").nth(2).click();
      await page
        .locator("div")
        .filter({ hasText: /^Operator$/ })
        .nth(3)
        .click();
      await page.getByText("Equal To", { exact: true }).click();
      await page.getByRole("textbox").nth(1).click();
      await page.getByRole("textbox").nth(1).fill("<EMAIL>");
      await page.getByRole("button", { name: "Apply", exact: true }).click();
      await page.getByTestId("pt-9000").first().click();
      await page.getByTestId("pt-1000").first().click();
    });

    test("Draw adjustments should reflect in crystal screen and projected values are adjusted as per draw type added.", async ({
      adminPage,
    }) => {
      // Payee test should have some draw balance for July 2024 but no adjustment
      const page = adminPage.page;

      await page.goto("/crystal", { waitUntil: "networkidle" });
      await page
        .getByRole("link", { name: "Draw test sim Modified by" })
        .click();
      const page1Promise = page.waitForEvent("popup");
      await page.getByRole("button", { name: "Preview as payee" }).click();
      const page1 = await page1Promise;
      await page1.getByTestId("pt-crystal-payee-dropdown").click();
      await page1.getByTitle("Payee test").click();
      await expect(
        await page1.getByText("AU$0.00Current Payout").first()
      ).toBeVisible();
      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page
        .getByRole("menuitem", { name: "Recover Draw" })
        .locator("span")
        .click();
      const now = new Date();
      await page.getByLabel("Payee*").click();
      await page.getByText("Payee test").click();
      await page.getByLabel("Fiscal Year*").click();
      await page.getByLabel("Fiscal Year*").fill("2024");
      await page.getByRole("listitem").getByText("2024").click();
      await page.getByLabel("Period*").click();
      const options = { month: "short" }; // 'short' provides the abbreviated form
      const formatter = new Intl.DateTimeFormat("en-US", options);
      const month = formatter.format(now);
      await page.locator("span").filter({ hasText: month }).click();
      await page.getByLabel("Amount*").click();
      await page.getByLabel("Amount*").fill("200");
      await page.getByRole("button", { name: "Submit" }).click();
      await expect(
        await page.getByText("Adjustment saved successfully")
      ).toBeVisible();
      await page1.reload({ waitUntil: "networkidle" });
      await page1.getByTestId("pt-crystal-payee-dropdown").click();
      await page1.getByTitle("Payee test").click();
      await expect(
        await page1.getByText("AU$0.00Current Payout").first()
      ).toBeVisible();
      await page.getByRole("button", { name: "Recover Draw" }).click();
      await page
        .getByTestId("<EMAIL>-200")
        .locator("svg")
        .click();
      await page.getByRole("button", { name: "Yes" }).click();
      await expect(await page.getByText("Adjustment deleted")).toBeVisible();
      await page1.reload({ waitUntil: "networkidle" });
      await expect(
        await page1.getByText("AU$0.00Current Payout").first()
      ).toBeVisible();
    });

    test("When an adjustment is added for an recoverable & none recoverable draw-commission value should be adjusted accordingly.", async ({
      adminPage,
    }) => {
      // Remove adjustments addded for Draw User5, May and June
      const page = adminPage.page;

      await setPayoutsPeriod(page, "June 2024");
      await page.getByRole("link", { name: "Draw User5" }).click();
      await expect(await page.getByText("$20,000.00").first()).toBeVisible();
      await page.getByTestId("ever-select").getByText("June").click();
      await page.getByText("May").click();
      await expect(await page.getByText("$15,000.00").first()).toBeVisible();
      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page
        .getByRole("menuitem", { name: "Recover Draw" })
        .locator("span")
        .click();
      await page.getByLabel("Payee*").click();
      await page.getByText("Draw User5").click();
      await page.getByLabel("Fiscal Year*").click();
      await page.getByRole("listitem").getByText("2024").click();
      await page.getByLabel("Period*").click();
      await page.locator("span").filter({ hasText: "May" }).click();
      await page.getByLabel("Amount*").click();
      await page.getByLabel("Amount*").fill("1000");
      await page.getByRole("button", { name: "Submit" }).click();
      await expect(
        await page.getByText("Adjustment saved successfully")
      ).toBeVisible();
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page
        .getByRole("menuitem", { name: "Recover Draw" })
        .locator("span")
        .click();
      await page.getByLabel("Payee*").click();
      await page.getByText("Draw User5").click();
      await page.getByLabel("Fiscal Year*").click();
      await page.getByRole("listitem").getByText("2024").click();
      await page.getByLabel("Period*").click();
      await page.locator("span").filter({ hasText: "Jun" }).click();
      await page.getByLabel("Amount*").click();
      await page.getByLabel("Amount*").fill("1500");
      await page.getByRole("button", { name: "Submit" }).click();
      await expect(
        await page.getByText("Adjustment saved successfully")
      ).toBeVisible();
      await setPayoutsPeriod(page, "June 2024");
      await page.getByRole("link", { name: "Draw User5" }).click();
      await expect(await page.getByText("$18,500.00").first()).toBeVisible();
      await page.getByTestId("ever-select").getByText("June").click();
      await page.getByText("May").click();
      await expect(await page.getByText("$14,000.00").first()).toBeVisible();
    });

    test("FX and Draw adjustment flow in crytsal and payouts", async ({
      adminPage,
    }, testInfo) => {
      // Change fx for INR to 1 for march 2024 and run commissions
      // Remove adjustment for FX crystal user and unlock statements
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 1200000);
      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Recover Draw" }).click();
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page.getByRole("menuitem", { name: "Recover Draw" }).click();
      await page.getByLabel("Payee*").click();
      await page.getByText("Fx Crystal User").click();
      await page.getByLabel("Fiscal Year*").click();
      const now = new Date();
      const year = now.getFullYear();
      await page.getByRole("listitem").getByText("2024").click();
      await page.getByLabel("Period*").click();
      const options = { month: "short" };
      const formatter = new Intl.DateTimeFormat("en-US", options);
      const month = formatter.format(now);
      await page.locator(`span[title="${month}"]`).click();
      await page.getByLabel("Amount*").click();
      await page.getByLabel("Amount*").fill("1000");
      await page.getByRole("button", { name: "Submit" }).click();
      await expect(
        await page.getByText("Adjustment saved successfully")
      ).toBeVisible();
      const options1 = { month: "long" };
      const formatter1 = new Intl.DateTimeFormat("en-US", options1);
      const month1 = formatter1.format(now);
      await page.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await page
        .locator("span")
        .filter({ hasText: "Selected Payees" })
        .nth(1)
        .click();
      await page.getByTestId("ever-select").locator("div").nth(1).click();
      await page.getByText("Fx Crystal User").click();
      await page.getByPlaceholder("Select month").click();
      await page.getByText(month, { exact: true }).click();
      await page.getByRole("button", { name: "Run", exact: true }).click();
      await page.getByRole("button", { name: "Skip & Run" }).click();
      await expect(
        await page.getByText("Calculating Commissions...")
      ).toBeVisible();
      await expect(
        await page.getByText("Commission Calculations Completed")
      ).toBeVisible({
        timeout: 800000,
      });
      await setPayoutsPeriod(page, `${month1} ${year}`);
      await page.getByRole("link", { name: "Fx Crystal User" }).click();
      await expect(await page.getByText("₹3,000.00").first()).toBeVisible();
      await page.goto(
        "/crystal/preview/01222e37-7f0a-4122-bcd8-b3ecda714453/<EMAIL>",
        { waitUntil: "networkidle" }
      );
      await page.getByText("₹3,000.00Current Payout").click();

      await page.goto("/settings/basic", { waitUntil: "networkidle" });
      await page
        .locator("div")
        .filter({ hasText: /^Select currency$/ })
        .nth(3)
        .click();
      await page.locator("span").filter({ hasText: "INR" }).click();
      await page.getByPlaceholder("Start month").click();
      await page.getByPlaceholder("Start month").fill(`${month}-${year}`);
      await page.getByPlaceholder("Start month").press("Enter");
      await page.getByPlaceholder("End month").click();
      await page.getByPlaceholder("End month").fill(`${month}-${year}`);
      await page.getByPlaceholder("End month").press("Enter");
      await page.getByRole("spinbutton").click();
      await page.getByRole("spinbutton").fill("2.5");
      await page.getByRole("button", { name: "Update" }).click();
      await expect(
        await page.getByText("Updated Successfully!!")
      ).toBeVisible();
      await setPayoutsPeriod(page, `${month1} ${year}`);
      // await page
      //   .getByTestId("<EMAIL>")
      //   .getByText("₹2000.00")
      //   .click();
      await expect(
        await page.getByTestId("<EMAIL>")
      ).toHaveText("₹3,000.00");
      await page.getByRole("link", { name: "Fx Crystal User" }).click();
      await expect(await page.getByText("₹3,000.00").first()).toBeVisible();
      await page.goto(
        "/crystal/preview/01222e37-7f0a-4122-bcd8-b3ecda714453/<EMAIL>?kd=2024-03-31"
      );
      await page.getByText("₹3,000.00Current Payout").click();
      await page.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await page
        .locator("span")
        .filter({ hasText: "Selected Payees" })
        .nth(1)
        .click();
      await page.getByTestId("ever-select").locator("div").nth(1).click();
      await page.getByText("Fx Crystal User").click();
      await page.getByPlaceholder("Select month").click();
      await page.getByText(month, { exact: true }).click();
      await page.getByRole("button", { name: "Run", exact: true }).click();
      await page.getByRole("button", { name: "Skip & Run" }).click();
      await expect(
        await page.getByText("Calculating Commissions...")
      ).toBeVisible();
      await expect(
        await page.getByText("Commission Calculations Completed")
      ).toBeVisible({
        timeout: 800000,
      });
      await setPayoutsPeriod(page, `${month1} ${year}`);
      await expect(
        await page
          .getByTestId("<EMAIL>")
          .getByText("₹7,500.00")
      ).toBeVisible();
      await page.getByRole("link", { name: "Fx Crystal User" }).click();
      await expect(await page.getByText("₹7,500.00").first()).toBeVisible();
      await page.goto(
        "/crystal/preview/01222e37-7f0a-4122-bcd8-b3ecda714453/<EMAIL>?kd=2024-03-31"
      );
      await expect(
        await page.getByText("₹7,500.00Current Payout")
      ).toBeVisible();
    });

    test("FX and Draw adjustment flow in comm report and summary sheets", async ({
      adminPage,
    }, testInfo) => {
      // Change fx for GBP to 1 for June 2024 and run commissions
      // Com report FX change user should be unlocked
      const page = adminPage.page;
      const commonpage = new CommonUtils(page);
      testInfo.setTimeout(testInfo.timeout + 1200000);
      await setPayoutsPeriod(page, "June 2024");
      await page.getByRole("link", { name: "Comm report FX change" }).click();
      await expect(await page.getByText("£3,000.00").first()).toBeVisible();
      await page.getByTestId("lock-unlock-button").click();
      await expect(
        await page.getByText("Statement locked successfully")
      ).toBeVisible();
      await page.goto("/settings/basic", { waitUntil: "networkidle" });
      await page
        .locator("div")
        .filter({ hasText: /^Select currency$/ })
        .nth(3)
        .click();
      await page.locator("span").filter({ hasText: "GBP" }).click();
      await page.getByPlaceholder("Start month").click();
      await page.getByPlaceholder("Start month").fill("Jun-2024");
      await page.getByPlaceholder("Start month").press("Enter");
      await page.getByPlaceholder("End month").click();
      await page.getByPlaceholder("End month").fill("Jun-2024");
      await page.getByPlaceholder("End month").press("Enter");
      await page.getByRole("spinbutton").click();
      await page.getByRole("spinbutton").fill("2.0");
      await page.getByRole("button", { name: "Update" }).click();
      await expect(
        await page.getByText("Updated Successfully!!")
      ).toBeVisible();
      await page.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await page
        .locator("span")
        .filter({ hasText: "Selected Payees" })
        .nth(1)
        .click();
      await page.getByTestId("ever-select").locator("div").nth(1).click();
      await page.getByText("Comm report FX change").click();
      await commonpage.setSelectMonthComboBox("2024-06");
      await page
        .locator("span")
        .filter({ hasText: "Refresh databooks & then" })
        .first()
        .click();
      await page.getByRole("button", { name: "Run", exact: true }).click();
      await page.getByRole("button", { name: "Skip & Run" }).click();
      await expect(
        await page.getByText("Calculating Commissions...")
      ).toBeVisible();
      await expect(
        await page.getByText("Commission Calculations Completed")
      ).toBeVisible({
        timeout: 800000,
      });
      await page.goto("/databook", { waitUntil: "networkidle" });
      await page.getByRole("link", { name: "Comm Summary" }).click();
      await page.getByRole("button", { name: "Add Filter" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Column$/ })
        .nth(3)
        .click();
      await page
        .locator("div[role='listitem']>div>span[title='Payee Email']")
        .filter({ hasText: "Payee Email" })
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Operator$/ })
        .nth(3)
        .click();
      await page.getByText("Equal To", { exact: true }).click();
      await page.getByRole("textbox").nth(1).click();
      await page
        .getByRole("textbox")
        .nth(1)
        .fill("<EMAIL>");
      await page.getByRole("button", { name: "Apply", exact: true }).click();
      await page.getByTestId("login-indicator").getByText("3,000").click();
      await page.goto("/databook", { waitUntil: "networkidle" });
      await page.getByRole("link", { name: "Report" }).click();
      await page.getByRole("button", { name: "Add Filter" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Column$/ })
        .nth(3)
        .click();
      await page
        .locator("div[role='listitem']>div>span[title='Payee Email']")
        .filter({ hasText: "Payee Email" })
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Operator$/ })
        .nth(3)
        .click();
      await page.getByText("Equal To", { exact: true }).click();
      await page.getByRole("textbox").nth(1).click();
      await page
        .getByRole("textbox")
        .nth(1)
        .fill("<EMAIL>");
      await page.getByRole("button", { name: "Apply", exact: true }).click();
      await page.locator('[data-testid="pt-1"]').first().click();
      await setPayoutsPeriod(page, "June 2024");
      await page.getByRole("link", { name: "Comm report FX change" }).click();
      await expect(await page.getByText("£3,000.00").first()).toBeVisible();
      await page.getByTestId("lock-unlock-button").click();
      await expect(await page.getByText("£6,000.00").first()).toBeVisible();
      await page.goto("/databook", { waitUntil: "networkidle" });
      await page.getByRole("link", { name: "Comm Summary" }).click();
      await page.getByRole("button", { name: "Update Data" }).click();
      await expect(
        await page.getByText("Datasheet sync request has")
      ).toBeVisible();
      await expect(
        await page.getByText("Datasheet has been generated")
      ).toBeVisible({
        timeout: 180000,
      });
      await page.getByRole("button", { name: "Add Filter" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Column$/ })
        .nth(3)
        .click();
      await page
        .locator("div[role='listitem']>div>span[title='Payee Email']")
        .filter({ hasText: "Payee Email" })
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Operator$/ })
        .nth(3)
        .click();
      await page.getByText("Equal To", { exact: true }).click();
      await page.getByRole("textbox").nth(1).click();
      await page
        .getByRole("textbox")
        .nth(1)
        .fill("<EMAIL>");
      await page.getByRole("button", { name: "Apply", exact: true }).click();
      await expect(
        await page.getByRole("gridcell", { name: "3,000" })
      ).toBeVisible();
      await page.goto("/databook", { waitUntil: "networkidle" });
      await page.getByRole("link", { name: "Report" }).click();
      await page.getByRole("button", { name: "Add Filter" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Column$/ })
        .nth(3)
        .click();
      await page
        .locator("div[role='listitem']>div>span[title='Payee Email']")
        .filter({ hasText: "Payee Email" })
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Operator$/ })
        .nth(3)
        .click();
      await page.getByText("Equal To", { exact: true }).click();
      await page.getByRole("textbox").nth(1).click();
      await page
        .getByRole("textbox")
        .nth(1)
        .fill("<EMAIL>");
      await page.getByRole("button", { name: "Apply", exact: true }).click();
      await page.locator('[data-testid="pt-1"]').first().click();
    });
  }
);
