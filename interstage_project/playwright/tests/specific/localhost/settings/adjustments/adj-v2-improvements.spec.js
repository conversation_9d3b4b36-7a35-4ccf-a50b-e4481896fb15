import AdjustementsV2Page from "../../../../../test-objects/adjustments-v2-objects";

const {
  adjustmentv2Fixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  const exitButton = await page.getByRole("button", {
    name: "Exit",
    exact: true,
  });
  if (await exitButton.isVisible()) {
    await exitButton.click();
  }
});

test.describe(
  "Adjustments V2 Automation",
  { tag: ["@commissionAdjustments", "@regression", "@primelogic-3"] },
  () => {
    test(
      "Add an adjustment with an existing workflow and custom reason category, and verify able to select all reason(Custom)",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T17819, INTER-T17821,INTER-22536",
          },
          {
            type: "Description",
            description:
              "Validate whether user can add an adjustment with existing approval workflow and custom reason category. Also verify able to select all reason(Custom)",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "Adjustments should be reflected on the statements screen.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        // Navigate to approval settings and enable the workflow
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickCommissionAdjustmentBtn();
        await page.getByLabel("Reason Category").click();
        const categories = [
          { label: "Calculation Issue", text: "Calculation Issue" },
          { label: "CRM Issue", text: "CRM Issue" },
          { label: "Others", text: "Others" },
          { label: "new category 2", text: "new category" },
          { label: "New category 3", text: "New category" },
          { label: "Test category", text: "Test category" },
          { label: "Test reason", text: "Test reason" },
        ];
        for (const { label, text } of categories) {
          await page
            .locator("div")
            .filter({ hasText: new RegExp(`^${label}$`) })
            .nth(1)
            .click();
          await page.getByLabel("Add Adjustment").getByText(text).click();
        }
        await page.getByLabel("Close").click();
        await adjustmentV2.ToggleApprovalsAdj();
        await adjustmentV2.setUserWhenApprovalIsOverridden("Boss Payee Week");
        await expect(page.getByText("User notify list saved.")).toBeVisible();

        // Navigate to adjustments page and add an adjustment
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.addCommissionAdjustment(
          "payee 3",
          "January 2024",
          "1000",
          "payee 3&4 plan",
          "simple",
          "New category 3",
          "10",
          "Test"
        );

        // Submit adjustment and validate
        await adjustmentV2.clickWorkflowInAdjustment("commission adjustments");
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentValidation();
      }
    );

    test(
      "Validate that clicking the search icon sets focus on the search bar",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18509, INTER-T18510)" },
          {
            type: "Description",
            description:
              "Validate clicking the search icon focuses the search bar, and pressing ESC reverts it.",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description: "Search bar focus and ESC functionality should work.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");

        // Click search icon and verify focus
        await adjustmentV2.clickSearchButton();
        await expect(page.getByPlaceholder("Search")).toBeVisible();
      }
    );

    test(
      "Search functionality",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description: "Validate search filters functionality.",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description: "Search filters should work correctly.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        // Navigate to adjustments and use search functionality
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickSearchButton();
        await expect(page.getByPlaceholder("Search")).toBeVisible();
        await page.getByPlaceholder("Search").fill("payee 3");

        // Validate filtered results
        try {
          await expect(
            page.getByRole("gridcell", { name: "payee 3", exact: true })
          ).toBeVisible();
          console.log("Filter applied successfully.");
        } catch (error) {
          console.error("'payee 3' not visible:", error.message);
          throw error;
        }
      }
    );

    test(
      "Add a payee with no commission data in dropdown",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18519" },
          {
            type: "Description",
            description:
              "Add a payee without commission data and check effective period.",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description: "No data should be displayed.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        // Navigate and add a payee with no data
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickCommissionAdjustmentBtn();
        await adjustmentV2.addPayee("Boss Payee Week");
        await page.getByLabel("Effective Period*").click();

        // Validate no data is shown
        await expect(page.getByText("No Data")).toBeVisible();
      }
    );

    test(
      "No commission plan Validation and Multiple commission plan validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T22531,INTER-T22532" },
          {
            type: "Description",
            description:
              "Add a payee without commission plan and check effective period. Check all commission plans displayed and able to select",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "No data should be displayed. All plan should be displayed",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickCommissionAdjustmentBtn();
        await page.getByLabel("Commission Plan").click();

        // Validate no data is shown
        await expect(page.getByText("No Data")).toBeVisible();

        await adjustmentV2.closeAdjustmentModal();
        await adjustmentV2.clickCommissionAdjustmentBtn();
        await adjustmentV2.addPayee("Payee 1");
        await adjustmentV2.selectEffectivePeriod("January 2024");
        await page.getByLabel("Commission Plan").click();
        const categories = [
          {
            label: "Commission_plan_Payee_1(2024)",
            text: "Commission_plan_Payee_1(2024)",
          },
          { label: "Monthly", text: "Monthly" },
        ];
        for (const { label, text } of categories) {
          await page.locator(`div[title="${label}"]`).click();
          await page.getByLabel("Add Adjustment").getByText(text).click();
        }
        await page.getByLabel("Close").click();
      }
    );

    test(
      "No Compoenent Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T22533" },
          {
            type: "Description",
            description:
              "Add a payee without component and check effective period.",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description: "No data should be displayed.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickCommissionAdjustmentBtn();
        await page.getByLabel("Component Name").click();

        // Validate no data is shown
        await expect(page.getByText("No Data")).toBeVisible();
      }
    );

    test(
      "All Payees Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T22525" },
          {
            type: "Description",
            description:
              "Validate that all payees are listed in the Payees dropdown of the 'Add Adjustment' modal",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "All the Payees should be displayed in the Payees dropdown",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const assert = require("assert");
        const adjustmentV2 = new AdjustementsV2Page(page);
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickCommissionAdjustmentBtn();
        await page.getByLabel("Payee*").click();

        const allPayees = await adjustmentV2.getDropdownValues();

        const expectedPayees = [
          "Boss Payee Half",
          "Boss Payee Week",
          "Payee 1",
          "payee 3",
          "payee 4",
          "Payee Annual",
          "Payee Currency Change",
          "Payee Half",
          "Payee Multiple",
          "Payee Quar",
          "payee testdb",
          "Payee Week",
          "Sample Payee E2E",
        ];

        const allCheck = expectedPayees.some((item) =>
          allPayees.includes(item)
        );

        try {
          assert.strictEqual(
            allCheck,
            true,
            "All the Payees aren't showing up in the dropdown"
          );
          console.log("All the Payees are showing up in the dropdown");
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Check whether editing an existing adjustment affects the reason category",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18529" },
          {
            type: "Description",
            description:
              "Validate if editing a field in an adjustment affects the reason category.",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description: "Reason category should remain unaffected.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        // Navigate to adjustments and edit an existing adjustment
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.editAdjustments("1");

        // Capture the initial reason category value
        const reasonCategory = await page
          .locator("input[id='commission form_reasonCategoryId']")
          .inputValue();
        console.log(`Reason category value: ${reasonCategory}`);

        // Replace ID in line item and validate update
        await adjustmentV2.replaceIdinLineItemid("20");
        await adjustmentV2.clickWorkflowInAdjustment("commission adjustments");
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentUpdateValidation();

        // Verify reason category remains unchanged after edit
        await adjustmentV2.editAdjustments("1");
        await expect(
          page.locator("//input[@id='commission form_reasonCategoryId']")
        ).toHaveValue(reasonCategory);

        // Close the adjustment modal
        await adjustmentV2.closeAdjustmentModal();
      }
    );

    test(
      "Validate editing custom category in an adjustment",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18531" },
          {
            type: "Description",
            description:
              "Ensure the user can change the custom category when editing an adjustment.",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description: "User should be able to change the category.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        // Navigate and edit adjustment to change custom category
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.editAdjustments(0);
        await page.getByTitle("New category 3").click();
        // await page.getByText("Test category").nth(1).click();
        await page
          .locator('span.inline-block.w-full.truncate[title="Test category"]')
          .click();

        // Submit and validate the adjustment
        await adjustmentV2.clickWorkflowInAdjustment("commission adjustments");
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.adjustmentUpdateValidation();

        // Verify new category is updated correctly
        await adjustmentV2.editAdjustments(0);
        await expect(
          page
            .getByLabel("Edit Adjustment")
            .getByText("Test category", { exact: true })
        ).toBeVisible();
        await adjustmentV2.closeAdjustmentModal();
      }
    );

    test(
      "Add an adjustment with a new custom workflow and category",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18521" },
          {
            type: "Description",
            description:
              "Ensure user can add adjustment with a custom workflow and category.",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "Adjustment with custom workflow and category should be added successfully.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        // Navigate and add adjustment with custom workflow
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.addCommissionAdjustment(
          "payee 3",
          "January 2024",
          "1000",
          "payee 3&4 plan",
          "simple",
          "New category 3",
          "1000",
          "customworkflow"
        );
        await adjustmentV2.createCustomWorkflow("<EMAIL>");

        // Validate the adjustment
        await adjustmentV2.adjustmentValidation();
      }
    );

    test(
      "Add an adjustment with 'Skip approval workflow' checkbox",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T17819, INTER-T17821" },
          {
            type: "Description",
            description:
              "Validate auto-approval of adjustment using 'Skip approval workflow' checkbox.",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description: "Adjustment should be auto-approved.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        // Add adjustment with 'Skip approval workflow' enabled
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.addCommissionAdjustment(
          "payee 3",
          "January 2024",
          "20000",
          "payee 3&4 plan",
          "simple",
          "New category 3",
          "20000",
          "customworkflow"
        );
        await adjustmentV2.skipApprovalWorkflow("skip");

        // Validate adjustment is auto-approved
        await adjustmentV2.adjustmentValidation();
        await expect(
          page.getByRole("gridcell", { name: "approved" })
        ).toBeVisible();
      }
    );

    test(
      "Check bulk delete functionality",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18511,INTER-24185" },
          {
            type: "Description",
            description:
              "Validate whether bulk delete works correctly when both locked and unlocked statements are present.",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description: "User should be able to delete adjustments in bulk.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Jan 2024");
        await adjustmentV2.lockStatements("<EMAIL>");
        // Navigate, select all adjustments, and delete them
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickAllAdjustmentCheckbox();
        await adjustmentV2.clickDeleteButton();

        // Verify success message
        await expect(page.getByText("7 Adjustments deleted")).toBeVisible();

        await adjustmentV2.ToggleApprovalsAdj();
        await adjustmentV2.clickConfirmToggleOff();
      }
    );
  }
);
