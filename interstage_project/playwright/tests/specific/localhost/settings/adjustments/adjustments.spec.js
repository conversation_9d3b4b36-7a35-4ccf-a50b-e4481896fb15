const {
  localplayFixtures: { test, expect },
} = require("../../../../fixtures");

function formatDateToMonthYear(date) {
  return date.toLocaleDateString("en-us", { year: "numeric", month: "long" });
}

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
});

test.describe(
  "Adjustments",
  { tag: ["@commissionAdjustments", "@regression", "@primelogic-3"] },
  () => {
    test("Add commission adjustment", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page.getByRole("menuitem", { name: "Commission" }).click();
      await page.getByLabel("Payee").click();
      await page.getByTitle("<PERSON>").first().click();
      await page.getByLabel("Effective Period").click();
      await page.getByText("February 2023").first().click();
      await page.locator('[id="commission\\ form_amount_newAmount"]').fill("1");
      await page.getByLabel("Commission Plan").click();
      await page.getByTitle("Approvals").getByText("Approvals").click();
      await page.getByRole("button", { name: "Submit" }).click();
      const comAdjustStatement = await page.getByText(
        "Adjustment saved successfully"
      );
      await page.waitForTimeout(2000);
      await comAdjustStatement.waitFor({ state: "visible", timeout: 5000 });
      await page.goto(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJsYWtzaG1pQGV2ZXJzdGFnZS5jb20iLCJwc2QiOiIyMDIzLTAyLTAxIiwicGVkIjoiMjAyMy0wMi0yOCJ9",
        { waitUntil: "networkidle" }
      );
      await page
        .getByText("Profile")
        .waitFor({ state: "visible", timeout: 15000 });
      await page.getByText("Adjustments").click();
      await expect(
        page.locator("//div[@col-id='amount']//span[text()='₹1.00']")
      ).toBeVisible();

      // delete adjustment
      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.locator('(//div[@role="row"]//button)[2]').click();
      await page.getByRole("menuitem", { name: "Delete" }).click();
      await page.getByRole("button", { name: "Yes" }).click();
      const comDelete = await page.getByText("Adjustment deleted successfully");
      await comDelete.waitFor({ state: "visible", timeout: 5000 });
    });

    test("Add draw adjustment", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page.getByRole("menuitem", { name: "Recover Draw" }).click();
      await page.getByLabel("Payee").click();
      await page.getByLabel("Payee").press("CapsLock");
      await page.getByLabel("Payee").fill("Request");
      await page.getByText("Request User").click();
      await page.getByLabel("Fiscal Year").click();
      await page.getByTitle("2023").getByText("2023").click();
      await page.getByLabel("Period").click();
      await page.getByTitle("Jan").getByText("Jan").click();
      await page.getByLabel("Amount").click();
      await page.getByLabel("Amount").fill("`");
      await page.getByLabel("Amount").click();
      await page.getByLabel("Amount").fill("1");
      await page.getByLabel("Comments").click();
      await page.getByLabel("Comments").fill("test");
      await page.waitForTimeout(2000);
      await page.getByRole("button", { name: "Submit" }).click();
      const drawAdjustStatement = await page.getByText(
        "Adjustment saved successfully"
      );
      await drawAdjustStatement.waitFor({ state: "visible", timeout: 10000 });
      await page.goto("/commissions", { waitUntil: "networkidle" });
      const formattedDate = formatDateToMonthYear(new Date());
      await page.getByText(formattedDate).first().click();
      await page.getByTitle("June 2023").first().click();
      await page.getByRole("link", { name: "Request User" }).click();
      await page
        .getByText("Profile")
        .waitFor({ state: "visible", timeout: 5000 });

      await page.getByTitle("June 2023", { exact: true }).click();
      await page.getByText("January 2023").click();
      await page.getByText("Draw Adjustments").click();
      await page.getByText("Draw Recovered").isVisible();
      await page.getByRole("gridcell", { name: "₹1.00" }).isVisible();

      // delete adjustment
      await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Recover Draw" }).click();
      // await page
      //   .getByRole("row", { name: /Request User 2023 Jan.*. 1 test/ })
      //   .locator("svg")
      //   .click();
      const rowId = await page.locator(
        "//span[text()='Request User']/../../.."
      );
      const attribute = await rowId.getAttribute("row-id");
      console.log(attribute);
      await page
        .locator(
          "div.ag-pinned-right-cols-container div.ag-row-level-0[row-index='" +
            attribute +
            "']"
        )
        .last()
        .click();
      await page.getByRole("button", { name: "Yes" }).click();
      const deleteDraw = await page.getByText(
        "Adjustment Deleted Successfully"
      );
      await deleteDraw.waitFor({ state: "visible", timeout: 5000 });
    });

    test("Delete draw adjustment", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.getByRole("button", { name: "Recover Draw" }).click();
      // await page
      //   .getByRole("row", { name: "Delete Adjustment" })
      //   .locator("svg")
      //   .click();
      const rowId = await page.locator(
        "//span[text()='Delete Adjustment']/../../.."
      );
      const attribute = await rowId.getAttribute("row-id");
      console.log(attribute);
      await page
        .locator(
          "div.ag-pinned-right-cols-container div.ag-row-level-0[row-index='" +
            attribute +
            "']"
        )
        .last()
        .click();
      await page.getByRole("button", { name: "Yes" }).click();
      const deleteDraw = await page.getByText(
        "Adjustment Deleted Successfully"
      );
      await deleteDraw.waitFor({ state: "visible", timeout: 5000 });
    });
    test("delete adjustment when payouts locked", async ({ adminPage }) => {
      const page = adminPage.page;
      const elem = await page
        .getByRole("row", {
          name: /Lock user 01-Mar-2023 31-Mar-2023 ₹ 1.00 INR.*.Approvals/,
        })
        .getByRole("button");
      await expect(elem).toHaveCount(0);
    });
  }
);
