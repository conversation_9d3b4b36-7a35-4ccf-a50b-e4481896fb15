const CommonUtils = require("../../../../../test-objects/common-utils-objects");
const {
  commissionAdjustmentFixtures: { test, expect },
} = require("../../../../fixtures");
const XLSX = require("xlsx");

test.beforeEach(async ({ adminPage, payeePage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 1200000);
  const page = adminPage.page;
  const exitButton = page.locator('role=button[name="Exit"]');
  if ((await exitButton.count({ timeout: 5000 })) > 0) {
    await exitButton.click();
    await page.waitForTimeout(5000);
  }
  await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
  const page1 = payeePage.page;
  await page1.goto("/settings/adjustments", { waitUntil: "networkidle" });
});

test.describe(
  "Commission Adjustments tests",
  { tag: ["@commissionAdjustments", "@regression", "@primelogic-3"] },
  () => {
    test.describe("Comm Adjustments - Flow check", () => {
      test("Check flow for locked user before fx update and before running sync", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.goto("/commissions", { waitUntil: "networkidle" });
        const value = await page
          .getByTestId("period-select")
          .locator("div")
          .first()
          .innerText();
        if (value !== "June 2024") {
          await page
            .getByTestId("period-select")
            .locator("div")
            .first()
            .click();
          await page.getByText("June 2024").click();
        }
        await page.getByRole("link", { name: "Flow Check" }).click();
        await page.waitForLoadState("networkidle");
        try {
          await page.waitForTimeout(3000);
          await page.getByTestId("lock-unlock-button").click(); // lock
          await expect(
            await page.getByText("Statement locked successfully")
          ).toBeVisible();
        } catch {
          await page.getByTestId("lock-unlock-button").click(); // unlock
          await page.waitForTimeout(3000);
          await page.getByTestId("lock-unlock-button").click(); // lock
          await expect(
            await page.getByText("Statement locked successfully")
          ).toBeVisible();
        }

        await page.getByText("AL$2,280.00").first().click();
        await page.goto("/commissions", { waitUntil: "networkidle" });
        await page
          .getByTestId("<EMAIL>")
          .getByText("AL$2,280.00")
          .click();
        await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
        await page.getByPlaceholder("Search").click();
        await page
          .getByPlaceholder("Search")
          .fill("<EMAIL>");
        await page.getByText("AL$150.00").click();
        await page.goto("/databook", { waitUntil: "networkidle" });
        await page.getByRole("link", { name: "Commission summary" }).click();
        await page
          .getByRole("Button", { name: "Update Data", exact: true })
          .click();
        await page
          .getByText("This datasheet is currently being generated")
          .waitFor({ state: "visible" });
        await page
          .getByText("This datasheet is currently being generated")
          .waitFor({ state: "hidden", timeout: 240000 });
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page
          .getByRole("textbox")
          .nth(1)
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(
          await page.getByTestId("login-indicator").getByText("2,130")
        ).toBeVisible();
        // await expect(
        //   await page.getByTestId("login-indicator").getByText("150")
        // ).toBeVisible();
        await page.getByRole("button", { name: "Clear filter" }).click();
        await page.getByText("Commission report").click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page
          .getByRole("textbox")
          .nth(1)
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(await page.getByText("1,050").first()).toBeVisible();
        await expect(await page.getByText("1,080").first()).toBeVisible();
        await expect(await page.getByText("150").first()).toBeVisible();
        await page.getByRole("button", { name: "Clear filter" }).click();
        await page.goto("/users", { waitUntil: "networkidle" });
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page.getByRole("button", { name: "Login as user" }).click();
        // await expect(
        //   await page.getByText("Logging in as Flow Check")
        // ).toBeVisible();
        await page.waitForTimeout(5000);
        await page.getByPlaceholder("Select year").click();
        await page.getByPlaceholder("Select year").fill("2024");
        await page.getByPlaceholder("Select year").press("Enter");
        await expect(await page.getByText("AL$2.28K")).toBeVisible();
        await page.getByRole("button", { name: "Exit" }).click();
        await page.waitForTimeout(5000);
        await page.goto("/commissions", { waitUntil: "networkidle" });
        await page.getByRole("link", { name: "Flow Check" }).click();
        await page.getByTestId("statement-menu").click();
        await page.getByRole("button", { name: "Export statement" }).hover();
        await page.getByText("Microsoft Excel (.xlsx)").click();
        const downloadPromise = page.waitForEvent("download");
        await expect(
          await page.getByText("Exporting statements as Excel")
        ).toBeVisible();
        const download = await downloadPromise;
        const filePath = await download.path();
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(worksheet);
        const totPayout = data[data.length - 1].__EMPTY;
        expect(totPayout).toBe(2280);
      });

      test("Check flow for unlocked user before fx update and before running sync", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.goto("/commissions", { waitUntil: "networkidle" });
        const value = await page
          .getByTestId("period-select")
          .locator("div")
          .first()
          .innerText();
        if (value !== "June 2024") {
          await page
            .getByTestId("period-select")
            .locator("div")
            .first()
            .click();
          await page.getByText("June 2024").click();
        }
        await page.getByRole("link", { name: "Flow Check" }).click();
        await page.waitForLoadState("networkidle");
        try {
          await page.waitForTimeout(3000);
          await page.getByTestId("lock-unlock-button").click(); // lock
          await expect(
            await page.getByText("Statement locked successfully")
          ).toBeVisible();
        } catch {
          await page.getByTestId("lock-unlock-button").click(); // unlock
          await page.waitForTimeout(3000);
          await page.getByTestId("lock-unlock-button").click(); // lock
          await expect(
            await page.getByText("Statement locked successfully")
          ).toBeVisible();
        }

        await page.getByText("AL$2,280.00").first().click();
        await page.goto("/commissions", { waitUntil: "networkidle" });
        await page
          .getByTestId("<EMAIL>")
          .getByText("AL$2,280.00")
          .click();
        await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
        await page.getByPlaceholder("Search").click();
        await page
          .getByPlaceholder("Search")
          .fill("<EMAIL>");
        await page.getByText("AL$150.00").click();
        await page.goto("/databook", { waitUntil: "networkidle" });
        await page.getByRole("link", { name: "Commission summary" }).click();
        await page.getByText("Comm Summary").click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page
          .getByRole("textbox")
          .nth(1)
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(
          await page.getByTestId("login-indicator").getByText("2,130")
        ).toBeVisible();
        await expect(
          await page.getByTestId("login-indicator").getByText("150")
        ).toBeVisible();
        await page.getByRole("button", { name: "Clear filter" }).click();
        await page.getByText("Commission report").click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page
          .getByRole("textbox")
          .nth(1)
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(await page.getByText("1,050").first()).toBeVisible();
        await expect(await page.getByText("1,080").first()).toBeVisible();
        await expect(await page.getByText("150").first()).toBeVisible();
        await page.getByRole("button", { name: "Clear filter" }).click();
        await page.goto("/users", { waitUntil: "networkidle" });
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page.getByRole("button", { name: "Login as user" }).click();
        // await expect(
        //   await page.getByText("Logging in as Flow Check")
        // ).toBeVisible();
        await page.waitForTimeout(10000);
        await page
          .getByText("Payouts Tracker")
          .last()
          .waitFor({ state: "visible" });
        await page.getByPlaceholder("Select year").last().click();
        await page.getByPlaceholder("Select year").last().fill("2024");
        await page.getByPlaceholder("Select year").last().press("Enter");
        await expect(await page.getByText("AL$2.28K")).toBeVisible();
        await page.getByRole("button", { name: "Exit" }).click();
        await page.waitForTimeout(5000);
        await expect(async () => {
          // June 2024 - Flow check user
          await page.goto(
            "/statements/eyJwYXllZUVtYWlsSWQiOiJmbG93Y2hlY2t1c2VyQGNvbW0tYWRqcy5jb20iLCJwc2QiOiIyMDI0LTA2LTAxIiwicGVkIjoiMjAyNC0wNi0zMCJ9"
          );
          await page.getByTestId("statement-menu").click();
          await page.getByRole("button", { name: "Export statement" }).hover();
          await page.getByText("Microsoft Excel (.xlsx)").click();
        }).toPass({
          timeout: 200000,
          intervals: [5000],
        });
        const downloadPromise = page.waitForEvent("download");
        await expect(
          await page.getByText("Exporting statements as Excel")
        ).toBeVisible();
        const download = await downloadPromise;
        const filePath = await download.path();
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(worksheet);
        const totPayout = data[data.length - 1].__EMPTY;
        expect(totPayout).toBe(2280);
      });

      test.skip("Check flow for unlocked user after fx update and before running sync", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.goto("/settings/basic", { waitUntil: "networkidle" });
        await page
          .locator("div")
          .filter({ hasText: /^Select currency$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "ALL" }).click();
        await page.getByPlaceholder("Start month").click();
        await page.getByPlaceholder("Start month").fill("Jun-2024");
        await page.getByPlaceholder("Start month").press("Enter");
        await page.getByPlaceholder("End month").click();
        await page.getByPlaceholder("End month").fill("Jun-2024");
        await page.getByPlaceholder("End month").press("Enter");
        await page.getByRole("spinbutton").click();
        await page.getByRole("spinbutton").fill("2.0");
        await page.getByRole("button", { name: "Update" }).click();
        await page.getByText("Updated Successfully!!").click();
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        await page
          .locator("span")
          .filter({ hasText: "Selected Payees" })
          .nth(1)
          .click();
        await page.getByTestId("ever-select").locator("div").nth(1).click();
        await page.getByText("Flow Check").click();
        await page.getByPlaceholder("Select month").click();
        await page.getByText("Jun", { exact: true }).click();
        await page.getByRole("button", { name: "Run", exact: true }).click();
        await page.getByRole("button", { name: "Skip & Run" }).click();
        await expect(
          await page.getByText("Calculating Commissions...")
        ).toBeVisible();
        await expect(
          await page.getByText("Commission Calculations")
        ).toBeVisible({
          timeout: 300000,
        });
        try {
          await page.waitForTimeout(10000);
          await page.goto("/commissions", { waitUntil: "networkidle" });
          const value = await page
            .getByTestId("period-select")
            .locator("div")
            .innerText();
          if (value !== "June 2024") {
            await page.getByTestId("period-select").locator("div").click();
            await page.getByText("June 2024").click();
          }
          await page
            .getByTestId("<EMAIL>")
            .getByText("AL$4,410.00")
            .click();
        } catch {
          await page.reload();
          await page.goto("/commissions", { waitUntil: "networkidle" });
          const value = await page
            .getByTestId("period-select")
            .locator("div")
            .innerText();
          if (value !== "June 2024") {
            await page.getByTestId("period-select").locator("div").click();
            await page.getByText("June 2024").click();
          }
          await page
            .getByTestId("<EMAIL>")
            .getByText("AL$4,410.00")
            .click();
        }
        await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
        await page.getByPlaceholder("Search").click();
        await page
          .getByPlaceholder("Search")
          .fill("<EMAIL>");
        await page.getByText("AL$150.00").click();
        await page.goto("/databook", { waitUntil: "networkidle" });
        await page.getByRole("link", { name: "Commission summary" }).click();
        await page.getByText("Comm Summary").click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page
          .getByRole("textbox")
          .nth(1)
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(
          await page.getByTestId("login-indicator").getByText("2,130")
        ).toBeVisible();
        await expect(
          await page
            .getByTestId("login-indicator")
            .getByText("75", { exact: true })
        ).toBeVisible();
        await page.getByRole("button", { name: "Clear filter" }).click();
        await page.getByText("Commission report").click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page
          .getByRole("textbox")
          .nth(1)
          .fill("<EMAIL>");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(await page.getByText("1,050").first()).toBeVisible();
        await expect(await page.getByText("1,080").first()).toBeVisible();
        await expect(await page.getByText("150").first()).toBeVisible();
        await page.getByRole("button", { name: "Clear filter" }).click();
        await page.goto("/users", { waitUntil: "networkidle" });
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page.getByRole("button", { name: "Login as user" }).click();
        await expect(
          await page.getByText("Logging in as Flow Check")
        ).toBeVisible();
        await page.waitForTimeout(5000);
        await page.getByPlaceholder("Select year").click();
        await page.getByPlaceholder("Select year").fill("2024");
        await page.getByPlaceholder("Select year").press("Enter");
        await expect(await page.getByText("AL$4.41K")).toBeVisible();
        await page.getByRole("button", { name: "Exit" }).click();
        await page.waitForTimeout(5000);
        await page.goto("/commissions", { waitUntil: "networkidle" });
        const value1 = await page
          .getByTestId("period-select")
          .locator("div")
          .innerText();
        if (value1 !== "June 2024") {
          await page.getByTestId("period-select").locator("div").click();
          await page.getByText("June 2024").click();
        }
        await page.getByRole("link", { name: "Flow Check" }).click();
        await page.getByTestId("statement-menu").click();
        await page.getByRole("button", { name: "Export statement" }).hover();
        await page.getByText("Microsoft Excel (.xlsx)").click();
        const downloadPromise = page.waitForEvent("download");
        await expect(
          await page.getByText("Exporting statements as Excel")
        ).toBeVisible();
        const download = await downloadPromise;
        const filePath = await download.path();
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(worksheet);
        const totPayout = data[data.length - 1].__EMPTY;
        expect(totPayout).toBe(4410);
      });
    });

    test.describe("Commission Adjustments", () => {
      test("Check add adjustment flow", async ({ adminPage }) => {
        const page = adminPage.page;

        await page.getByRole("button", { name: "Add Adjustment" }).click();
        await page
          .getByRole("menuitem", { name: "Commission" })
          .locator("span")
          .click();
        await page.getByLabel("Effective Period*").click();
        await expect(await page.getByText("No Data")).toBeVisible();
        await page.getByLabel("Payee*").click();
        await page.getByText("Payee test").click();
        await page.getByLabel("Effective Period*").click();
        await expect(await page.getByText("May")).toBeVisible();
        await page.getByText("May").click();
        await expect(await page.getByTitle("USD")).toBeVisible();
        await page.getByTitle("USD").click();
        await page.getByRole("listitem").getByText("USD").click();
      });

      test("Positive and negative adjustments should be in green and red colors", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        const greenElementClasses = await page
          .getByText("$100.00", { exact: true })
          .getAttribute("class");
        await expect(
          greenElementClasses.includes("text-ever-success-content-lite")
        ).toBe(true);
        const redElementClasses = await page
          .getByText("- $100.00")
          .getAttribute("class");
        await expect(
          redElementClasses.includes("text-ever-error-content-lite")
        ).toBe(true);
      });

      test("Adjustments to locked statements should be added since the feature is on", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        await page.goto("/commissions", { waitUntil: "networkidle" });
        const value = await page
          .getByTestId("period-select")
          .locator("div")
          .first()
          .innerText();
        if (value !== "June 2024") {
          await page
            .getByTestId("period-select")
            .locator("div")
            .first()
            .click();
          await page.getByText("June 2024").click();
        }
        await page.getByRole("link", { name: "User 2" }).click();
        await expect(
          await page.getByText("Denotes components that are")
        ).toBeVisible();
        try {
          await page.waitForTimeout(3000);
          await page.getByTestId("lock-unlock-button").click(); // lock
          await expect(
            await page.getByText("Statement locked successfully")
          ).toBeVisible();
        } catch {
          await page.getByTestId("lock-unlock-button").click(); // unlock
          await page.waitForTimeout(3000);
          await page.getByTestId("lock-unlock-button").click(); // lock
          await expect(
            await page.getByText("Statement locked successfully")
          ).toBeVisible();
        }

        await page.goto("/settings/adjustments", { waitUntil: "networkidle" });
        await page.getByRole("button", { name: "Add Adjustment" }).click();
        await page
          .getByRole("menuitem", { name: "Commission" })
          .locator("span")
          .click();
        await page.getByLabel("Payee*").click();
        await page.getByText("User 2").click();
        await page.getByLabel("Effective Period*").click();
        await page.getByText("June 2024 (commission locked)").click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("10");
        await page.waitForTimeout(1000);
        await page.getByRole("button", { name: "Submit" }).click();
        await page.getByText("Adjustment saved successfully").click();
        await expect(await page.getByText("$10.00")).toBeVisible();
        await expect(
          await page.locator(
            "//span[contains(@id, 'employeeName') and text()='User 2']"
          )
        ).toBeVisible();
      });

      test("Once the amount is processed, changing the fx and sync run sould not add amount in the pending amount", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        await page.goto("/commissions", { waitUntil: "networkidle" });
        const value = await page
          .getByTestId("period-select")
          .locator("div")
          .first()
          .innerText();
        if (value !== "June 2024") {
          await page
            .getByTestId("period-select")
            .locator("div")
            .first()
            .click();
          await page.getByText("June 2024").click();
        }
        await page
          .getByTestId("<EMAIL>-register-payment")
          .click();
        await expect(
          await page.getByText("You are registering full")
        ).toBeVisible();
        await page.getByRole("button", { name: "Register" }).click();
        await expect(
          await page.getByText("Payment registered successfully")
        ).toBeVisible();
        await page.waitForTimeout(2000);
        expect(
          await page
            .getByTestId("<EMAIL>-pending-amount")
            .innerText()
        ).toBe("$0.00");
        await page.goto("/settings/basic", { waitUntil: "networkidle" });
        await page
          .locator("div")
          .filter({ hasText: /^Select currency$/ })
          .nth(3)
          .click();
        await page.getByTitle("USD").nth(1).click();
        await page.getByPlaceholder("Start month").click();
        await page.getByPlaceholder("Start month").fill("Jan-2024");
        await page.getByPlaceholder("Start month").press("Enter");
        await page.getByPlaceholder("End month").click();
        await page.getByPlaceholder("End month").fill("Dec-2024");
        await page.getByPlaceholder("End month").press("Enter");
        await page.getByRole("spinbutton").click();
        await page.getByRole("spinbutton").fill("2.0");
        await page.getByRole("button", { name: "Update" }).click();
        await expect(
          await page.getByText("Updated Successfully!!")
        ).toBeVisible();
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        await page
          .locator("span")
          .filter({ hasText: "Selected Payees" })
          .nth(1)
          .click();
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Select payees" })
          .click();
        await page.getByText("Payee test").click();
        await page.getByPlaceholder("Select month").click();
        await page.getByText("Jun", { exact: true }).click();
        await page.getByRole("button", { name: "Run", exact: true }).click();
        await page.getByRole("button", { name: "Skip & Run" }).click();
        await expect(
          await page.getByText("Calculating Commissions...")
        ).toBeVisible();
        try {
          await page.waitForSelector(
            'text="Commission Calculations Completed"',
            {
              state: "visible",
              timeout: 300000,
            }
          );
        } catch {
          await page.reload();
          await page.waitForSelector(
            'text="Commission Calculations Completed"',
            {
              state: "visible",
              timeout: 15000,
            }
          );
        }

        await page.goto("/commissions", { waitUntil: "networkidle" });
        const value1 = await page
          .getByTestId("period-select")
          .locator("div")
          .first()
          .innerText();
        if (value1 !== "June 2024") {
          await page
            .getByTestId("period-select")
            .locator("div")
            .first()
            .click();
          await page
            .getByTestId("period-select")
            .getByText("June 2024")
            .click();
        }
        expect(
          await page
            .getByTestId("<EMAIL>-pending-amount")
            .innerText()
        ).toBe("$0.00");
      });

      test("Only power admin should be allowed to add ajustments on frozen periods", async ({
        adminPage,
        payeePage,
      }) => {
        const pageAdmin = adminPage.page;
        const pagePayee = payeePage.page;

        await pageAdmin.getByRole("button", { name: "Add Adjustment" }).click();
        await pageAdmin.getByRole("menuitem", { name: "Commission" }).click();
        await pageAdmin.getByLabel("Payee*").click();
        await pageAdmin.getByText("Payee test").click();
        await pageAdmin.getByLabel("Effective Period*").click();
        await pageAdmin.getByText("June 2024 (commission locked)").click();
        await pageAdmin.getByPlaceholder("Add amount").click();
        await pageAdmin.getByPlaceholder("Add amount").fill("0");
        await pageAdmin.waitForTimeout(1000);
        await pageAdmin.getByRole("button", { name: "Submit" }).click();
        await expect(
          await pageAdmin.getByText("Adjustment saved successfully")
        ).toBeVisible();
        await pagePayee.getByRole("button", { name: "Add Adjustment" }).click();
        await pagePayee
          .getByRole("menuitem", { name: "Commission" })
          .locator("span")
          .click();
        await pagePayee.getByLabel("Payee*").click();
        await pagePayee.getByText("Payee test").click();
        await pagePayee.getByLabel("Effective Period*").click();
        await pagePayee.getByText("June 2024 (commission locked)").click();
        await pagePayee.getByRole("button", { name: "Submit" }).click();
        await expect(
          await pagePayee.getByText("Please select a period!")
        ).toBeVisible();
      });

      test("On adding a commission adjustment, data should not be duplicated in commission summary sheet", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        await page.getByRole("button", { name: "Add Adjustment" }).click();
        await page
          .getByRole("menuitem", { name: "Commission" })
          .locator("span")
          .click();
        await page.getByLabel("Payee*").click();
        await page.getByText("User 3").nth(2).click();
        await page.getByLabel("Effective Period*").click();
        await page.getByText("May").click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("4569");
        await page.waitForTimeout(1000);
        await page.getByRole("button", { name: "Submit" }).click();
        await expect(
          await page.getByText("Adjustment saved successfully")
        ).toBeVisible();
        await page.goto("/databook", { waitUntil: "networkidle" });
        await page.getByRole("link", { name: "Commission summary" }).click();
        await page.getByRole("button", { name: "Update Data" }).click();
        await expect(
          await page.getByText("Datasheet sync request has")
        ).toBeVisible();
        await expect(
          await page.getByText("Datasheet has been generated")
        ).toBeVisible({ timeout: 240000 });
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("<EMAIL>");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(
          await page.getByRole("gridcell", { name: "4,569" }).first()
        ).toBeVisible();
        const gridCells = await page.locator('role=gridcell[name="4,569"]');
        const count = await gridCells.count();
        expect(count === 1 || count === 2).toBeTruthy();
      });

      test("Test flow for 2 payees in Commission Adjustments and Payouts", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        await page.getByRole("button", { name: "Add Adjustment" }).click();
        await page.getByRole("menuitem", { name: "Commission" }).click();
        await page.getByLabel("Payee*").click();
        await page.getByText("Test User1").click();
        await page.getByLabel("Effective Period*").click();
        await page
          .locator("div")
          .filter({ hasText: /^June 2024$/ })
          .nth(1)
          .click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("1500");
        await page.waitForTimeout(500);
        await page.getByRole("button", { name: "Submit" }).click();
        await expect(
          await page.getByText("Adjustment saved successfully")
        ).toBeVisible();
        await page.goto("/commissions", { waitUntil: "networkidle" });
        const value = await page
          .getByTestId("period-select")
          .locator("div")
          .first()
          .innerText();
        if (value !== "June 2024") {
          await page
            .getByTestId("period-select")
            .locator("div")
            .first()
            .click();
          await page.getByText("June 2024").click();
        }
        await page.getByRole("link", { name: "Test User1" }).click();
        await page.waitForLoadState("networkidle");
        try {
          await page.waitForTimeout(3000);
          await page.getByTestId("lock-unlock-button").click(); // lock
          await expect(
            await page.getByText("Statement locked successfully")
          ).toBeVisible();
        } catch {
          await page.getByTestId("lock-unlock-button").click(); // unlock
          await page.waitForTimeout(3000);
          await page.getByTestId("lock-unlock-button").click(); // lock
          await expect(
            await page.getByText("Statement locked successfully")
          ).toBeVisible();
        }

        await page.goto("/databook", { waitUntil: "networkidle" });
        await page
          .getByRole("link", { name: "Commission summary" })
          .click({ waitForLoadState: "networkidle" });
        // await page
        //   .locator('//div[@role="tab"]/span[text()="Comm Summary"]')
        //   .click({ waitForLoadState: "networkidle" });
        await page.getByText("Comm Summary").click();
        await page.getByRole("button", { name: "Update Data" }).click();
        await expect(
          await page.getByText("Datasheet sync request has")
        ).toBeVisible();
        await expect(
          await page.getByText("Datasheet has been generated")
        ).toBeVisible({ timeout: 240000 });
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("<EMAIL>");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(
          await page.getByRole("gridcell", { name: "1,500" }).first()
        ).toBeVisible();
        await expect(
          await page.getByRole("gridcell", { name: "true" }).nth(1)
        ).toBeVisible();
        await page.getByRole("button", { name: "Clear filter" }).click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Equal To", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("<EMAIL>");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(
          await page.getByRole("gridcell", { name: "1,500" })
        ).toBeHidden();
        await expect(
          await page.getByRole("gridcell", { name: "true" }).nth(1)
        ).toBeHidden();
      });

      test("Adjustment logic should be updated for Payouts & payouts tracker widget in payee dashboard", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Add Adjustment" }).click();
        await page.getByRole("menuitem", { name: "Commission" }).click();
        await page.getByLabel("Payee*").click();
        await page.getByText("Test User2").last().click();
        await page.getByLabel("Effective Period*").click();
        await page.getByText("June").click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("2000");
        await page.waitForTimeout(1000);
        await page.getByRole("button", { name: "Submit" }).click();
        await expect(
          await page.getByText("Adjustment saved successfully")
        ).toBeVisible();
        await page.goto("/users", { waitUntil: "networkidle" });
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page.getByRole("button", { name: "Login as user" }).click();
        await page.click("#Statements");
        await page.waitForLoadState("networkidle");
        await expect(page.getByTestId("login-indicator")).toContainText(
          "June 2024Total Payout€2.000,00"
        );
        await page.getByRole("button", { name: "Exit" }).click();
        await page.waitForTimeout(5000);
      });

      test("When the past period is unlocked and re-calculating the payouts, it should take the latest fx rate for that period", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        const commonpage = new CommonUtils(page);
        await page.goto("/commissions", { waitUntil: "networkidle" });
        const value1 = await page
          .getByTestId("period-select")
          .locator("div")
          .first()
          .innerText();
        if (value1 !== "May 2024") {
          await page
            .getByTestId("period-select")
            .locator("div")
            .first()
            .click();
          await page.getByText("May 2024").click();
        }
        expect(
          await page
            .getByTestId("<EMAIL>")
            .innerText()
        ).toBe("CA$1,100.00");
        await page.goto("/settings/basic", { waitUntil: "networkidle" });
        await page
          .locator("div")
          .filter({ hasText: /^Select currency$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "CAD" }).click();
        await page.getByPlaceholder("Start month").click();
        await page.getByPlaceholder("Start month").fill("May-2024");
        await page.getByPlaceholder("Start month").press("Enter");
        await page.getByPlaceholder("End month").click();
        await page.getByPlaceholder("End month").fill("May-2024");
        await page.getByPlaceholder("End month").press("Enter");
        await page.getByRole("spinbutton").click();
        await page.getByRole("spinbutton").fill("10.5");
        await page.getByRole("button", { name: "Update" }).click();
        await expect(
          await page.getByText("Updated Successfully!!")
        ).toBeVisible();
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        await page
          .locator("span")
          .filter({ hasText: "Selected Payees" })
          .nth(1)
          .click();
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Select payees" })
          .click();
        await page.getByText("Fx Change User").click();
        await page
          .locator("span")
          .filter({ hasText: "Selected Payees" })
          .nth(1)
          .click();
        await commonpage.setSelectMonthComboBox("2024-05");
        await page.getByRole("button", { name: "Run", exact: true }).click();
        await page.getByRole("button", { name: "Skip & Run" }).click();
        await expect(
          await page.getByText("Calculating Commissions...")
        ).toBeVisible();
        await expect(
          await page.getByText("Commission Calculations Completed")
        ).toBeVisible({ timeout: 300000 });
        await page.goto("/commissions", { waitUntil: "networkidle" });
        await commonpage.setStorageCommValue("May-2024");
        expect(
          await page
            .getByTestId("<EMAIL>")
            .innerText()
        ).toBe("CA$11,550.00");
      });

      test("Modifying fx should not reflect in payouts when unlocked before running sync", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        await page.goto("/commissions", { waitUntil: "networkidle" });
        const value1 = await page
          .getByTestId("period-select")
          .locator("div")
          .first()
          .innerText();
        if (value1 !== "April 2024") {
          await page
            .getByTestId("period-select")
            .locator("div")
            .first()
            .click();
          await page.getByText("April 2024").click();
        }
        expect(
          await page
            .getByTestId("<EMAIL>")
            .innerText()
        ).toBe("£1,050.00");
        await page.getByRole("link", { name: "Modify FX1" }).click();
        await page.waitForTimeout(2000);
        await page.waitForLoadState("networkidle");
        try {
          await page.waitForTimeout(3000);
          await page.getByTestId("lock-unlock-button").click(); // lock
          await expect(
            await page.getByText("Statement locked successfully")
          ).toBeVisible();
        } catch {
          await page.getByTestId("lock-unlock-button").click(); // unlock
          await page.waitForTimeout(3000);
          await page.getByTestId("lock-unlock-button").click(); // lock
          await expect(
            await page.getByText("Statement locked successfully")
          ).toBeVisible();
        }

        await page.goto("/settings/basic", { waitUntil: "networkidle" });
        await page
          .locator("div")
          .filter({ hasText: /^Select currency$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "GBP" }).click();
        await page.getByPlaceholder("Start month").click();
        await page.getByPlaceholder("Start month").fill("Apr-2024");
        await page.getByPlaceholder("Start month").press("Enter");
        await page.getByPlaceholder("End month").click();
        await page.getByPlaceholder("End month").fill("Apr-2024");
        await page.getByPlaceholder("End month").press("Enter");
        await page.getByRole("spinbutton").fill("1.5");
        await page.getByRole("button", { name: "Update" }).click();
        await expect(
          await page.getByText("Updated Successfully!!")
        ).toBeVisible();
        await page.goto("/commissions", { waitUntil: "networkidle" });
        const value = await page
          .getByTestId("period-select")
          .locator("div")
          .first()
          .innerText();
        if (value !== "April 2024") {
          await page
            .getByTestId("period-select")
            .locator("div")
            .first()
            .click();
          await page.getByText("April 2024").click();
        }
        expect(
          await page
            .getByTestId("<EMAIL>")
            .innerText()
        ).toBe("£1,050.00");
      });

      test("Modifying fx should reflect in payouts only after running sync & then unlocking", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        await page.goto("/commissions", { waitUntil: "networkidle" });
        const value1 = await page
          .getByTestId("period-select")
          .locator("div")
          .first()
          .innerText();
        if (value1 !== "April 2024") {
          await page
            .getByTestId("period-select")
            .locator("div")
            .first()
            .click();
          await page.getByText("April 2024").click();
        }
        expect(
          await page
            .getByTestId("<EMAIL>")
            .innerText()
        ).toBe("AU$1,040.00");

        await page.goto("/settings/basic", { waitUntil: "networkidle" });
        await page
          .locator("div")
          .filter({ hasText: /^Select currency$/ })
          .nth(3)
          .click();
        await page.locator("div").filter({ hasText: /^AUD$/ }).nth(2).click();
        await page.getByPlaceholder("Start month").click();
        await page.getByPlaceholder("Start month").fill("Apr-2024");
        await page.getByPlaceholder("Start month").press("Enter");
        await page.getByPlaceholder("End month").click();
        await page.getByPlaceholder("End month").fill("Apr-2024");
        await page.getByPlaceholder("End month").press("Enter");
        await page.getByRole("spinbutton").click();
        await page.getByRole("spinbutton").fill("2.5");
        await page.getByRole("button", { name: "Update" }).click();
        await expect(
          await page.getByText("Updated Successfully!!")
        ).toBeVisible();
        await page.goto("/commissions", { waitUntil: "networkidle" });
        await page
          .getByTestId("<EMAIL>")
          .getByText("AU$1,040.00")
          .click();
        await page.goto("/settings/commissions-and-data-sync", {
          waitUntil: "networkidle",
        });
        await page
          .locator("span")
          .filter({ hasText: "Selected Payees" })
          .nth(1)
          .click();
        await page
          .getByTestId("ever-select")
          .locator("div")
          .filter({ hasText: "Select payees" })
          .click();
        await page.getByText("Modify FX2").click();
        await page.getByPlaceholder("Select month").click();
        await page.getByText("Apr", { exact: true }).click();
        await page.getByRole("button", { name: "Run", exact: true }).click();
        await page.getByRole("button", { name: "Skip & Run" }).click();
        await expect(
          await page.getByText("Calculating Commissions...")
        ).toBeVisible();
        await expect(
          await page.getByText("Commission Calculations Completed")
        ).toBeVisible({ timeout: 300000 });
        await page.goto("/commissions", { waitUntil: "networkidle" });
        await page.getByRole("link", { name: "Modify FX2" }).click();
        await expect(await page.getByText("AU$1,040.00").first()).toBeVisible();
        await page.getByRole("button").nth(1).click();
        // await expect(await page.getByText("AU$2,600.00").first()).toBeVisible();
      });

      test("Switching b/w payee/org currency, paid statuses should be same in statements", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        await page.goto(
          "/statements/eyJwYXllZUVtYWlsSWQiOiJjdXJyY2hlY2t1c2VyQGNvbW0tYWRqcy5jb20iLCJwc2QiOiIyMDI0LTA2LTAxIiwicGVkIjoiMjAyNC0wNi0zMCJ9",
          { waitUntil: "networkidle" }
        );
        await page.getByText("₹1,150.00").first().click();
        await page.getByRole("button").nth(3).click();
        await expect(
          await page.getByText("Register payment", { exact: true })
        ).toBeVisible();
        await expect(
          await page.getByText("You are registering full payment")
        ).toBeVisible();
        await page.getByRole("button", { name: "Register" }).click();
        await expect(await page.getByText("Payment registered")).toBeVisible();
        await expect(await page.getByText("Fully paid")).toBeVisible();
        // await page.getByRole("button").first().click();
        // await page.getByText("Global").click();
        // await expect(await page.getByText("$1,150.00").first()).toBeVisible();
      });
    });
  }
);
