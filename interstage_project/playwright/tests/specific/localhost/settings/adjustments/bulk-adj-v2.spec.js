import AdjustementsV2Page from "../../../../../test-objects/adjustments-v2-objects";
import CanvasCommission from "../../../../../test-objects/canvas-objects";

const fs = require("fs");
const path = require("path");
const readline = require("readline");

const {
  bulkAdjustmentv2Fixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.addInitScript(() => {
    localStorage.clear();
    sessionStorage.clear();
  });
});

test.describe(
  "Bulk Commission Adjustments V2 Automation",
  { tag: ["@commissionAdjustments", "@regression", "@primelogic-3"] },
  () => {
    test(
      "Bulk Adjustment Statement Period of all types",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T18909, INTER-T18910, INTER-T24570",
          },
          {
            type: "Description",
            description:
              "Validate whether the user can select all types of Statement Periods from the dropdown (Monthly, Quarterly, Half-Yearly, Annually, Custom cal)",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to select all types of statement period dropdown",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        const allPeriods = await adjustmentV2.getAllStatementPeriods();

        const expectedList = [
          "December 2024",
          "H2 (Jul 2024 - Dec 2024)",
          "2024",
          "November 2024",
          "August 2024",
          "July 2024",
          "June 2024",
          "Q2 (Apr 2024 - Jun 2024)",
          "H1 (Jan 2024 - Jun 2024)",
          "May 2024",
          "April 2024",
          "Q1 (Jan 2024 - Mar 2024)",
          "February 2024",
          "January 2024",
          "Jan 02, 2024 - Jan 08, 2024",
        ];

        await adjustmentV2.closeAdjustmentModal();
        await adjustmentV2.clickBulkImport("Import Adjustments");
        // Half Yearly
        await adjustmentV2.clickStatementPeriodDropdown();
        await adjustmentV2.clickStatementPeriod("H2 (Jul 2024 - Dec 2024)");

        // Yearly
        await adjustmentV2.clickStatementPeriodDropdown();
        await adjustmentV2.clickStatementPeriod("H2 (Jul 2024 - Dec 2024)");
        await adjustmentV2.clickStatementPeriodDropdown();
        await adjustmentV2.clickStatementPeriod("2024");

        // Custom Calendar
        await adjustmentV2.clickStatementPeriodDropdown();
        await adjustmentV2.clickStatementPeriod("2024");
        await adjustmentV2.clickStatementPeriodDropdown();
        await adjustmentV2.clickStatementPeriod("Jan 02, 2024 - Jan 08, 2024");

        // Quarterly
        await adjustmentV2.clickStatementPeriodDropdown();
        await adjustmentV2.clickStatementPeriod("Jan 02, 2024 - Jan 08, 2024");
        await adjustmentV2.clickStatementPeriodDropdown();
        await adjustmentV2.clickStatementPeriod("Q2 (Apr 2024 - Jun 2024)");

        // Monthly
        await adjustmentV2.clickStatementPeriodDropdown();
        await adjustmentV2.clickStatementPeriod("Q2 (Apr 2024 - Jun 2024)");
        await adjustmentV2.clickStatementPeriodDropdown();
        await adjustmentV2.clickStatementPeriod("June 2024");

        try {
          assert.deepStrictEqual(
            allPeriods,
            expectedList,
            "Lists are not identical"
          );
          console.log(
            "The Statement Period dropdown contains all types of periods for adjustments!"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "Close button Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18912" },
          {
            type: "Description",
            description:
              "Validate whether clicking the close button closes the import wizard",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "Clicking the close button should close the Bulk Import wizard",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");

        await adjustmentV2.closeBulkImportWizard();
      }
    );

    test(
      "Bulk Adjustment without selecting Statement Period",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18914, INTER-T24568" },
          {
            type: "Description",
            description:
              "Validate whether able to upload a CSV file without selecting the Statement Period",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "The option to upload a CSV file should be disabled, and the user should not be able to upload any file unless the Statement Period is selected",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");

        const uploadButton = await page
          .locator(".ant-upload.ant-upload-disabled")
          .first();

        // Checking of Browse button disable
        const cursorStyle = await uploadButton.evaluate(
          (el) => window.getComputedStyle(el).cursor
        );

        expect(cursorStyle).toBe("not-allowed");

        await adjustmentV2.UploadAsCSVhover();

        const toolTip = await adjustmentV2.getTooltipMessage();

        try {
          assert.strictEqual(
            toolTip,
            "Unable to upload data because the statement period is not selected. Please choose the 'Statement Period' above",
            "CSV file can be added without selecting the statement period"
          );
          console.log(
            "CSV file can't be added without selecting the statement period, Passed"
          );
        } catch (error) {
          console.error(error.message);
          throw error;
        }
      }
    );

    test(
      "Positive and Negative amounts in the Bulk Commission Adjustments",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18917" },
          {
            type: "Description",
            description:
              "Validate that both positive and negative amounts in the adjustments CSV are reflected correctly on the Commission Adjustments screen after bulk import",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "Both the positive as well as negative values should be reflected correctly on Commission Adjustments screen after Bulk import",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("February 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Positive and Negative.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();
        await adjustmentV2.validateSuccess();
        await adjustmentV2.clickImportButton();
        await adjustmentV2.clickImportButtonConfirm();
        await adjustmentV2.fillEmail("<EMAIL>");
        await adjustmentV2.clickSubmitBtn();

        await adjustmentV2.checkUploadstatus();

        await page.waitForTimeout(5000);
        await adjustmentV2.navigate("/settings/adjustments");

        await page.waitForTimeout(2000);

        const amounts = await adjustmentV2.getAllAdjustmentAmounts();

        const Checks = ["- £120,000.00", "€9,998.00"];
        const allPresent = Checks.every((item) => amounts.includes(item));

        try {
          assert.strictEqual(
            allPresent,
            true,
            "Bulk adjustments for positive and negative values aren't updated on the Adjustments page."
          );
          console.log(
            "Bulk adjustments for positive and negative values are updated on the Adjustments page, Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "'Payee Currency' column empty or 'Yes' Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T18918, INTER-T18913, INTER-T18920, INTER-T19068, INTER-T24569",
          },
          {
            type: "Description",
            description:
              "Validate that leaving the 'Payee Currency' column empty or setting it to 'Yes' allows the commission adjustment to be added in the payee's currency",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description: "The Bulk Import should take the empty values as Yes",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("January 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Empty Payee Currency.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await page.waitForTimeout(3000);

        const payeeCurrenciesCheck = await adjustmentV2.getPayeeCurrencies();

        const Checks = ["Yes", "Yes"];
        const allPresent = Checks.every((item) =>
          payeeCurrenciesCheck.includes(item)
        );

        await adjustmentV2.validateSuccess();

        await adjustmentV2.clickImportButton();
        await adjustmentV2.clickImportButtonConfirm();
        await adjustmentV2.fillEmail("<EMAIL>");
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.checkUploadstatus();

        await page.waitForTimeout(5000);
        await adjustmentV2.navigate("/settings/adjustments");

        const allCurrencies = await adjustmentV2.getallCurrencies();

        const isUsdAbsent = allCurrencies.every(
          (currency) => currency !== "USD"
        );

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 Jan 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Adjustments");

        const amount = await adjustmentV2.getAdjustedAmount();

        try {
          assert.strictEqual(
            allPresent,
            true,
            "When the Payee Currency column is kept empty, it doesn't take the default value as 'Yes'. in the Bulk Import Wizard"
          );
          assert.strictEqual(
            isUsdAbsent,
            true,
            "When the Payee Currency column is kept empty, it doesn't take the default value as 'Yes'. in the Adjustments listing view"
          );
          assert.strictEqual(
            amount,
            "£6,769.90",
            "The Bulk Adjusted Amount is not reflected in the Statements"
          );
          console.log(
            "When the Payee Currency column is kept empty, it is taking the default value as 'Yes'., Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "Downloading of .CSV template for Bulk Commission Adjustments Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18911, INTER-T18923" },
          {
            type: "Description",
            description:
              "Validate whether the user can download the .CSV template for Bulk Commission Adjustments upload",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "The user should be able to download the Bulk Import Template CSV file and it should be in the correct format",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");

        const downloadPromise = page.waitForEvent("download");
        await adjustmentV2.clickDownloadTemplate();
        const download = await downloadPromise;

        const downloadsPath = path.join(__dirname, "downloads");
        if (!fs.existsSync(downloadsPath)) {
          fs.mkdirSync(downloadsPath);
        }

        const filePath = path.join(downloadsPath, download.suggestedFilename());
        await download.saveAs(filePath);

        // Verify the file exists
        if (!fs.existsSync(filePath)) {
          throw new Error(`File not found at: ${filePath}`);
        }
        console.log(`File downloaded successfully: ${filePath}`);

        const fileContent = fs.readFileSync(filePath, "utf8");
        const rows = fileContent.split("\n");
        const headers = rows[0].split("\t");

        // Expected headers
        const expectedHeaders = [
          "Email",
          "Amount",
          "Payee Currency",
          "Reason Description",
          "Reason Category",
          "Plan Name",
          "Component Name",
          "Line Item Id",
        ];

        const allPresent = expectedHeaders.every((item) =>
          JSON.stringify(headers).includes(item)
        );

        try {
          assert.strictEqual(
            allPresent,
            true,
            "The Downlaoded template doesn't contains all the header columns"
          );
          console.log(
            "The Downlaoded template contains all the header columns, Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "'Payee Currency' column as 'No' Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18919, INTER-T19060" },
          {
            type: "Description",
            description:
              "Validate that setting the 'Payee Currency' column to 'No' adds the commission adjustment in the system's default currency",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "When the Payee Currency column is kept as No, it should take the default value as 'No' (Global Currency)",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("Q1 (Jan 2024 - Mar 2024)");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Payee Currency No.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await adjustmentV2.validateSuccess();
        await page.waitForTimeout(3000);

        const payeeCurrenciesCheck = await adjustmentV2.getPayeeCurrencies();

        await adjustmentV2.clickImportButton();
        await adjustmentV2.clickImportButtonConfirm();
        await adjustmentV2.fillEmail("<EMAIL>");
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.checkUploadstatus();

        await page.waitForTimeout(6000);
        await page.reload();
        await adjustmentV2.navigate("/settings/adjustments");

        const allCurrencies = await adjustmentV2.getallCurrencies();

        const Checks = ["USD"];
        const isUsdPresent = Checks.every((item) =>
          allCurrencies.includes(item)
        );

        try {
          assert.strictEqual(
            payeeCurrenciesCheck[0],
            "No",
            "When the Payee Currency column is kept as No, it doesn't take the default value as 'No'. in the Bulk Import Wizard"
          );
          assert.strictEqual(
            isUsdPresent,
            true,
            "When the Payee Currency column is kept as No, it doesn't take the default value as 'No'. in the Adjustments listing view"
          );
          console.log(
            "When the Payee Currency column is kept as No, it is taking the default value as 'No'., Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "Invalid Commission plan and Component Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18921, INTER-T18933" },
          {
            type: "Description",
            description:
              "Validate that providing an irrelevant commission plan and component (for a user with no commission plan) for the statement period results in a 'No payouts available' validation error",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "On giving Invalid Commission Plan and Component, Bulk Adjustments should throw Validation error",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();
        await adjustmentV2.clickStatementPeriod("January 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Invalid Commission Plan and Component.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await page.waitForTimeout(3000);

        await adjustmentV2.hoverValidationErrorMessage();
        const validationMessage = await adjustmentV2.getTooltipMessage();

        try {
          assert.strictEqual(
            validationMessage,
            "_: No matching commission plan found for this period. These are the available commission plans for that period: ['allCrt_Plan']. _: No matching component found for this period. These are the available component for that period: {'allCrt_Plan': ['CONDITIONAL', 'QUOTA', 'SIMPLE', 'TIER']}."
          );
          console.log(
            "Upon validating, Bulk Adjustments is throwing the 'No matching commission plan' error., Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "'Import and Validate' button in the Bulk Import wizard Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18922" },
          {
            type: "Description",
            description:
              "Validate whether the 'Import and Validate' button works when no columns from the header are mapped to the respective fields",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "If no columns are mapped, upon clicking Import and Validate button should throw a popup saying 'Select all required fields'",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("January 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Empty Payee Currency.csv"
        );

        await adjustmentV2.clickNextButton();
        await page.waitForTimeout(3000);

        const columnTitles = [
          "Email",
          "Amount",
          "Payee Currency",
          "Reason Description",
          "Reason Category",
          "Plan Name",
          "Component Name",
          "Line Item Id",
        ];

        for (const title of columnTitles) {
          // Hover over the title to make the clear button visible
          await page.locator(`span[title='${title}']`).hover();

          await page.waitForTimeout(4000);

          await adjustmentV2.crossComponentbutton();

          console.log(`Cleared column: ${title}`);
        }

        await adjustmentV2.clickImportandalidateButton();
        await adjustmentV2.requiredFieldsMessage();
      }
    );

    test(
      "'Add Commission Type' dropdown in the Bulk Import Wizard Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T19054" },
          {
            type: "Description",
            description:
              "Validate that the 'Add Commission Type' dropdown is disabled at the Upload CSV step in the Bulk Import Wizard",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "The Adjustment Type dropdown should be disabled by default",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        const btnDropdown = await page.locator("div.ant-select-disabled input");

        try {
          assert.strictEqual(
            await btnDropdown.isDisabled(),
            true,
            "The Dropdown 'Add Adjustment Type' is enabled"
          );
          console.log("The Dropdown 'Add Adjustment Type' is disabled, Passed");
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "Statement Period Reset in Bulk Import wizard Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T19053" },
          {
            type: "Description",
            description:
              "Validate that clicking the close button in the Bulk Import wizard resets the statement period to null when reopening the Bulk Import wizard",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "Upon reopening the Bulk Import Wizard after setting the Statement Period, the Statement Period selection should be reset to null once the wizard is closed",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("January 2024");
        await adjustmentV2.closeBulkImportWizard();

        await adjustmentV2.clickBulkImport("Import Adjustments");

        const statementPeriod = await adjustmentV2.getStatamentPeriod();

        try {
          assert.strictEqual(
            statementPeriod.trim(),
            "",
            "The Statement Period option is retaining the value"
          );
          console.log("The Statement Period option is empty, Passed");
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "Empty CSV Upload Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18936" },
          {
            type: "Description",
            description:
              "Validate that when an empty file is uploaded, clicking the 'Download the below data' button should trigger an alert stating 'No records to download'",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "Uploading an empty CSV should trigger an alert message, and the validations should fail",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("January 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - No Records.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await adjustmentV2.getValidateError();
        await page.waitForTimeout(3000);

        await page.on("dialog", async (dialog) => {
          console.log("Dialog message:", dialog.message());
          await dialog.accept();
          expect(dialog.message()).toBe("No records to download.");
        });

        await adjustmentV2.clickDownloadValidatedData();
      }
    );

    test(
      "Adjustments for a period where the email doesn't have a statement Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18915" },
          {
            type: "Description",
            description:
              "Validate whether the user can upload adjustments for a period where the email doesn't have a statement",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "The validation should fail and display a message stating that no payouts are available",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("H1 (Jan 2024 - Jun 2024)");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Not Valid Period.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await page.waitForTimeout(3000);

        await adjustmentV2.hoverValidationErrorMessage();
        const validationMessage = await adjustmentV2.getTooltipMessage();

        try {
          assert.strictEqual(
            validationMessage,
            "_: No commission plans found for employee '<EMAIL>'.. _: No payout available for payee: <EMAIL> for period H1 (Jan 2024 - Jun 2024)..",
            "On giving Invalid Statement Period where the email doesn't have statement, Bulk Adjustments aren't throwing Validation error"
          );
          console.log(
            "Upon Valiating, Bulk Adjustments is throwing the 'No payouts available error., Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "'All Results', 'Validated', and 'Errors' count Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T19056, INTER-T19063, INTER-T19064",
          },
          {
            type: "Description",
            description:
              "Validate that the correct count is displayed under 'All Results', 'Validated', and 'Errors' in the import step",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "The actual count of 'All results', 'Validated' and 'Errors' should be displayed properly",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("February 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - All Results.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await page.waitForTimeout(3000);

        // Error Validation Message color Vaidation
        await expect(
          page.locator("div[col-id='errors'] span.text-ever-error")
        ).toHaveCSS("color", "rgb(220, 38, 38)");

        // Success Validation Message color Validation
        await expect(
          page.locator("(//span[@class='text-ever-success p-0 m-0'])[2]")
        ).toHaveCSS("color", "rgb(34, 196, 94)");

        const containerLocator = page.locator("div.ant-row.items-center.gap-4");

        // Extract the counts
        const counts = await containerLocator.evaluate((container) => {
          // Helper function to get the count based on label text
          const getCountByLabel = (label) => {
            const span = Array.from(container.querySelectorAll("span")).find(
              (el) => el.previousSibling?.textContent?.includes(label)
            );
            return span ? parseInt(span.textContent.trim(), 10) : 0;
          };

          return {
            allResults: getCountByLabel("All Results:"),
            validated: getCountByLabel("Validated:"),
            errors: getCountByLabel("Errors:"),
          };
        });

        // Log the counts
        console.log("Counts:", counts);

        expect(counts.allResults).toBe(3);
        expect(counts.validated).toBe(2);
        expect(counts.errors).toBe(1);
      }
    );

    test(
      "Incorrect formatted email address Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T19051" },
          {
            type: "Description",
            description:
              "Validate that an incorrectly formatted email address, which does not follow the standard email syntax, triggers an error",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should see a validation error - Email doesn't exist when trying to add bulk adjustment for incorrect / wrong email address",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;

        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("November 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Invalid Email Format.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await page.waitForTimeout(3000);
        await adjustmentV2.hoverValidationErrorMessage();

        const validationMessage = await adjustmentV2.getTooltipMessage();

        try {
          assert.strictEqual(
            validationMessage,
            "Email: Email is not valid..",
            "On giving Invalid Email, Bulk Adjustments aren't throwing Validation error"
          );
          console.log(
            "Upon Valiating, Bulk Adjustments is throwing the 'Email: Email is not valid..' error., Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "Negative and positive amount color Validation in the Import step",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T19058" },
          {
            type: "Description",
            description:
              "Validate that if the amount entered through Bulk Import is negative, it is displayed in red, and if positive, it is displayed in green",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should see the negative amount in red color and positive amount in green color in the Bulk Import",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("February 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Positive and Negative.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await page.waitForTimeout(3000);

        // Negative amount color Vaidation
        await expect(
          page.locator("//div //span[starts-with(text(),'-120000.0')]")
        ).toHaveCSS("color", "rgb(220, 38, 38)");

        // Success Validation Message color Validation
        await expect(
          page.locator("//div //span[starts-with(text(),'9998.0')]")
        ).toHaveCSS("color", "rgb(34, 196, 94)");
      }
    );

    test(
      "Email address removal Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T19066" },
          {
            type: "Description",
            description:
              "Validate that if the email address is removed from any record after uploading, revalidating the record should trigger an error",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "Upon removing the email from the record and revalidating it should throw an error",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("Jan 02, 2024 - Jan 08, 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Not Valid Period.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await page.waitForTimeout(3000);

        await adjustmentV2.editBulkAdjustment("<EMAIL>", "");
        await adjustmentV2.clickRevalidatebutton();

        await adjustmentV2.validateSuccess();

        await page.waitForTimeout(3000);

        await adjustmentV2.hoverValidationErrorMessage();
        const validationMessage = await adjustmentV2.getTooltipMessage();

        try {
          assert.strictEqual(
            validationMessage,
            "Email: Email id is a mandatory field.",
            "On Re-Validating the empty Email field, Bulk Adjustments is't throwing Validation error"
          );
          console.log(
            "Upon Re-Valiating, Bulk Adjustments is throwing the 'Email: Email id is a mandatory field.' error., Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "Incorrect Component Name Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18934" },
          {
            type: "Description",
            description:
              "Validate that if an incorrect Component Name is provided for any adjustment, the validation error 'No matching component found in this Commission plan, these are the available components in this list.' is thrown",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "When incorrect or no Component is provided in Bulk Import, it should throw an error saying 'No matching component found for this period'",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;

        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("H2 (Jul 2024 - Dec 2024)");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Invalid Component.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await page.waitForTimeout(3000);

        await adjustmentV2.hoverValidationErrorMessage();
        const validationMessage = await adjustmentV2.getTooltipMessage();

        try {
          assert.strictEqual(
            validationMessage,
            "_: No matching component found in this Commission plan, these are the available components in this list.['Simple_Rule'].",
            "On giving incorrect Component Name, Bulk Adjustments isn't throwing Validation error"
          );
          console.log(
            "Upon Valiating, Bulk Adjustments is throwing the 'No matching component found in this Commission plan' error., Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "Emails where the users do not belong to the account Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T18930, INTER-T19061, INTER-T18926",
          },
          {
            type: "Description",
            description:
              "Validate that for emails where the users do not belong to the account, a validation error is thrown during bulk import stating, 'The Payee email is not found in the User's list.'",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("Jan 02, 2024 - Jan 08, 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Invalid Email.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await page.waitForTimeout(3000);

        await adjustmentV2.hoverValidationErrorMessage();
        const validationMessage = await adjustmentV2.getTooltipMessage();

        // Import Button Disabled check
        await expect(page.locator("//button //div[.='Import']")).toBeDisabled();

        await adjustmentV2.editBulkAdjustment(
          "<EMAIL>",
          "<EMAIL>"
        );

        await adjustmentV2.clickRevalidatebutton();
        await adjustmentV2.validateSuccess();

        // Import Button Enable check
        await expect(page.locator("//button //div[.='Import']")).toBeEnabled();

        await adjustmentV2.clickImportButton();
        await adjustmentV2.clickImportButtonConfirm();
        await adjustmentV2.fillEmail("<EMAIL>");
        await adjustmentV2.clickSubmitBtn();

        await adjustmentV2.checkUploadstatus();

        await page.waitForTimeout(5000);
        await adjustmentV2.navigate("/settings/adjustments");

        try {
          assert.strictEqual(
            validationMessage,
            "Email: Email does not exist..",
            "On giving incorrect Email Id which is not present in the account, Bulk Adjustments isn't throwing Validation error"
          );
          console.log(
            "Upon Valiating, Bulk Adjustments is throwing the 'Email: Email does not exist..' error., Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "Emails with no statement period Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18931, INTER-T24566" },
          {
            type: "Description",
            description:
              "Validate that for emails where the statement period is not available, a validation error is thrown during bulk import stating, 'There are no statements available for this period; the closest available period is 'x.'",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "Upon Valiating, Bulk Adjustments should throw the 'No payout available' error.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("June 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Valid Data.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await page.waitForTimeout(3000);

        await adjustmentV2.hoverValidationErrorMessage();
        const validationMessage = await adjustmentV2.getTooltipMessage();

        try {
          assert.strictEqual(
            validationMessage,
            "_: No commission plans found for employee '<EMAIL>'.. _: No payout available for payee: <EMAIL> for period June 2024..",
            "On giving incorrect Period where payee doesn't have statements, Bulk Adjustments isn't throwing Validation error"
          );
          console.log(
            "Upon Valiating, Bulk Adjustments is throwing the 'No payout available' error., Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "The fields Email, Amount, Payee Currency, Reason Description, Reason Category, Plan Name, Component Name, and Line Item ID Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18924" },
          {
            type: "Description",
            description:
              "Validate whether the fields Email, Amount, Payee Currency, Reason Description, Reason Category, Plan Name, Component Name, and Line Item ID on the validation screen are editable",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to edit Email, Amount, Payee Currency, Reason Description, Reason Category, Plan Name, Component Name, and Line Item ID in the Validation screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("Q1 (Jan 2024 - Mar 2024)");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Invalid Record.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await page.waitForTimeout(3000);

        // Edit Amount
        await adjustmentV2.editBulkAdjustment("5198.85", "6722.90");

        // Edit Payee Currency
        await adjustmentV2.editBulkAdjustmentpayeeCurrency("No", "Yes");

        // Edit Reason
        await adjustmentV2.editBulkAdjustment(
          "Reason Not found",
          "Payouts not matching"
        );

        // Reason category
        await adjustmentV2.editBulkAdjustmentReasonCategory(
          "CRM Issue",
          "Calculation Issue"
        );

        // Edit Plan
        await adjustmentV2.editBulkAdjustmentPlan(
          "xyz_PLAN",
          "Quota_Plan_2024"
        );

        // Edit component
        await adjustmentV2.editBulkAdjustmentPlan("SIMPLE", "Simple_Rule");

        // Edit Line Item ID
        await adjustmentV2.editBulkAdjustment("67", "32");

        await adjustmentV2.clickRevalidatebutton();
        await adjustmentV2.validateSuccess();

        // Import Button Enable check
        await expect(page.locator("//button //div[.='Import']")).toBeEnabled();

        await adjustmentV2.clickImportButton();
        await adjustmentV2.clickImportButtonConfirm();
        await adjustmentV2.fillEmail("<EMAIL>");
        await adjustmentV2.clickSubmitBtn();

        await adjustmentV2.checkUploadstatus();

        await page.waitForTimeout(5000);
        await adjustmentV2.navigate("/settings/adjustments");
      }
    );

    test(
      "Custom terminology Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T19052" },
          {
            type: "Description",
            description:
              "Validate that custom terminology is applied consistently throughout the Commission Adjustment bulk import steps",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "The custom terminology should be applied successfully in the 'Adjustment Type' dropdown as well as in the 'Show more details' section",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("settings/custom-terminology");
        await adjustmentV2.changeCustomTerminology("Adjustment", "Adj");
        await adjustmentV2.changeCustomTerminology("Adjustments", "Adjs");
        await adjustmentV2.clickUpdateButton();

        await page.waitForTimeout(3000);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjs");

        const addCommAdjValue = page.locator(
          "span[title='Add Commission Adj']"
        );

        // Check if the title attribute is 'Add Commission Adj'
        await expect(addCommAdjValue).toHaveAttribute(
          "title",
          "Add Commission Adj"
        );

        const addCommAdjType = await page
          .locator("//label[contains(.,'Adj Type')] //span")
          .innerText();

        // Check if the title is 'Adj Type'
        expect(addCommAdjType).toBe("Adj Type*");
        await adjustmentV2.clickShowmoreDetails();

        // Show more details
        expect(
          await page
            .locator(
              "div[class='ant-drawer ant-drawer-top ant-drawer-open'] li:nth-child(2)"
            )
            .innerText()
        ).toBe(
          "- For the adj amount, use positive values for additional payments and negative values for deductions."
        );

        expect(
          await page
            .locator(
              "div[class='ant-drawer ant-drawer-top ant-drawer-open'] li:nth-child(3)"
            )
            .innerText()
        ).toBe(
          "- If you choose 'Yes' or leave the 'Amount in Payee's Currency'’s column blank, the commission adj will be added in the payee's currency. If 'No' is chosen, the commission Adj will be added in the system's default currency."
        );

        await adjustmentV2.navigate("settings/custom-terminology");
        await adjustmentV2.changeCustomTerminology("Adjustment", "");
        await adjustmentV2.changeCustomTerminology("Adjustments", "");
        await adjustmentV2.clickUpdateButton();
      }
    );

    test(
      "Validate that if any relevant / irrelevant information is imported, clicking the 'Download the below data' provides a file containing all adjustments data including errors and all columns",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T18925" },
          {
            type: "Description",
            description:
              "Validate that if any relevant / irrelevant information is imported, clicking the 'Download the below data' provides a file containing all adjustments data including errors and all columns",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "Upon downloading the validation file, it should contain all the records, including the validated ones, and not just the error records",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("February 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - All Results.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();

        await page.waitForTimeout(3000);

        const downloadPromise = page.waitForEvent("download");
        await adjustmentV2.clickDownloadValidatedData();
        const download = await downloadPromise;

        const downloadsPath = path.join(__dirname, "downloads");
        if (!fs.existsSync(downloadsPath)) {
          fs.mkdirSync(downloadsPath);
        }

        const filePath = path.join(downloadsPath, download.suggestedFilename());
        await download.saveAs(filePath);

        // Verify the file exists
        if (!fs.existsSync(filePath)) {
          throw new Error(`File not found at: ${filePath}`);
        }
        console.log(`File downloaded successfully: ${filePath}`);

        // Verify the number of records in the CSV
        let rowCount = 0;
        const fileStream = fs.createReadStream(filePath);
        const rl = readline.createInterface({
          input: fileStream,
          crlfDelay: Infinity,
        });

        // eslint-disable-next-line no-unused-vars
        for await (const line of rl) {
          // Skip the header row
          if (rowCount === 0) {
            rowCount++;
            continue;
          }
          rowCount++;
        }

        // Subtract the header row
        rowCount -= 1;

        console.log(`Number of records in CSV: ${rowCount}`);

        // Validate the row count
        const expectedRowCount = 3;
        expect(rowCount).toBe(expectedRowCount);
      }
    );

    test(
      "'Total Records Detected' Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T19050, INTER-T18916" },
          {
            type: "Description",
            description:
              "Validate that the 'Total Records Detected' is correct and contains the accurate number of rows at the 'Map Columns' step",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "Bulk Commission Adjustment Import should detect the Total Records Detected count properly",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Annual.csv"
        );

        await adjustmentV2.clickNextButton();
        const totalCount = await adjustmentV2.getAllResultsCount();

        expect(totalCount[1]).toBe("1");

        await adjustmentV2.clickImportandalidateButton();
        await page.waitForTimeout(3000);

        await adjustmentV2.validateSuccess();
        await adjustmentV2.clickImportButton();
        await adjustmentV2.clickImportButtonConfirm();
        await adjustmentV2.fillEmail("<EMAIL>");
        await adjustmentV2.clickSubmitBtn();

        await adjustmentV2.checkUploadstatus();
        await page.waitForTimeout(5000);
        await adjustmentV2.navigate("/settings/adjustments");
      }
    );

    test(
      "The 'Preview Information' section Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T19057" },
          {
            type: "Description",
            description:
              "Validate that in the 'Preview Information' section, the first 3 records are displayed. If some columns have fewer than 3 entries, those available entries should be displayed in the Map CSV Fields step",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "If more than 3 entries are present in the BULK import CSV, only 3 entries should be displayed in the Preview Information' as well as in the tooltip",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - More than 3.csv"
        );

        await adjustmentV2.clickNextButton();

        // Plan Name
        await adjustmentV2.hoverPlanField();
        const plans = await adjustmentV2.getTooltipPlanField();
        expect(plans).toBe(
          "<EMAIL>, <EMAIL>, <EMAIL>"
        );
      }
    );

    test(
      "Auto-Approval and Override checkbox Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T19069, INTER-T18927, INTER-T18928, INTER-T19070",
          },
          {
            type: "Description",
            description:
              "Validate that if the approval feature is enabled, a different modal should be displayed showing the validated count and auto-approved count. If no records can be auto-approved, the Override checkbox should be disabled",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "If none of the values are within the threshold, then the Override all records for approval' checkbox should be disabled",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.ToggleApprovalsAdj();
        await adjustmentV2.setApprovalThreshold("1000", "2999");
        await adjustmentV2.thresholdSetValidation();

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("December 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Not Auto-Approved.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();
        await adjustmentV2.clickImportButton();

        // Since the amount is not under auto-approval the Override checkbox should be disabled
        await expect(
          page.locator("span.ant-checkbox-disabled input")
        ).toBeDisabled();

        // Import button should be disabled since no workflow is selected
        await expect(
          page.locator("//button[contains(.,'Yes, Import')]")
        ).toBeDisabled();

        const countAppr = await page
          .locator("div.bg-ever-warning-lite")
          .innerText();

        await adjustmentV2.selectApprovalWorkflowBulk("Adjustment Workflow");
        await adjustmentV2.clickImportButtonConfirm();
        await adjustmentV2.fillEmail("<EMAIL>");

        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.checkUploadstatus();

        await page.waitForTimeout(5000);
        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.ToggleApprovalsAdj();
        await adjustmentV2.clickConfirmToggleOff();

        try {
          assert.strictEqual(
            countAppr,
            "Of the 1 validated records,0 were automatically approved based on the threshold. Please select an approval workflow for the remaining 1 records",
            "The Validated count in the Approval workflow and the Override checkbox is not working properly"
          );
          console.log(
            "The Validated count in the Approval workflow and the Override checkbox is working properly, Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "Override checkbox is not enabled Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T19073, INTER-T19072, INTER-T18935",
          },
          {
            type: "Description",
            description:
              "Validate that if the Override checkbox is not enabled, all auto-approved records should be automatically approved, and for others, a workflow request and notification should be sent",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "If the 'Override all records for approval' checkbox is not selected, records within the threshold should be auto-approved, while others should be sent for workflow approval.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.ToggleApprovalsAdj();
        await adjustmentV2.setApprovalThreshold("1000", "2999");
        await adjustmentV2.thresholdSetValidation();

        await page.waitForTimeout(3000);

        await adjustmentV2.addCurrencyThreshold("INR", "EUR");
        await adjustmentV2.clickUpdateButton();
        await adjustmentV2.thresholdSetValidation();
        await adjustmentV2.addCurrencyThreshold("EUR", "GBP");
        await adjustmentV2.clickUpdateButton();
        await adjustmentV2.thresholdSetValidation();

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("February 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Auto-Approved.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();
        await adjustmentV2.clickImportButton();
        await page.waitForTimeout(2000);

        await adjustmentV2.selectApprovalWorkflowBulk("Adjustment Workflow");
        await adjustmentV2.clickImportButtonConfirm();
        await adjustmentV2.fillEmail("<EMAIL>");
        await adjustmentV2.clickSubmitBtn();
        await adjustmentV2.checkUploadstatus();

        await page.waitForTimeout(6000);
        await page.reload();
        await adjustmentV2.navigate(
          "/settings/adjustments?status=all&period=2024-02-29"
        );

        await adjustmentV2.changeApprovalsFilter();
        const empNamePending = await adjustmentV2.getAllEmployees();
        expect(empNamePending).toBe("Sample Payee E2E");

        await adjustmentV2.navigate("/users");
        await adjustmentV2.impersonatePayee("<EMAIL>");
        await adjustmentV2.navigate(
          "/approvals/commission-adjustments?status=all"
        );
        await adjustmentV2.approveCommissionAdj("Reason not defined");
        await adjustmentV2.exitImpersonation();

        await adjustmentV2.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "29 Feb 2024");
        await adjustmentV2.selectPayee("<EMAIL>");
        await adjustmentV2.clickComponent("Adjustments");

        const adjAmount = await page
          .locator("div[row-id='adjustments'] div[col-id='amount']")
          .innerText();

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.ToggleApprovalsAdj();
        await adjustmentV2.clickConfirmToggleOff();

        try {
          assert.strictEqual(
            adjAmount,
            "€17.787,90",
            "The Adjustment Amount is not getting reflected in the Payouts after its approved, Failed"
          );
          console.log(
            "The Adjustment Amount is getting reflected in the Payouts after its approved, Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "Validate that if the Override checkbox is selected, a workflow request should be sent for all records, regardless of whether they are automatically approved or not",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T19071" },
          {
            type: "Description",
            description:
              "Validate that if the Override checkbox is selected, a workflow request should be sent for all records, regardless of whether they are automatically approved or not",
          },
          { type: "Precondition", description: "Commission Adjustments" },
          {
            type: "Expected Behaviour",
            description:
              "If the 'Override all records for approval' checkbox is selected, regardless of auto-approval it should send the workflow request",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;

        const adjustmentV2 = new AdjustementsV2Page(page);
        const canvasCommission = new CanvasCommission(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.ToggleApprovalsAdj();
        await adjustmentV2.setApprovalThreshold("1000", "2999");
        await adjustmentV2.thresholdSetValidation();

        await page.waitForTimeout(3000);

        await adjustmentV2.addCurrencyThreshold("INR", "EUR");
        await adjustmentV2.clickUpdateButton();
        await adjustmentV2.thresholdSetValidation();
        await adjustmentV2.addCurrencyThreshold("EUR", "GBP");
        await adjustmentV2.clickUpdateButton();
        await adjustmentV2.thresholdSetValidation();

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        await adjustmentV2.clickStatementPeriod("February 2024");
        await adjustmentV2.clickBrowse();

        await canvasCommission.setInputFiles(
          "./upload-files/bulk-commission-adjustments/Bulk - Adjustments - Auto-Approved.csv"
        );

        await adjustmentV2.clickNextButton();
        await adjustmentV2.clickImportandalidateButton();
        await adjustmentV2.clickImportButton();

        await page.waitForTimeout(2000);

        await adjustmentV2.selectApprovalWorkflowBulk("Adjustment Workflow");

        await adjustmentV2.selectOverrideCheckbox();

        await adjustmentV2.clickImportButtonConfirm();
        await adjustmentV2.fillEmail("<EMAIL>");
        await adjustmentV2.clickSubmitBtn();

        await adjustmentV2.checkUploadstatus();

        await page.waitForTimeout(3000);

        await adjustmentV2.changeApprovalsFilter();
        await page.waitForTimeout(3000);
        const empNamePending = await adjustmentV2.getAllEmployeesAdj();

        expect(empNamePending[0]).toBe("Payee Random");
        expect(empNamePending[1]).toBe("Sample Payee E2E");
      }
    );

    test(
      "'Upload as CSV for period' Statement Period Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T24575" },
          {
            type: "Description",
            description:
              "Validate that upon selecting the Statement Period, it is displayed along with the text 'Upload as CSV for period' in the Bulk Adjustment Wizard",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Statement Period should be visible along with the 'Upload as CSV for Period' text",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);
        const assert = require("assert");

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickBulkImport("Import Adjustments");

        await adjustmentV2.clickStatementPeriodDropdown();
        await adjustmentV2.clickStatementPeriod("Jan 02, 2024 - Jan 08, 2024");

        const statementPeriod = await adjustmentV2.getPeriodValue();

        try {
          assert.strictEqual(
            statementPeriod,
            "Jan 02, 2024 - Jan 08, 2024",
            "The Statement Period is not appearing in the Bulk Import Wizard along with the 'Upload as CSV' text., Failed"
          );
          console.log(
            "The Statement Period is showing up in the Bulk Import Wizard along with the 'Upload as CSV' text., Passed"
          );
        } catch (error) {
          console.error("Validation failed:", error.message);
          throw error;
        }
      }
    );

    test(
      "Statement Period Redirection Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T24576" },
          {
            type: "Description",
            description:
              "Validate that when clicking the Statement Period in the Adjustments listing table, it redirects to the Statements section of the clicked payee",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments",
          },
          {
            type: "Expected Behaviour",
            description:
              "Upon clicking the Statement Period from the listing screen, it should redirect to the Statement screen of the particular payee",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");
        await adjustmentV2.clickStatementPeriodLink("2024");
        await expect(
          await page.getByLabel("Payout Summary").getByText("₹61,996.87")
        ).toBeVisible();
      }
    );

    test(
      "Statement Period Filter descending order Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T24577" },
          {
            type: "Description",
            description:
              "Validate that the Statement Period in the Filters as well as in the Bulk Adjustments shows the periods in descending order in the dropdown values",
          },
          {
            type: "Precondition",
            description: "Commission Adjustments",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Statement Period Filter dropdown values should be in the descending order based on the Period",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const adjustmentV2 = new AdjustementsV2Page(page);

        await adjustmentV2.navigate("/settings/adjustments");

        await adjustmentV2.clickFilterCTA();
        await adjustmentV2.selectFilterBulk("statementPeriod");
        const statementPeriodValuesFilter =
          await adjustmentV2.getDropdownValues();
        console.log(statementPeriodValuesFilter);
        const expectedOrderFilter = [
          "December 2024",
          "H2 (Jul 2024 - Dec 2024)",
          "2024",
          "November 2024",
          "August 2024",
          "July 2024",
          "June 2024",
          "Q2 (Apr 2024 - Jun 2024)",
        ];

        function areListsEqualOrdered(listA, listB) {
          return JSON.stringify(listA) === JSON.stringify(listB);
        }

        const isEqualFilter = areListsEqualOrdered(
          statementPeriodValuesFilter,
          expectedOrderFilter
        );
        console.log(
          "Are the Statement Period Dropdown values descending in Commission Adjustment Filter?",
          isEqualFilter
        );
        expect(isEqualFilter).toBeTruthy();
        await adjustmentV2.closeFilterview();

        await adjustmentV2.clickBulkImport("Import Adjustments");
        await adjustmentV2.clickStatementPeriodDropdown();

        const statementPeriodValuesBulkImport =
          await adjustmentV2.getDropdownValues();

        const expectedOrderBulk = [
          "December 2024",
          "H2 (Jul 2024 - Dec 2024)",
          "2024",
          "November 2024",
          "August 2024",
          "July 2024",
          "June 2024",
          "Q2 (Apr 2024 - Jun 2024)",
          "H1 (Jan 2024 - Jun 2024)",
          "May 2024",
          "April 2024",
          "Q1 (Jan 2024 - Mar 2024)",
          "February 2024",
          "January 2024",
          "Jan 02, 2024 - Jan 08, 2024",
        ];

        const isEqualBulk = areListsEqualOrdered(
          statementPeriodValuesBulkImport,
          expectedOrderBulk
        );
        console.log(
          "Are the Statement Period Dropdown values descending in Commission Adjustment Bulk Import Wizard?",
          isEqualBulk
        );
        expect(isEqualBulk).toBeTruthy();
      }
    );
  }
);
