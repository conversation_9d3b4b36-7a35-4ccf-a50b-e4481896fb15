import CommissionsSyncPrevPeriod from "../../../../../test-objects/commissionSyncPrevPeriod-objects";
import CommissionsSync from "../../../../../test-objects/commissionSync-objects";
import CommissionPlan from "../../../../../test-objects/plan-objects";

const {
  commissionSyncFixtures: { test, expect },
} = require("../../../../fixtures");

test.describe(
  "Commission Sync",
  { tag: ["@commissionsync", "@regression", "@primelogic-1"] },
  () => {
    const clickEnabledRunButton = async (adminPage) => {
      const page = adminPage.page;
      const buttons = await page.$$("button:has-text('Run')");
      for (const button of buttons) {
        const isEnabled = await button.isEnabled();
        if (isEnabled) {
          await button.click();
          break;
        }
      }
    };
    const expandMenu = async (page, buttonName) => {
      // Ensure all buttons are closed
      await page.waitForTimeout(2000);
      await page.waitForFunction(() => {
        const elements = document.querySelectorAll("div.ant-collapse-header");
        return elements.length === 7;
      });
      const buttons = await page.locator("div.ant-collapse-header").all();
      console.log(buttons);

      for (const button of buttons) {
        const ariaExpanded = await button.getAttribute("aria-expanded");
        if (ariaExpanded === "true") {
          // Close the button if it is expanded
          await button.click();
        }
      }

      // Click on buttons that are still expanded
      for (const button of buttons) {
        const ariaExpanded = await button.getAttribute("aria-expanded");
        if (ariaExpanded === "true") {
          await button.click();
        }
      }
      await page.waitForTimeout(2000);

      switch (buttonName) {
        case "Calculate Commissions Run":
          await page
            .getByRole("button", { name: "Calculate Commissions Run" })
            .click();
          break;
        case "Calculate Settlements Run":
          await page
            .getByRole("button", { name: "Calculate Settlements Run" })
            .click();
          break;
        case "Refresh Databooks Uploaded":
          await page
            .getByRole("button", { name: "Refresh Databooks Uploaded" })
            .click();
          break;
        case "Sync Data from Connectors":
          await page
            .getByRole("button", { name: "Sync Data from Connectors" })
            .click();
          break;
        case "Report ETL Run ETL for report":
          await page
            .getByRole("button", { name: "Report ETL Run ETL for report" })
            .click();
          break;
        case "Migrate data to inter objects":
          await page
            .getByRole("button", { name: "Migrate data to inter objects" })
            .click();
          break;
        default:
          break;
      }
    };

    test("Saving the commission plans", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 300000);
      await page.goto("/plans", {
        waitUntil: "networkidle",
      });
      await page
        .getByPlaceholder("Search by plan name")
        .click({ timeout: 40000 });
      await page.getByPlaceholder("Search by plan name").fill("Frequency_plan");
      await page.waitForTimeout(2000);
      await page.getByText("frequency_plan").click();
      await page.getByText("simple").first().click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.waitForTimeout(3000);
      await page.getByRole("button", { name: "Exit Canvas" }).click();
      await page.getByLabel("close-circle").click();
      await page.getByPlaceholder("Search by plan name").click();
      await page.getByPlaceholder("Search by plan name").fill("Plan");
      await page.waitForTimeout(1000);
      await page.getByText("Plan").nth(4).click();
      await page.getByText("simple").first().click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.waitForTimeout(3000);
    });

    test("Validate Commission sync widget", async ({
      adminPage,
      payeePage,
    }, testInfo) => {
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 300000);
      await page.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await expect(page.getByText("Here's what you can do")).toBeVisible();
      const page1 = payeePage.page;
      await page1.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await expect(
        page1.getByText("Sorry, you’re not authorized to access this page.")
      ).toBeVisible();
    });

    test("migarte data to interobjects validation", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await expandMenu(page, "Migrate data to inter objects");
      await page.getByPlaceholder("Select month").click();
      await page.getByPlaceholder("Select month").fill("01/06/2024");
      await page.getByPlaceholder("Select month").press("Enter");
      await page.getByRole("button", { name: "Run", exact: true }).click();
      const skipAndRunButton = page.getByRole("button", { name: "Skip & Run" });
      await page.waitForTimeout(1000);
      if (await skipAndRunButton.isVisible()) {
        await skipAndRunButton.click();
      }
      await expect(
        page.getByText("Migration to Inter Objects Completed")
      ).toBeVisible({ timeout: 300000 });
      await page.getByText("show past activities").click();
      await expect(
        page.getByText("Migrate Inter Object Data").first()
      ).toBeVisible();
    });

    test("commission sync using custom calender validation", async ({
      adminPage,
    }, testInfo) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);
      const cs = new CommissionsSync(page);

      testInfo.setTimeout(testInfo.timeout + 300000);
      await csPrev.navigate("/settings/commissions-and-data-sync");
      await expandMenu(page, "Calculate Commissions Run");
      await cs.checkAllPayeesOption();
      await csPrev.selectDate("01 Jun 2024");
      await csPrev.runCommissions();
      await expect(page.getByText("Notify when complete")).toHaveCount(1);
      await csPrev.clickSkipandRun();
      await csPrev.waitForCalculationMessage();
      await csPrev.waitForCommissionsSuccess();
    });

    test("payees in commission plan validation", async ({
      adminPage,
    }, testInfo) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);
      const cs = new CommissionsSync(page);

      testInfo.setTimeout(testInfo.timeout + 300000);
      await csPrev.navigate("/settings/commissions-and-data-sync");
      await expandMenu(page, "Calculate Commissions Run");
      await cs.checkPayeesInCommissionPlanOption();
      await cs.selectPlansCommissionSync(["frequency_plan", "plan"]);
      await csPrev.selectDate("01 Jun 2024");
      await csPrev.runCommissions();
      await expect(page.getByText("Notify when complete")).toHaveCount(1);
      await csPrev.clickSkipandRun();
      await csPrev.waitForCalculationMessage();
      await csPrev.waitForCommissionsSuccess();

      const key = "commission-view-period";
      const value = "30-June-2024";
      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await csPrev.navigate("/commissions");
      await expect(page.getByText("Admin test")).toBeVisible();
    });

    test("add payee to plan and calculate commission", async ({
      adminPage,
    }, testInfo) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);
      const cs = new CommissionsSync(page);
      const plans = new CommissionPlan(page);

      testInfo.setTimeout(testInfo.timeout + 300000);
      await csPrev.navigate("/plans");
      await plans.searchPlan("plan");
      await plans.selectPlan("plan");
      await cs.selectAllPayeesinCommissionPlan();
      await csPrev.navigate("/settings/commissions-and-data-sync");
      await expandMenu(page, "Calculate Commissions Run");
      await cs.checkPayeesInCommissionPlanOption();
      await cs.selectPlansCommissionSync(["plan"]);
      await csPrev.selectDate("01 Jun 2024");
      await csPrev.runCommissions();
      await expect(page.getByText("Notify when complete")).toHaveCount(1);
      await csPrev.clickSkipandRun();
      await csPrev.waitForCalculationMessage();
      await csPrev.waitForCommissionsSuccess();

      const key = "commission-view-period";
      const value = "30-June-2024";
      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await csPrev.navigate("/commissions");
      await expect(page.getByText("Admin test")).toBeVisible();
      await cs.searchUser("super user");
      await expect(page.getByText("super user")).toBeVisible({
        timeout: 120000,
      });
      await expect(page.getByText("₹1,000.00")).toBeVisible({
        timeout: 120000,
      });
    });

    test("Databook refresh validation", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 300000);
      await page.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await expandMenu(page, "Refresh Databooks Uploaded");
      await page.getByLabel("All Databooks").check();
      await clickEnabledRunButton(adminPage);
      const skipAndRunButton = page.getByRole("button", { name: "Skip & Run" });
      await page.waitForTimeout(1000);
      if (await skipAndRunButton.isVisible()) {
        await skipAndRunButton.click();
      }
      await expect(page.getByText("Databook Refresh Completed")).toBeVisible({
        timeout: 300000,
      });
      await expandMenu(page, "Refresh Databooks Uploaded");
      await page.getByLabel("Selected Databooks").check();
      await page.getByTestId("ever-select").locator("div").nth(1).click();
      await page.locator(".ant-select-item-option-content").first().click();
      await page.locator(".ant-select-item-option-content").nth(1).click();
      await page.getByTestId("ever-select").locator("div").nth(1).click();
      await page.getByLabel("Selected Databooks").check({ timeout: 1000 });
      await clickEnabledRunButton(adminPage);
      await expect(page.getByText("Databook Refresh Completed")).toBeVisible({
        timeout: 120000,
      });
      await expandMenu(page, "Refresh Databooks Uploaded");
      await page.getByLabel("Based on Object").check();
      await page.getByTestId("ever-select").locator("div").first().click();
      await page
        .locator("div")
        .filter({ hasText: /^New Object 1$/ })
        .nth(1)
        .click();
      await page.getByLabel("Based on Object").check();
      await clickEnabledRunButton(adminPage);
      await expect(page.getByText("Databook Refresh Completed")).toBeVisible({
        timeout: 120000,
      });
    });

    test("Generate data from data card", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 300000);
      await page.goto("/databook", {
        waitUntil: "networkidle",
      });
      await page.getByRole("link", { name: "Sheet", exact: true }).click();
      await page.getByRole("button", { name: "Generate Data" }).click();
      await expect(page.getByText("Generating DataBook Data...")).toBeVisible();
      await page.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await expect(
        page.getByText("Databook Refresh Completed").first()
      ).toBeVisible();
    });

    test("datasheet validation for report object", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await expandMenu(page, "Refresh Databooks Uploaded");
      await page.getByLabel("All Databooks").check();
      await page.waitForTimeout(3000);
      await clickEnabledRunButton(adminPage);
      const skipAndRunButton = page.getByRole("button", { name: "Skip & Run" });
      await page.waitForTimeout(1000);
      if (await skipAndRunButton.isVisible()) {
        await skipAndRunButton.click();
      }
      await expect(page.getByText("Databook Refresh Completed")).toBeVisible({
        timeout: 300000,
      });
      await page.goto("/databook", {
        waitUntil: "networkidle",
      });
      await page.getByRole("link", { name: "Sheet reports" }).click();
      await page.waitForTimeout(2000);
      await expect(
        page.getByRole("columnheader", { name: "Effective Start Date" })
      ).toBeVisible();
      await expect(
        page.getByRole("columnheader", { name: "Effective End Date" })
      ).toBeVisible();
      await expect(
        page.getByRole("columnheader", { name: "Employee Id" })
      ).toBeVisible();
      await expect(
        page.getByRole("columnheader", { name: "Joining Date" })
      ).toBeVisible();
      await expect(
        page.getByRole("columnheader", { name: "Current Time Zone" })
      ).toBeVisible();
      await expect(
        page.getByRole("columnheader", { name: "Designation" })
      ).toBeVisible();
      await expect(
        page.getByRole("columnheader", { name: "Employment Country" })
      ).toBeVisible();
      await expect(
        page.getByRole("columnheader", { name: "Payout Frequency" })
      ).toBeVisible();
    });

    test("connection name validation", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 300000);
      await page.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await expandMenu(page, "Sync Data from Connectors");
      await page.getByLabel("Selected Connectors").check();
      await page.getByTestId("ever-select").locator("div").nth(1).click();
      await expect(page.getByText("Companies (hubspot)")).toBeVisible();
    });

    test("recent activity tile validation", async ({ adminPage }) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);
      const cs = new CommissionsSync(page);

      await csPrev.navigate("/settings/commissions-and-data-sync");
      await cs.clickShowPastActivities();
      await expect(
        page.getByText("Past Activities", { exact: true })
      ).toBeVisible();
      await expect(page.getByText("Status")).toBeVisible();
      await expect(page.getByText("Activity", { exact: true })).toBeVisible();
      await expect(page.getByText("Period", { exact: true })).toBeVisible();
      await expect(page.getByText("End time", { exact: true })).toBeVisible();
      await expect(page.getByText("Start time", { exact: true })).toBeVisible();
      await cs.closeShowPastActivities();
      await expect(
        page.getByText("Past Activites", { exact: true })
      ).toHaveCount(0);
    });

    test("completed status card validation", async ({
      adminPage,
    }, testInfo) => {
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 300000);
      await page.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await expect(
        page.getByText("Initiated <EMAIL>")
      ).toBeVisible();
      await expect(page.getByText("Start Time")).toBeVisible();
      await expect(page.getByText("End Time")).toBeVisible();
      await expect(page.getByText("Show Details")).toBeVisible();
    });

    test("settlement sync validation", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);
      const cs = new CommissionsSync(page);

      testInfo.setTimeout(testInfo.timeout + 300000);
      await csPrev.navigate("/settings/commissions-and-data-sync");
      await expandMenu(page, "Calculate Settlements Run");
      await cs.checkAllPayeesOption();
      await csPrev.selectDate("01 Jun 2024");
      await csPrev.runCommissions();
      await expect(page.getByText("Notify when complete")).toHaveCount(1);
      await csPrev.clickSkipandRun();
      await csPrev.waitForSettlementCalculationMessage();
      await csPrev.waitForSettlementSuccess();

      await expandMenu(page, "Calculate Settlements Run");
      await cs.checkSelectedPayeesOption();
      await cs.selectPayeeSelectedPayeesOption("Admin test");
      await csPrev.selectDate("01 Jun 2024");
      await csPrev.runCommissions();
      await expect(page.getByText("Notify when complete")).toHaveCount(1);
      await csPrev.clickSkipandRun();
      await csPrev.waitForSettlementCalculationMessage();
      await csPrev.waitForSettlementSuccess();

      await expandMenu(page, "Calculate Settlements Run");
      await cs.checkPayeesInCommissionPlanOption();
      await cs.selectPlansCommissionSync(["frequency_plan"]);
      await csPrev.selectDate("01 Jun 2024");
      await csPrev.runCommissions();
      await expect(page.getByText("Notify when complete")).toHaveCount(1);
      await csPrev.clickSkipandRun();
      await csPrev.waitForSettlementCalculationMessage();
      await csPrev.waitForSettlementSuccess();
    });

    test("sync commission in presence of another card validation", async ({
      adminPage,
    }, testInfo) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);
      const cs = new CommissionsSync(page);

      testInfo.setTimeout(testInfo.timeout + 300000);
      await csPrev.navigate("/settings/commissions-and-data-sync");

      await expect(page.getByText("Completed")).toBeVisible({
        timeout: 300000,
      });
      // await page.locator("svg").first().click();
      await expandMenu(page, "Calculate Commissions Run");
      await cs.checkAllPayeesOption();
      await csPrev.selectDate("01 Jun 2024");
      await csPrev.runCommissions();
      await expect(page.getByText("Notify when complete")).toHaveCount(1);
      await csPrev.clickSkipandRun();
      await csPrev.waitForCalculationMessage();
      await csPrev.navigate("/quotas");
      await csPrev.navigate("/settings/commissions-and-data-sync");
      await csPrev.waitForCommissionsSuccess();
    });

    test("Connector Sync validation", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);
      const cs = new CommissionsSync(page);

      testInfo.setTimeout(testInfo.timeout + 300000);
      await csPrev.navigate("/settings/commissions-and-data-sync");
      await expandMenu(page, "Sync Data from Connectors");
      await cs.checkAllConnectorsOption();
      await clickEnabledRunButton(adminPage);
      await csPrev.clickSkipandRun();
      await csPrev.waitForE2ESuccess();
    });

    test("Connector Sync validation with selected connector", async ({
      adminPage,
    }, testInfo) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);
      const cs = new CommissionsSync(page);

      testInfo.setTimeout(testInfo.timeout + 300000);
      await csPrev.navigate("/settings/commissions-and-data-sync");
      await expandMenu(page, "Sync Data from Connectors");
      await cs.checkSelectedConnectorsOption("Companies (hubspot)");
      await clickEnabledRunButton(adminPage);
      await csPrev.clickSkipandRun();
      await csPrev.waitForE2ESuccess();
    });

    test("Connector Sync validation with upstream sync", async ({
      adminPage,
    }, testInfo) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);
      const cs = new CommissionsSync(page);

      testInfo.setTimeout(testInfo.timeout + 500000);
      await csPrev.navigate("/settings/commissions-and-data-sync");
      await expandMenu(page, "Sync Data from Connectors");
      await cs.checkAllConnectorsOption();
      await cs.checkUpstreamSyncOption();
      await clickEnabledRunButton(adminPage);
      await csPrev.clickSkipandRun();
      await csPrev.waitForUpstreamSuccess();
    });
  }
);
