import CommissionSync from "../../../../../test-objects/commissionSync-objects";
import CommissionsSyncPrevPeriod from "../../../../../test-objects/commissionSyncPrevPeriod-objects";
import DatasheetV2Page from "../../../../../test-objects/datasheet-v2-objects";
import DatasheetV2EditViewPage from "../../../../../test-objects/datasheet-v2-editView-objects";

const {
  commissionSyncETLFixtures: { test, expect },
} = require("../../../../fixtures");

async function staleDatasheet(dsV2Page, dsV2EditPage, dbName, dsName) {
  console.log("Stale data:", dbName, dsName);
  await dsV2Page.goToDatasheet(dbName, dsName);
  await dsV2Page.clickEditBtn();
  await dsV2EditPage.toggleCheckbox("stale");
  await dsV2EditPage.saveEdits();
  await dsV2Page.waitForRefresh();
}

test.beforeEach(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 760000);

  const page = adminPage.page;
  await page.goto("/settings/commissions-and-data-sync", {
    waitUntil: "networkidle",
  });
});

test.describe(
  "Commission Sync ETL - Verify only associated datasheets corresponding to commission criteria needs to be generated when refresh is enabled in sync.",
  { tag: ["@commissionsync", "@regression", "@primelogic-2"] },
  () => {
    test(
      "Commission Calculation Sync",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8985" },
          {
            type: "Description",
            description: "INTER-9955",
          },
          {
            type: "Precondition",
            description:
              "Create datasheets with stale data and have plans, forecasts published. ",
          },
          {
            type: "Expected Behaviour",
            description:
              "Verify only assosiated datasheets are generated on chosing selected payees, payees in commission plans, all payees",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(page);
        const commSyncPage = new CommissionSync(page);
        const csPrevPage = new CommissionsSyncPrevPeriod(page);

        await commSyncPage.action("Calculate Commissions Run");
        console.log(
          "Verify only assosiated datasheets are refreshed on selecting selected payees"
        );
        await commSyncPage.selectCriteria("selected-payees");
        await commSyncPage.selectDropdown(["Alice J", "Bob lee"]);
        await csPrevPage.selectDate("30 Nov 2024");
        await commSyncPage.checkRefreshdatabooks("commissions");
        await csPrevPage.runCommissions();
        await csPrevPage.clickSkipandRun();
        await csPrevPage.waitForCalculationMessage();
        await csPrevPage.waitForCommissionsSuccess();
        await commSyncPage.clickDetailedView();
        await expect(
          page.getByText(
            "3 out of 3 datasheets have stale data and need to be re-generated."
          )
        ).toBeVisible();
        await commSyncPage.closeShowDetails();

        console.log(
          "Verify only assosiated datasheets are refreshed on selecting Payees in Commission Plan"
        );
        await commSyncPage.selectCriteria("payeee-in-plan");
        await commSyncPage.selectDropdown([
          "freelance-commission-plan1",
          "freelance-commission-plan1_Copy",
        ]);
        await csPrevPage.selectDate("30 Nov 2024");
        await commSyncPage.checkRefreshdatabooks("commissions");
        await csPrevPage.runCommissions();
        await csPrevPage.clickSkipandRun();
        await csPrevPage.waitForCalculationMessage();
        await csPrevPage.waitForCommissionsSuccess();
        await commSyncPage.clickDetailedView();
        await expect(
          page.getByText("Out of 3 datasheets none of them are stale")
        ).toBeVisible();
        await commSyncPage.closeShowDetails();

        console.log(
          "Verify only assosiated datasheets are refreshed on selecting All Payees"
        );
        await commSyncPage.selectCriteria("all-payees");
        await csPrevPage.selectDate("30 Nov 2024");
        await commSyncPage.checkRefreshdatabooks("commissions");
        await csPrevPage.runCommissions();
        await csPrevPage.clickSkipandRun();
        await csPrevPage.waitForCalculationMessage();
        await csPrevPage.waitForCommissionsSuccess();
        await commSyncPage.clickDetailedView();
        await expect(
          page.getByText(
            "6 out of 9 datasheets have stale data and need to be re-generated."
          )
        ).toBeVisible();
        await commSyncPage.closeShowDetails();

        await csPrevPage.navigate("/datasheet");
        await staleDatasheet(
          dsV2Page,
          dsV2EditPage,
          "freelance-commissions-DB",
          "freelance-commissions-sheet"
        );
        await staleDatasheet(
          dsV2Page,
          dsV2EditPage,
          "freelance-commissions-DB",
          "freelance-comm-50rows"
        );
        await staleDatasheet(
          dsV2Page,
          dsV2EditPage,
          "Anime Heros",
          "anime-hero-sheet"
        );
      }
    );

    test(
      "Settlement Commission Calculation Sync",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8985" },
          {
            type: "Description",
            description: "INTER-9955",
          },
          {
            type: "Precondition",
            description:
              "Create datasheets with stale data and have plans, forecasts published.",
          },
          {
            type: "Expected Behaviour",
            description:
              "Verify only assosiated datasheets are generated on chosing selected payees, payees in commission plans, all payees",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(page);
        const commSyncPage = new CommissionSync(page);
        const csPrevPage = new CommissionsSyncPrevPeriod(page);

        await commSyncPage.action("Calculate Settlements Run");
        console.log(
          "Verify only assosiated datasheets are refreshed on selecting selected payees"
        );
        await commSyncPage.selectCriteria("selected-payees");
        await commSyncPage.selectDropdown(["Jane Smith", "Goku Son"]);
        await csPrevPage.selectDate("30 Nov 2024");
        await commSyncPage.checkRefreshdatabooks("settlements");
        await csPrevPage.runCommissions();
        await csPrevPage.clickSkipandRun();
        await csPrevPage.waitForSettlementCalculationMessage();
        await csPrevPage.waitForSettlementSuccess();
        await commSyncPage.clickDetailedView();
        await expect(
          page.getByText(
            "2 out of 2 datasheets have stale data and need to be re-generated."
          )
        ).toBeVisible();
        await commSyncPage.closeShowDetails();

        console.log(
          "Verify only assosiated datasheets are refreshed on selecting Payees in Commission Plan"
        );
        await commSyncPage.selectCriteria("payeee-in-plan");
        await commSyncPage.selectDropdown([
          "anime heros plan",
          "freelance-commission-plan1",
        ]);
        await csPrevPage.selectDate("30 Nov 2024");
        await commSyncPage.checkRefreshdatabooks("settlements");
        await csPrevPage.runCommissions();
        await csPrevPage.clickSkipandRun();
        await csPrevPage.waitForSettlementCalculationMessage();
        await csPrevPage.waitForSettlementSuccess();
        await commSyncPage.clickDetailedView();
        await expect(
          page.getByText(
            "1 out of 3 datasheets have stale data and need to be re-generated."
          )
        ).toBeVisible();
        await commSyncPage.closeShowDetails();

        console.log(
          "Verify only assosiated datasheets are refreshed on selecting All Payees"
        );
        await commSyncPage.selectCriteria("all-payees");
        await csPrevPage.selectDate("30 Nov 2024");
        await commSyncPage.checkRefreshdatabooks("settlements");
        await csPrevPage.runCommissions();
        await csPrevPage.waitForSettlementCalculationMessage();
        await csPrevPage.waitForSettlementSuccess();
        await commSyncPage.clickDetailedView();
        await expect(
          page.getByText("Out of 3 datasheets none of them are stale")
        ).toBeVisible();
        await commSyncPage.closeShowDetails();

        await csPrevPage.navigate("/datasheet");
        await staleDatasheet(
          dsV2Page,
          dsV2EditPage,
          "freelance-commissions-DB",
          "freelance-commissions-sheet"
        );
        await staleDatasheet(
          dsV2Page,
          dsV2EditPage,
          "freelance-commissions-DB",
          "freelance-comm-50rows"
        );
        await staleDatasheet(dsV2Page, dsV2EditPage, "payee-book", "payee");
        await staleDatasheet(
          dsV2Page,
          dsV2EditPage,
          "Anime Heros",
          "anime-hero-sheet"
        );
      }
    );

    test(
      "Forecast Commission Calculation Sync",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8985" },
          {
            type: "Description",
            description: "INTER-9955",
          },
          {
            type: "Precondition",
            description:
              "Create datasheets with stale data and have plans, forecasts published.",
          },
          {
            type: "Expected Behaviour",
            description:
              "Verify only assosiated datasheets are generated on chosing selected payees, payees in commission plans, all payees",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(page);
        const commSyncPage = new CommissionSync(page);
        const csPrevPage = new CommissionsSyncPrevPeriod(page);

        await commSyncPage.action("Commission Forecasting");
        console.log(
          "Verify only assosiated datasheets are refreshed on selecting selected payees"
        );
        await commSyncPage.selectCriteria("selected-payees");
        await commSyncPage.selectDropdown(["Alice J", "Goku Son"]);
        await csPrevPage.selectDate("30 Nov 2024");
        await commSyncPage.checkRefreshdatabooks("commission forecast");
        await csPrevPage.runCommissions();
        await csPrevPage.clickSkipandRun();
        await csPrevPage.waitForForecastCalculationMessage();
        await csPrevPage.waitForForecastSuccess();
        await commSyncPage.clickDetailedView();
        await expect(
          page.getByText(
            "1 out of 1 datasheets have stale data and need to be re-generated."
          )
        ).toBeVisible();
        await commSyncPage.closeShowDetails();

        console.log(
          "Verify only assosiated datasheets are refreshed on selecting Payees in Commission Plan"
        );
        await commSyncPage.selectCriteria("payeee-in-plan");
        await commSyncPage.selectDropdown(["forecast 1", "forecast 2"]);
        await csPrevPage.selectDate("30 Nov 2024");
        await commSyncPage.checkRefreshdatabooks("commission forecast");
        await csPrevPage.runCommissions();
        await csPrevPage.clickSkipandRun();
        await csPrevPage.waitForForecastCalculationMessage();
        await csPrevPage.waitForForecastSuccess();
        await commSyncPage.clickDetailedView();
        await expect(
          page.getByText(
            "1 out of 2 datasheets have stale data and need to be re-generated."
          )
        ).toBeVisible();
        await commSyncPage.closeShowDetails();

        console.log(
          "Verify only assosiated datasheets are refreshed on selecting All Payees"
        );
        await commSyncPage.selectCriteria("all-payees");
        await csPrevPage.selectDate("30 Nov 2024");
        await commSyncPage.checkRefreshdatabooks("commission forecast");
        await csPrevPage.runCommissions();
        await csPrevPage.clickSkipandRun();
        await csPrevPage.waitForForecastCalculationMessage();
        await csPrevPage.waitForForecastSuccess();
        await commSyncPage.clickDetailedView();
        await expect(
          page.getByText(
            "1 out of 3 datasheets have stale data and need to be re-generated."
          )
        ).toBeVisible();
        await commSyncPage.closeShowDetails();

        await csPrevPage.navigate("/datasheet");
        await staleDatasheet(
          dsV2Page,
          dsV2EditPage,
          "freelance-commissions-DB",
          "freelance-commissions-sheet"
        );
        await staleDatasheet(
          dsV2Page,
          dsV2EditPage,
          "freelance-commissions-DB",
          "freelance-comm-50rows"
        );
        await staleDatasheet(dsV2Page, dsV2EditPage, "payee-book", "payee");
      }
    );

    test(
      "Refresh databook based on custom objects",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8985" },
          {
            type: "Description",
            description: "INTER-9955",
          },
          {
            type: "Precondition",
            description:
              "Create datasheets with stale data and have plans, forecasts published.",
          },
          {
            type: "Expected Behaviour",
            description:
              "Verify only assosiated datasheets are generated on chosing selected payees, payees in commission plans, all payees",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commSyncPage = new CommissionSync(page);
        const csPrevPage = new CommissionsSyncPrevPeriod(page);

        await commSyncPage.action("Refresh Databooks Uploaded");
        console.log(
          "Verify only assosiated datasheets are refreshed on selecting a custom object."
        );
        await commSyncPage.selectCriteria("object");
        await commSyncPage.selectObject("join1");
        await csPrevPage.runCommissions();
        await csPrevPage.clickSkipandRun();
        await csPrevPage.waitForRefreshMessage();
        await csPrevPage.waitForRefreshSuccess();
        await commSyncPage.clickDetailedView();
        await expect(
          page.getByText("Out of 2 datasheets none of them are stale")
        ).toBeVisible();
        await commSyncPage.closeShowDetails();

        console.log(
          "Verify only assosiated datasheets are refreshed on selecting a custom object"
        );
        await commSyncPage.selectCriteria("selected-databook");
        await commSyncPage.selectDropdown(["Anime Heros", "payee-book"]);
        await csPrevPage.runCommissions();
        await csPrevPage.clickSkipandRun();
        await csPrevPage.waitForRefreshMessage();
        await csPrevPage.waitForRefreshSuccess();
        await commSyncPage.clickDetailedView();
        await expect(
          page.getByText(
            "2 out of 4 datasheets have stale data and need to be re-generated."
          )
        ).toBeVisible();
        await commSyncPage.closeShowDetails();

        console.log(
          "Verify only assosiated datasheets are refreshed on selecting a all databooks"
        );
        await commSyncPage.selectCriteria("all-databooks");
        await csPrevPage.runCommissions();
        await csPrevPage.clickSkipandRun();
        await csPrevPage.waitForRefreshMessage();
        await csPrevPage.waitForRefreshSuccess();
        await commSyncPage.clickDetailedView();
        await expect(
          page.getByText(
            "2 out of 9 datasheets have stale data and need to be re-generated."
          )
        ).toBeVisible();
        await commSyncPage.closeShowDetails();
      }
    );
  }
);
