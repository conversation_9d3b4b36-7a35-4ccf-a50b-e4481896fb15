import CommissionsSyncPrevPeriod from "../../../../../test-objects/commissionSyncPrevPeriod-objects";
import CommissionSync from "../../../../../test-objects/commissionSync-objects";

const {
  commissionSyncAdminFixtures: { test: test1, expect },
} = require("../../../../fixtures");

test1.describe(
  "Commission Sync",
  { tag: ["@commissionsync", "@regression", "@primelogic-1"] },
  () => {
    test1(
      "Commission sync card visible to all admins while running",
      async ({ adminPage, payeePage }, testInfo) => {
        const page = adminPage.page;
        const csPrev = new CommissionsSyncPrevPeriod(page);

        testInfo.setTimeout(testInfo.timeout + 300000);
        await csPrev.navigate("/settings/commissions-and-data-sync");
        const page1 = payeePage.page;
        const csPrev1 = new CommissionsSyncPrevPeriod(page1);
        await csPrev1.navigate("/settings/commissions-and-data-sync");
        await csPrev.selectDate("01 Dec 2024");

        await csPrev.runCommissions();
        await csPrev.clickSkipandRun();
        await csPrev.waitForCalculationMessage();
        await page1.reload();
        await csPrev1.waitForCalculationMessage();
        await csPrev1.waitForCommissionsSuccess();
      }
    );

    test1(
      "detail view check in sync validation",
      async ({ adminPage, payeePage }, testInfo) => {
        const page = adminPage.page;
        const csPrev = new CommissionsSyncPrevPeriod(page);
        const cs = new CommissionSync(page);

        testInfo.setTimeout(testInfo.timeout + 300000);
        await csPrev.navigate("/settings/commissions-and-data-sync");
        await cs.clickShowPastActivities();
        await expect(page.getByText("Completed").first()).toBeVisible();
        await expect(page.getByText("End time", { exact: true })).toBeVisible();
        await expect(
          page.getByText("Start time", { exact: true })
        ).toBeVisible();
        await expect(page.getByText("Initiated by").first()).toBeVisible();
        await cs.closeShowPastActivities();

        const page1 = payeePage.page;
        const csPrev1 = new CommissionsSyncPrevPeriod(page1);
        const cs1 = new CommissionSync(page1);
        await csPrev1.navigate("/settings/commissions-and-data-sync");
        await cs1.clickShowPastActivities();

        await expect(page1.getByText("Detailed view")).toHaveCount(0);
      }
    );
  }
);
