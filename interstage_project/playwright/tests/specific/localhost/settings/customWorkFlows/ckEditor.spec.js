import CwPage from "../../../../../test-objects/cw-objects";
import { expect } from "@playwright/test";
const {
  ckEditorCwFixtures: { test },
} = require("../../../../fixtures");

test.describe(
  "Ck Editor Custom Workflows",
  { tag: ["@customworkflow", "@regression", "@adminchamp-1"] },
  () => {
    test("dynamic value feature", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "1" },
        {
          type: "Description",
          description: "Test dynamic value feature",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "user should be able to add dynamic value",
        }
      );

      const page = adminPage.page;
      const cwPage = new CwPage(adminPage.page);

      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("main book", "payee1&2");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction();
      await cwPage.clickSendAnEmail();
      await cwPage.selectDeliveryCriteria(
        "Send separate messages for every record to each recipient"
      );
      await cwPage.insertDynamicValue("name");
      await page.waitForTimeout(3000);
      await expect(page.getByText("{{datasheet.name}}")).toBeVisible();
      await cwPage.clickExit();
    });

    test("Test table embeded feature", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "2" },
        {
          type: "Description",
          description: "Test table embeded feature",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "user should be able to embed table",
        }
      );

      const page = adminPage.page;
      const cwPage = new CwPage(adminPage.page);

      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("main book", "payee1&2");
      await cwPage.clickNextButton();
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("main book", "payee1&2");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction();
      await cwPage.clickSendAnEmail();
      await cwPage.selectDeliveryCriteria(
        "Send separate messages for every record to each recipient"
      );
      await cwPage.selectDeliveryCriteria(
        "Send a single message for all records to each recipient"
      );
      await cwPage.clickEmbedTable();
      await expect(
        page.getByRole("img", { name: "Embedded Table" })
      ).toBeVisible();
      await cwPage.clickExit();
    });

    test.skip("Test Alignment feature", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "3" },
        {
          type: "Description",
          description: "Test alignment feature",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to align text right",
        }
      );

      const page = adminPage.page;
      const cwPage = new CwPage(adminPage.page);

      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("main book", "payee1&2");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction();
      await cwPage.clickSendAnEmail();
      await cwPage.selectDeliveryCriteria(
        "Send separate messages for every record to each recipient"
      );
      await cwPage.selectDeliveryCriteria(
        "Send a single message for all records to each recipient"
      );
      await cwPage.clickCkEditor();
      await cwPage.addTextInCkEditor("Test Description");
      await cwPage.clickAlignRight();
      // await page.locator("button.ck.ck-button.ck-off").nth(9).click();
      await page.waitForTimeout(3000);
      // Locate the element and check if "Test Description" is right-aligned
      const isRightAligned = await page
        .locator('p[data-placeholder="Type something..."]')
        .evaluate((el) => {
          const isTextCorrect = el.textContent.trim() === "Test Description";
          const isAlignedRight =
            window.getComputedStyle(el).textAlign === "right";
          return isTextCorrect && isAlignedRight;
        });
      console.log(`"Test Description" is right-aligned: ${isRightAligned}`);
      await cwPage.clickExit();
    });

    test("Test link, bullet list, number list", async ({
      adminPage,
      context,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "4" },
        {
          type: "Description",
          description: "Test link, bullet list, number list feature",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "user should be able to add link, bullet list, number list",
        }
      );

      const page = adminPage.page;
      const cwPage = new CwPage(adminPage.page);

      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("main book", "payee1&2");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction();
      await cwPage.clickSendAnEmail();
      await cwPage.selectDeliveryCriteria(
        "Send separate messages for every record to each recipient"
      );
      await cwPage.selectDeliveryCriteria(
        "Send a single message for all records to each recipient"
      );
      await cwPage.clickCkEditor();
      await cwPage.addTextInCkEditor("Test Description");
      // check numbered list feature
      await cwPage.showMoreItems();
      await page.getByLabel("Numbered List").click();
      // Locate the element with the specified class and text

      const locator = page.locator(
        "ol > li > span.ck-list-bogus-paragraph:has-text('Test Description')"
      );
      // Check that the element is visible
      await expect(locator).toBeVisible();

      await cwPage.showMoreItems();
      // check bulleted list feature
      await page.getByLabel("Bulleted List").click();

      const locator2 = page.locator(
        "ul > li > span.ck-list-bogus-paragraph:has-text('Test Description')"
      );
      // Check that the element is visible
      await expect(locator2).toBeVisible();

      // check link feature
      await cwPage.showMoreItems();
      await cwPage.addLink("https://qa.everstage.com/");
      await page
        .getByRole("link", { name: "https://qa.everstage.com/" })
        .click();
      const page1Promise = page.waitForEvent("popup");
      await page.getByLabel("https://qa.everstage.com/").click();
      const page1 = await page1Promise;
      await expect(
        page1.getByRole("heading", { name: "Welcome to Everstage" })
      ).toBeVisible();
    });

    test("Test bold, italic, strike through, code block, publish workflow, delete workflow", async ({
      adminPage,
      context,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "5" },
        {
          type: "Description",
          description: "Test bold, italic, strike through, code block feature",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "user should be able to add bold, italic, strike through, code block",
        }
      );

      const page = adminPage.page;
      const cwPage = new CwPage(adminPage.page);
      await cwPage.workflowScreen();
      await cwPage.newWorkflowButton();
      await cwPage.selectingDatabook("main book", "payee1&2");
      await cwPage.clickNextButton();
      await cwPage.clickAddAction();
      await cwPage.clickThenAddAnAction();
      await cwPage.clickSendAnEmail();
      await cwPage.selectDeliveryCriteria(
        "Send separate messages for every record to each recipient"
      );
      await cwPage.selectRecipient("everstage admin");
      await page
        .locator("//button/*[text()='Send test email']")
        .click({ force: true, timeout: 5000 });
      await cwPage.enterSubjectLine("Test Subject");
      await cwPage.clickCkEditor();
      await cwPage.addTextInCkEditor("Test Description");
      await cwPage.selectAll();

      // check bold feature
      await cwPage.selectBold();
      const isTagName = await cwPage.verifyTagName(
        "strong",
        "Test Description"
      );
      expect(isTagName).toBe(true);
      await cwPage.selectAll();
      await cwPage.selectBold();

      // check italic feature
      await cwPage.clickCkEditor();
      await cwPage.selectAll();
      await cwPage.selectItalic();
      const isTagName2 = await cwPage.verifyTagName("i", "Test Description");
      expect(isTagName2).toBe(true);
      await cwPage.selectAll();
      await cwPage.selectItalic();

      // check strikethrough feature
      await cwPage.clickCkEditor();
      await cwPage.selectAll();
      await cwPage.selectStrikethrough();
      const isTagName3 = await cwPage.verifyTagName("s", "Test Description");
      expect(isTagName3).toBe(true);
      await cwPage.selectAll();
      await cwPage.selectStrikethrough();

      // check quote format feature
      await cwPage.clickCkEditor();
      await cwPage.selectAll();
      await cwPage.showMoreItems();
      await cwPage.quoteFormat();
      const isTagName4 = await cwPage.verifyParentTagName(
        "p",
        "Test Description",
        "//blockquote"
      );
      expect(isTagName4).toBe(true);

      await cwPage.clickNextButton();
      await cwPage.nameWorkflow("test");
      await cwPage.publishWorkflow();
      await expect(page.getByText("Workflow published")).toBeVisible();
      await cwPage.clickExit();
      await page.waitForTimeout(3000);
      await cwPage.deleteWorkflow();
      await expect(page.getByText("workflow Deleted")).toBeVisible();
    });
  }
);
