/* eslint-disable playwright/no-wait-for-timeout */
import AdjustementsV2Page from "../../../../../test-objects/adjustments-v2-objects";

const {
  customCalendarFixtures: { test, expect },
} = require("../../../../fixtures");

test.describe(
  "Custom Calendar",
  { tag: ["@regression", "@customcalendar", "@settings", "@primelogic-4"] },
  () => {
    test("User should be able to complete basic Custom Calendar flow", async ({
      adminPage,
    }) => {
      // Delete test calendar
      const page = adminPage.page;
      const adjv2 = new AdjustementsV2Page(page);

      await adjv2.navigate("/settings");
      await expect(
        await page.getByRole("link", { name: "Custom Calendar" })
      ).toBeVisible();
      await page.getByRole("link", { name: "Custom Calendar" }).click();
      await page.getByRole("button", { name: "Create Calendar" }).click();
      await expect(
        await page
          .locator("span")
          .filter({ hasText: "Create Manually" })
          .first()
      ).toBeVisible();
      await expect(
        await page
          .locator("span")
          .filter({ hasText: "Bulk ImportImport .CSV file" })
          .first()
      ).toBeVisible();
      await page
        .locator("span")
        .filter({ hasText: "Create Manually" })
        .first()
        .click();
      await page.getByRole("button", { name: "Next" }).click();
      await expect(
        await page.locator("label").filter({ hasText: "Weekly" }).first()
      ).toBeVisible();
      await expect(
        await page.locator("label").filter({ hasText: "Bi-Weekly" }).first()
      ).toBeVisible();
      await expect(
        await page.locator("label").filter({ hasText: "Custom" }).first()
      ).toBeVisible();
      await page.locator("label").filter({ hasText: "Weekly" }).first().click();
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByPlaceholder("Calendar Name").click();
      await page.getByPlaceholder("Calendar Name").fill("Test Calendar");
      await page.getByLabel("Start Week On*").click();
      await page.locator("span").filter({ hasText: "Sunday" }).click();
      await adjv2.selectPeriod("Select the date", "01 Feb 2024");
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "Validate" }).click();
      await expect(
        await page.getByText("Successfully validated custom")
      ).toBeVisible();
      await expect(async () => {
        await page.getByRole("button", { name: "Create", exact: true }).click();
      }).toPass({ timeout: 10000, intervals: [2000] });
      await expect(
        await page.getByText("Custom calendar was created")
      ).toBeVisible();
      await expect(await page.getByText("Test Calendar")).toBeVisible();
      await page.getByText("Test Calendar").click();
      await expect(await page.getByText("View/Edit Calendar")).toBeVisible();
      await page.locator("span.ant-modal-close-x").click();
    });

    test("User should be able to create bi-weekly calendar and 2 calendars should not have the same name", async ({
      adminPage,
    }) => {
      // Delete biweekly test calendar
      const page = adminPage.page;
      const adjv2 = new AdjustementsV2Page(page);

      await adjv2.navigate("/settings");
      await page.getByRole("link", { name: "Custom Calendar" }).click();
      await page.getByRole("button", { name: "Create Calendar" }).click();
      await page
        .locator("span")
        .filter({ hasText: "Create Manually" })
        .first()
        .click();
      await page.getByRole("button", { name: "Next" }).click();
      await page
        .locator("label")
        .filter({ hasText: "Bi-Weekly" })
        .first()
        .click();
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByPlaceholder("Calendar Name").click();
      await page.getByPlaceholder("Calendar Name").fill("Test Calendar");
      await page.getByLabel("Start Bi-weekly On*").click();
      await page.locator("span").filter({ hasText: "Sunday" }).click();
      await adjv2.selectPeriod("Select the date", "01 Feb 2024");
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "Validate" }).click();
      await expect(
        await page.getByText("Custom calendar with this name already exists")
      ).toBeVisible();
      await page.getByRole("button", { name: "Back" }).click();
      await page.getByPlaceholder("Calendar Name").click();
      await page
        .getByPlaceholder("Calendar Name")
        .fill("Bi Weekly test calendar");
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "Validate" }).click();
      await expect(
        await page.getByText("Successfully validated custom")
      ).toBeVisible();
      await expect(async () => {
        await page.getByRole("button", { name: "Create", exact: true }).click();
      }).toPass({ timeout: 10000, intervals: [2000] });
      await expect(
        await page.getByText("Custom calendar was created")
      ).toBeVisible();
      await expect(
        await page.getByText("Bi Weekly test calendar")
      ).toBeVisible();
      await page.getByText("Bi Weekly test calendar").click();
      await expect(await page.getByText("View/Edit Calendar")).toBeVisible();
      await page.locator("span.ant-modal-close-x").click();
    });

    test("User should be able rename/edit/delete a custom calendar", async ({
      adminPage,
    }) => {
      // remove misc calendars
      const page = adminPage.page;
      const adjv2 = new AdjustementsV2Page(page);

      await adjv2.navigate("/settings");
      await page.getByRole("link", { name: "Custom Calendar" }).click();
      await page.getByRole("button", { name: "Create Calendar" }).click();
      await page
        .locator("span")
        .filter({ hasText: "Create Manually" })
        .first()
        .click();
      await page.getByRole("button", { name: "Next" }).click();
      await page
        .locator("label")
        .filter({ hasText: "Bi-Weekly" })
        .first()
        .click();
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByPlaceholder("Calendar Name").click();
      await page.getByPlaceholder("Calendar Name").fill("Misc testing");
      await page.getByLabel("Start Bi-weekly On*").click();
      await page.locator("span").filter({ hasText: "Sunday" }).click();
      await adjv2.selectPeriod("Select the date", "01 Feb 2024");
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "Validate" }).click();
      await expect(
        await page.getByText("Successfully validated custom")
      ).toBeVisible();
      await expect(async () => {
        await page.getByRole("button", { name: "Create", exact: true }).click();
      }).toPass({ timeout: 10000, intervals: [2000] });
      await expect(
        await page.getByText("Custom calendar was created")
      ).toBeVisible();
      await expect(await page.getByText("Misc testing")).toBeVisible();
      await page.getByText("Misc testing").click();
      await expect(await page.getByText("View/Edit Calendar")).toBeVisible();
      await page.getByRole("button", { name: "Edit" }).click();
      await page.getByPlaceholder("Enter calender name").click();
      await page
        .getByPlaceholder("Enter calender name")
        .fill("Misc testing renamed");
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        await page.getByText("Successfully saved custom calendar")
      ).toBeVisible();
      await page
        .getByText("Successfully saved custom calendar")
        .waitFor({ state: "hidden" });
      await page.locator("span.ant-modal-close-x").click();
      await expect(await page.getByText("Misc testing renamed")).toBeVisible();
      await page.getByText("Misc testing renamed").click();
      await page.getByRole("button", { name: "Edit" }).click();
      await page
        .getByRole("gridcell", { name: "Dec 31, 2023 - Jan 13, 2024" })
        .dblclick();
      await page.getByLabel("Input Editor").fill("Dec 31, 2023 - Jan 15, 2024");
      await page.getByLabel("Input Editor").press("Enter");
      await page
        .getByRole("gridcell", { name: "Jan 14, 2024 - Jan 27, 2024" })
        .dblclick();
      await page.getByLabel("Input Editor").fill("Jan 15, 2024 - Jan 27, 2024");
      await page.getByLabel("Input Editor").press("Enter");
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        await page.getByText("Successfully saved custom calendar").first()
      ).toBeVisible();
      await page.locator("span.ant-modal-close-x").click();
      await page.getByText("Misc testing renamed").click();
      await expect(
        await page.getByRole("gridcell", { name: "Dec 31, 2023 - Jan 15," })
      ).toBeVisible();
      await expect(
        await page.getByRole("gridcell", { name: "Jan 15, 2024 - Jan 27," })
      ).toBeVisible();
      await page.locator("span.ant-modal-close-x").click();
      await page.getByTestId("pt-action-Misc testing renamed").click();
      await page.getByRole("menuitem", { name: "Delete" }).click();
      await page.getByRole("button", { name: "Yes, delete" }).click();
      await page.getByText("Custom calendar was deleted").click();
    });

    test("Created calendar should be available in payout frequency dropdown", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.goto("/settings", { waitUntil: "networkidle" });
      await page.getByRole("link", { name: "Custom Calendar" }).click();
      await expect(await page.getByText("Payee Calendar")).toBeVisible();
      await page.goto("/users", { waitUntil: "networkidle" });
      await page
        .getByRole("gridcell", { name: "AT Admin test admin.10409@" })
        .getByRole("button")
        .click();
      await page.getByLabel("Payout Frequency*").click();
      await expect(await page.getByText("Payee Calendar")).toBeVisible();
    });

    test("Datewise options should be present in statemenet/payouts/sync", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      const adjv2 = new AdjustementsV2Page(page);

      await adjv2.navigate("/settings");
      await page.getByRole("link", { name: "Custom Calendar" }).click();
      await expect(await page.getByText("Payee Calendar")).toBeVisible();
      await adjv2.navigate("/commissions");
      await adjv2.selectPeriod("Select date", "16 Jun 2024");
      await expect(await page.getByText("Zero Payout")).toBeVisible();
      await adjv2.selectPeriod("Select date", "24 Mar 2024");
      await expect(await page.getByText("$4,414.00")).toBeVisible();
      await adjv2.selectPeriod("Select date", "10 Mar 2024");
      await expect(await page.getByText("$2,120.00")).toBeVisible();
      await adjv2.selectPeriod("Select date", "21 Apr 2024");
      await expect(await page.getByText("$1,040.00")).toBeVisible();
      await adjv2.selectPeriod("Select date", "02 Jun 2024");
      await expect(await page.getByText("Zero Payout")).toBeVisible();
      await page.getByRole("link", { name: "Payee test" }).click();
      await page.waitForNavigation();
      await page
        .getByTestId("ever-select")
        .getByText("May 20, 2024 - Jun 02, 2024")
        .click();
      await expect(
        await page.getByText("Jun 03, 2024 - Jun 16, 2024")
      ).toBeVisible();
      await expect(
        await page.getByText("Apr 08, 2024 - Apr 21, 2024")
      ).toBeVisible();
      await expect(
        await page.getByText("Mar 25, 2024 - Apr 07, 2024")
      ).toBeVisible();
      await expect(
        await page.getByText("Mar 11, 2024 - Mar 24, 2024")
      ).toBeVisible();
    });
  }
);
