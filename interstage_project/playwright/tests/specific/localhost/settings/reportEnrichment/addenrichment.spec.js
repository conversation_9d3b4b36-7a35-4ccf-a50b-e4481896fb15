import { setupGraphQLRouteInterceptor } from "../../../../bearerToken";
import DatasheetV2EditViewPage from "../../../../../test-objects/datasheet-v2-editView-objects";
import DatasheetV2Page from "../../../../../test-objects/datasheet-v2-objects";
import V2Transformations from "../../../../../test-objects/datasheetV2_transformations-objects";
import ReportEnrichmentPage from "../../../../../test-objects/reportEnrichment-objects";

const {
  enrichFixtures: { test, expect },
} = require("../../../../fixtures");
const {
  dsV2EnrichmentFixtures: { test: test1, expect: expect1 },
} = require("../../../../fixtures");

let token = "";

test.describe(
  "Report_Enrichment",
  { tag: ["@reportenrichment", "@regression", "@primelogic-5"] },
  () => {
    test.skip("Generate_datasheet after enrichment", async ({
      adminPage,
      request,
    }, testInfo) => {
      const page = adminPage.page;
      await page.goto("/settings/report-enrichment", {
        waitUntil: "networkidle",
      });
      await page.reload();

      // create the new enrichment variable and edit the field name
      token = await setupGraphQLRouteInterceptor(page);
      await page.waitForLoadState("networkidle");
      testInfo.setTimeout(testInfo.timeout + 80000);
      await page
        .locator('input:near(span:text("Commission Plan"))')
        .locator("nth=0")
        .click();
      await page.getByTitle("Demo-quarterly").locator("div").first().click();
      await page
        .locator('input:near(span:text("Report Type"))')
        .locator("nth=0")
        .click();
      await page
        .getByTitle("Commission Report", { exact: true })
        .locator("div")
        .first()
        .click();
      await page
        .locator('input:near(span:text("Criterias"))')
        .locator("nth=0")
        .click();
      await page.getByTitle("Quarterly-simple").locator("div").first().click();
      await page
        .locator('input:near(span:text("Variables"))')
        .locator("nth=1")
        .click();
      await page.getByTitle("calc_field").locator("div").first().click();
      await page
        .locator("label")
        .filter({ hasText: "Include all plans using this datasheet" })
        .locator("span")
        .first()
        .click();
      await expect(
        page.getByText(
          "Variables that have already been added to a criteria will be skipped during the "
        )
      ).toHaveCount(1);
      await page.getByRole("button", { name: "Validate & Add" }).click();
      await page
        .getByText("Enrichment variables validated successfully")
        .waitFor({ state: "visible", timeout: 5000 });
      await page.getByRole("button", { name: "Save" }).click();
      await page
        .getByText("Changes saved successfully")
        .waitFor({ state: "visible", timeout: 5000 });
      const agGrid = await page.locator(".ag-root");

      // Edit the name of the enriched variable
      async function scrollAGGridRight(pixels) {
        await agGrid.evaluate((node, pixels) => {
          const scrollContainer = node.querySelector(
            ".ag-body-horizontal-scroll-container"
          );
          scrollContainer.scrollLeft += pixels;
        }, pixels);
      }

      // Scroll the AG Grid horizontally by 100 pixels
      await scrollAGGridRight(100);
      await page
        .getByRole("gridcell", { name: "calc_field" })
        .waitFor({ state: "visible", timeout: 5000 });
      await page.getByRole("gridcell", { name: "calc_field" }).dblclick();

      await page
        .getByRole("textbox", { name: "Input Editor" })
        .fill("calc_field_edit");
      await page.waitForTimeout(1000);
      await page.getByText("Settings/Report Enrichment").click();
      await expect(
        page.getByRole("gridcell", { name: "calc_field_edit" })
      ).toHaveCount(1);

      // Add the enriched variable in the datsheet and verify the value

      await page.goto("/databook/ecbf4e5c-413a-41ec-b8f1-6887d6d5cbbe", {
        waitUntil: "networkidle",
      });
      await page.getByRole("button", { name: "remove" }).nth(1).click();
      await page
        .getByRole("menuitem", { name: "Edit" })
        .getByText("Edit")
        .click();
      await page.waitForTimeout(1000);
      await page.locator(".ant-checkbox-input").locator("nth=0").click();
      await page.waitForTimeout(1000);
      await page.getByRole("button", { name: "Validate" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(page.getByText("Datasheet saved successfully")).toHaveCount(
        1
      );
      const resp = await request.post(
        "commission_engine/generate_datasheet_data",
        {
          data: {
            databook_id: "ecbf4e5c-413a-41ec-b8f1-6887d6d5cbbe",
            datasheet_id: "682bd593-fab0-4e3b-bd4c-94e107bdcb7f",
            is_report_data_stale: false,
            is_force_invalidate: false,
          },
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const response2 = await resp.json();
      expect(await response2.Status).toBe("Task created successfully");
      await page.waitForTimeout(2500);
      await page.goto("/databook/ecbf4e5c-413a-41ec-b8f1-6887d6d5cbbe", {
        waitUntil: "networkidle",
      });
      await page.getByText("commission_Copy").click();
      await page
        .getByText("This datasheet is currently being generated")
        .waitFor({ state: "hidden", timeout: 40000 });
      await page.goto("/databook", { waitUntil: "networkidle" });
      const resp2 = await request.post("commission_engine/run_report_etl", {
        data: {
          mode: "all",
          reportObjectId: "commission",
          email: "",
          payeeList: null,
          reportEtlPeriod: null,
        },
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const response = await resp2.json();
      expect(await response).toBe("Success");
      await page.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await page
        .getByText("ETL For Report Object Completed")
        .waitFor({ state: "visible", timeout: 50000 });
      await page.goto("/databook/ecbf4e5c-413a-41ec-b8f1-6887d6d5cbbe", {
        waitUntil: "networkidle",
      });
      await page.getByText("commission_Copy").click();
      await page.reload({ waitUntil: "networkidle" });
      await page.getByRole("button", { name: "Update Data" }).click();
      await page.getByText("Datasheet sync request has been submitted").click();
      await page
        .getByText("This datasheet is currently being generated")
        .waitFor({ state: "hidden", timeout: 40000 });
      await page.locator(".grow > div > .ant-btn").click();
      await page
        .getByRole("tooltip", {
          name: "Saved Views Search Views Quarterly-simple Filter",
        })
        .getByText("Quarterly-simple")
        .click();
      await page.getByRole("tab", { name: "Customize Columns" }).click();
      await page
        .getByRole("treeitem", { name: "Period Start Date Column" })
        .getByText("Period Start Date")
        .click();
      await page
        .getByRole("treeitem", { name: "Period End Date Column" })
        .getByText("Period End Date")
        .click();
      await page
        .getByRole("treeitem", { name: "Commission Plan Column" })
        .getByText("Commission Plan")
        .click();
      await page
        .getByRole("treeitem", { name: "Criteria Column" })
        .getByText("Criteria")
        .click();
      await page
        .getByRole("treeitem", { name: "Line Item Id Column" })
        .getByText("Line Item Id")
        .click();
      await page.getByPlaceholder("Search...").click();
      await page.getByPlaceholder("Search...").fill("id");
      await page
        .getByRole("treeitem", { name: "Plan Id Column" })
        .getByText("Plan Id")
        .click();
      await page
        .getByRole("treeitem", { name: "Criteria Id Column" })
        .getByText("Criteria Id")
        .click();
      await page.getByRole("tab", { name: "Customize Columns" }).click();
      await expect(
        page.getByRole("gridcell", { name: "421.70" }).getByText("421.70")
      ).toHaveCount(1);
      await page.goto("/databook", { waitUntil: "networkidle" });
    });
  }
);

test1.describe(
  "Datasheet V2 Report Enrichment - Prod Bugs",
  { tag: ["@regression", "@datasheet", "@adminchamp-3"] },
  () => {
    test1(
      "PR 9071 - PROD Bug - Adding same Report Enrichment variables from different plans resulted in duplicate variables in Datasheet columns",
      {
        annotation: [
          { type: "Test ID", description: "PR-9071" },
          {
            type: "Description",
            description:
              "Verify that adding same Report Enrichment variables from different plans should not bring in duplicate variables names in Datasheet columns",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "Varibles with duplicate names should not be displayed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        const dsV2Page = new DatasheetV2Page(page);
        const v2EditPage = new DatasheetV2EditViewPage(page);
        const reportEnrichment = new ReportEnrichmentPage(page);

        await reportEnrichment.navigateToReportEnrichmentPage();
        await reportEnrichment.selectCommissionPlan("Enrichment Plan");
        await reportEnrichment.selectReportType("Commission");
        await reportEnrichment.selectCriteria("Simple Enrich");
        await reportEnrichment.selectVariables([
          "Freelancer Name",
          "Payment Confirmed",
        ]);
        await reportEnrichment.clickValidateAndAdd();
        await reportEnrichment.save();

        await reportEnrichment.navigateToReportEnrichmentPage();
        await reportEnrichment.selectCommissionPlan("Enrichment Second Plan");
        await reportEnrichment.selectReportType("Commission");
        await reportEnrichment.selectCriteria("Simple Enrich");
        await reportEnrichment.selectVariables(["Payment Confirmed"]);
        await reportEnrichment.clickValidateAndAdd();
        await reportEnrichment.save();

        await reportEnrichment.navigateToReportEnrichmentPage();
        await reportEnrichment.selectCommissionPlan("Enrichment Third Plan");
        await reportEnrichment.selectReportType("Commission");
        await reportEnrichment.selectCriteria("Simple Enrich");
        await reportEnrichment.selectVariables([
          "Freelancer Name",
          "Payment Confirmed",
        ]);
        await reportEnrichment.clickValidateAndAdd();
        await reportEnrichment.save();

        await v2Transformations.navigateToDatasheetPage();
        await dsV2Page.goToDatasheet("Prod Bugs", "Existing Comm Report");
        await dsV2Page.clickEditBtn();

        await expect1(
          page.getByText("Simple Enrich::Freelancer Name", { exact: true })
        ).toHaveCount(1);
        await expect1(
          page.getByText("Commission :: Simple Enrich::Freelancer Name")
        ).toHaveCount(1);
        await expect1(
          page.getByText("Simple Enrich::Payment Confirmed", { exact: true })
        ).toHaveCount(1);
        await expect1(
          page.getByText("Commission :: Simple Enrich::Payment Confirmed", {
            exact: true,
          })
        ).toHaveCount(1);
        await expect1(
          page.getByText(
            "Commission :: Commission :: Simple Enrich::Payment Confirmed",
            { exact: true }
          )
        ).toHaveCount(1);
        await v2EditPage.verifyCheckboxesCountToBe(33);
      }
    );
  }
);
