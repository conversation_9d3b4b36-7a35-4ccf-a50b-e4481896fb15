import DatasheetV2EditViewPage from "../../../../../test-objects/datasheet-v2-editView-objects";
import CommissionSync from "../../../../../test-objects/commissionSync-objects";
import CommissionsSyncPrevPeriod from "../../../../../test-objects/commissionSyncPrevPeriod-objects";
import CommissionRBAC from "../../../../../test-objects/commisionRBAC-objects";
import CommonUtils from "../../../../../test-objects/common-utils-objects";

const {
  default: DatasheetV2Page,
} = require("../../../../../test-objects/datasheet-v2-objects");
const {
  autoenrichFixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({}, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 90000);
});

test.describe(
  "Auto Enrichment Report",
  { tag: ["@reportenrichment", "@regression", "@primelogic-5"] },
  () => {
    test(
      "Validate whether Report Enrichment Screen is not present in Settings when auto-enrich report flag is on",
      {
        annotation: [
          {
            type: "TestID",
            description: "INTER-T19075",
          },
          {
            type: "Description",
            description:
              "Validate whether Report Enrichment Screen is not present in Settings when auto-enrich report flag is on",
          },
          {
            type: "Expected Behaviour",
            description:
              "Report Enrichment screen should not present in Settings when auto-enrich report flag is on",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/settings", { waitUntil: "networkidle" });
        await expect(
          await page.getByRole("link", { name: "Report Enrichment" })
        ).toBeHidden();
      }
    );

    test(
      "Validate whether Datasheet used in Comm Plans are auto populated in existing Commission and inter commission Report Object Datasheet",
      {
        annotation: [
          {
            type: "TestID",
            description:
              "INTER-T19076,INTER-T19083,INTER-T19088,INTER-T19092,INTER-T19093",
          },
          {
            type: "Description",
            description:
              "Validate whether Datasheet used in Comm Plans are auto populated in Commission and inter commission Report Object Datasheet",
          },
          {
            type: "Expected Behaviour",
            description:
              "Datasheet used in comm plans should be auto populated in Commission and inter commission Report Object Datasheet",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/datasheet", { waitUntil: "networkidle" });
        const datasheet = new DatasheetV2Page(page);
        await datasheet.goToDatasheet("Deals", "Comm report");
        await datasheet.validateVariables([
          "Deals:Deals 2024:amount",
          "Deals:Deals 2024:bool",
          "Deals:Deals 2024:date of sale",
          "Deals:Deals 2024:email",
          "Deals:Deals 2024:id",
          "Manage Payee Details:Payee Details:commission amount",
          "Manage Payee Details:Payee Details:date of closing",
          "Manage Payee Details:Payee Details:id",
          "Manage Payee Details:Payee Details:payee email",
        ]);
        await datasheet.validateVariablesisNotPresent([
          "Manage Payee Details:Payee Details:commission percentage",
        ]);
        await datasheet.goToDatasheet("Deals", "Inter comm report");
        await datasheet.validateVariables([
          "Deals:Deals 2024:amount",
          "Deals:Deals 2024:bool",
          "Deals:Deals 2024:date of sale",
          "Deals:Deals 2024:email",
          "Deals:Deals 2024:id",
          "Manage Payee Details:Payee Details:commission amount",
          "Manage Payee Details:Payee Details:date of closing",
          "Manage Payee Details:Payee Details:id",
          "Manage Payee Details:Payee Details:payee email",
        ]);
        await datasheet.validateVariablesisNotPresent([
          "Manage Payee Details:Payee Details:commission percentage",
        ]);
      }
    );

    test(
      "Validate whether Datasheet used in Comm Plans are auto populated in new Commission and inter commission Report Object Datasheet",
      {
        annotation: [
          {
            type: "TestID",
            description:
              "INTER-T19077,INTER-T19081,INTER-T19081,INTER-T19089,INTER-T19094",
          },
          {
            type: "Description",
            description:
              "Validate whether Datasheet used in Comm Plans are auto populated in Commission and inter commission Report Object Datasheet",
          },
          {
            type: "Expected Behaviour",
            description:
              "Datasheet used in comm plans should be auto populated in Commission and inter commission Report Object Datasheet",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("/datasheet", { waitUntil: "networkidle" });
        const datasheet = new DatasheetV2Page(page);
        await datasheet.goToDatasheet("Deals", "Comm report");
        await datasheet.createDatasheet(
          "Deals",
          "Commission Report",
          "report",
          "Commission"
        );
        await datasheet.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await datasheet.clickSaveBtn();
        await datasheet.validateVariables([
          "Deals:Deals 2024:amount",
          "Deals:Deals 2024:bool",
          "Deals:Deals 2024:date of sale",
          "Deals:Deals 2024:email",
          "Deals:Deals 2024:id",
          "Manage Payee Details:Payee Details:commission amount",
          "Manage Payee Details:Payee Details:date of closing",
          "Manage Payee Details:Payee Details:id",
          "Manage Payee Details:Payee Details:payee email",
        ]);
        await datasheet.validateVariablesisNotPresent([
          "Manage Payee Details:Payee Details:commission percentage",
        ]);
        await datasheet.goToDatasheet("Deals", "Inter comm report");
        await datasheet.createDatasheet(
          "Deals",
          "Inter Commission Report",
          "report",
          "Inter Commission"
        );
        await datasheet.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await datasheet.clickSaveBtn();
        await datasheet.validateVariables([
          "Deals:Deals 2024:amount",
          "Deals:Deals 2024:bool",
          "Deals:Deals 2024:date of sale",
          "Deals:Deals 2024:email",
          "Deals:Deals 2024:id",
          "Manage Payee Details:Payee Details:commission amount",
          "Manage Payee Details:Payee Details:date of closing",
          "Manage Payee Details:Payee Details:id",
          "Manage Payee Details:Payee Details:payee email",
        ]);
        await datasheet.validateVariablesisNotPresent([
          "Manage Payee Details:Payee Details:commission percentage",
        ]);
      }
    );

    test(
      "Validate whether creating and deleting new criteria with datasheet autopopulating columns in existing commission and inter commission report object",
      {
        annotation: [
          {
            type: "TestID",
            description:
              "INTER-T19078,INTER-T19084,INTER-T19085,INTER-T19090,INTER-T19096",
          },
          {
            type: "Description",
            description:
              "Validate whether creating and deleting new criteria with datasheet autopopulating columns in existing commission and inter commission report object",
          },
          {
            type: "Expected Behaviour",
            description:
              "Creating New Criteria and deleting criteria with datasheet should auto populate columns in exising commission and inter commission report object",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commissionPage = new CommissionRBAC(page);
        await commissionPage.navigation("/plans");
        const clonedPlanName = await commissionPage.cloneSimplePlan(
          "January Plan Deals",
          "Test",
          "simple",
          "Manager Pay Details",
          "manager payee Details",
          "email",
          "date of sales",
          "amount"
        );
        await commissionPage.navigation("/datasheet");
        const datasheet = new DatasheetV2Page(page);
        await datasheet.goToDatasheet("Deals", "Comm report");
        await datasheet.validateVariables([
          "Manager Pay Details:manager payee Details:amount",
          "Manager Pay Details:manager payee Details:commission percentage",
          "Manager Pay Details:manager payee Details:date of sales",
          "Manager Pay Details:manager payee Details:email",
          "Manager Pay Details:manager payee Details:id",
        ]);
        await datasheet.goToDatasheet("Deals", "Inter comm report");
        await datasheet.validateVariables([
          "Manager Pay Details:manager payee Details:amount",
          "Manager Pay Details:manager payee Details:commission percentage",
          "Manager Pay Details:manager payee Details:date of sales",
          "Manager Pay Details:manager payee Details:email",
          "Manager Pay Details:manager payee Details:id",
        ]);
        await commissionPage.navigation("/plans");
        await commissionPage.DeletePlan(clonedPlanName);
        await commissionPage.navigation("/datasheet");
        await datasheet.goToDatasheet("Deals", "Comm report");
        await datasheet.validateVariablesisNotPresent([
          "Manager Pay Details:manager payee Details:amount",
          "Manager Pay Details:manager payee Details:commission percentage",
          "Manager Pay Details:manager payee Details:date of sales",
          "Manager Pay Details:manager payee Details:email",
          "Manager Pay Details:manager payee Details:id",
        ]);
        await datasheet.goToDatasheet("Deals", "Inter comm report");
        await datasheet.validateVariablesisNotPresent([
          "Manager Pay Details:manager payee Details:amount",
          "Manager Pay Details:manager payee Details:commission percentage",
          "Manager Pay Details:manager payee Details:date of sales",
          "Manager Pay Details:manager payee Details:email",
          "Manager Pay Details:manager payee Details:id",
        ]);
      }
    );

    test(
      "Validate whether creating and deleting new criteria with datasheet autopopulating columns in new commission and inter commission report object",
      {
        annotation: [
          {
            type: "TestID",
            description: "INTER-T19079,INTER-T19091",
          },
          {
            type: "Description",
            description:
              "Validate whether creating and deleting new criteria with datasheet autopopulating columns in new commission and inter commission report object",
          },
          {
            type: "Expected Behaviour",
            description:
              "Creating New Criteria and deleting criteria with datasheet should auto populate columns in exising commission and inter commission report object",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        testInfo.setTimeout(testInfo.timeout + 240000);
        const page = adminPage.page;
        const commissionPage = new CommissionRBAC(page);
        await commissionPage.navigation("/plans");
        const clonedPlanName = await commissionPage.cloneSimplePlan(
          "January Plan Deals",
          "Test",
          "simple",
          "Manager Pay Details",
          "manager payee Details",
          "email",
          "date of sales",
          "amount"
        );
        await commissionPage.navigation("/datasheet");
        const datasheet = new DatasheetV2Page(page);
        await datasheet.goToDatasheet("Deals", "Comm report");
        await datasheet.createDatasheet(
          "Deals",
          "Commission Report 2",
          "report",
          "Commission"
        );
        await datasheet.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await datasheet.clickSaveBtn();
        await datasheet.validateVariables([
          "Manager Pay Details:manager payee Details:amount",
          "Manager Pay Details:manager payee Details:commission percentage",
          "Manager Pay Details:manager payee Details:date of sales",
          "Manager Pay Details:manager payee Details:email",
          "Manager Pay Details:manager payee Details:id",
        ]);
        await datasheet.goToDatasheet("Deals", "Inter comm report");
        await datasheet.createDatasheet(
          "Deals",
          "Inter Commission Report 2",
          "report",
          "Inter Commission"
        );
        await datasheet.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await datasheet.clickSaveBtn();
        await datasheet.validateVariables([
          "Manager Pay Details:manager payee Details:amount",
          "Manager Pay Details:manager payee Details:commission percentage",
          "Manager Pay Details:manager payee Details:date of sales",
          "Manager Pay Details:manager payee Details:email",
          "Manager Pay Details:manager payee Details:id",
        ]);
        await commissionPage.navigation("/plans");
        await commissionPage.DeletePlan(clonedPlanName);
        await commissionPage.navigation("/datasheet");
        await datasheet.goToDatasheet("Deals", "Comm report");
        await datasheet.createDatasheet(
          "Deals",
          "Commission Report 3",
          "report",
          "Commission"
        );
        await datasheet.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await datasheet.clickSaveBtn();
        await datasheet.validateVariablesisNotPresent([
          "Manager Pay Details:manager payee Details:amount",
          "Manager Pay Details:manager payee Details:commission percentage",
          "Manager Pay Details:manager payee Details:date of sales",
          "Manager Pay Details:manager payee Details:email",
          "Manager Pay Details:manager payee Details:id",
        ]);
        await datasheet.goToDatasheet("Deals", "Inter comm report");
        await datasheet.createDatasheet(
          "Deals",
          "Inter Commission Report 3",
          "report",
          "Inter Commission"
        );
        await datasheet.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await datasheet.clickSaveBtn();
        await datasheet.validateVariablesisNotPresent([
          "Manager Pay Details:manager payee Details:amount",
          "Manager Pay Details:manager payee Details:commission percentage",
          "Manager Pay Details:manager payee Details:date of sales",
          "Manager Pay Details:manager payee Details:email",
          "Manager Pay Details:manager payee Details:id",
        ]);
      }
    );

    test(
      "Validate whether warning symbol is displayed adjacent to the variables when plan is deleted and on refresh data sheet Column should be null value in existing commission report",
      {
        annotation: [
          {
            type: "TestID",
            description: "INTER-T19080",
          },
          {
            type: "Description",
            description:
              "Validate whether warning symbol is displayed adjacent to the variables when plan is deleted and on refresh data sheet Column should be null value",
          },
          {
            type: "Expected Behaviour",
            description:
              "Warning symbol should be displayed adjacent to the variables when plan is deleted and datasheet column should be null value on refresh",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        testInfo.setTimeout(testInfo.timeout + 300000);
        const commissionPage = new CommissionRBAC(page);
        const commSync = new CommissionSync(page);
        const csPrev = new CommissionsSyncPrevPeriod(page);

        await commissionPage.navigation("/plans");
        await commissionPage.DeletePlan("January Plan Deals");

        await csPrev.navigate("/settings/commissions-and-data-sync");
        await commSync.selectCriteria("all-payees");
        await page.getByPlaceholder("Select month").click();
        await page.locator("button").first().click();
        await page.getByText("Jan").click();
        await csPrev.runCommissions();
        await csPrev.clickSkipandRun();
        await csPrev.waitForCalculationMessage();
        await csPrev.waitForCommissionsSuccess();

        await commissionPage.navigation("/datasheet");
        const datasheet = new DatasheetV2Page(page);
        await datasheet.goToDatasheet("Deals", "Comm report");
        // await datasheet.validateWarningSymbol([
        //   "Deals:Deals 2024:amount",
        //   "Deals:Deals 2024:bool",
        //   "Deals:Deals 2024:date of sale",
        //   "Deals:Deals 2024:email",
        //   "Deals:Deals 2024:id",
        // ]);
        // await datasheet.applyAdjustmentChanges();
        await datasheet.validateColumnValuesinDatasheet(
          "amount",
          ["", "", "", "", "", "", "", "", ""],
          "1"
        );
        await datasheet.validateColumnValuesinDatasheet(
          "dateofsale",
          ["", "", "", "", "", "", "", "", ""],
          "1"
        );
        await datasheet.validateColumnValuesinDatasheet(
          "bool",
          ["", "", "", "", "", "", "", "", ""],
          "1"
        );
        await datasheet.validateColumnValuesinDatasheet(
          "email",
          ["", "", "", "", "", "", "", "", ""],
          "1"
        );
        await datasheet.validateColumnValuesinDatasheet(
          "id",
          ["", "", "", "", "", "", "", "", ""],
          "1"
        );
      }
    );

    test(
      "Validate whether warning symbol is displayed adjacent to the variables when auto enrich variable is deleted from the original datasheet",
      {
        annotation: [
          {
            type: "TestID",
            description: "INTER-T19086",
          },
          {
            type: "Description",
            description:
              "Validate whether warning symbol is displayed adjacent to the variables when auto enrich variable is deleted from the original datasheet",
          },
          {
            type: "Expected Behaviour",
            description:
              "Warning symbol should be displayed adjacent to the variables auto enrich variable is deleted from the original datasheet",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        testInfo.setTimeout(testInfo.timeout + 120000);
        const commissionPage = new CommissionRBAC(page);
        await commissionPage.navigation("/datasheet");
        const datasheet = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(page);
        await datasheet.goToDatasheet("Manage Payee Details", "Payee Details");
        await datasheet.clickEditBtn();
        await dsV2EditPage.unCheckColumn("Details");
        await dsV2EditPage.saveEdits();
        await datasheet.fetchLatestData();
        await datasheet.goToDatasheet("Deals", "Comm report");
        await datasheet.validateWarningSymbol([
          "Manage Payee Details:Payee Details:Details",
        ]);
        const commSyncPage = new CommissionSync(page);
        const csPrevPage = new CommissionsSyncPrevPeriod(page);
        const commonPage = new CommonUtils(page);
        await commissionPage.navigation("/settings/commissions-and-data-sync");
        await commonPage.expandMenu("Report ETL Run ETL for report", "7");
        await commSyncPage.runETLReportforAllpayeesAndPeriod("Commission");
        await csPrevPage.runCommissions();
        await csPrevPage.clickSkipandRun();
        await csPrevPage.waitForReportETLSuccess();
        await commissionPage.navigation("/datasheet");
        await datasheet.goToDatasheet("Deals", "Comm report");
        await datasheet.applyAdjustmentChanges();
        await datasheet.validateColumnValuesinDatasheet(
          "details",
          ["", "", "", "", "", "", "", "", ""],
          "2"
        );
      }
    );
  }
);
