const {
  contractFixtures: { test, expect },
} = require("../../../../fixtures");

test.describe(
  "contracts ",
  { tag: ["@contracts", "@regression", "@settings", "@adminchamp-5"] },
  () => {
    test.beforeAll(async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      await page.goto("/contracts", {
        waitUntil: "networkidle",
      });
      testInfo.setTimeout(testInfo.timeout + 90000);
      await page
        .getByText(
          "Sorry, you are not connected to Docusign. Please connect from the Settings page"
        )
        .waitFor({ state: "visible", timeout: 70000 });
      // check whether the connection is there
      await expect(
        page.getByText(
          "Sorry, you are not connected to Docusign. Please connect from the Settings page"
        )
      ).toHaveCount(1);
      // Connecting to docusign
      await page.goto("/settings", { waitUntil: "networkidle" });
      await page
        .getByRole("link", {
          name: "Contracts Send, sign, and manage commission plan contracts seamlessly.",
        })
        .click();
      const page1Promise = page.waitForEvent("popup");
      await page.getByRole("button", { name: "Connect" }).click();
      const page1 = await page1Promise;
      const loginButton = await page1.getByText("Log in to DocuSign");
      await loginButton.waitFor({ state: "visible", timeout: 50000 });
      await expect(page1.getByText("Log in to DocuSign")).toHaveCount(1);
      await page1.getByPlaceholder("Enter email").fill("<EMAIL>");
      await page1.getByRole("button", { name: "NEXT" }).click();
      await page1.getByPlaceholder("Enter password").click();
      await page1.getByPlaceholder("Enter password").fill("Applegirl2!");
      const log_btn = await page1.getByRole("button", { name: "Log in" });
      await log_btn.waitFor({ state: "visible", timeout: 10000 });
      await page1.getByRole("button", { name: "Log in" }).click();
      await page1.waitForTimeout(10000);
      const ConnectButton1 = await page1.getByRole("button", {
        name: "Connected",
      });
      await ConnectButton1.waitFor({ state: "visible", timeout: 50000 });
      await page1.close();
    });

    test("Create contract", async ({ adminPage }, testInfo) => {
      //Create contracts
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 270000);
      await page.goto("/settings/contracts", { waitUntil: "networkidle" });
      await page.reload({ waitUntil: "networkidle" });
      await page.waitForTimeout(8000);
      const ConnectButton = await page.getByRole("button", {
        name: "Connected",
      });
      await ConnectButton.waitFor({ state: "visible", timeout: 50000 });
      await page.goto("/contracts", {
        waitUntil: "networkidle",
      });
      await page
        .getByText("Create Contract")
        .waitFor({ state: "visible", timeout: 70000 });
      await page.getByRole("button", { name: "Create Contract" }).click();
      await page
        .getByPlaceholder("Enter Contract Name")
        .fill("playwright_test_contract");
      await page
        .locator("span>input[type='file']")
        .setInputFiles("./upload-files/playwright_contract.pdf");
      await expect(page.getByText("playwright_contract.pdf")).toBeVisible();
      await page.getByPlaceholder("Select Year").click();
      await page.getByText("2023").click();
      await page.getByRole("textbox").nth(3).fill("Admin");
      await page.getByRole("button", { name: "Add Recipient" }).click();
      await page.getByRole("textbox").nth(4).fill("Payee");
      await page.getByText("Needs To Sign").nth(1).click();
      await page.getByTitle("Receives A Copy").first().click();
      await page.locator('input:above(:text("Reminders"))').first().click();
      await page.getByTitle("Payee").locator("div").first().click();
      await page.waitForTimeout(1000);
      await page.getByRole("button", { name: "Next" }).click();
      const createTemplate = page.getByText("Creating template...");
      await expect(createTemplate).toBeHidden();
      await expect(
        page.getByText("Created template successfully!")
      ).toBeVisible({
        timeout: 20000,
      });
      const sendButton = await page
        .frameLocator("iframe")
        .getByRole("button", { name: "Signature" });
      await sendButton.waitFor({ state: "visible", timeout: 40000 });
      await page
        .frameLocator("iframe")
        .getByRole("button", { name: "Signature" })
        .dragTo(page.frameLocator("iframe").locator("image"));
      const location = await page
        .frameLocator("iframe")
        .getByRole("button", { name: "Location" });
      await location.waitFor({ state: "visible", timeout: 30000 });
      await page
        .frameLocator("iframe")
        .getByRole("button", { name: "Location" })
        .click();
      await page.frameLocator("iframe").getByLabel("Pixels from Left").click();
      await page
        .frameLocator("iframe")
        .getByLabel("Pixels from Left")
        .press("Meta+a");
      await page
        .frameLocator("iframe")
        .getByLabel("Pixels from Left")
        .fill("100");
      await page.frameLocator("iframe").getByLabel("Pixels from Top").click();
      await page
        .frameLocator("iframe")
        .getByLabel("Pixels from Top")
        .press("Meta+a");
      await page
        .frameLocator("iframe")
        .getByLabel("Pixels from Top")
        .fill("100");
      await page.getByRole("button", { name: "Back" }).click();
      const imgElement2 = page.locator("g:nth-child(2) > g > path").first();
      await imgElement2.waitFor({ state: "hidden", timeout: 40000 });
      const lockalert = await page.getByText(
        "Template in lock! Please Save and Close before leaving this page to unlock the t"
      );
      await lockalert.waitFor({ state: "visible", timeout: 40000 });
      await expect(
        page.getByText(
          "Template in lock! Please Save and Close before leaving this page to unlock the t"
        )
      ).toBeVisible();
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("button", { name: "animation" }).nth(1).isHidden();
      const lockalert2 = await page.getByText(
        "Template in lock! Please Save and Close before leaving this page to unlock the t"
      );
      await lockalert2.waitFor({ state: "visible", timeout: 30000 });
      await expect(
        page.getByText(
          "Template in lock! Please Save and Close before leaving this page to unlock the t"
        )
      ).toBeVisible();
      await page.waitForTimeout(8000);
      await expect(
        page
          .frameLocator("iframe")
          .getByRole("button", { name: "Save and Close" })
          .last()
      ).toBeVisible();
      await page.frameLocator("iframe").getByRole(
        "button",
        { name: "Save and Close" },
        {
          waitUntil: "networkidle",
        }
      );
      const save = await page
        .frameLocator("iframe")
        .getByRole("button", { name: "Save and Close" })
        .last();
      await save.click();
      await page.frameLocator("iframe").getByText("Saving...").isHidden();
      // await page.getByRole('button', { name: 'Next' }).click();
      await page.waitForTimeout(2000);
      await page.getByRole("button", { name: "Next" }).click();
      await page.waitForTimeout(1000);
      await expect(page.getByRole("button", { name: "Admin" })).toBeVisible({
        timeout: 20000,
      });
      const adminFields = await page
        .locator("//span[text()='Admin']/../../../..//ul/li")
        .count();
      expect(adminFields).toBe(4);
      await page
        .getByRole("button", { name: "Create Contract" })
        .nth(1)
        .click();
      await expect(page.getByText("Create Contract complete")).toBeVisible({
        timeout: 5000,
      });
      await expect(
        page.getByText("Contract template created successfully!")
      ).toBeVisible({ timeout: 5000 });
      await page.getByRole("button", { name: "Close" }).click();
    });

    test("Send_envelopes", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 150000);
      await page.goto("/settings", { waitUntil: "networkidle" });
      await page.goto("/contracts", {
        waitUntil: "networkidle",
      });
      await page
        .getByText("Create Contract")
        .waitFor({ state: "visible", timeout: 60000 });
      const imgElement = page.locator("g:nth-child(2) > g > path").first();
      await imgElement.waitFor({ state: "hidden", timeout: 10000 });
      const dropbtn = await page
        .locator('span:near(:text("Fiscal Year"))')
        .locator("nth=1");
      await dropbtn.waitFor({ state: "visible", timeout: 30000 });
      // await page.locator("span.ant-select-arrow")
      await page
        .locator('span:near(:text("Fiscal Year"))')
        .locator("nth=1")
        .click();
      await page.getByTitle("2023").getByText("2023").click();
      await page.waitForSelector("input[placeholder='Search by']");
      await page.getByPlaceholder("Search by").fill("playwright_template");
      const template = await page.getByText("playwright_template").count();
      expect(template).toBe(1);
      await page.getByText("playwright_template").click();
      await page.getByRole("button", { name: "Send envelopes" }).click();
      await page.getByRole("button", { name: "animation" }).isHidden();
      await page.locator(".ant-select-selector").first().click();
      await page.getByText("contracts-book").click();
      await page
        .locator(
          "div:nth-child(2) > .relative > .ant-select > .ant-select-selector"
        )
        .click();
      await page.getByText("contract-template-sheet").click();
      await page.getByRole("button", { name: "Load Data" }).click();
      await page.getByRole(
        "button",
        { name: "animation" },
        {
          waitUntil: "networkidle",
        }
      );
      const noOfEnvelope = await page
        .locator("//span[text()='Envelopes']/..//div/div")
        .count();
      expect(noOfEnvelope).toBe(1);
      const textField = await page.getByRole("gridcell", {
        name: "subject-vennila Admin",
      });
      textField.dblclick();
      await page.getByRole("textbox", { name: "Input Editor" }).fill("");
      await page.getByRole("button", { name: "Validate" }).click();
      await expect(
        page.getByText("Errors detected. Fix them to proceed further")
      ).toBeVisible();
      await page.locator(".ag-cell > div").click();
      await expect(
        page.getByRole("tooltip", { name: "Mandatory Field - No Data" })
      ).toBeVisible();
      const editedText = await page.locator(".ag-cell > div");
      editedText.dblclick();
      await page
        .getByRole("textbox", { name: "Input Editor" })
        .fill("vennila-everstage-text");
      await page.getByRole("button", { name: "Validate" }).click();
      await expect(page.getByText("Validation Successful!")).toBeVisible();
      const sendEnvpButton = await page
        .getByRole("button", { name: "Send envelopes" })
        .nth(1);
      expect(sendEnvpButton.isDisabled()).toBeTruthy();
      await page
        .getByRole("checkbox", {
          name: "Press Space to toggle row selection (unchecked)",
        })
        .check();
      await sendEnvpButton.click();
      await expect(page.getByText("Envelope(s) queued").nth(1)).toBeVisible();
      await expect(
        page.getByText("1 envelope(s) queued successfully!")
      ).toBeVisible();
      await page.getByRole("button", { name: "Close" }).click();
      await page.waitForTimeout(2500);
      await Promise.all([
        page.waitForEvent("download", { timeout: 20000 }),
        await page.locator(".flex button").nth(-3).click(), // export button
      ]);
      const successMsg = await page.getByText(
        "Envelopes status exported successfully"
      );
      successMsg.waitFor({ state: "visible", timeout: 20000 });
    });
    test.afterAll(async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 150000);
      console.log("am in afterall hook");
      await page.goto("/users", { waitUntil: "networkidle" });
      await page.goto("/settings/contracts", { waitUntil: "networkidle" });
      await page.reload({ waitUntil: "networkidle" });
      const app = await page.getByText("Applications");
      await app.waitFor({ state: "visible", timeout: 50000 });
      await page.goto("/settings/contracts", { waitUntil: "networkidle" });
      await page.waitForTimeout(20000);
      const ConnectButton2 = await page.getByRole("button", {
        name: "Connected",
      });
      await ConnectButton2.waitFor({ state: "visible", timeout: 60000 });
      const page2Promise = page.waitForEvent("popup");
      await page.getByRole("button", { name: "Connected" }).click();
      const page2 = await page2Promise;
      await page2.waitForLoadState("networkidle");
      const connectbtn = await page2.getByRole("button", { name: "Connect" });
      await connectbtn.waitFor({ state: "visible", timeout: 70000 });
      console.log("am in afterall hook new page");
      await expect(page2.getByRole("button", { name: "Connect" })).toHaveCount(
        1
      );
      await page2.close();
    });
  }
);
