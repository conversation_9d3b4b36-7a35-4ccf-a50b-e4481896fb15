import CommissionsSyncPrevPeriod from "../../../../test-objects/commissionSyncPrevPeriod-objects";
import CommissionSync from "../../../../test-objects/commissionSync-objects";

const {
  payoutsBalaFixtures: { test, expect },
  payoutsPayeeFixtures3: { test: test3, expect: expect3 },
  payoutsBalaFixtures3: { test: test4, expect: expect4 },
  payoutsBalaFixtures2: { test: test2, expect: expect2 },
} = require("../../../fixtures");

test.describe(
  "Tracing Testcases with test",
  { tag: ["@payouts", "@regression", "@primelogic-5"] },
  () => {
    // Uses Payee 5, No Write
    test.describe("Trace and Full Trace Visible", async () => {
      test.beforeEach(async ({ adminPage }, testInfo) => {
        testInfo.setTimeout(testInfo.timeout + 1200000);
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTVAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ==",
          { waitUntil: "networkidle" }
        );
        await expect(page.getByText("Last")).toBeVisible({
          timeout: 60000,
        });
        await page.getByText("Tracing Plan").click();
      });

      test("Tier Sum Row", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Tier Sum Row" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Tier Sum Row")
          .getByTestId("pt-151054-trace-icon")
          .click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Tier Count Row", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Tier Count Row" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Tier Count Row")
          .getByTestId("pt-151054-trace-icon")
          .click();
        await page.getByRole("tab", { name: "Tier 0" }).click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Tier Sum Overall", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Tier Sum Overall" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Tier Sum Overall")
          .getByTestId("pt-151053-trace-icon")
          .click();
        await page.getByRole("tab", { name: "Tier 1" }).click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Tier Count Overall", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Tier Count Overall" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Tier Count Overall")
          .getByTestId("pt-151053-trace-icon")
          .click();
        await page.getByRole("tab", { name: "Tier 0" }).click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Tier Sum Conditional", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByRole("button", { name: "Tier Sum Conditional" })
          .click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Tier Sum Conditional")
          .getByTestId("pt-151053-trace-icon")
          .click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
        await page.getByLabel("Close").nth(1).click();
      });

      test("Quota Sum Row Att", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Quota Sum Row Att" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Quota Sum Row Att")
          .getByTestId("pt-151054-trace-icon")
          .click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Quota Sum Row Value", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Quota Sum Row Value" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Quota Sum Row Value")
          .getByTestId("pt-151054-trace-icon")
          .click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Quota Sum Overall Att", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByRole("button", { name: "Quota Sum Overall Att" })
          .click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Quota Sum Overall Att")
          .getByTestId("pt-151053-trace-icon")
          .click();
        await page.getByRole("tab", { name: "Tier 1" }).click();
      });

      test("Quota Sum Overall Value", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByRole("button", { name: "Quota Sum Overall Value" })
          .click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Quota Sum Overall Value")
          .getByTestId("pt-151053-trace-icon")
          .click();
        await page.getByRole("tab", { name: "Tier 1" }).click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Quota Count Row Att", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Quota Count Row Att" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Quota Count Row Att")
          .getByTestId("pt-151053-trace-icon")
          .click();
        await page.getByRole("tab", { name: "Tier 0" }).click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Quota Count Row Value", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByRole("button", { name: "Quota Count Row Value" })
          .click();
        await page
          .getByLabel("Quota Count Row Value")
          .getByTestId("pt-151053-trace-icon")
          .click();
        await page.getByRole("tab", { name: "Tier 0" }).click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Quota Count Overall Att", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByRole("button", { name: "Quota Count Overall Att" })
          .click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Quota Count Overall Att")
          .getByTestId("pt-151053-trace-icon")
          .click();
        await page.getByRole("tab", { name: "Tier 0" }).click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Quota Count Overall Value", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByRole("button", { name: "Quota Count Overall Value" })
          .click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Quota Count Overall Value")
          .getByTestId("pt-151053-trace-icon")
          .click();
        await page.getByRole("tab", { name: "Tier 0" }).click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Quota Sum Conditional", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByRole("button", { name: "Quota Sum Conditional" })
          .click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Quota Sum Conditional")
          .getByTestId("pt-151053-trace-icon")
          .click();
        await page.getByRole("tab", { name: "Tier 1" }).click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Team PandT Simple", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Team PandT Simple" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Team PandT Simple")
          .getByTestId("pt-151064-trace-icon")
          .click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Team Payee Simple", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Team Payee Simple" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Team Payee Simple")
          .getByTestId("pt-151064-trace-icon")
          .click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Team PandT Cond", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Team PandT Cond" }).click();
        await page.waitForLoadState("networkidle");
        page
          .getByLabel("Team PandT Cond")
          .getByTestId("pt-151063-trace-icon")
          .click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Team Payee Cond", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Team Payee Cond" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Team Payee Cond")
          .getByTestId("pt-151064-trace-icon")
          .click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Team PandT Tier", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Team PandT Tier" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Team PandT Tier")
          .getByTestId("pt-151064-trace-icon")
          .click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Team Payee Tier", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Team Payee Tier" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Team Payee Tier")
          .getByTestId("pt-151064-trace-icon")
          .click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Team PandT Quota", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Team PandT Quota" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Team PandT Quota")
          .getByTestId("pt-151064-trace-icon")
          .click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });

      test("Team Payee Quota", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Team Payee Quota" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Team Payee Quota")
          .getByTestId("pt-151064-trace-icon")
          .click();
        await expect(page.getByText("Show full trace")).toBeVisible({
          timeout: 45000,
        });
      });
    });

    test("Trace Toggle Visible", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=333180bd-aace-492d-b745-ca20676f4b80",
        {
          waitUntil: "networkidle",
        }
      );
      // Simple;
      await page.locator(".relative > .p-4").first().click();
      await page.waitForLoadState("networkidle");
      await page.getByRole("button", { name: "Configuration" }).click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator(
          "//span[text()='Data source']/ancestor::div[4]/preceding-sibling::button"
        )
        .first()
        .click();

      // Conditional
      await page.locator("div:nth-child(2) > .relative > .p-4").click();
      await page.waitForLoadState("networkidle");
      await page.getByRole("button", { name: "Configuration" }).click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator(
          "//span[text()='Data source']/ancestor::div[4]/preceding-sibling::button"
        )
        .first()
        .click();
      await page.waitForLoadState("networkidle");

      // Tier
      await page.locator("div:nth-child(3) > .relative > .p-4").click();
      await page.waitForLoadState("networkidle");
      await page.getByRole("button", { name: "Configuration" }).click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator(
          "//span[text()='Data source']/ancestor::div[4]/preceding-sibling::button"
        )
        .first()
        .click();
      await page.waitForLoadState("networkidle");

      // Team
      await page.locator("div:nth-child(5) > .relative > .p-4").click();
      await page.waitForLoadState("networkidle");
      await page.getByRole("button", { name: "Configuration" }).click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator(
          "//span[text()='Data source']/ancestor::div[4]/preceding-sibling::button"
        )
        .first()
        .click();
      await page.waitForLoadState("networkidle");

      // Quota
      await page.locator("div:nth-child(4) > .relative > .p-4").click();
      await page.waitForLoadState("networkidle");
      await page.getByRole("button", { name: "Configuration" }).click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator(
          "//span[text()='Data source']/ancestor::div[4]/preceding-sibling::button"
        )
        .first()
        .click();
      await page.waitForLoadState("networkidle");
    });

    // No Write
    test("Spiff Trace Toggle Visible", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/plans?plan_id=d5d6c74d-815f-4f5a-b570-c65d3902664b",
        {
          waitUntil: "networkidle",
        }
      );
      // Simple;
      await page.locator(".relative > .p-4").first().click();
      await page.getByRole("button", { name: "Configuration" }).click();
      await expect(page.getByText("Show commission trace")).toBeVisible();
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page
        .locator(
          "//span[text()='Data source']/ancestor::div[4]/preceding-sibling::button"
        )
        .first()
        .click();

      // Conditional
      await page.locator("div:nth-child(2) > .relative > .p-4").click();
      await page.getByRole("button", { name: "Configuration" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page
        .locator(
          "//span[text()='Data source']/ancestor::div[4]/preceding-sibling::button"
        )
        .first()
        .click();

      // Tier
      await page.locator("div:nth-child(3) > .relative > .p-4").click();
      await page.getByRole("button", { name: "Configuration" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page
        .locator(
          "//span[text()='Data source']/ancestor::div[4]/preceding-sibling::button"
        )
        .first()
        .click();

      // Team
      await page.locator("div:nth-child(5) > .relative > .p-4").click();
      await page.getByRole("button", { name: "Configuration" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page
        .locator(
          "//span[text()='Data source']/ancestor::div[4]/preceding-sibling::button"
        )
        .first()
        .click();

      // Quota
      await page.locator("div:nth-child(4) > .relative > .p-4").click();
      await page.getByRole("button", { name: "Configuration" }).click();
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page.waitForLoadState("networkidle");
      await page
        .locator("div")
        .filter({ hasText: /^Show commission trace$/ })
        .getByRole("switch")
        .click();
      await page
        .locator(
          "//span[text()='Data source']/ancestor::div[4]/preceding-sibling::button"
        )
        .first()
        .click();
    });

    // Uses Payee 5, No write
    test("Hidden Quota Trace", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTVAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ==",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.getByText("Quota Hidden Trace").click();
      await expect(
        page.getByRole("row", { name: "Quota ₹" }).locator("svg")
      ).toBeVisible();
      await page.getByRole("button", { name: "Quota" }).click();
      await expect(page.getByTestId("pt-151054-trace-icon")).toBeVisible();
      await page.getByTestId("pt-151054-trace-icon").click();
      await page.getByRole("tab", { name: "Tier" }).click();
      await expect(page.getByText("Show full trace")).toBeVisible({
        timeout: 45000,
      });
    });

    // Uses Payee 5, No write
    test("Inner Trace", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTVAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ==",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.getByText("Tracing Plan").click();
      await page.getByRole("button", { name: "Quota Sum Row Att" }).click();
      await page.waitForLoadState("networkidle");
      await page.getByTestId("pt-151054-trace-icon").click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByLabel("Quota Erosion")).toContainText(
        "2,000amount₹0.00Monthly Variable Pay10"
      );
      await page.getByRole("switch").click();
      await expect(
        page.getByText("₹0.00Annual Variable Pay12₹0.00")
      ).toBeVisible();
    });

    // Uses Payee5
    test.describe("Conditional Zero, Do Nothing, Nested", () => {
      test.beforeEach(async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTVAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ==",
          { waitUntil: "networkidle" }
        );
        await expect(page.getByText("Last")).toBeVisible({
          timeout: 60000,
        });
        await page.getByText("Conditional Trace").click();
      });

      test("Nested Conditional Zero", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Conditional1" }).click();
        await page.getByTestId("pt-151054-trace-icon").click();
        await page.getByText("Show all").click();
        await expect(
          page.getByLabel("Condition(s)").getByRole("document")
        ).toContainText("Since amount(2,000) is greater than 5,000");
        await expect(
          page.getByLabel("Condition(s)").getByRole("document")
        ).toContainText("Since amount(2,000) is greater than 4,000");
      });

      test("Conditional Do Nothing", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Conditional2" }).click();
        await page.getByTestId("pt-151054-trace-icon").click();
        await page.getByLabel("Close").nth(1).click();
        await expect(page.getByTestId("pt-151054-trace-icon")).toBeVisible();
        await expect(page.getByTestId("pt-151053-trace-icon")).toBeVisible();
        await expect(page.getByTestId("pt-151055-trace-icon")).toBeVisible();
      });
    });

    // Uses Payee 5
    test("Trace Expandable Function", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTVAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ==",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.getByText("Trace Expandable Function").click();
      await page.getByRole("button", { name: "Quota" }).click();
      await page.getByTestId("pt-151054-trace-icon").click();

      const toggle = page.getByRole("switch");
      const isOff = (await toggle.getAttribute("aria-checked")) === "false";
      expect(isOff).toBe(true);

      await page.getByText("Monthly Variable Pay").first().click();
      await page.getByText("Monthly Variable Pay").nth(1).click();
      await expect(page.getByText("Annual Variable Pay")).toBeVisible();
      await expect(page.getByText("Annual Variable Pay").nth(1)).toBeHidden();
      const isOn = (await toggle.getAttribute("aria-checked")) === "true";
      expect(isOn).toBe(true);
    });

    // Uses Payee 5
    test.describe("Tier Value Percentage Label", () => {
      test.beforeEach(async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTVAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ==",
          { waitUntil: "networkidle" }
        );
        await expect(page.getByText("Last")).toBeVisible({
          timeout: 60000,
        });
        await page.getByText("Tier Label Plan").click();
      });

      test("Tier Value", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Tier", exact: true }).click();
        await page.getByTestId("pt-151054-trace-icon").click();
        await page.getByRole("tab", { name: "Tier 1" }).click();
        await expect(page.getByLabel("Tier 1")).toContainText(
          "2,000Tier value"
        );
        await page.getByLabel("Close").nth(1).click();
        await page.getByTestId("pt-151053-trace-icon").first().click();
        await page.getByRole("tab", { name: "Tier 0" }).click();
        await expect(page.getByLabel("Tier 0")).toContainText(
          "1,000Tier value"
        );
        await page.getByRole("tab", { name: "Tier 1" }).click();
        await expect(page.getByLabel("Tier 1")).toContainText(
          "2,000Tier value"
        );
        await expect(page.locator("body")).toContainText(
          "Your commission calculated for this deal falls under multiple tiers. Trace respective commissions below."
        );
      });

      test("Tier Percentage", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Tier2" }).click();
        await page.getByTestId("pt-151054-trace-icon").click();
        await page.getByRole("tab", { name: "Tier 1" }).click();
        await page.getByRole("switch").click();
        await expect(page.getByText("Value")).toBeVisible();
        await expect(page.getByLabel("Tier 1")).toContainText(
          "2,000Quota Erosion"
        );
        await page.getByLabel("Close").nth(1).click();
        await page.getByTestId("pt-151053-trace-icon").first().click();
        await page.getByRole("tab", { name: "Tier 0" }).click();
        await page.getByRole("switch").click();
        await expect(page.getByText("Value")).toBeVisible();
        await expect(page.getByLabel("Tier 0")).toContainText(
          "2,000Quota Erosion"
        );
        await page.getByRole("tab", { name: "Tier 1" }).click();
        await page.getByRole("switch").click();
        await expect(
          page.getByLabel("Tier 1").getByText("Value")
        ).toBeVisible();
        await expect(page.getByLabel("Tier 1")).toContainText(
          "1,000Quota Erosion"
        );
        await expect(page.locator("body")).toContainText(
          "Your commission calculated for this deal falls under multiple tiers. Trace respective commissions below."
        );
      });
    });

    // Uses Payee6
    test("Settlement Commission Variable", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTZAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ==",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.getByText("Commission Summary").click();
      await page.getByText("Earned Commissions").click();
      await page.getByText("Settlement Commission Plan").click();
      await page.getByRole("button", { name: "Simple" }).click();
      await expect(page.getByText("Customize component columns")).toBeVisible();
      await expect(page.getByText("id", { exact: true })).toBeVisible();
      await expect(page.getByText("amount")).toBeVisible();
      await expect(page.getByText("email")).toBeVisible();
      await expect(
        page.getByLabel("Simple").getByText("Commission")
      ).toBeVisible();
      await page.getByTestId("pt-151063-trace-icon").click();
      await expect(page.locator("body")).toContainText("5,000");
    });

    // Commission sync run in serial
    test.describe("Sync Serial", () => {
      // Uses write on Payee4 January
      test("Clone Trace and Detailed View", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTRAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMS0wMSIsInBlZCI6IjIwMjQtMDEtMzEifQ==",
          { waitUntil: "networkidle" }
        );
        await expect(page.getByText("Last")).toBeVisible({
          timeout: 60000,
        });
        await expect(page.getByText("payee4 payouts")).toBeVisible({
          timeout: 90000,
        });
        await expect(
          page.getByText("Total Payout₹38,400.00").first()
        ).toBeVisible();

        // Trace Visible
        await page.getByText("Simple Clone").click();
        await page.getByRole("button", { name: "Simple" }).click();
        await expect(page.getByTestId("pt-151033-trace-icon")).toBeVisible();
        await expect(page.getByTestId("pt-151032-trace-icon")).toBeVisible();
        await expect(page.getByTestId("pt-151034-trace-icon")).toBeVisible();

        // Commission and Detailed View
        await expect(page.locator("body")).toContainText(
          "Plan level commission₹16,200.00"
        );
        await expect(
          page.getByRole("gridcell", { name: "Total" })
        ).toBeVisible();
        await expect(page.getByLabel("Close")).toBeVisible();
        await expect(
          page
            .locator("div")
            .filter({ hasText: /^Customize component columns$/ })
            .nth(1)
        ).toBeVisible();
      });

      // Delete Before Sync
      test("Delete Plan", async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        testInfo.setTimeout(testInfo.timeout + 1200000);
        const csPrev = new CommissionsSyncPrevPeriod(page);
        const commSync = new CommissionSync(page);

        await csPrev.navigate("/settings/commissions-and-data-sync");

        await commSync.selectCriteria("selected-payees");
        await commSync.selectDropdown(["payee4 payouts"]);

        await csPrev.selectDate("31 Jan 2024");
        await csPrev.runCommissions();
        await csPrev.clickSkipandRun();
        await csPrev.waitForCalculationMessage();
        await csPrev.waitForCommissionsSuccess();
      });

      test("Check after Delete Plan", async ({ adminPage }) => {
        const page = adminPage.page;
        const csPrev = new CommissionsSyncPrevPeriod(page);

        await csPrev.navigate(
          "/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTRAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMS0wMSIsInBlZCI6IjIwMjQtMDEtMzEifQ=="
        );

        await expect(page.getByText("Last")).toBeVisible({
          timeout: 60000,
        });
        await page.waitForLoadState("networkidle");
        await expect(page.getByTestId("login-indicator")).toContainText(
          "Total Payout₹22,200.00"
        );
      });
      // Uses Payee7 Has Write
    });

    // Uses Payee 8 February
    test.describe("Quota Tier", () => {
      test.beforeEach(async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZThAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ==",
          { waitUntil: "networkidle" }
        );
        await expect(page.getByText("Last")).toBeVisible({
          timeout: 60000,
        });
        await page.getByText("Tier and Quota Multi").click();
      });

      test("Quota Tiers", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Quota" }).click();
        await page.getByTestId("pt-151078-trace-icon").first().click();
        await expect(
          page.getByRole("tab", { name: "Quota Erosion" })
        ).toBeVisible();
        await page.getByRole("tab", { name: "Quota Erosion" }).click();
        await expect(
          page.getByRole("cell", { name: "Range based on quota" })
        ).toBeVisible();
        await expect(
          page.getByRole("cell", { name: "Cumulative quota attainment" })
        ).toBeVisible();
        await expect(
          page.getByRole("cell", { name: "Cumulative quota erosion" })
        ).toBeVisible();

        await expect(
          page.getByRole("cell", { name: "Upto 100%" })
        ).toBeVisible();
        await expect(
          page.getByRole("cell", { name: "% to 200%" })
        ).toBeVisible();
        await expect(
          page.getByRole("cell", { name: "100%", exact: true })
        ).toBeVisible();
        await expect(
          page.getByRole("cell", { name: "200%", exact: true })
        ).toBeVisible();
        await expect(page.getByRole("cell", { name: "1,000" })).toBeVisible();
        await expect(page.getByRole("cell", { name: "2,000" })).toBeVisible();
        await expect(
          page.getByRole("button", { name: "Tier 0" })
        ).toBeVisible();
        await page.getByRole("button", { name: "Tier 0" }).click();
        await expect(
          page.getByLabel("Tier 0").getByText("Show full trace")
        ).toBeVisible();
        await expect(page.getByLabel("Tier 0")).toContainText(
          "600%Quota attainment"
        );
        await expect(page.getByLabel("Tier 0")).toContainText(
          "Tier based on quota attainment %Tier 0 (Upto 100%)"
        );
        await expect(page.getByLabel("Tier 0")).toContainText(
          "Quota erosion1000"
        );
        await page.getByRole("tab", { name: "Quota Erosion" }).click();
        await expect(
          page.getByRole("button", { name: "Tier 1" })
        ).toBeVisible();

        await page.getByRole("button", { name: "Tier 1" }).click();
        await expect(page.getByLabel("Tier 1")).toContainText(
          "Tier based on quota attainment %Tier 1 (100% to 200%)"
        );
        await expect(page.getByLabel("Tier 1")).toContainText(
          "Quota erosion1000"
        );
        // await expect(page.getByLabel("Tier 1")).toContainText("€20.00");
        await expect(
          page
            .getByLabel("Tier 1")
            .getByText("Show full trace100%Tier percentage20")
        ).toBeVisible();
        await expect(
          page.getByText(
            "Your commission calculated for this deal falls under multiple tiers. Trace respective commissions below."
          )
        ).toBeVisible();
        // await expect(page.locator("body")).toContainText("€36.00");
      });

      test("Tier Split Ups", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Tier" }).click();
        await page.getByTestId("pt-151078-trace-icon").first().click();
        await page.getByRole("tab", { name: "Tier Value" }).click();
        await expect(
          page.getByText(
            "Your commission calculated for this deal falls under multiple tiers. Trace respective commissions below."
          )
        ).toBeVisible();
        await expect(
          page.getByRole("button", { name: "Tier 0" })
        ).toBeVisible();
        await expect(
          page.getByRole("button", { name: "Tier 1" })
        ).toBeVisible();
        await expect(
          page.getByRole("button", { name: "Tier 2" })
        ).toBeVisible();
        await expect(
          page.getByRole("cell", { name: "Range based on tier value" })
        ).toBeVisible();
        await expect(page.getByRole("cell", { name: "Upto" })).toBeVisible();
        await expect(page.getByRole("cell", { name: "to 1500" })).toBeVisible();
        await expect(page.getByRole("cell", { name: "Above" })).toBeVisible();
        await expect(
          page.getByRole("cell", { name: "Tier value", exact: true })
        ).toBeVisible();
        await expect(page.getByRole("cell", { name: "1,000" })).toBeVisible();
        await expect(
          page.getByRole("cell", { name: "500" }).nth(1)
        ).toBeVisible();
        await expect(
          page.getByRole("cell", { name: "500" }).nth(3)
        ).toBeVisible();
        await page.getByRole("button", { name: "Tier 0" }).click();
        await expect(page.getByLabel("Tier 0")).toContainText(
          "TierTier 0 (Upto 1000)"
        );
        await expect(page.getByLabel("Tier 0")).toContainText("Tier Value1000");
        await page.getByRole("tab", { name: "Tier Value" }).click();
        await page.getByRole("button", { name: "Tier 1" }).click();
        await expect(page.getByLabel("Tier 1")).toContainText(
          "TierTier 1 (1000 to 1500)"
        );
        await expect(page.getByLabel("Tier 1")).toContainText("Tier Value500");
        await page.getByRole("tab", { name: "Tier Value" }).click();
        await page.getByRole("button", { name: "Tier 2" }).click();
        await expect(page.getByLabel("Tier 2")).toContainText(
          "TierTier 2 (Above 1500)"
        );
        await expect(page.getByLabel("Tier 2")).toContainText("Tier Value500");
      });
    });

    // Use Payee 9 March
    test.describe("Team Functions Tooltip", () => {
      test.beforeEach(async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTlAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMy0wMSIsInBlZCI6IjIwMjQtMDMtMzEifQ==",
          { waitUntil: "networkidle" }
        );
        await expect(page.getByText("payee9 payouts")).toBeVisible({
          timeout: 60000,
        });
        await page.getByText("Team Functions").click();
      });

      // Simple
      test("Team Functions1", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Simple" }).click();
        await page.waitForLoadState("networkidle");
        await page.getByTestId("pt-151080-trace-icon").click();
        await page.waitForLoadState("networkidle");
        await page.getByText("5,000", { exact: true }).click();
        await expect(
          page
            .getByRole("tooltip")
            .getByText("Sum of amount since amount is greater than 1000")
        ).toBeVisible();

        await page.locator("div").filter({ hasText: /^2$/ }).nth(2).click();
        await expect(
          page
            .getByRole("tooltip")
            .getByText("Count of amount since amount is greater than 1000")
        ).toBeVisible();

        await page.getByText("3", { exact: true }).nth(1).click();
        await expect(
          page.getByRole("tooltip").getByText("Count of amount not null")
        ).toBeVisible();

        await page.getByText("1,000", { exact: true }).click();
        await expect(
          page.getByRole("tooltip").getByText("Minimum of amount")
        ).toBeVisible();

        await page.getByText("3,000", { exact: true }).click();
        await expect(
          page.getByRole("tooltip").getByText("Maximum of amount")
        ).toBeVisible();

        await page.getByText("2,000", { exact: true }).click();
        await expect(
          page.getByRole("tooltip").getByText("Average of amount")
        ).toBeVisible();

        await page.getByText("3", { exact: true }).nth(2).click();
        await expect(
          page.getByRole("tooltip").getByText("Distinct count of amount")
        ).toBeVisible();
      });

      // Quota
      test("Team Functions2", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Quota" }).click();
        await page.waitForLoadState("networkidle");
        await page.getByTestId("pt-151080-trace-icon").click();
        await page.waitForLoadState("networkidle");
        await page.getByRole("tab", { name: "Quota Erosion" }).click();
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Quota Erosion")
          .getByText("Quota erosion")
          .click();
        await expect(
          page
            .getByRole("tooltip")
            .getByText("Primary Quota for March 2024")
            .first()
        ).toBeVisible();
        await page.getByText("Quota attainment", { exact: true }).click();
        await expect(
          page
            .getByRole("tooltip")
            .getByText("Primary Quota for March 2024")
            .first()
        ).toBeVisible();
        await page.getByRole("tab", { name: "Tier" }).click();
        await page.waitForLoadState("networkidle");
        await page.getByText("Team-Quota erosion").click();
        await page.waitForLoadState("networkidle");
        await expect(
          page
            .getByRole("tooltip")
            .getByText("Primary Quota for March 2024 for the team")
        ).toBeVisible();
      });
    });

    // Use Payee 9 March
    test.describe("Do Nothing and Left Tab Hidden", () => {
      test.beforeEach(async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTlAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMy0wMSIsInBlZCI6IjIwMjQtMDMtMzEifQ==",
          { waitUntil: "networkidle" }
        );
        await expect(page.getByText("Last")).toBeVisible({
          timeout: 60000,
        });
        await page.getByText("Do Nothing Team").click();
      });

      // Do Nothing
      test("Do Nothing Team", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Tier", exact: true }).click();
        await page.waitForLoadState("networkidle");
        await expect(page.getByTestId("pt-151091-trace-icon")).toBeVisible();
        // await page.getByLabel("Close").click();
        await page.getByRole("tab", { name: "Quota", exact: true }).click();
        await page.waitForLoadState("networkidle");
        await expect(
          page
            .getByLabel("Quota", { exact: true })
            .getByTestId("pt-151091-trace-icon")
        ).toBeVisible();
        // await page.getByLabel("Close").click();
        await page.getByRole("tab", { name: "Conditional" }).click();
        await page.waitForLoadState("networkidle");
        await expect(
          page.getByLabel("Conditional").getByTestId("pt-151082-trace-icon")
        ).toBeVisible();
        await expect(
          page.getByLabel("Conditional").getByTestId("pt-151091-trace-icon")
        ).toBeVisible();
        await page.getByLabel("Close").click();
      });

      // Left Tab
      test("Left OT", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByRole("button", { name: "Overall Tier", exact: true })
          .click();
        await page.getByTestId("pt-151089-trace-icon").click();
        await page.waitForLoadState("networkidle");
        await expect(
          page.locator("span").filter({ hasText: /^id$/ })
        ).toBeHidden();
        await expect(
          page.locator("span").filter({ hasText: "Tier Name" })
        ).toBeHidden();
      });

      test("Left OQ", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Overall Quota" }).click();
        await page.getByTestId("pt-151089-trace-icon").click();
        await page.waitForLoadState("networkidle");
        await expect(
          page.locator("span").filter({ hasText: /^id$/ })
        ).toBeHidden();
        await expect(
          page.locator("span").filter({ hasText: "Tier Name" })
        ).toBeHidden();
      });

      test("Left TOT", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Team Overall Tier" }).click();
        await page.getByTestId("pt-151080-trace-icon").click();
        await page.waitForLoadState("networkidle");
        await expect(
          page.locator("span").filter({ hasText: /^id$/ })
        ).toBeHidden();
        await expect(
          page.locator("span").filter({ hasText: "Tier Name" })
        ).toBeHidden();
      });
    });

    // Uses payee 10 January
    test.describe("Functions and Constants", () => {
      test.beforeEach(async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTEwQHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDEtMDEiLCJwZWQiOiIyMDI0LTAxLTMxIn0=",
          { waitUntil: "networkidle" }
        );
        await expect(page.getByText("Last")).toBeVisible({
          timeout: 60000,
        });
        await page.getByText("Functions and Constants_Copy").click();
      });

      test("Quota Function", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Quota", exact: true }).click();
        await page.getByTestId("pt-151093-trace-icon").click();
        await page.getByRole("tab", { name: "Tier 1" }).click();
        await page.getByLabel("Tier 1").getByText("6,000").click();
        await expect(
          page.getByRole("tooltip", { name: "Sum of amount" })
        ).toBeVisible();
        await page.getByLabel("Tier 1").getByText("5,000").click();
        await expect(
          page.getByRole("tooltip", { name: "Sum of amount since amount is" })
        ).toBeVisible();
        await page.getByLabel("Tier 1").getByText("3,000").click();
        await expect(
          page.getByRole("tooltip", { name: "Maximum of amount" })
        ).toBeVisible();
        await page
          .getByLabel("Tier 1")
          .getByText("1,000", { exact: true })
          .click();
        await expect(
          page.getByRole("tooltip", { name: "Minimum of amount" })
        ).toBeVisible();
        await page.getByLabel("Tier 1").getByText("2,000").first().click();
        await expect(
          page.getByRole("tooltip", { name: "Average of amount" })
        ).toBeVisible();
        await page.getByLabel("Tier 1").getByText("2", { exact: true }).click();
        await expect(
          page.getByRole("tooltip", { name: "Count of amount since amount" })
        ).toBeVisible();
        await page
          .getByLabel("Tier 1")
          .getByText("3", { exact: true })
          .first()
          .click();
        await expect(
          page.getByRole("tooltip", { name: "Count of amount not null" })
        ).toBeVisible();
        await page
          .getByLabel("Tier 1")
          .getByText("3", { exact: true })
          .nth(1)
          .click();
        await expect(
          page.getByRole("tooltip", { name: "Distinct count of amount" })
        ).toBeVisible();
        await page.getByLabel("Tier 1").getByText("Show all").click();
        await expect(
          page.getByLabel("Condition(s)").getByRole("document")
        ).toContainText(
          'Since 3 is IN (1, 2, 3) OR Concat ("", [string1, string2])(string1string2) is equal to string1string2 OR CurrentPayoutPeriod (Month)(1) is less than 3 OR GetDate (start, Month, Fiscal)(1) is IN GetDate (start, Month, Fiscal)(1) OR email contains "@"(True) OR email does not contain "@"(False) OR IsEmpty (email)(False) OR IsNotEmpty (email)(True) OR Lowercase (email)(<EMAIL>) is IN Lowercase (email)(<EMAIL>) OR PositionOf ("@" in email)(8) is greater than 2 OR dynamic(10) is IN dynamic(10) AND True'
        );
      });

      // Tier
      test("Tier Function", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Tier", exact: true }).click();
        await page.getByTestId("pt-151093-trace-icon").click();
        await page.getByRole("tab", { name: "Tier Value" }).click();
        await page.getByLabel("Tier Value").getByText("6,000").click();
        await expect(
          page.getByRole("tooltip", { name: "Sum of amount" })
        ).toBeVisible();
        await page.getByLabel("Tier Value").getByText("5,000").click();
        await expect(
          page.getByRole("tooltip", { name: "Sum of amount since amount is" })
        ).toBeVisible();
        await page.getByLabel("Tier Value").getByText("3,000").click();
        await expect(
          page.getByRole("tooltip", { name: "Maximum of amount" })
        ).toBeVisible();
        await page
          .getByLabel("Tier Value")
          .getByText("1,000", { exact: true })
          .click();
        await expect(
          page.getByRole("tooltip", { name: "Minimum of amount" })
        ).toBeVisible();
        await page.getByLabel("Tier Value").getByText("2,000").click();
        await expect(
          page.getByRole("tooltip", { name: "Average of amount" })
        ).toBeVisible();
        await page
          .getByLabel("Tier Value")
          .getByText("2", { exact: true })
          .click();
        await expect(
          page.getByRole("tooltip", { name: "Count of amount since amount" })
        ).toBeVisible();
        await page
          .getByLabel("Tier Value")
          .getByText("3", { exact: true })
          .first()
          .click();
        await expect(
          page.getByRole("tooltip", { name: "Count of amount not null" })
        ).toBeVisible();
        await page
          .getByLabel("Tier Value")
          .getByText("3", { exact: true })
          .nth(1)
          .click();
        await expect(
          page.getByRole("tooltip", { name: "Distinct count of amount" })
        ).toBeVisible();
        await page.getByRole("tab", { name: "Tier 1" }).click();
        await page.getByText("Show all").click();
        await expect(
          page.getByLabel("Condition(s)").getByRole("document")
        ).toContainText(
          'Since 3 is IN (1, 2, 3) OR Concat ("", [string1, string2])(string1string2) is equal to string1string2 OR CurrentPayoutPeriod (Month)(1) is less than 3 OR GetDate (start, Month, Fiscal)(1) is IN GetDate (start, Month, Fiscal)(1) OR email contains "@"(True) OR email does not contain "@"(False) OR IsEmpty (email)(False) OR IsNotEmpty (email)(True) OR Lowercase (email)(<EMAIL>) is IN Lowercase (email)(<EMAIL>) OR PositionOf ("@" in email)(8) is greater than 2 OR dynamic(10) is IN dynamic(10) AND True'
        );
      });

      // Team Tier
      test("Team Tier Function", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Team Tier" }).click();
        await page.getByTestId("pt-151103-trace-icon").click();
        await page.getByText("12,000").click();
        await expect(
          page.getByRole("tooltip", { name: "Sum of amount" })
        ).toBeVisible();
        await page.getByText("10,000").click();
        await expect(
          page.getByRole("tooltip", { name: "Sum of amount since amount is" })
        ).toBeVisible();
        await page.getByText("3,000").click();
        await expect(
          page.getByRole("tooltip", { name: "Maximum of amount" })
        ).toBeVisible();
        await page.getByText("1,000", { exact: true }).click();
        await expect(
          page.getByRole("tooltip", { name: "Minimum of amount" })
        ).toBeVisible();
        await page.getByText("2,000").nth(1).click();
        await expect(
          page.getByRole("tooltip", { name: "Average of amount" })
        ).toBeVisible();
        await page.getByLabel("Tier Value").getByText("4").click();
        await expect(
          page.getByRole("tooltip", { name: "Count of amount since amount" })
        ).toBeVisible();
        await page.getByLabel("Tier Value").getByText("6").click();
        await expect(
          page.getByRole("tooltip", { name: "Count of amount not null" })
        ).toBeVisible();
        await page
          .getByLabel("Tier Value")
          .getByText("3", { exact: true })
          .click();
        await expect(
          page.getByRole("tooltip", { name: "Distinct count of amount" })
        ).toBeVisible();
        await page.getByText("Show all").click();
        await expect(
          page.getByLabel("Condition(s)").getByRole("document")
        ).toContainText(
          'Since 3 is IN (1, 2, 3) OR Concat ("", [string1, string2])(string1string2) is equal to string1string2 OR CurrentPayoutPeriod (Month)(1) is less than 3 OR GetDate (start, Month, Fiscal)(1) is IN GetDate (start, Month, Fiscal)(1) OR email contains "@"(True) OR email does not contain "@"(False) OR IsEmpty (email)(False) OR IsNotEmpty (email)(True) OR Lowercase (email)(<EMAIL>) is IN Lowercase (email)(<EMAIL>) OR PositionOf ("@" in email)(8) is greater than 2 OR dynamic(10) is IN dynamic(10) AND True'
        );
      });

      // Team Quota
      test("Team Quota Function", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.getByRole("button", { name: "Team Quota" }).click();
        await page.getByTestId("pt-151103-trace-icon").click();
        await page.getByText("12,000").click();
        await expect(
          page.getByRole("tooltip", { name: "Sum of amount" })
        ).toBeVisible();
        await page.getByText("10,000").click();
        await expect(
          page.getByRole("tooltip", { name: "Sum of amount since amount is" })
        ).toBeVisible();
        await page.getByText("3,000").click();
        await expect(
          page.getByRole("tooltip", { name: "Maximum of amount" })
        ).toBeVisible();
        await page.getByText("1,000", { exact: true }).click();
        await expect(
          page.getByRole("tooltip", { name: "Minimum of amount" })
        ).toBeVisible();
        await page.getByText("2,000").nth(2).click();
        await expect(
          page.getByRole("tooltip", { name: "Average of amount" })
        ).toBeVisible();
        await page.getByLabel("Quota Erosion").getByText("4").click();
        await expect(
          page.getByRole("tooltip", { name: "Count of amount since amount" })
        ).toBeVisible();
        await page.getByLabel("Quota Erosion").getByText("6").click();
        await expect(
          page.getByRole("tooltip", { name: "Count of amount not null" })
        ).toBeVisible();
        await page
          .getByLabel("Quota Erosion")
          .getByText("3", { exact: true })
          .click();
        await expect(
          page.getByRole("tooltip", { name: "Distinct count of amount" })
        ).toBeVisible();
        await page.getByText("Show all").click();
        await expect(
          page.getByLabel("Condition(s)").getByRole("document")
        ).toContainText(
          'Since 3 is IN (1, 2, 3) OR Concat ("", [string1, string2])(string1string2) is equal to string1string2 OR CurrentPayoutPeriod (Month)(1) is less than 3 OR GetDate (start, Month, Fiscal)(1) is IN GetDate (start, Month, Fiscal)(1) OR email contains "@"(True) OR email does not contain "@"(False) OR IsEmpty (email)(False) OR IsNotEmpty (email)(True) OR Lowercase (email)(<EMAIL>) is IN Lowercase (email)(<EMAIL>) OR PositionOf ("@" in email)(8) is greater than 2 OR dynamic(10) is IN dynamic(10) AND True'
        );
      });
    });

    // Calculated Fields Functions Payee10 March
    test("Calculated Field Functions", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTEwQHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.getByText("Calculated Field Plan").click();
      await page.getByRole("button", { name: "Conditional" }).click();
      await page.getByTestId("pt-151097-trace-icon").click();
      await page.getByText("Show all").click();
      await expect(
        page.getByLabel("Condition(s)").getByRole("document")
      ).toContainText(
        'Since DateDifference between dates and start in Days(-5) is greater than 1 OR DateDifference between dates and end in Days(22) is greater than 2 OR GetDate (dates, Month, Fiscal)(2) is NOT IN GetDate (dates, Month, Fiscal)(2) OR (number) rounded by 1 decimal places(2,000) is not equal to 100 OR (number) rounded up by 1 decimal places(2,000) is not equal to 200 OR (number) rounded down by 1 decimal places(2,000) is not equal to 200 OR IsEmpty (string)(False) OR IsNotEmpty (string)(True) OR string contains "abc"(False) OR string does not contain "abc"(True) OR Lowercase (string)(151097id) is not equal to Concat ("", [string])(151097id) OR PositionOf ("@" in string)(0) is less than 2'
      );
      await page
        .getByLabel("Condition(s)")
        .getByRole("document")
        .getByRole("button")
        .click();
      await page.getByText("-5", { exact: true }).first().click();
      await expect(
        page.getByRole("tooltip", { name: "Date difference between dates" })
      ).toBeVisible();
      await page.getByText("22").click();
      await expect(
        page.getByRole("tooltip", {
          name: "Date difference between dates and end in Days",
        })
      ).toBeVisible();
      await page.getByText("2,000").click();
      await expect(
        page.getByRole("tooltip", { name: "Value of number rounded up by" })
      ).toBeVisible();
      await page.getByText("0", { exact: true }).click();
      await expect(
        page.getByRole("tooltip", { name: 'Position of "@" in string' })
      ).toBeVisible();
    });

    // payee 14
    test("Quarterly", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTE0QHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDEtMDEiLCJwZWQiOiIyMDI0LTAzLTMxIn0=",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.getByText("Quarterly Plan").click();
      await page.getByRole("button", { name: "Quota" }).click();
      await expect(page.getByTestId("pt-151127-trace-icon")).toBeVisible();
      await expect(
        page.getByTestId("pt-151125-trace-icon").locator("path").nth(1)
      ).toBeVisible();
      await expect(page.getByTestId("pt-151126-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151124-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151122-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151123-trace-icon")).toBeVisible();
      await page.getByTestId("pt-151127-trace-icon").click();
      await expect(page.getByText("Show full trace")).toBeVisible({
        timeout: 45000,
      });
      await page.getByRole("switch").click();
      await expect(
        page.getByLabel("Quota Erosion").getByText("1,000amount1,000")
      ).toBeVisible();
      await expect(page.getByText("₹0.00Annual Variable Pay4")).toBeVisible();
      await page.getByRole("button", { name: "Tier" }).click();
      await expect(
        page.getByLabel("Tier").getByText("Show full trace")
      ).toBeVisible();
      await page.getByRole("switch").click();
      await expect(
        page.getByLabel("Tier").getByText("₹0.00Annual Variable Pay4")
      ).toBeVisible();
      await expect(
        page.getByLabel("Tier").getByText("1,000amount1,000")
      ).toBeVisible();
    });

    // payee15
    test("Half", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTE1QHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDEtMDEiLCJwZWQiOiIyMDI0LTA2LTMwIn0=",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.getByText("Half Plan").click();
      await page.getByRole("button", { name: "Quota" }).click();
      await expect(page.getByTestId("pt-151135-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151136-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151134-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151131-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151132-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151133-trace-icon")).toBeVisible();
      await page.getByTestId("pt-151135-trace-icon").click();
      await expect(page.getByText("Show full trace")).toBeVisible({
        timeout: 45000,
      });
      await page.getByRole("switch").click();
      await expect(page.getByText("2,000amount1,000")).toBeVisible();
      await expect(page.getByText("₹0.00Annual Variable Pay2")).toBeVisible();
      await page.getByRole("tab", { name: "Tier" }).click();
      await expect(
        page.getByLabel("Tier").getByText("Show full trace")
      ).toBeVisible();
      await page.getByRole("switch").click();
      await expect(
        page.getByLabel("Tier").getByText("2,000amount1,000")
      ).toBeVisible();
    });

    // payee16
    test("Annual", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTE2QHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDEtMDEiLCJwZWQiOiIyMDI0LTEyLTMxIn0=",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.getByText("Annual Plan").click();
      await page.getByRole("button", { name: "Quota" }).click();
      await expect(page.getByTestId("pt-151144-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151145-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151143-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151141-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151140-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151142-trace-icon")).toBeVisible();
      await page.getByTestId("pt-151144-trace-icon").click();
      await expect(page.getByText("Show full trace")).toBeVisible({
        timeout: 45000,
      });
      await page.getByRole("switch").click();
      await expect(page.getByText("2,000amount1,000")).toBeVisible();
      await page.getByRole("tab", { name: "Tier" }).click();
      await expect(
        page.getByLabel("Tier").getByText("Show full trace")
      ).toBeVisible();
      await page.getByRole("switch").click();
      await expect(
        page.getByLabel("Tier").getByText("2,000amount1,000")
      ).toBeVisible();
    });

    test("Payout Summary", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTEzQHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.getByText("Payout from current period").click();
      await page.getByText("Calculated Conditional").click();
      await page.getByRole("button", { name: "Conditional" }).click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByTestId("pt-151114-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151115-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151113-trace-icon")).toBeHidden();
      // await page.getByLabel("Close").click();
      await page.getByRole("tab", { name: "Quota", exact: true }).click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByTestId("pt-151114-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151115-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151113-trace-icon")).toBeHidden();
      // await page.getByLabel("Close").click();
      await page.getByRole("tab", { name: "Simple" }).click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByTestId("pt-151114-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151115-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151113-trace-icon")).toBeHidden();
      await page.getByLabel("Close").click();
      await page.getByText("Payout from previously").click();

      await page.getByText("Calculated Conditional").nth(1).click();
      await page
        .getByRole("row", { name: "Conditional ₹9,000.00", exact: true })
        .getByRole("button")
        .click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByTestId("pt-151114-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151115-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151113-trace-icon")).toBeHidden();
      await page.getByLabel("Close").click();
    });

    // Earned
    test("Commission Summary Conditional", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTEzQHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.getByText("Commission Summary").click();
      await page.getByText("Earned Commissions").click();
      await page
        .getByLabel("Commission Summary")
        .getByText("Calculated Conditional")
        .click();
      await page.getByRole("button", { name: "Conditional" }).click();
      await expect(page.getByTestId("pt-151114-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151115-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151113-trace-icon")).toBeVisible();
      await page.getByTestId("pt-151114-trace-icon").click();
      await expect(page.getByText("Show full trace")).toBeVisible({
        timeout: 45000,
      });
      await page.getByRole("switch").click();
      await expect(page.getByText("2,000amount1,000")).toBeVisible();
      await expect(page.getByText("₹0.00Annual Variable Pay12")).toBeVisible();
    });

    test("Commission Summary Quota", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTEzQHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.getByText("Commission Summary").click();
      await page.getByText("Earned Commissions").click();
      await page
        .getByLabel("Commission Summary")
        .getByText("Calculated Conditional")
        .click();
      await page.getByRole("button", { name: "Quota" }).click();
      await expect(page.getByTestId("pt-151114-trace-icon")).toBeVisible();
      await expect(page.getByTestId("pt-151115-trace-icon")).toBeVisible();
      await expect(
        page.getByTestId("pt-151113-trace-icon").first()
      ).toBeVisible();
      await expect(
        page.getByTestId("pt-151113-trace-icon").nth(1)
      ).toBeVisible();
      await page.getByTestId("pt-151114-trace-icon").click();
      await expect(page.getByText("Show full trace")).toBeVisible({
        timeout: 45000,
      });
      await page.getByRole("switch").click();
      await expect(page.getByText("₹0.00Annual Variable Pay12")).toBeVisible();
      await page.getByRole("tab", { name: "Tier" }).click();
      await expect(
        page.getByLabel("Tier").getByText("Show full trace")
      ).toBeVisible();
      await page.getByRole("switch").click();
      await expect(
        page.getByLabel("Tier").getByText("₹0.00Annual Variable Pay12")
      ).toBeVisible();
    });

    test("Commission Summary Simple", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTEzQHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.getByText("Commission Summary").click();
      await page.getByText("Earned Commissions").click();
      await page
        .getByLabel("Commission Summary")
        .getByText("Calculated Conditional")
        .click();
      await page.getByRole("button", { name: "Simple" }).click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByTestId("pt-151114-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151115-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151113-trace-icon")).toBeHidden();
      await page.getByLabel("Close").click();
    });
    // Deferred

    test("Commission Summary Deferred", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTEzQHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.getByText("Commission Summary").click();
      await page.getByText("Deferred Commissions", { exact: true }).click();
      await page.getByText("Calculated Conditional").click();
      await page.getByRole("button", { name: "Conditional" }).click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByTestId("pt-151114-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151115-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151113-trace-icon")).toBeHidden();
      // await page.getByLabel("Close").click();
      await page.getByRole("tab", { name: "Quota", exact: true }).click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByTestId("pt-151114-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151115-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151113-trace-icon")).toBeHidden();
      // await page.getByLabel("Close").click();
      await page.getByRole("tab", { name: "Simple" }).click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByTestId("pt-151114-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151115-trace-icon")).toBeHidden();
      await expect(page.getByTestId("pt-151113-trace-icon")).toBeHidden();
    });
  }
);

// Write on Payee3 February
test2.describe(
  "Fx Rate Check",
  { tag: ["@payouts", "@regression", "@primelogic-5"] },
  () => {
    test2.beforeEach(async ({ adminPage }, testInfo) => {
      testInfo.setTimeout(testInfo.timeout + 1200000);
    });

    test2("Fx Rate", async ({ adminPage }) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);

      await csPrev.navigate(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTNAcGF5b3V0c2JhbGEyLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0="
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await expect2(page.getByTestId("login-indicator")).toContainText(
        "Total Payout€100.000,00"
      );
      await csPrev.navigate("/settings/basic");
      await page
        .locator("div")
        .filter({ hasText: /^Select currency$/ })
        .nth(3)
        .click();
      await page.locator("span").filter({ hasText: "EUR" }).click();
      await page.getByPlaceholder("Start month").click();
      await page.getByPlaceholder("Start month").fill("Jan-2024");
      await page.getByPlaceholder("Start month").press("Enter");
      await page.getByPlaceholder("End month").click();
      await page.getByPlaceholder("End month").fill("Feb-2024");
      await page.getByPlaceholder("End month").press("Enter");

      await page.getByRole("spinbutton").click();
      await page.getByRole("spinbutton").fill("3");
      await page.getByRole("button", { name: "Update" }).click();

      await expect2(page.getByText("Updated Successfully!!")).toBeVisible();
      await page.waitForLoadState("networkidle");
    });

    test2("Fx Rate Sync", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 1200000);
      const csPrev = new CommissionsSyncPrevPeriod(page);
      const commSync = new CommissionSync(page);

      await csPrev.navigate("/settings/commissions-and-data-sync");

      await commSync.selectCriteria("selected-payees");
      await commSync.selectDropdown(["payee3 payouts3"]);

      await csPrev.selectDate("29 Feb 2024");
      await csPrev.runCommissions();
      await csPrev.clickSkipandRun();
      await csPrev.waitForCalculationMessage();
      await csPrev.waitForCommissionsSuccess();
    });

    test2("Fx Rate Updation", async ({ adminPage }) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);

      await csPrev.navigate(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTNAcGF5b3V0c2JhbGEyLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0="
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await expect2(page.getByTestId("login-indicator")).toContainText(
        "Total Payout€60.000,00"
      );
    });
  }
);

// Payee 12 login Feb
test3(
  "Restricted DS and Custom Object",
  { tag: ["@payouts", "@regression", "@primelogic-5"] },
  async ({ adminPage }) => {
    const page = adminPage.page;
    await page.goto(
      "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTEyQHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
      { waitUntil: "networkidle" }
    );
    await expect(page.getByText("Last")).toBeVisible({
      timeout: 60000,
    });
    await page.getByText("Restricted Plan").click();
    await page.getByRole("button", { name: "DS Perm" }).click();
    await expect3(page.getByTestId("pt-151104-trace-icon")).toBeHidden();
    await page.waitForLoadState("networkidle");
    // await page.getByLabel("Close").click();
    await page.getByRole("tab", { name: "CO Perm" }).click();
    await page.waitForLoadState("networkidle");
    await expect3(page.getByTestId("pt-151104-trace-icon")).toBeHidden();
    await expect3(page.getByTestId("pt-151105-trace-icon")).toBeHidden();
    await expect3(page.getByTestId("pt-151106-trace-icon")).toBeHidden();
  }
);

test4.describe(
  "Variable Pay Reflection Trace",
  { tag: ["@payouts", "@regression", "@primelogic-5"] },
  () => {
    test4.beforeEach(async ({ adminPage }, testInfo) => {
      testInfo.setTimeout(testInfo.timeout + 1200000);
    });

    test4("Variable Pay Lock", async ({ adminPage }) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);

      await csPrev.navigate(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTFAcGF5b3V0c2JhbGEzLmNvbSIsInBzZCI6IjIwMjQtMDEtMDEiLCJwZWQiOiIyMDI0LTAxLTMxIn0="
      );

      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.locator("button:nth-child(4)").click();
      await expect4(
        page.getByText("Statement locked successfully")
      ).toBeVisible({
        timeout: 45000,
      });
      await page.waitForLoadState("networkidle");

      await page.getByText("Simple").click();
      await page.getByRole("button", { name: "Simple" }).click();
      await page.getByTestId("pt-151005-trace-icon").click();
      await expect4(page.locator("body")).toContainText(
        "₹8.33Monthly Variable Pay"
      );
    });

    test4("Variable Pay Change", async ({ adminPage }) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);

      await csPrev.navigate("/users");
      await page.getByTestId("<EMAIL> users dd button").click();
      await page
        .getByRole("menuitem", { name: "Map Payee" })
        .getByRole("button")
        .click();
      await page.getByPlaceholder("Enter Variable Pay").click();
      await page.getByPlaceholder("Enter Variable Pay").fill("200");
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByLabel("Save by overwriting current").check();
      await page.getByRole("button", { name: "Save", exact: true }).click();
      await expect4(page.getByText("Employee details saved!")).toBeVisible();
      await page.waitForLoadState("networkidle");
    });

    test4("Variable Pay Sync", async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 1200000);
      const csPrev = new CommissionsSyncPrevPeriod(page);
      const commSync = new CommissionSync(page);

      await csPrev.navigate("/settings/commissions-and-data-sync");

      await commSync.selectCriteria("selected-payees");
      await commSync.selectDropdown(["payee1 payouts3"]);

      await csPrev.selectDate("31 Jan 2024");
      await csPrev.runCommissions();
      await csPrev.clickSkipandRun();
      await csPrev.waitForCalculationMessage();
      await csPrev.waitForCommissionsSuccess();
    });

    test4("Variable Pay Check", async ({ adminPage }) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);

      await csPrev.navigate(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTFAcGF5b3V0c2JhbGEzLmNvbSIsInBzZCI6IjIwMjQtMDEtMDEiLCJwZWQiOiIyMDI0LTAxLTMxIn0="
      );
      await expect(page.getByText("Last")).toBeVisible({
        timeout: 60000,
      });
      await page.locator("button:nth-child(4)").click();
      await expect4(
        page.getByText("Statement unlocked successfully")
      ).toBeVisible({
        timeout: 45000,
      });
      await page.waitForLoadState("networkidle");
      await page.getByText("Simple").click();
      await page.getByRole("button", { name: "Simple" }).click();
      await page.getByTestId("pt-151005-trace-icon").click();
      await expect4(page.locator("body")).toContainText(
        "₹16.67Monthly Variable Pay"
      );
    });
  }
);
