import { expect } from "@playwright/test";
import PayoutFilterPageObjs from "../../../../test-objects/payfilterobj";
const {
  localCloneFixtures: { test },
} = require("../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  const key = "commission-view-period";
  const value = "January-2023";
  await page.evaluate(
    ({ key, value }) => {
      // Set the localStorage value for the current page
      localStorage.setItem(key, value);
    },
    { key, value }
  );
  await page.goto("/commissions", { waitUntil: "networkidle" });
  await page.waitForLoadState("networkidle");
  await page.waitForTimeout(2000);
});

test.describe(
  "Payout filters ",
  { tag: ["@payouts", "@regression", "@primelogic-5"] },
  () => {
    test("Lock status - UnLocked statements", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12055" },
        { type: "Description", description: "unlocked statements" },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Unlocked statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectUnlockedStatements();
      await payoutpage.verifyAndLogVisibility(
        "Lock Status InUnlocked Statements",
        "Filter Applied"
      );
    });

    test("Lock status - Locked statements", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12055" },
        { type: "Description", description: "locked statements" },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "locked statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectLockedStatements();
      await payoutpage.verifyAndLogVisibility(
        "Lock Status InLocked Statements",
        "Filter Applied"
      );
    });

    test("Payout status - Unpaid", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12056" },
        { type: "Description", description: "Unpaid statements" },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Unpaid statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectUnpaidPayouts();
      await payoutpage.verifyAndLogVisibility(
        "Payout Status InUnpaid",
        "Filter Applied"
      );
    });
    // unpaidPayout Status InPaid
    test("Payout status - Paid", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12056" },
        { type: "Description", description: "Paid statements" },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Paid statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectPaidPayouts("January 2023");
      await payoutpage.verifyAndLogVisibility(
        "Payout Status InPaid",
        "Filter Applied"
      );
    });

    test("Payout status - Zero payout", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12056" },
        { type: "Description", description: "Zero payout statements" },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Zero payout statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectZeroPayouts();
      await payoutpage.verifyAndLogVisibility(
        "Payout Status InZero Payout",
        "Filter Applied"
      );
    });

    test("Payout status - Not In Zero payout", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12056" },
        { type: "Description", description: "Not in - Zero payout statements" },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Not in - Zero payout statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectNotInZeroPayouts();
      await payoutpage.verifyAndLogVisibility(
        "Payout Status Not InZero Payout",
        "Filter Applied"
      );
    });

    test("Payout frequency - Monthly", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12057" },
        { type: "Description", description: "Monthly payout statements" },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Monthly payout frequency statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectMonthlyPayoutFrequency();
      await payoutpage.verifyAndLogVisibility(
        "Payout Frequency InMonthly",
        "Filter Applied"
      );
    });

    test("Payout frequency - Quarterly", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12057" },
        { type: "Description", description: "Quarterly payout statements" },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Quarterly payout frequency statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectQuarterlyPayoutFrequency();
      await payoutpage.verifyAndLogVisibility(
        "Payout Frequency InQuarterly",
        "Filter Applied"
      );
    });

    test("In Commission plan", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12058" },
        {
          type: "Description",
          description: "IN commission plan -Icm Dev statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "IN commission plan -Icm Dev statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectInCommPlan("ICM dev");
      await payoutpage.verifyAndLogVisibility(
        "Commission Plans InICM dev",
        "Filter Applied"
      );
    });

    test("Not In Comm plan", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12058" },
        {
          type: "Description",
          description: "NOT IN commission plan -Icm Dev statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "NOT IN commission plan -Icm Dev statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectNotInCommPlan("ICM dev");
      await payoutpage.verifyAndLogVisibility(
        "Commission Plans Not InICM dev",
        "Filter Applied"
      );
    });

    test("Not Requested - APPROVAL STATUS", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12059" },
        {
          type: "Description",
          description: "Not Requested - APPROVAL STATUS statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Not Requested - APPROVAL STATUS statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectNotRequested();
      await payoutpage.verifyAndLogVisibility(
        "Approval Status InNot Requested",
        "Filter Applied"
      );
    });

    test("Not in Approved", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12059" },
        {
          type: "Description",
          description: "Not IN - APPROVED STATUS statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Not in - APPROVED STATUS statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectNotInApproved();
      await payoutpage.verifyAndLogVisibility(
        "Approval Status Not InApproved",
        "Filter Applied"
      );
    });

    test("Reporting manager In", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12060" },
        {
          type: "Description",
          description: "Reporting manager In - Ashiq Everstage statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Reporting manager In - Ashiq Everstage statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectReportingManagerIn("Ashiq Everstage");
      await payoutpage.verifyAndLogVisibility(
        "Reporting Manager InAshiq Everstage",
        "Filter Applied"
      );
    });

    test("Reporting Manager - Not In", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12060" },
        {
          type: "Description",
          description: "Reporting manager Not In - Ashiq Everstage statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Reporting manager Not In - Ashiq Everstage statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectReportingManagerNotIn("Ashiq Everstage");
      await payoutpage.verifyAndLogVisibility(
        "Reporting Manager Not InAshiq Everstage",
        "Filter Applied"
      );
    });

    test("Designation - Contains", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12061" },
        {
          type: "Description",
          description: "Designation - Contains - 'a' statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Designation - Contains - 'a' statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectDesignationContains("c");
      await payoutpage.verifyAndLogVisibility(
        "Designation Containsc",
        "Filter Applied"
      );
    });

    test("Pending Amount - Greater Than", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12062" },
        {
          type: "Description",
          description: "Pending Amount - Greater Than - '20000' statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "ending Amount - Greater Than - '20000' statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectPendingAmountGreaterThan("20000");
      await payoutpage.verifyAndLogVisibility(
        "Payout Amount Greater Than20000",
        "Filter Applied"
      );
    });

    test("Employment country", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12063" },
        {
          type: "Description",
          description: "Employment country - 'India' statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Employment country - 'India' statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectEmploymentCountry("India");
      await payoutpage.verifyAndLogVisibility(
        "Employment Country InIND",
        "Filter Applied"
      );
    });

    test("Payout Currency - In", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12064" },
        {
          type: "Description",
          description: "Payout Currency - In - 'inr' statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Payout Currency - In - 'inr'  statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectPayoutCurrencyIn("INR");
      await payoutpage.verifyAndLogVisibility(
        "Payout Currency InINR",
        "Filter Applied"
      );
    });

    test("Payout Amount - In Between", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12065" },
        {
          type: "Description",
          description:
            "Payout Amount - In Between - '0' to '9000000' statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "ayout Amount - In Between - '0' to '9000000'  statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectPayoutAmountInBetween("0", "9000000");
      await payoutpage.verifyAndLogVisibility(
        "Payout Amount In Between0 to 9000000",
        "Filter Applied"
      );
    });

    test("Commission % equal to", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12066" },
        {
          type: "Description",
          description: "Commission equal to '25000' statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Commission equal to '25000' statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectCommissionGreaterthan("10");
      await payoutpage.verifyAndLogVisibility(
        "Commission % Greater Than10",
        "Filter Applied"
      );
    });

    test("Commission Not Equal To", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12066" },
        {
          type: "Description",
          description: "Commission not equal to '0' statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Commission not equal to '0' statements should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectCommissionNotEqualTo("10");
      await payoutpage.verifyAndLogVisibility(
        "Commission % Not Equal To10",
        "Filter Applied"
      );
    });

    test("Base pay - greater than 10000", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12081" },
        {
          type: "Description",
          description: "Base pay - greater than 10000",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Base pay - greater than 10000 should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectBasePayGreaterThan("10000");
      await payoutpage.verifyAndLogVisibility(
        "Base Pay Greater Than10000",
        "Filter Applied"
      );
    });

    test("Export - without Filters", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12718" },
        {
          type: "Description",
          description: "export without any filters",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "export without any filters",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.exportWithoutFilters();
      await payoutpage.verifyAndLogVisibility(
        "Successfully Exported",
        "Filter Applied",
        "Banner Message"
      );
    });

    test("Export - with Filters", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12718" },
        {
          type: "Description",
          description: "export with filters - locked statements",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "export with filters -  locked statements",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.exportWithFilters();
      await payoutpage.verifyAndLogVisibility(
        "Successfully Exported",
        "Filter Applied",
        "Banner Message"
      );
    });

    test("More filters - processed", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12068" },
        {
          type: "Description",
          description: "More filters - processed - is not empty",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "More filters - processed - Is not empty",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectProcessedEqualTo("Is Not Empty");
      await payoutpage.verifyAndLogVisibility(
        "Processed Is Not Emptytrue",
        "Filter Applied"
      );
    });

    test("More filters - Roles", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12081" },
        {
          type: "Description",
          description: "More filters - Roles - payee",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "More filters - More filters - Roles - payee should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectRoles("Payee");
      await payoutpage.verifyAndLogVisibility(
        "Roles InPayee",
        "Filter Applied"
      );
    });
    test("More filters - Joining Date - In between", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12071" },
        {
          type: "Description",
          description: "More filters - Joining Date - In between",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "More filters Joining Date - In between '2022-01-01', '2024-12-31' should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectJoiningDate("2022-01-01", "2024-12-31");
      await payoutpage.verifyAndLogVisibility(
        "Joining Date In Between01-Jan-2022 to 31-Dec-2024",
        "Filter Applied"
      );
    });

    test("More filters - Exit Date - In between", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12071" },
        {
          type: "Description",
          description: "More filters - Exit Date - In between",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "More filters Exit Date - In between '2022-01-01', '2024-12-31' should be filtered",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.applyExitDateFilter("2022-01-01", "2024-12-31");
      await payoutpage.waitForTimeout(2000);
      await payoutpage.verifyAndLogVisibility(
        "Exit Date In Between01-Jan-2022 to 31-Dec-2024",
        "Filter Applied"
      );
    });

    test("Variable pay - less than 500000", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12071" },
        {
          type: "Description",
          description: "More filters variable pay- less than",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "More filters variable pay - less than 50000",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.applyVariablePayFilterLessThan("50000");
      await payoutpage.verifyAndLogVisibility(
        "Variable Pay Less than50000",
        "Filter Applied"
      );
    });

    test("Multiple filter - Unpaid & Monthly", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12075" },
        {
          type: "Description",
          description: "Custom filter - Unpaid & Monthly",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Custom filter - Unpaid & Monthly",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectCustomFilter("Unpaid", "Monthly");
      await payoutpage.verifyAndLogVisibility(
        "Payout Frequency InMonthly" && "Payout Status InUnpaid",
        "Filter Applied"
      );
    });

    test("Save a filter,  and Delete", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12078,INTER-T12085" },
        {
          type: "Description",
          description: "Save a filter,  and Delete",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Save a filter,  and Delete",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      const filterName = "Unpaid 3";
      await payoutpage.saveFilterView(filterName);
      await payoutpage.verifyAndLogVisibility(
        "Payout Status InUnpaid",
        "Filter Saved"
      );
      await payoutpage.waitForTimeout(2000);
      await payoutpage.clickClearButton();
      await payoutpage.deleteSavedFilter(filterName);
      const isFilterVisible = await payoutpage.isTextVisible(
        "Unpaid 3",
        "Filter Name"
      );
      if (!isFilterVisible) {
        console.log("Filter deleted");
      } else {
        expect(isFilterVisible).toBe(false);
      }
    });

    test("more filter - Ignored - Greater than", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12069" },
        {
          type: "Description",
          description: "more filters Ignored - Greater than 50000",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "more filters Ignored - Greater than 50000",
        }
      );

      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.selectIgnoredGreaterThan("50000");
      await payoutpage.verifyAndLogVisibility(
        "Ignored Greater Than50000",
        "Filter Applied"
      );
    });

    test("Add,Clone,Edit and Save", async ({ adminPage }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T12082, INTER-T12080, INTER-T12078, INTER-T12083, INTER-T12084",
        },
        {
          type: "Description",
          description: "Add,Clone,Edit and Save",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "New filter is added the in more save filters added filter is selected and cloned. the cloned filter is edited and saved and at last the parent filter is deleted",
        }
      );
      const payoutpage = new PayoutFilterPageObjs(adminPage.page);
      await payoutpage.saveFilterView("Unpaid 2");
      await payoutpage.verifyAndLogVisibility(
        "Payout Status InUnpaid",
        "Filter Saved"
      );
      await payoutpage.moresavedfilter("Unpaid 2");
      await payoutpage.cloneFilterAndView("Unpaid 2");
      await payoutpage.waitForTimeout(5000);
      await payoutpage.verifyAndLogVisibility(
        "Copy of Unpaid 2",
        "Filter cloned",
        "Filter Name"
      );
      await payoutpage.editSavedFilter("Zero Payout");
      await payoutpage.waitForTimeout(2000);
      await payoutpage.verifyAndLogVisibility(
        "Copy of Unpaid 2",
        "Filter edited",
        "Filter Name"
      );
      await payoutpage.clickClearButton();
      await payoutpage.deleteSavedFilter("Unpaid 2");
      await payoutpage.waitForDeletedMessage(5000);
      await payoutpage.deleteSavedFilter("Copy of Unpaid 2");
    });

    test(
      "Validate that when applying Employee ID field, the filters are successfully applied",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T19275, INTER-T19276, INTER-T19277, INTER-T19278, INTER-T19279, INTER-T19280, INTER-T19281, INTER-T19282, INTER-T19283, INTER-T19284",
          },
          {
            type: "Description",
            description:
              "Validating that Employee ID field with various actions like Contains,Does Not Contain,Starts With, Ends With,Equal To,NotEqual To,Is Empty,Is Not Empty,In,Not In",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const payoutPage = new PayoutFilterPageObjs(page);
        // Validating Employee ID - Contains
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Employee ID"]);
        await payoutPage.enterValue("Employee ID", "Contains", ["10"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Employee ID Contains10",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Employee ID - Does Not Contain
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Employee ID"]);
        await payoutPage.enterValue("Employee ID", "Does not Contain", ["10"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Employee ID Does not Contain10",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Employee ID - Starts With
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Employee ID"]);
        await payoutPage.enterValue("Employee ID", "Starts With", ["1A"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Employee ID Starts With1A",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Employee ID - Ends With
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Employee ID"]);
        await payoutPage.enterValue("Employee ID", "Ends With", ["0E"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Employee ID Ends With0E",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Employee ID - Equal To
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Employee ID"]);
        await payoutPage.enterValue("Employee ID", "Equal To", ["W21"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Employee ID Equal ToW21",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Employee ID - Not Equal To
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Employee ID"]);
        await payoutPage.enterValue("Employee ID", "Not Equal To", ["2D1"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Employee ID Not Equal To2D1",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Employee ID - Is Empty
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Employee ID"]);
        await payoutPage.enterValue("Employee ID", "Is Empty");
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Employee ID Is Empty",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Employee ID - Is Not Empty
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Employee ID"]);
        await payoutPage.enterValue("Employee ID", "Is Not Empty");
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Employee ID Is Not Empty",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom Textfield - In

        /** TBC Once Open bug Fixed */

        // Validating Custom Textfield - Not In

        /** TBC Once Open bug Fixed */
      }
    );

    test(
      "Validate that when applying User Source dropdown field, the filters are successfully applied",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T19285, INTER-T19286, INTER-T19287, INTER-T19288, INTER-T19289, INTER-T19290",
          },
          {
            type: "Description",
            description:
              "Validating that User Source dropdown field with various actions like In and Not In with Manually managed and Managed by integrations ",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const payoutPage = new PayoutFilterPageObjs(page);
        // Validating In with Manually managed
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["User Source"]);
        await payoutPage.selectDropdownOption("user_source", "In");
        await payoutPage.selectDropdownValue("user_source", [
          "Manually managed",
        ]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "User Source InManually managed",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating In with Managed by integrations
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["User Source"]);
        await payoutPage.selectDropdownOption("user_source", "In");
        await payoutPage.selectDropdownValue("user_source", [
          "Managed by integrations",
        ]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "User Source InManaged by integrations",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating In with Manually managed  Managed by integrations
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["User Source"]);
        await payoutPage.selectDropdownOption("user_source", "In");
        await payoutPage.selectDropdownValue("user_source", [
          "Manually managed",
          "Managed by integrations",
        ]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "User Source InManually managed",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Not In with Manually managed
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["User Source"]);
        await payoutPage.selectDropdownOption("user_source", "Not In");
        await payoutPage.selectDropdownValue("user_source", [
          "Manually managed",
        ]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "User Source Not InManually managed",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Not In with Managed by integrations
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["User Source"]);
        await payoutPage.selectDropdownOption("user_source", "Not In");
        await payoutPage.selectDropdownValue("user_source", [
          "Managed by integrations",
        ]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "User Source Not InManaged by integrations",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Not In with Manually managed and Managed by integrations
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["User Source"]);
        await payoutPage.selectDropdownOption("user_source", "Not In");
        await payoutPage.selectDropdownValue("user_source", [
          "Manually managed",
          "Managed by integrations",
        ]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "User Source Not InManually managed",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
      }
    );

    test(
      "Validate that when applying Custom Text field, the filters are successfully applied",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T19291, INTER-T19292, INTER-T19293, INTER-T19294, INTER-T19295, INTER-T19296, INTER-T19297, INTER-T19298, INTER-T19299, INTER-T19300",
          },
          {
            type: "Description",
            description:
              "Validating that Custom_Text field with various actions like Contains,Does Not Contain,Starts With, Ends With,Equal To,NotEqual To,Is Empty,Is Not Empty,In,Not In",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const payoutPage = new PayoutFilterPageObjs(page);
        // Validating Custom Textfield - Contains
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Text"]);
        await payoutPage.enterValue("Custom_Text", "Contains", ["Text"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Text ContainsText",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom Textfield - Does Not Contain
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Text"]);
        await payoutPage.enterValue("Custom_Text", "Does not Contain", [
          "Text",
        ]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Text Does not ContainText",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom Textfield - Starts With
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Text"]);
        await payoutPage.enterValue("Custom_Text", "Starts With", ["T"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Text Starts WithT",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom Textfield - Ends With
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Text"]);
        await payoutPage.enterValue("Custom_Text", "Ends With", ["t"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Text Ends Witht",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom Textfield - Equal To
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Text"]);
        await payoutPage.enterValue("Custom_Text", "Equal To", ["Payee"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Text Equal ToPayee",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom Textfield - Not Equal To
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Text"]);
        await payoutPage.enterValue("Custom_Text", "Not Equal To", ["Payee"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Text Not Equal ToPayee",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom Textfield - Is Empty
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Text"]);
        await payoutPage.enterValue("Custom_Text", "Is Empty");
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Text Is Empty",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom Textfield - Is Not Empty
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Text"]);
        await payoutPage.enterValue("Custom_Text", "Is Not Empty");
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Text Is Not Empty",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom Textfield - In

        /** TBC Once Open bug Fixed */

        // Validating Custom Textfield - Not In

        /** TBC Once Open bug Fixed */
      }
    );

    test(
      "Validate that when applying Custom Email field, the filters are successfully applied",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T19301, INTER-T19302, INTER-T19303, INTER-T19304, INTER-T19305, INTER-T19306, INTER-T19307, INTER-T19308, INTER-T19309, INTER-T19310",
          },
          {
            type: "Description",
            description:
              "Validating that Custom_Mail field with various actions like Contains,Does Not Contain,Starts With, Ends With,Equal To,NotEqual To,Is Empty,Is Not Empty,In,Not In",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const payoutPage = new PayoutFilterPageObjs(page);
        // Validating Custom Email - Contains
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Mail"]);
        await payoutPage.enterValue("Custom_Mail", "Contains", ["<EMAIL>"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Mail <EMAIL>",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom Email - Does Not Contain
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Mail"]);
        await payoutPage.enterValue("Custom_Mail", "Does not Contain", [
          "<EMAIL>",
        ]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Mail <NAME_EMAIL>",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom Email - Starts With
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Mail"]);
        await payoutPage.enterValue("Custom_Mail", "Starts With", ["m"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Mail Starts Withm",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom Email - Ends With
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Mail"]);
        await payoutPage.enterValue("Custom_Mail", "Ends With", ["in"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Mail Ends Within",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom Email - Equal To
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Mail"]);
        await payoutPage.enterValue("Custom_Mail", "Equal To", ["<EMAIL>"]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_<NAME_EMAIL>",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom Email - Not Equal To
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Mail"]);
        await payoutPage.enterValue("Custom_Mail", "Not Equal To", [
          "<EMAIL>",
        ]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Mail <NAME_EMAIL>",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom Email - Is Empty
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Mail"]);
        await payoutPage.enterValue("Custom_Mail", "Is Empty");
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Mail Is Empty",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom Email - Is Not Empty
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Mail"]);
        await payoutPage.enterValue("Custom_Mail", "Is Not Empty");
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Mail Is Not Empty",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom Email - In

        /** TBC Once Open bug Fixed */

        // Validating Custom Email - Not In

        /** TBC Once Open bug Fixed */
      }
    );

    test(
      "Validate that when applying Custom Checkbox field, the filters are successfully applied",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T19311, INTER-T19312, INTER-T19313, INTER-T19314, INTER-T19315, INTER-T19316",
          },
          {
            type: "Description",
            description:
              "Validating that Custom Checkbox field field with various actions like In and Not In with True and False",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const payoutPage = new PayoutFilterPageObjs(page);
        // Validating Custom_Checkbox In with True
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Checkbox"]);
        await payoutPage.selectDropdownOption("cf_2006_custom_checkbox", "In");
        await payoutPage.selectDropdownValue("cf_2006_custom_checkbox", [
          "True",
        ]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Checkbox InTrue",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom_Checkbox In with False
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Checkbox"]);
        await payoutPage.selectDropdownOption("cf_2006_custom_checkbox", "In");
        await payoutPage.selectDropdownValue("cf_2006_custom_checkbox", [
          "False",
        ]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Checkbox InFalse",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom_Checkbox In with True and False
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Checkbox"]);
        await payoutPage.selectDropdownOption("cf_2006_custom_checkbox", "In");
        await payoutPage.selectDropdownValue("cf_2006_custom_checkbox", [
          "True",
          "False",
        ]);
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Checkbox InTrue, False",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom_Checkbox Not In with True
        /** TBC Once Open bug Fixed */

        // Validating Custom_Checkbox Not In False
        /** TBC Once Open bug Fixed */

        // Validating Custom_Checkbox Not In True and False
        /** TBC Once Open bug Fixed */
      }
    );

    test(
      "Validate that when applying Custom Number field, the filters are successfully applied",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T19322, INTER-T19323, INTER-T19324, INTER-T19325, INTER-T19326, INTER-T19327, INTER-T19328, INTER-T19329, INTER-T19330, INTER-T19331",
          },
          {
            type: "Description",
            description:
              "Validating that Custom Number field with various actions like In and Not In with option 1 and option 2",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const payoutPage = new PayoutFilterPageObjs(page);

        // Validating Custom_Number with Equal to
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Number"]);
        await payoutPage.enterValue(
          "Custom_Number",
          "Equal To",
          ["21"],
          "number"
        );
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Number Equal To21",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom_Number with Greater Than
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Number"]);
        await payoutPage.enterValue(
          "Custom_Number",
          "Greater Than",
          ["21"],
          "number"
        );
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Number Greater Than21",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom_Number with Greater than or Equal To
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Number"]);
        await payoutPage.enterValue(
          "Custom_Number",
          "Greater than or Equal to",
          ["21"],
          "number"
        );
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Number Greater than or Equal to21",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom_Number with In
        // /** TBC Once Open bug Fixed */

        // Validating Custom_Number with Is Empty
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Number"]);
        await payoutPage.enterValue("Custom_Number", "Is Empty");
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Number Is Empty",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom_Number with Is Not Empty
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Number"]);
        await payoutPage.enterValue("Custom_Number", "Is Not Empty");
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Number Is Not Empty",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom_Number with Less Than
        // /** TBC Once Open bug Fixed */

        // Validating Custom_Number with Less than or Equal to
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Number"]);
        await payoutPage.enterValue(
          "Custom_Number",
          "Less than or Equal to",
          ["21"],
          "number"
        );
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Number Less than or Equal to21",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom_Number with Not Equal to
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Number"]);
        await payoutPage.enterValue(
          "Custom_Number",
          "Not Equal To",
          ["21"],
          "number"
        );
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Number Not Equal To21",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();

        // Validating Custom_Number with Not In
        /** TBC Once Open bug Fixed */

        // Validating Custom_Number with Not Equal to
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Number"]);
        await payoutPage.enterValue(
          "Custom_Number",
          "In Between",
          ["0", "100"],
          "number"
        );
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Number In Between0 to 100",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
      }
    );

    test(
      "Validate that when applying Custom_Date Field, the filters are successfully applied and its giving the accurate results",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T19317, INTER-T19318, INTER-T19319, INTER-T19320, INTER-T19321",
          },
          {
            type: "Description",
            description:
              "Validating that when filter is applied, the expected results are displayed in the user screen",
          },
          {
            type: "Expected Behaviour",
            description:
              "when filter is applied, the expected results should displayed in the user screen",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const payoutPage = new PayoutFilterPageObjs(page);
        // Validating Custom_Date Field - In Between
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Date"]);
        await payoutPage.enterDateFields(
          "Custom_Date",
          "In Between",
          "2024-01-01",
          "2025-01-01"
        );
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Date In Between01-Jan-2024 to 01-Jan-2025",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom_Date Field - Greater Than
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Date"]);
        await payoutPage.enterDateFields(
          "Custom_Date",
          "Greater Than",
          "2024-09-30"
        );
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Date Greater Than30-Sep-2024",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom_Date Field - Greater than or Equal To
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Date"]);
        await payoutPage.enterDateFields(
          "Custom_Date",
          "Greater than or Equal to",
          "2024-09-30"
        );
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Date Greater than or Equal to30-Sep-2024",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom_Date Field - Less than
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Date"]);
        await payoutPage.enterDateFields(
          "Custom_Date",
          "Less than",
          "2024-09-30"
        );
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Date Less than30-Sep-2024",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
        // Validating Custom_Date Field - Less Than or Equal To
        await payoutPage.clickFilterButton();
        await payoutPage.addMoreFilters(["Custom_Date"]);
        await payoutPage.enterDateFields(
          "Custom_Date",
          "Less than or Equal to",
          "2024-09-30"
        );
        await payoutPage.clickApplyButton();
        await payoutPage.verifyAndLogVisibility(
          "Custom_Date Less than or Equal to30-Sep-2024",
          "Filter Applied"
        );
        await payoutPage.closeFilterView();
        await payoutPage.clickClearButton();
      }
    );
  }
);
