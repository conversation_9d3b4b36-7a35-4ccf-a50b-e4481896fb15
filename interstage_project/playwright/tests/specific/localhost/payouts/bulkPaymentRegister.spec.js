/* eslint-disable dot-notation */
import AdjustementsV2Page from "../../../../test-objects/adjustments-v2-objects";
import BulkRegisterPayments from "../../../../test-objects/bulkRegisterPayments-objects";

const {
  bulkRegisterPaymwnts: { test, expect },
} = require("../../../fixtures");

const fs = require("fs");
const path = require("path");
const { parse } = require("csv-parse/sync");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  const key = "commission-view-period";
  const value = "31-March-2025";

  await page.evaluate(
    ({ key, value }) => {
      // Set the localStorage value for the current page
      localStorage.setItem(key, value);
    },
    { key, value }
  );

  if (await page.getByText("Logged in as").isVisible({ timeout: 10000 })) {
    try {
      await page.getByRole("button", { name: "Exit" }).click();
      await page.waitForTimeout(5000);
    } catch {
      console.log(
        "In before Each, unable to click on Exit button for Logged in user"
      );
    }
  }
});

test.describe(
  "Concurrent Bulk Register Payments Automation",
  {
    tag: ["@bulkRegisterPayments", "@regression", "@primelogic-3"],
  },
  () => {
    test.skip(
      "Single Concurrent Payment Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T25694",
          },
          {
            type: "Description",
            description:
              "Initiate a payment for a payee and period. Then, from another window, initiate the payment for the same payee and period before the first process completes. Validate that the second payment request is blocked and a error message is displayed",
          },
          { type: "Precondition", description: "Payouts" },
          {
            type: "Expected Behaviour",
            description:
              "Upon single concurrent payments, the 2nd payment should be blocked and a CSV should be downloaded listing the detail on the failed payee and the reason",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const bulkRegisterPaymentsPage1 = new BulkRegisterPayments(page);
        const context = await page.context();

        await bulkRegisterPaymentsPage1.navigate("/commissions");

        // Open second tab for concurrent payment
        const newPage = await context.newPage();
        const bulkRegisterPaymentsPage2 = new BulkRegisterPayments(newPage);
        await bulkRegisterPaymentsPage2.navigate("/commissions");

        // Fill in payment details on both pages
        await Promise.all([
          bulkRegisterPaymentsPage1.initiatePayment(
            "<EMAIL>",
            "31 Mar",
            "5567"
          ),
          bulkRegisterPaymentsPage2.initiatePayment(
            "<EMAIL>",
            "31 Mar",
            "5567"
          ),
        ]);

        // Wait for download from second page (expected failure result)
        const downloadPromise = newPage.waitForEvent("download");

        // Trigger both Register buttons concurrently
        await Promise.all([
          bulkRegisterPaymentsPage1.clickBulkRegister(),
          await page.waitForTimeout(1000),
          bulkRegisterPaymentsPage2.clickBulkRegister(),
        ]);

        // Get the download from the 2nd page
        const download = await downloadPromise;

        // Save the file locally
        const downloadPath = path.join(__dirname, "downloads");
        const filePath = path.join(downloadPath, "Single_Blocked_Payments.csv");
        await download.saveAs(filePath);

        // Read and parse the CSV
        const csvContent = fs.readFileSync(filePath, "utf-8");
        const records = parse(csvContent, {
          columns: true,
          skip_empty_lines: true,
        });

        // ✅ CSV validation
        expect(records.length).toBeGreaterThan(0);
        expect(records[0]["Payee Email ID"]).toBe("<EMAIL>");
        expect(records[0]["Error"]).toBe("Previous process in progress");

        // ✅ UI validation on failure modal
        const failedTitle =
          await bulkRegisterPaymentsPage2.getFailedPaymentTitle();
        const failedMessage =
          await bulkRegisterPaymentsPage2.getFailedPaymentMessage();

        expect(failedTitle).toBe("Payment registration failed");
        expect(failedMessage).toBe(
          "Payment(s) could not be registered for the selected payee(s) as a previous process is still in progress. Please check the downloaded CSV for details and try again later."
        );

        console.log("✅ CSV Validation Passed:", records);
        await newPage.close();
      }
    );

    test(
      "Single Successful Payment Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T25693",
          },
          {
            type: "Description",
            description:
              "Validate that when a payment is registered for a single payee, a toast message pops up displaying the success message for the payment",
          },
          { type: "Precondition", description: "Payouts" },
          {
            type: "Expected Behaviour",
            description:
              "Upon Successfull payment registration, a toast message should be displayed",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const bulkRegisterPaymentsPage1 = new BulkRegisterPayments(page);

        await bulkRegisterPaymentsPage1.navigate("/commissions");

        await bulkRegisterPaymentsPage1.initiatePayment(
          "<EMAIL>",
          "31 Mar",
          "6008"
        );

        await bulkRegisterPaymentsPage1.clickBulkRegister();

        const toastMessage =
          await bulkRegisterPaymentsPage1.getPaymentSuccessToast();

        expect(toastMessage).toBe("Payment(s) registered successfully");
      }
    );

    test(
      "Bulk Register Payment Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T25694",
          },
          {
            type: "Description",
            description:
              "Validate that when a payment is registered for multiple payees, a toast message pops up displaying the success message for the payment",
          },
          { type: "Precondition", description: "Payouts" },
          {
            type: "Expected Behaviour",
            description:
              "Upon Successfull payment registration, a toast message should be displayed",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const bulkRegisterPaymentsPage1 = new BulkRegisterPayments(page);

        await bulkRegisterPaymentsPage1.navigate("/commissions");

        // Select multiple payees
        const payees = [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ];

        await bulkRegisterPaymentsPage1.selectPayees(payees);

        // Select Bulk Register Payment
        await bulkRegisterPaymentsPage1.clickbulkRegisterPayment();

        // Fill in payment amounts in [Actual value, new Value] manner
        const paymentValues = {
          "70,857.79": "7087",
          "11,074.38": "1111",
          "116,074.93": "1160",
          "53,565.27": "5355",
        };

        await bulkRegisterPaymentsPage1.fillPaymentValues(paymentValues);

        await bulkRegisterPaymentsPage1.clickBulkRegister();

        const toastMessage =
          await bulkRegisterPaymentsPage1.getPaymentSuccessToast();

        expect(toastMessage).toBe("Payment(s) registered successfully");
      }
    );

    test.skip(
      "Bulk Concurrent Payment Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T25695, INTER-T25700, INTER-T25702",
          },
          {
            type: "Description",
            description:
              "Initiate a payment for multiple payees and a specific period. Then, from another window, initiate the payment for the same payees and period before the first process completes. Validate that the second payment request is blocked and an error message is displayed. Additionally, a CSV file should be automatically downloaded, containing the payees for whom the second payment request was blocked along with the corresponding error message",
          },
          { type: "Precondition", description: "Payouts" },
          {
            type: "Expected Behaviour",
            description:
              "Upon bulk concurrent payments, the 2nd payment should be blocked and a CSV should be downloaded listing the detail on the failed payees and the reasons",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const bulkRegisterPaymentsPage1 = new BulkRegisterPayments(page);
        const context = await page.context();

        await bulkRegisterPaymentsPage1.navigate("/commissions");
        await bulkRegisterPaymentsPage1.selectAllPayees();
        await bulkRegisterPaymentsPage1.clickbulkRegisterPayment();

        const paymentValues = {
          "6,020.00": "600",
          "198,140.77": "1987",
          "164,790.62": "1631",
        };

        await bulkRegisterPaymentsPage1.fillPaymentValues(paymentValues);

        // Open second tab and prepare same inputs
        const newPage = await context.newPage();
        const bulkRegisterPaymentsPage2 = new BulkRegisterPayments(newPage);
        await bulkRegisterPaymentsPage2.navigate("/commissions");

        await bulkRegisterPaymentsPage2.selectAllPayees();
        await bulkRegisterPaymentsPage2.clickbulkRegisterPayment();
        await bulkRegisterPaymentsPage2.fillPaymentValues(paymentValues);

        // Listen for CSV download from 2nd page
        const downloadPromise = newPage.waitForEvent("download", {
          timeout: 90000,
        });

        // Trigger both bulk register actions concurrently
        await Promise.all([
          bulkRegisterPaymentsPage1.clickBulkRegister(),
          bulkRegisterPaymentsPage2.clickBulkRegister(),
        ]);

        const download = await downloadPromise;

        // Save and read the CSV file
        const downloadPath = path.join(__dirname, "downloads");
        const filePath = path.join(
          downloadPath,
          "Multiple_Blocked_Payments.csv"
        );
        await download.saveAs(filePath);

        const csvContent = fs.readFileSync(filePath, "utf-8");
        const records = parse(csvContent, {
          columns: true,
          skip_empty_lines: true,
        });

        const expectedEntries = [
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
          "<EMAIL>,Previous process in progress",
        ];

        for (const entry of expectedEntries) {
          expect(csvContent).toContain(entry);
        }

        const failedTitle =
          await bulkRegisterPaymentsPage2.getFailedPaymentTitle();
        const failedMessage =
          await bulkRegisterPaymentsPage2.getFailedPaymentMessage();

        expect(failedTitle).toBe("Payment registration failed");
        expect(failedMessage).toBe(
          "Payment(s) could not be registered for the selected payee(s) as a previous process is still in progress. Please check the downloaded CSV for details and try again later."
        );

        console.log("✅ CSV Validation Passed:", records);

        await newPage.close();
      }
    );

    test(
      "Single Payment and Lock Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T25696",
          },
          {
            type: "Description",
            description:
              "Initiate a payment for a payee and period. Then, from another window, lock the payouts for the same payee and period before the first process completes. Validate that the payment process behaves as expected",
          },
          { type: "Precondition", description: "Payouts" },
          {
            type: "Expected Behaviour",
            description:
              "Upon concurrent payment and locking of statements shouldn't affect the payment registration",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const bulkRegisterPaymentsPage1 = new BulkRegisterPayments(page);
        const adjustmentV2 = new AdjustementsV2Page(page);

        const context = await page.context();
        await bulkRegisterPaymentsPage1.navigate("/commissions");

        await adjustmentV2.selectPeriod("Select date", "28 Feb 2025");

        // Select multiple payees
        const payees = [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ];
        await page.reload();

        await bulkRegisterPaymentsPage1.selectPayees(payees);

        // Select Bulk Register Payment
        await bulkRegisterPaymentsPage1.clickbulkRegisterPayment();

        // Open a new tab and attempt the same payment
        const newPage = await context.newPage();
        const bulkRegisterPaymentsPage2 = new BulkRegisterPayments(newPage);
        await bulkRegisterPaymentsPage2.navigate("/commissions");

        await bulkRegisterPaymentsPage2.selectAllPayees();

        await bulkRegisterPaymentsPage2.clickBulkLockStatements();
        await bulkRegisterPaymentsPage1.clickBulkRegister();

        // Bulk Lock Statements
        await bulkRegisterPaymentsPage2.performBulkLockingStatements();
        await newPage.waitForLoadState("networkidle"); // Wait for network to be idle

        const toastMessage =
          await bulkRegisterPaymentsPage1.getPaymentSuccessToast();

        expect(toastMessage).toBe("Payment(s) registered successfully");

        await newPage.close();
      }
    );

    test(
      "Single Payment and Unlock Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T25697",
          },
          {
            type: "Description",
            description:
              "Initiate a payment for a payee and period. Then, from another window, unlock the payouts for the same payee and period before the first process completes. Validate that the payment process behaves as expected",
          },
          { type: "Precondition", description: "Payouts" },
          {
            type: "Expected Behaviour",
            description:
              "Upon concurrent payment and unlocking of statements shouldn't affect the payment registration",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const bulkRegisterPaymentsPage1 = new BulkRegisterPayments(page);
        const adjustmentV2 = new AdjustementsV2Page(page);

        const context = await page.context();
        await bulkRegisterPaymentsPage1.navigate("/commissions");

        await adjustmentV2.selectPeriod("Select date", "31 Jan 2025");
        await page.reload();

        // Bulk Locking the statements
        await bulkRegisterPaymentsPage1.selectAllPayees();

        await bulkRegisterPaymentsPage1.clickBulkLockStatements();
        await bulkRegisterPaymentsPage1.performBulkLockingStatements();
        await bulkRegisterPaymentsPage1.lockValidation();

        // Select multiple payees
        const payees = ["<EMAIL>", "<EMAIL>"];

        await bulkRegisterPaymentsPage1.selectPayees(payees);

        // Select Bulk Register Payment
        await bulkRegisterPaymentsPage1.clickbulkRegisterPayment();

        // Fill in payment amounts in [Actual value, new Value] manner for first page
        const paymentValues = {
          "82,913.41": "8293",
          "151,147.70": "1002",
        };

        await bulkRegisterPaymentsPage1.fillPaymentValues(paymentValues);

        // Open a new tab and attempt the same payment
        const newPage = await context.newPage();
        const bulkRegisterPaymentsPage2 = new BulkRegisterPayments(newPage);
        await bulkRegisterPaymentsPage2.navigate("/commissions");

        await bulkRegisterPaymentsPage2.selectAllPayees();
        await bulkRegisterPaymentsPage2.clickBulkUnlockStatements();

        await bulkRegisterPaymentsPage1.clickBulkRegister();

        // Bulk Un-Lock Statements
        await bulkRegisterPaymentsPage2.performBulkUnLockingStatements();
        await newPage.waitForLoadState("networkidle"); // Wait for network to be idle

        const toastMessage =
          await bulkRegisterPaymentsPage1.getPaymentSuccessToast();

        expect(toastMessage).toBe("Payment(s) registered successfully");

        await newPage.close();
      }
    );

    test(
      "Bulk Payment and Request Approval Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T25698",
          },
          {
            type: "Description",
            description:
              "Initiate a payment for a payee and period. Then, from another window, request the approvals for the same payee and period before the first process completes. Validate that the payment process behaves as expected",
          },
          { type: "Precondition", description: "Payouts" },
          {
            type: "Expected Behaviour",
            description:
              "Upon concurrent payment and requesting approvals of statements shouldn't affect the payment registration",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const bulkRegisterPaymentsPage1 = new BulkRegisterPayments(page);
        const adjustmentV2 = new AdjustementsV2Page(page);

        const context = await page.context();

        await bulkRegisterPaymentsPage1.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "30 Apr 2025");
        await page.reload();

        // Bulk Locking the statements
        await bulkRegisterPaymentsPage1.selectAllPayees();
        await bulkRegisterPaymentsPage1.clickBulkLockStatements();
        await bulkRegisterPaymentsPage1.performBulkLockingStatements();
        await bulkRegisterPaymentsPage1.lockValidation();

        // Select multiple payees
        const payees = [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ];

        await bulkRegisterPaymentsPage1.selectPayees(payees);

        // Select Bulk Register Payment
        await bulkRegisterPaymentsPage1.clickbulkRegisterPayment();

        // Fill in payment amounts in [Actual value, new Value] manner for first page
        const paymentValues = {
          "93,669.27": "9366",
          "60,205.96": "6025",
          "67,123.08": "6712",
        };

        await bulkRegisterPaymentsPage1.fillPaymentValues(paymentValues);

        // Open a new tab and attempt the same payment
        const newPage = await context.newPage();
        const bulkRegisterPaymentsPage2 = new BulkRegisterPayments(newPage);
        await bulkRegisterPaymentsPage2.navigate("/commissions");

        await bulkRegisterPaymentsPage2.selectAllPayees();
        await bulkRegisterPaymentsPage2.clickRequestApproval(
          "Payouts_Workflow_NEW"
        );
        await bulkRegisterPaymentsPage1.clickBulkRegister();

        // Bulk Approval Request
        await bulkRegisterPaymentsPage2.sendApproval();

        const toastMessage =
          await bulkRegisterPaymentsPage1.getPaymentSuccessToast();

        expect(toastMessage).toBe("Payment(s) registered successfully");

        await bulkRegisterPaymentsPage1.navigate("/commissions");

        const approvalStatus =
          await bulkRegisterPaymentsPage1.getApprovalStatus();

        expect(approvalStatus).toBe("Requested");

        await newPage.close();
      }
    );

    // The Bulk Revoke Approval is not working in the CI execution, will go through the issue later
    test.skip(
      "Bulk Payment and Revoke Approval Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T25699",
          },
          {
            type: "Description",
            description:
              "Initiate a payment for a payee and period. Then, from another window, revoke the approvals for the same payee and period before the first process completes. Validate that the payment process behaves as expected",
          },
          { type: "Precondition", description: "Payouts" },
          {
            type: "Expected Behaviour",
            description:
              "Upon concurrent payment and revoking approvals of statements shouldn't affect the payment registration",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const bulkRegisterPaymentsPage1 = new BulkRegisterPayments(page);
        const adjustmentV2 = new AdjustementsV2Page(page);

        const context = await page.context();
        await bulkRegisterPaymentsPage1.navigate("/commissions");

        await adjustmentV2.selectPeriod("Select date", "30 Apr 2025");

        // Select multiple payees
        const payees = [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ];

        await bulkRegisterPaymentsPage1.selectPayees(payees);

        // Select Bulk Register Payment
        await bulkRegisterPaymentsPage1.clickbulkRegisterPayment();

        // Fill in payment amounts in [Actual value, new Value] manner for first page
        const paymentValues = {
          "156,897.69": "1569",
          "151,444.16": "1521",
        };

        await bulkRegisterPaymentsPage1.fillPaymentValues(paymentValues);

        // Open a new tab and attempt the same payment
        const newPage = await context.newPage();
        const bulkRegisterPaymentsPage2 = new BulkRegisterPayments(newPage);
        await bulkRegisterPaymentsPage2.navigate("/commissions");

        await bulkRegisterPaymentsPage2.selectAllPayees();
        await bulkRegisterPaymentsPage2.sendRevokeApproval();
        await bulkRegisterPaymentsPage2.clickRevokeApproval();

        await bulkRegisterPaymentsPage1.clickBulkRegister();
        await page.waitForTimeout(2000);

        // Bulk Approval Request
        await bulkRegisterPaymentsPage2.clickRevokeApproval();

        const toastMessage =
          await bulkRegisterPaymentsPage1.getPaymentSuccessToast();

        expect(toastMessage).toBe("Payment(s) registered successfully");
        await page.waitForTimeout(4000);

        await bulkRegisterPaymentsPage1.navigate("/commissions");

        const approvalStatus =
          await bulkRegisterPaymentsPage1.getApprovalStatus();
        expect(approvalStatus).toBe("Revoked");

        await newPage.close();
      }
    );

    test.skip(
      "Concurent Bulk register with Impersonation as a Manager Validation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T25701",
          },
          {
            type: "Description",
            description:
              "Validate the concurent Bulk register payment with Impersonation as a Manager",
          },
          { type: "Precondition", description: "Payouts" },
          {
            type: "Expected Behaviour",
            description:
              "Concurrent Bulk Register Payments should work on Managers as well",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const bulkRegisterPaymentsPage1 = new BulkRegisterPayments(page);
        const adjustmentV2 = new AdjustementsV2Page(page);

        const context = await page.context();
        await bulkRegisterPaymentsPage1.navigate("/users");
        await adjustmentV2.impersonatePayee("<EMAIL>");

        await bulkRegisterPaymentsPage1.navigate("/commissions");
        await adjustmentV2.selectPeriod("Select date", "31 May 2025");

        // Open a new tab and attempt the same payment
        const newPage = await context.newPage();
        const bulkRegisterPaymentsPage2 = new BulkRegisterPayments(newPage);
        await bulkRegisterPaymentsPage2.navigate("/commissions");

        // Initiate payments concurrently
        await Promise.all([
          bulkRegisterPaymentsPage1.initiatePayment(
            "<EMAIL>",
            "31 May",
            "1470"
          ),
          bulkRegisterPaymentsPage2.initiatePayment(
            "<EMAIL>",
            "31 May",
            "1470"
          ),
        ]);

        await bulkRegisterPaymentsPage1.clickBulkRegister();

        // Ensure button is visible and enabled before clicking
        await expect(
          bulkRegisterPaymentsPage2.getBulkRegisterButton()
        ).toBeVisible();

        await expect(
          bulkRegisterPaymentsPage2.getBulkRegisterButton()
        ).toBeEnabled();

        const [download] = await Promise.all([
          newPage.waitForEvent("download", { timeout: 90000 }), // Wait for the download event
          bulkRegisterPaymentsPage2.clickBulkRegister(),
        ]);

        // Save the file
        const downloadPath = path.join(__dirname, "downloads");
        const filePath = path.join(
          downloadPath,
          "Manager_Blocked_Payments.csv"
        );
        await download.saveAs(filePath);

        // Read and parse the CSV file
        const csvContent = fs.readFileSync(filePath, "utf-8");
        const records = parse(csvContent, {
          columns: true,
          skip_empty_lines: true,
        });

        // ✅ Validate CSV content
        expect(records.length).toBeGreaterThan(0); // Ensure CSV is not empty
        expect(records[0]["Payee Email ID"]).toBe("<EMAIL>"); // Validate Payee Email
        expect(records[0]["Error"]).toBe("Previous process in progress"); // Validate Error Message

        const failedTitle =
          await bulkRegisterPaymentsPage2.getFailedPaymentTitle();

        const failedMessage =
          await bulkRegisterPaymentsPage2.getFailedPaymentMessage();

        expect(failedTitle).toBe("Payment registration failed");
        expect(failedMessage).toBe(
          "Payment(s) could not be registered for the selected payee(s) as a previous process is still in progress. Please check the downloaded CSV for details and try again later."
        );

        console.log("✅ CSV Validation Passed:", records);
        await newPage.close();

        await adjustmentV2.exitImpersonation();
      }
    );
  }
);
