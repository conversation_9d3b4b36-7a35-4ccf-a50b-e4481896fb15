/* eslint-disable playwright/no-wait-for-timeout */
import CommissionsSyncPrevPeriod from "../../../../test-objects/commissionSyncPrevPeriod-objects";
import PayoutFilterPageObjs from "../../../../test-objects/payfilterobj";

const {
  payoutsBalaFixtures: { test, expect },
  payoutsPayeeFixtures: { test: test1, expect: expect1 },
  payoutsPayeeFixtures2: { test: test2, expect: expect2 },
  payoutsBalaFixtures2: { test: test3, expect: expect3 },
  localplaywrightFixtures: { test: test4, expect: expect4 },
} = require("../../../fixtures");
const XLSX = require("xlsx");

test.describe(
  "Payouts Testcases",
  { tag: ["@payouts", "@regression", "@primelogic-5"] },
  () => {
    test.describe("Lock, Unlock, Query, Register", () => {
      test("Statement Lock", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZUBwYXlvdXRzYmFsYS5jb20iLCJwc2QiOiIyMDI0LTAyLTAxIiwicGVkIjoiMjAyNC0wMi0yOSJ9",
          { waitUntil: "networkidle" }
        );
        await expect(page.getByText("payee payouts")).toBeVisible({
          timeout: 60000,
        });
        // Lock and Unlock
        await page.locator("button:nth-child(4)").click();
        await expect(
          page.getByText("Statement locked successfully")
        ).toBeVisible({
          timeout: 45000,
        });
        await expect(
          page
            .locator("div")
            .filter({ hasText: /^Statement Locked$/ })
            .first()
        ).toBeVisible();
      });

      test("Statement UnLock", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZUBwYXlvdXRzYmFsYS5jb20iLCJwc2QiOiIyMDI0LTAyLTAxIiwicGVkIjoiMjAyNC0wMi0yOSJ9",
          { waitUntil: "networkidle" }
        );
        await expect(page.getByText("payee payouts")).toBeVisible({
          timeout: 60000,
        });
        await page.getByRole("button").nth(2).click();
        await expect(
          page.getByText("Statement unlocked successfully")
        ).toBeVisible({
          timeout: 45000,
        });
        await expect(
          page
            .locator("div")
            .filter({ hasText: /^Statement Unlocked$/ })
            .first()
        ).toBeVisible();
      });
      // Raise Query
      test("Raise Query", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZUBwYXlvdXRzYmFsYS5jb20iLCJwc2QiOiIyMDI0LTAyLTAxIiwicGVkIjoiMjAyNC0wMi0yOSJ9",
          { waitUntil: "networkidle" }
        );
        await expect(page.getByText("payee payouts")).toBeVisible({
          timeout: 60000,
        });
        await page.getByRole("button").first().click();
        await page
          .locator("div")
          .filter({ hasText: /^Select category$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "General query" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Select assignee$/ })
          .nth(3)
          .click();
        await page.getByText("superadmin payouts").first().click();
        await page.getByRole("button", { name: "Create" }).click();
        await page.waitForLoadState("networkidle");
        await expect(page.getByText("Query Saved Successfully")).toBeVisible();
      });

      test("Register Payment", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTJAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ==",
          { waitUntil: "networkidle" }
        );
        await expect(page.getByText("payee2 payouts")).toBeVisible({
          timeout: 60000,
        });
        // Register Payment
        await page.getByRole("button").nth(3).click();
        await expect(page.getByText("To be paid")).toBeVisible();
        await expect(
          page.getByText("You are registering full payment")
        ).toBeVisible();
        await page.getByRole("button", { name: "Register" }).click();
        await expect(
          page.getByText("Payment registered successfully")
        ).toBeVisible();
        await expect(
          page.getByText("Payment registered successfully")
        ).toBeHidden();
      });

      test("Plan Details", async ({ adminPage }) => {
        const page = adminPage.page;
        const csPrev = new CommissionsSyncPrevPeriod(page);

        await csPrev.navigate(
          "/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTJAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ=="
        );
        await expect(page.getByText("payee2 payouts")).toBeVisible({
          timeout: 60000,
        });
        // Plan Details
        await page.getByText("Payout from current period").click();
        await page.getByLabel("Payout Summary").getByRole("button").click();
        await expect(
          page.getByText("View Plan Details", { exact: true })
        ).toBeVisible();
        await expect(
          page.getByRole("button", { name: "Plan Summary" })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Simple", exact: true })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Simple Hidden" })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Conditional Hidden" })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Tier Hidden" })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Quota Hidden" })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Settlement Hidden" })
        ).toBeVisible();
        await page.getByLabel("Close").click();

        await page.getByText("Payout from previously").click();
        await page
          .getByRole("row", { name: "Hidden Plans ₹3,000.00" })
          .getByRole("button")
          .click();

        await page.getByLabel("Close").click();

        await page.getByText("Payout from previously").click();
        await page.getByText("Commission Summary").click();
        await page.getByText("Earned Commissions").click();
        await page.getByLabel("Commission Summary").getByRole("button").click();

        await expect(
          page.getByRole("tab", { name: "Simple Hidden" })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Conditional Hidden" })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Tier Hidden" })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Quota Hidden" })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Settlement Hidden" })
        ).toBeVisible();

        await page.getByLabel("Close").click();

        await page.getByText("Deferred Commissions", { exact: true }).click();
        await page
          .getByRole("row", { name: "Hidden Plans -₹" })
          .getByRole("button")
          .click();

        await expect(
          page.getByRole("tab", { name: "Simple Hidden" })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Conditional Hidden" })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Tier Hidden" })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Quota Hidden" })
        ).toBeVisible();
        await expect(
          page.getByRole("tab", { name: "Settlement Hidden" })
        ).toBeVisible();
      });
    });

    // Ensure No Quota for Payee4
    test(" Lock, Register, Profile, Plan", async ({ adminPage }) => {
      const page = adminPage.page;
      const csPrev = new CommissionsSyncPrevPeriod(page);

      await csPrev.navigate(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZUBwYXlvdXRzYmFsYS5jb20iLCJwc2QiOiIyMDI0LTAyLTAxIiwicGVkIjoiMjAyNC0wMi0yOSJ9"
      );

      await expect(page.getByText("payee payouts")).toBeVisible({
        timeout: 60000,
      });
      await expect(page.getByLabel("Payout Summary")).toContainText(
        "Payout from previously deferred commissions₹3,000.00"
      );
      await expect(page.getByLabel("Payout Summary")).toContainText(
        "Payout from current period₹1,41,070.00"
      );
      await expect(page.getByLabel("Payout Summary")).toContainText(
        "Total Payout₹1,44,070.00"
      );

      // Quota Value
      await page.getByText("Quota Attainment").click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByLabel("Quota Attainment")).toContainText(
        "6,000.00of1,500.00400.00%You're on fire!🔥You've achieved400.00%of your target"
      );

      await page.getByText("Payout Summary").click();
      await page.waitForLoadState("networkidle");
      await page.getByText("Payout from current period").click();
      await page.waitForLoadState("networkidle");
      await page.getByText("Hidden Plans").click();
      await page.waitForLoadState("networkidle");
      await page.getByRole("button", { name: "Simple", exact: true }).click();
      await page.waitForLoadState("networkidle");

      // Line Item criteria values
      await page.getByRole("tab", { name: "Simple", exact: true }).click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "₹1,000.00" })
      ).toBeVisible();
      await expect(
        page
          .getByLabel("Simple", { exact: true })
          .getByRole("gridcell", { name: "₹3,000.00" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "₹2,000.00" })
      ).toBeVisible();

      await page.getByRole("tab", { name: "Simple Hidden" }).click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByRole("gridcell", { name: "151010" })).toBeHidden();
      await expect(page.getByRole("gridcell", { name: "151008" })).toBeHidden();
      await expect(page.getByRole("gridcell", { name: "151009" })).toBeHidden();

      await page.getByRole("tab", { name: "Conditional Hidden" }).click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "₹10,000.00" }).first()
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "₹15,000.00" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "₹10,000.00" }).nth(1)
      ).toBeVisible();

      await page.getByRole("tab", { name: "Tier Hidden" }).click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "₹20,000.00" }).first()
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "₹60,000.00" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "₹20,000.00" }).nth(1)
      ).toBeVisible();

      await page.getByRole("tab", { name: "Quota Hidden" }).click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "₹13.33" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "₹40.00" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "₹16.67" })
      ).toBeVisible();
      await page.getByLabel("Close").click();
      await page.waitForLoadState("networkidle");

      // Plan Document
      await page
        .getByRole("row", { name: "Hidden Plans ₹" })
        .getByRole("button")
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("button", { name: "Plan Document" })
      ).toBeVisible();
      await page.getByRole("button", { name: "Plan Document" }).click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByText("No documents present")).toBeVisible();
      await page.getByLabel("Close").click();
      await page.waitForLoadState("networkidle");
      // Under Statements, Earned and Deferred Commission, Criterias
      await page.getByText("Payout Summary").click();
      await page.waitForTimeout(3000);
      await page.getByText("Commission Summary").click();
      await page.waitForLoadState("networkidle");
      await page.getByText("Earned Commissions").click();
      await page.waitForLoadState("networkidle");
      await page
        .getByLabel("Commission Summary")
        .getByText("Hidden Plans")
        .click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "Simple", exact: true })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "Simple Hidden" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "Conditional Hidden" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "Tier Hidden" })
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "Quota Hidden" })
      ).toBeVisible();
      await page.getByText("Deferred Commissions", { exact: true }).click();
      await page.waitForLoadState("networkidle");
      await page.getByText("Hidden Plans").nth(2).click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByRole("gridcell", { name: "Simple" }).first()
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "Simple Hidden" }).first()
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "Conditional Hidden" }).first()
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "Tier Hidden" }).first()
      ).toBeVisible();
      await expect(
        page.getByRole("gridcell", { name: "Quota Hidden" }).first()
      ).toBeVisible();

      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTJAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ==",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("payee2 payouts")).toBeVisible({
        timeout: 60000,
      });
      // Total Payout
      await expect(page.getByLabel("Payout Summary")).toContainText(
        "₹1,41,000.00"
      );
      await expect(page.getByLabel("Payout Summary")).toContainText(
        "₹3,000.00"
      );
      await expect(page.getByLabel("Payout Summary")).toContainText(
        "Total Payout₹1,44,000.00"
      );
      await page.getByText("Commission Summary").click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByLabel("Commission Summary")).toContainText(
        "₹1,47,000.00"
      );
      await expect(page.getByLabel("Commission Summary")).toContainText(
        "-₹6,000.00"
      );

      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTNAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ==",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("payee3 payouts")).toBeVisible({
        timeout: 60000,
      });
      await expect(page.getByLabel("Payout Summary")).toContainText("₹0.00");
      await page.getByText("With Quota").click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByRole("button", { name: "Quota" })).toBeVisible();

      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTRAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ==",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("payee4 payouts")).toBeVisible({
        timeout: 60000,
      });
      await expect(page.getByText("Quota Attainment")).toBeHidden();
    });

    // Checks Payee4 February Payout
    test("Payout Filters", async ({ adminPage }) => {
      const page = adminPage.page;
      const payOut = new PayoutFilterPageObjs(page);

      const key = "commission-view-period";
      const value = "29-February-2024";

      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await page.goto("/commissions", { waitUntil: "networkidle" });
      await payOut.clickFilterButton();
      // Lock Status
      await page
        .locator(
          ".input-left-border-radius-none > .relative > .ant-select > .ant-select-selector"
        )
        .first()
        .click();
      await page
        .getByTestId("calc_status")
        .getByText("Locked Statements", { exact: true })
        .click();
      await page.getByRole("button", { name: "Apply" }).click();
      await page.waitForTimeout(5000);
      expect(
        await page.getByRole("link", { name: "payee payouts" }).count()
      ).toBe(0);
      expect(
        await page.getByRole("link", { name: "payee3 payouts" }).count()
      ).toBe(0);
      await page.locator(".h-14 > div > .ant-btn").first().click();
      await page.getByRole("button", { name: "Clear" }).click();

      // Payouts Status
      await payOut.clickFilterButton();
      await page
        .getByTestId("payment_status")
        .locator("div")
        .filter({ hasText: "Select" })
        .click();
      await page
        .getByTestId("payment_status")
        .locator("span")
        .filter({ hasText: "Zero Payout" })
        .click();
      await page.getByRole("button", { name: "Apply" }).click();
      await expect(
        page.getByRole("link", { name: "payee3 payouts" })
      ).toBeVisible();
      await page.locator(".h-14 > div > .ant-btn").first().click();
      await page.getByRole("button", { name: "Clear" }).click();

      // Payout Frequency
      await payOut.clickFilterButton();
      await page
        .getByTestId("payout_frequency")
        .locator("div")
        .filter({ hasText: "Select" })
        .click();
      await page.locator("span").filter({ hasText: "Annual" }).click();
      await page.getByRole("button", { name: "Apply" }).click();
      await expect(
        page.getByRole("link", { name: "payee payouts" })
      ).toBeHidden();
      await expect(
        page.getByRole("link", { name: "payee2 payouts" })
      ).toBeHidden();
      await expect(
        page.getByRole("link", { name: "payee3 payouts" })
      ).toBeHidden();
      await page.locator(".h-14 > div > .ant-btn").first().click();
      await page.getByRole("button", { name: "Clear" }).click();

      // Plan
      await payOut.clickFilterButton();
      await page
        .getByTestId("commission_plan")
        .locator("div")
        .filter({ hasText: "Select" })
        .click();
      await page.getByText("With Quota").click();
      await page.getByRole("button", { name: "Apply" }).click();
      await expect(
        page.getByRole("link", { name: "payee3 payouts" })
      ).toBeVisible();
      await page.locator(".h-14 > div > .ant-btn").first().click();
      await page.getByRole("button", { name: "Clear" }).click();

      // Manager
      await payOut.clickFilterButton();
      await page
        .locator("[data-testid='reporting_manager']>div.ant-select-selector")
        .click();
      await page.getByText("superadmin payouts").click();
      await page.getByRole("button", { name: "Apply" }).click();
      await expect(
        page.getByRole("link", { name: "payee4 payouts" })
      ).toBeVisible();
      await page.locator(".h-14 > div > .ant-btn").first().click();
      await page.getByRole("button", { name: "Clear" }).click();

      // Designation
      await payOut.clickFilterButton();
      await page.getByRole("textbox", { name: "Type" }).click();
      await page.getByRole("textbox", { name: "Type" }).fill("Payee");
      await page.getByRole("button", { name: "Apply" }).click();
      await expect(
        page.getByRole("link", { name: "payee4 payouts" })
      ).toBeVisible();
      await page.locator(".h-14 > div > .ant-btn").first().click();
      await page.getByRole("button", { name: "Clear" }).click();

      // Pending Amount
      await payOut.clickFilterButton();
      await page
        .locator("div")
        .filter({ hasText: /^Pending AmountEqual To$/ })
        .getByPlaceholder("Type")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Pending AmountEqual To$/ })
        .getByPlaceholder("Type")
        .fill("0");
      await page.getByRole("button", { name: "Apply" }).click();
      await expect(
        page.getByRole("link", { name: "payee3 payouts" })
      ).toBeVisible();
      await page.locator(".h-14 > div > .ant-btn").first().click();
      await page.getByRole("button", { name: "Clear" }).click();

      // Country - employment_country
      await payOut.clickFilterButton();
      await page
        .locator("[data-testid='employment_country']>div.ant-select-selector")
        .click();
      await page.getByText("India").first().click();
      await page.getByRole("button", { name: "Apply" }).click();
      await expect(
        page.getByRole("link", { name: "payee payouts" })
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "payee2 payouts" })
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "payee3 payouts" })
      ).toBeVisible();
      await page.locator(".h-14 > div > .ant-btn").first().click();
      await page.getByRole("button", { name: "Clear" }).click();

      // Currency
      await payOut.clickFilterButton();
      await page
        .locator("[data-testid='pay_currency']>div.ant-select-selector")
        .click();
      await page.getByTitle("INR").nth(1).click();
      await page.getByRole("button", { name: "Apply" }).click();
      await expect(
        page.getByRole("link", { name: "payee payouts" })
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "payee2 payouts" })
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "payee3 payouts" })
      ).toBeVisible();
      await page.locator(".h-14 > div > .ant-btn").first().click();
      await page.getByRole("button", { name: "Clear" }).click();

      // Payout Amount
      await payOut.clickFilterButton();
      await page
        .locator("div")
        .filter({ hasText: /^Payout AmountEqual To$/ })
        .getByPlaceholder("Type")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Payout AmountEqual To$/ })
        .getByPlaceholder("Type")
        .fill("6000");
      await page.getByRole("button", { name: "Apply" }).click();
      await expect(
        page.getByRole("link", { name: "payee4 payouts" })
      ).toBeVisible();
      await page.locator(".h-14 > div > .ant-btn").first().click();
      await page.getByRole("button", { name: "Clear" }).click();

      // Commission
      await payOut.clickFilterButton();
      await page
        .locator("div")
        .filter({ hasText: /^Commission %Equal To$/ })
        .getByPlaceholder("Type")
        .click();
      await page
        .locator("div")
        .filter({ hasText: /^Commission %Equal To$/ })
        .getByPlaceholder("Type")
        .fill("0");
      await page.getByRole("button", { name: "Apply" }).click();
      await expect(
        page.getByRole("link", { name: "payee payouts" })
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "payee2 payouts" })
      ).toBeVisible();
      await expect(
        page.getByRole("link", { name: "payee3 payouts" })
      ).toBeVisible();
      await page.locator(".h-14 > div > .ant-btn").first().click();

      // Export
      await page
        .locator(
          "(//span[.//input[@placeholder='Search by name or email']])[1]/following-sibling::button[2]"
        )
        .click();
      await expect(
        page.getByText("Export 14 payouts that match")
      ).toBeVisible();
      await expect(page.getByText("Export all payouts")).toBeVisible();
      await expect(page.getByRole("button", { name: "Proceed" })).toBeVisible();
    });

    // Zero Payout Register Disable
    test("Register Disable Individual", async ({ adminPage }) => {
      const page = adminPage.page;
      const key = "commission-view-period";
      const value = "29-February-2024";

      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await page.goto("http://localhost:3000/commissions", {
        waitUntil: "networkidle",
      });
      await expect(
        page.getByTestId("<EMAIL>-register-payment")
      ).toBeDisabled();
    });

    // Multi Page Select
    test("Multi Page Select user", async ({ adminPage }) => {
      const page = adminPage.page;
      const key = "commission-view-period";
      const value = "29-February-2024";

      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await page.goto("http://localhost:3000/commissions", {
        waitUntil: "networkidle",
      });
      await page.getByTestId("ever-select").getByText("20").click();
      await page.locator("div").filter({ hasText: /^10$/ }).nth(2).click();
      await page.waitForLoadState("networkidle");
      await page.locator('input[name="row-9"]').check();
      await page
        .locator("div")
        .filter({ hasText: /^1 - / })
        .getByRole("button")
        .nth(2)
        .click();
      await page.waitForLoadState("networkidle");
      await page.locator('input[name="row-0"]').check();
      await expect(page.getByTestId("login-indicator")).toContainText(
        " selected"
      );
      await page
        .locator("div")
        .filter({ hasText: / selected$/ })
        .locator("svg")
        .click();
      await page.getByTestId("ever-select").getByText("10").click();
      await page.locator("div").filter({ hasText: /^20$/ }).nth(2).click();
      await page.waitForLoadState("networkidle");
    });

    test("Export payouts", async ({ adminPage }) => {
      const page = adminPage.page;
      const key = "commission-view-period";
      const value = "29-February-2024";

      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await page.goto("http://localhost:3000/commissions", {
        waitUntil: "networkidle",
      });
      // Export Payouts
      await page
        .locator(
          "(//span[.//input[@placeholder='Search by name or email']])[1]/following-sibling::button[2]"
        )
        .click();
      await expect(page.getByText("Exporting data")).toBeVisible();
      await expect(page.getByText("Successfully Exported")).toBeVisible();
    });

    test("Currency Changeable and Fx Rate Check", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTE4QHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("payee18 payouts")).toBeVisible({
        timeout: 60000,
      });
      await expect(page.getByTestId("login-indicator")).toContainText(
        "Total Payout€4.500,00"
      );
      await page.locator("button:nth-child(3)").click();
      await page.getByText("Global", { exact: true }).click();
      await page.waitForLoadState("networkidle");
      await expect(page.getByTestId("login-indicator")).toContainText(
        "Total Payout$900.00"
      );
    });

    test("Export Currency and Default and User Columns and Without Quota", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTE4QHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
        { waitUntil: "networkidle" }
      );
      await expect(page.getByText("payee18 payouts")).toBeVisible({
        timeout: 60000,
      });
      await page.getByTestId("statement-menu").click();
      await page
        .getByRole("button", { name: "Export statement right" })
        .click();
      await page.getByText("Microsoft Excel (.xlsx)").click();
      const downloadPromise = page.waitForEvent("download");
      await expect(
        await page.getByText("Exporting statements as Excel")
      ).toBeVisible();
      const download = await downloadPromise;
      const filePath = await download.path();
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);
      const currency = data[10].__EMPTY;
      const payout = data[data.length - 1].__EMPTY;
      expect(currency).toBe("EUR");
      expect(payout).toBe(4500);
      expect(data[8]["Statement for February 2024"]).toBe("Reporting Manager");
      expect(data[9]["Statement for February 2024"]).toBe("Country");
      expect(data[10]["Statement for February 2024"]).toBe("Payout Currency");
      expect(data[11]["Statement for February 2024"]).toBe(
        "On-Target Variable Pay"
      );
      expect(data[12]["Statement for February 2024"]).toBe("user");
    });

    test.describe("Change Payout Frequency", () => {
      test("Change Payout Frequency solo", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("http://localhost:3000/users?search=payee17", {
          waitUntil: "networkidle",
        });
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page.waitForLoadState("networkidle");
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page.waitForLoadState("networkidle");
        await page.getByText("Monthly").click();
        await page
          .locator("span")
          .filter({ hasText: /^Quarterly$/ })
          .click();
        await page.getByRole("button", { name: "Save" }).click();
        await page.waitForLoadState("networkidle");
        await page.getByLabel("Save by overwriting current").check();
        await page.getByRole("button", { name: "Save", exact: true }).click();
        await page.waitForLoadState("networkidle");
        await expect(page.getByText("Employee details saved!")).toBeVisible();
      });

      test("Update Payout Frequency - Bulk", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("http://localhost:3000/users", {
          waitUntil: "networkidle",
        });
        await page.getByRole("button", { name: "Import / Export" }).click();
        await page.getByText("Edit Existing Users").click();
        await page.getByRole("checkbox", { name: "Payout Frequency" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        const input = await page
          .locator("span>input[type='file']")
          .setInputFiles(
            "./upload-files/bulk-payout-frequency/bulk_edit_allowed.csv"
          );
        await page.waitForLoadState("networkidle");
        await page.getByRole("button", { name: "Next" }).click();
        await page.waitForLoadState("networkidle");
        await page.getByRole("button", { name: "Next" }).click();
        await page.waitForLoadState("networkidle");
        await expect(page.getByText("Validation Passed")).toBeVisible();
      });
    });

    test("Can't Change Payout Frequency ", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("http://localhost:3000/users?search=payee18", {
        waitUntil: "networkidle",
      });
      await expect(page.getByText("payee18 payouts")).toBeVisible();
      await page.getByTestId("<EMAIL> users dd button").click();
      await page
        .getByRole("menuitem", { name: "Map Payee" })
        .getByRole("button")
        .click();
      await page.getByText("Monthly").click();
      await page
        .locator("span")
        .filter({ hasText: /^Quarterly$/ })
        .click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.getByLabel("Save by overwriting current").check();
      await page.getByRole("button", { name: "Save", exact: true }).click();
      await expect(
        page.getByText(
          "Cannot update the payout frequency for a period when the user has active commission plans."
        )
      ).toBeVisible();
    });

    test("Export With Quota", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTE5QHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDEtMDEiLCJwZWQiOiIyMDI0LTAxLTMxIn0=",
        { waitUntil: "networkidle" }
      );
      await expect(await page.getByText("payee19 payouts")).toBeVisible({
        timeout: 60000,
      });
      await page.getByTestId("statement-menu").click();
      await page
        .getByRole("button", { name: "Export statement right" })
        .click();
      await page.getByText("Microsoft Excel (.xlsx)").click();
      await expect(
        await page.getByText("Exporting statements as Excel")
      ).toBeVisible();
      await page.waitForEvent("download");
      await page.waitForLoadState("networkidle");
      await expect(page.getByText("Downloaded Successfully!!")).toBeVisible({
        timeout: 60000,
      });
    });

    test("Export Without Quota and Only Adjustment", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTE5QHBheW91dHNiYWxhLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
        { waitUntil: "networkidle" }
      );
      await expect(await page.getByText("payee19 payouts")).toBeVisible({
        timeout: 60000,
      });
      await page.getByTestId("statement-menu").click();
      await page
        .getByRole("button", { name: "Export statement right" })
        .click();
      await page.getByText("Microsoft Excel (.xlsx)").click();
      await expect(
        await page.getByText("Exporting statements as Excel")
      ).toBeVisible();
      await page.waitForEvent("download");
      await page.waitForLoadState("networkidle");
      await expect(page.getByText("Downloaded Successfully!!")).toBeVisible({
        timeout: 60000,
      });
    });

    test("Can't Update Payout Frequency - Bulk", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("http://localhost:3000/users", {
        waitUntil: "networkidle",
      });
      await page.getByRole("button", { name: "Import / Export" }).click();
      await page.getByText("Edit Existing Users").click();
      await page.getByRole("checkbox", { name: "Payout Frequency" }).click();
      await page.getByRole("button", { name: "Next" }).click();
      const input = await page
        .locator("span>input[type='file']")
        .setInputFiles(
          "./upload-files/bulk-payout-frequency/bulk_edit_not_allowed.csv"
        );
      await page.waitForLoadState("networkidle");
      await page.getByRole("button", { name: "Next" }).click();
      await page.waitForLoadState("networkidle");
      await page.getByRole("button", { name: "Next" }).click();
      await page.waitForLoadState("networkidle");
      await expect(
        page.getByText(
          "Payroll Effective Start Date: Effective start date should be greater than 01-Jan-2024 +"
        )
      ).toBeVisible();
    });
  }
);

test1(
  "Hidden Criteria With Permission",
  { tag: ["@payouts", "@regression", "@primelogic-5"] },
  async ({ adminPage }) => {
    const page = adminPage.page;
    await page.goto(
      "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZUBwYXlvdXRzYmFsYS5jb20iLCJwc2QiOiIyMDI0LTAyLTAxIiwicGVkIjoiMjAyNC0wMi0yOSJ9",
      { waitUntil: "networkidle" }
    );
    await expect(page.getByText("payee payouts")).toBeVisible({
      timeout: 60000,
    });
    await expect1(
      page.locator("div").filter({
        hasText: /^Denotes components that are hidden from payees\.$/,
      })
    ).toBeVisible();

    await page.getByText("Payout from current period").click();
    await page.getByText("Hidden Plans").click();

    await expect1(
      page.getByRole("row", { name: "Simple Hidden ₹" }).locator("svg")
    ).toBeVisible();
    await expect1(
      page.getByRole("row", { name: "Conditional Hidden ₹" }).locator("svg")
    ).toBeVisible();
    await expect1(
      page.getByRole("row", { name: "Tier Hidden ₹" }).locator("svg")
    ).toBeVisible();
    await expect1(
      page.getByRole("row", { name: "Quota Hidden ₹" }).locator("svg")
    ).toBeVisible();

    await expect1(
      page.getByRole("button", { name: "Simple Hidden" })
    ).toBeVisible();
    await expect1(
      page.getByRole("button", { name: "Conditional Hidden" })
    ).toBeVisible();
    await expect1(
      page.getByRole("button", { name: "Tier Hidden" })
    ).toBeVisible();
    await expect1(
      page.getByRole("button", { name: "Quota Hidden" })
    ).toBeVisible();

    await page.getByText("Commission Summary").click();
    await page.getByText("Earned Commissions").click();
    await page
      .getByLabel("Commission Summary")
      .getByText("Hidden Plans")
      .click();

    await expect1(
      page.getByRole("row", { name: "Simple Hidden ₹" }).locator("svg")
    ).toBeVisible();
    await expect1(
      page.getByRole("row", { name: "Conditional Hidden ₹" }).locator("svg")
    ).toBeVisible();
    await expect1(
      page.getByRole("row", { name: "Tier Hidden ₹" }).locator("svg")
    ).toBeVisible();
    await expect1(
      page.getByRole("row", { name: "Quota Hidden ₹" }).locator("svg")
    ).toBeVisible();

    await expect1(
      page.getByRole("button", { name: "Simple Hidden" })
    ).toBeVisible();
    await expect1(
      page.getByRole("button", { name: "Conditional Hidden" })
    ).toBeVisible();
    await expect1(
      page.getByRole("button", { name: "Tier Hidden" })
    ).toBeVisible();
    await expect1(
      page.getByRole("button", { name: "Quota Hidden" })
    ).toBeVisible();
  }
);

test2(
  "Hidden Criteria Without Permission",
  { tag: ["@payouts", "@regression", "@primelogic-5"] },
  async ({ adminPage }) => {
    const page = adminPage.page;
    await page.goto(
      "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJwYXllZTJAcGF5b3V0c2JhbGEuY29tIiwicHNkIjoiMjAyNC0wMi0wMSIsInBlZCI6IjIwMjQtMDItMjkifQ==",
      { waitUntil: "networkidle" }
    );
    await expect(page.getByText("payee2 payouts")).toBeVisible({
      timeout: 60000,
    });
    await expect2(
      page.locator("div").filter({
        hasText: /^Denotes components that are hidden from payees\.$/,
      })
    ).toBeHidden();

    await page.getByText("Payout from current period").click();
    await page.getByText("Hidden Plans").click();

    await expect2(
      page.getByRole("button", { name: "Simple Hidden" })
    ).toBeHidden();
    await expect2(
      page.getByRole("button", { name: "Conditional Hidden" })
    ).toBeHidden();
    await expect2(
      page.getByRole("button", { name: "Tier Hidden" })
    ).toBeHidden();
    await expect2(
      page.getByRole("button", { name: "Quota Hidden" })
    ).toBeHidden();

    await page.getByText("Commission Summary").click();
    await page.getByText("Earned Commissions").click();
    await page
      .getByLabel("Commission Summary")
      .getByText("Hidden Plans")
      .click();

    await expect2(
      page.getByRole("button", { name: "Simple Hidden" })
    ).toBeHidden();
    await expect2(
      page.getByRole("button", { name: "Conditional Hidden" })
    ).toBeHidden();
    await expect2(
      page.getByRole("button", { name: "Tier Hidden" })
    ).toBeHidden();
    await expect2(
      page.getByRole("button", { name: "Quota Hidden" })
    ).toBeHidden();
  }
);

test3.describe(
  "Payout Lock, Approval, Register Bulk",
  { tag: ["@payouts", "@regression", "@primelogic-5"] },
  async () => {
    test3.beforeEach(async ({ adminPage }) => {
      const page = adminPage.page;
      const key = "commission-view-period";
      const value = "31-January-2024";

      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await page.goto("http://localhost:3000/commissions", {
        waitUntil: "networkidle",
      });
    });

    // Lock
    test3("Lock Individual", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.getByTestId("<EMAIL>-actions-dd").click();
      await page.getByRole("menuitem", { name: "Lock Statements" }).click();
      await expect3(page.getByText("Lock status updated")).toBeVisible();
      await page.waitForLoadState("networkidle");
    });

    // Approval
    test3("Approval Individual", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.getByTestId("<EMAIL>-actions-dd").click();
      await page.getByRole("menuitem", { name: "Request Approval" }).click();
      await page.getByPlaceholder("Choose who needs to be").first().click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByText("Admin test").first().click();
      await page.getByText("Notify when Rejected*").click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByText("Admin test").nth(1).click();
      await page.getByText("Configure approvers, due date").click();
      await page.getByRole("button", { name: "Send Approval Request" }).click();
      await expect3(page.getByText("Approval requested")).toBeVisible();
      await page.waitForLoadState("networkidle");
    });

    // Unlock
    test3("Unlock Individual", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.getByTestId("<EMAIL>-actions-dd").click();
      await page.getByRole("menuitem", { name: "Unlock Statements" }).click();
      await page.getByRole("button", { name: "Confirm" }).click();
      await expect3(page.getByText("Lock status updated")).toBeVisible();
      await page.waitForLoadState("networkidle");
    });

    // Register Partial Payment
    test3("Register Partial Individual", async ({ adminPage }) => {
      const page = adminPage.page;
      await page
        .getByTestId("<EMAIL>-register-payment")
        .click();
      await page
        .getByLabel("Register payment31 Jan")
        .locator('input[type="text"]')
        .click();
      await page
        .getByLabel("Register payment31 Jan")
        .locator('input[type="text"]')
        .fill("10.00");
      await page.getByRole("button", { name: "Register" }).click();
      await expect3(
        page.getByText("Payment registered successfully")
      ).toBeVisible();
      await expect3(
        page.getByText("Payment registered successfully")
      ).toBeHidden();
      await page.waitForLoadState("networkidle");

      await page
        .getByTestId("<EMAIL>-name-cell")
        .locator("xpath=../..")
        .locator(".ag-group-contracted")
        .click();

      await page.getByRole("button", { name: "Invalidate" }).click();
      await expect3(page.getByText("Payment invalidated")).toBeVisible();
      await expect3(page.getByText("Payment invalidated")).toBeHidden();
      await page.waitForLoadState("networkidle");
    });

    // Register Full Payment
    test3("Register Full Individual", async ({ adminPage }) => {
      const page = adminPage.page;
      await page
        .getByTestId("<EMAIL>-register-payment")
        .click();
      await page.getByRole("button", { name: "Register" }).click();
      await expect3(
        page.getByText("Payment registered successfully")
      ).toBeVisible();
      await expect3(
        page.getByText("Payment registered successfully")
      ).toBeHidden();
      await page.waitForLoadState("networkidle");
      await page
        .getByTestId("<EMAIL>-name-cell")
        .locator("xpath=../..")
        .locator(".ag-group-contracted")
        .click();

      await page.getByRole("button", { name: "Invalidate" }).click();
      await expect3(page.getByText("Payment invalidated")).toBeVisible();
      await page.waitForLoadState("networkidle");
    });

    // Bulk Lock
    test3("Bulk Lock", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.locator('input[name="row-0"]').check();
      await page.locator('input[name="row-1"]').check();
      await page.getByText("Lock", { exact: true }).click();
      await page.getByRole("button", { name: "Lock" }).click();
      await expect3(page.getByText("Lock status updated")).toBeVisible();
      await page.waitForLoadState("networkidle");
    });

    // Bulk Approval
    test3("Bulk Approval", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.locator('input[name="row-0"]').check();
      await page.locator('input[name="row-1"]').check();
      await page.getByText("Request Approval").click();
      await page.getByRole("button", { name: "Ok, request approval" }).click();
      await page.getByPlaceholder("Choose who needs to be").first().click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByText("Admin test").first().click();
      await page.getByText("Notify when Rejected*").click();
      await page.getByPlaceholder("Choose approvers").click();
      await page.getByRole("tab", { name: "Users" }).click();
      await page.getByText("Admin test").nth(1).click();
      await page.getByText("Configure approvers, due date").click();
      await page.getByRole("button", { name: "Send Approval Request" }).click();
      await expect3(
        page.getByText("Bulk approval creation task")
      ).toBeVisible();
      await page.waitForLoadState("networkidle");
    });

    // Bulk Unlock
    test3("Bulk Unlock", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.locator('input[name="row-0"]').check();
      await page.locator('input[name="row-1"]').check();
      await page
        .locator("div")
        .filter({ hasText: /^Unlock$/ })
        .click();
      await page.getByRole("button", { name: "Unlock" }).click();
      await expect3(page.getByText("Lock status updated")).toBeVisible();
      await page.waitForLoadState("networkidle");
    });

    // Bulk Register Payment
    test3("Bulk Register", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.locator('input[name="row-0"]').check();
      await page.locator('input[name="row-1"]').check();
      await page.getByText("Register Payment").click();
      await page.getByRole("button", { name: "Register" }).click();
      await expect3(
        page.getByText("Payment registered successfully")
      ).toBeVisible();
      await expect3(
        page.getByText("Payment registered successfully")
      ).toBeHidden();
      await page.waitForLoadState("networkidle");
    });
  }
);

test4.describe(
  "Payouts",
  { tag: ["@payouts", "@regression", "@primelogic-5"] },
  () => {
    test4.beforeEach(async ({ adminPage }) => {
      const page = adminPage.page;
      const key = "commission-view-period";
      const value = "31-July-2023";

      if (await page.getByText("Logged in as").isVisible({ timeout: 10000 })) {
        try {
          await page.getByRole("button", { name: "Exit" }).click();
          await page.waitForTimeout(5000);
        } catch {
          console.log(
            "in before each, unable to click on Exit button for Logged in user"
          );
        }
      }
      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await page.goto("/commissions", { waitUntil: "networkidle" });
    });

    test4("Payout Lock/Unlock", async ({ adminPage }) => {
      const page = adminPage.page;
      await page
        .getByRole("gridcell", { name: "Unpaid" })
        .getByText("Unpaid")
        .waitFor({ state: "visible", timeout: 10000 });
      await page.locator("div.ag-cell-wrapper button").last().click();
      await page.getByText("Lock Statements").click();
      await page
        .getByText("Lock status updated successfully")
        .waitFor({ state: "visible", timeout: 5000 });
      await page.waitForTimeout(5000);
      await page
        .getByText("Lock status updated successfully")
        .waitFor({ state: "hidden", timeout: 10000 });
      await page.locator("div.ag-cell-wrapper button").last().click();
      await page.getByText("Unlock Statements").click();
      await page
        .getByText("Lock status updated successfully")
        .waitFor({ state: "visible", timeout: 5000 });
    });

    test4("Export Payouts", async ({ adminPage }) => {
      const page = adminPage.page;
      await page
        .getByRole("gridcell", { name: "Unpaid" })
        .getByText("Unpaid")
        .waitFor({ state: "visible", timeout: 10000 });
      await page
        .locator(
          "(//span[.//input[@placeholder='Search by name or email']])[1]/following-sibling::button[2]"
        )
        .click();
      await page
        .getByText("Successfully Exported")
        .waitFor({ state: "visible", timeout: 5000 });
    });

    test4("Register Payment", async ({ adminPage }) => {
      const page = adminPage.page;
      await page
        .getByRole("gridcell", { name: "Unpaid" })
        .getByText("Unpaid")
        .waitFor({ state: "visible", timeout: 10000 });
      await page
        .getByTestId("<EMAIL>-register-payment")
        .click();
      await page.locator("div.ant-modal-body input[type='text']").fill("10");
      await page.locator("//button/div[text()='Register']").click();
      await page
        .getByText("Payment registered successfully")
        .waitFor({ state: "visible" });
    });

    test4("Arrear View - Process", async ({ adminPage }) => {
      const page = adminPage.page;
      await page
        .getByRole("gridcell", { name: "Partially Paid" })
        .getByText("Partially Paid")
        .waitFor({ state: "visible", timeout: 10000 });
      await page.getByText("Payouts View").hover();
      await page.getByText("Arrears View").click();
      await page.getByRole("button", { name: "Process" }).click();
      await page
        .getByText("₹90.00")
        .last()
        .waitFor({ state: "visible", timeout: 10000 });
      await page.getByRole("button", { name: "Move" }).click();
      await page
        .getByText("Arrear processed successfully")
        .waitFor({ state: "visible", timeout: 10000 });
    });
  }
);
