const {
  localCloneFixtures: { test, expect },
} = require("../../../../fixtures");
const moment = require("moment");

test.use({ viewport: { width: 1920, height: 1080 } });
test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.goto("/teams", { waitUntil: "networkidle" });
});

test.describe(
  "Teams",
  { tag: ["@teams", "@regression", "@adminchamp-2"] },
  () => {
    test("User should be able to add new team", async ({ adminPage }) => {
      const page = adminPage.page;
      const today = moment();
      const firstDay = today.startOf("month").format("MMM DD, YYYY");
      const lastDay = today.endOf("month").format("MMM DD, YYYY");
      await page.getByRole("tab", { name: "Custom Teams" }).click();
      await page.getByRole("button", { name: "Add Team" }).click();
      await page.getByPlaceholder("Enter Team Name").click();
      await page.getByPlaceholder("Enter Team Name").fill("Playwright Team");
      await page.getByLabel("Payee Name*").click();
      await page.getByText("Abe McDougle").click();
      await page.getByPlaceholder("Start date").click();
      await page.getByPlaceholder("Start date").fill(firstDay);
      await page.getByPlaceholder("Start date").press("Enter");
      await page.getByPlaceholder("End date").click();
      await page.getByPlaceholder("End date").fill(lastDay);
      await page.getByPlaceholder("End date").press("Enter");
      await page.getByRole("button", { name: "Add" }).nth(2).click();
      await page
        .getByText("Team Added Successfully")
        .waitFor({ state: "visible", timeout: 10000 });
      await page
        .getByRole("row", { name: "Playwright Team", exact: true })
        .locator("svg")
        .first()
        .click();
      await page.getByText("Abe McDougle").click();
    });

    test("User should be able to remove team", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.getByRole("tab", { name: "Custom Teams" }).click();
      await page
        .getByRole("row", { name: "Johan" })
        .getByRole("button")
        .nth(1)
        .click();
      await page.getByRole("button", { name: "Yes" }).click();
      await page
        .getByText("Team Deleted Successfully")
        .waitFor({ state: "visible", timeout: 10000 });
    });
  }
);
