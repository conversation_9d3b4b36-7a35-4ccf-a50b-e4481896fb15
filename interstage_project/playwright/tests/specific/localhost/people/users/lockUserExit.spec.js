import LockUser from "../../../../../test-objects/lockUserExit-objects";
const {
  userlockFixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.addInitScript(() => {
    localStorage.clear();
    sessionStorage.clear();
  });
});
test.describe(
  "Lock Statement and Initiate Exit User",
  { tag: ["@regression", "@user", "@adminchamp-1"] },
  () => {
    test(
      "Simple Locked Statement Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8315" },
          {
            type: "Description",
            description:
              "Validate whether the payouts are locked for a particular month or quarter",
          },
          { type: "Precondition", description: "Payouts and Users" },
          {
            type: "Expected Behaviour",
            description:
              "It should display 'Exit Restricted: Statement Locked or Payment/Arrear Registered' and the Confirm button should be disabled",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const lockUser = new LockUser(page);
        await lockUser.navigate("/commissions");
        await lockUser.selectDate("31 Mar 2024");
        await lockUser.searchByNameOrEmail("Payee 1");
        await lockUser.lockStatements("<EMAIL>");
        await lockUser.navigate("/users");
        await lockUser.searchByUsernameOrEmail("<EMAIL>");
        await lockUser.initiateExit("Feb 05, 2024");

        const ErrorMSG = await lockUser.getErrorMessage();
        if (
          ErrorMSG ===
          "Exit Restricted: Statement Locked or Payment/Arrear Registered"
        ) {
          console.log("Validation Passed for Locked Statements");
        } else {
          console.log("Validation Failed");
        }

        const isConfirmButtonDisabled =
          await lockUser.isConfirmButtonDisabled();
        console.log(
          "Send True if the Button is disabled else send False - ",
          isConfirmButtonDisabled
        );

        const results = await lockUser.getResultsifMany();
        console.log(results);
        expect(results[0]).toContain("March 2024");
      }
    );

    test(
      "Locked Statement with Full Payment Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8315" },
          {
            type: "Description",
            description:
              "Validate whether the payouts are registered with full payment for a particular month or quarter for a locked statement",
          },
          { type: "Precondition", description: "Payouts and Users" },
          {
            type: "Expected Behaviour",
            description:
              "It should display 'Exit Restricted: Statement Locked or Payment/Arrear Registered' and the Confirm button should be disabled",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const lockUser = new LockUser(page);
        await lockUser.navigate("/commissions");
        await lockUser.selectDate("31 Mar 2024");
        await lockUser.searchByNameOrEmail("Payee 1");
        await lockUser.registerFullPayment();
        await lockUser.navigate("/users");
        await lockUser.searchByUsernameOrEmail("<EMAIL>");
        await lockUser.initiateExit("Jan 10, 2024");

        const ErrorMSG = await lockUser.getErrorMessage();
        if (
          ErrorMSG ===
          "Exit Restricted: Statement Locked or Payment/Arrear Registered"
        ) {
          console.log("Validation Passed for Locked Statements");
        } else {
          console.log("Validation Failed");
        }

        const isConfirmButtonDisabled =
          await lockUser.isConfirmButtonDisabled();
        console.log(
          "Send True if the Button is disabled else send False - ",
          isConfirmButtonDisabled
        );

        const results = await lockUser.getResultsifMany();
        const inputString = results[0];
        const splitString = inputString.replace(
          /(February|March|April|May|June|July|August|September|October|November|December)/g,
          ",$1"
        );
        const resultList = splitString.split(",");
        console.log(resultList);
        expect(resultList[1]).toBe("February 2024");
        expect(resultList[2]).toBe("March 2024");
      }
    );

    test(
      "Locked Statement with Partial Payment Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8315" },
          {
            type: "Description",
            description:
              "Validate whether the payouts are registered with partial payment for a particular month or quarter for a locked statement",
          },
          { type: "Precondition", description: "Payouts and Users" },
          {
            type: "Expected Behaviour",
            description:
              "It should display 'Exit Restricted: Statement Locked or Payment/Arrear Registered.' and the Confirm button should be disabled",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const lockUser = new LockUser(page);
        await lockUser.navigate("/commissions");
        await lockUser.selectDate("29 Feb 2024");
        await lockUser.searchByNameOrEmail("Payee 1");
        await lockUser.registerPartialPayment("Register payment29 Feb");
        await lockUser.navigate("/users");
        await lockUser.searchByUsernameOrEmail("<EMAIL>");
        await lockUser.initiateExit("Jan 10, 2024");

        const ErrorMSG = await lockUser.getErrorMessage();
        if (
          ErrorMSG ===
          "Exit Restricted: Statement Locked or Payment/Arrear Registered"
        ) {
          console.log("Validation Passed for Locked Statements");
        } else {
          console.log("Validation Failed");
        }

        const isConfirmButtonDisabled =
          await lockUser.isConfirmButtonDisabled();
        console.log(
          "Send True if the Button is disabled else send False - ",
          isConfirmButtonDisabled
        );

        const results = await lockUser.getResultsifMany();
        const inputString = results[0];
        const splitString = inputString.replace(
          /(February|March|April|May|June|July|August|September|October|November|December)/g,
          ",$1"
        );
        const resultList = splitString.split(",");
        console.log(resultList);
        expect(resultList[1]).toBe("February 2024");
        expect(resultList[2]).toBe("March 2024");
      }
    );

    test(
      "Locked Statement with OverPaid Payment Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8315" },
          {
            type: "Description",
            description:
              "Validate whether the payouts are registered with overpaid payment for a particular month or quarter for a locked statement",
          },
          { type: "Precondition", description: "Payouts and Users" },
          {
            type: "Expected Behaviour",
            description:
              "It should display 'Exit Restricted: Statement Locked or Payment/Arrear Registered.' and the Confirm button should be disabled for locked statements and It should display 'Statements generated post the last commission period.' for unlocked statements",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const lockUser = new LockUser(page);
        await lockUser.navigate("/commissions");
        await lockUser.selectDate("31 Mar 2024");
        await lockUser.searchByNameOrEmail("Payee 3");
        await lockUser.lockStatements("<EMAIL>");
        await lockUser.navigate("/users");
        await lockUser.searchByUsernameOrEmail("<EMAIL>");
        await lockUser.initiateExit("Jan 12, 2024");
        const ErrorMSG = await lockUser.getErrorMessage();

        if (
          ErrorMSG ===
          "Exit Restricted: Statement Locked or Payment/Arrear Registered"
        ) {
          console.log("Validation Passed for Locked Statements");
        } else {
          console.log("Validation Failed");
        }

        const WarningMSG = await lockUser.getWarningMessageWithError();
        if (
          WarningMSG === "Statements generated post the last commission period"
        ) {
          console.log("Validation Passed for Un-locked Statements");
        } else {
          console.log("Validation Failed");
        }

        const isConfirmButtonDisabled =
          await lockUser.isConfirmButtonDisabled();
        console.log(
          "Send True if the Button is disabled else send False - ",
          isConfirmButtonDisabled
        );

        // Validation for the locked statements error modal
        const resultsError = await lockUser.getResults(
          "div.ant-alert-error div.ant-alert-description div.flex-col"
        );
        const inputStringError = resultsError[0];
        const splitStringError = inputStringError.replace(
          /(February|March|April|May|June|July|August|September|October|November|December)/g,
          ",$1"
        );
        const resultListError = splitStringError.split(",");
        console.log(resultListError);
        expect(resultListError[1]).toBe("March 2024");

        // Validation for the un-locked statements warning modal
        const resultsWarning = await lockUser.getResults(
          "div.ant-alert-info div.ant-alert-description div.flex-col"
        );
        const inputStringWarning = resultsWarning[0];
        const splitStringWarning = inputStringWarning.replace(
          /(February|March|April|May|June|July|August|September|October|November|December)/g,
          ",$1"
        );
        const resultListWarning = splitStringWarning.split(",");
        console.log(resultListWarning);
        expect(resultListWarning[3]).toBe("February 2024");
      }
    );

    test(
      "New Page Redirect for Locked Statements Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8315" },
          {
            type: "Description",
            description:
              "Validate if clicking the months redirects to the statement for the particular month in a new tab",
          },
          { type: "Precondition", description: "Payouts and Users" },
          {
            type: "Expected Behaviour",
            description:
              "It should redirect to the new page on clicking the particular month",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const lockUser = new LockUser(page);
        await lockUser.navigate("/users");
        await lockUser.searchByUsernameOrEmail("<EMAIL>");
        await lockUser.initiateExit("Feb 05, 2024");

        // Validate whether the new tab is opened or not
        const newPage = await lockUser.clickMonth("March 2024");
        const url = newPage.url();
        console.log("New tab URL:", url);
        await newPage.close();
      }
    );

    test(
      "Same Month Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8315" },
          {
            type: "Description",
            description:
              "Validate whether the validation passes when the commission end date and the statement lock period are the same",
          },
          { type: "Precondition", description: "Payouts and Users" },
          {
            type: "Expected Behaviour",
            description:
              "The Validation for Inititate Exit should pass with a message 'Validation Successful!' and the Confirm button should be enabled",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const lockUser = new LockUser(page);
        await lockUser.navigate("/users");
        await lockUser.searchByUsernameOrEmail("<EMAIL>");
        await lockUser.initiateExit("Mar 15, 2024");

        await lockUser.validateSuccessMessage();
        const isEnabled = await lockUser.isConfirmButtonEnabled();
        console.log(
          "Send True if the Button is enabled else send False - ",
          isEnabled
        );
      }
    );

    test(
      "Locked Statement with Arrears proccessed Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8315" },
          {
            type: "Description",
            description:
              "Validate whether the payouts are arrear processed for a particular month or quarter for a locked statement",
          },
          { type: "Precondition", description: "Payouts and Users" },
          {
            type: "Expected Behaviour",
            description:
              "It should display 'Exit Restricted: Statement Locked or Payment/Arrear Registered' and the Confirm button should be disabled",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const lockUser = new LockUser(page);
        await lockUser.navigate("/commissions");
        await page.waitForTimeout(2000);
        await lockUser.openArrearsView();
        await lockUser.moveToArrear("Payee 2");
        await page.waitForTimeout(2000);
        await lockUser.navigate("/commissions");
        await page.waitForTimeout(2000);
        await lockUser.selectDate("30 Jun 2024");
        await lockUser.lockStatements("<EMAIL>");
        await lockUser.navigate("/users");
        await lockUser.searchByUsernameOrEmail("<EMAIL>");
        await lockUser.initiateExit("Mar 12, 2024");

        const ErrorMSG = await lockUser.getErrorMessage();
        if (
          ErrorMSG ===
          "Exit Restricted: Statement Locked or Payment/Arrear Registered"
        ) {
          console.log("Validation Passed for Locked Statements");
        } else {
          console.log("Validation Failed");
        }

        const isDisabled = await lockUser.isConfirmButtonDisabled();
        console.log(
          "Send True if the Button is disabled else send False - ",
          isDisabled
        );

        const results = [];
        const elements = await page.locator(
          "div.ant-alert-description div.flex-col"
        );
        for (let i = 0; i < (await elements.count()); i++) {
          const text = await elements.nth(i).textContent();
          results.push(text);
        }
        const inputString = results[0];
        console.log(inputString);
        const splitPattern = /(\bQ[1-4] \(\w{3} \d{4} - \w{3} \d{4}\))/;
        const result = inputString.split(splitPattern);
        // Filter out any empty strings that might result from the split
        const filteredResult = result.filter((str) => str.trim().length > 0);
        console.log(filteredResult);
        expect(filteredResult[1]).toBe("Q2 (Apr 2024 - Jun 2024)");
      }
    );

    test(
      "Simple Unlocked Statement Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8315" },
          {
            type: "Description",
            description:
              "Validate whether the payouts are unlocked for a particular month or quarter",
          },
          { type: "Precondition", description: "Payouts and Users" },
          {
            type: "Expected Behaviour",
            description:
              "It should display the warning 'Statements generated post the last commission period' and the Confirm button should be Enabled",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const lockUser = new LockUser(page);
        await lockUser.navigate("/commissions");
        await lockUser.selectDate("31 Mar 2024");
        await lockUser.searchByNameOrEmail("Payee 1");
        await lockUser.invalidatePayment();
        await lockUser.unlockStatements("<EMAIL>");
        await lockUser.navigate("/users");
        await lockUser.searchByUsernameOrEmail("<EMAIL>");
        await lockUser.initiateExit("Feb 20, 2024");

        const WarningMSG = await lockUser.getWarningMessage();
        if (
          WarningMSG === "Statements generated post the last commission period"
        ) {
          console.log("Validation Passed for Un-locked Statements");
        } else {
          console.log("Validation Failed");
        }

        const isEnabled = await lockUser.isConfirmButtonEnabled();
        console.log(
          "Send True if the Button is enabled else send False - ",
          isEnabled
        );

        const validationResult = await lockUser.getResults(
          "div.ant-alert-info div.ant-alert-description div.flex-col"
        );
        const inputstring2 = validationResult[0];
        const splitStringWarning = inputstring2.replace(
          /(February|March|April|May|June|July|August|September|October|November|December)/g,
          ",$1"
        );
        const resultListWarning = splitStringWarning.split(",");
        console.log(resultListWarning);
        expect(resultListWarning[3]).toBe("March 2024");
      }
    );

    test(
      "Unlocked Statement with Full Payment Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8315" },
          {
            type: "Description",
            description:
              "Validate whether the payouts are registered with full payment for a particular month or quarter for a unlocked statement",
          },
          { type: "Precondition", description: "Payouts and Users" },
          {
            type: "Expected Behaviour",
            description:
              "It should display 'Exit Restricted: Statement Locked or Payment/Arrear Registered.' and the Confirm button should be disabled",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const lockUser = new LockUser(page);
        await lockUser.navigate("/commissions");
        await lockUser.selectDate("31 Mar 2024");

        await lockUser.searchByNameOrEmail("Payee 1");
        await lockUser.lockStatements("<EMAIL>");
        await lockUser.registerFullPayment();
        await lockUser.unlockStatements("<EMAIL>");
        await lockUser.navigate("/users");
        await lockUser.searchByUsernameOrEmail("<EMAIL>");
        await lockUser.initiateExit("Feb 10, 2024");

        const ErrorMSG = await lockUser.getErrorMessage();
        if (
          ErrorMSG ===
          "Exit Restricted: Statement Locked or Payment/Arrear Registered"
        ) {
          console.log("Validation Passed for Un-locked Statements");
        } else {
          console.log("Validation Failed");
        }

        const isDisabled = await lockUser.isConfirmButtonDisabled();
        console.log(
          "Send True if the Button is disabled else send False - ",
          isDisabled
        );

        const validationResult = await lockUser.getResultsifMany();
        const inputString = validationResult[0];
        const splitString = inputString.replace(
          /(February|March|April|May|June|July|August|September|October|November|December)/g,
          ",$1"
        );
        const resultList = splitString.split(",");
        console.log(resultList);
        expect(resultList[1]).toBe("March 2024");
      }
    );

    test(
      "Unlocked Statement with Partial Payment Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8315" },
          {
            type: "Description",
            description:
              "Validate whether the payouts are registered with partial payment for a particular month or quarter for a unlocked statement",
          },
          { type: "Precondition", description: "Payouts and Users" },
          {
            type: "Expected Behaviour",
            description:
              "It should display 'Exit Restricted: Statement Locked or Payment/Arrear Registered.' and the Confirm button should be disabled",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const lockUser = new LockUser(page);
        await lockUser.navigate("/commissions");
        await lockUser.selectDate("31 Mar 2024");
        await lockUser.searchByNameOrEmail("Payee 1");
        await lockUser.invalidatePayment();
        await lockUser.registerPartialPayment("Register payment31 Mar");
        await lockUser.selectDate("31 Mar 2024");
        await lockUser.unlockStatements("<EMAIL>");
        await lockUser.navigate("/users");
        await lockUser.searchByUsernameOrEmail("<EMAIL>");
        await lockUser.initiateExit("Feb 18, 2024");

        const ErrorMSG = await lockUser.getErrorMessage();
        if (
          ErrorMSG ===
          "Exit Restricted: Statement Locked or Payment/Arrear Registered"
        ) {
          console.log("Validation Passed for Un-locked Statements");
        } else {
          console.log("Validation Failed");
        }

        const isDisabled = await lockUser.isConfirmButtonDisabled();
        console.log(
          "Send True if the Button is disabled else send False - ",
          isDisabled
        );

        const results = [];
        const elements = await page.locator(
          "div.ant-alert-description div.flex-col"
        );
        for (let i = 0; i < (await elements.count()); i++) {
          const text = await elements.nth(i).textContent();
          results.push(text);
        }
        const inputString = results[0];
        console.log(inputString);
        const resultList = inputString.split(".");
        console.log(resultList);
        expect(resultList[1]).toBe("March 2024");
      }
    );

    test(
      "Unlocked Statement with OverPaid Payment Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8315" },
          {
            type: "Description",
            description:
              "Validate whether the payouts are registered with overpaid payment for a particular month or quarter for a unlocked statement",
          },
          { type: "Precondition", description: "Payouts and Users" },
          {
            type: "Expected Behaviour",
            description:
              "It should display 'Exit Restricted: Statement Locked or Payment/Arrear Registered.' and the Confirm button should be disabled",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const lockUser = new LockUser(page);
        await lockUser.navigate("/users");
        await page.waitForTimeout(5000);
        await lockUser.searchByUsernameOrEmail("<EMAIL>");
        await lockUser.initiateExit("Feb 15, 2024");

        const ErrorMSG = await lockUser.getErrorMessage();
        if (
          ErrorMSG ===
          "Exit Restricted: Statement Locked or Payment/Arrear Registered"
        ) {
          console.log("Validation Passed for Un-locked Statements");
        } else {
          console.log("Validation Failed");
        }

        const isDisabled = await lockUser.isConfirmButtonDisabled();
        console.log(
          "Send True if the Button is disabled else send False - ",
          isDisabled
        );

        const validationResult = await lockUser.getResultsifMany();
        const inputString = validationResult[0];
        const splitString = inputString.replace(
          /(February|March|April|May|June|July|August|September|October|November|December)/g,
          ",$1"
        );
        const resultList = splitString.split(",");
        console.log(resultList);
        expect(resultList[1]).toBe("March 2024");
      }
    );

    test(
      "New Page Redirect for Unlocked Statements Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8315" },
          {
            type: "Description",
            description:
              "Validate if clicking the months redirects to the statement for the particular month in a new tab",
          },
          { type: "Precondition", description: "Payouts and Users" },
          {
            type: "Expected Behaviour",
            description:
              "It should redirect to the new page on clicking the particular month",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const lockUser = new LockUser(page);
        await lockUser.navigate("/commissions");
        await page.waitForTimeout(5000);
        await lockUser.selectDate("31 Mar 2024");
        await lockUser.searchByNameOrEmail("Payee 1");
        await lockUser.invalidatePayment();
        await lockUser.navigate("/users");
        await lockUser.searchByUsernameOrEmail("<EMAIL>");
        await lockUser.initiateExit("Feb 20, 2024");

        // Validate whether the new tab is opened or not
        const newPage = await lockUser.clickMonth("March 2024");
        const url = newPage.url();
        console.log("New tab URL:", url);
        await newPage.close();
      }
    );

    test(
      "Unlocked Statement with Arrears processed Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8315" },
          {
            type: "Description",
            description:
              "Validate whether the payouts are arrear processed for a particular month or quarter for a unlocked statement",
          },
          { type: "Precondition", description: "Payouts and Users" },
          {
            type: "Expected Behaviour",
            description:
              "It should display 'Exit Restricted: Statement Locked or Payment/Arrear Registered' and the Confirm button should be disabled",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const lockUser = new LockUser(page);
        await lockUser.navigate("/commissions");
        await lockUser.selectDate("30 Jun 2024");
        await lockUser.unlockStatements("<EMAIL>");
        await lockUser.navigate("/users");
        await lockUser.searchByUsernameOrEmail("<EMAIL>");
        await lockUser.initiateExit("Mar 20, 2024");

        const ErrorMSG = await lockUser.getErrorMessage();
        if (
          ErrorMSG ===
          "Exit Restricted: Statement Locked or Payment/Arrear Registered"
        ) {
          console.log("Validation Passed for Un-locked Statements");
        } else {
          console.log("Validation Failed");
        }

        const isDisabled = await lockUser.isConfirmButtonDisabled();
        console.log(
          "Send True if the Button is disabled else send False - ",
          isDisabled
        );

        const results = [];
        const elements = await page.locator(
          "div.ant-alert-description div.flex-col"
        );
        for (let i = 0; i < (await elements.count()); i++) {
          const text = await elements.nth(i).textContent();
          results.push(text);
        }
        const inputString = results[0];
        const result = inputString.split(".");
        console.log(result);
        expect(result[1]).toBe("Q2 (Apr 2024 - Jun 2024)");
      }
    );
  }
);
