const {
  playwrightHierarchyFixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.goto("/users", { waitUntil: "networkidle" });
});

test.describe(
  "hierarchyUser Testcases",
  { tag: ["@regression", "@user", "@adminchamp-1"] },
  () => {
    test.describe("Add a new manager and override tests", () => {
      test("Add new manager", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.locator("#reporting-mgr_reportingManagerEmailId").click();
        await page.locator('div[title="Manager 1 Test 1"]').click();
        await page.keyboard.press("Tab");
        await page.getByRole("button", { name: "Save Changes" }).click();
        await expect(
          page.getByText("Hierarchy created successfully!")
        ).toBeVisible();
      });

      test("Override previously set manager", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("switch").click();
        await page.getByRole("button", { name: "Okay" }).click();
        await page
          .locator("#reporting-mgr")
          .getByText("Manager 1 Test")
          .click();
        await page.locator('div[title="Manager 2 Test 2"]').click();
        await page.keyboard.press("Tab");
        await page.getByRole("button", { name: "Save Changes" }).click();
        await expect(
          page.getByText("Hierarchy updated successfully!")
        ).toBeVisible();
      });
    });

    test.describe("Add manager for future period and check tests", () => {
      test("Add future period manager", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page.getByRole("button", { name: "Next" }).click();
        await page
          .locator("#reporting-mgr")
          .getByText("Manager 1 Test")
          .click();
        await page.locator('div[title="Manager 2 Test 2"]').click();
        await page.getByLabel("Effective Start Date*").click();
        await page.getByLabel("Effective Start Date*").fill("Jun 01, 2023");
        await page.getByLabel("Effective Start Date*").press("Enter");
        await page.waitForTimeout(1000);
        await page.keyboard.press("Tab");
        await page.getByRole("button", { name: "Save Changes" }).click();
        await expect(
          page.getByText("Hierarchy updated successfully!")
        ).toBeVisible();
        await page
          .locator("#reporting-mgr")
          .getByText("Manager 2 Test")
          .click();
        await page.locator('div[title="Manager 3 Test 3"]').click();
        await page.getByLabel("Effective Start Date*").click();
        await page.getByLabel("Effective Start Date*").fill("Aug 01, 2023");
        await page.getByLabel("Effective Start Date*").press("Enter");
        await page.keyboard.press("Tab");
        await page.getByRole("button", { name: "Save Changes" }).click();
        await expect(
          page.getByText("Hierarchy updated successfully!").first()
        ).toBeVisible();
      });

      // to check
      test("Check history of managers", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page.getByRole("button", { name: "Next" }).click();
        await expect(page.getByText("MTManager 3 Test")).toBeVisible();
        await expect(page.getByText("Aug 01, 2023")).toBeVisible();
        await expect(page.getByText("MTManager 2 Test")).toBeVisible();
        await expect(page.getByText("Jun 01, 2023")).toBeVisible();
        await expect(page.getByText("Jul 31, 2023")).toBeVisible();
        await expect(page.getByText("MTManager 1 Test")).toBeVisible();
        await expect(page.getByText("Apr 01, 2023")).toBeVisible();
        await expect(page.getByText("May 31, 2023")).toBeVisible();
      });
    });

    test("Not allow circular hierarchy test", async ({ adminPage }) => {
      const page = adminPage.page;
      await page
        .getByTestId("<EMAIL> users dd button")
        .click();
      await page
        .getByRole("menuitem", { name: "Map Payee" })
        .getByRole("button")
        .click();
      await page.getByRole("button", { name: "Next" }).click();
      await page.locator("#reporting-mgr_reportingManagerEmailId").click();
      await page.locator('div[title="TL 1 Test 1"]').click();
      await page.keyboard.press("Tab");
      await page.getByRole("button", { name: "Save Changes" }).click();
      await expect(
        page.getByText(
          "Adding this reporting manager would result in cyclic hierarchy. Please check!"
        )
      ).toBeVisible();
    });

    // full message not displayed
    test("Same payee and reporting manager test", async ({ adminPage }) => {
      const page = adminPage.page;
      await page
        .getByTestId("<EMAIL> users dd button")
        .click();
      await page
        .getByRole("menuitem", { name: "Map Payee" })
        .getByRole("button")
        .click();
      await page.getByRole("button", { name: "Next" }).click();
      await page.locator("#reporting-mgr_reportingManagerEmailId").click();
      await page.locator('div[title="User 4 Test 4"]').click();
      await expect(page.getByText("Payee and reporting manager")).toBeVisible();
    });
  }
);
