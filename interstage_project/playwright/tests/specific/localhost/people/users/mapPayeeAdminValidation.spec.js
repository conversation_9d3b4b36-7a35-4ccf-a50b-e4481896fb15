const {
  mapPayeeFixtures: { test },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.goto("/users", { waitUntil: "networkidle" });
  await page.waitForTimeout(2000);
});

test.describe(
  "Mappayee Validations",
  { tag: ["@regression", "@user", "@adminchamp-1"] },
  () => {
    test("Joining date cannot be modified", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.getByRole("button", { name: "Import / Export" }).click();
      await page.getByText("Edit Existing Users").click();
      await page.getByLabel("Joining Date").check();
      await page.getByRole("button", { name: "Next" }).click();
      await page
        .locator("span>input[type='file']")
        .setInputFiles("./upload-files/override - validation.csv");
      await page.getByRole("button", { name: "Next" }).click();
      await page.locator("//span[text()='Select a column']/..").click();
      await page.getByText("Payroll Effective Start Date").click();
      await page.getByRole("button", { name: "Next" }).click();
      await page
        .getByText("Joining date cannot be modified")
        .waitFor({ state: "visible", timeout: 3000 });
      await page.getByRole("button", { name: "Close" }).click();
    });

    test("Circular Hierarchy -Override", async ({ adminPage }) => {
      const page = adminPage.page;
      await mapPayee(page, "<EMAIL>");
      await page.getByRole("button", { name: "Next" }).click();
      await page.getByRole("switch").first().click();
      await page.getByRole("button", { name: "Okay" }).click();
      await page.locator("#reporting-mgr").getByText("Vivek S").click();
      await page.waitForTimeout(1000);
      await page.keyboard.press("ArrowUp");
      await page.getByText("Vengatesh Shan").click();
      await page
        .locator("#reporting-mgr")
        .getByRole("button", { name: "Save" })
        .click();
      await page
        .getByText(
          "Adding this reporting manager would result in cyclic hierarchy. Please check!"
        )
        .waitFor({ state: "visible", timeout: 5000 });
      await page.screenshot({ path: "Error text.png", fullPage: true });

      await page.getByRole("button", { name: "Close" }).nth(1).click();
    });

    test("Circular Hierarchy - Non Override", async ({ adminPage }) => {
      const page = adminPage.page;
      await mapPayee(page, "<EMAIL>");
      await page.getByRole("button", { name: "Next" }).click();
      await page.locator("#reporting-mgr").getByText("Vivek S").click();
      await page.waitForTimeout(1000);
      await page.keyboard.press("ArrowUp");
      await page.getByText("Vengatesh Shan").last().click();
      await page.getByLabel("Effective Start Date*").click();
      await page.getByRole("cell", { name: "25" }).click();

      await page
        .locator("#reporting-mgr")
        .getByRole("button", { name: "Save" })
        .click();
      await page
        .getByText(
          "Adding this reporting manager would result in cyclic hierarchy. Please check!"
        )
        .waitFor({ state: "visible", timeout: 5000 });
      await page.screenshot({ path: "Error text.png", fullPage: true });

      await page.getByRole("button", { name: "Close" }).nth(1).click();
    });

    test("Frequency Change - Override", async ({ adminPage }) => {
      const page = adminPage.page;
      await mapPayee(page, "<EMAIL>");
      await page.getByTitle("Monthly").click();
      await page.getByTitle("Quarterly").first().click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.locator("input[value='OVERWRITE']").check();
      await page.locator("div.ant-modal-content button").last().click();
      await page
        .getByText(
          "Cannot update the payout frequency for a period when the user has active commission plans."
        )
        .waitFor({ state: "visible", timeout: 5000 });
      await page.getByRole("button", { name: "Cancel" }).last().click();
    });

    test("Frequency Change - Non Override", async ({ adminPage }) => {
      const page = adminPage.page;
      await mapPayee(page, "<EMAIL>");
      await page.getByTitle("Monthly").click();
      await page.getByTitle("Quarterly").first().click();
      await page.getByRole("button", { name: "Save" }).click();
      await page.locator("div.ant-modal-content button").last().click();
      await page.getByRole("textbox", { name: "Select date" }).click();
      await page
        .getByRole("textbox", { name: "Select date" })
        .fill("Feb 13, 2023");
      await page.keyboard.press("Enter");
      await page.locator("div.ant-modal-content button").last().click();
      await page
        .getByText(
          "Cannot update the payout frequency for a period when the user has active commission plans."
        )
        .waitFor({ state: "visible", timeout: 5000 });
      await page.getByRole("button", { name: "Cancel" }).last().click();
    });
  }
);

async function mapPayee(page, email) {
  await page.waitForLoadState("load", { timeout: 20000 });
  await page
    .getByPlaceholder("Search by name or email", { exact: true })
    .fill(email, { timeout: 25000 });
  await page.waitForTimeout(1000);
  await page
    .locator("//span[text()='1']/following-sibling::span[1][text()='rows']")
    .waitFor({ state: "visible", timeout: 10000 });
  await page.waitForTimeout(1000);
  await page.getByTestId(`${email} users dd button`).click();
  await page.getByRole("menuitem", { name: "Map Payee" }).click();
}
