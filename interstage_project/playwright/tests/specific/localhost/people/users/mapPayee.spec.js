const {
  playwrightMapPayeeFixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  await page.goto("/users", { waitUntil: "networkidle" });
});

test.describe(
  "mapPayee Testcases",
  { tag: ["@regression", "@user", "@adminchamp-1"] },
  () => {
    test("Check if user is not allowed to save payroll without providing mandatory payroll fields/custom fields tests", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page
        .getByTestId("<EMAIL> users dd button")
        .click();
      await page
        .getByRole("menuitem", { name: "Map Payee" })
        .getByRole("button")
        .click();
      await page.getByPlaceholder("Enter Designation").click();
      await page.getByPlaceholder("Enter Designation").fill("Payee");
      await page.getByLabel("Crystal Access*").click();
      await page.getByText("Yes").click();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        await page.getByText("Please select joining date!")
      ).toBeVisible();
      await expect(
        await page.getByText("Please select country!")
      ).toBeVisible();
      await expect(
        await page.getByText("Please select currency!")
      ).toBeVisible();
      await expect(await page.getByText("Please select payouts")).toBeVisible();
      await page.getByLabel("Joining Date*").click();
      await page.getByLabel("Joining Date*").fill("Apr 12, 2023");
      await page.getByLabel("Joining Date*").press("Enter");
      await page.getByLabel("Employment Country*").click();
      await page.getByText("India").click();
      await page.getByLabel("Payout Currency*").click();
      await page.locator("span").filter({ hasText: "INR" }).click();
      await page.getByLabel("Payout Frequency*").click();
      await page.locator("span").filter({ hasText: "Monthly" }).click();
      await page.getByLabel("Mandatory Number eff*").click();
      await page.getByLabel("Mandatory Number eff*").fill("1200");
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        await page.getByText("Please enter the field").first()
      ).toBeVisible();
    });

    test.describe("Check if start date cannot be less than the previous effective start date tests", () => {
      test.skip("For payroll history modification", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page
          .getByLabel("Basic & Payroll History")
          .locator("button")
          .click();
        await page.getByLabel("Effective Start Date*").click();
        await page.getByLabel("Effective Start Date*").fill("Oct 21, 2023");
        await page.getByLabel("Effective Start Date*").press("Enter");
        await page
          .getByLabel("Modify Effective Start Date")
          .getByRole("button", { name: "Save" })
          .click();
        await expect(
          page.getByText("Please select effective start date")
        ).toBeVisible();
      });

      test.skip("For custom field history modifcation", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page.getByRole("tab", { name: "Custom Field History" }).click();
        await page.getByLabel("Custom Field History").locator("button").click();
        await page.getByLabel("Effective Start Date*").click();
        await page.getByLabel("Effective Start Date*").fill("Feb 28, 2024");
        await page.getByLabel("Effective Start Date*").press("Enter");
        await page
          .getByLabel("Modify Effective Start Date")
          .getByRole("button", { name: "Save" })
          .click();
        await expect(
          page.getByText("Please select effective start date")
        ).toBeVisible();
      });
    });

    test.describe("Check if user is not allowed to set effective start date beyond joining date tests", () => {
      test.skip("Check for new user", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page.getByLabel("Joining Date*").click();
        await page.getByLabel("Joining Date*").fill("Feb 28, 2024");
        await page.getByLabel("Joining Date*").press("Enter");
        await page.getByPlaceholder("Enter Designation").click();
        await page.getByPlaceholder("Enter Designation").fill("Payee");
        await page.getByLabel("Crystal Access*").click();
        await page.getByText("Yes").click();
        await page.getByLabel("Employment Country*").click();
        await page.getByText("India").click();
        await page.getByLabel("Payout Currency*").click();
        await page.locator("span").filter({ hasText: "EUR" }).click();
        await page.getByLabel("Payout Frequency*").click();
        await page.locator("span").filter({ hasText: "Quarterly" }).click();
        await page.getByLabel("Mandatory Number eff*").click();
        await page.getByLabel("Mandatory Number eff*").fill("1200");
        await page.getByLabel("Mandatory date*").click();
        await page.getByLabel("Mandatory date*").fill("May 27, 2024");
        await page.getByLabel("Mandatory date*").press("Enter");
        await page.getByRole("button", { name: "Save" }).click();
        await page.getByRole("button", { name: "Save", exact: true }).click();
        await expect(page.getByText("Employee details saved!")).toBeVisible();
        await page
          .getByLabel("Basic & Payroll History")
          .locator("button")
          .click();
        await page.getByLabel("Effective Start Date*").click();
        await page.getByLabel("Effective Start Date*").fill("Feb 20, 2024");
        await page.getByLabel("Effective Start Date*").press("Enter");
        await page
          .getByLabel("Modify Effective Start Date")
          .getByRole("button", { name: "Save" })
          .click();
        await expect(
          page.getByText("Please select effective start date")
        ).toBeVisible();
      });

      test.skip("Check for already added user", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page
          .getByLabel("Basic & Payroll History")
          .locator("button")
          .click();
        await page.getByLabel("Effective Start Date*").click();
        await page.getByLabel("Effective Start Date*").fill("Apr 02, 2023");
        await page.getByLabel("Effective Start Date*").press("Enter");
        await page
          .getByLabel("Modify Effective Start Date")
          .getByRole("button", { name: "Save" })
          .click();
        await expect(
          page.getByText("Please select effective start date")
        ).toBeVisible();
      });
    });

    test.describe("Verify whether user is able to save payroll tests", () => {
      test("Add payee details", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page.getByLabel("Joining Date*").click();
        await page.getByLabel("Joining Date*").fill("Jun 14, 2022");
        await page.getByLabel("Joining Date*").press("Enter");
        await page.getByPlaceholder("Enter Employee ID").click();
        await page.getByPlaceholder("Enter Employee ID").fill("1");
        await page.getByPlaceholder("Enter Designation").click();
        await page.getByPlaceholder("Enter Designation").fill("Payee Test");
        await page.getByLabel("Crystal Access*").click();
        await page.getByText("Yes").click();
        await page.getByLabel("Employment Country*").click();
        await page.getByText("United States Of America").click();
        await page.getByLabel("Payout Currency*").click();
        await page.locator("span").filter({ hasText: "USD" }).click();
        await page.getByLabel("Payout Frequency*").click();
        await page.getByText("Half-yearly").click();
        await page.getByPlaceholder("Enter Base Pay").click();
        await page.getByPlaceholder("Enter Base Pay").fill("1200");
        await page.getByLabel("Mandatory Number eff*").click();
        await page.getByLabel("Mandatory Number eff*").fill("1000");
        await page.getByLabel("Mandatory date*").click();
        await page.getByLabel("Mandatory date*").fill("Jan 11, 2022");
        await page.getByLabel("Mandatory date*").press("Enter");
        await page.getByRole("button", { name: "Save" }).click();
        await page.getByRole("button", { name: "Save", exact: true }).click();
        await expect(page.getByText("Employee details saved!")).toBeVisible();
      });

      test("Add future period data", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page.getByText("USD").click();
        await page.locator("span").filter({ hasText: "AUD" }).click();
        await page.getByRole("button", { name: "Save" }).click();
        await page.getByLabel("Save and mark them as").check();
        await page
          .getByLabel("Update User Details")
          .getByRole("button", { name: "Next" })
          .click();
        const dateInput = await page.getByRole("textbox", {
          name: "Select date",
        });
        await dateInput.click();
        await dateInput.fill("Jul 31, 2024");
        await page.getByText("31").click();
        await page.getByRole("button", { name: "Save", exact: true }).click();
        await expect(page.getByText("Employee details saved!")).toBeVisible();
      });

      test("History view - payroll", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await expect(
          page.getByRole("tab", { name: "Basic & Payroll History" })
        ).toBeVisible();
        await expect(
          page.getByRole("button", { name: "Start Date: Jul 31" })
        ).toBeVisible();
        await page.getByRole("button", { name: "Start Date: Jul 31" }).click();
        await expect(page.getByText("Payout Currency:AUD")).toBeVisible();
        await expect(
          page.getByRole("button", { name: "Start Date: Jun 14, 2022 End" })
        ).toBeVisible();
      });
    });

    test.describe("Verify whether user is able to save custom fields tests", () => {
      test("Check if inactive custom fields not present test", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        expect(await page.getByText("Archived Number").isVisible()).toBe(false);
        expect(await page.getByText("Deleted text").isVisible()).toBe(false);
      });

      //   can throw error
      test("Add custom field data", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page.getByText("NM D dropdown").click();
        await page.getByLabel("Joining Date*").click();
        await page.getByLabel("Joining Date*").fill("Jan 01, 2024");
        await page.getByLabel("Joining Date*").press("Enter");
        await page.getByPlaceholder("Enter Employee ID").click();
        await page.getByPlaceholder("Enter Employee ID").fill("2");
        await page.getByPlaceholder("Enter Designation").click();
        await page.getByPlaceholder("Enter Designation").fill("Payee test");
        await page.getByLabel("Crystal Access*").click();
        await page.getByText("Yes").click();
        await page.getByLabel("Employment Country*").click();
        await page.getByText("Australia").click();
        await page.getByLabel("Payout Currency*").click();
        await page.locator("span").filter({ hasText: "AUD" }).click();
        await page.getByLabel("Payout Frequency*").click();
        await page.locator("span").filter({ hasText: "Monthly" }).click();
        await page.getByPlaceholder("Enter Base Pay").click();
        await page.getByPlaceholder("Enter Base Pay").fill("1200");
        await page.getByLabel("NM 1 text eff").click();
        await page.getByLabel("NM 1 text eff").fill("Text check 1");
        await page.getByLabel("NM 2 email eff").click();
        await page
          .getByLabel("NM 2 email eff")
          .fill("<EMAIL>");
        await page.getByLabel("NM 3 number eff").click();
        await page.getByLabel("NM 3 number eff").fill("1000");
        await page.getByLabel("NM 4 dropdown eff").click();
        //   check
        await page.getByText("Yes").nth(3).click();
        await page.getByLabel("NM 5 date eff").click();
        await page.getByText("Today").nth(1).click();
        await page.getByLabel("NM 6 checkbox eff").check();
        await page.getByLabel("NM A text").click();
        await page.getByLabel("NM A text").fill("Text check 2");
        await page.getByLabel("NM B email").click();
        await page.getByLabel("NM B email").fill("<EMAIL>");
        await page.getByLabel("NM C number").click();
        await page.getByLabel("NM C number").fill("2000");
        await page.getByLabel("NM D dropdown").click();
        await page.locator("span").filter({ hasText: "True" }).click();
        await page.getByLabel("NM E date").click();
        await page.getByLabel("NM E date").fill("Feb 20, 2024");
        await page.getByLabel("NM E date").press("Enter");
        await page.getByLabel("NM F checkbox").check();
        await page.getByLabel("Mandatory Number eff*").click();
        await page.getByLabel("Mandatory Number eff*").fill("4000");
        await page.getByLabel("Mandatory date*").click();
        await page.getByLabel("Mandatory date*").fill("Mar 04, 2024");
        await page.getByLabel("Mandatory date*").press("Enter");
        await page.getByRole("button", { name: "Save" }).click();
        await page.getByLabel("Set same date for all fields").check();
        await page.getByRole("button", { name: "Save", exact: true }).click();
        await expect(page.getByText("Employee details saved!")).toBeVisible();
      });

      test("Add future period data for custom field", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page.getByLabel("Mandatory Number eff*").click();
        await page.getByLabel("Mandatory Number eff*").click();
        await page.getByLabel("Mandatory Number eff*").fill("5000");
        await page.getByLabel("NM 1 text eff").click();
        await page.getByLabel("NM 1 text eff").fill("Text check 1 updated");
        await page.getByText("True").click();
        await page.locator("span").filter({ hasText: "False" }).click();
        await page.getByRole("button", { name: "Save" }).click();
        await page.getByLabel("Save and mark them as").check();
        await page
          .getByLabel("Update User Details")
          .getByRole("button", { name: "Next" })
          .click();
        await page.getByLabel("Set same date for all fields").check();
        const dateInput = await page.getByRole("textbox", {
          name: "Select date",
        });
        await dateInput.click();
        await dateInput.fill("Jun 15, 2024");
        await page.getByText("15").click();

        await page.getByRole("button", { name: "Save", exact: true }).click();
        await expect(page.getByText("Employee details saved!")).toBeVisible();
      });

      test("History view - custom field", async ({ adminPage }) => {
        const page = adminPage.page;
        await page
          .getByTestId("<EMAIL> users dd button")
          .click();
        await page
          .getByRole("menuitem", { name: "Map Payee" })
          .getByRole("button")
          .click();
        await page.getByRole("tab", { name: "Custom Field History" }).click();
        await page.getByRole("button", { name: "Start Date: Jun 15" }).click();

        await expect(
          page.getByLabel("Custom Field History").getByText("NM 1 text eff")
        ).toBeVisible();
        await expect(page.getByText("Text check 1 updated")).toBeVisible();
        await expect(
          page.getByText("Mandatory Number eff", { exact: true })
        ).toBeVisible();
        await expect(page.getByText("5000")).toBeVisible();
        await expect(
          page.getByRole("button", { name: "Start Date: Jan 01, 2024 End" })
        ).toBeVisible();
      });
    });
  }
);
