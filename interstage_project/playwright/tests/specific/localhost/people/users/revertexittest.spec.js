import Revertexitpage from "../../../../../test-objects/revertexit-objects";

const {
  revertFixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  const admin = new Revertexitpage(page);
  await admin.gotoUsersPage();
});

test.describe(
  "Revert Exit",
  { tag: ["@regression", "@user", "@adminchamp-1"] },
  () => {
    test("Revert Exit for an inactive payee", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T14186" },
        {
          type: "Description",
          description: "payee 1 is in inactive state - Revert inactive payee",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      const userEmail = "<EMAIL>";
      await admin.searchUser(userEmail);
      await admin.clickRevertExit(userEmail);
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.clickOptions();
      await admin.wait(3000);
      await admin.verifyAndLogVisibility("Initiate Exit", "User not exited");
    });

    test("Revert Exit for a Marked for exit payee", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T14193" },
        {
          type: "Description",
          description:
            "payee 2 is in marked for exit state - Revert Marked for exit payee",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      const userEmail = "<EMAIL>";
      await admin.searchUser(userEmail);
      await admin.clickRevertExit(userEmail);
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.clickOptions();
      await admin.wait(3000);
      await admin.verifyAndLogVisibility("Initiate Exit", "User not exited");
    });

    test("Check Revert Exit is displayed for Marked for deactivation payee", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T14199" },
        {
          type: "Description",
          description: "Revert Marked for deactivation payee",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      await admin.searchUser("payee5@crick");
      await admin.clickOptions();
      await admin.wait(3000);
      await admin.verifyAndLogVisibility("Initiate Exit", "User not exited");
    });

    test("Check Revert Exit is displayed for Marked for Pending exit", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER - T14198" },
        { type: "Description", description: "Revert Marked for Pending exit" },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should not be able to revert exit",
        }
      );

      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      await admin.searchUser("payee4@crick");
      await admin.clickOptions();
      await admin.wait(3000);
      await admin.verifyAndLogVisibility("Initiate Exit", "User not exited");
    });

    test("Initiating exit for reverted user", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T14182" },
        {
          type: "Description",
          description: "Initiating exit for reverted user",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to initiate exit",
        }
      );

      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      const userEmail = "<EMAIL>";
      await admin.searchUser(userEmail);
      await admin.initiateExit(userEmail);
      await admin.selectDate("Jan 06, 2024");
      await admin.validateAction();
      await admin.confirmAction();
      await admin.wait(3000);
      await admin.clickRevertExit(userEmail);
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.initiateExit(userEmail);
      await admin.selectDate("Jan 06, 2024");
      await admin.validateAction();
      await admin.confirmAction();
      await admin.wait(3000);
      await admin.clickOptions();
      await admin.wait(3000);
      await admin.verifyAndLogVisibility("Revert Exit", "User already exited");
    });

    test("Reverting exit for payee in plan", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T14187" },
        {
          type: "Description",
          description: "revert exit for payee added to plan",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      const userEmail = "<EMAIL>";
      await admin.searchUser(userEmail);
      await admin.clickRevertExit(userEmail);
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.clickOptions();
      await admin.wait(3000);
      await admin.verifyAndLogVisibility(
        "Initiate Exit",
        "User already exited"
      );
    });

    test("Reverting exit for Manager of a payee", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T14189" },
        {
          type: "Description",
          description: "revert exit for manager of payee",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      const userEmail = "<EMAIL>";
      await admin.searchUser(userEmail);
      await admin.initiateExit(userEmail);
      await admin.selectDate("Jan 06, 2024");
      await admin.validateAction();
      await admin.confirmAction();
      await admin.wait(1000);
      await admin.clickRevertExit(userEmail);
      await admin.confirmAction();
      await admin.waitForRevertedMessage(2000);
      await admin.clickOptions();
      await admin.wait(1000);
      await admin.verifyAndLogVisibility(
        "Initiate Exit",
        "User already exited"
      );
    });

    test("check whether Revert Exit is displayed for invited payee", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T14197" },
        { type: "Description", description: "Revert invited payee" },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should not be able to revert exit",
        }
      );

      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      await admin.searchUser("hr1@crick");
      await admin.clickOptions();
      await admin.wait(3000);
      await admin.verifyAndLogVisibility("Initiate Exit", "User not exited");
    });

    test("Initiating exit for pending exit user and reverting", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T14200" },
        {
          type: "Description",
          description:
            "Initiating exit for pending exit user and reverting exit",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to initiate exit and revert",
        }
      );

      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      const userEmail = "<EMAIL>";
      await admin.searchUser(userEmail);
      await admin.initiateExit(userEmail);
      await admin.selectDate("Jan 06, 2024");
      await admin.validateAction();
      await admin.confirmAction();
      await admin.wait(2000);
      await admin.clickRevertExit(userEmail);
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.clickOptions();
      await admin.wait(3000);
      await admin.verifyAndLogVisibility("Initiate Exit", "User reverted");
    });

    test("Initiating exit for marked for deactivation user and reverting", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T14201" },
        {
          type: "Description",
          description:
            "Initiating exit for marked for deactivation user and reverting exit",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to initiate exit and revert",
        }
      );

      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      const userEmail = "<EMAIL>";
      await admin.searchUser(userEmail);
      await admin.initiateExit(userEmail);
      await admin.selectDate("Jan 06, 2024");
      await admin.validateAction();
      await admin.confirmAction();
      await admin.wait(2000);
      await admin.clickRevertExit(userEmail);
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.clickOptions();
      await admin.wait(3000);
      await admin.verifyAndLogVisibility("Initiate Exit", "User reverted");
    });
  }
);
