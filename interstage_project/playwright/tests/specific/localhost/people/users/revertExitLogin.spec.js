import { expect } from "@playwright/test";
import Revertexitpage from "../../../../../test-objects/revertexit-objects";
const { test } = require("@playwright/test");

test.describe(
  "Revertexitlogin",
  { tag: ["@user", "@regression", "@adminchamp-1"] },
  () => {
    test("Test whether 'Payee' able to revert exit for 'payee' 'admin' 'superadmin' 'custom role' post enabling 'create & edit' option in roles section,when logged in as payee.", async ({
      page,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T17160, INTER-T17161, INTER-T17162, INTER-T17163 ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );
      const admin = new Revertexitpage(page);
      await admin.login("<EMAIL>", ";p1mYBK.s");
      // Revert exit for a payee
      await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      await admin.initiateExit();
      await admin.selectDate("Sep 01, 2024");
      await admin.validateAction();
      await admin.confirmAction();
      await admin.wait(3000);
      await admin.clickRevertExit();
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.clickOptions();
      await expect(
        page.getByRole("button", { name: "Initiate Exit" })
      ).toBeVisible();
      // Revert exit for a admin
      await admin.searchUser("<EMAIL>");
      await admin.initiateExit();
      await admin.selectDate("Sep 01, 2024");
      await admin.validateAction();
      await admin.confirmAction();
      await admin.wait(3000);
      await admin.clickRevertExit();
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.clickOptions();
      await expect(
        page.getByRole("button", { name: "Initiate Exit" })
      ).toBeVisible();
      // Revert exit for a Superadmin
      await admin.searchUser("<EMAIL>");
      await admin.initiateExit();
      await admin.selectDate("Sep 01, 2024");
      await admin.validateAction();
      await admin.confirmAction();
      await admin.wait(3000);
      await admin.clickRevertExit();
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.clickOptions();
      await expect(
        page.getByRole("button", { name: "Initiate Exit" })
      ).toBeVisible();
      // Revert exit for a customuser
      await admin.searchUser("<EMAIL>");
      await admin.initiateExit();
      await admin.selectDate("Sep 01, 2024");
      await admin.validateAction();
      await admin.confirmAction();
      await admin.wait(3000);
      await admin.clickRevertExit();
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.clickOptions();
      await expect(
        page.getByRole("button", { name: "Initiate Exit" })
      ).toBeVisible();
      await admin.Logout();
      await admin.wait(3000);
      await expect(
        page.getByRole("heading", { name: "Welcome to Everstage" })
      ).toBeVisible();
    });

    test("Test whether user can initiate exit or revert exit for same user when logged in", async ({
      page,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17164",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "The menu should not be displayed",
        }
      );
      const admin = new Revertexitpage(page);
      await admin.login("<EMAIL>", ";p1mYBK.s");
      // Revert exit for a payee
      await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      const isVisible = await page
        .locator("button[data-testid*='users dd button']")
        .isVisible();
      if (isVisible) {
        throw new Error(
          "The element 'button[data-testid*='users dd button']' is visible, but it should not be."
        );
      }
      await admin.Logout();
      await admin.wait(2000);
      await expect(
        page.getByRole("heading", { name: "Welcome to Everstage" })
      ).toBeVisible();
    });
  }
);
