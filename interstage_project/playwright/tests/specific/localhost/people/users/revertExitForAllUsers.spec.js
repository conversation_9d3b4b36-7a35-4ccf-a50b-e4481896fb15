import Revertexitpage from "../../../../../test-objects/revertexit-objects";

const {
  localplaywrightFixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  const admin = new Revertexitpage(page);
  await admin.gotoUsersPage();
});

test.describe(
  "Revert Exit for all",
  { tag: ["@user", "@regression", "@adminchamp-2"] },
  () => {
    test("revert exit for payee by another payee post 'create & edit option' is enabled in roles section,when impersonated.", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T17166" },
        {
          type: "Description",
          description:
            "Initiate exit for a payee, impersonate as a payee, Revert exit for payee",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );
      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      await admin.searchUser("<EMAIL>");
      await admin.initiateExit();
      await admin.selectDate("Sep 01, 2024");
      await admin.validateAction();
      await admin.confirmAction();
      await admin.wait(3000);
      await admin.searchUser("<EMAIL>");
      await admin.impersonateUser();
      await admin.wait(3000);
      await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      await admin.clickRevertExit();
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.exitImpersonation();
      await admin.wait(2000);
      // await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      await admin.wait(2000);
      await admin.clickOptions();
      await admin.wait(2000);
      await expect(
        page.getByRole("button", { name: "Initiate Exit" })
      ).toBeVisible();
    });

    test("revert exit for custom user by a payee post 'create & edit option' is enabled in roles section,when impersonated", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T17159" },
        {
          type: "Description",
          description:
            "Initiate exit for a Custom user, impersonate as a payee, Revert exit for Custom user",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );
      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      await admin.searchUser("<EMAIL>");
      await admin.initiateExit();
      await admin.selectDate("Sep 01, 2024");
      await admin.validateAction();
      await admin.confirmAction();
      await admin.wait(3000);
      await admin.searchUser("<EMAIL>");
      await admin.impersonateUser();
      await admin.wait(3000);
      await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      await admin.clickRevertExit();
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.exitImpersonation();
      await admin.wait(2000);
      await admin.searchUser("<EMAIL>");
      await admin.clickOptions();
      await admin.wait(2000);
      await expect(
        page.getByRole("button", { name: "Initiate Exit" })
      ).toBeVisible();
    });

    test("revert exit for Admin user by a payee post 'create & edit option' is enabled in roles section,when impersonated", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T17157" },
        {
          type: "Description",
          description:
            "Initiate exit for a Admin user, impersonate as a payee, Revert exit for Admin user",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );
      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      await admin.searchUser("<EMAIL>");
      await admin.initiateExit();
      await admin.selectDate("Sep 01, 2024");
      await admin.validateAction();
      await admin.confirmAction();
      await admin.wait(3000);
      await admin.searchUser("<EMAIL>");
      await admin.impersonateUser();
      await admin.wait(3000);
      await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      await admin.clickRevertExit();
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.exitImpersonation();
      await admin.wait(2000);
      await admin.searchUser("<EMAIL>");
      await admin.clickOptions();
      await admin.wait(2000);
      await expect(
        page.getByRole("button", { name: "Initiate Exit" })
      ).toBeVisible();
    });

    test("revert exit for Super Admin user by a payee post 'create & edit option' is enabled in roles section,when impersonated", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T17158" },
        {
          type: "Description",
          description:
            "Initiate exit for a Superadmin user, impersonate as a payee, Revert exit for Superadmin user",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );
      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      await admin.searchUser("<EMAIL>");
      await admin.initiateExit();
      await admin.selectDate("Sep 01, 2024");
      await admin.validateAction();
      await admin.confirmAction();
      await admin.wait(3000);
      await admin.searchUser("<EMAIL>");
      await admin.impersonateUser();
      await admin.wait(3000);
      await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      await admin.clickRevertExit();
      await admin.confirmAction();
      await admin.waitForRevertedMessage(3000);
      await admin.wait(2000);
      await admin.exitImpersonation();
      await admin.wait(3000);
      await admin.searchUser("<EMAIL>");
      await admin.clickOptions();
      await admin.wait(2000);
      await expect(
        page.getByRole("button", { name: "Initiate Exit" })
      ).toBeVisible();
    });

    test("Test whether user can initiate exit or revert exit for same user when impersonated", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T17165" },
        {
          type: "Description",
          description: "Impersonate as a user, check whether menu is displayed",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "The menu should not be displayed",
        }
      );
      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      await admin.searchUser("<EMAIL>");
      await admin.impersonateUser();
      await admin.wait(2000);
      await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      const isVisible = await page
        .locator("button[data-testid*='users dd button']")
        .isVisible();
      if (isVisible) {
        throw new Error(
          "The element 'button[data-testid*='users dd button']' is visible, but it should not be."
        );
      }
      await admin.exitImpersonation();
      await admin.wait(2000);
      await admin.searchUser("<EMAIL>");
      await admin.clickOptions();
      await expect(
        page.getByRole("button", { name: "Initiate Exit" })
      ).toBeVisible();
    });
  }
);
