import Revertexitpage from "../../../../../test-objects/revertexit-objects.js";
import Mappayeeobjs from "../../../../../test-objects/mapPayee-objects.js";
import ManagerEffectiveEndDate from "../../../../../test-objects/managerEffEndDate-objects.js";
import CommissionsSyncPrevPeriod from "../../../../../test-objects/commissionSyncPrevPeriod-objects.js";
import { expect } from "@playwright/test";

const {
  splitEmployeeRecordsFixtures: { test },
} = require("../../../../fixtures.js");

test.describe(
  "split employee records",
  { tag: ["@user", "@regression", "@adminchamp-1"] },
  () => {
    test("split a employee record", async ({ adminPage }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T25884,INTER-T25895,INTER-T25894,INTER-T25893,INTER-T25887,INTER-T25888,INTER-T25889,INTER-T25896,INTER-T25892",
        },
        {
          type: "Description",
          description:
            "Test whether 'Split' option in Basic & Payroll History records",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "'Split' option in Basic & Payroll History records should be working properly",
        }
      );
      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      const mappayeeobj = new Mappayeeobjs(page);
      const managerobj = new ManagerEffectiveEndDate(page);
      await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      await admin.clickOptions();
      await mappayeeobj.clickMapPayee();
      await mappayeeobj.clickaRecordOption(0);
      await mappayeeobj.clickSplit();
      await mappayeeobj.changeEffStartDate(40);
      await mappayeeobj.inputEmployeeId("Lm10");
      await mappayeeobj.inputVariablePay("20000");
      await mappayeeobj.verifyFieldsUpdated(2);
      await mappayeeobj.clickProceed();
      await mappayeeobj.clickApplyChangesandverifyRunCommissionPopup();
      await mappayeeobj.verifytext("Start Date: Feb 10, 2025");
      await page.waitForTimeout(2000);
      // verify payouts is updated before running the sync
      await managerobj.navigate("/commissions");
      await managerobj.setPayoutsDate("Feb 28, 2025");
      await admin.searchUser("<EMAIL>");
      await managerobj.clickPayeeinPayouts("split payee1");
      await expect(page.getByRole("gridcell", { name: "0%" })).toBeVisible();
    });

    test("split a custom field record", async ({ adminPage }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19186,INTER-T25905,INTER-T26197",
        },
        {
          type: "Description",
          description:
            "Verify 'Delete' option is displayed, click Delete user who don't have any commission plan",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to split a custom field record",
        }
      );
      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      const mappayeeobj = new Mappayeeobjs(page);
      await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      await admin.clickOptions();
      await mappayeeobj.clickMapPayee();
      await mappayeeobj.clickCustomFieldHistory();
      await mappayeeobj.clickaRecordOption(2);
      await mappayeeobj.clickSplit();
      await mappayeeobj.changeEffStartDate(10);
      await mappayeeobj.selectdropdownoptioninCF();
      await mappayeeobj.verifyFieldsUpdated(1);
      await mappayeeobj.clickProceed();
      await mappayeeobj.clickApplyChangesandverifyRunCommissionPopup();
      await mappayeeobj.verifytext("Start Date: Jan 11, 2025");
    });

    test("split a reporting manager record", async ({ adminPage }) => {
      test.setTimeout(720000);

      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T25900",
        },
        {
          type: "Description",
          description:
            "verify user able to split a reporting manager record and check changes in hierachy on teams page",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should be able to split a reporting manager record",
        }
      );
      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      const mappayeeobj = new Mappayeeobjs(page);
      const managerobj = new ManagerEffectiveEndDate(page);
      const commobj = new CommissionsSyncPrevPeriod(page);
      await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      await admin.clickOptions();
      await mappayeeobj.clickMapPayee();
      await mappayeeobj.clickNext();
      await mappayeeobj.clickaRecordOption(0);
      await mappayeeobj.clickSplit();
      await mappayeeobj.changeEffStartDate(40);
      await mappayeeobj.selectManager("manager 1", "manager 2");
      await mappayeeobj.verifytext("Reporting manager updated");
      await mappayeeobj.clickProceed();
      await mappayeeobj.clickApplyChangesinEdit();
      await expect(
        page.getByRole("cell", { name: "Feb 10, 2025" })
      ).toBeVisible();
      await managerobj.navigate("/settings/commissions-and-data-sync");
      await commobj.selectPayees("split payee3");
      await commobj.selectDateinCommissions("Feb 28, 2025");
      await commobj.runCommissions();
      await commobj.clickSkipandRun();
      await commobj.waitForCalculationMessage();
      await commobj.waitForCommissionsSuccess();
      await admin.goToTeamspage();
      await page.locator('text="M2"').click();
      await expect(page.getByText("split payee3")).toBeVisible();
    });

    test("split a record and verify results in payout post running the sync", async ({
      adminPage,
    }) => {
      test.setTimeout(720000);
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T25897",
        },
        {
          type: "Description",
          description:
            "Verify by updating the variable pay while splitting the record, run the sync and verify it in payouts",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The commission value should beupdated post running the sync",
        }
      );
      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      const mappayeeobj = new Mappayeeobjs(page);
      const managerobj = new ManagerEffectiveEndDate(page);
      const commobj = new CommissionsSyncPrevPeriod(page);
      await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      await admin.clickOptions();
      await mappayeeobj.clickMapPayee();
      await mappayeeobj.clickaRecordOption(0);
      await mappayeeobj.clickSplit();
      await mappayeeobj.changeEffStartDate(40);
      await mappayeeobj.inputVariablePay("0");
      await mappayeeobj.verifyFieldsUpdated(1);
      await mappayeeobj.clickProceed();
      await mappayeeobj.clickApplyChangesandverifyRunCommissionPopup();
      await mappayeeobj.verifytext("Start Date: Feb 10, 2025");
      await managerobj.navigate("/settings/commissions-and-data-sync");
      await commobj.selectPayees("split payee2");
      await commobj.selectDateinCommissions("Feb 28, 2025");
      await commobj.runCommissions();
      await commobj.clickSkipandRun();
      await commobj.waitForCalculationMessage();
      await commobj.waitForCommissionsSuccess();
      await managerobj.navigate("/commissions");
      await managerobj.setPayoutsDate("Feb 28, 2025");
      await admin.searchUser("<EMAIL>");
      await managerobj.clickPayeeinPayouts("split payee2");
      await expect(page.getByRole("gridcell", { name: "0%" })).toBeVisible();
    });

    test("edit a splitted record, merge and delete record ", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T25903,IINTER-T25904",
        },
        {
          type: "Description",
          description:
            "Verify 'Delete' and 'edit' is working fine for splitted record",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should be able to edit and delete a splitted record",
        }
      );
      const page = adminPage.page;
      const admin = new Revertexitpage(page);
      const mappayeeobj = new Mappayeeobjs(page);
      await admin.gotoUsersPage();
      await admin.searchUser("<EMAIL>");
      await admin.clickOptions();
      await mappayeeobj.clickMapPayee();
      await mappayeeobj.clickNext();
      await mappayeeobj.clickaRecordOption(0);
      await mappayeeobj.clickSplit();
      await mappayeeobj.changeEffStartDate(5);
      await mappayeeobj.selectManager("manager 2", "dummy payee");
      await mappayeeobj.verifytext("Reporting manager updated");
      await mappayeeobj.clickProceed();
      await mappayeeobj.clickApplyChangesinEdit();
      await expect(
        page.getByRole("cell", { name: "Feb 15, 2025" })
      ).toBeVisible();
      await mappayeeobj.clickaRecordOption(1);
      await mappayeeobj.clickEdit();
      await mappayeeobj.selectManager("manager 2", "manager 1");
      await mappayeeobj.verifytext("Reporting manager updated");
      await mappayeeobj.clickProceed();
      await mappayeeobj.clickApply();
      await mappayeeobj.clickMerge();
      await expect(page.getByText("M2")).toBeHidden();
      await mappayeeobj.clickaRecordOption(0);
      await mappayeeobj.clickDelete();
      await expect(page.getByText("CP")).toBeHidden();
    });
  }
);
