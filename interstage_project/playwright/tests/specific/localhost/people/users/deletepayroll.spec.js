import UserPage from "../../../../../test-objects/user-objects.js";
import datasheetPage from "../../../../../test-objects/datasheet-objects.js";
import databookPage from "../../../../../test-objects/databook-objects.js";
import DatasheetV2Page from "../../../../../test-objects/datasheet-v2-objects.js";
const {
  deletePayrollFixtures: { test, expect },
} = require("../../../../fixtures");

test.describe(
  "Delete user in user's module",
  { tag: ["@user", "@regression", "@adminchamp-1"] },
  () => {
    test("Delete CTA should not displayed for least effective start date", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19148",
        },
        {
          type: "Description",
          description:
            "Check whether the user should not able to see the 'delete' button when payroll fields with least effective start date",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      const page = adminPage.page;
      const admin = new UserPage(page);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.editleastPayroll();
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(false);
    });

    test("alert pop up should be displayed after clicking the delete CTA ", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19149,INTER-T19150, INTER-T19151",
        },
        {
          type: "Description",
          description:
            "Alert pop should be displayed after clicking the delete CTA",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should able to see the proper date in the delete pop up",
        }
      );

      const page = adminPage.page;
      const admin = new UserPage(page);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.editPayroll("Start Date: Jan 22");
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(true);
      await admin.deletePayroll();
      const isDateVisible = await admin.datePopup("January 22, 2025");
      if (isDateVisible) {
        console.log("The date 'January 22, 2025' is visible.");
      } else {
        console.log("The date 'January 22, 2025' is NOT visible.");
      }
      const isCancelButtonVisible = await admin.cancelCTA();
      if (isCancelButtonVisible) {
        console.log("Cancel button is visible");
      } else {
        console.log("Cancel button is not visible");
      }
      await admin.deletedialog();
      const isAlertDisplayed = await admin.payrollDeleteAlert();
      if (isAlertDisplayed) {
        console.log("Payroll alert is displayed");
      } else {
        console.log("Payroll alert is not displayed");
      }
    });

    test("User report should updated after deleting the payroll fields", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19166",
        },
        {
          type: "Description",
          description:
            "Alert pop should be displayed after clicking the delete CTA",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should able to see the proper date in the delete pop up",
        }
      );

      const page = adminPage.page;
      const datapage = new datasheetPage(page);
      const databookpage = new databookPage(page);
      const dsV2page = new DatasheetV2Page(page);
      await databookpage.naviagtetoDatabook();
      await databookpage.selectDatabook("Sales-book");
      await datapage.selectDatasheet("user report");
      await dsV2page.letColumnsLoad("Employee Id");
      const value = await datapage.verifyDatasheetValue("fixed_pay");
      console.log("Fixed Pay Value:", value);
      expect(value).toEqual(["0", "100"]);
      await datapage.updateDatasheet();
      await datapage.generateDatasheet();
      const value1 = await datapage.verifyDatasheetValue("fixed_pay");
      console.log("Fixed Pay Value:", value1);
      expect(value1).toEqual(["0"]);
    });

    test("User should able to delete the recent record import via bulk import", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19152",
        },
        {
          type: "Description",
          description:
            "Check whether the user should not able to see the 'delete' button when payroll fields with least effective start date",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      const page = adminPage.page;
      const admin = new UserPage(page);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.openImportExportMenu();
      await admin.EditExistingUsers();
      await admin.editParollfields("Base Pay");
      await admin.nextAction();
      await admin.dataUpload("delete_payroll.csv");
      await admin.nextAction();
      await admin.nextAction();
      await admin.updateBulkuser();
      await admin.acceptBulkImport();
      await admin.startImport();
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.editPayroll("Start Date: Feb 14");
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(true);
      await admin.deletePayroll();
      await admin.deletedialog();
      const isdeleteAlertDisplayed = await admin.payrollDeleteAlert();
      if (isdeleteAlertDisplayed) {
        console.log("Payroll alert is displayed");
      } else {
        console.log("Payroll alert is not displayed");
      }
    });

    test("User should able to delete recent two payroll data", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19153",
        },
        {
          type: "Description",
          description:
            "Check whether the user has three rows of payroll fields, and user should able to delete recent two rows of data",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should be able to delete the recent two rows of data",
        }
      );

      const page = adminPage.page;
      const admin = new UserPage(page);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.editPayroll("Apr 25");
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(true);
      await admin.deletePayroll();
      await admin.deletedialog();
      const isdeleteAlertDisplayed = await admin.payrollDeleteAlert();
      if (isdeleteAlertDisplayed) {
        console.log("Payroll alert is displayed");
      } else {
        console.log("Payroll alert is not displayed");
      }
      await admin.waitForIdleState(5000);
      await admin.editPayroll("Start Date: Mar 28");
      const isTextPresent1 = await admin.checkIfTextExists("Delete");
      expect(isTextPresent1).toBe(true);
      await admin.deletePayroll();
      await admin.deletedialog();
      const isdeleteAlertDisplayed1 = await admin.payrollDeleteAlert();
      if (isdeleteAlertDisplayed1) {
        console.log("Payroll alert is displayed");
      } else {
        console.log("Payroll alert is not displayed");
      }
    });

    test("Delete CTA should not displayed for least effective start date for custom fields", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19154",
        },
        {
          type: "Description",
          description:
            "Check whether the user should not able to see the 'delete' button when custom fields with least effective start date",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should not able to delete the custom fields",
        }
      );

      const page = adminPage.page;
      const admin = new UserPage(page);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.clickCustomField();
      await admin.editleastCustomField();
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(false);
    });

    test("alert pop up should be displayed after clicking the delete CTA for custom fields", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19155",
        },
        {
          type: "Description",
          description:
            "Alert pop should be displayed after clicking the delete CTA for custom fields",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should able to see the proper date in the delete pop up for custom fields",
        }
      );

      const page = adminPage.page;
      const admin = new UserPage(page);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.clickCustomField();
      await admin.editPayroll("Start Date: Jan 16");
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(true);
      await admin.deletePayroll();
      const isDateVisible = await admin.datePopup("January 16, 2024");
      if (isDateVisible) {
        console.log("The date 'January 16, 2024' is visible.");
      } else {
        console.log("The date 'January 16, 2024' is NOT visible.");
      }
      const isCancelButtonVisible = await admin.cancelCTA();
      if (isCancelButtonVisible) {
        console.log("Cancel button is visible");
      } else {
        console.log("Cancel button is not visible");
      }
      await admin.deletedialog();
      const isAlertDisplayed = await admin.customFieldDeleteAlert();
      if (isAlertDisplayed) {
        console.log("custom field delete alert is displayed");
      } else {
        console.log("custom field delete is not displayed");
      }
    });

    test("User should able to delete the recent record import via bulk import for custom fields", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19156",
        },
        {
          type: "Description",
          description:
            "Check whether the user should not able to see the 'delete' button when cutom fields with least effective start date",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should be able to delete the custom fields imported via bulk import",
        }
      );

      const page = adminPage.page;
      const admin = new UserPage(page);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.openImportExportMenu();
      await admin.EditExistingUsers();
      await admin.editcustomfields("name");
      await admin.nextAction();
      await admin.dataUpload("delete_custom.csv");
      await admin.nextAction();
      await admin.nextAction();
      await admin.updateBulkuser();
      await admin.acceptBulkImport();
      await admin.startImport();
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.clickCustomField();
      await admin.editPayroll("Start Date: Apr 15");
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(true);
      await admin.deletePayroll();
      await admin.deletedialog();
      const isdeleteAlertDisplayed = await admin.customFieldDeleteAlert();
      if (isdeleteAlertDisplayed) {
        console.log("custom field alert is displayed");
      } else {
        console.log("custom field alert is not displayed");
      }
    });

    test("User should able to delete recent two custom field data", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19157",
        },
        {
          type: "Description",
          description:
            "Check whether the user has three rows of custom fields, and user should able to delete recent two rows of data",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should be able to delete the recent two rows of data",
        }
      );

      const page = adminPage.page;
      const admin = new UserPage(page);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.clickCustomField();
      await admin.editPayroll("Mar 29");
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(true);
      await admin.deletePayroll();
      await admin.deletedialog();
      const isdeleteAlertDisplayed = await admin.customFieldDeleteAlert();
      if (isdeleteAlertDisplayed) {
        console.log("Custom alert is displayed");
      } else {
        console.log("Custom alert is not displayed");
      }
      await admin.waitForIdleState(5000);
      await admin.editPayroll("Start Date: Feb 21");
      const isTextPresent1 = await admin.checkIfTextExists("Delete");
      expect(isTextPresent1).toBe(true);
      await admin.deletePayroll();
      await admin.deletedialog();
      const isdeleteAlertDisplayed1 = await admin.customFieldDeleteAlert();
      if (isdeleteAlertDisplayed1) {
        console.log("Custom alert is displayed");
      } else {
        console.log("Custom alert is not displayed");
      }
    });

    test("The user should not edit and delete the payroll fields which has been mapped in HRIS ", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T19158, INTER-T19159",
        },
        {
          type: "Description",
          description:
            "The user should not edit and delete the payroll fields which has been mapped in HRIS",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The user should not edit and delete the payroll fields which has been mapped in HRIS",
        }
      );

      const page = adminPage.page;
      const admin = new UserPage(page);
      const userEmail = "<EMAIL>";
      await admin.navigateToUser();
      await admin.fillSearch(userEmail);
      await admin.waitForSearchResults();
      await admin.initiateMapping();
      await admin.editPayroll("Start Date: Jan 07");
      const isTextPresent = await admin.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(false);
      await admin.clickCustomField();
      await admin.editPayroll("Jan 18");
      const isTextPresent1 = await admin.checkIfTextExists("Delete");
      expect(isTextPresent1).toBe(false);
    });
  }
);
