import Revertexitpage from "../../../../../test-objects/revertexit-objects";
import ManagerEffectiveEndDate from "../../../../../test-objects/managerEffEndDate-objects";

const {
  ikUsersFixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  const managerObj = new ManagerEffectiveEndDate(page);
  await managerObj.checkImpersonationandExit();
  await page.addInitScript(() => {
    localStorage.clear();
    sessionStorage.clear();
  });
});

test.describe(
  "Manager Effective End Date Tests",
  { tag: ["@user", "@regression", "@adminchamp-1"] },
  () => {
    test("Map payee screen", async ({ adminPage }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T17592, INTER-T17593, INTER-T17595, INTER-T17626, INTER-T17591, INTER-T17595, INTER-T17596, INTER-T17599",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );
      // 1. Test whether effective end date dropdown is displayed and optional for new user created manually
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      await managerObj.navigateToManagerScreen("<EMAIL>");
      await expect(
        page.locator("label").filter({ hasText: "Effective End Date" }).first()
      ).toBeVisible();

      // 2.Test whether effective start date and effective end date can be same
      await managerObj.setEffEndDateFromStartDate();
      const enddatevalue = await page
        .getByLabel("Effective End Date")
        .getAttribute("title");
      await expect(enddatevalue).toBe("Jan 01, 2024");

      // 3.Test whether manager, eff start and end date resets when reset button is clicked
      await managerObj.clickReset();
      const endDateField = page.getByLabel("Effective End Date");
      const titleValue2 = await endDateField.getAttribute("title");
      const assert = require("assert");
      await expect(endDateField).toHaveValue("");
      assert.strictEqual(titleValue2, "");
      console.log("Effective End Date", endDateField);

      // 4.Test whether error message displayed when effective start is greater than effective end date
      await managerObj.setEndDate("Sep 10, 2024");
      const startdateField = page.locator(
        "#reporting-mgr_reportingEffectiveStartDate"
      );
      // Get the title attribute and save it in a constant
      const titleValue = await startdateField.getAttribute("title");
      console.log("Title value:", titleValue);
      await page.waitForTimeout(3000);
      await managerObj.setStartDate("Sep 20, 2024");
      const titleValue3 = await startdateField.getAttribute("title");
      console.log("Start Date", titleValue3);
      await expect(titleValue3).toBe("Sep 20, 2024");
      // await expect(page.getByText("Effective end date should be")).toBeVisible();
      await page.getByLabel("Close", { exact: true }).click();

      // 5. Test the functionality of overwrite current details flag
      await managerObj.navigateToManagerScreen("<EMAIL>");
      await managerObj.overWriteCurrentDetails();
      await managerObj.setStartDate("Mar 10, 2024");
      await managerObj.setEndDate("Mar 20, 2024");
      await managerObj.savechangesbutton();
      const titleValue4 = await startdateField.getAttribute("title");
      console.log("Start Date", titleValue4);
      expect(titleValue4).toBe("Mar 10, 2024");
      await page.getByLabel("Close", { exact: true }).click();
      // 6. Test whether reporting hierarchy is updated when reporting manager of a payee is changed
      await managerObj.navigateToManagerScreen("<EMAIL>");
      const managerName = await page
        .locator(
          "//*[@id='reporting-mgr_reportingManagerEmailId']/../following-sibling::span"
        )
        .getAttribute("title");
      console.log(managerName);
      await page.getByLabel("Close", { exact: true }).click();
      await page.goto("/teams", { waitUntil: "networkidle" });
      await page.getByText("M1").click();
      await expect(
        page.getByRole("gridcell", { name: "P1 Payee" }).locator("div").first()
      ).toBeVisible();
    });

    test("Validation in Payouts page - Test 1", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17603",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      // 1. Test whether statements are displayed correctly for managers with rbac permission (user can see only teams data) when impersonated - payee1 repoerts to manager1
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      const revertObj = new Revertexitpage(page);
      testInfo.setTimeout(testInfo.timeout + 180000);
      await revertObj.gotoUsersPage();
      await revertObj.searchUser("<EMAIL>");
      await revertObj.wait(3000);
      await revertObj.impersonateUser();
      await revertObj.wait(2000);
      await managerObj.navigate("/commissions");
      await managerObj.setPayoutsDate("Nov 30, 2024");
      await revertObj.wait(2000);
      await expect(
        page.getByText("<EMAIL>", { exact: true })
      ).toBeVisible();
      await expect(
        page.getByText("<EMAIL>", { exact: true })
      ).toBeHidden();
      await revertObj.exitImpersonation();
    });

    test("Validation in Payouts page - Test 2", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17604 ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      // 2. Test whether current reporting manager able to view payout from older period on which payee was reporting to another manager with rbac permission (user can see only teams data) when impersonated
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      const revertObj = new Revertexitpage(page);
      testInfo.setTimeout(testInfo.timeout + 180000);
      await revertObj.gotoUsersPage();
      await revertObj.searchUser("<EMAIL>");
      await revertObj.impersonateUser();
      await revertObj.wait(2000);
      await managerObj.navigate("/commissions");
      await revertObj.wait(2000);
      await managerObj.setPayoutsDate("Jan 31, 2024");
      await revertObj.wait(2000);
      await expect(
        page.getByText("<EMAIL>", { exact: true })
      ).toBeHidden();
      await revertObj.exitImpersonation();
    });

    test("Validation in Payouts page - Test 3", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17605 ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      // 3. Test whether current reporting manager able to view payout from future period on which payee is reporting to another manager with rbac permission (user can see only teams data) when impersonated
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      const revertObj = new Revertexitpage(page);
      testInfo.setTimeout(testInfo.timeout + 180000);
      await revertObj.gotoUsersPage();
      await revertObj.searchUser("<EMAIL>");
      await revertObj.impersonateUser();
      await revertObj.wait(2000);
      await managerObj.navigate("/commissions");
      await revertObj.wait(2000);
      await managerObj.setFutureYearPayoutsDate("Jan 31, 2026");
      await revertObj.wait(2000);
      await expect(
        page.getByText("<EMAIL>", { exact: true })
      ).toBeHidden();
      await revertObj.exitImpersonation();
    });

    test("Validation in Payouts page - Test 4", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17606",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      // 4. Test whether older reporting manager able to view payout from current and future period on which payee is reporting to another manager with rbac permission (user can see only teams data) when impersonated
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      const revertObj = new Revertexitpage(page);
      testInfo.setTimeout(testInfo.timeout + 180000);
      await revertObj.gotoUsersPage();
      await revertObj.searchUser("<EMAIL>");
      await revertObj.impersonateUser();
      await revertObj.wait(2000);
      await managerObj.navigate("/commissions");
      await revertObj.wait(2000);
      await managerObj.setPayoutsDate("Sep 30, 2024");
      await revertObj.wait(2000);
      await expect(
        page.getByText("<EMAIL>", { exact: true })
      ).toBeHidden();
      await revertObj.wait(2000);
      await managerObj.setFutureYearPayoutsDate("Jan 31, 2026");
      await revertObj.wait(2000);
      await expect(
        page.getByText("<EMAIL>", { exact: true })
      ).toBeHidden();
      await revertObj.exitImpersonation();
    });

    test("Validation in Payouts page - Test 5", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17607 ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      // 5. Test whether future reporting manager able to view payout from current and older period on which payee is reporting to another manager with rbac permission (user can see only teams data) when impersonated
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      const revertObj = new Revertexitpage(page);
      testInfo.setTimeout(testInfo.timeout + 180000);
      await revertObj.gotoUsersPage();
      await revertObj.searchUser("<EMAIL>");
      await revertObj.impersonateUser();
      await revertObj.wait(2000);
      await managerObj.navigate("/commissions");
      await managerObj.setPayoutsDate("Jan 31, 2024");
      await revertObj.wait(2000);
      await expect(
        page.getByText("<EMAIL>", { exact: true })
      ).toBeHidden();
      await revertObj.wait(2000);
      await managerObj.setPayoutsDate("Sep 30, 2024");
      await revertObj.wait(2000);
      await expect(
        page.getByText("<EMAIL>", { exact: true })
      ).toBeHidden();
      await revertObj.exitImpersonation();
    });

    test("Validation in Payouts page - Test 6", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17609 ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      // 6. Test whether reporting manager able to view payout from any period on which payee is not reporting to any manager with rbac permission (user can see only teams data) when impersonated
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      const revertObj = new Revertexitpage(page);
      testInfo.setTimeout(testInfo.timeout + 180000);
      await revertObj.gotoUsersPage();
      await revertObj.searchUser("<EMAIL>");
      await revertObj.impersonateUser();
      await revertObj.wait(2000);
      await managerObj.navigate("/commissions");
      await managerObj.setPayoutsDate("Feb 29, 2024");
      await revertObj.wait(2000);
      await expect(
        page.getByText("<EMAIL>", { exact: true })
      ).toBeHidden();
      await revertObj.exitImpersonation();
      await revertObj.wait(2000);
    });

    test("ValidationsRegister Payment", async ({ adminPage }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17612 ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );
      // Test whether current manager able to register payment of reporting payee in current period
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      const revertObj = new Revertexitpage(page);
      await revertObj.gotoUsersPage();
      await revertObj.searchUser("<EMAIL>");
      await revertObj.impersonateUser();
      await revertObj.wait(2000);
      await managerObj.navigate("/commissions");
      await managerObj.setPayoutsDate("Oct 31, 2024");
      await revertObj.wait(2000);
      // CLICK REGISTER BUTTON
      await managerObj.clickregisterPayment("<EMAIL>");
      await managerObj.registerPayment("31 Oct", "9000");
      await expect(
        page.getByText("Payment registered successfully").first()
      ).toBeVisible();
    });

    test("Validations in Quotas/Draws page", async ({ adminPage }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description:
            "INTER-T17615, INTER-T17616, INTER-T17617, INTER-T17618 ",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );
      // Test whether payee is displayed under reporting hierarchy when manager is active for current period in quotas
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      const revertObj = new Revertexitpage(page);
      await managerObj.navigate("/quotas");
      await managerObj.clickPlusbuttoninQuotas();
      await revertObj.wait(3000);
      await expect(page.getByText("Payee 1")).toBeVisible();

      // Test whether payee is displayed seperately when the reporting manager is not active for current period in quotas
      await managerObj.navigate("/quotas");
      await expect(page.getByText("Payee 2")).toBeVisible();

      // Test whether payee is displayed under reporting hierarchy when manager is active for current period in draws
      await managerObj.navigate("/draws");
      await managerObj.clickPlusbuttoninDraws();
      await revertObj.wait(3000);
      await expect(page.getByText("Payee 1")).toBeVisible();

      // Test whether payee is displayed seperately when the reporting manager is not active for current period in Draws
      await managerObj.navigate("/draws");
      await expect(page.getByText("Payee 2")).toBeVisible();
    });

    test("Validations in User Report", async ({ adminPage }) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T17598",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to revert exit",
        }
      );

      // Validate User Report
      const page = adminPage.page;
      const managerObj = new ManagerEffectiveEndDate(page);
      // const revertObj = new Revertexitpage(page);
      await managerObj.navigate("/databook");
      await page.getByRole("link", { name: "Report book" }).click();
      await managerObj.ValidateUserReport("Employee Email Id", "Equal To");
      await expect(
        page.getByRole("gridcell", { name: "<EMAIL>" })
      ).toBeVisible();
    });
  }
);
