import UserPage from "../../../../test-objects/user-objects";
import DashboardPage from "../../../../test-objects/dashboard-objects";

const {
  g2Review: { test, expect },
} = require("../../../fixtures");

const TEST_DATA = {
  PAYEE_EMAIL: `<EMAIL>`,
  ADMIN_EMAIL: `<EMAIL>`,
  SUPER_ADMIN_EMAIL: `<EMAIL>`,
  ACTION: `Login as user`,
};

let dashboardPage;
let userPage;

test.beforeEach(async ({ adminPage }) => {
  const page = adminPage.page;
  dashboardPage = new DashboardPage(page);
  userPage = new UserPage(page);
});

test.describe(
  "G2 Review Form Automation",
  { tag: ["@regression", "@dashboard", "@repconnect-1"] },
  () => {
    /**
     * Helper function to verify G2 Review form details
     * @param {Page} g2Page - Page object representing the G2 review form
     * @param {DashboardPage} dPage - Dashboard page object
     * @param {string} expectedEmail - Expected email to verify on the G2 review form
     */
    const verifyG2ReviewFormDetails = async (g2Page, dPage, expectedEmail) => {
      // Validate that the URL contains 'g2.com'
      const url = g2Page.url();
      await expect(url).toContain('g2.com');
    };

    /**
     * Helper function to for common annotation
     * @param {string} descriptionDetails - Brief description of the test case
     * @param {string} expectedBehavior - Outcome of the test case.
     * @returns {Array<Object>} - Returns an array of annotation objects used in test metadata.
     */

    const customAnnotation = (testId, descriptionDetails, expectedBehavior) => {
      return [
        {
          type: "Test ID",
          description: testId,
        },
        {
          type: "Precondition",
          description:
            "1.Show G2 Review Form feature flag should be enabled{Static/Dynamic} in admin-ui \n2. Users like Admin,Payee and Super Admin should be created and available in Users page.",
        },
        {
          type: "Description",
          description: descriptionDetails,
        },
        {
          type: "Expected Behavior",
          description: expectedBehavior,
        },
      ];
    };

    test(
      "Verify the G2Review Hyperlink as a Power Admin on the general dashboard-1",
      {
        annotation: customAnnotation(
          "INTER-T17863",
          "Validate that the power admin user is unable to view G2 Review Form on the general Dashboard Page.",
          "Power Admin user should not be able to view G2 Review Form on the general Dashboard Page"
        ),
      },
      async () => {
        // Verify Invisibility of the G2Review LInk
        await dashboardPage.cancelImpersonationAndNavigateToGeneralDashboard();
        await expect(dashboardPage.getG2ReviewLinkLocator()).toBeHidden();
      }
    );

    test(
      "Verify the G2Review Hyperlink as a Power Admin on the superset dashboard-2",
      {
        annotation: customAnnotation(
          "INTER-T17864",
          "Validate that the power admin user is unable to view G2 Review Form on the superset Dashboard Page.",
          "Power Admin user should not be able to view G2 Review Form on the superset Dashboard Page"
        ),
      },
      async () => {
        await dashboardPage.clickDashboardsLink();
        await dashboardPage.clickSupersetDashboard();
        // Verify Invisibility of the G2Review Link
        await expect(dashboardPage.getG2ReviewLinkLocator()).toBeHidden();
        await dashboardPage.goBack();
        await dashboardPage.clickGeneralDashboard();
      }
    );

    test(
      "Verify the G2Review Hyperlink as a Payee on the general dashboard-3",
      {
        annotation: customAnnotation(
          "INTER-T17865",
          "Validate that the payee user is able to view G2 Review Form on the general Dashboard Page.",
          "Payee user should be able to view G2 Review Form as on the general Dashboard Page"
        ),
      },
      async () => {
        await userPage.navigateToUser();
        await userPage.clickTripleDotsAndPerformAction(
          TEST_DATA.PAYEE_EMAIL,
          TEST_DATA.ACTION
        );
        await dashboardPage.waitForButton(`Exit`);
        // Verify logged user on the general dashboard before switching into g2 tab.
        expect(await dashboardPage.getLoggedUser()).toContain(
          TEST_DATA.PAYEE_EMAIL
        );
        const g2Page = await dashboardPage.switchToG2ReviewTab();
        // Verify G2 Review form details
        await verifyG2ReviewFormDetails(
          g2Page,
          dashboardPage,
          TEST_DATA.PAYEE_EMAIL
        );
        await dashboardPage.closeNewPageBringFocusBack(g2Page);
      }
    );

    test(
      "Verify the G2Review Hyperlink as a Payee on the superset dashboard-4",
      {
        annotation: customAnnotation(
          "INTER-T17866",
          "Validate that the payee user is able to view G2 Review Form on the superset Dashboard Page.",
          "Payee user should be able to view G2 Review Form as on the superset Dashboard Page"
        ),
      },
      async () => {
        await dashboardPage.clickDashboardsLink();
        await dashboardPage.clickSupersetDashboard();
        const g2Page = await dashboardPage.switchToG2ReviewTab();
        // Verify G2 Review form details
        await verifyG2ReviewFormDetails(
          g2Page,
          dashboardPage,
          TEST_DATA.PAYEE_EMAIL
        );
        await dashboardPage.closeNewPageBringFocusBack(g2Page);
        // Verify logged user on the superset dashboard before exit.
        expect(await dashboardPage.getLoggedUser()).toContain(
          TEST_DATA.PAYEE_EMAIL
        );
        await dashboardPage.waitForButton(`Exit`, true);
      }
    );

    test(
      "Verify the G2 Review hyperlink as an admin on the general dashboard-5",
      {
        annotation: customAnnotation(
          "INTER-T17867",
          "Validate that the Admin user is able to view G2 Review Form on the general dashboard Page.",
          "Admin user should be able to view G2 Review Form on the general dashboard Page"
        ),
      },
      async () => {
        await userPage.clickTripleDotsAndPerformAction(
          TEST_DATA.ADMIN_EMAIL,
          TEST_DATA.ACTION
        );
        await dashboardPage.waitForButton(`Exit`);
        // Verify logged user on the general dashboard before switching into g2 tab.
        expect(await dashboardPage.getLoggedUser()).toContain(
          TEST_DATA.ADMIN_EMAIL
        );
        const g2Page = await dashboardPage.switchToG2ReviewTab();
        // Verify G2 Review form details
        await verifyG2ReviewFormDetails(
          g2Page,
          dashboardPage,
          TEST_DATA.ADMIN_EMAIL
        );
        await dashboardPage.closeNewPageBringFocusBack(g2Page);
      }
    );

    test(
      "Verify the G2 Review hyperlink as an admin on the superset dashboard-6",
      {
        annotation: customAnnotation(
          "INTER-T17868",
          "Validate that the Admin user is able to view G2 Review Form on the superset dashboard Page.",
          "Admin user should be able to view G2 Review Form on the superset dashboard Page"
        ),
      },
      async () => {
        await dashboardPage.clickDashboardsLink();
        await dashboardPage.clickSupersetDashboard();
        const g2Page = await dashboardPage.switchToG2ReviewTab();
        // Verify G2 Review form details
        await verifyG2ReviewFormDetails(
          g2Page,
          dashboardPage,
          TEST_DATA.ADMIN_EMAIL
        );
        await dashboardPage.closeNewPageBringFocusBack(g2Page);
        // Verify logged user on the superset dashboard before exit.
        expect(await dashboardPage.getLoggedUser()).toContain(
          TEST_DATA.ADMIN_EMAIL
        );
        await dashboardPage.waitForButton(`Exit`, true);
      }
    );

    test(
      "Verify the G2Review Hyperlink as a Super Admin on the general dashboard-7",
      {
        annotation: customAnnotation(
          "INTER-T17869",
          "Validate that the super admin user is able to view G2 Review Form on the general Dashboard Page.",
          "Super Admin user should be able to view G2 Review Form on the general Dashboard Page"
        ),
      },
      async () => {
        await userPage.clickTripleDotsAndPerformAction(
          TEST_DATA.SUPER_ADMIN_EMAIL,
          TEST_DATA.ACTION
        );
        await dashboardPage.waitForButton(`Exit`);
        // Verify logged user on the general dashboard before switching into g2 tab.
        expect(await dashboardPage.getLoggedUser()).toContain(
          TEST_DATA.SUPER_ADMIN_EMAIL
        );
        const g2Page = await dashboardPage.switchToG2ReviewTab();
        // Verify G2 Review form details
        await verifyG2ReviewFormDetails(
          g2Page,
          dashboardPage,
          TEST_DATA.SUPER_ADMIN_EMAIL
        );
        await dashboardPage.closeNewPageBringFocusBack(g2Page);
      }
    );

    test(
      "Verify the G2Review Hyperlink as a Super Admin on the superset dashboard-8",
      {
        annotation: customAnnotation(
          "INTER-T17870",
          "Validate that the super admin user is able to view G2 Review Form on the superset Dashboard Page.",
          "Super Admin user should be able to view G2 Review Form on the superset Dashboard Page"
        ),
      },
      async () => {
        await dashboardPage.clickDashboardsLink();
        await dashboardPage.clickSupersetDashboard();
        const g2Page = await dashboardPage.switchToG2ReviewTab();
        // Verify G2 Review form details
        await verifyG2ReviewFormDetails(
          g2Page,
          dashboardPage,
          TEST_DATA.SUPER_ADMIN_EMAIL
        );
        await dashboardPage.closeNewPageBringFocusBack(g2Page);
        // Verify logged user on the superset dashboard before exit.
        expect(await dashboardPage.getLoggedUser()).toContain(
          TEST_DATA.SUPER_ADMIN_EMAIL
        );
        await dashboardPage.waitForButton(`Exit`, true);
      }
    );
  }
);
