import CustomObjectsV3 from "../../../../test-objects/customObjectsV3";

/* eslint-disable playwright/no-wait-for-timeout */
const {
  objectPermissionsFixtures: { test, expect },
} = require("../../../fixtures");

// test.use({ viewport: { width: 1620, height: 920 } });
test.beforeEach(async ({ adminPage, payeePage }) => {
  const customObjV3_pageAdmin = new CustomObjectsV3(adminPage.page);
  const customObjV3_pagePayee = new CustomObjectsV3(payeePage.page);

  const pageAdmin = adminPage.page;
  const pagePayee = payeePage.page;
  await customObjV3_pageAdmin.navigateToObjectsPage();
  await customObjV3_pagePayee.navigateToObjectsPage();
});

test.describe(
  "Custom and report object permissions",
  { tag: ["@regression", "@connectors", "@rbac", "@launchpad-1"] },
  () => {
    test("User should be able to complete basic flow", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.getByText("Base Object").click();
      await expect(
        await page.getByRole("button", { name: "Manage Permissions" })
      ).toBeVisible();
      await page.getByRole("button", { name: "Manage Permissions" }).click();
      await expect(
        await page.locator("label").filter({ hasText: "Everyone can access" })
      ).toBeVisible();
      await expect(
        await page.locator("label").filter({ hasText: "Include only specific" })
      ).toBeVisible();
      await expect(
        await page.locator("label").filter({ hasText: "Exclude certain users" })
      ).toBeVisible();
      await page
        .locator("label")
        .filter({ hasText: "Exclude certain users" })
        .click();
      await page.getByTestId("ever-select").locator("div").nth(1).click();
      await expect(
        await page.getByRole("tab", { name: "Users" })
      ).toBeVisible();
      await expect(
        await page.getByRole("tab", { name: "Groups" })
      ).toBeVisible();
      await page.getByText("Admin test", { exact: true }).click();
      await page.getByText("Power Admin test").click();
      await expect(
        await page
          .getByTestId("ever-select")
          .getByText("Admin test", { exact: true }).nth(1)
      ).toBeVisible();
      await expect(
        await page.getByTestId("ever-select").getByText("Power Admin test").nth(1)
      ).toBeVisible();
      await page.locator(".ant-radio-group").click();
      await page.getByText("Include only specific users").click();
      await page.getByText("Exclude certain users").click();
      await expect(
        await page
          .getByTestId("ever-select")
          .getByText("Admin test", { exact: true })
      ).toBeHidden();
      await expect(
        await page.getByTestId("ever-select").getByText("Power Admin test")
      ).toBeHidden();
      await page.locator(".ant-radio-group").click();
      await page.getByText("Include only specific users").click();
      await page.getByTestId("ever-select").locator("div").nth(1).click();
      await page.getByPlaceholder("Search", { exact: true }).click();
      await page.getByPlaceholder("Search", { exact: true }).fill("Payee");
      await page.getByRole("heading", { name: "Payee test" }).click();
      await expect(
        await page.getByTestId("ever-select").getByText("Payee test").nth(1)
      ).toBeVisible();
      await page.getByPlaceholder("Search", { exact: true }).click();
      await page.getByPlaceholder("Search", { exact: true }).fill("");
      await expect(
        await page.getByTestId("ever-select").getByText("Payee test").nth(1)
      ).toBeVisible();
    });

    test("Custom object should not appear for users who are restricted with access", async ({
      payeePage,
    }) => {
      const page = payeePage.page;
      await expect(await page.getByText("Base Object")).toBeVisible();
      await expect(await page.getByText("Restricted for payee")).toBeHidden();
    });

    test("User should be able to exclude/include users for objects created by other users", async ({
      adminPage,
      payeePage,
    }) => {
      // Payee created and Base object should be open to all
      const pageAdmin = adminPage.page;

      await pageAdmin.getByText("Payee created").click();
      await expect(
        await pageAdmin.getByText("<EMAIL>")
      ).toBeVisible();
      await pageAdmin
        .getByRole("button", { name: "Manage Permissions" })
        .click();
      await pageAdmin.getByLabel("Exclude certain users").click();
      await pageAdmin.getByTestId("ever-select").locator("div").nth(1).click();
      await pageAdmin.getByRole("heading", { name: "User 1" }).click();
      await pageAdmin.getByRole("button", { name: "Save" }).click();
      await expect(
        await pageAdmin.getByText("Object permissions saved")
      ).toBeVisible();

      const pagePayee = payeePage.page;
      await pagePayee.getByText("Base Object").click();
      await expect(
        await pagePayee.getByText(
          "<EMAIL>"
        )
      ).toBeVisible();
      await pagePayee
        .getByRole("button", { name: "Manage Permissions" })
        .click();
      await pagePayee
        .locator("label")
        .filter({ hasText: "Exclude certain users" })
        .click();
      await pagePayee.getByTestId("ever-select").locator("div").nth(1).click();
      await pagePayee.getByRole("heading", { name: "User 2" }).click();
      await pagePayee.getByRole("button", { name: "Save" }).click();
      await expect(
        await pagePayee.getByText("Object permissions saved")
      ).toBeVisible();
    });

    test("User role change include/exclude flow", async ({
      adminPage,
      payeePage,
    }) => {
      // Payee test should be assigned Payee role
      const pageAdmin = adminPage.page;
      const pagePayee = payeePage.page;

      await pageAdmin.goto("/settings/user-roles", {
        waitUntil: "networkidle",
      });
      await pageAdmin.getByText("Payee").first().click();
      await pageAdmin.getByTestId("pt-open-assigned-drawer").click();
      await expect(
        await pageAdmin.getByText("payee.10402@objectspermission")
      ).toBeVisible();
      // await pageAdmin.goto("/groups", {
      //   waitUntil: "networkidle",
      // });
      // await pageAdmin.getByText("All Payees").click();
      // await expect(
      //   await pageAdmin.getByText("payee.10402@objectspermission")
      // ).toBeVisible();
      await expect(
        await pagePayee.getByText("Payee Role - Include")
      ).toBeVisible();
      await pagePayee.getByText("Payee Role - Include").click();
      await expect(
        await pagePayee.getByRole("button", { name: "Manage Permissions" })
      ).toBeVisible();
      await pagePayee
        .getByRole("button", { name: "Manage Permissions" })
        .click();
      await expect(
        await pagePayee.getByText("Configure who can access this")
      ).toBeVisible();
      await pagePayee.locator('[data-test-id="drawer-close-icon"]').click();
      await expect(
        await pagePayee.getByText("Payee Role - Exclude")
      ).toBeHidden();
      await pageAdmin.goto("/users", {
        waitUntil: "networkidle",
      });
      await pageAdmin
        .getByTestId("<EMAIL> users dd button")
        .click();

      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      await pageAdmin
        .getByLabel("Update User")
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Payee" })
        .click();
      await pageAdmin.getByText("Payee-Copy").click();
      await pageAdmin.getByRole("button", { name: "Update User" }).click();
      await expect(
        await pageAdmin.getByText("User Updated successfully")
      ).toBeVisible();
      await pageAdmin.goto("/settings/user-roles", {
        waitUntil: "networkidle",
      });
      await pageAdmin.getByText("Payee").first().click();
      await pageAdmin.getByTestId("pt-open-assigned-drawer").click();
      await expect(
        await pageAdmin.getByText("payee.10402@objectspermission")
      ).toBeHidden();
    });

    test("Manage Permission should not be visible for datasheet created with restricted object", async ({
      adminPage,
    }) => {
      const pageAdmin = adminPage.page;

      await pageAdmin.goto("/databook", {
        waitUntil: "networkidle",
      });
      await pageAdmin.getByRole("link", { name: "Restricted DB" }).click();
      await expect(
        await pageAdmin.getByText("RE Restricted", { exact: true })
      ).toBeVisible();
      await pageAdmin.getByText("RE Restricted", { exact: true }).click();
      await pageAdmin.getByLabel("remove").first().click();
      await expect(
        await pageAdmin.getByRole("menuitem", { name: "Manage Permission" })
      ).toBeHidden();
    });

    test("User with access should be able to use custom object in datasheets and report enrichment", async ({
      adminPage,
    }) => {
      // Remove all report enrichment vars
      const page = adminPage.page;

      await page.goto("/databook", {
        waitUntil: "networkidle",
      });
      await page.getByRole("link", { name: "Open to all" }).click();
      await page.getByText("Open to all DS").click();
      await page.getByRole("button", { name: "Generate Data" }).click();
      await expect(
        await page.getByText("Generating DataBook Data...")
      ).toBeVisible();
      await page.waitForTimeout(15000);
      await expect(await page.getByText("Databook Data generated")).toBeVisible(
        { timeout: 60000 }
      );
      await expect(
        await page.locator(".ag-center-cols-viewport")
      ).toBeVisible();
      await page.getByText("Open to all from DS").click();
      await page.getByRole("button", { name: "Generate Data" }).click();
      await expect(
        await page.getByText("Generating DataBook Data...")
      ).toBeVisible();
      await page.waitForTimeout(15000);
      await expect(
        await page.getByText("Databook Data generated")
      ).toBeVisible();
      await expect(
        await page.locator(".ag-center-cols-viewport")
      ).toBeVisible();
      await page.goto("/settings/report-enrichment", {
        waitUntil: "networkidle",
      });
      await page.getByText("Commission PlanSelect").click();
      await page.getByText("Open to all").click();
      await page.getByText("Report TypeSelect a Report").click();
      await page.getByText("Commission Report").click();
      await page.getByText("CriteriasSelect a Criteria").click();
      await page.getByText("Simple").click();
      await page.getByText("Variables Select a Variable").click();
      await page.getByText("Name", { exact: true }).click();
      await page.getByText("Number", { exact: true }).click();
      await page.getByText("Start date").click();
      await page.getByText("Email", { exact: true }).click();
      await page.getByRole("button", { name: "Validate & Add" }).click();
      await page.getByRole("button", { name: "Save" }).click();
      await expect(
        await page.getByText("Changes saved successfully")
      ).toBeVisible();
    });

    test("User with revoke access should not be able to perform datasheet actions", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      await page.goto("/databook", {
        waitUntil: "networkidle",
      });
      await page.getByRole("link", { name: "Restricted DB" }).click();
      await page.getByText("RE Restricted", { exact: true }).click();
      await page.getByText("You have limited access to").click();
      await page.getByText("Please reach out to your").click();
      await page.getByText("RE Restricted with DS").click();
      await page.getByText("You have limited access to").click();
      await expect(
        await page.getByText(
          "You have limited access to data in this sheetPlease reach out to your"
        )
      ).toBeVisible();
      await expect(
        await page.getByText("Please reach out to your administrator")
      ).toBeVisible();
      await page.getByText("RE Restricted", { exact: true }).click();
      await page.getByRole("button", { name: "Generate Data" }).click();
      await expect(
        await page.getByText("Generating DataBook Data...")
      ).toBeVisible();
      await page.waitForTimeout(12000);
      await expect(
        await page.getByText("Databook Data generated")
      ).toBeVisible();
      const rowCount = await page.$$eval(
        ".ag-center-cols-container .ag-row",
        (rows) => rows.length
      );
      expect(rowCount).toBe(0);
      await page.getByText("RE Restricted with DS").click();
      await page.getByRole("button", { name: "Add tab" }).click();
      await page.getByPlaceholder("Enter Name").click();
      await page.getByPlaceholder("Enter Name").fill("Restricted test");
      await page
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select Object" })
        .click();
      await page.getByText("RE Restricted").nth(2).click();
      await page.getByRole("button", { name: "Validate" }).click();
      await expect(
        await page.getByText("Error while validating")
      ).toBeVisible();
      await expect(
        await page.getByText("You can't perform this action")
      ).toBeVisible();
    });

    test.skip("Last updated time should be changed when changes in permissions are made", async ({
      adminPage,
      payeePage,
    }) => {
      const pageAdmin = adminPage.page;
      const pagePayee = payeePage.page;

      // eslint-disable-next-line no-unused-vars
      const dateBeforeUpdate = "";

      await pageAdmin.getByText("Last Updated TEST").click();
      const adminBeforeDate = await pageAdmin
        .getByTestId("pt-co-permissions-last-updated")
        .innerText();
      await pagePayee.getByText("Last Updated TEST").click();

      const payeeBeforeDate = await pagePayee
        .getByTestId("pt-co-permissions-last-updated")
        .innerText();
      await pagePayee.getByRole("button", { name: "Cancel" }).click();
      await pageAdmin
        .getByRole("button", { name: "Manage Permissions" })
        .click();
      await pageAdmin.getByText("Everyone can access", { exact: true }).click();
      await pageAdmin.getByRole("button", { name: "Save" }).click();
      await expect(
        await pageAdmin.getByText("Object permissions saved")
      ).toBeVisible();
      await pageAdmin.getByText("Last Updated TEST").click();
      const adminAfterDate = await pageAdmin
        .getByTestId("pt-co-permissions-last-updated")
        .innerText();
      await pagePayee.getByText("Last Updated TEST").click();
      const payeeAfterDate = await pagePayee
        .getByTestId("pt-co-permissions-last-updated")
        .innerText();
      await pagePayee
        .locator('[data-test-id="drawer-close-icon"] path')
        .click();

      console.log(adminBeforeDate, payeeBeforeDate);
      console.log(adminAfterDate, payeeAfterDate);
    });

    test("Export datasheet button should be disabled/hidden for user with restrictions", async ({
      adminPage,
      payeePage,
    }) => {
      const customObjV3_pageAdmin = new CustomObjectsV3(adminPage.page);
      const customObjV3_pagePayee = new CustomObjectsV3(payeePage.page);
      // Payee test should have role payee
      const pageAdmin = adminPage.page;
      const pagePayee = payeePage.page;

      await pageAdmin.goto("/users", {
        waitUntil: "networkidle",
      });
      await pageAdmin
        .getByTestId("<EMAIL> users dd button")
        .click();

      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      await pageAdmin
        .getByTestId("ever-select")
        .filter({ hasText: "Payee" })
        .click();
      await pageAdmin.getByText("Super Admin").nth(2).click();
      await pageAdmin.getByRole("button", { name: "Update User" }).click();
      await expect(
        await pageAdmin.getByText("User Updated successfully")
      ).toBeVisible();
      await pageAdmin.goto("/settings/user-roles", {
        waitUntil: "networkidle",
      });
      await pageAdmin.getByText("Super Admin").nth(1).click();
      await pageAdmin
        .getByTestId("pt-open-assigned-drawer")
        .getByText("Assigned to 1 user")
        .click();
      await expect(
        await pageAdmin.getByText("<EMAIL>")
      ).toBeVisible();
      await pageAdmin.getByLabel("Close").nth(1).click();
      await pageAdmin.getByText("Databooks").click();
      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      expect(
        await pageAdmin
          .locator("span")
          .filter({ hasText: "Export datasheet" })
          .first()
          .isChecked()
      ).toBe(true);
      await pageAdmin.getByRole("button", { name: "Cancel" }).click();
      await customObjV3_pageAdmin.navigateToObjectsPage();
      await pageAdmin.getByText("Restricted for payee").click();
      await pageAdmin
        .getByRole("button", { name: "Manage Permissions" })
        .click();
      await pageAdmin.getByText("Exclude certain users").click();
      await expect(
        await pageAdmin.getByTestId("ever-select").getByText("Payee test")
      ).toBeVisible();
      await pagePayee.goto("/databook", {
        waitUntil: "networkidle",
      });
      await pagePayee
        .getByRole("link", { name: "Restricted for Payee" })
        .click();
      const exportButton = await pagePayee.getByRole("button", {
        name: "Export CSV",
      });
      const isDisabled = (await exportButton.getAttribute("disabled")) !== null;
      expect(isDisabled).toBe(true);
      await pageAdmin.getByRole("button", { name: "Cancel" }).click();
      await pageAdmin.goto("/settings/user-roles", {
        waitUntil: "networkidle",
      });
      await pageAdmin.getByText("Super Admin").first().click();
      await pageAdmin
        .getByTestId("pt-open-assigned-drawer")
        .getByText("Assigned to 1 user")
        .click();
      await expect(
        await pageAdmin.getByText("payee.10402@objectspermission")
      ).toBeVisible();
      await pageAdmin.getByLabel("Close").nth(1).click();
      await pageAdmin.getByText("Databooks").click();
      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      await pageAdmin
        .locator("span")
        .filter({ hasText: "Export datasheet" })
        .first()
        .click();
      await pageAdmin.getByRole("button", { name: "Save" }).click();
      await pagePayee.reload({ waitUntil: "networkidle" });
      await pagePayee.waitForSelector('button:has-text("Add Filter")');
      await expect(
        await pagePayee.getByRole("button", {
          name: "Export CSV",
        })
      ).toBeHidden();
      await pageAdmin.getByText("Databooks").click();
      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      await pageAdmin
        .locator("span")
        .filter({ hasText: "Export datasheet" })
        .first()
        .click();
      await pageAdmin.getByRole("button", { name: "Save" }).click();
      await pageAdmin.goto("/users", {
        waitUntil: "networkidle",
      });
      await pageAdmin
        .getByTestId("<EMAIL> users dd button")
        .click();

      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      await pageAdmin
        .getByTestId("ever-select")
        .filter({ hasText: "Super Admin" })
        .click();
      await pageAdmin.getByText("Payee", { exact: true }).nth(1).click();
      await pageAdmin.getByRole("button", { name: "Update User" }).click();
      await expect(
        await pageAdmin.getByText("User Updated successfully")
      ).toBeVisible();
    });

    test("Object records should be hidden for user in Time machine and Statements", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      await expect(await page.getByText("Plan object")).toBeHidden();
      await page.goto("/plans", {
        waitUntil: "networkidle",
      });
      await page.getByText("Comm Plan").click();
      await page.getByRole("button", { name: "Time Machine" }).click();
      await page.getByText("ComponentSelect Criteria").click();
      await page.getByRole("listitem").getByText("Conditional").click();
      await page.getByPlaceholder("Start date").click();
      await page.getByPlaceholder("Start date").fill("Jun 01, 2024");
      await page.getByPlaceholder("Start date").press("Enter");
      await page.getByPlaceholder("End date").click();
      await page.getByPlaceholder("End date").fill("Jun 30, 2024");
      await page.getByPlaceholder("End date").press("Enter");
      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("User").click();
      await page.getByRole("button", { name: "Simulate" }).click();
      await page.waitForTimeout(2500);
      await page.getByText("User 1").nth(2).click();
      await expect(page.getByText("No rows found")).toBeVisible();
      await page.goto("/commissions", {
        waitUntil: "networkidle",
      });
      await page.waitForTimeout(1000);
      const value = await page
        .getByTestId("period-select")
        .locator("div")
        .first()
        .innerText();
      if (value !== "June 2024") {
        await page.getByTestId("period-select").locator("div").first().click();
        await page.getByText("June 2024").click();
      }
      await page.getByRole("link", { name: "User" }).click();
      await page.getByText("Comm Plan").click();
      await page.getByRole("button", { name: "Conditional" }).click();
      await expect(
        page.getByText("No records to display", { exact: true })
      ).toBeVisible();
    });

    test("Commissions should be calculated for a plan using restricted object but data should be masked", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      await expect(await page.getByText("Plan object")).toBeHidden();
      await page.goto("/plans", {
        waitUntil: "networkidle",
      });
      await page.getByText("Comm Plan").click();
      await page.getByRole("button", { name: "Time Machine" }).click();
      await page.getByText("ComponentSelect Criteria").click();
      await page.getByRole("listitem").getByText("Conditional").click();
      await page.getByPlaceholder("Start date").click();
      await page.getByPlaceholder("Start date").fill("Jun 01, 2024");
      await page.getByPlaceholder("Start date").press("Enter");
      await page.getByPlaceholder("End date").click();
      await page.getByPlaceholder("End date").fill("Jun 30, 2024");
      await page.getByPlaceholder("End date").press("Enter");
      await page.getByText("Simulate for Select payee").click();
      await page.getByRole("listitem").getByText("User").click();
      await page.getByRole("button", { name: "Simulate" }).click();
      await page.waitForTimeout(2500);
      await page.getByText("User 1").nth(2).click();
      await expect(page.getByText("No rows found")).toBeVisible();
      await page.goto("/commissions", {
        waitUntil: "networkidle",
      });
      await page.waitForTimeout(1000);
      const value = await page
        .getByTestId("period-select")
        .locator("div")
        .first()
        .innerText();
      if (value !== "June 2024") {
        await page.getByTestId("period-select").locator("div").first().click();
        await page.getByText("June 2024").click();
      }
      await expect(await page.getByText("AL$5,310.00")).toBeVisible();
      await page.getByRole("link", { name: "User" }).click();
      await page.waitForTimeout(2000);
      await expect(
        await page
          .getByRole("gridcell", { name: "AL$5,310.00" })
          .locator("span")
      ).toBeVisible();
      await page.getByText("Comm Plan").click();
      await page.getByRole("button", { name: "Conditional" }).click();
      await expect(await page.getByText("AL$5,310.00").nth(3)).toBeVisible();
      await expect(
        page.getByText("No records to display", { exact: true })
      ).toBeVisible();
    });

    test("Data from restricted custom object should not be visible in crystal", async ({
      adminPage,
      payeePage,
    }) => {
      // Reset open to all object to be open to all
      const pageAdmin = adminPage.page;
      const pagePayee = payeePage.page;

      await pageAdmin.goto("/crystal", {
        waitUntil: "networkidle",
      });
      await pageAdmin.getByRole("button", { name: "New Simulator" }).click();
      await pageAdmin.getByPlaceholder("Enter Crystal View").click();
      await pageAdmin
        .getByPlaceholder("Enter Crystal View")
        .fill("Restricted test");
      await pageAdmin
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select Payee" })
        .click();
      await pageAdmin.getByText("User 1").click();
      await pageAdmin.getByText("Create Simulator").click();
      await pageAdmin.getByRole("button", { name: "Create" }).click();
      await expect(
        await pageAdmin.getByText("Created Crystal View...")
      ).toBeVisible();
      await pageAdmin.getByRole("button", { name: "Add View" }).click();
      await pageAdmin.getByPlaceholder("Enter the View name").click();
      await pageAdmin
        .getByPlaceholder("Enter the View name")
        .fill("Restricted test");
      await pageAdmin.getByPlaceholder("Enter the View name").press("Tab");
      await pageAdmin
        .locator("#newTableForm_crystalTableDescription")
        .fill("Restricted view test");
      await pageAdmin.getByText("Select Datasheet*").click();
      await pageAdmin.locator("#newTableForm_sourceId").click();
      await pageAdmin.getByText("Plan DS").nth(1).click();
      await pageAdmin
        .getByTestId("pt-removable-component-remove")
        .first()
        .click();
      await pageAdmin.getByTestId("pt-removable-component-remove").click();
      await pageAdmin.locator("#newTableForm_emailField").click();
      await pageAdmin.getByRole("listitem").getByText("email").click();
      await pageAdmin.locator("#newTableForm_dateField").click();
      await pageAdmin.getByRole("listitem").getByText("Date").click();
      await pageAdmin.locator("#newTableForm_rowName").click();
      await pageAdmin
        .locator(
          "div:nth-child(17) > .ant-col > .ant-form-item-control-input > .ant-form-item-control-input-content > div > .ant-select > div:nth-child(3) > div > .ant-select-dropdown > div > .rc-virtual-list > .rc-virtual-list-holder > div > .rc-virtual-list-holder-inner > div > .ant-select-item-option-content"
        )
        .first()
        .click();
      await pageAdmin.getByRole("button", { name: "Confirm" }).click();
      await pageAdmin.getByText("Table created successfully").click();
      const rowCount = await pageAdmin.$$eval(
        ".ag-center-cols-container .ag-row",
        (rows) => rows.length
      );
      expect(rowCount).toBe(0);
      const page1Promise = pageAdmin.waitForEvent("popup");
      await pageAdmin.getByRole("button", { name: "Preview as payee" }).click();
      const page1 = await page1Promise;
      await page1.getByRole("button", { name: "Select opportunities" }).click();
      await pageAdmin.waitForTimeout(2500);
      const rowCount1 = await page1.$$eval(
        ".w-full > .ag-theme-material > div > .ag-root-wrapper > .ag-root-wrapper-body > .ag-root > .ag-body-viewport > .ag-center-cols-clipper > .ag-center-cols-container .ag-row",
        (rows) => rows.length
      );
      expect(rowCount1).toBe(0);
      await page1.close();
      await pageAdmin.getByRole("link", { name: "Crystal" }).click();
      await pageAdmin.getByText("Open to all", { exact: true }).click();
      await pageAdmin.waitForTimeout(2500);
      const rowCount2 = await pageAdmin.$$eval(
        ".ag-center-cols-container .ag-row",
        (rows) => rows.length
      );
      expect(rowCount2).toBe(5);
      await pagePayee.getByText("Open to all").click();
      await pagePayee
        .getByRole("button", { name: "Manage Permissions" })
        .click();
      await pagePayee.getByText("Exclude certain users").click();
      await pagePayee.getByTestId("ever-select").locator("div").nth(1).click();
      await pagePayee.getByText("Power Admin test").click();
      await pagePayee.getByRole("button", { name: "Save" }).click();
      await expect(
        await pagePayee.getByText("Object permissions saved")
      ).toBeVisible();
      await pageAdmin.goto("/crystal", {
        waitUntil: "networkidle",
      });
      await pageAdmin.getByText("Open to all", { exact: true }).click();
      await pageAdmin.waitForTimeout(2500);
      const rowCount3 = await pageAdmin.$$eval(
        ".ag-center-cols-container .ag-row",
        (rows) => rows.length
      );
      expect(rowCount3).toBe(0);
      await pageAdmin.getByRole("link", { name: "Crystal" }).click();
      await pageAdmin.locator(".absolute > .ant-btn").first().click();
      await pageAdmin
        .getByRole("menuitem", { name: "Delete Simulator" })
        .locator("span")
        .click();
      await pageAdmin.getByRole("button", { name: "Delete" }).click();
      await expect(
        await pageAdmin.getByText("Crystal View successfully")
      ).toBeVisible();
      await pagePayee.getByText("Open to all").click();
      await pagePayee
        .getByRole("button", { name: "Manage Permissions" })
        .click();
      await pagePayee.getByText("Everyone can access", { exact: true }).click();
      await pagePayee.getByRole("button", { name: "Save" }).click();
      await expect(
        await pagePayee.getByText("Object permissions saved")
      ).toBeVisible();
    });

    test("User should not be able to create datasheet with restricted object", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      await page.goto("/databook", {
        waitUntil: "networkidle",
      });
      await page.getByRole("link", { name: "Restricted DB" }).click();
      await page.getByRole("button", { name: "Add tab" }).click();
      await page.getByPlaceholder("Enter Name").click();
      await page.getByPlaceholder("Enter Name").fill("Test DS");
      await page
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select Object" })
        .click();
      await page.getByText("RE Restricted").nth(2).click();
      await page.getByRole("button", { name: "Validate" }).click();
      await expect(
        await page.getByText("Error while validating")
      ).toBeVisible();
      await expect(
        await page.getByText("You can't perform this action")
      ).toBeVisible();
    });

    test("User should not be able to delete datasheet with restricted object", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      await page.goto("/databook", {
        waitUntil: "networkidle",
      });
      await page.getByRole("link", { name: "Restricted DB" }).click();
      await page.getByText("RE Restricted", { exact: true }).click();
      await expect(
        await page.getByText("You have limited access to")
      ).toBeVisible();
      await page.getByLabel("remove").first().click();
      await page.getByText("Delete").click();
      await expect(await page.getByText("Deleting datasheet...")).toBeVisible();
      await expect(await page.getByText("Error")).toBeVisible();
    });

    test("User with no access to connectors should see restricted datasheet", async ({
      adminPage,
      payeePage,
    }) => {
      const customObjV3_pageAdmin = new CustomObjectsV3(adminPage.page);
      const customObjV3_pagePayee = new CustomObjectsV3(payeePage.page);
      // Remove restictions and update payee.10402 to payee
      const pageAdmin = adminPage.page;
      const pagePayee = payeePage.page;

      await pageAdmin.goto("/users", { waitUntil: "networkidle" });
      await pageAdmin
        .getByTestId("<EMAIL> users dd button")
        .click();
      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      await pageAdmin
        .getByTestId("ever-select")
        .filter({ hasText: "Payee" })
        .click();
      await pageAdmin.getByText("Admin", { exact: true }).nth(2).click();
      await pageAdmin.getByRole("button", { name: "Update User" }).click();
      await expect(
        await pageAdmin.getByText("User Updated successfully")
      ).toBeVisible();
      await pageAdmin.goto("/settings/user-roles", {
        waitUntil: "networkidle",
      });
      await pageAdmin.getByText("Admin", { exact: true }).first().click();
      await pageAdmin
        .getByTestId("pt-open-assigned-drawer")
        .getByText("Assigned to 2 users")
        .click();
      await expect(
        await pageAdmin.getByText("payee.10402@objectspermission")
      ).toBeVisible();
      await pageAdmin.getByLabel("Close").nth(1).click();
      await pageAdmin.getByText("Settings").nth(2).click();
      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      await pageAdmin
        .locator("span")
        .filter({ hasText: "Manage data & data" })
        .first()
        .click();
      await pageAdmin.getByRole("button", { name: "Save" }).click();
      await customObjV3_pageAdmin.navigateToObjectsPage();
      await pageAdmin.getByText("Open to all").click();
      await pageAdmin
        .getByRole("button", { name: "Manage Permissions" })
        .click();
      await pageAdmin.getByText("Exclude certain users").click();
      await pageAdmin
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select by users and groups..." })
        .click();
      await pageAdmin.getByText("Payee test").click();
      await pageAdmin.getByRole("button", { name: "Save" }).click();
      await expect(
        await pageAdmin.getByText("Object permissions saved")
      ).toBeVisible();
      await pagePayee.goto("/settings", { waitUntil: "networkidle" });
      await expect(
        await pagePayee.getByRole("link", { name: "Connectors Manage app" })
      ).toBeHidden();
      await pagePayee.goto("/databook", { waitUntil: "networkidle" });
      await pagePayee.getByRole("link", { name: "Open to all" }).click();
      await expect(
        await pagePayee.getByText("You have limited access to")
      ).toBeVisible();
      await expect(
        await pagePayee.getByText("Please reach out to your")
      ).toBeVisible();
      await pagePayee.getByLabel("remove").first().click();
      await pagePayee.getByText("Edit").click();
      await expect(await pagePayee.getByTitle("Open to all")).toBeVisible();
      await customObjV3_pageAdmin.navigateToObjectsPage();
      await pageAdmin.getByText("Open to all").click();
      await pageAdmin
        .getByRole("button", { name: "Manage Permissions" })
        .click();
      await pageAdmin.getByText("Everyone can access", { exact: true }).click();
      await pageAdmin.getByRole("button", { name: "Save" }).click();
      await expect(
        await pageAdmin.getByText("Object permissions saved")
      ).toBeVisible();
      await pageAdmin.goto("/settings/user-roles", {
        waitUntil: "networkidle",
      });
      await pageAdmin.getByText("Admin", { exact: true }).first().click();
      await pageAdmin
        .locator("span[title]>span")
        .filter({ hasText: "Settings" })
        .click();
      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      await pageAdmin
        .locator("span")
        .filter({ hasText: "Manage data & data" })
        .first()
        .click();
      await pageAdmin.getByRole("button", { name: "Save" }).click();
      await pageAdmin.goto("/users", { waitUntil: "networkidle" });
      await pageAdmin
        .getByTestId("<EMAIL> users dd button")
        .click();
      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      await pageAdmin
        .getByLabel("Update User")
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Admin" })
        .click();
      await pageAdmin.getByText("Payee", { exact: true }).nth(1).click();
      await pageAdmin.getByRole("button", { name: "Update User" }).click();
      await expect(
        await pageAdmin.getByText("User Updated successfully")
      ).toBeVisible();
    });

    test("If 'Data and Data integrations' is turned off -> Check if restricted custom object datasheet is visible", async ({
      adminPage,
      payeePage,
    }) => {
      const customObjV3_pageAdmin = new CustomObjectsV3(adminPage.page);
      const customObjV3_pagePayee = new CustomObjectsV3(payeePage.page);
      const pageAdmin = adminPage.page;
      const pagePayee = payeePage.page;

      await pageAdmin.goto("/settings/user-roles", {
        waitUntil: "networkidle",
      });
      await pageAdmin.getByText("Payee").first().click();
      await pageAdmin
        .getByTestId("pt-open-assigned-drawer")
        .getByText("Assigned to 2 users")
        .click();
      await pageAdmin.getByText("payee.10402@objectspermission").click();
      await pageAdmin.getByLabel("Close").nth(1).click();
      await pageAdmin.getByText("Settings").nth(2).click();
      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      await pageAdmin
        .locator("span")
        .filter({ hasText: "Manage data & data" })
        .first()
        .click();
      await pageAdmin.getByRole("button", { name: "Save" }).click();
      await customObjV3_pagePayee.navigateToObjectsPage();
      await pagePayee.getByText("Sorry, you’re not authorized").click();
      await pagePayee.getByText("Error").click();
      await pageAdmin.getByText("Settings").nth(2).click();
      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      await pageAdmin
        .locator("span")
        .filter({ hasText: "Manage data & data" })
        .first()
        .click();
      await pageAdmin.getByRole("button", { name: "Save" }).click();
      await customObjV3_pagePayee.navigateToObjectsPage();
    });

    test("User group include/exclude flow", async ({
      adminPage,
      payeePage,
    }) => {
      const customObjV3_pageAdmin = new CustomObjectsV3(adminPage.page);
      const customObjV3_pagePayee = new CustomObjectsV3(payeePage.page);
      // Include/Exclude group shoud be accessable to all
      // Payee test should be added to group
      const pageAdmin = adminPage.page;
      const pagePayee = payeePage.page;

      await pageAdmin.goto("/groups", {
        waitUntil: "networkidle",
      });
      await pageAdmin
        .locator('[data-test-id="group-Objects Permission group"]')
        .click();
      await expect(await pageAdmin.getByText("Group members")).toBeVisible();
      await expect(
        await pageAdmin.getByText("payee.10402@objectspermission")
      ).toBeVisible();
      await customObjV3_pageAdmin.navigateToObjectsPage();
      await pageAdmin.getByText("Include group").click();
      await pageAdmin
        .getByRole("button", { name: "Manage Permissions" })
        .click();
      await expect(
        await pageAdmin.getByText("Include only specific users")
      ).toBeVisible();
      await pageAdmin.getByText("Include only specific users").click();
      await pageAdmin.getByTestId("ever-select").locator("div").nth(1).click();
      await pageAdmin.getByRole("tab", { name: "Groups" }).click();
      await pageAdmin.getByText("Objects Permission group").click();
      await pageAdmin.getByRole("button", { name: "Save" }).click();
      await pagePayee.reload({ waitUntil: "networkidle" });
      await pagePayee.getByText("Include group").click();
      await expect(
        await pagePayee.getByRole("button", { name: "Manage Permissions" })
      ).toBeVisible();
      await pagePayee
        .getByRole("button", { name: "Manage Permissions" })
        .click();
      await expect(
        await pagePayee.getByText("Configure who can access this")
      ).toBeVisible();
      await pageAdmin.goto("/groups", {
        waitUntil: "networkidle",
      });
      await pageAdmin
        .locator('[data-test-id="group-Objects Permission group"]')
        .click();
      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      await pageAdmin.getByTestId("ever-select").nth(1).click();
      await pageAdmin.getByText("Payee test").nth(2).click();
      await pageAdmin.getByRole("button", { name: "Update" }).click();
      await pageAdmin.getByText("Objects Permission group updated").click();
      await pageAdmin.waitForTimeout(2000);
      await expect(
        await pageAdmin.getByText("payee.10402@objectspermission")
      ).toBeHidden();
      await pagePayee.reload({ waitUntil: "networkidle" });
      await expect(await pagePayee.getByText("Include group")).toBeHidden();
      await expect(await pagePayee.getByText("Exclude group")).toBeVisible();
      await customObjV3_pageAdmin.navigateToObjectsPage();
      await pageAdmin.getByText("Exclude group").click();
      await pageAdmin
        .getByRole("button", { name: "Manage Permissions" })
        .click();
      await pageAdmin.getByText("Exclude certain users").click();
      await pageAdmin.getByTestId("ever-select").locator("div").nth(1).click();
      await pageAdmin.getByRole("tab", { name: "Groups" }).click();
      await pageAdmin
        .getByLabel("Groups")
        .getByText("Objects Permission group")
        .click();
      await pageAdmin.getByRole("button", { name: "Save" }).click();
      await pagePayee.reload({ waitUntil: "networkidle" });
      await expect(await pagePayee.getByText("Exclude group")).toBeVisible();
      await pageAdmin.goto("/groups", {
        waitUntil: "networkidle",
      });
      await pageAdmin.getByRole("button", { name: "Edit" }).click();
      await pageAdmin.getByTestId("ever-select").nth(1).click();
      await pageAdmin
        .locator("div")
        .filter({ hasText: /^Payee test$/ })
        .nth(1)
        .click();
      await pageAdmin.getByRole("button", { name: "Update" }).click();
      await pagePayee.reload({ waitUntil: "networkidle" });
      await expect(await pagePayee.getByText("Exclude group")).toBeHidden();
    });
  }
);
