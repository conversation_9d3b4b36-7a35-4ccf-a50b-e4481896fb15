import CustomObjectsV3 from "../../../../test-objects/customObjectsV3";

const {
  forecastFixtures: { test, expect },
} = require("../../../fixtures");

const objName = "Object Name Automation";
const connectionObjName = "Connection Object Automation";
const clearDataObj = "Clear data Obj";

test.beforeEach(async ({ adminPage }, testInfo) => {
  const page = adminPage.page;
  testInfo.setTimeout(testInfo.timeout + 300000);
  if (await page.getByText("Logged in as").isVisible({ timeout: 15000 })) {
    try {
      await page.getByRole("button", { name: "Exit" }).click();
      await page.waitForTimeout(5000);
    } catch {
      console.log(
        "in before each, unable to click on Exit button for Logged in user"
      );
    }
  }
  const customObjV3 = new CustomObjectsV3(page);
  await customObjV3.navigateToObjectsPage();
});

test.describe(
  "Custom Objects V3",
  { tag: ["@regression", "@connectors", "@launchpad-1"] },
  () => {
    test(
      "Create a Custom Object from Scratch & Upload Data",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7785" },
          {
            type: "Description",
            description:
              "Create a Custom Object from Scratch, upload data to the object and verify the upload status",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The Custom Object should be created and data should be uploaded successfully using From Scratch option",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const customObjV3 = new CustomObjectsV3(page);
        testInfo.setTimeout(testInfo.timeout + 200000);
        const fields = [
          {
            name: "dealId",
            type: "Number",
          },
          {
            name: "email",
            type: "Email",
          },
          {
            name: "amount",
            type: "Number",
          },
          {
            name: "createdDate",
            type: "Date",
          },
          {
            name: "stage",
            type: "String",
            values: ["open", "closed"],
          },
        ];

        await customObjV3.validateBreadcrumbAndTitle();
        await customObjV3.clickCreateObjectFromScratchButton();
        await customObjV3.enterObjectName(objName);
        await customObjV3.addFields(fields);
        await customObjV3.save();
        await customObjV3.assertDuplicateFieldError();
        await customObjV3.assertPrimaryKeyError();
        await customObjV3.removeDuplicateField();
        await customObjV3.setPrimaryKey();
        await customObjV3.save();
        await customObjV3.assertManualObjectSuccessMsg();
        await customObjV3.verifyCreatedObjectInList(objName);
        await customObjV3.uploadNewData(objName, "CO_Manage_Data_Manual.csv");
        await customObjV3.verifyUploadStatusInActivityLogs(
          objName,
          "Completed"
        );
      }
    );

    test(
      "Create a Custom Object from Connection & Upload Data",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7785" },
          {
            type: "Description",
            description:
              "Create a Custom Object from Connection, upload data to the object and verify the upload status",
          },
          { type: "Precondition", description: "Databook Data" },
          {
            type: "Expected Behaviour",
            description:
              "The Custom Object should be created and data should be uploaded successfully using From Connection option",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const customObjV3 = new CustomObjectsV3(page);
        testInfo.setTimeout(testInfo.timeout + 300000);
        await customObjV3.clickCreateObjectFromConnection();
        await customObjV3.selectHubspotConnection();
        await customObjV3.pickSourceObject("Companies", "companies");
        await customObjV3.searchFieldsConnection("Recent Deal Close Date");
        await customObjV3.selectChecbox();
        await customObjV3.searchFieldsConnection("HubSpot Owner Email");
        await customObjV3.selectChecbox();
        await customObjV3.selectStringDataType();
        await customObjV3.save();
        await customObjV3.assertPrimaryKeyError();
        await customObjV3.setPrimaryKey();
        await customObjV3.enterObjectName(connectionObjName);
        await customObjV3.save();
        await customObjV3.assertConnectionObjectSuccessMsg();

        await customObjV3.uploadNewData(
          connectionObjName,
          "CO_Manage_Data_Connection.csv"
        );
        await customObjV3.verifyUploadStatusInActivityLogs(
          connectionObjName,
          "Completed"
        );
      }
    );

    test(
      "Filter Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7785" },
          {
            type: "Description",
            description:
              "Filter the Manually Managed & Connection filters and Verify",
          },
          { type: "Precondition", description: "Databook Data" },
          {
            type: "Expected Behaviour",
            description: "Search results must be fetched based on the filters",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const customObjV3 = new CustomObjectsV3(page);
        testInfo.setTimeout(testInfo.timeout + 300000);
        await customObjV3.verifyReportObjectsArePresent();
        await customObjV3.filterManuallyManagedVerify();
        await customObjV3.searchConnectionObjectInManuallyManagedFilter();
        await customObjV3.filterHubspotVerify();
        await customObjV3.searchManualObjectInHubspotFilter();
      }
    );

    test(
      "Link Manual object to a Connection",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7785" },
          {
            type: "Description",
            description: "Link a manually created object to Hubspot Connection",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The manually created object should be linked with Hubspot Connection and listed under Hubspot filter",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const customObjV3 = new CustomObjectsV3(page);
        testInfo.setTimeout(testInfo.timeout + 300000);
        await customObjV3.searchObject(objName);
        await customObjV3.selectMenu();
        await customObjV3.selectLinkToAConnectionButton();
        await customObjV3.linkHubspotConnection();
        await customObjV3.pickSourceObject("Companies", "companies");
        await customObjV3.selectFieldToMapInLinkConnection(
          "HubSpot Owner Emailowneremailstring"
        );
        await customObjV3.clickMapAndUpdateButton();
        await customObjV3.assertLinkObjectSuccessMsg();

        await customObjV3.verifyLinkedConnectionIsListed(objName);
      }
    );

    test(
      "Change Sync Start Date",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7785" },
          {
            type: "Description",
            description: "Update the Sync start date of a Connection Object",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description: "The Sync Start date should be updated",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const customObjV3 = new CustomObjectsV3(page);
        testInfo.setTimeout(testInfo.timeout + 300000);
        await customObjV3.searchObject(connectionObjName);
        await customObjV3.selectMenu();
        await customObjV3.selectChangeSyncStartDateButton();
        const currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth() - 6);
        const isoDate = currentDate.toISOString().split("T")[0]; // YYYY-MM-DD
        const displayOptions = {
          year: "numeric",
          month: "short",
          day: "2-digit",
        };
        const uiDate = new Intl.DateTimeFormat("en-US", displayOptions).format(
          currentDate
        );
        await page.getByPlaceholder("Select date").click();
        await page.getByPlaceholder("Select date").fill(uiDate);
        await page.getByTitle(isoDate).click();
        await customObjV3.save();
        await customObjV3.assertSyncDateUpdationMsg();

        await customObjV3.searchObject(objName);
        await customObjV3.selectMenu();
        await customObjV3.selectChangeSyncStartDateButton();
        await page.getByPlaceholder("Select date").click();
        await page.getByPlaceholder("Select date").fill(uiDate);
        await page.getByTitle(isoDate).click();
        // await customObjV3.selectSyncDate(date);
        await customObjV3.save();
        await customObjV3.assertSyncDateUpdationMsg();
      }
    );

    test(
      "Verify Object Details",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7785" },
          {
            type: "Description",
            description: "Verfiy the details in Object Details menu",
          },
          { type: "Precondition", description: "Databook Data" },
          {
            type: "Expected Behaviour",
            description:
              "The total number of rows present in the Object, Linked sheets count,Last synced date should be verified",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const customObjV3 = new CustomObjectsV3(page);
        testInfo.setTimeout(testInfo.timeout + 300000);
        await customObjV3.searchObject(objName);
        await customObjV3.selectMenu();
        await customObjV3.selectObjectDetailsButton();
        const currentDate = new Date();
        const options = { year: "numeric", month: "short", day: "2-digit" };
        const updatedDate = currentDate.toLocaleDateString("en-US", options);
        currentDate.setMonth(currentDate.getMonth() - 6);
        // Format the date to 'Jan 21, 2024' format - 6 months back
        const date = currentDate.toLocaleDateString("en-US", options);
        await customObjV3.verifyText(`Updated On${updatedDate}`);
        await customObjV3.verifyText(`Initial Sync Date${date}`);
        await customObjV3.verifyLinkedSheetsCount("");
        await customObjV3.verifyTotalRowsInObject("1");
        await customObjV3.closeButton();

        await customObjV3.searchObject(connectionObjName);
        await customObjV3.selectMenu();
        await customObjV3.selectObjectDetailsButton();

        await customObjV3.verifyText(`Updated On${updatedDate}`);
        await customObjV3.verifyText(`Initial Sync Date${date}`);

        await customObjV3.verifyLinkedSheetsCount("");
        await customObjV3.verifyTotalRowsInObject("1");
        await customObjV3.closeButton();
      }
    );

    test(
      "Start Data Sync Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7785" },
          {
            type: "Description",
            description: "",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The data sync must be triggered and last synced date should be updated",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const customObjV3 = new CustomObjectsV3(page);
        testInfo.setTimeout(testInfo.timeout + 300000);
        await customObjV3.searchObject(connectionObjName);
        await customObjV3.clickStartDataSyncIcon();
        await customObjV3.triggerDataSync();
        await customObjV3.assertDataSyncSuccessMsg(connectionObjName);
        // await customObjV3.verifyLastSyncedAt();
      }
    );

    test(
      "Delete Mapped fields and Pick more fields",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8625" },
          {
            type: "Description",
            description:
              "Verify deleting Mapped fields and adding it from Pick fields option",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The fields should be in a state to delete and the deleted fields should also be added",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const customObjV3 = new CustomObjectsV3(page);
        testInfo.setTimeout(testInfo.timeout + 300000);
        await customObjV3.searchObject(connectionObjName);
        await customObjV3.clickObject("Connection Object Automation");
        await customObjV3.pickMoreFieldsButton();
        await customObjV3.searchFieldsConnection("About Us");
        await customObjV3.selectChecbox();
        await customObjV3.selectStringDataType();
        await customObjV3.save();
        await customObjV3.verifyText("Successfully created variable");
        await page.click("button.p-0");
        await customObjV3.verifyText("Mapped Fields [3]");
        await customObjV3.searchFieldsConnection("About Us");
        await customObjV3.removeMappingIcon();
        await customObjV3.verifyText("Unmapped the variable");
        await customObjV3.verifyText("Unmapped Fields [1]");
        await customObjV3.deleteVariableIcon();
        await customObjV3.verifyText("Variable deleted successfully");
        await customObjV3.pickMoreFieldsButton();
        await customObjV3.searchFieldsConnection("About Us");
        await customObjV3.selectChecbox();
        await customObjV3.selectStringDataType();
        await customObjV3.save();
        await customObjV3.verifyText("Successfully created variable");
        await customObjV3.verifyText("Mapped Fields [3]");
        await page.click("button.p-0");
      }
    );

    test(
      "Datasheet Linkings",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8625" },
          {
            type: "Description",
            description:
              "Perform various databook operations like split adjustment, Archive, Unarchive",
          },
          { type: "Precondition", description: "Databook Data" },
          {
            type: "Expected Behaviour",
            description:
              "The adjustment must be created and object details must be updated",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const customObjV3 = new CustomObjectsV3(page);
        testInfo.setTimeout(testInfo.timeout + 200000);

        await customObjV3.createDatasheetFromObject();
        await customObjV3.createDatasheetFromDatasheet();

        // Split adjustment
        await customObjV3.createSplitAdjustment();

        // Archive the databook
        await customObjV3.archiveDatabook("Custom Obj Book - Playwright");

        // Check Object details-total Objects
        await page.goto("/objects", { waitUntil: "networkidle" });
        await customObjV3.searchObject(objName);
        await customObjV3.selectMenu();
        await customObjV3.selectObjectDetailsButton();
        await customObjV3.verifyLinkedSheetsCount("02");
        await customObjV3.verifyTotalRowsInObject("1");
        await customObjV3.closeButton();

        // Verifying Error Msg - when linked variable is deleted
        await page
          .getByRole("gridcell", { name: "Object Name Automation" })
          .locator("div")
          .click();
        await page.getByPlaceholder("Search by field name").click();
        await page.getByPlaceholder("Search by field name").fill("email");
        await customObjV3.deleteVariableIcon();
        await expect(
          page.getByText("Field is used in one or more datasheets")
        ).toBeVisible();
        await page.getByRole("button", { name: "Cancel" }).click();
        await expect(
          page.getByRole("button", { name: "Add new object" })
        ).toBeVisible();

        // Verify linked objects cannot be deleted
        await customObjV3.selectMenu();
        await customObjV3.selectDeleteObjectButton();
        await customObjV3.verifyText("Object is used in datasheets");

        // Clear data from object
        await customObjV3.selectMenu();
        await customObjV3.selectClearDataFromObject();
        await customObjV3.assertClearDataConfirmationMsg();
        await customObjV3.assertClearDataPopupButtons();
        await customObjV3.selectClearDataButton();
        await customObjV3.assertClearDataSuccessMsg();

        // Check Object details after clearing data-total Objects
        await customObjV3.selectMenu();
        await customObjV3.selectObjectDetailsButton();
        await customObjV3.verifyLinkedSheetsCount("02");
        await customObjV3.verifyTotalRowsInObject("0");
        await customObjV3.verifyLinkedBookName("Custom Obj Book - Playwright");
        await customObjV3.closeButton();

        // Navigate to datasheet after clearing data
        await page.goto("/databook/d592ba0a-f888-4ae0-b719-eaddcb17f724", {
          waitUntil: "networkidle",
        });

        await customObjV3.checkStalenessAndUpdateSheet();
        await customObjV3.verifyTextIsHidden("<EMAIL>");
        await page.getByText("Playwright Sheet").click();
        await customObjV3.verifyTextIsHidden("<EMAIL>");

        // Unarchive
        await customObjV3.unarchiveDatabook("Custom Obj Book - Playwright");
      }
    );

    test(
      "Clear data from Object and Verify Plans, Crystal, Payouts, Dashboards",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8625" },
          {
            type: "Description",
            description:
              "Verify whether clearing the object data is reflected in Plans, Crystal, Payouts, Dashboards ",
          },
          { type: "Precondition", description: "Databook Data" },
          {
            type: "Expected Behaviour",
            description:
              "The data must be cleared from Plans, Crystal, Payouts, Dashboards",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const customObjV3 = new CustomObjectsV3(page);
        testInfo.setTimeout(testInfo.timeout + 200000);

        // Validate "clear data, Delete Object" is not present for report objects
        await customObjV3.searchObject("Quota Attainment");
        await customObjV3.selectMenu();
        await customObjV3.verifyClearDataFromObjectIsHidden();
        await customObjV3.verifyDeleteObjectButtonIsHidden();

        // Validate "clear data, Delete Object" is present for Manual objects
        await customObjV3.searchObject(objName);
        await customObjV3.selectMenu();
        await customObjV3.verifyClearDataFromObjectIsVisible();
        await customObjV3.verifyDeleteObjectButtonIsVisible();

        // Validate "clear data, Delete Object" is present for connection objects
        await customObjV3.searchObject(connectionObjName);
        await customObjV3.selectMenu();
        await customObjV3.verifyClearDataFromObjectIsVisible();
        await customObjV3.verifyDeleteObjectButtonIsVisible();

        // Clear data from object
        await customObjV3.searchObject(clearDataObj);
        await customObjV3.selectMenu();
        await customObjV3.selectClearDataFromObject();
        await customObjV3.assertClearDataConfirmationMsg();
        await customObjV3.assertClearDataPopupButtons();
        await customObjV3.selectClearDataButton();
        await customObjV3.assertClearDataSuccessMsg();

        await customObjV3.verifyAuditLogClearDataFromObject(clearDataObj);

        // Refresh datasheet
        await page.goto("/databook/f6cc17a1-7636-44c2-88f2-d784d42fdf32", {
          waitUntil: "networkidle",
        });
        await customObjV3.checkStalenessAndUpdateSheet();
        await customObjV3.verifyTextIsHidden("<EMAIL>");

        // Run sync
        await customObjV3.navigateToCommissionSyncPage();
        await customObjV3.checkPayeesInCommissionPlanOption();
        await page.getByTestId("ever-select").locator("div").nth(1).click();
        await page
          .locator("div")
          .filter({ hasText: /^Clear data Plan$/ })
          .nth(1)
          .click();
        await page.keyboard.press("Escape");
        await customObjV3.clickMonthPicker();
        await customObjV3.selectYear("2024");
        await customObjV3.selectSyncMonth("Jun");
        await customObjV3.runSync();
        await customObjV3.verifyText("Preparing to start...");
        await page
          .getByText("Calculating Commissions...")
          .waitFor({ state: "visible", timeout: 30000 });
        await page
          .getByText("Compensation Calculations Completed")
          .waitFor({ state: "visible", timeout: 600000 });

        // Verify in Commission Plan
        await customObjV3.verifyDataInCommissionPlan();

        // Verify in Payouts
        await customObjV3.verifyDataInPayouts();

        // Verify Crystal
        await customObjV3.verifyDataInCrystal();

        // Verify Dashboards
        await customObjV3.verifyDataInDashboards();
      }
    );

    test(
      "Upload new data to existing object via V1,V2 and clear object through V3",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8625" },
          {
            type: "Description",
            description:
              "Verify whether data can be uploaded to existing object via V1,V2 and clear the object through V3",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The data should be uploaded successfully and object data must be cleared",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const customObjV3 = new CustomObjectsV3(page);
        testInfo.setTimeout(testInfo.timeout + 200000);

        // Upload via V1
        await customObjV3.navigateToLegacyUpload();
        await customObjV3.uploadNewDataV1(objName);

        // Upload via V2
        await customObjV3.uploadNewDataV2(objName, "CO_Manage_Data_V2.csv");
        await customObjV3.verifyUploadStatusInActivityLogs(
          objName,
          "Completed"
        );

        // verify the uploaded data
        await page.goto("/databook/d592ba0a-f888-4ae0-b719-eaddcb17f724", {
          waitUntil: "networkidle",
        });
        await customObjV3.checkStalenessAndUpdateSheet();
        await customObjV3.verifyText("New Data via V1");
        await customObjV3.verifyText("Data Uploaded via V2");

        // Clear data from object
        await customObjV3.navigateToObjectsPage();
        await customObjV3.searchObject(objName);
        await customObjV3.selectMenu();
        await customObjV3.selectClearDataFromObject();
        await customObjV3.assertClearDataConfirmationMsg();
        await customObjV3.assertClearDataPopupButtons();
        await customObjV3.selectClearDataButton();
        await customObjV3.assertClearDataSuccessMsg();

        await customObjV3.verifyAuditLogClearDataFromObject(objName);

        // Refresh datasheet
        await page.goto("/databook/d592ba0a-f888-4ae0-b719-eaddcb17f724", {
          waitUntil: "networkidle",
        });
        await customObjV3.checkStalenessAndUpdateSheet();
        await customObjV3.verifyTextIsHidden("New Data via V1");
        await customObjV3.verifyTextIsHidden("Data Uploaded via V2");
      }
    );

    test(
      "Delete Datasheet & Object",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7785,8625" },
          {
            type: "Description",
            description: "Delete a unlinked variable, datasheets and objects",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "Unlinked variable, datasheets and objects must be deleted successfully",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const customObjV3 = new CustomObjectsV3(page);
        testInfo.setTimeout(testInfo.timeout + 300000);
        await page.goto("/databook", { waitUntil: "networkidle" });
        await customObjV3.deleteDatasheet();

        // Delete unlinked variable and verify audit log
        await customObjV3.navigateToObjectsPage();
        await customObjV3.searchObject(objName);
        await page
          .getByRole("gridcell", { name: "Object Name Automation" })
          .locator("div")
          .click();
        await customObjV3.searchFieldsManual("email");
        await customObjV3.deleteVariableIcon();
        await customObjV3.verifyText("Variable deleted successfully");
        await customObjV3.cancel();
        await customObjV3.verifyText("4 Fields");

        await customObjV3.verifyAuditLogDeleteVariable(objName);

        // Delete Objects and verify audit log
        await customObjV3.navigateToObjectsPage();
        await customObjV3.deleteObject(objName);
        await customObjV3.deleteObject(connectionObjName);

        await customObjV3.verifyAuditLogDeleteObject(objName);
        await customObjV3.verifyAuditLogDeleteObject(connectionObjName);
      }
    );
  }
);
