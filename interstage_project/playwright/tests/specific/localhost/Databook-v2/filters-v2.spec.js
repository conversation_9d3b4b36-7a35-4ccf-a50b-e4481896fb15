import DatasheetV2Page from "../../../../test-objects/datasheet-v2-objects";

const {
  databookUI2Fixtures: { test, expect },
} = require("../../../fixtures");
const {
  datasheetProdAutomateFixtures: { test: test1, expect: expect1 },
} = require("../../../fixtures");

test.describe(
  "Databook v2 Views & Filters Automation",
  { tag: ["@datasheet", "@regression", "@adminchamp-6"] },
  () => {
    test.beforeEach(async ({ adminPage }, testInfo) => {
      testInfo.setTimeout(testInfo.timeout + 2400000);
      const page = adminPage.page;
      const dsV2Page = new DatasheetV2Page(adminPage.page);
      await page.goto(
        "/datasheet?id=fe19efb4-ec5a-4cc7-94e1-ab6089e9b035&name=Filters+sheet&viewId=all_data",
        { waitUntil: "networkidle" }
      );
      await dsV2Page.letColumnsLoad("ID");
    });

    test(
      "Basic Views Operations - CRUD",
      {
        annotation: [
          {
            type: "Description",
            description:
              "Validate that the user is able to create and delete a new View and verify the error messages & alerts",
          },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to create, delete the views along with filters in datasheet",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);

        await dsV2Page.createDatasheet(
          "View Filters",
          "CRUD Sheet",
          "object",
          "All data PW Obj",
          ""
        );

        await dsV2Page.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsV2Page.closeEditsheetPage();
        await dsV2Page.delay(1000);
        await expect(
          page.getByRole("button", { name: "Filter", exact: true })
        ).toBeDisabled();

        // Verify Add new view button is disabled when sheet is not generated
        const element = await page
          .locator('[aria-label="Add tab"] > span')
          .first();
        const classAttribute = await element.getAttribute("class");
        expect(classAttribute).toContain("cursor-not-allowed");

        await dsV2Page.generateDatasheet();
        await dsV2Page.letColumnsLoad("ID");

        // Verify Empty View name cannot be created
        await dsV2Page.createNewView("ID");
        await dsV2Page.verifyViewName("New View 1");
        await dsV2Page.verifyDuplicateAndEmptyViewName("New View 1", "");

        // Create new view
        await dsV2Page.createNewView("ID");
        await dsV2Page.verifyViewName("New View 2");
        await dsV2Page.renameView("New View 2", "CRUD View");
        await dsV2Page.createFilterFormula("ID");

        // Create new view with same filters used in previous view
        await dsV2Page.createNewView("ID");
        await dsV2Page.verifyViewName("New View 3");
        await dsV2Page.renameView("New View 3", "CRUD View 2");
        await dsV2Page.createFilterFormula("ID");

        // Duplicate View name
        await dsV2Page.createNewView("ID");
        await dsV2Page.verifyViewName("New View 4");
        await dsV2Page.verifyDuplicateAndEmptyViewName(
          "New View 4",
          "CRUD View"
        );

        // Verify variables used in views cannot be unchecked
        await dsV2Page.createNewView("ID");
        await dsV2Page.verifyViewName("New View 5");
        await dsV2Page.renameView(
          "New View 5",
          'CRUD View 3 - !@#$%^&*(){}|:"<>?[];,./'
        );
        await dsV2Page.createFilterFormula("Amount");
        await dsV2Page.clickEditBtn();
        await dsV2Page.selectAllCheckBox();
        await dsV2Page.dialogPrompt(
          "span",
          "Cannot Remove Variable Amount since it is used in views"
        );
        await dsV2Page.closeEditsheetPage();

        // #PROD Bug,PR-9992 = Internal server error was displayed when variables used in views were Unchecked - For startsWith(/endsWith() methods
        await dsV2Page.createNewView("ID");
        await dsV2Page.verifyViewName("New View 6");
        await dsV2Page.renameView("New View 6", "CRUD View 4");
        await dsV2Page.createViewWithFunction("StartsWith");
        await dsV2Page.createNewView("ID");
        await dsV2Page.verifyViewName("New View 7");
        await dsV2Page.renameView("New View 7", "CRUD View 5");
        await dsV2Page.createViewWithFunction("EndsWith");
        await dsV2Page.clickEditBtn();
        await dsV2Page.selectAllCheckBox();
        await dsV2Page.dialogPrompt(
          "span",
          "Cannot Remove Variables First Name,Amount since they are used in views"
        );
        await dsV2Page.closeEditsheetPage();

        //Create a filter in All data and verify Save as filter option
        await dsV2Page.goToDatasheet("View Filters", "Filters sheet");
        await dsV2Page.goToDatasheet("View Filters", "CRUD Sheet");
        await page.getByRole("button", { name: "Filter", exact: true }).click();

        const resetIcon = await page
          .locator(".ant-tabs-tabpane:nth-child(1)>div>div>button")
          .nth(1);
        const saveAsFilterIcon = await page
          .locator(".ant-tabs-tabpane:nth-child(1)>div>div>button")
          .nth(2);

        await expect(resetIcon).toBeDisabled();
        await expect(saveAsFilterIcon).toBeDisabled();
        await dsV2Page.enterFilterFormula(["ID", ">", "2"]);
        await expect(resetIcon).toBeEnabled();
        await expect(saveAsFilterIcon).toBeDisabled();
        await dsV2Page.clickApplyBtn("ID");
        await expect(saveAsFilterIcon).toBeEnabled();
        await saveAsFilterIcon.click();
        await dsV2Page.dialogPrompt(
          "span",
          "Datasheet View created successfully"
        );
        await dsV2Page.letColumnsLoad("ID");
        await dsV2Page.verifyViewName("New View 8");

        // Verify whether the applied expression is carry forwarded to the newly created filter
        const expressionValue = await dsV2Page.getFilterCalc();
        console.log(expressionValue);
        expect(expressionValue).toBe("ID>2");

        // Verify whether the rename operation gets discarded (after entering empty view name and clicking Enter), when navigated to another sheet and come back
        await dsV2Page.createNewView("ID");
        await dsV2Page.verifyViewName("New View 9");
        await dsV2Page.verifyDuplicateAndEmptyViewName("New View 9", "");
        await dsV2Page.goToDatasheet("View Filters", "Filters sheet");
        await dsV2Page.goToDatasheet("View Filters", "CRUD Sheet");
        expect(await dsV2Page.getActiveViewName()).toBe("All data");

        // Delete Datasheet with filters
        await dsV2Page.goToDatasheet("View Filters", "CRUD Sheet");
        await dsV2Page.deleteDatasheet();
      }
    );

    test(
      "Creation and validation of filters using various functions, operators for all datatypes",
      {
        annotation: [
          {
            type: "Description",
            description:
              "Validate whether the user is able to create Filters with all functions, datatypes in all combinations",
          },
          {
            type: "Expected Behaviour",
            description:
              "The relevant results must be fetched on filtering using various functions and operators",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const dsV2Page = new DatasheetV2Page(adminPage.page);

        await dsV2Page.createNewView("ID");
        await dsV2Page.renameView("New View 1", "Filter View");

        await dsV2Page.createVerifyFilterFunction("IsEmpty");
        await dsV2Page.sortColumn("ID", "DESC");
        await dsV2Page.createVerifyFilterFunction("IsNotEmpty");
        await dsV2Page.sortColumn("ID", "DESC");

        await dsV2Page.createVerifyConstantFilterFunction("Contains");
        await dsV2Page.sortColumn("ID", "DESC");
        await dsV2Page.createVerifyConstantFilterFunction("NotContains");
        await dsV2Page.sortColumn("ID", "DESC");
        await dsV2Page.createVerifyConstantFilterFunction("StartsWith");
        await dsV2Page.sortColumn("ID", "DESC");
        await dsV2Page.createVerifyConstantFilterFunction("EndsWith");
        await dsV2Page.sortColumn("ID", "DESC");

        await dsV2Page.createVerifyDatasheetFieldFilterFunction("Contains");
        await dsV2Page.sortColumn("ID", "DESC");
        await dsV2Page.createVerifyDatasheetFieldFilterFunction("NotContains");
        await dsV2Page.sortColumn("ID", "DESC");
        await dsV2Page.createVerifyDatasheetFieldFilterFunction("StartsWith");
        await dsV2Page.sortColumn("ID", "DESC");
        await dsV2Page.createVerifyDatasheetFieldFilterFunction("EndsWith");
        await dsV2Page.sortColumn("ID", "DESC");

        await dsV2Page.createVerifyFilterOperators();
      }
    );
  }
);

test1.describe(
  "Datasheet V2 - Prod bugs",
  { tag: ["@regression", "@datasheet", "@adminchamp-3"] },
  () => {
    test1.beforeEach(async ({ adminPage }, testInfo) => {
      testInfo.setTimeout(testInfo.timeout + 200000);
      const page = adminPage.page;
      await page.goto(
        "/datasheet?id=00bb7248-f49c-4bf6-96fb-1714d74dcd21&name=dummy&viewId=all_data",
        { waitUntil: "networkidle" }
      );
    });

    test1(
      "Verify that a decimal number can be used to filter data",
      {
        annotation: [
          { type: "Test ID", description: "INTER-9556" },
          {
            type: "Description",
            description:
              "Verify that a decimal number can be used to filter data and that the filter is applied correctly.",
          },
          {
            type: "Precondition",
            description:
              "Data set containing decimal values available for filtering",
          },
          {
            type: "Expected Behaviour",
            description:
              "The filter should correctly apply when using a decimal number, returning the expected filtered results.",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        await dsV2Page.createDatasheet(
          "prod-DB",
          "DecimalFilter",
          "object",
          "freelance-comm-50-extended-cols-obj",
          ""
        );
        await dsV2Page.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsV2Page.closeEditsheetPage();
        await dsV2Page.generateDatasheet();
        await dsV2Page.letColumnsLoad("ID");
        const TotalCount = Number(
          (
            await page.locator('//div[text()="All data"]//span').textContent()
          ).trim()
        );
        await page.getByRole("button", { name: "Filter" }).click();
        let formulaLocator = await dsV2Page.selectFormula(
          "DATASHEET COLUMNS",
          "Sale Amount"
        );
        await formulaLocator.click();
        await dsV2Page.typeFormulaExpression(">=");
        await dsV2Page.typeFormulaExpression("100000.356");
        const applyButton = page.getByRole("button", { name: "Apply" });
        await expect(applyButton).toBeEnabled({ timeout: 2000 });
        await applyButton.click();
        await dsV2Page.letColumnsLoad("ID");
        const greaterThanEqualToCount = Number(
          (
            await page.locator('//div[text()="All data"]//span').textContent()
          ).trim()
        );
        const resetButton = page.locator(
          '//div[@data-testid="copy-button"]/ancestor::div[3]/following-sibling::button[2]'
        );
        await resetButton.waitFor({ state: "visible" });
        await resetButton.click();
        await dsV2Page.letColumnsLoad("ID");
        formulaLocator = await dsV2Page.selectFormula(
          "DATASHEET COLUMNS",
          "Sale Amount"
        );
        await formulaLocator.click();
        await dsV2Page.typeFormulaExpression("<");
        await dsV2Page.typeFormulaExpression("100000.356");
        await expect(applyButton).toBeEnabled({ timeout: 2000 });
        await applyButton.click();
        await dsV2Page.letColumnsLoad("ID");
        const lesserThanCount = Number(
          (
            await page.locator('//div[text()="All data"]//span').textContent()
          ).trim()
        );
        expect(greaterThanEqualToCount + lesserThanCount).toBe(TotalCount);
      }
    );
  }
);
