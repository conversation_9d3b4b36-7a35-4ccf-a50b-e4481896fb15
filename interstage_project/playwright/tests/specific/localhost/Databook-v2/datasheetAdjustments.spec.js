import DatasheetV2Page from "../../../../test-objects/datasheet-v2-objects";

const {
  datasheetV2Fixtures: { test, expect },
} = require("../../../fixtures");
const {
  datasheetProdAutomateFixtures: { test: test1, expect: expect1 },
} = require("../../../fixtures");

test.beforeEach(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 600000);
  const page = adminPage.page;
  await page.goto("/datasheet", { waitUntil: "networkidle" });
});

test1.beforeEach(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 200000);
  const page = adminPage.page;
  await page.goto(
    "/datasheet?id=00bb7248-f49c-4bf6-96fb-1714d74dcd21&name=dummy&viewId=all_data", // prod-DB>dummy sheet
    { waitUntil: "networkidle" }
  );
});

test.describe(
  "Datasheet v2 Adjustments",
  { tag: ["@datasheet", "@regression", "@adminchamp-4"] },
  () => {
    test(
      "Verify user is able to perform Action - Adjustment Data and Validate Datasheet-Local, Global sheets.",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T5970, INTER-T5971, INTER-T5972, INTER-T5973, INTER-T5974, INTER-T5975, INTER-T15970, INTER-T15971, INTER-T15972, INTER-T5961, INTER-T5962, INTER-T5963, INTER-T5964, INTER-T5965, INTER-T5966",
          },
          {
            type: "Description",
            description:
              "Verify user is able to apply adjustent - local ignore, global ignore, local update, global update, local split, global split.",
          },
          {
            type: "Precondition",
            description: "Create a test-adjustment datasheet with few columns.",
          },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to perform all adjustments and create a new datasheet from the local/global sheet as source with CF.",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        testInfo.setTimeout(testInfo.timeout + 900000);
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        await dsV2Page.goToDatasheet("test-databook-4", "test-adjustment");
        await dsV2Page.validateAdjusment(
          "Ignore Record",
          8,
          "local",
          "Local-Ignore"
        );
        await dsV2Page.validateAdjusment(
          "Ignore Record",
          7,
          "global",
          "Global-Ignore"
        );
        await dsV2Page.validateAdjusment(
          "Update Record",
          6,
          "local",
          "Local-Update"
        );
        await dsV2Page.validateAdjusment(
          "Update Record",
          5,
          "global",
          "Global-Update"
        );
        await dsV2Page.validateAdjusment(
          "Split Record",
          4,
          "local",
          "Local-Split"
        );
        await dsV2Page.validateAdjusment(
          "Split Record",
          3,
          "global",
          "Global-Split"
        );
        await dsV2Page.letColumnsLoad("id");
        const adjustmentCount = await dsV2Page.getTabCount("Adjustments");
        expect(adjustmentCount).toBe("06");
        let valuesReceived = await dsV2Page.getColumnValues("id", "id");
        expect(valuesReceived).not.toContain("7");
        expect(valuesReceived).not.toContain("8");

        await dsV2Page.createDatasheet(
          "test-databook-4",
          "validate-adjustedSheet",
          "datasheet",
          "test-databook-4",
          "test-adjustment"
        );
        await dsV2Page.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsV2Page.closeEditsheetPage();
        await dsV2Page.generateDatasheet();
        await dsV2Page.letColumnsLoad("id");
        valuesReceived = await dsV2Page.getColumnValues("id", "id");
        expect(valuesReceived).not.toContain("7");
        expect(valuesReceived).toContain("8");
      }
    );
  }
);
test1.describe(
  "Datasheet V2 - Prod bugs",
  { tag: ["@regression", "@datasheet", "@adminchamp-3"] },
  () => {
    test1(
      "Verify able to add adjustment number more than 1,000,000",
      {
        annotation: [
          { type: "Test ID", description: "PR-10000" },
          {
            type: "Description",
            description:
              "Verify that while making a Datasheet level adjustment in Datasheet V2, the system should accept any amount greater than 1,000,000.",
          },
          {
            type: "Precondition",
            description: "Datasheet V2 with adjustment functionality enabled",
          },
          {
            type: "Expected Behaviour",
            description:
              "The system should allow Datasheet level adjustments with amounts greater than 1,000,000 without restrictions.",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        await dsV2Page.createDatasheet(
          "prod-DB",
          "sheet2 as source",
          "datasheet",
          "prod-DB",
          "sheet2"
        );
        await dsV2Page.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsV2Page.closeEditsheetPage();
        await dsV2Page.generateDatasheet();
        await dsV2Page.letColumnsLoad("ID");
        await dsV2Page.adjustmentUpdate(
          1,
          "local",
          "sale_amount",
          910882.78,
          9999999,
          "local update to 9,999,999"
        );
      }
    );
    test1(
      "Verify that global adjustments are correctly reflected in the child sheet.",
      {
        annotation: [
          { type: "Test ID", description: "INTER-11660" },
          {
            type: "Description",
            description:
              "Verify that global adjustments are correctly reflected in the child sheet(Ignore,Update,Split).",
          },
          {
            type: "Precondition",
            description:
              "A parent sheet with global adjustments and a linked child sheet",
          },
          {
            type: "Expected Behaviour",
            description:
              "Any global adjustments made in the parent sheet should be accurately reflected in the child sheet.",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        await dsV2Page.createDatasheet(
          "prod-DB",
          "GlobalAdjustmentBase",
          "object",
          "freelance-comm-50-obj",
          ""
        );
        await dsV2Page.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsV2Page.closeEditsheetPage();
        await dsV2Page.generateDatasheet();
        await dsV2Page.letColumnsLoad("ID");
        await dsV2Page.createDatasheet(
          "prod-DB",
          "GlobalAdjustmentChild",
          "datasheet",
          "prod-DB",
          "GlobalAdjustmentBase"
        );
        await dsV2Page.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsV2Page.closeEditsheetPage();
        await dsV2Page.generateDatasheet();
        await dsV2Page.letColumnsLoad("ID");
        await dsV2Page.sortColumn("ID", "ASC");
        await expect(page.getByText("910,882.78")).toBeVisible();
        await expect(page.getByText("863,801.94")).toBeVisible();
        await expect(page.getByText("108,487.29")).toBeVisible();
        await dsV2Page.goToDatasheet("prod-DB", "GlobalAdjustmentBase");
        await dsV2Page.adjustmentUpdate(
          1,
          "global",
          "sale_amount",
          910882.78,
          9999999,
          "global update to 9,999,999"
        );
        await dsV2Page.adjustmentIgnore(2, "global", "Global-Ignore");
        await dsV2Page.adjustmentSplit(
          2,
          "global",
          "sale_amount",
          108487.29,
          50000,
          50000,
          "global update to 9,999,999"
        );
        await dsV2Page.goToDatasheet("prod-DB", "GlobalAdjustmentChild");
        await dsV2Page.letColumnsLoad("ID");
        await dsV2Page.applyAdjustmentChanges();
        await dsV2Page.letColumnsLoad("ID");
        await dsV2Page.sortColumn("ID", "ASC");
        await expect(page.getByText("9,999,999")).toBeVisible();
        await expect(page.getByText("863,801.94")).toBeHidden();
        const count = await page.getByText("50,000").count();
        expect(count).toBe(2);
      }
    );
    test1(
      "Verify that adjustments can be added to a numeric datatype with negative, comma, currency",
      {
        annotation: [
          { type: "Test ID", description: "INTER-10190" },
          {
            type: "Description",
            description:
              "Verify that adjustments can be added to a numeric datatype, including support for negative values, comma-formatted values, currency symbols, and special characters.",
          },
          {
            type: "Precondition",
            description:
              "Adjustment input field available with numeric data type support",
          },
          {
            type: "Expected Behaviour",
            description:
              "Users should be able to enter or paste negative values and numbers formatted with commas (e.g., 12,325.56). Currency symbols and special characters should be automatically converted into a valid number where possible. If conversion is not possible, an 'Invalid input' error should be displayed.",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        await dsV2Page.createDatasheet(
          "prod-DB",
          "AdjustmentFormatting",
          "object",
          "freelance-comm-50-obj",
          ""
        );
        await dsV2Page.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsV2Page.closeEditsheetPage();
        await dsV2Page.generateDatasheet();
        await dsV2Page.letColumnsLoad("ID");

        await dsV2Page.sortColumn("ID", "ASC");
        await page.waitForLoadState("networkidle");
        await dsV2Page.letColumnsLoad("ID");
        const actionLocator = page.locator(
          `//div[@row-id="0"]/div[@col-id='action']//button`
        );
        await actionLocator.waitFor({ state: "visible" });
        await actionLocator.click({ timeout: 3000 });
        await dsV2Page.goToAdjustDataMenu("Update Record");
        //with comma
        await dsV2Page.validateAdjustmentFormatting(
          "sale_amount",
          "910882.78",
          "12,325.56",
          "12325.56"
        );
        //with Currency
        await dsV2Page.validateAdjustmentFormatting(
          "sale_amount",
          "910882.78",
          "$12,123.123",
          "12123.123"
        );
        //with brackets
        await dsV2Page.validateAdjustmentFormatting(
          "sale_amount",
          "910882.78",
          "(12,123.123)",
          "12123.123"
        );
        //with brackets with negative
        await dsV2Page.validateAdjustmentFormatting(
          "sale_amount",
          "910882.78",
          "-(12,123.123)",
          "-12123.123"
        );
        //with negative,currency, brackets, comma
        await dsV2Page.validateAdjustmentFormatting(
          "sale_amount",
          "910882.78",
          "-$(12,123.123)",
          "-12123.123"
        );
        await page
          .getByPlaceholder("Enter reason for adjustment")
          .fill("Ajustment Validation");
        const updateBt = await page.locator("button div", {
          hasText: "Update",
        });
        await updateBt.click();
        await dsV2Page.applyAdjustmentChanges();
      }
    );

    test1(
      "Using Tab to navigate on date variable made the page to break",
      {
        annotation: [
          { type: "Test ID", description: "ICM-1695" },
          {
            type: "Description",
            description:
              "Navigate using Tab on Date variable fields in Create/Edit Adjustments page",
          },
          {
            type: "Precondition",
            description: "None",
          },
          {
            type: "Expected Behaviour",
            description:
              "The Page break must not occur and the date picker must be opened",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);

        //Update adjustment page
        await dsV2Page.goToDatasheet("prod-DB", "sheet1");
        await dsV2Page.clickFilterButton();
        await dsV2Page.enterFilterFormula(["ID", "==", "20"]);
        await dsV2Page.clickApplyBtn("ID");
        const actionLocator = page.locator(
          `//div[@row-id="0"]/div[@col-id='action']//button`
        );
        await actionLocator.waitFor({ state: "visible" });
        await actionLocator.click({ timeout: 3000 });
        await dsV2Page.goToAdjustDataMenu("Update Record");
        await page
          .locator(
            `//div[contains(@col-id,"co_1_commission_amount")]/div[contains(text(), "297174.08")]`
          )
          .click();
        await page.keyboard.press("Tab");
        await dsV2Page.delay(1000);
        const errorMsg = await page.getByText(
          "TypeError - Cannot convert undefined or null to object"
        );
        await errorMsg.waitFor({ state: "hidden" });
        const errorMsg2 = await page.getByText("Something went wrong");
        await errorMsg2.waitFor({ state: "hidden" });
        await expect(page.getByText("Today")).toBeVisible();
        await page.keyboard.press("Tab");
        await dsV2Page.delay(1000);
        await expect(page.getByText("Today")).toBeVisible();
        await page.keyboard.press("Escape");
        await dsV2Page.clickCancelButton();

        await dsV2Page.clearFilter();

        //Split adjustment page
        await dsV2Page.enterFilterFormula(["ID", "==", "47"]);
        await dsV2Page.clickApplyBtn("ID");
        await actionLocator.waitFor({ state: "visible" });
        await actionLocator.click({ timeout: 3000 });
        await dsV2Page.goToAdjustDataMenu("Split Record");
        await dsV2Page.delay(1000);
        await page
          .locator(
            "//div[contains(@col-id,'co_1_commission_amount')]/div[contains(text(), '43033.49')]"
          )
          .nth(-2)
          .click();
        await page.keyboard.press("Tab");
        await dsV2Page.delay(1000);
        await errorMsg.waitFor({ state: "hidden" });
        await errorMsg2.waitFor({ state: "hidden" });
        await expect(page.getByText("Today")).toBeVisible();
      }
    );
  }
);
