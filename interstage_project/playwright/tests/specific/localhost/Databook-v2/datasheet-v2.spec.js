import DatasheetV2Page from "../../../../test-objects/datasheet-v2-objects";
import DatasheetV2EditViewPage from "../../../../test-objects/datasheet-v2-editView-objects";

const {
  datasheetV2Fixtures: { test, expect },
} = require("../../../fixtures");

test.beforeEach(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 600000);
  const page = adminPage.page;
  await page.goto("/datasheet", { waitUntil: "networkidle" });
});

test.describe(
  "Databook v2 CRUD Automation",
  { tag: ["@datasheet", "@regression", "@adminchamp-4"] },
  () => {
    test(
      "Creation and Deletion of DataBook(rename as well), DataSheet with custom object as source",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T5902, INTER-T5903, INTER-T5906, INTER-T5907, INTER-T5948, INTER-T15936, INTER-T15942, INTER-T15943, INTER-T15944, INTER-T15953",
          },
          {
            type: "Description",
            description:
              "Validate that the user is able to create and delete a new Databook and a new Datasheet. Verify deleted db should be removed from pinned list and Adjustments tab should have no count",
          },
          { type: "Precondition", description: "" },
          {
            type: "Expected Behaviour",
            description:
              "User should be able to see the creation of new databook, datasheet and the deletion of same.",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);

        await dsV2Page.createDatabook();
        await page.waitForLoadState("networkidle");
        const databookName = "db1";
        const datasheetName = "datasheet-1";
        await dsV2Page.renameDatabook(
          "Untitled Databook(1)",
          databookName,
          false
        );
        await dsV2Page.hoverDatabook(databookName);
        await dsV2Page.createDatasheet(
          databookName,
          datasheetName,
          "object",
          "New Object 1",
          ""
        );
        await dsV2Page.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsV2Page.closeEditsheetPage();
        const formattedCurrentDateTime = await dsV2Page.generateDatasheet();

        const refreshLocator = await page.locator(
          "//span[contains(text(), 'Last refreshed on')]/span"
        );
        await dsV2Page.letColumnsLoad("id");
        const lastRefreshedOn = await refreshLocator.textContent();
        expect(lastRefreshedOn).toBe(formattedCurrentDateTime);

        await dsV2Page.datasheetMoreActions(datasheetName);
        await page.locator("span", { hasText: "Pin this sheet" }).click();
        let isPinedPresent = await dsV2Page.validateInPinned(datasheetName);
        expect(isPinedPresent).toBe(true);

        await dsV2Page.goToTab("Adjustments");
        await expect(page.locator('(//div[@role="tab"])[2]//span')).toHaveCount(
          0
        );

        await dsV2Page.deleteDatasheet();
        const dsDeletionMSG = await page.getByText(
          datasheetName + " deleted successfully."
        );
        await expect(dsDeletionMSG).toBeVisible();
        await dsV2Page.deleteDatabook(databookName);
        await dsV2Page.dialogPrompt("span", "Databook deleted successfully.");
        isPinedPresent = await dsV2Page.validateInPinned(datasheetName);
        expect(isPinedPresent).toBe(false);
      }
    );

    test(
      "Deletion of dependant sheet and Verifying error message is displayed",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T5914, INTER-T5915, INTER-T5924, INTER-T15954",
          },
          {
            type: "Description",
            description:
              "Validate Error message is shown when a user tries to delete a sheet which is used by other datasheet.",
          },
          {
            type: "Precondition",
            description:
              "Create test-datasheet-1 in test-databook-1, test-datasheet-2 in test-databook-2, test-datasheet-3 in test-databook-3. Perform union on test-datasheet-1 with test-datasheet-2. Perform data source on test-datasheet-3 with test-databook-1",
          },
          {
            type: "Expected Behaviour",
            description: "User should not be able to delete datasheet.",
          },
        ],
      },
      async ({ adminPage }) => {
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const databookName1 = "test-databook-1";
        const databookName2 = "test-databook-2";
        const datasheetName2 = "test-datasheet-2";
        await dsV2Page.goToDatasheet(databookName2, datasheetName2);
        await dsV2Page.deleteDatasheet();
        await dsV2Page.dialogPrompt(
          "span",
          "Error: Cannot delete datasheet test-datasheet-2 since the following datasheets are dependent on this datasheet"
        );
        await dsV2Page.cancelDeletion();
        await dsV2Page.deleteDatabook(databookName1);
        await dsV2Page.dialogPrompt("span", "Error: Databook has dependencies");
      }
    );

    test(
      "Validate user is able to export csv data, pre-adjusted data, post-adjusted data.",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T5944, INTER-T5945, INTER-T5946",
          },
          {
            type: "Description",
            description:
              "Validate user is able to export csv data, pre-adjusted data, post-adjusted data.",
          },
          {
            type: "Precondition",
            description: "Create a datasheet.",
          },
          {
            type: "Expected Behaviour",
            description: "User should be able to export the data.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const databookName = "test-databook-3";
        const datasheetName = "test-datasheet-3";
        await dsV2Page.goToDatasheet(databookName, datasheetName);
        await dsV2Page.clickDatasheetMenu();
        await page.getByRole("menuitem", { name: "Export" }).click();
        await page.getByRole("menuitem", { name: "Pre Adjustment" }).click();
        await dsV2Page.dialogPrompt("span", "Downloaded Successfully!!");
        await dsV2Page.clickDatasheetMenu();
        await page.getByRole("menuitem", { name: "Export" }).click();
        await page.getByRole("menuitem", { name: "Post Adjustment" }).click();
        await dsV2Page.dialogPrompt("span", "Downloaded Successfully!!");
      }
    );

    test(
      "Validate error message on duplicate naming of databook/datasheet",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T5930, INTER-T5931" },
          {
            type: "Description",
            description:
              "Validate error message is seen when user tries to give existing name to the sheet.\n Validate error message is seen when user tries to give existing name to the databook",
          },
          {
            type: "Precondition",
            description:
              "Create a databook and datasheet with name test-databook-1, test-datasheet-1.",
          },
          {
            type: "Expected Behaviour",
            description:
              "User should see a error message saying \n Error: Databook with name test-databook-1 already exists.\n Error: Datasheet with name test-datasheet-1 already exists.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const ExistingDataBook = "test-databook-1";
        const ExistingDataSheet = "test-datasheet-1";
        const NewDataBook = "db2";
        await dsV2Page.databookMoreActions(NewDataBook);
        const dbookActionsLocator = page.locator(".ant-dropdown-menu span");
        const receivedDBookActions =
          await dbookActionsLocator.allTextContents();
        const expectedDBookActions = [
          "Rename",
          "Refresh all sheets",
          "Show dependencies",
          "Clone databook",
          "Archive",
          "Delete",
        ];
        expect(receivedDBookActions).toEqual(expectedDBookActions);
        await dsV2Page.goToDatasheet(ExistingDataBook, "table-join-1");
        await dsV2Page.letColumnsLoad("id");
        await dsV2Page.createDatasheet(
          ExistingDataBook,
          ExistingDataSheet,
          "object",
          "New Object 1",
          ""
        );
        const element = "span";
        const text =
          "Error: Datasheet with name test-datasheet-1 already exists.";
        const prompt = page.locator(element, { hasText: text });
        await expect(prompt).toBeVisible({ timeout: 10000 });
        await dsV2Page.closeDatasheetPopup();
        await dsV2Page.renameDatabook(NewDataBook, ExistingDataBook);
        await dsV2Page.dialogPrompt(
          "span",
          "Error: Databook with name test-databook-1 already exists."
        );
      }
    );

    test(
      "Validate datasheet creation with different sources.",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T5926, INTER-T5949, INTER-T5950, INTER-T5951, INTER-T5952, INTER-T5953, INTER-T5955, INTER-T15945, INTER-T15946",
          },
          {
            type: "Description",
            description:
              "Validate datasheet with - \n Report Object as source \n Datasheet as source \n Union \n Join \n Group By Filter \n Advanced Filter.",
          },
          {
            type: "Precondition",
            description: "",
          },
          {
            type: "Expected Behaviour",
            description: "User should be able to create new datasheet.",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        testInfo.setTimeout(testInfo.timeout + 800000);
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const NewDBookName = "validate dbook - diff sources";
        await dsV2Page.clickDatabook(NewDBookName);
        await dsV2Page.validateDataSources(
          NewDBookName,
          "sheet - report object",
          "report",
          "Commission",
          ""
        );

        await dsV2Page.validateDataSources(
          NewDBookName,
          "sheet - datasheet",
          "datasheet",
          "test-databook-1",
          "test-datasheet-1"
        );

        await dsV2Page.validateDataSources(
          NewDBookName,
          "sheet - join",
          "datasheet",
          "test-databook-1",
          "table-join-1"
        );

        await dsV2Page.validateDataSources(
          NewDBookName,
          "sheet - GrpByFilter",
          "datasheet",
          "test-databook-1",
          "test-datasheet-grpByFilter"
        );

        await dsV2Page.validateDataSources(
          NewDBookName,
          "sheet - AdvFilter",
          "datasheet",
          "test-databook-1",
          "test-datasheet-advFilter"
        );

        await dsV2Page.databookRefreshSheets(NewDBookName, "id", false);
      }
    );

    test(
      "Verify DataSheet-V2 testcases like left pane expand/collide, Recent sheets, Pinned Sheets, View By Dropdown, Datasheet",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T15933, INTER-TT15934, INTER-T15935, INTER-T15937, INTER-T15939, INTER-T15940, INTER-T15947, INTER-T15948, INTER-T15949, INTER-T15950, INTER-T15951, INTER-T15952, INTER-T15955, INTER-T15956, INTER-T15957, INTER-T15958, INTER-T15959, INTER-T15960, INTER-T15961, INTER-T15962",
          },
          {
            type: "Description",
            description: ".",
          },
          {
            type: "Precondition",
            description: "",
          },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const databookName = "test-databook-2";
        const datasheetName = "test-datasheet-2";
        const newdatasheetName = "test-datasheet-2-renamed";

        await dsV2Page.verifyDbookExpansion();

        const recentsText = await page.locator("span", { hasText: "Recents" });
        await dsV2Page.goToDatasheet(databookName, datasheetName);

        const refreshDetails = await page.locator(
          "(//div[@id='datasheet-canvas-view'])[1]//span[contains(text(), 'Last refreshed on')]"
        );
        await expect(refreshDetails).toBeVisible();

        const editBtn = await page.locator("button", {
          hasText: "Edit",
        });
        await expect(editBtn).toBeVisible();

        let isRecentPresent = await dsV2Page.validateInRecents(datasheetName);
        expect(isRecentPresent).toBe(true);

        await dsV2Page.collapseLeftPane();
        await expect(recentsText).toBeHidden();
        await dsV2Page.expandLeftPane();
        await expect(recentsText).toBeVisible();

        const dbLocator = await page
          .locator(".AccordionRoot span", {
            hasText: databookName,
          })
          .last();
        await dbLocator.waitFor({ state: "visible" });

        await dsV2Page.renameDatasheet(datasheetName, newdatasheetName);

        await dsV2Page.datasheetMoreActions(newdatasheetName);
        const dsheetActionsLocator = page
          .locator(".ant-dropdown-menu")
          .last()
          .locator("span");
        let receivedDSheetActions =
          await dsheetActionsLocator.allTextContents();
        receivedDSheetActions = receivedDSheetActions.slice(0, -1);
        const expectedDSheetActions = [
          "Edit sheet",
          "Rename sheet",
          "Open in new tab",
          "Sheet details",
          "Pin this sheet",
          "Show dependencies",
          "Copy link to sheet",
          "Clone this sheet",
          "Delete",
          "Last refreshed on",
        ];
        expect(receivedDSheetActions).toEqual(expectedDSheetActions);

        await page.locator("span", { hasText: "Pin this sheet" }).click();
        const isPinedPresent = await dsV2Page.validateInPinned(
          newdatasheetName
        );
        expect(isPinedPresent).toBe(true);

        isRecentPresent = await dsV2Page.validateInRecents(newdatasheetName);
        expect(isRecentPresent).toBe(true);
        const name = await dsV2Page.getDatasheetCanvasHeading();
        expect(name).toBe(newdatasheetName);

        await page.evaluate(() => {
          navigator.clipboard.writeText = async (text) => {
            console.log(`Mocked clipboard text: ${text}`);
          };
        });

        await dsV2Page.goToDatasheet(databookName, newdatasheetName);
        await dsV2Page.datasheetMoreActions(newdatasheetName);
        await page.locator("span", { hasText: "Copy link to sheet" }).click();
        await expect(page.locator("text='Copied to clipboard'")).toBeVisible();

        await dsV2Page.datasheetMoreActions(newdatasheetName);
        const currentURl = page.url();
        const newPage = await dsV2Page.verifyOpenNewTab();
        const newTabURL = newPage.url();
        await newPage.close();
        expect(newTabURL).toBe(currentURl);

        await dsV2Page.renameDatasheet(newdatasheetName, datasheetName);

        console.log("***** Verify sheet details *****");
        await dsV2Page.clickDatabook("Freelance-Commissions");
        await dsV2Page.datasheetMoreActions("commission-table");
        await dsV2Page.verifySheetDetails();

        console.log(
          "***** Validate all commission plan related databooks are present in Commission Plans dropdown *****"
        );
        await dsV2Page.viewBy("Commission Plans");
        for (const plan of [
          "test-commission-plan",
          "commissionPlan-previewNothing",
          "freelance-commission-plan2",
        ]) {
          const result = await dsV2Page.verifyCommissionPlans(plan);
          expect(result.isPlanVisible).toBe(true);
        }
      }
    );

    test(
      "Verify Tabs, Views, and Filters, Sorting column in Datasheet canvas.",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T15963, INTER-T15964, INTER-T15964, INTER-T15965, INTER-T15966, INTER-T15967, INTER-T15968, INTER-T15969, INTER-T15973",
          },
          {
            type: "Description",
            description:
              "Verify Filters Expressions, Saved Filters and View retain, Customized columns, All data Tab, Adjustments Tab.",
          },
          {
            type: "Precondition",
            description: "",
          },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const databookName = "test-databook-3";
        const datasheetName = "test-datasheet-3";
        await dsV2Page.goToDatasheet(databookName, datasheetName);
        await dsV2Page.letColumnsLoad("id");

        const TabstoCheck = ["All data", "Adjustments", "New View"];
        const isVisible = await dsV2Page.verifyTabs(TabstoCheck);
        expect(isVisible).toBe(true);
        await page
          .locator(
            `//div[@role='tab' and contains(., '${TabstoCheck[0]}')]//span`
          )
          .filter({ hasText: "03" })
          .waitFor({ state: "visible" });
        const AlldataCount = await dsV2Page.getTabCount(TabstoCheck[0]);
        expect(AlldataCount).toBe("03");
        const adjustmentCount = await dsV2Page.getTabCount(TabstoCheck[1]);
        expect(adjustmentCount).toBe("01");
        const newViewCount = await dsV2Page.getTabCount(TabstoCheck[2]);
        expect(newViewCount).toBe("02");

        await page.waitForLoadState("networkidle");
        // await dsV2Page.clickTabButton("Show/hide columns");
        await dsV2Page.customizeColumn("date");
        await dsV2Page.letColumnsLoad("id");
        const isColumnPresent = await dsV2Page.verifyCustomizeColumn("date");
        expect(isColumnPresent).toBe(false);

        await dsV2Page.goToTab(TabstoCheck[2]);
        const isFilterVisible = await dsV2Page.isFilterVisible();
        expect(isFilterVisible).toBe(true);
        const filterCalculation = await dsV2Page.getFilterCalc();
        expect(filterCalculation).toBe("commission>2000");

        await dsV2Page.goToDatasheet("test-databook-1", "table-join-2");
        await dsV2Page.sortColumn("id", "ASC");
        const valuesReceived = await dsV2Page.getColumnValues("id", "id");
        const valuesExpected = ["3", "4", "5", "6", "7"];
        expect(valuesReceived).toEqual(valuesExpected);
      }
    );

    test(
      " Verify whether the records are displayed based on the Pagination applied",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T15974",
          },
          {
            type: "Description",
            description: "Verify Pagination details.",
          },
          {
            type: "Precondition",
            description:
              "Create a datasheet with 100 rows for pagination verification.",
          },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        await dsV2Page.goToDatasheet("test-databook-4", "test-pagination");
        await dsV2Page.sortColumn("id", "ASC");
        let valuesReceived = await dsV2Page.getColumnValues("id", "id");
        let expectedValues = Array.from({ length: 50 }, (_, i) =>
          (i + 1).toString()
        );
        expect(valuesReceived).toEqual(expectedValues);
        const value = await page
          .locator("//div[@data-testid='ever-select']//span[@title='50']")
          .textContent();
        expect(value).toBe("50");
        const locator = page.locator("//div[@data-testid='pt-row-count']");
        await locator.waitFor({ state: "visible" });
        const textContent = await locator.textContent();
        expect(textContent).toBe("1 - 50of100rows");
        const buttonLocator = page.locator(
          "//div[@data-testid='pt-row-count']/following-sibling::div/button"
        );
        await buttonLocator.nth(3).click();
        await page.waitForLoadState("networkidle");
        valuesReceived = await dsV2Page.getColumnValues("id", "id");
        expectedValues = Array.from({ length: 50 }, (_, i) =>
          (i + 51).toString()
        );
        expect(valuesReceived).toEqual(expectedValues);
      }
    );

    test(
      "Verify change in datasheet variable names is reflected across the application",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "Tests cases covered are from Datasheet-V2 Automation excel sheet",
          },
          {
            type: "Description",
            description:
              "Verify datasheet variable changes is reflected across all pages such as custom objects, edit sheet, plans/forecast(along with statement and simualte columns), crystal, settlements, and analytics dashboard.",
          },
          {
            type: "Precondition",
            description:
              "Create a datasheet and have plans, forecasts, crystal and analytics dashboard.",
          },
          {
            type: "Expected Behaviour",
            description: "",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(adminPage.page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);

        const currentSheetName = "commission-table";
        const newSheetName = "commission-table-new";
        const renameVariables = [
          ["CF sales amount", "CF sales"],
          ["sales amount", "sale"],
          ["commission percentage", "comm percent"],
          ["commission amount", "commission"],
          ["freelancer email", "email"],
          ["sales date", "sale date"],
        ];

        const newVariables = [
          "sale",
          "comm percent",
          "commission",
          "sale date",
          "email",
          "CF sales",
        ];

        await dsV2Page.goToDatasheet("Freelance-Commissions", currentSheetName);
        await dsV2Page.datasheetMoreActions(currentSheetName);
        await page.getByRole("menuitem", { name: "Edit sheet" }).click();
        const editSheetUrl =
          "http://localhost:3000/datasheet?id=53406e13-f541-4cd5-97cf-80a05bc51c11&name=commission-table&viewId=all_data&isEditView=true";

        expect(page.url()).toBe(editSheetUrl);

        await expect(page.locator(".ant-drawer-title")).toContainText(
          "Edit commission-tableSave"
        );

        await dsV2EditPage.hoverOnField("id");
        await expect(
          page.getByText(
            "Primary columns are essential and cannot be deselected"
          )
        ).toBeVisible();

        console.log(
          "***** Rename variable fiels in Edit page and validate the new changes are reflected in All Tabs or not *****"
        );
        await dsV2EditPage.renameFields(renameVariables);
        await dsV2EditPage.saveEdits();
        await dsV2Page.fetchLatestData();
        await dsV2Page.letColumnsLoad("id");

        let isColumnPresent;
        for (const variable of newVariables) {
          console.log(
            "verify new variable is present in the all data tab - ",
            variable
          );
          isColumnPresent = await dsV2Page.verifyCustomizeColumn(variable);
          expect(isColumnPresent).toBe(true);
        }

        await dsV2Page.goToTab("Adjustments");
        await dsV2Page.letColumnsLoad("Adjustment ID");
        for (const variable of newVariables) {
          console.log(
            "verify new variable is present in the Adjustments tab - ",
            variable
          );
          isColumnPresent = await dsV2Page.verifyCustomizeColumn(variable);
          expect(isColumnPresent).toBe(true);
        }

        await dsV2Page.goToTab("New View");
        await dsV2Page.letColumnsLoad("id");
        for (const variable of newVariables) {
          console.log(
            "verify new variable is present in the New View tab - ",
            variable
          );
          isColumnPresent = await dsV2Page.verifyCustomizeColumn(variable);
          expect(isColumnPresent).toBe(true);
        }

        console.log(
          "***** Rename the datasheet name and verify its reflected in canvas view, databook list and recents in databook.*****"
        );
        await dsV2Page.EditSheetName(currentSheetName, newSheetName);

        console.log(
          "***** Verify the updated datasheet and variable names reflected in plans/forecasts/crystal of both draft and published plans *****"
        );
        await dsV2Page.ValidatePlanVariables(
          newVariables,
          "plans",
          "freelance-commission-plan1",
          "freelance-commission-plan1-simple-component",
          [["CF sales", 1]],
          "freelance-commission-plan1-conditional-component",
          [
            ["sale", 2],
            ["commission", 4],
            ["comm percent", 1],
          ]
        );
        await dsV2Page.ValidatePlanVariables(
          newVariables,
          "plans",
          "freelance-commission-plan2",
          "freelance-commission-plan1-simple-component",
          [["CF sales", 1]],
          "freelance-commission-plan1-conditional-component",
          [
            ["sale", 2],
            ["commission", 3],
          ]
        );
        await dsV2Page.ValidatePlanVariables(
          newVariables,
          "forecasts",
          "freelance-forecast-plan1",
          "freelance-forecast-plan1-simple-component",
          [["CF sales", 1]],
          "freelance-forecast-plan1-conditional-component",
          [
            ["sale", 2],
            ["commission", 4],
            ["comm percent", 1],
          ]
        );
        await dsV2Page.ValidatePlanVariables(
          newVariables,
          "forecasts",
          "freelance-forecast-plan2",
          "freelance-forecast-plan2-simple-component",
          [["CF sales", 1]],
          "freelance-forecast-plan2-conditional-component",
          [
            ["sale", 3],
            ["commission", 1],
            ["comm percent", 2],
          ]
        );

        await dsV2Page.ValidateCrytalVariables(
          "Freelance-crystal-1",
          newVariables,
          false
        );
        await dsV2Page.ValidateCrytalVariables(
          "Freelance - crystal 2",
          newVariables,
          true
        );

        console.log(
          "***** Validate variable name changes are reflected on transformation applied datasheets *****"
        );
        await page.goto("/datasheet", { waitUntil: "networkidle" });
        await dsV2Page.goToDatasheet("test-databook-1", "table-join-2");
        await dsV2Page.datasheetMoreActions("table-join-2");
        await page.getByRole("menuitem", { name: "Edit sheet" }).click();
        await expect(page.locator(".ant-drawer-title")).toContainText(
          "Edit table-join-2Save"
        );
        await dsV2EditPage.renameFields([["id", "id-2-edited"]]);
        await dsV2EditPage.saveEdits();
        await dsV2Page.letColumnsLoad("id-2-edited");

        await dsV2Page.goToDatasheet("test-databook-1", "table-join-1");
        await dsV2Page.datasheetMoreActions("table-join-1");
        await page.getByRole("menuitem", { name: "Edit sheet" }).click();
        await expect(page.locator(".ant-drawer-title")).toContainText(
          "Edit table-join-1Save"
        );
        await expect(page.locator("div[name=rhsLookupCols]")).toContainText(
          "id-2-edited"
        );
        await expect(page.locator("div[name=rhsCols]")).toContainText(
          "id-2-edited"
        );
      }
    );

    test(
      "Verify pin is working as expected",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T22849,INTER-T22850,INTER-T22851,INTER-T22852,INTER-T22853,INTER-T22854,INTER-T22855",
          },
          {
            type: "Description",
            description: "All the scenarios are covered",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "Changing column order are present successfully and the relevant records must be displayed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dbName = "Columnorder-DB";
        const dsName = "columnorder-sheet";
        await dsV2Page.goToDatasheet(dbName, dsName);
        await dsV2Page.sortColumn("id", "ASC");
        await dsV2Page.pinColumn("freelancer_name");
      }
    );
  }
);
