import DatasheetV2EditViewPage from "../../../../test-objects/datasheet-v2-editView-objects";
import DatasheetV2Page from "../../../../test-objects/datasheet-v2-objects";
import V2Transformations from "../../../../test-objects/datasheetV2_transformations-objects";
const {
  databookUI2Fixtures: { test, expect },
} = require("../../../fixtures");
const {
  datasheetProdAutomateFixtures: { test: test1, expect: expect1 },
} = require("../../../fixtures");

test.describe(
  "Datasheet V2 Transformations",
  { tag: ["@regression", "@datasheet", "@adminchamp-3"] },
  () => {
    test.beforeEach(async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 200000);
      const v2Transformations = new V2Transformations(page);
      await v2Transformations.navigateToDatasheetPage();
    });

    test(
      "Create a sheet with UNION",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8994" },
          {
            type: "Description",
            description:
              "Create a sheet with UNION transformation and verify the records",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The datasheet must be generated successfully and the relevant records must be displayed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        testInfo.setTimeout(testInfo.timeout + 100000);

        await v2Transformations.createSheetWithUnion("Transformation Book");
        await v2Transformations.verifyResultcreateSheetWithUnion();
      }
    );

    test(
      "Create new sheets with JOINS",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8994" },
          {
            type: "Description",
            description:
              "Create new sheets with all Join transformations and verify the records",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The datasheet must be generated successfully and the relevant records must be displayed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        testInfo.setTimeout(testInfo.timeout + 200000);

        // Left Join
        await v2Transformations.createSheetWithJoin(
          "Transformation Book",
          "Left"
        );
        await v2Transformations.verifyResultcreateSheetWithJoinLeft();
        // Right Join
        await v2Transformations.createSheetWithJoin(
          "Transformation Book",
          "Right"
        );
        await v2Transformations.verifyResultcreateSheetWithJoinRight();
        // Inner Join
        await v2Transformations.createSheetWithJoin(
          "Transformation Book",
          "Inner"
        );
        await v2Transformations.verifyResultcreateSheetWithJoinInner();
        // Full Join
        await v2Transformations.createSheetWithJoin(
          "Transformation Book",
          "Full"
        );
        await v2Transformations.verifyResultcreateSheetWithJoinFull();
      }
    );

    test(
      "Create a sheet with GROUP BY",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8994" },
          {
            type: "Description",
            description:
              "Create a sheet with GROUP BY transformation and verify the records",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The datasheet must be generated successfully and the relevant records must be displayed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        testInfo.setTimeout(testInfo.timeout + 200000);

        await v2Transformations.createSheetWithGroupBy("Transformation Book");
        await v2Transformations.verifyResultcreateSheetWithGroupBy();
      }
    );

    test(
      "Create a sheet with FILTER",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8994" },
          {
            type: "Description",
            description:
              "Create a sheet with FILTER transformation and verify the records",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The datasheet must be generated successfully and the relevant records must be displayed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const v2Transformations = new V2Transformations(page);
        testInfo.setTimeout(testInfo.timeout + 200000);

        await v2Transformations.createSheetWithFilter("Transformation Book");
        await dsV2Page.sortColumn("ID", "ASC");
        const valuesReceived = await dsV2Page.getColumnValues("ID", "id");
        expect(valuesReceived).toStrictEqual(["26", "27"]);
      }
    );

    test(
      "Create a sheet with GET USER PROPERTIES",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8994" },
          {
            type: "Description",
            description:
              "Create a sheet with GET USER PROPERTIES transformation and verify the records",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The datasheet must be generated successfully and the relevant records must be displayed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        testInfo.setTimeout(testInfo.timeout + 200000);

        await v2Transformations.createSheetWithGetUserProperties(
          "Transformation Book"
        );
        await v2Transformations.verifyResultcreateSheetWithGetUserProperties();
      }
    );

    test(
      "Create a sheet with TEMPORAL SPLICE",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8994" },
          {
            type: "Description",
            description:
              "Create a sheet with TEMPORAL SPLICE transformation and verify the records",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The datasheet must be generated successfully and the relevant records must be displayed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        const DatasheetV2 = new DatasheetV2Page(page);
        testInfo.setTimeout(testInfo.timeout + 200000);

        await v2Transformations.createSheetWithTemporalSplice(
          "Transformation Book"
        );
        await v2Transformations.verifyResultcreateSheetWithTemporalSplice();
      }
    );

    test(
      "Create a sheet with FLATTEN HIERARCHY",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8994" },
          {
            type: "Description",
            description:
              "Create a sheet with FLATTEN HIERARCHY transformation and verify the records",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The datasheet must be generated successfully and the relevant records must be displayed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        testInfo.setTimeout(testInfo.timeout + 200000);

        await v2Transformations.createSheetWithFlattenHierarchy(
          "Transformation Book",
          "String"
        );
        await v2Transformations.verifyResultcreateSheetWithFlattenHierarchyString();

        await v2Transformations.createSheetWithFlattenHierarchy(
          "Transformation Book",
          "Email"
        );
        await v2Transformations.verifyResultcreateSheetWithFlattenHierarchyEmail();
      }
    );

    test(
      "Multiple Transformations - LEFT JOIN & FILTER",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8994" },
          {
            type: "Description",
            description:
              "Create a sheet with LEFT JOIN & FILTER transformations and verify the records",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The datasheet must be generated successfully and the relevant records must be displayed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        testInfo.setTimeout(testInfo.timeout + 200000);

        await v2Transformations.createSheetWithJoinAndFilter(
          "Transformation Book",
          "Left"
        );
        await v2Transformations.verifyResultcreateSheetWithJoinAndFilter();
      }
    );

    test(
      "Multiple Transformations - LEFT JOIN & GROUP BY",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8994" },
          {
            type: "Description",
            description:
              "Create a sheet with LEFT JOIN & GROUP BY transformations and verify the records",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The datasheet must be generated successfully and the relevant records must be displayed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        testInfo.setTimeout(testInfo.timeout + 200000);

        await v2Transformations.createSheetWithJoinAndGroupBy(
          "Transformation Book",
          "Left"
        );
        await v2Transformations.verifyResultcreateSheetWithJoinAndGroupBy();
      }
    );

    test(
      "Multiple Transformations - FILTER & GROUP BY",
      {
        annotation: [
          { type: "Test ID", description: "INTER-8994" },
          {
            type: "Description",
            description:
              "Create a sheet with FILTER & GROUP BY transformations and verify the records",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The datasheet must be generated successfully and the relevant records must be displayed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        testInfo.setTimeout(testInfo.timeout + 200000);

        await v2Transformations.createSheetWithFilterAndGroupBy(
          "Transformation Book"
        );
        await v2Transformations.verifyResultcreateSheetWithFilterAndGroupBy();
      }
    );
  }
);

test1.describe(
  "Datasheet V2 Transformations - Prod bugs",
  { tag: ["@regression", "@datasheet", "@adminchamp-3"] },
  () => {
    test1.beforeEach(async ({ adminPage }, testInfo) => {
      const page = adminPage.page;
      testInfo.setTimeout(testInfo.timeout + 200000);
      await page.goto(
        "/datasheet?id=00bb7248-f49c-4bf6-96fb-1714d74dcd21&name=dummy&viewId=all_data",
        { waitUntil: "networkidle" }
      );
    });

    test1(
      "Verify no prefix is added to the first string value of string array in expression box for any filter",
      {
        annotation: [
          { type: "Test ID", description: "" },
          {
            type: "Description",
            description:
              "Create a string array for any filter in transformations expression box, ex- ['Vinith', 'Rijo']",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The string array should be saved as ['Vinith', 'Rijo'] and not [' Vinith', 'Rijo']",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const editPage = new DatasheetV2EditViewPage(page);
        const v2Transformations = new V2Transformations(page);

        await dsV2Page.goToDatasheet("prod-DB", "sheet1");
        await dsV2Page.clickEditBtn();
        await v2Transformations.addTransformation("Filter");
        await editPage.clickOnPlaceholder();
        await editPage.fillPlaceholder('["Vinith", "Rijo"]');
        await editPage.selectConstant('["Vinith","Rijo"]', "StringArray");
        const receivedValue = await editPage.getEBvalue();
        expect(receivedValue[0]).toBe('["Vinith","Rijo"]');
        await editPage.quitEdit();
      }
    );

    test1(
      "BugFix PR-9992 = Duplicate variables were displayed in right columns pane",
      {
        annotation: [
          { type: "Test ID", description: "PR-9992" },
          {
            type: "Description",
            description:
              "Verify that duplicate varibales are not displayed in columns pane, when new variables are introduced in source sheet",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "Duplicate variables should not be listed in columns pane",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        const dsObjects = new DatasheetV2Page(page);
        const v2EditPage = new DatasheetV2EditViewPage(page);

        //Creating 2 sheets
        await dsObjects.createDatasheet(
          "prod-DB",
          "Transform 1",
          "object",
          "freelance-comm-50-obj",
          ""
        );
        await dsObjects.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsObjects.closeEditsheetPage();
        await dsObjects.createDatasheet(
          "prod-DB",
          "Transform 2",
          "datasheet",
          "prod-DB",
          "Transform 1"
        );
        await dsObjects.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsObjects.closeEditsheetPage();

        await dsObjects.goToDatasheet("prod-DB", "Transform 2");
        await dsObjects.clickEditBtn();
        await v2Transformations.addTransformation("Filter");
        await v2EditPage.clickOnPlaceholder();
        await v2EditPage.fillPlaceholder("ID");
        await v2EditPage.selectConstant("ID", "Integer");
        await page.keyboard.type(">");
        await v2EditPage.selectOperator(">");
        await page.keyboard.type("25");
        await v2EditPage.selectConstant("25", "Integer");
        await page.keyboard.press("Escape");
        await v2Transformations.validate(true);

        await v2Transformations.addTransformation("Filter");
        await v2EditPage.clickOnPlaceholder();
        await v2EditPage.selectConstant("Client Name", "String");
        await v2EditPage.selectOperator("==");
        await page.keyboard.type("Client b");
        await v2EditPage.selectConstant("Client b", "String");
        await page.keyboard.press("Escape");
        await v2Transformations.validateAndSave();

        //Add variable in source sheet through CF
        await dsObjects.goToDatasheet("prod-DB", "Transform 1");
        await dsObjects.clickEditBtn();
        await v2EditPage.clickAddFormula();
        await v2EditPage.formulaNameType("Automation CF", "Integer");
        await v2EditPage.clickFormulaExpressionBox();
        await v2EditPage.typeFormulaExpression("ID");
        await v2EditPage.typeFormulaExpression("*");
        await v2EditPage.typeFormulaExpression("10");
        await v2EditPage.clickCreateFn();
        await v2EditPage.saveEdits();

        await dsObjects.goToDatasheet("prod-DB", "Transform 2");
        await dsObjects.clickEditBtn();
        await expect(
          page.locator("//span[text()='Automation CF']")
        ).toHaveCount(1);
        await v2EditPage.verifyColumn("Automation CF", "Unchecked");
      }
    );

    test1(
      "BugFix PR-9969, INTER 11744 = Getting internal server error while removing a transformation in cloned sheet.",
      {
        annotation: [
          { type: "Test ID", description: "PR-9969" },
          {
            type: "Description",
            description:
              "Verify that Internal Server Error is not displayed on deleting a transformation in cloned sheet, when the CF added in source is added as display column in derived sheet",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description: "Internal Server Error must not be displayed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        const dsObjects = new DatasheetV2Page(page);
        const v2EditPage = new DatasheetV2EditViewPage(page);

        //Creating 2 sheets
        await dsObjects.createDatasheet(
          "prod-DB",
          "Joined Clone Source",
          "object",
          "freelance-comm-50-obj",
          ""
        );
        await dsObjects.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await v2EditPage.clickAddFormula();
        await v2EditPage.formulaNameType("Clone CF", "Integer");
        await v2EditPage.clickFormulaExpressionBox();
        await v2EditPage.typeFormulaExpression("ID");
        await v2EditPage.typeFormulaExpression("*");
        await v2EditPage.typeFormulaExpression("10");
        await v2EditPage.clickCreateFn();
        await v2EditPage.saveEdits();

        await dsObjects.createDatasheet(
          "prod-DB",
          "Joined Clone Derived",
          "datasheet",
          "prod-DB",
          "Joined Clone Source"
        );
        await dsObjects.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );

        //Add the created CF in display columns
        await v2Transformations.addTransformation("Join");
        await page
          .locator("div")
          .filter({ hasText: /^Select Datasheet$/ })
          .nth(2)
          .click();
        await page.keyboard.type("Joined Clone Source");
        await page.keyboard.press("Enter");
        await page.locator("[name='lhsLookupCols']").click();
        await page.keyboard.type("ID");
        await page.keyboard.press("Enter");
        await page.locator("[name='rhsLookupCols']").click();
        await page.keyboard.type("ID");
        await page.keyboard.press("Enter");
        await page.keyboard.press("Escape");
        await v2Transformations.validate(true);

        //Add Filter transformation
        await v2Transformations.addTransformation("Filter");
        await v2EditPage.clickOnPlaceholder();
        await v2EditPage.fillPlaceholder("ID");
        await v2EditPage.selectConstantByIndex("ID", "Integer", "0");
        await page.keyboard.type(">");
        await v2EditPage.selectOperator(">");
        await page.keyboard.type("25");
        await v2EditPage.selectConstant("25", "Integer");
        await page.keyboard.press("Escape");
        await v2Transformations.validateAndSave();

        await dsObjects.clickDatasheetMenu();
        await dsObjects.cloneDatasheet();
        await dsObjects.verifyClone("prod-DB", "Joined Clone Derived_Copy");
        await dsObjects.goToDatasheet("prod-DB", "Joined Clone Derived_Copy");
        await dsObjects.clickEditBtn();
        await v2Transformations.deleteTransformation("2");
        const error = await page.getByText("Internal Server Error");
        await error.waitFor({ state: "hidden" });
        await expect(page.locator('div[class="flex"]>svg')).toHaveCount(1);
        await v2EditPage.saveEdits();
        const error2 = await page.getByText(
          "Current update is stale, please refresh the page and try again"
        );
        await error2.waitFor({ state: "hidden" });
        await dsObjects.clickEditBtn(); //to ensure that the edits are saved successfully
      }
    );

    test1(
      "QA Bug - INTER-11445 = while creating a datasheet with transformations and deleting few columns in the joins transformation, deleted ones are added as primary key and datasheet is not getting generated",
      {
        annotation: [
          { type: "Test ID", description: "INTER-11445" },
          {
            type: "Description",
            description:
              "Verify whether deleted fields from display Columns field are not added as Primary Keys",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The deleted fields should not be added as Primary Keys",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        const dsObjects = new DatasheetV2Page(page);
        const v2EditPage = new DatasheetV2EditViewPage(page);

        //Creating 2 sheets
        await dsObjects.createDatasheet(
          "prod-DB",
          "Primary Key Source",
          "object",
          "freelance-comm-50-obj",
          ""
        );
        await dsObjects.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsObjects.closeEditsheetPage();
        await dsObjects.createDatasheet(
          "prod-DB",
          "Primary Key Derived",
          "datasheet",
          "prod-DB",
          "Primary Key Source"
        );
        await dsObjects.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );

        //Add Join
        await v2Transformations.addTransformation("Join");
        await v2Transformations.joinWith("prod-DB", "Primary Key Source", "1");
        await v2Transformations.clickLookupColumns("lhs", "1");
        await page.keyboard.type("ID");
        await page.keyboard.press("Enter");
        await v2Transformations.clickLookupColumns("rhs", "1");
        await page.keyboard.type("ID");
        await page.keyboard.press("Enter");
        await page.keyboard.press("Escape");
        await v2Transformations.validate(true);

        //Add Filter
        await v2Transformations.addTransformation("Filter");
        await v2EditPage.clickOnPlaceholder();
        await v2EditPage.fillPlaceholder("ID");
        await v2EditPage.selectConstantByIndex("ID", "Integer", "0");
        await page.keyboard.type(">");
        await v2EditPage.selectOperator(">");
        await page.keyboard.type("25");
        await v2EditPage.selectConstant("25", "Integer");
        await page.keyboard.press("Escape");

        await v2Transformations.validate(true);

        await v2Transformations.clickOutputColumns("rhs", "1");
        for (let i = 0; i < 3; i++) {
          await page.keyboard.press("Backspace");
        }

        await v2EditPage.saveEdits();
        // await v2EditPage.clickValidateAndSaveBtn();
        await dsObjects.generateDatasheet();
        await dsObjects.clickEditBtn();
        await expect(page.getByText("Primary", { exact: true })).toHaveCount(2);
      }
    );

    test1(
      "QA Bug - INTER-10845 = suggested variables should only be of the selected variables from source sheet. Currently, it shows all variables.",
      {
        annotation: [
          { type: "Test ID", description: "INTER-10845" },
          {
            type: "Description",
            description:
              "Verify that only the selected variables from the source sheet should be listed in display columns of tranformations in derived sheet",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description: "Only the selected variables are listed",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        const dsObjects = new DatasheetV2Page(page);
        const v2EditPage = new DatasheetV2EditViewPage(page);

        //Create source sheet
        await dsObjects.createDatasheet(
          "prod-DB",
          "Variable Source",
          "object",
          "freelance-comm-50-obj",
          ""
        );
        await dsObjects.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );

        //uncheck few columns
        await v2EditPage.unCheckColumn("Commission %", "true");
        await v2EditPage.unCheckColumn("Commission Amount", "true");
        await v2EditPage.unCheckColumn("Freelancer Name", "true");
        await v2EditPage.unCheckColumn("Payment Date", "true");
        await v2EditPage.saveEdits();

        //Create derived sheet
        await dsObjects.createDatasheet(
          "prod-DB",
          "Variable Derived",
          "datasheet",
          "prod-DB",
          "Variable Source"
        );
        await dsObjects.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );

        //Add Join
        await v2Transformations.addTransformation("Join");
        await v2Transformations.joinWith("prod-DB", "Variable Source", "1");
        await v2Transformations.clickLookupColumns("lhs", "1");
        await page.keyboard.type("ID");
        await page.keyboard.press("Enter");
        await v2Transformations.clickLookupColumns("rhs", "1");
        await page.keyboard.type("ID");
        await page.keyboard.press("Enter");
        await page.keyboard.press("Escape");
        await v2Transformations.clickOutputColumns("lhs", "1");
        await expect(page.locator('div[title="Commission %"]')).toBeHidden();
        await expect(
          page.locator('div[title="Commission Amount"]')
        ).toBeHidden();
        await expect(page.locator('div[title="Freelancer Name"]')).toBeHidden();
        await expect(page.locator('div[title="Payment Date"]')).toBeHidden();
        await page.keyboard.press("Escape");
        await v2Transformations.clickOutputColumns("rhs", "1");
        await page
          .locator(
            "//div[contains(@class, 'ant-select-dropdown')]//span[text()='Select All']"
          )
          .last()
          .click();
        await expect(page.locator('div[title="Commission %"]')).toBeHidden();
        await expect(
          page.locator('div[title="Commission Amount"]')
        ).toBeHidden();
        await expect(page.locator('div[title="Freelancer Name"]')).toBeHidden();
        await expect(page.locator('div[title="Payment Date"]')).toBeHidden();
        await page.keyboard.press("Escape");
        await v2Transformations.validate(true);

        //Add Temporal Splice
        await v2Transformations.addTransformation("Temporal Splice");
        await v2Transformations.clickEmailDropdownTemporalSplice();
        await page.locator('div[title="Freelancer Email"]').last().click();
        await v2Transformations.clickTemporalSpliceToggle();
        await v2Transformations.clickStartDateDropdownTemporalSplice();
        await v2Transformations.verifyTextIsHidden("Payment Date");
        await v2Transformations.clickEndDateDropdownTemporalSplice();
        await v2Transformations.verifyTextIsHidden("Payment Date");
      }
    );

    test1(
      "Prod Bug INTER-11301, PR-9705 = Duplicate datasheet variables were created for each joined datasheet field in Databook V2. Datasheet generation fails",
      {
        annotation: [
          { type: "Test ID", description: "INTER-11301" },
          {
            type: "Description",
            description:
              "Verify that duplicate variables are not listed in columns pane",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description: "Column variables must be unique",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        const dsObjects = new DatasheetV2Page(page);
        const v2EditPage = new DatasheetV2EditViewPage(page);

        //Create source sheet
        await dsObjects.createDatasheet(
          "prod-DB",
          "Additional Source",
          "object",
          "freelance-comm-50-obj",
          ""
        );
        await dsObjects.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsObjects.closeEditsheetPage();

        //Create derived sheet
        await dsObjects.createDatasheet(
          "prod-DB",
          "Additional Derived",
          "datasheet",
          "prod-DB",
          "Additional Source"
        );
        await dsObjects.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );

        //Add GroupBy
        await v2Transformations.addTransformation("Group by");
        await page.locator(".ant-select-selection-overflow").click();
        await page.keyboard.type("Client Name");
        await page.keyboard.press("Enter");
        await page.keyboard.press("Escape");
        await v2Transformations.validate(true);

        //Add Join
        await v2Transformations.addTransformation("Join");
        await v2Transformations.joinWith("prod-DB", "Additional Source", "2");
        await v2Transformations.clickLookupColumns("lhs", "2");
        await page.keyboard.type("Client Name");
        await page.keyboard.press("Enter");
        await v2Transformations.clickLookupColumns("rhs", "2");
        await page.keyboard.type("Client Name");
        await page.keyboard.press("Enter");
        await page.keyboard.press("Escape");
        await v2Transformations.clickOutputColumns("rhs", "2");
        await page
          .locator(
            "//div[contains(@class, 'ant-select-dropdown')]//span[text()='Select All']"
          )
          .last()
          .click();
        await v2Transformations.validateAndSave();
        await dsObjects.clickEditBtn();

        //Verify the columns
        await expect(page.getByText("Primary", { exact: true })).toHaveCount(2);
        await v2EditPage.verifyCheckboxesCountToBe(13);
      }
    );

    test1(
      "Prod Bug INTER-10899, PR-9481 = The new Primary key variables added through source sheet must have the source Sheet Name as prefix in Derived sheet",
      {
        annotation: [
          { type: "Test ID", description: "INTER-10899" },
          {
            type: "Description",
            description:
              "Newly added primary key variables in source sheet must be displayed with a unique name(sheet name as prefix), if there are similar fields present already in derived sheet",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description: "Column names must be unique",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const v2Transformations = new V2Transformations(page);
        const dsObjects = new DatasheetV2Page(page);
        const v2EditPage = new DatasheetV2EditViewPage(page);

        //Create source sheet
        await dsObjects.createDatasheet(
          "prod-DB",
          "Prefix Source",
          "object",
          "freelance-comm-50-obj",
          ""
        );
        await dsObjects.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        //Add GroupBy
        await v2Transformations.addTransformation("Group by");
        await page.locator(".ant-select-selection-overflow").click();
        await v2Transformations.selectOptions([
          "ID",
          "Freelancer Name",
          "Client Name",
          "Sale Amount",
        ]);
        await v2Transformations.validate(true);
        await dsObjects.clickSaveBtn();

        //Create derived sheet
        await dsObjects.createDatasheet(
          "prod-DB",
          "Prefix Derived",
          "datasheet",
          "prod-DB",
          "Prefix Source"
        );
        await dsObjects.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );

        //Add Join
        await v2Transformations.addTransformation("Join");
        await v2Transformations.joinWith("prod-DB", "Prefix Source", "1");
        await v2Transformations.clickLookupColumns("lhs", "1");
        await v2Transformations.selectOptions(["ID"]);
        await v2Transformations.clickLookupColumns("rhs", "1");
        await v2Transformations.selectOptions(["ID"]);
        await v2Transformations.validate(true);
        await v2EditPage.verifyCheckboxesCountToBe(9);
        await v2EditPage.saveEdits();

        //Add more variables in Group by of source sheet
        await dsObjects.goToDatasheet("prod-DB", "Prefix Source");
        await dsObjects.clickEditBtn();
        await page.locator(".ant-select-selection-overflow").click();
        await v2Transformations.selectOptions([
          "Commission %",
          "Commission Amount",
          "Sale Date",
          "Payment Date",
          "Status",
          "Freelancer Email",
          "Payment Confirmed",
        ]);
        await v2Transformations.validate(true);
        await v2EditPage.verifyCheckboxesCountToBe(12);
        await v2EditPage.saveEdits();

        // Navigate to Derived sheet & Verify the columns
        await dsObjects.goToDatasheet("prod-DB", "Prefix Derived");
        await dsObjects.clickEditBtn();
        await v2EditPage.verifyCheckboxesCountToBe(23);
        await expect(page.getByText("Primary", { exact: true })).toHaveCount(
          22
        );
        await expect(page.getByText("Prefix Source ::")).toHaveCount(11);
      }
    );

    test1(
      "Some variables created in the source sheet is listed in the Add Calculated Field expression box of the dependent sheet",
      {
        annotation: [
          { type: "Test ID", description: "INTER-10993" },
          {
            type: "Description",
            description: "Validate the function",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The newly added variables in source sheet must be listed",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const v2Transformations = new V2Transformations(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(page);

        await dsV2Page.goToDatasheet("prod-DB", "variable change A");
        await dsV2Page.clickEditBtn();
        await page.locator('div[name="groupBy"] input').last().click();
        await page.locator('div[title="Freelancer Name"]').click();
        await v2Transformations.validateAndSave();
        await dsV2Page.goToDatasheet("prod-DB", "variable change B");
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.clickAddFormula();
        await page.getByPlaceholder("Press Ctrl + H for help").last().click();
        await page
          .getByPlaceholder("Press Ctrl + H for help")
          .last()
          .fill("Freelancer Name");
        await dsV2EditPage.selectFormula("DATASHEET", "Freelancer Name");
      }
    );
    //skipping due to open bug - https://interstage.atlassian.net/browse/ICM-875
    test1.skip(
      "Verify able to create a datasheet with calculated field and Join Transformation",
      {
        annotation: [
          { type: "Test ID", description: "PR-10005" },
          {
            type: "Description",
            description:
              "Verify the ability to create a datasheet with a calculated field and apply a Join Transformation.",
          },
          {
            type: "Precondition",
            description:
              "Datasets with relevant fields available for calculation and join operations",
          },
          {
            type: "Expected Behaviour",
            description:
              "Users should be able to create a datasheet with a calculated field and successfully perform a Join Transformation without Internal Server Error.",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        const v2Transformations = new V2Transformations(page);
        await dsV2Page.createDatasheet(
          "prod-DB",
          "sheet3CalculatedJoin",
          "object",
          "freelance-comm-50-extended-cols-obj",
          ""
        );
        await dsV2EditPage.clickAddFormula();
        await dsV2EditPage.formulaNameType("Amount*2", "Integer");
        let formulaLocator = await dsV2EditPage.selectFormula(
          "DATASHEET COLUMNS",
          "Sale Amount"
        );
        await formulaLocator.click();
        await dsV2EditPage.typeFormulaExpression("*");
        await dsV2EditPage.typeFormulaExpression("2");
        await dsV2EditPage.clickCreateFn();
        await v2Transformations.addTransformation("Join");
        await v2Transformations.fillJoin(
          "Left",
          "prod-DB",
          "sheet1",
          "ID",
          "ID"
        );
        await v2Transformations.validateAndSave();
      }
    );
    test1(
      "Verify that a derived source variable cannot be removed after a series of transformations",
      {
        annotation: [
          { type: "Test ID", description: "INTER-9978" },
          {
            type: "Description",
            description:
              "Verify that a derived source variable cannot be removed after a series of transformations.",
          },
          {
            type: "Precondition",
            description:
              "A derived source variable is created and multiple transformations have been applied",
          },
          {
            type: "Expected Behaviour",
            description:
              "The system should prevent the removal of a derived source variable after a series of transformations have been applied.",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        const v2Transformations = new V2Transformations(page);
        await dsV2Page.createDatasheet(
          "prod-DB",
          "sheet2AsSourceBase",
          "datasheet",
          "prod-DB",
          "sheet2"
        );
        await dsV2Page.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await dsV2Page.closeEditsheetPage();
        await dsV2Page.generateDatasheet();
        await dsV2Page.letColumnsLoad("ID");
        await dsV2Page.createDatasheet(
          "prod-DB",
          "sheet2AsSourceWithTransformation",
          "datasheet",
          "prod-DB",
          "sheet2AsSourceBase"
        );
        await dsV2Page.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await v2Transformations.addTransformation("Join");
        await v2Transformations.fillJoin(
          "Left",
          "prod-DB",
          "sheet1",
          "ID",
          "ID"
        );
        await v2Transformations.validate(true);
        await v2Transformations.addTransformation("Union");
        await v2Transformations.fillUnion(
          "prod-DB",
          "join-sheet1-sheet2",
          "Freelancer Name",
          "Freelancer Name"
        );
        await page.waitForTimeout(1000);
        await v2Transformations.validateAndSave();
        await dsV2Page.goToDatasheet("prod-DB", "sheet2AsSourceBase");
        await dsV2Page.clickEditBtn();
        await dsV2EditPage.verifyUnCheckColumnDisabled("Freelancer Name");
      }
    );

    test1(
      "Temporal Splice-Datasheet Generation should not fail",
      {
        annotation: [
          { type: "Test ID", description: "ICM-1722" },
          {
            type: "Description",
            description: [
              "Keep the ESD value as null in object for a payee.The payee must have only one line item-Autogenerate the start date - That record will not be processed and datasheet will have no records",
              "Keep the ESD value as null in object for a payee.The payee must have multiple line items-Autogenerate the start date - Those records will not be processed and datasheet will have no records",
              "Keep the ESD value as null and not null for the same payee.The payee must have multiple line items-Few with null value and few with not null. Autogenerate the start date - The record that has Null value will be skipped and the record that had notnull value will be processed. Hence datasheet will have only one record",
            ],
          },
          {
            type: "Precondition",
            description: "None",
          },
          {
            type: "Expected Behaviour",
            description:
              "The datasheet generation should be successfull in all cases",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);

        await dsV2Page.goToDatasheet("prod-DB", "TS 1 Null");
        await dsV2Page.waitForRefresh();
        await dsV2Page.refreshDatasheet();
        const valuesReceived = await dsV2Page.getColumnValues(
          "TS::Effective Start Date",
          "ts_effective_start_date"
        );
        console.log("Values: " + valuesReceived);
        const valuesExpected = [];
        expect(valuesReceived).toEqual(valuesExpected);

        await dsV2Page.goToDatasheet("prod-DB", "TS 2 Null");
        await dsV2Page.waitForRefresh();
        await dsV2Page.refreshDatasheet();
        const valuesReceived2 = await dsV2Page.getColumnValues(
          "TS::Effective Start Date",
          "ts_effective_start_date"
        );
        console.log("Values: " + valuesReceived2);
        const valuesExpected2 = [];
        expect(valuesReceived2).toEqual(valuesExpected2);

        await dsV2Page.goToDatasheet(
          "prod-DB",
          "TS 1Null 1 Notnull-Same Payee"
        );
        await dsV2Page.waitForRefresh();
        await dsV2Page.refreshDatasheet();
        const valuesReceived3 = await dsV2Page.getColumnValues(
          "TS::Effective Start Date",
          "ts_effective_start_date"
        );
        console.log("Values: " + valuesReceived3);
        const valuesExpected3 = ["Jan 08, 2025"];
        expect(valuesReceived3).toEqual(valuesExpected3);
      }
    );

    test1(
      "'Primary key' batch for the 'group by' transformation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T29392" },
          {
            type: "Description",
            description:
              "Verify all columns have a 'Primary key' batch for the 'group by' transformation",
          },
          {
            type: "Precondition",
            description: "None",
          },
          {
            type: "Expected Behaviour",
            description:
              "Primary key batch should be assigned to LHS added column",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        const dsObjects = new DatasheetV2Page(page);
        const v2Transformations = new V2Transformations(page);

        await dsV2Page.goToDatasheet("prod-DB", "sheet1");
        await dsObjects.clickDatasheetMenu();
        await dsObjects.cloneDatasheet();
        await dsObjects.verifyClone("prod-DB", "sheet1_Copy");
        await dsObjects.goToDatasheet("prod-DB", "sheet1_Copy");
        await dsObjects.clickEditBtn();

        //Add GroupBy
        await v2Transformations.addTransformation("Group by");
        await page.locator(".ant-select-selection-overflow").click();
        await page.keyboard.type("Client Name");
        await page.keyboard.press("Enter");
        await page.keyboard.type("Freelancer Name");
        await page.keyboard.press("Enter");
        await page.keyboard.type("Status");
        await page.keyboard.press("Enter");
        await page.keyboard.press("Escape");
        await v2Transformations.validate(true);

        //Verify the columns
        const colCount = await page
          .locator(
            '//span[normalize-space(text())="Primary" and contains(@class, "text-ever-chartColors-20")]'
          )
          .count();
        expect(colCount).toBe(3);
      }
    );

    test1(
      "'Primary key' batch for the 'union' transformation",
      {
        annotation: [
          {
            type: "Test ID",
            description: "INTER-T29392, INTER-T29395, INTER-T29401",
          },
          {
            type: "Description",
            description:
              "Verify all columns have a 'Primary key' batch for the 'union' transformation",
          },
          {
            type: "Precondition",
            description: "None",
          },
          {
            type: "Expected Behaviour",
            description:
              "Primary key batch should be assigned to LHS added column",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        const dsObjects = new DatasheetV2Page(page);
        const v2Transformations = new V2Transformations(page);

        await dsV2Page.goToDatasheet("prod-DB", "sheet2");
        await dsObjects.clickDatasheetMenu();
        await dsObjects.cloneDatasheet();
        await dsObjects.verifyClone("prod-DB", "sheet2_Copy");
        await dsObjects.goToDatasheet("prod-DB", "sheet2_Copy");
        await dsObjects.clickEditBtn();

        // add Union
        await v2Transformations.addTransformation("Union");
        await v2Transformations.fillUnion(
          "prod-DB",
          "source as sheet1",
          "Freelancer Name",
          "Client Name"
        );

        await page.waitForTimeout(1000);
        await v2Transformations.validate(true);
        //Verify the columns
        const colCount = await page
          .locator(
            '//span[normalize-space(text())="Primary" and contains(@class, "text-ever-chartColors-20")]'
          )
          .count();
        expect(colCount).toBe(1);
      }
    );

    test1(
      "'Primary key' batch for the 'join' transformation with multiple primary keys",
      {
        annotation: [
          {
            type: "Test ID",
            description:
              "INTER-T29393, INTER-T29394, INTER-T29396, INTER-T29399, INTER-T29404",
          },
          {
            type: "Description",
            description:
              "Verify all columns have a 'Primary key' batch for the 'join' transformation",
          },
          {
            type: "Precondition",
            description: "None",
          },
          {
            type: "Expected Behaviour",
            description:
              "Primary key batch should be assigned to LHS added column",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        const dsObjects = new DatasheetV2Page(page);
        const v2Transformations = new V2Transformations(page);

        await dsObjects.goToDatasheet("prod-DB", "DS2");
        await dsObjects.clickEditBtn();

        //Add Join
        await v2Transformations.addTransformation("Join");
        await v2Transformations.joinWith("prod-DB", "source as sheet1", "1");
        await v2Transformations.clickLookupColumns("lhs", "1");
        await page.keyboard.type("ID");
        await page.keyboard.press("Enter");
        await page.keyboard.press("Escape");
        await page.keyboard.type("Email");
        await page.keyboard.press("Enter");
        await page.keyboard.press("Escape");
        await v2Transformations.clickLookupColumns("rhs", "1");
        await page.keyboard.type("ID");
        await page.keyboard.press("Enter");
        await page.keyboard.press("Escape");
        await page.keyboard.type("Freelancer Email");
        await page.keyboard.press("Enter");
        await page.keyboard.press("Escape");
        await v2Transformations.clickOutputColumns("rhs", "1");
        await v2Transformations.validate(true);

        //Verify the columns
        const colCount = await page
          .locator(
            '//span[normalize-space(text())="Primary" and contains(@class, "text-ever-chartColors-20")]'
          )
          .count();
        expect(colCount).toBe(3);

        //Add right Join
        await v2Transformations.addTransformation("Join");
        await page
          .locator("input[name='joinType'][value='RIGHT']")
          .nth(1)
          .click();
        await v2Transformations.joinWith("prod-DB", "DS1", "2");
        await v2Transformations.clickLookupColumns("lhs", "2");
        await page.keyboard.type("ID");
        await page.keyboard.press("Enter");
        await page.keyboard.press("Escape");
        await v2Transformations.clickLookupColumns("rhs", "2");
        await page.keyboard.type("ID");
        await page.keyboard.press("Enter");
        await page.keyboard.press("Escape");
        await v2Transformations.validate(true);

        //Verify the columns
        const colCount1 = await page
          .locator(
            '//span[normalize-space(text())="Primary" and contains(@class, "text-ever-chartColors-20")]'
          )
          .count();
        expect(colCount1).toBe(5);

        // delete transformation
        await v2Transformations.deleteTransformation("2");
        await page.waitForTimeout(2000);
        const joinTitles = await page
          .locator("span.text-lg.font-semibold", { hasText: "Join" })
          .all();
        expect(joinTitles).toHaveLength(1);
      }
    );

    test1(
      "Validate transformation in child sheet",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T29398" },
          {
            type: "Description",
            description: "Validate transformation in child sheet",
          },
          {
            type: "Precondition",
            description: "None",
          },
          {
            type: "Expected Behaviour",
            description:
              "Primary key batch should be assigned to LHS added column",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        const dsObjects = new DatasheetV2Page(page);
        const v2Transformations = new V2Transformations(page);

        await dsV2Page.createDatasheet(
          "prod-DB",
          "Test Sheet",
          "datasheet",
          "prod-DB",
          "DS1"
        );
        await dsV2Page.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await v2Transformations.addTransformation("Union");
        await v2Transformations.fillUnion(
          "prod-DB",
          "source as sheet1",
          "ID",
          "ID"
        );
        await v2Transformations.validate(true);
        //Verify the columns
        const colCount = await page
          .locator(
            '//span[normalize-space(text())="Primary" and contains(@class, "text-ever-chartColors-20")]'
          )
          .count();
        expect(colCount).toBe(1);
      }
    );

    test1(
      "Verify by applying transformation using a sheet not part of the same book",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T29400" },
          {
            type: "Description",
            description: "Validate transformation in sheet from different book",
          },
          {
            type: "Precondition",
            description: "None",
          },
          {
            type: "Expected Behaviour",
            description:
              "Primary batch should be applied to all LHS primary columns",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        const dsObjects = new DatasheetV2Page(page);
        const v2Transformations = new V2Transformations(page);
        await dsV2Page.goToDatasheet("prod-DB", "DS2");
        await dsV2Page.clickEditBtn();

        await v2Transformations.addTransformation("Union");
        await v2Transformations.fillUnion("TestBook", "SheetA", "ID", "ID");
        await v2Transformations.validate(true);
        //Verify the columns
        const colCount = await page
          .locator(
            '//span[normalize-space(text())="Primary" and contains(@class, "text-ever-chartColors-20")]'
          )
          .count();
        expect(colCount).toBe(1);
      }
    );

    test1(
      "Verify the primary key for sheets from the integration object",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T29402" },
          {
            type: "Description",
            description:
              "Validate transformation for sheets from integration obj",
          },
          {
            type: "Precondition",
            description: "None",
          },
          {
            type: "Expected Behaviour",
            description: "Primary batch key should be present in all column",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        const v2Transformations = new V2Transformations(page);

        await dsV2Page.createDatasheet(
          "prod-DB",
          "SheetA",
          "object",
          "Hubspot Object 1",
          ""
        );
        await dsV2Page.dialogPrompt(
          "span",
          "A new datasheet has been created successfully"
        );
        await v2Transformations.addTransformation("Union");
        await v2Transformations.fillUnion(
          "prod-DB",
          "source as sheet1",
          "Record ID",
          "Client Name"
        );
        await v2Transformations.validate(true);
        //Verify the columns
        const colCount = await page
          .locator(
            '//span[normalize-space(text())="Primary" and contains(@class, "text-ever-chartColors-20")]'
          )
          .count();
        expect(colCount).toBe(1);
      }
    );
    test1(
      "Verify primary key batch for transformations filter and get user properties",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T29397, INTER-T29405" },
          {
            type: "Description",
            description:
              "Validate transformation for sheets from integration obj",
          },
          {
            type: "Precondition",
            description: "None",
          },
          {
            type: "Expected Behaviour",
            description: "Primary batch key should be present in all column",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        const v2Transformations = new V2Transformations(page);
        // Filter
        await v2Transformations.createDatasheet("TestBook", "Test1", "Filter");
        await v2Transformations.addTransformation("Filter");
        await page.getByPlaceholder("Press Ctrl + H for help").click();
        await page.locator("span[title='ID']").click();
        await page
          .locator("li")
          .filter({ hasText: /^>$/ })
          .getByRole("listitem")
          .click();
        await page.getByRole("textbox").nth(2).fill("25");
        await page.waitForTimeout(2000);
        await page.getByText("25Integer").click();
        await page.keyboard.press("Escape");
        await v2Transformations.validate(true);
        //Verify the columns
        const colCount = await page
          .locator(
            '//span[normalize-space(text())="Primary" and contains(@class, "text-ever-chartColors-20")]'
          )
          .count();
        expect(colCount).toBe(2);

        // Edit transformation
        const inputBox = await page.locator(
          "div[data-testid='expression-input-box']"
        );
        await inputBox.click(); // Focus the field
        await page.keyboard.press("Backspace");
        await page.keyboard.press("Backspace");
        await page.keyboard.press("Backspace");
        await page.locator("span[title='Client Name']").click();
        await page
          .locator("li")
          .filter({ hasText: /^==$/ })
          .getByRole("listitem")
          .click();
        await page.locator("span[title='Freelancer Name']").click();
        await page.keyboard.press("Escape");
        await v2Transformations.validate(true);
        //Verify the columns
        const colCount1 = await page
          .locator(
            '//span[normalize-space(text())="Primary" and contains(@class, "text-ever-chartColors-20")]'
          )
          .count();
        expect(colCount1).toBe(2);

        // Get user property

        await v2Transformations.addTransformation("Get User Properties");
        await page
          .locator("div")
          .filter({ hasText: /^Select Email Column$/ })
          .nth(2)
          .click();
        await page.keyboard.type("Email");
        await page.keyboard.press("Enter");

        await page
          .locator("div")
          .filter({ hasText: /^Select Date Column$/ })
          .nth(2)
          .click();
        await page.keyboard.type("Date");
        await page.keyboard.press("Enter");

        await page
          .locator("form div")
          .filter({ hasText: "Select User Fields" })
          .nth(4)
          .click();

        // Check if d1 is visible before clicking
        const isD1Visible = await page
          .getByRole("listitem")
          .filter({ hasText: "Employee Id" })
          .locator("span")
          .isVisible();

        if (isD1Visible) {
          await page
            .getByRole("listitem")
            .filter({ hasText: "Employee Id" })
            .locator("span")
            .click();

          await v2Transformations.validate(true);
          //Verify the columns
          const colCount1 = await page
            .locator(
              '//span[normalize-space(text())="Primary" and contains(@class, "text-ever-chartColors-20")]'
            )
            .count();
          expect(colCount1).toBe(2);
        }
      }
    );
    test1(
      "Verify primary key post applying temporal splice",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T29409" },
          {
            type: "Description",
            description: "Validate primary key post applying temporal splice",
          },
          {
            type: "Precondition",
            description: "None",
          },
          {
            type: "Expected Behaviour",
            description:
              "Primary batch should be applied to start date, end date , email",
          },
        ],
      },
      async ({ adminPage }, testInfo) => {
        const page = adminPage.page;
        const dsV2Page = new DatasheetV2Page(page);
        const dsV2EditPage = new DatasheetV2EditViewPage(adminPage.page);
        const dsObjects = new DatasheetV2Page(page);
        const v2Transformations = new V2Transformations(page);

        await v2Transformations.createDatasheet("TestBook", "Test1", "TS");
        await v2Transformations.addTransformation("Temporal Splice");
        await page
          .locator("div")
          .filter({ hasText: /^Select$/ })
          .nth(2)
          .click();
        await page.getByText("Email").nth(3).click();
        await page.getByRole("switch").click();
        await page
          .locator("div")
          .filter({ hasText: /^Start date columnSelect$/ })
          .locator("div")
          .nth(2)
          .click();
        await page.locator("//span[@title='Date']").click();
        await page
          .locator("div")
          .filter({ hasText: /^Select$/ })
          .nth(2)
          .click();
        await page.getByText("Auto-generate").nth(2).click();

        await v2Transformations.validate(true);
        //Verify the columns
        const colCount = await page
          .locator(
            '//span[normalize-space(text())="Primary" and contains(@class, "text-ever-chartColors-20")]'
          )
          .count();
        expect(colCount).toBe(3);
      }
    );
  }
);
