import TsarPage from "../../../../test-objects/tsar-objects";
const UserPage = require("../../../../test-objects/user-objects");
const { chromium } = require("playwright");
const fs = require("fs");

const expectedUser = "<EMAIL>";
const supportUserEmail = "<EMAIL>";
const supportUserName = "everstage admin";

const {
  breakglassFixtures: { test, expect },
} = require("../../../fixtures");

let newBrowser, newContext, newPageForTest;

// Define a custom fixture for your test setup
test.describe(
  "tsar_user_module",
  { tag: ["@tsar", "@regression", "@user", "@repconnect-2"] },
  () => {
    test.beforeEach(async () => {
      newBrowser = await chromium.launch();
      newContext = await newBrowser.newContext();
      // Load cookies and set them in the context
      const savedCookies = JSON.parse(fs.readFileSync("cookies.json", "utf8"));
      await newContext.addCookies(savedCookies);
      // Create a new page and navigate
      newPageForTest = await newContext.newPage();
      await newPageForTest.goto("http://localhost:3000/", {
        waitUntil: "networkidle",
      });
      await newPageForTest.waitForTimeout(10000);
      const breakglassbtn = await newPageForTest
        .getByText("Choose account")
        .isVisible({ timeout: 100000 });
      if (breakglassbtn) {
        console.log("Found BreakGlass Button in the First loading...");
        await newPageForTest.getByRole("button", { name: "Continue" }).click();
        await newPageForTest.waitForLoadState("networkidle");
      }
      console.log("Passed First Check of Breakglass button...");

      const isLoggedIn = await newPageForTest
        .locator("//h1[text()='Welcome to Everstage']")
        .isVisible();
      if (isLoggedIn) {
        console.log("Found Continue With Google Page...");
        await newPageForTest
          .getByRole("button", { name: "Continue with Google" })
          .click();
        await newPageForTest.screenshot({ path: "screenshot_plan1.png" });
        await newPageForTest.waitForLoadState("networkidle");
        await newPageForTest.getByRole("button", { name: "Continue" }).click();
      }
      console.log("passed Second Check....");
      await newPageForTest.screenshot({ path: "screenshot_plan.png" });
      await newPageForTest.waitForTimeout(3000);
      const ele = await newPageForTest.locator(".ever-container");
      await ele.waitFor({ state: "visible", timeout: 60000 });
    });

    test.afterEach(async () => {
      await newBrowser.close();
    });

    test("User should see the proper client name with proper user name", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16723" },
        {
          type: "Description",
          description:
            "Validation in user module should be working as expected",
        },
        {
          type: "Precondition",
          description: "User should have support user access",
        },
        {
          type: "Expected Behaviour",
          description:
            "User should see only the power admin name instead of the support user ",
        }
      );

      const tsarPage = new TsarPage(newPageForTest);
      testInfo.setTimeout(testInfo.timeout + 90000);
      await tsarPage.supportMembership();
      await tsarPage.verifyClientName();
      await tsarPage.verifyAdminName();
    });

    test("User should see the power user email after overwriting the payroll details", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16723" },
        {
          type: "Description",
          description:
            "Validation in user module should be working as expected",
        },
        {
          type: "Precondition",
          description: "User should have support user access",
        },
        {
          type: "Expected Behaviour",
          description: "User should see only the support user name ",
        }
      );

      const tsarPage = new TsarPage(newPageForTest);
      const adminpage = adminPage.page;
      const tsarPage1 = new TsarPage(adminpage);
      const userPage = new UserPage(newPageForTest);
      testInfo.setTimeout(testInfo.timeout + 90000);
      await userPage.navigateToUser();
      await userPage.fillSearch("Gab");
      await userPage.waitForSearchResults();
      await userPage.mapPayee("<EMAIL>");
      await userPage.overwriteSinglePayroll("Enter Employee ID", "3456");
      await userPage.verifyMapPayeeSuccessMessage();
      await tsarPage.navigateAuditLogs1();
      let expectedAuditAction = "Updated User Details";
      let expectedDesc = "Gabrielb";
      await tsarPage.validAuditLog(
        expectedUser,
        expectedAuditAction,
        expectedDesc
      );
      await tsarPage1.navigateAuditLogs1();
      await tsarPage1.validAuditLogSupport(
        supportUserName,
        supportUserEmail,
        expectedAuditAction,
        expectedDesc
      );
    });

    test("User should see the power user email after edting the payroll details", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16723" },
        {
          type: "Description",
          description:
            "Validation in user module should be working as expected",
        },
        {
          type: "Precondition",
          description: "User should have support user access",
        },
        {
          type: "Expected Behaviour",
          description: "User should see only the support user name",
        }
      );

      const tsarPage = new TsarPage(newPageForTest);
      const userPage = new UserPage(newPageForTest);
      const adminpage = adminPage.page;
      const tsarPage1 = new TsarPage(adminpage);
      testInfo.setTimeout(testInfo.timeout + 100000);
      await userPage.navigateToUser();
      await userPage.fillSearch("oli");
      await userPage.waitForSearchResults();
      await userPage.mapPayee("<EMAIL>");
      await userPage.futurePayroll("Enter Employee ID", "5236");
      await userPage.verifyMapPayeeSuccessMessage();
      await tsarPage.navigateAuditLogs1();
      let expectedAuditAction = "Updated User Details";
      let expectedDesc = "Oliviab";
      await tsarPage.validAuditLog(
        expectedUser,
        expectedAuditAction,
        expectedDesc
      );
      await tsarPage1.navigateAuditLogs1();
      await tsarPage1.validAuditLogSupport(
        supportUserName,
        supportUserEmail,
        expectedAuditAction,
        expectedDesc
      );
    });

    test("User should see the support user email after adding the hierarchy details", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16723" },
        {
          type: "Description",
          description:
            "Validation in user module should be working as expected",
        },
        {
          type: "Precondition",
          description: "User should have support user access",
        },
        {
          type: "Expected Behaviour",
          description: "User should see only the support user name",
        }
      );

      const tsarPage = new TsarPage(newPageForTest);
      const userPage = new UserPage(newPageForTest);
      const adminpage = adminPage.page;
      const tsarPage1 = new TsarPage(adminpage);
      testInfo.setTimeout(testInfo.timeout + 90000);
      await userPage.navigateToUser();
      await userPage.fillSearch("chan");
      await userPage.waitForSearchResults();
      await userPage.mapPayee("<EMAIL>");
      await userPage.nextAction();
      await userPage.setNewmanager("Gabriel b");
      await userPage.saveHierarchychanges();
      await tsarPage.nextButton();
      await tsarPage.navigateAuditLogs1();
      let expectedAuditAction = "Updated Reporting Manager";
      let expectedDesc = "chandler b";
      await tsarPage.validAuditLog(
        expectedUser,
        expectedAuditAction,
        expectedDesc
      );
      await tsarPage1.navigateAuditLogs1();
      await tsarPage1.explictWait();
      await tsarPage1.validAuditLogSupport(
        supportUserName,
        supportUserEmail,
        expectedAuditAction,
        expectedDesc
      );
    });

    test("User should see the support user email after overwrting the hierarchy details", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16723" },
        {
          type: "Description",
          description:
            "Validation in user module should be working as expected",
        },
        {
          type: "Precondition",
          description: "User should have support user access",
        },
        {
          type: "Expected Behaviour",
          description: "User should see only the support user name",
        }
      );

      const tsarPage = new TsarPage(newPageForTest);
      const userPage = new UserPage(newPageForTest);
      const adminpage = adminPage.page;
      const tsarPage1 = new TsarPage(adminpage);
      await userPage.navigateToUser();
      testInfo.setTimeout(testInfo.timeout + 90000);
      await userPage.fillSearch("har");
      await userPage.waitForSearchResults();
      await userPage.mapPayeeByEmail("<EMAIL>");
      await userPage.nextAction();
      await userPage.setNewmanager("Gabriel b");
      await userPage.changeDate();
      await userPage.updateHierarchychanges();
      await tsarPage.nextButton();
      await tsarPage.navigateAuditLogs1();
      let expectedAuditAction = "Updated Reporting Manager";
      let expectedDesc = "harry b";
      await tsarPage.validAuditLog(
        expectedUser,
        expectedAuditAction,
        expectedDesc
      );
      await tsarPage1.navigateAuditLogs1();
      await tsarPage1.explictWait();
      await tsarPage1.validAuditLogSupport(
        supportUserName,
        supportUserEmail,
        expectedAuditAction,
        expectedDesc
      );
    });

    test("User should see the support user email after overwriting the hierarchy details", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16723" },
        {
          type: "Description",
          description:
            "Validation in user module should be working as expected",
        },
        {
          type: "Precondition",
          description: "User should have support user access",
        },
        {
          type: "Expected Behaviour",
          description: "User should see only the support user name",
        }
      );

      const tsarPage = new TsarPage(newPageForTest);
      const userPage = new UserPage(newPageForTest);
      await userPage.navigateToUser();
      const adminpage = adminPage.page;
      const tsarPage1 = new TsarPage(adminpage);
      testInfo.setTimeout(testInfo.timeout + 90000);
      await userPage.fillSearch("har");
      await userPage.waitForSearchResults();
      await userPage.mapPayeeByEmail("<EMAIL>");
      await userPage.nextAction();
      await userPage.overwritehierarchyDetails();
      await userPage.setNewmanager("Olivia b");
      await userPage.updateHierarchychanges();
      await tsarPage.nextButton();
      await tsarPage.navigateAuditLogs1();
      const expectedAuditAction1 = "Edited Reporting Manager";
      let expectedAuditAction = "Edited Reporting Manager";
      let expectedDesc = "harry b";
      await tsarPage.validAuditLog(
        expectedUser,
        expectedAuditAction,
        expectedDesc
      );
      await tsarPage1.navigateAuditLogs1();
      await tsarPage1.validAuditLogSupport(
        supportUserName,
        supportUserEmail,
        expectedAuditAction1,
        expectedDesc
      );
    });

    test("User should see the support user email after creation of payee", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16723" },
        {
          type: "Description",
          description:
            "Validation in user module should be working as expected",
        },
        {
          type: "Precondition",
          description: "User should have support user access",
        },
        {
          type: "Expected Behaviour",
          description: "User should see only the support user name",
        }
      );

      const tsarPage = new TsarPage(newPageForTest);
      const adminpage = adminPage.page;
      const tsarPage1 = new TsarPage(adminpage);
      const userPage = new UserPage(newPageForTest);
      await userPage.navigateToUser();
      testInfo.setTimeout(testInfo.timeout + 90000);
      await tsarPage.createNewuser("<EMAIL>", "finola", "f", "pt-Payee");
      await tsarPage.navigateAuditLogs1();
      const expectedAuditAction1 = "User Created";
      let expectedAuditAction = "User Created";
      let expectedDesc = "finolaf";
      await tsarPage.validAuditLog(
        expectedUser,
        expectedAuditAction,
        expectedDesc
      );
      await tsarPage1.navigateAuditLogs1();
      await tsarPage1.validAuditLogSupport(
        supportUserName,
        supportUserEmail,
        expectedAuditAction1,
        expectedDesc
      );
    });

    test("User should see the support user email after impersonation of payee", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16723" },
        {
          type: "Description",
          description:
            "Validation in user module should be working as expected",
        },
        {
          type: "Precondition",
          description: "User should have support user access",
        },
        {
          type: "Expected Behaviour",
          description: "User should see only the support user name",
        }
      );

      const tsarPage = new TsarPage(newPageForTest);
      const userPage = new UserPage(newPageForTest);
      const adminpage = adminPage.page;
      const tsarPage1 = new TsarPage(adminpage);
      testInfo.setTimeout(testInfo.timeout + 90000);
      await userPage.navigateToUser();
      await userPage.fillSearch("har");
      await userPage.loginasPayee("<EMAIL>");
      await userPage.validateImpersonation("harry b (<EMAIL>)");
      await userPage.validateexitImpersonation();
      await tsarPage.navigateAuditLogs1();
      const expectedAuditAction1 = "User Impersonation";
      let expectedAuditAction = "User Impersonation";
      let expectedDesc = "Logged out as harry b (<EMAIL>)";
      await tsarPage.validAuditLog(
        expectedUser,
        expectedAuditAction,
        expectedDesc
      );
      await tsarPage1.navigateAuditLogs1();
      await tsarPage1.explictWait();
      await tsarPage1.validAuditLogSupport(
        supportUserName,
        supportUserEmail,
        expectedAuditAction1,
        expectedDesc
      );
    });

    test("User should see the support user email after exiting a payee", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16723" },
        {
          type: "Description",
          description:
            "Validation in user module should be working as expected",
        },
        {
          type: "Precondition",
          description: "User should have support user access",
        },
        {
          type: "Expected Behaviour",
          description: "User should see only the support user name",
        }
      );

      const tsarPage = new TsarPage(newPageForTest);
      const adminpage = adminPage.page;
      const tsarPage1 = new TsarPage(adminpage);
      const userPage = new UserPage(newPageForTest);
      await userPage.navigateToUser();
      testInfo.setTimeout(testInfo.timeout + 90000);
      await userPage.fillSearch("dan");
      await userPage.waitForSearchResults();
      await userPage.initiateExit();
      await userPage.selectExitDateAndValidate();
      await userPage.confirmExit();
      await tsarPage.navigateAuditLogs1();
      const expectedAuditAction1 =
        "Initiated exit (Employee eff.end date modified)";
      let expectedAuditAction =
        "Initiated exit (Employee eff.end date modified)";
      let expectedDesc = "danielb";
      await tsarPage.validAuditLog(
        expectedUser,
        expectedAuditAction,
        expectedDesc
      );
      await tsarPage1.navigateAuditLogs1();
      await tsarPage1.validAuditLogSupport(
        supportUserName,
        supportUserEmail,
        expectedAuditAction1,
        expectedDesc
      );
    });

    test("User should see the support user email after revert exiting a payee", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T16723" },
        {
          type: "Description",
          description:
            "Validation in user module should be working as expected",
        },
        {
          type: "Precondition",
          description: "User should have support user access",
        },
        {
          type: "Expected Behaviour",
          description: "User should see only the support user name",
        }
      );

      const tsarPage = new TsarPage(newPageForTest);
      const adminpage = adminPage.page;
      const tsarPage1 = new TsarPage(adminpage);
      const userPage = new UserPage(newPageForTest);
      testInfo.setTimeout(testInfo.timeout + 120000);
      await userPage.navigateToUser();
      await userPage.fillSearch("joe");
      await userPage.waitForSearchResults();
      await userPage.clickRevertExit();
      await userPage.confirmExit();
      await tsarPage.navigateAuditLogs1();
      const expectedAuditAction1 =
        "Initiated exit (Employee eff.end date modified)";
      let expectedAuditAction = "Employee eff.end date modified";
      let expectedDesc = "User exit reverted successfully";
      await tsarPage.validAuditLog(
        expectedUser,
        expectedAuditAction,
        expectedDesc
      );
      await tsarPage1.navigateAuditLogs1();
      await tsarPage1.validAuditLogSupport(
        supportUserName,
        supportUserEmail,
        expectedAuditAction1,
        expectedDesc
      );
    });

    test("User should see the support user email after deleting the hierarchy", async ({
      adminPage,
    }, testInfo) => {
      test.info().annotations.push(
        {
          type: "Test ID",
          description: "INTER-T16723, INTER-T19183, INTER-T19184, INTER-T19185",
        },
        {
          type: "Description",
          description:
            "Validation in user module should be working as expected",
        },
        {
          type: "Precondition",
          description: "User should have support user access",
        },
        {
          type: "Expected Behaviour",
          description: "User should see only the support user name",
        }
      );

      const tsarPage = new TsarPage(newPageForTest);
      const adminpage = adminPage.page;
      const tsarPage1 = new TsarPage(adminpage);
      const userPage = new UserPage(newPageForTest);
      testInfo.setTimeout(testInfo.timeout + 120000);
      await userPage.navigateToUser();
      await userPage.fillSearch("rup");
      await userPage.waitForSearchResults();
      await userPage.initiateMapping();
      await userPage.nextAction();
      await userPage.deleteHierarchy("edwina b");
      const isTextPresent = await userPage.checkIfTextExists("Delete");
      expect(isTextPresent).toBe(true);
      await userPage.deletePayroll();
      const isDateVisible = await userPage.datePopup("July 21, 2022");
      if (isDateVisible) {
        console.log("The date 'July 21, 2022' is visible.");
      } else {
        console.log("The date 'July 21, 2022' is NOT visible.");
      }
      const isCancelButtonVisible = await userPage.cancelCTA();
      if (isCancelButtonVisible) {
        console.log("Cancel button is visible");
      } else {
        console.log("Cancel button is not visible");
      }
      await userPage.deletedialog();
      const isAlertDisplayed = await userPage.hierarchyDeleteAlert();
      if (isAlertDisplayed) {
        console.log("Hierarchy alert is displayed");
      } else {
        console.log("Hierarchy alert is not displayed");
      }
      await tsarPage.navigateAuditLogs1();
      const expectedAuditAction1 = "Deleted Reporting Manager";
      let expectedAuditAction = "Deleted Reporting Manager";
      let expectedDesc = "rupa b";
      await tsarPage.validAuditLog(
        expectedUser,
        expectedAuditAction,
        expectedDesc
      );
      await tsarPage1.navigateAuditLogs1();
      await tsarPage1.validAuditLogSupport(
        supportUserName,
        supportUserEmail,
        expectedAuditAction1,
        expectedDesc
      );
    });
  }
);
