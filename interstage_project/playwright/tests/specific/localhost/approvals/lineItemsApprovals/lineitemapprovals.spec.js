const {
  lineitemapprovalsFixtures: { test, expect },
} = require("../../../../fixtures");
const commonPage = require("../../../../../test-objects/common-utils-objects");

test.describe(
  "Line item approvals",
  { tag: ["@regression", "@approvals", "@adminchamp-2"] },
  () => {
    test("Admin view - validate the table", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("http://localhost:3000/approvals/payouts", {
        waitUntil: "networkidle",
      });

      const dropdown = await page.getByTestId("period-select");
      await dropdown.waitFor({ state: "visible", timeout: 30000 });
      await dropdown.click();

      await page
        .getByTitle("January 2024")
        .getByRole("listitem")
        .locator("div")
        .click();
      await page.getByText("Simple PlanSimple").click();

      await page
        .getByRole("columnheader", { name: "Payee Name" })
        .waitFor({ state: "visible", timeout: 20000 });

      expect(
        await page.getByRole("columnheader", { name: "Period" }).isVisible()
      ).toBeTruthy();
      expect(
        await page.getByRole("columnheader", { name: "Stage" }).isVisible()
      ).toBeTruthy();
      expect(
        await page.getByRole("columnheader", { name: "Due Date" }).isVisible()
      ).toBeTruthy();
      expect(
        await page.getByRole("columnheader", { name: "Approver" }).isVisible()
      ).toBeTruthy();
      expect(
        await page.getByRole("columnheader", { name: "Commission" }).isVisible()
      ).toBeTruthy();
      expect(
        await page.getByRole("columnheader", { name: "Approval" }).isVisible()
      ).toBeTruthy();
      await expect(
        await page.locator(
          "//div[@col-id='approvalsSysPayeeName']//span[text()='User 3']"
        )
      ).toBeVisible();
      await expect(
        await page.locator(
          "//div[@col-id='approvalsSysPayeeName']//span[text()='User 4']"
        )
      ).toBeVisible();
      await page.getByText("₹50.00").click();
      await page.getByText("₹100.00").click();

      const pendingRequestsButton = !(await page.isVisible(
        'button:has-text("Pending Requests")'
      ));
      expect(pendingRequestsButton).toBeTruthy();
      const allRequestsButton = !(await page.isVisible(
        'button:has-text("Pending Requests")'
      ));
      expect(allRequestsButton).toBeTruthy();

      await page.getByText("Manage Workflows").click();
    });
    test("Validate unlock & delete approved requests", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      const key = "commission-view-period";
      const value = "March-2024";

      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await page.goto("http://localhost:3000/commissions", {
        waitUntil: "networkidle",
      });

      await page.waitForSelector("text=Approved", { timeout: 25000 });
      const approvedStatus = await page.getByText("Approved").isVisible();
      expect(approvedStatus).toBeTruthy();
      const icon = page
        .getByRole("gridcell", { name: "₹50.00" })
        .locator("svg");
      await expect(icon).toBeVisible();
      await icon.click();
      await page.getByTestId("<EMAIL>").check();

      await page.waitForTimeout(2000);
      await page.getByText("Unlock").nth(1).click();
      const popUp = await page
        .getByText(
          "Are you sure you want to Unlock Statement(s) for selected payee(s)?"
        )
        .isVisible();
      expect(popUp).toBeTruthy();
      await page.getByRole("button", { name: "Unlock" }).click();
      await page.waitForSelector("text=Lock status updated successfully", {
        timeout: 30000,
      });
      const commonUtils = new commonPage(page);
      commonUtils.waitForLazyload(
        ".ag-theme-material.blur-sm",
        ".ag-theme-material.blur-0"
      );
      await page.waitForTimeout(3000);
      await commonUtils.navigateToApprovalRequestDrawer("Approved");
      await page.getByRole("button", { name: "Delete all requests" }).click();
      await page.getByRole("button", { name: "Yes, delete" }).click();
      await page.waitForSelector(
        "text=Approval requests deleted successfully",
        {
          timeout: 30000,
        }
      );
    });
    test("Validate unlock & delete rejected requests", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      const key = "commission-view-period";
      const value = "March-2024";

      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await page.goto("http://localhost:3000/commissions", {
        waitUntil: "networkidle",
      });
      await page.waitForSelector("text=Needs Attention", { timeout: 25000 });
      const needsAttentionStatus = await page
        .getByText("Needs Attention")
        .isVisible();
      expect(needsAttentionStatus).toBeTruthy();
      const icon = page
        .getByRole("gridcell", { name: "₹100.00" })
        .locator("svg");
      await expect(icon).toBeVisible();
      await icon.click();
      await page.getByTestId("<EMAIL>").check();
      await page.waitForTimeout(2000);
      await page.getByText("Unlock").nth(1).click();
      const popUp = await page
        .getByText(
          "Are you sure you want to Unlock Statement(s) for selected payee(s)?"
        )
        .isVisible();
      expect(popUp).toBeTruthy();
      await page.getByRole("button", { name: "Unlock" }).click();
      await page.waitForSelector("text=Lock status updated successfully", {
        timeout: 30000,
      });
      const commonUtils = new commonPage(page);
      commonUtils.waitForLazyload(
        ".ag-theme-material.blur-sm",
        ".ag-theme-material.blur-0"
      );
      await page.waitForTimeout(3000);
      await commonUtils.navigateToApprovalRequestDrawer("Needs Attention");
      await page.getByRole("button", { name: "Delete all requests" }).click();
      await page.getByRole("button", { name: "Yes, delete" }).click();
      await page.waitForSelector(
        "text=Approval requests deleted successfully",
        {
          timeout: 30000,
        }
      );
    });
    test("Validate unlock & delete withdrawn requests", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      const key = "commission-view-period";
      const value = "March-2024";

      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await page.goto("http://localhost:3000/commissions", {
        waitUntil: "networkidle",
      });

      await page.waitForSelector("text=Withdrawn", { timeout: 25000 });
      const needsAttentionStatus = await page
        .getByText("Withdrawn")
        .isVisible();
      expect(needsAttentionStatus).toBeTruthy();
      const icon = page
        .getByRole("gridcell", { name: "₹150.00" })
        .locator("svg");
      await expect(icon).toBeVisible();
      await icon.click();
      await page.getByTestId("<EMAIL>").check();
      await page.waitForTimeout(2000);
      await page.getByText("Unlock").nth(1).click();
      const popUp = await page
        .getByText(
          "Are you sure you want to Unlock Statement(s) for selected payee(s)?"
        )
        .isVisible();
      expect(popUp).toBeTruthy();
      await page.getByRole("button", { name: "Unlock" }).click();
      await page.waitForSelector("text=Lock status updated successfully", {
        timeout: 30000,
      });
      const commonUtils = new commonPage(page);
      commonUtils.waitForLazyload(
        ".ag-theme-material.blur-sm",
        ".ag-theme-material.blur-0"
      );
      await page.waitForTimeout(3000);
      await commonUtils.navigateToApprovalRequestDrawer("Withdrawn");
      await page.getByRole("button", { name: "Delete all requests" }).click();
      await page.getByRole("button", { name: "Yes, delete" }).click();
      await page.waitForSelector(
        "text=Approval requests deleted successfully",
        {
          timeout: 30000,
        }
      );
    });
    test("Validate unlock & delete pending requests", async ({ adminPage }) => {
      const page = adminPage.page;
      const key = "commission-view-period";
      const value = "March-2024";

      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
      await page.goto("http://localhost:3000/commissions", {
        waitUntil: "networkidle",
      });

      await page.waitForSelector("text=Requested", { timeout: 25000 });
      const needsAttentionStatus = await page
        .getByText("Requested", { exact: true })
        .isVisible();
      expect(needsAttentionStatus).toBeTruthy();
      const icon = page
        .getByRole("gridcell", { name: "₹200.00" })
        .locator("svg");
      await expect(icon).toBeVisible();
      await icon.click();
      await page.getByTestId("<EMAIL>").check();
      await page.waitForTimeout(2000);
      await page.getByText("Unlock").nth(1).click();
      const popUp = await page
        .getByText(
          "Are you sure you want to Unlock Statement(s) for selected payee(s)?"
        )
        .isVisible();
      expect(popUp).toBeTruthy();
      await page.getByRole("button", { name: "Unlock" }).click();
      await page.waitForSelector("text=Lock status updated successfully", {
        timeout: 30000,
      });

      const commonUtils = new commonPage(page);
      commonUtils.waitForLazyload(
        ".ag-theme-material.blur-sm",
        ".ag-theme-material.blur-0"
      );
      await page.waitForTimeout(3000);
      await commonUtils.navigateToApprovalRequestDrawer("Requested");
      await page.getByRole("button", { name: "Delete all requests" }).click();
      await page.getByRole("button", { name: "Yes, delete" }).click();
      await page.waitForSelector(
        "text=Approval requests deleted successfully",
        {
          timeout: 30000,
        }
      );
    });
    test("Approval for zero payout & withdraw request", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyN0BwbGF5d3JpZ2h0YXBwcm92YWxzbGlwLmNvbSIsInBzZCI6IjIwMjQtMDQtMDEiLCJwZWQiOiIyMDI0LTA0LTMwIn0=",
        {
          waitUntil: "networkidle",
        }
      );
      await page.waitForSelector('[data-testid="statement-menu"]', {
        timeout: 30000,
        state: "visible",
      });
      await page.getByTestId("statement-menu").click();
      await page.getByRole("menuitem", { name: "Request Approval" }).click();
      await page.getByPlaceholder("Choose approvers").click();
      await page
        .getByRole("dialog", { name: "Request Approval User 7 Apr 2024" })
        .getByRole("tablist")
        .locator("div")
        .filter({ hasText: "Users" })
        .nth(2)
        .click();
      await page.getByLabel("Users").getByText("power admin").click();
      await page.getByPlaceholder("Choose who needs to be").first().click();
      await page.getByRole("listitem").first().click();

      await page.getByRole("button", { name: "Send Approval Request" }).click();
      await page.waitForSelector(
        "text=Approval request not submitted as it has no line items to approve. ",
        { timeout: 20000 }
      );
    });
    test("Approval Exception - Reporting manager", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyNkBwbGF5d3JpZ2h0YXBwcm92YWxzbGlwLmNvbSIsInBzZCI6IjIwMjQtMDQtMDEiLCJwZWQiOiIyMDI0LTA0LTMwIn0=",
        {
          waitUntil: "networkidle",
        }
      );
      await page.waitForSelector('[data-testid="statement-menu"]', {
        timeout: 30000,
        state: "visible",
      });
      await page.getByTestId("statement-menu").click();
      await page
        .getByRole("menuitem", { name: "Request Approval" })
        .getByText("Request Approval")
        .click();
      await page.getByPlaceholder("Choose approvers").click();
      await page
        .getByRole("listitem")
        .filter({ hasText: "Reporting manager of payee" })
        .click();
      await page.getByPlaceholder("Choose who needs to be").first().click();
      await page.getByRole("listitem").first().click();

      await page.getByRole("button", { name: "Send Approval Request" }).click();

      await page.waitForSelector(
        "text=Approvers resolved to empty for stage - Stage #1 for the requested workflow.",
        { timeout: 20000 }
      );
    });
    test("Non-admin view basic tests & bulk approve/reject", async ({
      payeePage,
    }) => {
      const page = payeePage.page;
      await page.goto("http://localhost:3000/approvals/payouts", {
        waitUntil: "networkidle",
      });
      await page.pause();
      const dropdown = await page.getByTestId("period-select");
      await dropdown.waitFor({ state: "visible", timeout: 30000 });
      await dropdown.click();

      await page
        .getByTitle("January 2024")
        .getByRole("listitem")
        .locator("div")
        .click();

      await page
        .getByRole("columnheader", { name: "Payee Name" })
        .waitFor({ state: "visible", timeout: 30000 });
      expect(await page.locator("[name='selectAll']").isVisible()).toBeTruthy();
      await page.getByRole("button", { name: "All Requests" }).click();
      expect(
        !(await page.locator("[name='selectAll']").isVisible())
      ).toBeTruthy();
      await page
        .getByRole("row", { name: "₹50.00" })
        .locator("svg")
        .first()
        .click();
      await page.getByRole("button", { name: "Cancel" }).click();
      await page
        .getByRole("row", { name: "₹50.00" })
        .locator("svg")
        .nth(1)
        .click();
      await page.getByRole("button", { name: "Cancel" }).click();

      await page.getByRole("button", { name: "Pending Requests" }).click();

      await page.getByRole("row", { name: "U3 User" }).locator("input").click();
      await page
        .getByRole("tabpanel")
        .getByRole("button", { name: "Approve" })
        .click();

      await page.getByText("Do you want to approve this line item ?").click();
      await page.getByRole("button", { name: "Yes, approve" }).click();
      await page.waitForSelector("text=Requests approved successfully", {
        timeout: 20000,
      });

      await page.getByRole("row", { name: "U4 User" }).locator("input").click();
      await page
        .getByRole("tabpanel")
        .getByRole("button", { name: "Reject" })
        .click();
      await page.getByRole("dialog").getByRole("textbox").click();
      await page
        .getByRole("dialog")
        .getByRole("textbox")
        .fill("rejecting using bulk action");
      await page.getByRole("button", { name: "Yes, reject" }).click();
      await page.waitForSelector("text=Requests rejected successfully", {
        timeout: 20000,
      });
    });
    test("Raise approval for different criterias & validate additional details in table", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      await page.goto(
        "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJtYW5hZ2VyMUBwbGF5d3JpZ2h0YXBwcm92YWxzbGlwLmNvbSIsInBzZCI6IjIwMjQtMDEtMDEiLCJwZWQiOiIyMDI0LTAxLTMxIn0=",
        {
          waitUntil: "networkidle",
        }
      );

      await page.waitForSelector('[data-testid="statement-menu"]', {
        timeout: 30000,
        state: "visible",
      });
      await page.getByTestId("statement-menu").click();
      await page.getByRole("menuitem", { name: "Request Approval" }).click();
      await page.getByPlaceholder("Choose approvers").click();
      await page
        .getByRole("dialog", { name: "Request Approval Manager 1 Jan 2024" })
        .getByRole("tablist")
        .locator("div")
        .filter({ hasText: "Users" })
        .nth(2)
        .click();
      await page.getByLabel("Users").getByText("power admin").click();
      await page.getByPlaceholder("Choose who needs to be").first().click();
      await page.getByRole("listitem").first().click();

      await page.getByRole("button", { name: "Send Approval Request" }).click();
      await page.waitForSelector("text=Approval requested successfully.", {
        timeout: 30000,
      });

      await page.goto("http://localhost:3000/approvals/payouts", {
        waitUntil: "networkidle",
      });
      const dropdown = await page.getByTestId("period-select");

      await dropdown.waitFor({ state: "visible", timeout: 30000 });
      await dropdown.click();
      await page
        .getByTitle("January 2024")
        .getByRole("listitem")
        .locator("div")
        .click();

      await page
        .getByRole("columnheader", { name: "Payee Name" })
        .waitFor({ state: "visible", timeout: 25000 });

      await expect(
        page
          .getByTestId(
            "testId-Plan 2024-Quota simple rolling sum - overall count value"
          )
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page.getByTestId("testId-Plan 2024-Team conditional").getByText("Plan")
      ).toBeVisible();
      await expect(
        page.getByTestId("testId-Plan 2024-Team simple").getByText("Plan")
      ).toBeVisible();
      await expect(
        page.getByTestId("testId-Plan 2024-Team tier").getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId(
            "testId-Plan 2024-Quota conditional rolling sum - every row QA"
          )
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId(
            "testId-Plan 2024-Quota conditional rolling sum - every row value"
          )
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page.getByTestId("testId-Plan 2024-Simple row level").getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId("testId-Plan 2024-Simple overall sum")
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId("testId-Plan 2024-Conditional row level")
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId("testId-Plan 2024-Conditional row level - do nothing")
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId("testId-Plan 2024-Conditional summation level")
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId(
            "testId-Plan 2024-Conditional summation level - do nothing"
          )
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId(
            "testId-Plan 2024-Tier conditional rolling sum - every row"
          )
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId(
            "testId-Plan 2024-Tier conditional rolling sum - overall count"
          )
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId(
            "testId-Plan 2024-Tier simple rolling count - overall count"
          )
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId("testId-Plan 2024-Tier simple rolling count - every row")
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId("testId-Plan 2024-Tier simple rolling sum - every row")
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId(
            "testId-Plan 2024-Tier simple rolling sum - overall count"
          )
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId(
            "testId-Plan 2024-Quota conditional rolling sum - overall count QA"
          )
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId(
            "testId-Plan 2024-Quota conditional rolling sum - overall count value"
          )
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId(
            "testId-Plan 2024-Quota simple rolling sum - every row QA"
          )
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId(
            "testId-Plan 2024-Quota simple rolling sum - every row value"
          )
          .getByText("Plan")
      ).toBeVisible();
      await expect(
        page
          .getByTestId(
            "testId-Plan 2024-Quota simple rolling sum - overall count QA"
          )
          .getByText("Plan")
      ).toBeVisible();

      await page
        .getByRole("columnheader", { name: "Payee Name" })
        .waitFor({ state: "visible", timeout: 25000 });

      const locator = await page.getByTestId(
        "testId-Plan 2024-Quota simple rolling sum - overall count value"
      );

      await locator.scrollIntoViewIfNeeded();
      await page
        .getByTestId(
          "testId-Plan 2024-Quota simple rolling sum - overall count value"
        )
        .getByText("Plan")
        .click();
      await page
        .getByTestId(
          "testId-Plan 2024-Quota simple rolling sum - overall count value"
        )
        .getByText("Plan")
        .click();
      await page
        .getByText(
          "Commission shown here is the total commission earned by the user for the entire criteria not for each item."
        )
        .click();
    });

    test("Snapshot Sync", async ({ adminPage }) => {
      const page = adminPage.page;
      await page.goto("/settings/commissions-and-data-sync", {
        waitUntil: "networkidle",
      });
      await page.waitForTimeout(2000);
      await page
        .locator("span")
        .filter({ hasText: "Selected Payees" })
        .nth(1)
        .click();
      await page
        .getByTestId("ever-select")
        .locator("div")
        .filter({ hasText: "Select payees" })
        .click();
      await page.getByTitle("User 5").locator("div").first().click();
      await page.getByPlaceholder("Select month").click();
      const currentYear = new Date().getFullYear();
      await page.getByRole("button", { name: currentYear.toString() }).click();
      await page.getByText("2024").click();
      await page.getByRole("cell", { name: "May" }).click();
      await page.getByRole("button", { name: "Run" }).nth(1).click();
      await page.getByRole("button", { name: "Skip & Run" }).click();
      await page.waitForSelector("text=Calculating Commissions...", {
        timeout: 20000,
      });
      await page.waitForSelector("text=Commission Calculations Completed", {
        timeout: 180000,
      });
      await page.getByText("Show Details").click();
      // After ETL 3.0 Migration Commission Snapshot Sync is always be one, previous it was depends on commission plan count.
      await page.waitForSelector(
        '[data-test-id="Commission Snapshot Sync-Completed-1"]',
        {
          timeout: 300000,
        }
      );
    });

    test.describe("Approve action", () => {
      test("Validate statement & approve", async ({ adminPage }) => {
        const page = adminPage.page;

        await page.goto(
          "/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyM0BwbGF5d3JpZ2h0YXBwcm92YWxzbGlwLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
          {
            waitUntil: "networkidle",
          }
        );

        await page.waitForSelector("text=Waiting for your approval", {
          state: "visible",
          timeout: 30000,
        });
        await page.getByRole("button", { name: "Approve" }).click();
        await page.getByRole("button", { name: "Yes, approve" }).click();

        await page.waitForSelector("text=Requests approved successfully", {
          state: "visible",
          timeout: 30000,
        });
      });
      test("Validate banner", async ({ adminPage }) => {
        const page = adminPage.page;

        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyM0BwbGF5d3JpZ2h0YXBwcm92YWxzbGlwLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
          {
            waitUntil: "networkidle",
          }
        );
        await page.waitForSelector("text=Waiting for your approval", {
          state: "hidden",
          timeout: 30000,
        });
      });
      test("Validate audit log", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("http://localhost:3000/settings/audit-logs", {
          waitUntil: "networkidle",
        });

        await page.waitForSelector(
          "text=Approve line item level approval request",
          {
            state: "visible",
            timeout: 20000,
          }
        );
      });
    });

    test.describe("Reject action", () => {
      test("Validate statement & reject", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto(
          "http://localhost:3000/statements/eyJwYXllZUVtYWlsSWQiOiJ1c2VyNEBwbGF5d3JpZ2h0YXBwcm92YWxzbGlwLmNvbSIsInBzZCI6IjIwMjQtMDItMDEiLCJwZWQiOiIyMDI0LTAyLTI5In0=",
          {
            waitUntil: "networkidle",
          }
        );

        const bannerVisiblility = await page.waitForSelector(
          "text=Waiting for your approval",
          {
            state: "visible",
            timeout: 30000,
          }
        );
        expect(bannerVisiblility).toBeTruthy();

        await page.getByRole("button", { name: "Reject" }).click();
        await page
          .getByPlaceholder("Add your comments")
          .fill("Banner should be removed");
        await page.getByRole("button", { name: "Reject Request" }).click();

        await page.waitForSelector("text=Requests rejected successfully", {
          state: "visible",
          timeout: 30000,
        });
      });
      test("Validate banner", async ({ adminPage }) => {
        const page = adminPage.page;
        const hiddenBannerVisibility = await page.waitForSelector(
          "text=Waiting for your approval",
          {
            state: "hidden",
            timeout: 20000,
          }
        );
        expect(hiddenBannerVisibility).toBeNull();
      });
      test("Validate audit log", async ({ adminPage }) => {
        const page = adminPage.page;
        await page.goto("http://localhost:3000/settings/audit-logs", {
          waitUntil: "networkidle",
        });
        await page.waitForSelector(
          "text=Reject line item level approval request",
          {
            state: "visible",
            timeout: 20000,
          }
        );
      });
    });
  }
);
