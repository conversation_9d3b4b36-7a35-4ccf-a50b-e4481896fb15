import ApprovalPage from "../../../../test-objects/approval-objects";
import PayoutPage from "../../../../test-objects/payout-objects";

const {
  newappFixtures: { test, expect },
} = require("../../../fixtures");

test.beforeEach(async ({}, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 90000); // 3 minutes
});

test.describe(
  "Approvals_validation",
  { tag: ["@approvals", "@regression", "@adminchamp-2"] },
  () => {
    test("Validate status change from needs attention to withdrawn ", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9233" },
        {
          type: "Description",
          description:
            "Validate status change from needs attention to withdrawn",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Status should change from pending to withdrawn",
        }
      );
      // "D User-Sep 2023"
      const approvalpage = new ApprovalPage(adminPage.page);
      const payoutpage = new PayoutPage(adminPage.page);
      await payoutpage.navigateToPayouts();
      await approvalpage.statementsNav(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJkQGQuY29tIiwicHNkIjoiMjAyMy0wOS0wMSIsInBlZCI6IjIwMjMtMDktMzAifQ=="
      );
      await approvalpage.approvalsTab();
      await approvalpage.actionButtonClick(
        ".flex > div > .flex > .ant-dropdown-trigger"
      );
      await approvalpage.actionsButton("Withdraw pending requests");
      await approvalpage.popupButton("Withdraw Request");
      await approvalpage.loaderpopUp("Withdrawing requests...");
      await approvalpage.banner("Request withdrawn successfully.");
      await payoutpage.navigateToPayouts();
    });

    test("Validate user is not able to send re-request without any changes ", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9233" },
        {
          type: "Description",
          description:
            "Validate user is not able to send re-request without any changes",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Re-request should not be sent without any change",
        }
      );
      // "B User-Sep 2023"
      const approvalpage = new ApprovalPage(adminPage.page);
      const payoutpage = new PayoutPage(adminPage.page);
      await approvalpage.statementsNav(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJiQGIuY29tIiwicHNkIjoiMjAyMy0wOS0wMSIsInBlZCI6IjIwMjMtMDktMzAifQ=="
      );
      await approvalpage.approvalsTab();
      await approvalpage.actionButtonClick(".ant-dropdown-trigger > path");
      await approvalpage.actionsButton("Request approval again");
      await approvalpage.loaderpopUp("Requesting...");
      await approvalpage.popupButton("Send Request");
      await approvalpage.banner(
        "There are no new changes. Cannot request approval again"
      );
      await payoutpage.navigateToPayouts();
    });

    test("Adding due date  ", async ({ adminPage }) => {
      // "B User-Sep 2023"
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9233" },
        {
          type: "Description",
          description: "Adding due date",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User shuld be able to add due date",
        }
      );
      const approvalpage = new ApprovalPage(adminPage.page);
      const payoutpage = new PayoutPage(adminPage.page);
      await approvalpage.statementsNav(
        "/statements/eyJwYXllZUVtYWlsSWQiOiJiQGIuY29tIiwicHNkIjoiMjAyMy0wOS0wMSIsInBlZCI6IjIwMjMtMDktMzAifQ=="
      );
      await approvalpage.approvalsTab();
      await approvalpage.actionButtonClick(
        ".pl-6 > div > div > div > .ant-dropdown-trigger"
      );
      await approvalpage.actionsButton("Change due date");
      await approvalpage.dueDate();
      await approvalpage.popupButton("Update due date");
      await approvalpage.loaderpopUp("Updating due date...");
      await approvalpage.banner("Due date updated successfully.");
      await payoutpage.navigateToPayouts();
    });

    test("BULK APPROVAL  ", async ({ payeePage }) => {
      // "A User Jan 2023-Approval raised to  P User"
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9233" },
        {
          type: "Description",
          description: "Raising bulk approval and approving it",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should be to raise approval and should be able to approve it",
        }
      );
      const approvalpage = new ApprovalPage(payeePage.page);
      const payoutpage = new PayoutPage(payeePage.page);
      await approvalpage.payouts();
      await approvalpage.tabName("Pending Requests");
      await approvalpage.selectorButton();
      await approvalpage.payeeApproval(
        "January 2023",
        "FlattenSimple",
        "Select all 648",
        "FlattenSimple",
        "Approve"
      );
      await approvalpage.approveButton();
      await approvalpage.banner("Approving...");
      await approvalpage.banner("Requests approved successfully");
      await payoutpage.navigateToPayouts();
    });

    test("BULK REJECT  ", async ({ payeePage }) => {
      // "A User Jan 2023-Approval raised to  P User"
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9233" },
        {
          type: "Description",
          description: "Raising bulk reject request and rejecting it",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to reject it",
        }
      );
      const approvalpage = new ApprovalPage(payeePage.page);
      const payoutpage = new PayoutPage(payeePage.page);
      await approvalpage.payouts();
      await approvalpage.tabName("Pending Requests");
      await approvalpage.selectorButton();
      await approvalpage.payeeReject(
        "January 2023",
        "FlattenComplicated",
        "Select all 648",
        "Reject"
      );
      await approvalpage.rejectButton();
      await approvalpage.banner("Requests rejected successfully");
      await payoutpage.navigateToPayouts();
    });

    test("BULK APPROVAL DIFFERENT USER  ", async ({ payeePage }) => {
      // "A User Jan 2023-Approval raised to  P User"
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9233" },
        {
          type: "Description",
          description: "Approving for different user",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to approve it",
        }
      );
      const approvalpage = new ApprovalPage(payeePage.page);
      const payoutpage = new PayoutPage(payeePage.page);
      await approvalpage.payouts();
      await approvalpage.tabName("Pending Requests");
      await approvalpage.selectorButton();
      await approvalpage.payeeApprovalMultiple(
        "January 2023",
        "ApprovalsSimple",
        "ApprovalsSimple",
        "Approve"
      );
      await approvalpage.approveButton();
      await approvalpage.banner("Approving...");
      await approvalpage.banner("Requests approved successfully");
      await payoutpage.navigateToPayouts();
    });

    test("Zero payouts", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9233" },
        {
          type: "Description",
          description: "Bulk approval for zero payout",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to raise approval",
        }
      );
      const approvalPage = new ApprovalPage(adminPage.page);
      const payoutpage = new PayoutPage(adminPage.page);
      await approvalPage.setPayout("August-2023");
      await approvalPage.sendBulkRequest();
      await payoutpage.navigateToPayouts();
    });

    test("draw adjustment payouts", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9234" },
        {
          type: "Description",
          description: "Bulk approval for zero payout",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "User should be able to raise approval",
        }
      );
      const approvalPage = new ApprovalPage(adminPage.page);
      const payoutpage = new PayoutPage(adminPage.page);
      await approvalPage.setPayout("June-2023");
      await approvalPage.sendBulkRequest();
      await payoutpage.navigateToPayouts();
    });

    test("few unlocked payouts", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T9235" },
        {
          type: "Description",
          description: "Bulk approval for few unlocked payout",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "User should be able to raise approval only for locked payouts",
        }
      );
      const approvalPage = new ApprovalPage(adminPage.page);
      const payoutpage = new PayoutPage(adminPage.page);
      await approvalPage.setPayout("December-2023");
      await approvalPage.sendBulkRequest();
      await payoutpage.navigateToPayouts();
    });
  }
);

test.describe(
  "Approvals Improvements validation",
  { tag: ["@approvals", "@regression", "@adminchamp-2"] },
  () => {
    test("Columns verification in Payouts & Customize", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-11171" },
        {
          type: "Description",
          description:
            "Verify the newly added columns in Payouts and customize columns window",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The newly added columns must be displayed in Payouts and customize columns window",
        }
      );
      const page = adminPage.page;
      const approvalPage = new ApprovalPage(page);

      // Verify newly added columns in Customise Columns
      await approvalPage.setPayout("December-2024");
      await page.getByRole("tab", { name: "Customize Columns" }).click();

      const columnValues = [
        "Stage Name",
        "Stage Status",
        "Requested on",
        "Awaiting Response From",
      ];
      for (let i = 0; i < columnValues.length; i++) {
        await page.getByPlaceholder("Search by", { exact: true }).clear();
        await page
          .getByPlaceholder("Search by", { exact: true })
          .fill(columnValues[i]);
        await expect(
          page.getByRole("button", { name: columnValues[i] })
        ).toBeVisible();
      }
      await page.getByRole("tab", { name: "Customize Columns" }).click();

      // Verify newly added columns in Payouts table
      const columnNames = [
        "stage_requested_on",
        "awaiting_response_from",
        "stage_name",
        "due_date",
      ];
      for (let i = 0; i < columnNames.length; i++) {
        await expect(
          page.locator(`[col-id="${columnNames[i]}"]`).first()
        ).toBeVisible();
      }
    });

    test("Filter search using Awaiting Response From, Approval Status filters", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-11171" },
        {
          type: "Description",
          description:
            "Use filters for the newly added columns and verify the results",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Relevant results must be fetched",
        }
      );
      const page = adminPage.page;
      const approvalPage = new ApprovalPage(page);

      // Verify newly added columns in Customise Columns
      await approvalPage.setPayout("December-2024");
      await page
        .locator(
          '//span[.//input[@placeholder="Search by name or email"]]/following-sibling::button'
        )
        .first()
        .click();
      await approvalPage.searchFilter("Approval Status", "Requested");
      await expect(
        page.getByRole("gridcell", { name: "Requested" })
      ).toHaveCount(2);
      await approvalPage.clearFilter();

      await approvalPage.searchFilter("Awaiting Response From", "A User");
      await expect(page.getByRole("gridcell", { name: "A User" })).toHaveCount(
        1
      );
      await expect(page.getByRole("gridcell", { name: "B User" })).toHaveCount(
        1
      );
      await expect(page.getByRole("gridcell", { name: "C User" })).toHaveCount(
        1
      );
    });

    test("Revoke Approval feature", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-11171" },
        {
          type: "Description",
          description:
            "Verify whether Revoke approval feature is displayed for requests with Requested and Needs Attention status",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Revoke approval feature must be displayed only for requests with Requested and Needs Attention status",
        }
      );
      const page = adminPage.page;
      const approvalPage = new ApprovalPage(page);
      await approvalPage.setPayout("December-2024");

      // Only requested, needs attention approval requests can be revoked
      await approvalPage.verifyRevokeApprovalButtonIsDisplayed("Requested");
      await approvalPage.verifyRevokeApprovalButtonIsDisplayed(
        "Needs Attention"
      );

      // Verify Revoke approval
      await approvalPage.setPayout("December-2024");
      await approvalPage.revokeApprovalByEmail("<EMAIL>");
      await approvalPage.verifyApprovalStatus("<EMAIL>", "Revoked");

      await approvalPage.bulkRevokeApproval();
      await approvalPage.verifyApprovalStatus("<EMAIL>", "Revoked");
      await approvalPage.verifyApprovalStatus("<EMAIL>", "Revoked");

      // Do not show Stage, Stage Status, (On track/Overdue), and Approval waiting on, if the Approval status is Approved.
      await approvalPage.setPayout("April-2023");
      await approvalPage.verifyPayoutColumnValues("<EMAIL>");
      await approvalPage.verifyPayoutColumnValues("<EMAIL>");

      // Verify Revoked filter in Approval->Payouts
      await page.goto("/approvals/payouts", { waitUntil: "networkidle" });
      await page.locator('[data-testid="period-select"]').click();
      await page.getByText("December 2024").click();
      await page
        .locator("div")
        .filter({ hasText: /^Approval status$/ })
        .nth(2)
        .click();
      await page.getByTitle("Revoked").nth(1).click();
      const status = await page
        .locator('[col-id="approvalsSysStatus"]>div>div>span')
        .first()
        .textContent();
      expect(status).toBe("Revoked");
      await expect(
        page.locator('[col-id="approvalsSysStatus"]>div>div>span')
      ).toHaveCount(8);
    });

    test("Audit Logs", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-11171" },
        {
          type: "Description",
          description:
            "Verify whether the performed actions are logged in Audit logs",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The revoke approval process should be logged in Audit logs",
        }
      );
      const page = adminPage.page;
      const approvalPage = new ApprovalPage(page);

      await page.goto("/settings/audit-logs", { waitUntil: "networkidle" });
      await page.getByTitle("20").click();
      await page.getByTitle("100").locator("div").first().click();
      await approvalPage.delay(2000);
      await expect(
        page.getByText("Revoke Payout Line Item approval instance(s)")
      ).toHaveCount(2);
    });

    test("Verify the request timeline details and Withdraw stage", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-11171" },
        {
          type: "Description",
          description:
            "Verify the details in request timeline and withdraw the stage",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "The timeline details must be populated correctly and withdraw should be performed",
        }
      );
      const page = adminPage.page;
      const approvalPage = new ApprovalPage(page);
      const statementUrl =
        "/statements/eyJwYXllZUVtYWlsSWQiOiJjQGMuY29tIiwicHNkIjoiMjAyNC0xMi0wMSIsInBlZCI6IjIwMjQtMTItMzEifQ==";
      await page.goto(statementUrl, { waitUntil: "networkidle" });
      await approvalPage.raiseApprovalRequest("Flow with Due date");
      await approvalPage.approvalsTab();
      await page.getByText("On trackDue in 2 day(s)").click();

      await approvalPage.setPayout("December-2024");
      await approvalPage.searchUserEmail("<EMAIL>");
      await page
        .getByRole("gridcell", { name: "Requested" })
        .locator("svg")
        .nth(1)
        .click();
      await expect(page.getByText("On trackDue in 2 day(s)")).toBeVisible();

      await approvalPage.withdrawApprovalRequest();
      await approvalPage.verifyApprovalStatus("<EMAIL>", "Withdrawn");
      await page
        .getByRole("row", { name: "Unpaid INR Withdrawn by EA" })
        .locator("svg")
        .nth(1)
        .click();
      await expect(page.getByText("Withdrawn by everstage on")).toBeVisible();
    });
  }
);
