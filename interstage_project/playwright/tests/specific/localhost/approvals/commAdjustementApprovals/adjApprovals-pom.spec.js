const AdjustmentsPage = require("../../../../../test-objects/adjustments-objects");
const {
  adjApprovalsFixtures: { test, expect },
} = require("../../../../fixtures");

test.beforeEach(async ({ adminPage }) => {
  const adjustmentsPage = new AdjustmentsPage(adminPage.page);
  await adjustmentsPage.goToSettingsAdjustments();
});

test.describe(
  "Commission Adjustment Approvals",
  {
    tag: [
      "@approvals",
      "@regression",
      "@commissionAdjustments",
      "@adminchamp-2",
    ],
  },
  () => {
    test("Threshold limit", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12775" },
        { type: "Description", description: "Threshold limit validation" },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Limit should be set for the specified currency",
        }
      );
      const adjustmentsPage = new AdjustmentsPage(adminPage.page);
      await adjustmentsPage.setThresholdValues("0", "3");
      await adjustmentsPage.saveThresholdAndVerifySuccessMessage();
    });

    test("Approved by approver - Timeline validation", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12787" },
        {
          type: "Description",
          description:
            "Validating the timeline status for approved by approver data",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Timeline should display the correct status for approved by approver data",
        }
      );
      const adjustmentsPage = new AdjustmentsPage(adminPage.page);
      await adjustmentsPage.viewTimeline(1, "approved");
      await adjustmentsPage.validateApprovedByApproverTimeline(
        /Approved.*.Adjustment: ₹3.00/
      );
    });

    test("Skip workflow - Timeline validation", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12808" },
        {
          type: "Description",
          description:
            "Validating the timeline status for skipping workflow data",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Timeline should display the correct status for skipping workflow data",
        }
      );
      const adjustmentsPage = new AdjustmentsPage(adminPage.page);
      await adjustmentsPage.searchAdjustment("Adj User1");
      await adjustmentsPage.editRecord(1, "Adj User1");
      await adjustmentsPage.selectWorkflow(/^adj wf - admin$/);
      await adjustmentsPage.checkSkipWorkflow("skip wf");
      await adjustmentsPage.submitAdjustment();
      await adjustmentsPage.viewSkipWfTimeline(1, "approved");
      await adjustmentsPage.validateTextContext("Auto Approved");
      await adjustmentsPage.validateTextContext(
        "The adjustment was skipped for approval."
      );
    });

    test("Edit/delete functionality for locked commission adjustments", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12822" },
        {
          type: "Description",
          description:
            "Validating the edit/delete functionality for locked statements with adjustments",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description: "Edit/delete functionality should be disabled",
        }
      );
      const adjustmentsPage = new AdjustmentsPage(adminPage.page);
      await adjustmentsPage.hoverEditRecord(0, "Adj User1");
      await adjustmentsPage.validateTooltip(
        "Editing disabled since the statement is locked."
      );
      await adjustmentsPage.hoverDeleteRecord();
      await adjustmentsPage.validateTooltip(
        "Deleting disabled since the statement is locked."
      );
    });

    test("Delete a plan with adjustments", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12825" },
        {
          type: "Description",
          description: "Validating plan deletion with adjustments present",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Plan should not be deleted with proper warning message should be displayed",
        }
      );
      const adjustmentsPage = new AdjustmentsPage(adminPage.page);
      await adjustmentsPage.goToCommissionPlans();
      await adjustmentsPage.deletePlan("Adj Approval Plan1");
      await adjustmentsPage.validateTextContext(
        "• Certain commission adjustments here associated with this plan need to be removed"
      );
      await adjustmentsPage.validateTextContext(
        "This Commission plan can't be deleted."
      );
      await adjustmentsPage.closePopup();
    });

    test("Delete payee from a plan with adjustments", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12827" },
        {
          type: "Description",
          description:
            "Remove payee from a plan with active adjustments present",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Payee should not be removed with proper warning message",
        }
      );
      const adjustmentsPage = new AdjustmentsPage(adminPage.page);
      await adjustmentsPage.goToCommissionPlans();
      await adjustmentsPage.selectPlan("Adj Approval Plan1");
      await adjustmentsPage.deletePayeeFromPlan();
      await adjustmentsPage.validateValidationContext(
        "Active Commission adjustments are present"
      );
      await adjustmentsPage.exitCanvas();
    });

    test("Adjustment with no workflow", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12804" },
        {
          type: "Description",
          description: "Adding an adjustment with no workflow selected",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Adjustment should not be created and proper error message should be displayed",
        }
      );
      const adjustmentsPage = new AdjustmentsPage(adminPage.page);
      await adjustmentsPage.addAdjustment(
        "Adj User1",
        "April 2024",
        "2",
        "Adj Approval Plan1"
      );
      await adjustmentsPage.submitAdjustment();
      await adjustmentsPage.validateTextContext(
        "Please select workflow options!"
      );
    });

    test("Lock statements with active adjustments", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12791" },
        {
          type: "Description",
          description:
            "Locking the statements for a payee with active adjustments present",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Warning pop up should be displayed with proper context before lock",
        }
      );
      const adjustmentsPage = new AdjustmentsPage(adminPage.page);
      const page = adminPage.page;
      await adjustmentsPage.goToPayouts();
      await adjustmentsPage.selectPeriod("March 2024");
      await adjustmentsPage.lockStatements("Adj User2");
      await adjustmentsPage.validateTextContext(
        "The selected statement has commission adjustments that need to be approved."
      );
      await adjustmentsPage.goToReviewAdjustmentsFromPayouts();
      await expect(page).toHaveURL(/.*settings\/adjustments\?status=requested/);
    });

    test("Rejected by approver - Timeline validation", async ({
      adminPage,
    }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12829" },
        {
          type: "Description",
          description: "Validating the timeline status for rejected data",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Timeline should display the correct status for rejected data",
        }
      );
      const adjustmentsPage = new AdjustmentsPage(adminPage.page);
      await adjustmentsPage.goToCommissionAdjustments();
      await adjustmentsPage.goToCommAdjApprovalPendingRequests();
      await adjustmentsPage.rejectRequest(0, "reject");
      await adjustmentsPage.goToSettingsAdjustments();
      await adjustmentsPage.viewTimeline(1, "rejected");
      await adjustmentsPage.validateTextContext(
        /Rejected.*.Adjustment: AU\$4.00/
      );
    });

    test("Auto Approval - Timeline validation", async ({ adminPage }) => {
      test.info().annotations.push(
        { type: "Test ID", description: "INTER-T12788" },
        {
          type: "Description",
          description: "Validating the timeline status for auto approved data",
        },
        { type: "Precondition", description: "None" },
        {
          type: "Expected Behaviour",
          description:
            "Timeline should display the correct status for auto approved data",
        }
      );
      const adjustmentsPage = new AdjustmentsPage(adminPage.page);
      await adjustmentsPage.searchAdjustment("Adj User2");
      await adjustmentsPage.editRecord(1, "Adj User2");
      await adjustmentsPage.selectAutoApproval();
      await adjustmentsPage.submitAdjustment();
      await adjustmentsPage.viewTimeline(1, "approved");
      await adjustmentsPage.validateTextContext("Auto Approved");
      await adjustmentsPage.validateTextContext(
        "As the adjustment amount is within the threshold range."
      );
    });
  }
);
