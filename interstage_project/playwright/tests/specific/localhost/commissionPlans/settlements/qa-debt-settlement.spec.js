import CommissionsSyncPrevPeriod from "../../../../../test-objects/commissionSyncPrevPeriod-objects";
import CommissionSync from "../../../../../test-objects/commissionSync-objects";
import AdjustementsV2Page from "../../../../../test-objects/adjustments-v2-objects";

const {
  playwrightSettlementFixtures: { test, expect },
} = require("../../../../fixtures");

const CommonUtils = require("../../../../../test-objects/common-utils-objects");

// NOTE - Sync based tests may randomly fail due to the time taken for the sync to complete. Please re-run the test in case of failure.

// Function to run commission sync for selected user
async function runUserCommissionSync(page, userName, syncDate) {
  const csPrev = new CommissionsSyncPrevPeriod(page);
  const cs = new CommissionSync(page);

  await csPrev.navigate("/settings/commissions-and-data-sync");

  const button = await page.getByRole("button", {
    name: "Calculate Commissions Run",
  });

  const isExpanded = await button.getAttribute("aria-expanded");
  if (isExpanded === "false") {
    await button.click();
  }

  await cs.checkSelectedPayeesOption();
  await cs.selectPayeeSelectedPayeesOption(userName);
  await csPrev.selectDate(syncDate);
  await csPrev.runCommissions();
  await csPrev.clickSkipandRun();
  await csPrev.waitForCalculationMessage();
  await csPrev.waitForCommissionsSuccess();
}

// Function to run commission sync for selected plan
async function runPlanCommissionSync(
  page,
  planName,
  syncDate,
  runFirst = false
) {
  const csPrev = new CommissionsSyncPrevPeriod(page);
  const cs = new CommissionSync(page);

  await csPrev.navigate("/settings/commissions-and-data-sync");

  const button = await page.getByRole("button", {
    name: "Calculate Commissions Run",
  });

  const isExpanded = await button.getAttribute("aria-expanded");
  if (isExpanded === "false") {
    await button.click();
  }

  await cs.checkPayeesInCommissionPlanOption();
  await cs.selectPlansCommissionSync(planName);
  await csPrev.selectDate(syncDate);

  if (runFirst) {
    await page
      .getByRole("button", { name: "Run", exact: true })
      .first()
      .click();
  } else {
    await page
      .getByRole("button", { name: "Run", exact: true })
      .first()
      .click();
  }
  await csPrev.clickSkipandRun();
  await csPrev.waitForCalculationMessage();
  await csPrev.waitForCommissionsSuccess();
}

// Function to run report etl for settlement object
async function runReportEtlSyncSettlementObj(page) {
  const csPrev = new CommissionsSyncPrevPeriod(page);

  await csPrev.navigate("/settings/commissions-and-data-sync");

  const button = await page.getByRole("button", {
    name: "Report ETL Run ETL for report",
  });

  const isExpanded = await button.getAttribute("aria-expanded");
  if (isExpanded === "false") {
    await button.click();
  }

  await csPrev.selectETLReport("Settlement");
  await csPrev.runCommissions();
  await csPrev.clickSkipandRun();
  await csPrev.waitForReportETLSuccess();
}

test.beforeEach(async ({ adminPage }, testInfo) => {
  testInfo.setTimeout(testInfo.timeout + 900000);
});

test.describe(
  "Settlement Testcases",
  { tag: ["@settlement", "@regression", "@primelogic-2"] },
  () => {
    test.describe("Sync tests - may fail randomly", () => {
      // test.describe.configure({ retries: 2 });
      test.skip("Test to validate that once the amount is processed, changing the fx and sync is run, no amount should get reflected in the pending amount", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        const commonpage = new CommonUtils(page);
        // Processing amount
        await page.goto("/commissions", { waitUntil: "networkidle" });
        await commonpage.setStorageCommValue("30-June-2024");
        await page
          .getByPlaceholder("Search by name or email", { exact: true })
          .click();
        await page
          .getByPlaceholder("Search by name or email", { exact: true })
          .fill("user 3");
        await expect(page.getByRole("treegrid")).toContainText(
          "U3User 3 Test <EMAIL>₹23,000.00₹0.00₹0.00₹0.00--0 %"
        );
        await page
          .getByTestId("<EMAIL>-register-payment")
          .click();
        await page.getByText("₹INR").click();
        // await expect(
        //   page.getByLabel("Register payment30 Jun").getByRole("document")
        // ).toContainText("To be paid₹23,000.00");
        await page
          .getByLabel("Register payment30 Jun")
          .locator('input[type="text"]')
          .click();
        await page
          .getByLabel("Register payment30 Jun")
          .locator('input[type="text"]')
          .fill("15,000.00");
        await page.getByRole("button", { name: "Register" }).click();
        await page
          .getByText("Payment registered successfully")
          .waitFor({ state: "visible" });
        await expect(page.getByRole("treegrid")).toContainText(
          "U3User 3 Test <EMAIL>₹15,000.00₹15,000.00₹0.00₹0.00₹0.000 %"
        );

        // Changing fx rate
        await page.goto("/settings", { waitUntil: "networkidle" });
        await page
          .getByRole("link", { name: "Basic Settings Configure" })
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Select currency$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "INR" }).click();
        await page.getByPlaceholder("Start month").click();
        await page.getByPlaceholder("Start month").fill("Jun-2024");
        await page.getByPlaceholder("Start month").press("Enter");
        await page.getByPlaceholder("End month").click();
        await page.getByPlaceholder("End month").fill("Jun-2024");
        await page.getByPlaceholder("End month").press("Enter");
        await page.getByRole("spinbutton").fill("2");
        await page.getByRole("button", { name: "Update" }).click();
        await expect(page.locator("#root")).toContainText(
          "Updated Successfully!!"
        );

        // Running sync
        await runUserCommissionSync(page, "User 3 Test 3", "01 Jun 2024");

        // Checking if pending amount is intact
        await page.goto("/commissions", { waitUntil: "networkidle" });
        await commonpage.setStorageCommValue("30-June-2024");
        await page
          .getByPlaceholder("Search by name or email", { exact: true })
          .click();
        await page
          .getByPlaceholder("Search by name or email", { exact: true })
          .fill("user 3");
        // await expect(page.getByRole("treegrid")).toContainText(
        //   "U3User 3 Test <EMAIL>₹39,000.00₹39,000.00₹0.00₹0.00₹0.000 %"
        // );
        await expect(page.getByRole("treegrid")).toContainText(
          "U3User 3 Test <EMAIL>₹15,000.00₹15,000.00₹0.00₹0.00₹0.000 %"
        );
      });

      test("Test to check for payouts after settlement end date", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        // Checking deals data before sync
        await page.goto("/databook/4056384c-8835-47cd-8fbb-9c8dbf891470", {
          waitUntil: "networkidle",
        });
        await page.getByText("Ds 1").click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Contains", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("user5");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(page.getByRole("treegrid")).toContainText("29");
        await expect(page.getByRole("treegrid")).toContainText("30");
        await expect(page.getByRole("treegrid")).toContainText("31");

        // Running sync
        await runUserCommissionSync(page, "User 5 Test 5", "01 Jun 2024");

        // Checking settlement report object data after sync
        // No records after settlement end date
        await page.goto("/databook/47b5fa31-149d-40cd-9361-16d8114cd72b", {
          waitUntil: "networkidle",
        });
        await page.getByText("Ds 2 Settlement").click();
        await page
          .getByLabel("Ds 2 Settlement")
          .getByRole("button")
          .first()
          .click();
        await page.getByText("check 1").click();
        await expect(page.getByLabel("Ds 2 Settlement")).toContainText(
          "Settlement Line Item Id Equal To 29Settlement Criteria Name Equal To Settlement 1Edit filterClear filterExport CSVDrag here to set row groupsDrag here to set column labels Commission Line Item Id Settlement Line Item Id Payee Payee Email Payout Frequency Commission Plan Type Record Type Commission Plan Id Commission Plan Settlement Criteria Id Settlement Criteria Name Settlement Period Settlement Period Start Date Settlement Period End Date Settlement Date Earned Commission Criteria Id Earned Commission Criteria Name Earned Commission Period Commission Period Start Date Commission Period End Date Commission Date Earned Commission Amount (Org Currency) Settlement Commission Amount (Org Currency) Earned Commission Amount (Payee Currency) Settlement Commission Amount (Payee Currency) Adjustment Id Currency Conversion Rate Org Currency Payee Currency Variable Pay Settlement Data Book Settlement Data Sheet Updated at Settlement Is Locked Settlement Locked Date No Rows To Show Customize Columns Pivot Data Commission Line Item Id Pivot ModeRow Groups* Select Row GroupsColumn Labels* Select Column LabelsValues* Select ValuesApply & Save viewApply to of Page of"
        );
      });

      test("Test to check Commission adjustments for settlement report object", async ({
        adminPage,
      }) => {
        const page = adminPage.page;

        // Adding new adjustment
        await page.goto("/settings", { waitUntil: "networkidle" });
        await page
          .getByRole("link", { name: "Adjustments Manage your" })
          .click();
        await page.getByRole("button", { name: "Add Adjustment" }).click();
        await page
          .getByRole("menuitem", { name: "Commission" })
          .locator("span")
          .click();
        await page.getByLabel("Payee*").click();
        await page.getByText("User 6 Test").click();
        await page.getByLabel("Effective Period*").click();
        await page.getByText("July").click();
        await page.getByPlaceholder("Add amount").click();
        await page.getByPlaceholder("Add amount").fill("500");
        await page.getByLabel("Commission Plan").click();
        await page.getByText("Plan 4").click();
        await page.getByLabel("Component Name").click();
        await page.locator("span[title='Simple']").click();
        await page.getByLabel("Line Item Id").click();
        await page.getByLabel("Line Item Id").fill("34");
        await page.getByLabel("Reason Category").click();
        await page.locator("span").filter({ hasText: "Others" }).click();
        await page.getByPlaceholder("Enter a description...").click();
        await page.getByPlaceholder("Enter a description...").fill("Test");
        await page.getByRole("button", { name: "Submit" }).click();
        await expect(page.locator("#root")).toContainText(
          "Adjustment saved successfully"
        );

        // Running report etl sync
        await runReportEtlSyncSettlementObj(page);

        // Checking for commission adjustments data after sync in settlement report object datasheet
        await page.goto("/databook/47b5fa31-149d-40cd-9361-16d8114cd72b", {
          waitUntil: "networkidle",
        });
        await page.getByText("Ds 2 Settlement").click();
        await expect(
          await page.getByRole("button", { name: "Update Data" })
        ).toBeVisible();
        await page.getByRole("button", { name: "Generate Data" }).click();
        await page
          .getByText("Generating DataBook Data...")
          .waitFor({ state: "visible" });
        await page.waitForSelector(
          'text="Databook Data generated successfully!"',
          { timeout: 360000 }
        );
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Ds 2 Settlement")
          .getByRole("button")
          .first()
          .click();
        await page.getByText("check 2").click();
        await expect(page.getByRole("treegrid")).toContainText("500");
      });

      test("Test to check user record after report ETL is run", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        const csPrev = new CommissionsSyncPrevPeriod(page);

        // Add new user to plan
        await csPrev.navigate("/plans");
        await page.getByText("Plan 4").click();
        await page.getByLabel("U1User 10 Test").check();
        await page.getByRole("button", { name: "Add Payees" }).click();
        await page.waitForTimeout(2000);
        await page.getByRole("button", { name: "Save" }).click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();

        // Checking if no records are present in the settlement report object datasheet for this new user
        await csPrev.navigate("/databook/47b5fa31-149d-40cd-9361-16d8114cd72b");
        await page.getByText("Ds 2 Settlement").click();
        await page.waitForSelector('role=button[name="Clear filter"]');
        await page.getByRole("button", { name: "Clear filter" }).click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Contains", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("user10");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(page.getByTestId("login-indicator")).toContainText(
          "Commission Line Item Id Settlement Line Item Id Payee Payee Email Payout Frequency Commission Plan Type Record Type Commission Plan Id Commission Plan Settlement Criteria Id Settlement Criteria Name Settlement Period Settlement Period Start Date Settlement Period End Date Settlement Date Earned Commission Criteria Id Earned Commission Criteria Name Earned Commission Period Commission Period Start Date Commission Period End Date Commission Date Earned Commission Amount (Org Currency) Settlement Commission Amount (Org Currency) Earned Commission Amount (Payee Currency) Settlement Commission Amount (Payee Currency) Adjustment Id Currency Conversion Rate Org Currency Payee Currency Variable Pay Settlement Data Book Settlement Data Sheet Updated at Settlement Is Locked Settlement Locked Date No Rows To Show Customize Columns Pivot Data Commission Line Item Id Pivot ModeRow Groups* Select Row GroupsColumn Labels* Select Column LabelsValues* Select ValuesApply & Save viewApply to of Page of"
        );

        // Running sync
        await runUserCommissionSync(page, "User 10 Test 10", "01 Jul 2024");

        // Running report etl sync
        await runReportEtlSyncSettlementObj(page);

        // Checking for user record in settlement report object ds after running sync
        await csPrev.navigate("/databook/47b5fa31-149d-40cd-9361-16d8114cd72b");
        await page.getByText("Ds 2 Settlement").click();
        await expect(
          await page.getByRole("button", { name: "Update Data" })
        ).toBeVisible();
        await page.getByRole("button", { name: "Generate Data" }).click();
        await page
          .getByText("Generating DataBook Data...")
          .waitFor({ state: "visible" });
        await page.waitForSelector(
          'text="Databook Data generated successfully!"',
          { timeout: 360000 }
        );
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Ds 2 Settlement")
          .getByRole("button")
          .first()
          .click();
        await page.getByText("check 3").click();
        await expect(
          page.locator('//div[@col-id="payee_email_id"]').nth(1)
        ).toHaveText("<EMAIL>");
      });

      test("Test to check values before and after sync with fx rate modified", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        const commonpage = new CommonUtils(page);
        const csPrev = new CommissionsSyncPrevPeriod(page);
        // Checking values before sync
        // Partial payment and arrear processing done
        await csPrev.navigate("/commissions");
        await commonpage.setStorageCommValue("31-May-2024");
        await page
          .getByPlaceholder("Search by name or email", { exact: true })
          .click();
        await page
          .getByPlaceholder("Search by name or email", { exact: true })
          .fill("user 9");
        await page.waitForTimeout(2000);
        await expect(page.getByRole("treegrid")).toContainText(
          "U9User 9 Test <EMAIL>£4,000.00£1,000.00£3,000.00£0.00£0.000 %"
        );
        await page.locator(".ag-group-contracted > .ag-icon").first().click();
        await expect(page.getByTestId("login-indicator")).toContainText(
          "Jul 15, 2024£1,000.00Partial <EMAIL> 15, 2024£3,000"
        );
        await page.getByTestId("<EMAIL>-actions-dd").click();
        await page.getByText("Unlock Statements").click();
        await expect(page.locator("#root")).toContainText(
          "Lock status updated successfully"
        );
        await page.getByRole("link", { name: "User 9 Test" }).click();
        await expect(
          page.locator('div[row-id="currentPayouts"]')
        ).toContainText("Payout from current period£4,000.00");
        await page.getByTestId("ever-select").getByText("May").click();
        await page.getByText("June").click();
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Payout from previously deferred commissions£8,000.00"
        );
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Payout Arrears£3,000.00"
        );

        // Checking values in settlement report object before sync
        await csPrev.navigate("/databook/47b5fa31-149d-40cd-9361-16d8114cd72b");
        await page.getByText("Ds 2 Settlement").click();
        await page
          .getByLabel("Ds 2 Settlement")
          .getByRole("button")
          .first()
          .click();
        await page.getByText("check 4").click();
        await page.getByText("Earned Commission Criteria Id").click();
        await expect(page.getByRole("treegrid")).toContainText("2,000");
        await expect(page.getByRole("treegrid")).toContainText("2,100");

        // Checking for the updated fx rate
        await csPrev.navigate("/settings");
        await page
          .getByRole("link", { name: "Basic Settings Configure" })
          .click();
        await expect(page.getByTestId("login-indicator")).toContainText(
          "31-May-2024CurrencyFx RateINR4AUD3GBP2"
        );
        await page.waitForTimeout(10000);

        // Running sync
        await runUserCommissionSync(page, "User 9 Test 9", "01 May 2024");

        // Running report etl sync
        await runReportEtlSyncSettlementObj(page);

        // Checking settlement report object ds values after sync
        await csPrev.navigate("/databook/47b5fa31-149d-40cd-9361-16d8114cd72b");
        await page.getByText("Ds 2 Settlement").click();
        await expect(
          await page.getByRole("button", { name: "Update Data" })
        ).toBeVisible();
        await page.getByRole("button", { name: "Generate Data" }).click();
        await page
          .getByText("Generating DataBook Data...")
          .waitFor({ state: "visible" });
        await page.waitForSelector(
          'text="Databook Data generated successfully!"',
          { timeout: 360000 }
        );
        await page.waitForLoadState("networkidle");
        await page
          .getByLabel("Ds 2 Settlement")
          .getByRole("button")
          .first()
          .click();
        await page.getByText("check 4").click();
        await page.getByText("Earned Commission Criteria Id").click();
        await expect(page.getByRole("treegrid")).toContainText("4,000");
        await expect(page.getByRole("treegrid")).toContainText("4,200");

        // Checking user statement values after sync
        await csPrev.navigate("/commissions");
        await commonpage.setStorageCommValue("31-May-2024");
        await page.getByRole("link", { name: "User 9 Test" }).click();
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "£8,000.00"
        );
        await page.getByTestId("ever-select").getByText("May").click();
        await page.getByText("June").click();
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Payout from previously deferred commissions£16,000.00"
        );
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Payout Arrears£7,000.00"
        );
      });

      test("Test for report object validation of settlement on collection", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        const csPrev = new CommissionsSyncPrevPeriod(page);

        // Checking plan's commission and settlement date fields
        await csPrev.navigate("/plans");
        await page.getByText("Plan settlement on collection").click();
        await page.getByText("Simple").first().click();
        await page.getByRole("button", { name: "Configuration" }).click();
        await expect(page.locator("body")).toContainText("Deal date");
        await page.getByText("Settlement 1").first().click();
        await page.getByRole("button", { name: "Configuration" }).click();
        await expect(page.locator("body")).toContainText("Sale date");

        // Publishing the plan
        await page
          .getByRole("button", { name: "Publish", exact: true })
          .click();
        await page
          .getByRole("dialog")
          .getByRole("button", { name: "Publish" })
          .click();
        await expect(
          page.getByText(
            "You've successfully published Plan settlement on collection"
          )
        ).toBeVisible();
        await page.waitForTimeout(2000);
        await page.getByRole("button", { name: "Close" }).last().click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();

        // Checking settlement report object ds values before sync
        await csPrev.navigate("/databook/47b5fa31-149d-40cd-9361-16d8114cd72b");
        await page.getByText("Ds 2 Settlement").click();
        await page.getByRole("button", { name: "Clear filter" }).click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Contains", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("user11");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(page.getByTestId("login-indicator")).toContainText(
          "Commission Line Item Id Settlement Line Item Id Payee Payee Email Payout Frequency Commission Plan Type Record Type Commission Plan Id Commission Plan Settlement Criteria Id Settlement Criteria Name Settlement Period Settlement Period Start Date Settlement Period End Date Settlement Date Earned Commission Criteria Id Earned Commission Criteria Name Earned Commission Period Commission Period Start Date Commission Period End Date Commission Date Earned Commission Amount (Org Currency) Settlement Commission Amount (Org Currency) Earned Commission Amount (Payee Currency) Settlement Commission Amount (Payee Currency) Adjustment Id Currency Conversion Rate Org Currency Payee Currency Variable Pay Settlement Data Book Settlement Data Sheet Updated at Settlement Is Locked Settlement Locked Date No Rows To Show"
        );

        // Running sync
        await runPlanCommissionSync(
          page,
          ["Plan settlement on collection"],
          "01 Jan 2024"
        );

        // Checking settlement report object ds values after sync
        await csPrev.navigate("/databook/47b5fa31-149d-40cd-9361-16d8114cd72b");
        await page.getByText("Ds 2 Settlement").click();
        await page.getByRole("button", { name: "Clear filter" }).click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Contains", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("user11");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await page
          .getByRole("treegrid")
          .getByText("Commission Line Item Id")
          .click();
        await expect(page.getByRole("treegrid")).toContainText(
          "<EMAIL>"
        );
        await expect(page.getByRole("treegrid")).toContainText("39");
        await expect(page.getByRole("treegrid")).toContainText("40");
      });

      test("Test to check lock and unlock statements functionality on settlement collection", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        const commonpage = new CommonUtils(page);
        const csPrev = new CommissionsSyncPrevPeriod(page);
        // Locking statements
        await csPrev.navigate("/commissions");
        await commonpage.setStorageCommValue("31-January-2024");
        // await page.getByText("Period").first().click();

        await page.getByRole("link", { name: "User 11 Test" }).click();
        await page.getByText("Payout from current period").click();
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Drag here to set row groupsDrag here to set column labels Plan Plan settlement on collectionCA$1,000.00 to of Page of"
        );
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Plan Plan settlement on collectionCA$1,000.00"
        );
        await page.getByTestId("lock-unlock-button").click();
        await expect(page.locator("#root")).toContainText(
          "Statement locked successfully"
        );

        // Create new plan and publish
        await csPrev.navigate("/plans");
        await page
          .getByTestId("pt-actions-Plan settlement on collection")
          .click();
        await page.getByText("With Payees").click();
        await page
          .getByRole("button", { name: "Publish", exact: true })
          .click();
        await page
          .getByRole("dialog")
          .getByRole("button", { name: "Publish" })
          .click();
        await expect(
          page
            .getByText(
              "You've successfully published Plan settlement on collection_Copy"
            )
            .first()
        ).toBeVisible();
        await page.getByRole("button", { name: "Close" }).nth(-1).click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();

        // Running sync
        await runPlanCommissionSync(
          page,
          ["Plan settlement on collection_Copy"],
          "01 Jan 2024"
        );

        // Checking statements after sync and unlocking it
        await csPrev.navigate("/commissions");
        await commonpage.setStorageCommValue("31-January-2024");
        await page.waitForTimeout(2000);
        await page.getByRole("link", { name: "User 11 Test" }).click();
        await page.locator(".ag-group-contracted > .ag-icon").first().click();
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Plan Plan settlement on collectionCA$1,000.00"
        );
        await page.getByTestId("ever-select").getByText("January").click();
        await page.getByText("February").click();
        await page.locator(".ag-group-contracted > .ag-icon").first().click();
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Payouts from deferred commissions forJanuary 2024 CA$2,000.00Plan settlement on collectionCA$2,000.00"
        );
        await page.getByTitle("February").click();
        await page.getByText("January").nth(1).click();
        await page.getByTestId("lock-unlock-button").click();
        await expect(page.locator("#root")).toContainText(
          "Statement unlocked successfully"
        );

        // Running sync again
        await runPlanCommissionSync(
          page,
          ["Plan settlement on collection_Copy"],
          "01 Jan 2024",
          true
        );

        // Checking statements after sync
        await csPrev.navigate("/commissions");
        await commonpage.setStorageCommValue("31-January-2024");
        await page.waitForTimeout(2000);
        await page.getByRole("link", { name: "User 11 Test" }).click();
        await page.locator(".ag-group-contracted > .ag-icon").first().click();
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Payout from current periodCA$2,000.00"
        );
        await page.getByTestId("ever-select").getByText("January").click();
        await page.getByText("February").click();
        await page.locator(".ag-group-contracted > .ag-icon").first().click();
        await page.getByText("Payout Summary").click();
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Payouts from deferred commissions forJanuary 2024 CA$4,000.00"
        );
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Plan settlement on collectionCA$2,000.00"
        );
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Plan settlement on collection_CopyCA$2,000.00"
        );
      });

      test("Test for report object validation of settlement on booking", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        const csPrev = new CommissionsSyncPrevPeriod(page);

        // Checking plan's commission and settlement date fields
        await csPrev.navigate("/plans");
        await page.getByText("Plan settlement on booking").click();
        await page.getByText("Simple").first().click();
        await page.getByRole("button", { name: "Configuration" }).click();
        await expect(page.locator("body")).toContainText("Deal date");
        await page.getByText("Settlement 1").first().click();
        await page.getByRole("button", { name: "Configuration" }).click();
        await expect(page.locator("body")).toContainText("Deal date");

        // Publishing the plan
        await page
          .getByRole("button", { name: "Publish", exact: true })
          .click();
        await page
          .getByRole("dialog")
          .getByRole("button", { name: "Publish" })
          .click();
        await expect(
          page.getByText(
            "You've successfully published Plan settlement on booking"
          )
        ).toBeVisible();
        await page.getByRole("button", { name: "Close" }).last().click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();

        // Checking settlement report object ds values before sync
        await csPrev.navigate("/databook/47b5fa31-149d-40cd-9361-16d8114cd72b");
        await page.getByText("Ds 2 Settlement").click();
        await page.getByRole("button", { name: "Clear filter" }).click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Contains", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("user12");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await expect(page.getByTestId("login-indicator")).toContainText(
          "Commission Line Item Id Settlement Line Item Id Payee Payee Email Payout Frequency Commission Plan Type Record Type Commission Plan Id Commission Plan Settlement Criteria Id Settlement Criteria Name Settlement Period Settlement Period Start Date Settlement Period End Date Settlement Date Earned Commission Criteria Id Earned Commission Criteria Name Earned Commission Period Commission Period Start Date Commission Period End Date Commission Date Earned Commission Amount (Org Currency) Settlement Commission Amount (Org Currency) Earned Commission Amount (Payee Currency) Settlement Commission Amount (Payee Currency) Adjustment Id Currency Conversion Rate Org Currency Payee Currency Variable Pay Settlement Data Book Settlement Data Sheet Updated at Settlement Is Locked Settlement Locked Date No Rows To Show"
        );

        // Running sync
        await runPlanCommissionSync(
          page,
          ["Plan settlement on booking"],
          "01 Jan 2024"
        );

        // Checking settlement report object ds values after sync
        await csPrev.navigate("/databook/47b5fa31-149d-40cd-9361-16d8114cd72b");
        await page.getByText("Ds 2 Settlement").click();
        await page.getByRole("button", { name: "Clear filter" }).click();
        await page.getByRole("button", { name: "Add Filter" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Column$/ })
          .nth(3)
          .click();
        await page.locator("span").filter({ hasText: "Payee Email" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Operator$/ })
          .nth(3)
          .click();
        await page.getByText("Contains", { exact: true }).click();
        await page.getByRole("textbox").nth(1).click();
        await page.getByRole("textbox").nth(1).fill("user12");
        await page.getByRole("button", { name: "Apply", exact: true }).click();
        await page
          .getByRole("treegrid")
          .getByText("Commission Line Item Id")
          .click();
        await expect(page.getByRole("treegrid")).toContainText(
          "<EMAIL>"
        );
        await expect(page.getByRole("treegrid")).toContainText("42");
        await expect(page.getByRole("treegrid")).toContainText("43");
      });

      test("Test to check lock and unlock statements functionality on settlement booking", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        const commonpage = new CommonUtils(page);
        const csPrev = new CommissionsSyncPrevPeriod(page);
        // Locking statements
        await csPrev.navigate("/commissions");
        await commonpage.setStorageCommValue("31-January-2024");
        await page.getByRole("link", { name: "User 12 Test" }).click();
        await page.getByText("Payout from current period").click();
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Plan Plan settlement on booking€3.000,00"
        );
        await page.getByTestId("lock-unlock-button").click();
        await expect(page.locator("#root")).toContainText(
          "Statement locked successfully"
        );

        // Create new plan and publish
        await csPrev.navigate("/plans");
        await page.getByTestId("pt-actions-Plan settlement on booking").click();
        await page.getByText("With Payees").click();
        await page
          .getByRole("button", { name: "Publish", exact: true })
          .click();
        await page
          .getByRole("dialog")
          .getByRole("button", { name: "Publish" })
          .click();
        await expect(
          page
            .getByText(
              "You've successfully published Plan settlement on booking_Copy"
            )
            .first()
        ).toBeVisible();
        await page.getByRole("button", { name: "Close" }).last().click();
        await page.getByRole("button", { name: "Exit Canvas" }).click();

        // Running sync
        await runPlanCommissionSync(
          page,
          ["Plan settlement on booking_Copy"],
          "01 Jan 2024"
        );

        // Checking statements after sync and unlocking it
        await csPrev.navigate("/commissions");
        await commonpage.setStorageCommValue("31-January-2024");
        await page.waitForTimeout(2000);
        await page.getByRole("link", { name: "User 12 Test" }).click();
        await page.locator(".ag-group-contracted > .ag-icon").first().click();
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Plan Plan settlement on booking€3.000,00"
        );
        await page.getByTestId("lock-unlock-button").click();
        await expect(page.locator("#root")).toContainText(
          "Statement unlocked successfully"
        );

        // Running sync again
        await runPlanCommissionSync(
          page,
          ["Plan settlement on booking_Copy"],
          "01 Jan 2024",
          true
        );

        // Checking statements after sync
        await csPrev.navigate("/commissions");
        await commonpage.setStorageCommValue("31-January-2024");
        await page.waitForTimeout(2000);
        await page.getByRole("link", { name: "User 12 Test" }).click();
        await page.locator(".ag-group-contracted > .ag-icon").first().click();
        await expect(page.getByLabel("Payout Summary")).toContainText(
          "Payout from current period€6.000,00"
        );
      });

      test("Test to check Settlement - Invalidating the invoice entry(date) for a rule having multiple criterias and adding it again with the same invoice id", async ({
        adminPage,
      }) => {
        const page = adminPage.page;
        const csPrev = new CommissionsSyncPrevPeriod(page);

        // Checking settlement report object ds values before sync
        await csPrev.navigate("/databook/47b5fa31-149d-40cd-9361-16d8114cd72b");
        await page.getByText("Ds 2 Settlement").click();
        await page
          .getByLabel("Ds 2 Settlement")
          .getByRole("button")
          .first()
          .click();
        await page.getByRole("list").getByText("user 13").click();
        // await expect(page.getByRole("treegrid")).toContainText(
        //   "14510User 13 Test 13user13@testclientsett1.comMonthlyMAINSettlementb81c2f46-b8fd-46ea-8652-939d74b7ea2ePlan for user 13da30e27f-abdd-4634-9891-55a0ded241e7Settlement 1March 202401 Mar 202431 Mar 202418 Mar 2024d6f44d77-0a1f-4d79-b168-f7f3fb9a77c9ConditionMarch 202401 Mar 202431 Mar 202412 Mar 20241,0001001,0001001USDAUD0DealsDs 2 Invoice sheet23 Jul 2024false24510User 13 Test 13user13@testclientsett1.comMonthlyMAINSettlementb81c2f46-b8fd-46ea-8652-939d74b7ea2ePlan for user 13da30e27f-abdd-4634-9891-55a0ded241e7Settlement 1March 202401 Mar 202431 Mar 202418 Mar 2024bc1ef407-1e1c-4ed5-ab70-1fffb1e5cdf1SimpleMarch 202401 Mar 202431 Mar 202412 Mar 20241,1001101,1001101USDAUD0DealsDs 2 Invoice sheet23 Jul 2024false"
        // );
        await expect(
          page.locator("div.ag-row-even div[col-id$='settlement_date']")
        ).toContainText("Mar 18, 2024");

        // Delete invoice id 10
        await csPrev.navigate("/settings");
        await page
          .getByRole("link", { name: "Manage Data Manage your data" })
          .click();
        await page.getByText("Delete data", { exact: true }).click();
        await page.getByText("Invoice Obj").click();
        await page.getByRole("button", { name: "Next" }).click();
        await page
          .getByRole("button", { name: "Drag and drop file here, or" })
          .click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles("./upload-files/delete 10.csv");
        await expect(page.locator("#root")).toContainText(
          "delete 10.csv file read successfully."
        );
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Validate" }).click();
        await page
          .getByRole("row")
          .getByText("Validated")
          .waitFor({ state: "visible" });
        await page.getByRole("button", { name: "Delete" }).click();
        await page.getByRole("button", { name: "Confirm" }).click();
        await page.getByPlaceholder("Email").click();
        await page.getByPlaceholder("Email").fill("<EMAIL>");
        await page.getByRole("button", { name: "Submit" }).click();
        await page.getByRole("button", { name: "Got it" }).click();
        await page.waitForTimeout(12000);

        // Add invoice id 10 again with different invoice date
        await csPrev.navigate("/settings");
        await page
          .getByRole("link", { name: "Manage Data Manage your data" })
          .click();
        await page.getByText("Create new data", { exact: true }).click();
        await page
          .getByLabel("Manually created objects2")
          .getByText("Invoice Obj")
          .click();
        await page.getByRole("button", { name: "Next" }).click();
        await page
          .getByRole("button", { name: "Drag and drop file here, or" })
          .click();
        await page
          .locator("span>input[type='file']")
          .setInputFiles("./upload-files/upload 10.csv");
        await expect(page.locator("#root")).toContainText(
          "upload 10.csv file read successfully."
        );
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Next" }).click();
        await page.getByRole("button", { name: "Validate" }).click();
        await page
          .getByRole("row")
          .getByText("Validated")
          .waitFor({ state: "visible" });
        await page.getByRole("button", { name: "Import" }).click();
        await page.getByPlaceholder("Email").click();
        await page.getByPlaceholder("Email").fill("<EMAIL>");
        await page.getByRole("button", { name: "Submit" }).click();
        await page.getByRole("button", { name: "Got it" }).click();
        await page.waitForTimeout(14000);

        // Generate invoice datasheet
        await csPrev.navigate("/databook/4056384c-8835-47cd-8fbb-9c8dbf891470");
        await page.getByText("Ds 2 Invoice sheet").click();
        await expect(
          await page.getByRole("button", { name: "Update Data" })
        ).toBeVisible();
        await page.getByRole("button", { name: "Generate Data" }).click();
        await page
          .getByText("Generating DataBook Data...")
          .waitFor({ state: "visible" });
        await page.waitForSelector(
          'text="Databook Data generated successfully!"',
          { timeout: 360000 }
        );
        await page.waitForLoadState("networkidle");
        await expect(
          page
            .locator("div.ag-row-even div[col-id$='co_2_invoice_date']")
            .last()
        ).toContainText("Mar 25, 2024");

        // Running sync
        await runUserCommissionSync(page, "User 13 Test 13", "01 Mar 2024");

        // Checking settlement report object ds values after sync with changed invoice date
        await csPrev.navigate("/databook/47b5fa31-149d-40cd-9361-16d8114cd72b");
        await page.getByText("Ds 2 Settlement").click();
        await page
          .getByLabel("Ds 2 Settlement")
          .getByRole("button")
          .first()
          .click();
        await page.getByRole("list").getByText("user 13").click();
        await expect(
          page.locator("div.ag-row-even div[col-id$='settlement_date']")
        ).toContainText("Mar 25, 2024");
      });
    });

    test("Test to validate that the commission amount is displaying as expected when exiting a user and selecting a commission period", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      const commonpage = new CommonUtils(page);
      const adjv2 = new AdjustementsV2Page(page);
      // Checking for user earned and deferred commission values before exit
      await page.goto("/commissions", { waitUntil: "networkidle" });
      await commonpage.setStorageCommValue("31-May-2024");
      await page.getByRole("link", { name: "User 7 Test" }).click();
      await page.getByText("Commission Summary").click();
      await expect(page.getByLabel("Commission Summary")).toContainText(
        "Earned CommissionsCA$6,200.00"
      );
      await expect(page.getByLabel("Commission Summary")).toContainText(
        "Deferred Commissions-CA$2,200.00"
      );

      // Exiting the user and verification
      await adjv2.navigate("/users");
      await page
        .getByTestId("<EMAIL> users dd button")
        .click();
      await page.getByRole("button", { name: "Initiate Exit" }).click();
      await adjv2.selectPeriod("Select date", "30 May 2024");
      // Fetch the element locator
      const element = page.getByLabel("Exit user").getByRole("document");

      await expect(element).toContainText(
        "Commission end periodMay 2024 (FY 2024)"
      );
      await page.getByRole("button", { name: "Validate" }).click();
      await expect(page.locator("#root")).toContainText(
        "Validation Successful!"
      );
      // Perform the assertion
      await expect(element).toContainText(
        "Last processed commissionsPeriodTotal PayoutPayout statusMay 2024 (FY 2024)CA$4000.00UNPAIDView statement"
      );

      await page.getByRole("button", { name: "Confirm" }).click();
      await expect(page.locator("#root")).toContainText("Save Successful!");
    });

    test("Test to check adding adjustments for the plan and deleting the same published plan case", async ({
      adminPage,
    }) => {
      const page = adminPage.page;

      // Adding new adjustment
      await page.goto("/settings", { waitUntil: "networkidle" });
      await page.getByRole("link", { name: "Adjustments Manage your" }).click();
      await page.getByRole("button", { name: "Add Adjustment" }).click();
      await page
        .getByRole("menuitem", { name: "Commission" })
        .locator("span")
        .click();
      await page.getByLabel("Payee*").click();
      await page.getByText("User 2 Test").click();
      await page.getByLabel("Effective Period*").click();
      await page.getByText("June").click();
      await page.getByPlaceholder("Add amount").click();
      await page.getByPlaceholder("Add amount").fill("150");
      await page.getByLabel("Commission Plan").click();
      await page.getByText("Plan 3 Delete").click();
      await page.getByLabel("Component Name").click();
      await page.locator('span[title="Simple"]').click();
      await page.getByPlaceholder("Enter a description...").click();
      await page.getByPlaceholder("Enter a description...").fill("Test");
      await page.getByRole("button", { name: "Submit" }).click();
      await expect(page.locator("#root")).toContainText(
        "Adjustment saved successfully"
      );

      // Deleting the plan for which adjustment was added
      await page.goto("/plans", { waitUntil: "networkidle" });
      await page.getByPlaceholder("Search by plan name").click();
      await page.getByPlaceholder("Search by plan name").fill("Plan 3 delete");
      await page.getByTestId("pt-actions-Plan 3 Delete").click();
      await page.getByText("Delete", { exact: true }).click();
      await expect(page.getByRole("dialog")).toContainText(
        "This Commission plan can't be deleted."
      );
      await expect(page.getByRole("dialog")).toContainText(
        "• Certain commission adjustments here associated with this plan need to be removed before the plan can be deleted. (Review here)"
      );
      await page.getByRole("link", { name: "here" }).click();
      await expect(page.getByText("User 1 Test 1")).toBeVisible();
      await expect(page.getByText("User 2 Test 2")).toBeVisible();
    });

    test("Test to check switch currency updates values as per conversion rates and paid status remains the same", async ({
      adminPage,
    }) => {
      const page = adminPage.page;
      const commonpage = new CommonUtils(page);
      await page.goto("/settings", { waitUntil: "networkidle" });
      await page
        .getByRole("link", { name: "Basic Settings Configure" })
        .click();
      // Fx rate updated for May month
      await expect(page.getByTestId("login-indicator")).toContainText(
        "31-May-2024CurrencyFx RateINR4AUD3GBP2"
      );
      await page.goto("/commissions", { waitUntil: "networkidle" });
      await commonpage.setStorageCommValue("31-May-2024");
      await page.getByRole("link", { name: "User 1 Test" }).click();

      // Checking earned commission and commission adjustment with payee currency
      await page.getByTestId("currency-button").click();
      await page.getByText("Payee Currency").click();
      await expect(page.getByLabel("Payout Summary")).toContainText(
        "Payout from current periodAU$12,000.00"
      );
      await expect(page.getByLabel("Payout Summary")).toContainText(
        "AdjustmentsAU$200.00"
      );

      // Checking for paid status with payee currency
      await expect(page.getByTestId("login-indicator")).toContainText(
        "Not paid"
      );

      // Checking earned commission and commission adjustment with global currency
      await page.getByTestId("currency-button").click();
      await page.getByText("Global", { exact: true }).click();
      await expect(page.getByLabel("Payout Summary")).toContainText(
        "Payout from current period$4,000.00"
      );
      await expect(page.getByLabel("Payout Summary")).toContainText(
        "Adjustments$66.67"
      );

      // Checking for paid status with payee currency
      await expect(page.getByTestId("login-indicator")).toContainText(
        "Not paid"
      );

      // Checking deferred commission with payee currency
      await page.getByTestId("ever-select").getByText("May").click();
      await page.getByText("June").click();
      await page.getByTestId("currency-button").click();
      await page.getByText("Payee Currency").click();
      await expect(page.getByLabel("Payout Summary")).toContainText(
        "Payout from previously deferred commissionsAU$24,000.00"
      );
      // Checking deferred commission with global currency
      await page.getByTestId("currency-button").click();
      await page.getByRole("menuitem", { name: "Global USD ($)" }).click();
      await expect(page.getByLabel("Payout Summary")).toContainText(
        "Payout from previously deferred commissions$24,000.00"
      );
    });
  }
);
