import planperiodexclusion from "../../../../../test-objects/planPeriodExclusion-objects";
const {
  planPeriodExlusionFixtures: { test, expect },
} = require("../../../../fixtures");

// const simplePlanID = 'a3092638-e10a-483c-9cf3-05e31b56daad';
// const simplePlanID = '34df710f-9029-4af4-b040-6d97d6c0d5d6';

test.describe(
  "Plan Period Exclusion",
  { tag: ["@commissionplan", "@regression", "@adminchamp-5"] },
  () => {
    test.beforeEach(async ({ adminPage }, testInfo) => {
      testInfo.setTimeout(testInfo.timeout + 1200000);
      const page = adminPage.page;
      console.log("Running Before Each");
      const key = "commission-view-period";
      const value = "31-January-2024";

      await page.evaluate(
        ({ key, value }) => {
          // Set the localStorage value for the current page
          localStorage.setItem(key, value);
        },
        { key, value }
      );
    });

    test(
      "Validating plan period exclusion , quota erosion addition inside component view , payout and commission summary standardisation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-T16389" },
          {
            type: "Description",
            description:
              "Validate if january month is excluded from commission calculation",
          },
          {
            type: "Expected Behaviour",
            description: "Jan to have zero payout for Payee 1",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const planPeriodExclusion = new planperiodexclusion(page);
        await planPeriodExclusion.navigation(
          "plans?plan_id=80fcd20a-37c8-42ba-8bba-cb97bcd96105"
        );
        await planPeriodExclusion.excludePeriod();
        await planPeriodExclusion.navigation(
          "/settings/commissions-and-data-sync"
        );
        await planPeriodExclusion.runCommissionSync();
        await expect(
          page.getByText("Commission Calculations Completed")
        ).toBeVisible({ timeout: 480000 });

        // Validate the total payout in payouts page of payee1 ,payee 2, payee 3 based on plan exclusion period

        await planPeriodExclusion.navigation("/commissions");
        await page.getByRole("gridcell", { name: "$400.00" }).first().click();
        await page.getByRole("gridcell", { name: "$400.00" }).nth(1).click();
        await planPeriodExclusion.getIntostatements(
          "<EMAIL>-name-cell"
        );
        await page.getByText("0.00", { exact: true }).click();

        // Validate the total payout in statements of payee 1 , payee 2 , payee3 based on plan exlusion period
        await page.getByText("Payee 1").click();
        await page.getByText("Payee 2").click();
        await page.getByText("22,000.00").click();

        await page.getByText("Payee 2").click();
        await page.getByText("Payee 3").click();
        await page.getByText("22,000.00").click();

        // Validate the total payout in payouts page of payee1 ,payee 2, payee 3 based on plan exclusion period

        await planPeriodExclusion.navigation("/commissions");
        await page.getByPlaceholder("Select date").click();
        await page.locator(".ant-picker-header-next-btn").click();
        await planPeriodExclusion.setPayoutDate("-02-29");
        await page.getByRole("gridcell", { name: "$300.00" }).click();
        await page.getByRole("gridcell", { name: "$700.00" }).click();

        // Validate the total quota eriosion in statements of payee 1 , payee 2 , payee3 based on plan exclusion period

        await planPeriodExclusion.getIntostatements(
          "<EMAIL>-name-cell"
        );
        await page.getByText("41,000.00").click();
        await page.getByText("Payee 1").click();
        await page
          .locator("li")
          .filter({ hasText: "Payee 2" })
          .getByRole("listitem")
          .click();
        await page.getByText("0.00", { exact: true }).click();
        await page.getByText("Payee 2").click();
        await page
          .locator("li")
          .filter({ hasText: "Payee 3" })
          .getByRole("listitem")
          .click();
        await page.getByText("183,000.00").click();

        // Validate the total quota erosion inside the quota components of payee 1, payee 2 payee 2 based on plan exclusion period

        await planPeriodExclusion.navigation("/commissions");

        await page.getByPlaceholder("Select date").click();
        await page.locator(".ant-picker-header-prev-btn").click();
        await planPeriodExclusion.setPayoutDate("-01-31");
        // await page.getByTitle("-01-31").locator("div").click();

        await planPeriodExclusion.getIntostatements(
          "<EMAIL>-name-cell"
        );

        await planPeriodExclusion.earnedCommissionsComponent();
        await page.getByText("0.00", { exact: true }).nth(1).click();
        await page.getByLabel("Close").click();

        await page.getByText("Payee 1").click();
        await page
          .locator("li")
          .filter({ hasText: "Payee 2" })
          .getByRole("listitem")
          .click();

        await planPeriodExclusion.earnedCommissionsComponent();
        await page.getByText("22,000.00").nth(1).click();
        await page.getByLabel("Close").click();

        await page.getByText("Payee 2").click();
        await page.getByText("Payee 3").click();


        await planPeriodExclusion.earnedCommissionsComponent();
        await page.getByText("22,000.00").nth(1).click();
        await page.getByLabel("Close").click();

        // Validate the standardisation of payouts numbers in payout summary and commission summary section

        await planPeriodExclusion.payoutSummaryComponent();
        await page
          .getByLabel("Quota", { exact: true })
          .getByRole("gridcell", { name: "$400.00" })
          .click();

        await page.getByLabel("Close").click();
        await page.getByText("Commission Summary").click();
        await page
          .getByLabel("Commission Summary")
          .getByText("$400.00")
          .click();

        await planPeriodExclusion.navigation("/commissions");

        await page.getByPlaceholder("Select date").click();
        await page.locator(".ant-picker-header-next-btn").click();
        await planPeriodExclusion.setPayoutDate("-02-29");
        await planPeriodExclusion.getIntostatements(
          "<EMAIL>-name-cell"
        );
        await page.getByText("Commission Summary").click();
        await page.getByText("Earned Commissions").click();
        await page
          .getByLabel("Commission Summary")
          .getByText("$300.00")
          .click();
        await page
          .locator("div")
          .filter({ hasText: /^Payout Summary$/ })
          .first()
          .click();
        await page
          .getByText("Payout from current period", { exact: true })
          .click();
        await page
          .getByLabel("Payout Summary")
          .getByText("Commission Plan")
          .click();
        await page.getByRole("button", { name: "Quota" }).click();
        await page
          .locator("div")
          .filter({ hasText: /^Total\$300\.00$/ })
          .getByRole("gridcell")
          .nth(4)
          .click();
        await page.getByLabel("Close").click();

        await page.getByText("Payee 1").click();
        await page
          .locator("li")
          .filter({ hasText: "Payee 3" })
          .getByRole("listitem")
          .click();
        await page.getByText("Payout from current period").click();
        await page.getByText("Commission Plan").click();
        await page.getByRole("button", { name: "Quota" }).click();
        await page
          .getByLabel("Quota", { exact: true })
          .getByRole("gridcell", { name: "$700.00" })
          .click();
        await page.getByLabel("Close").click();
        await page.getByText("Commission Summary").click();
        await page
          .getByLabel("Commission Summary")
          .getByText("$700.00")
          .click();
      }
    );
  }
);

