import CanvasCommission from "../../../../../test-objects/canvas-objects";

const {
  canvasUIFixtures: { test, expect },
} = require("../../../../fixtures");
const COM_VARIABLE = {
  currentYear: new Date().getFullYear().toString(),
};

test.describe(
  "Canvas Commission Plan",
  { tag: ["@commissionplan", "@regression", "@adminchamp-5"] },
  () => {
    test(
      "Create a Main Commission plan",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7944" },
          {
            type: "Description",
            description:
              "Create a Main Commission plan with valid criteria / component and verify its creation.",
          },
          { type: "Precondition", description: "Databook / Datasheet Data" },
          {
            type: "Expected Behaviour",
            description:
              "The Commission Plan should be successfully created with the valid criteria and the URL should contain the plan_id",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commission = new CanvasCommission(page);
        await commission.goToPlans("/plans");
        await commission.clickBuildNewPlanButton();
        await commission.enterPlanName("Automation_Commission_Plan");
        await commission.enterDate(
          `Jan 1, ${COM_VARIABLE.currentYear}`,
          "From"
        );
        console.log(COM_VARIABLE.currentYear);
        await commission.enterDateTo(
          `Dec 31, ${COM_VARIABLE.currentYear}`,
          "To"
        );
        await commission.selectPayoutFrequency("Quarterly");
        await commission.clickBuildPlanButton();
        await commission.checkP1Payee();
        await commission.clickAddPayeesButton();
        await commission.clickSaveButton();
        await commission.waitForTimeout(10000);
        await commission.clickAddComponentButton();
        const currentURL = await commission.getCurrentURL();
        const planId = await commission.getPlanIdFromURL(currentURL);
        console.log("Plan ID: ", planId);
        await commission.clickGetStartedButton();
        await commission.enterComponentName("Simple_Rule");
        await commission.clickNextButton();
        await commission.clickStep2SelectData();
        await commission.selectDataBook("REG_Book");
        await commission.selectDataSheet("Reg_sheet");
        await commission.selectEmailField("email");
        await commission.selectDateField("DateOfSale");
        await commission.clickCreateButton();
        await commission.selectAutoSuggestionItem("amount");
        await commission.enterPercentage("35%");
        await commission.waitForTimeout(5000);
        await commission.waitForSVGVisible();
        await commission.clickSaveComponentButton();
        await commission.waitForComponentSavedMessage(20000);
        await commission.exitCanvas();
      }
    );

    test(
      "Read the Commission Plan",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7944" },
          {
            type: "Description",
            description: "Read the Main Commission Plan created earlier",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description: "The newly Commission Plan should be opened",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commission = new CanvasCommission(page);

        await commission.goToPlans("/plans");
        await commission.clickCommissionPlan("Automation_Commission_Plan");
        await commission.exitCanvas();
      }
    );

    test(
      "Update the Commission Plan",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7944" },
          {
            type: "Description",
            description: "Update the Existing Commission Plan created",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The updates, such as changing the Commission Plan name and adding a new criterion, should work as expected.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commission = new CanvasCommission(page);

        await commission.goToPlans("/plans");
        await commission.clickCommissionPlan("Automation_Commission_Plan");
        await commission.updateCommissionPlan();
        await commission.addComponentAndSave();
        await commission.waitForTimeout(10000);
        await commission.exitCanvas();
      }
    );

    test(
      "Publish a Commission Plan",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7944" },
          {
            type: "Description",
            description: "Publish the created Commission Plan",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "After publishing the Commission Plan, it should be successfully published, and it should contain the 'Published' label.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commission = new CanvasCommission(page);

        await commission.goToPlans("/plans");
        await commission.clickCommissionPlan(
          "Automation_Commission_Plan_edited"
        );
        await commission.publishCommissionPlan();
        await commission.exitCanvas();
      }
    );

    test(
      "Clone a Commission Plan",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7944" },
          {
            type: "Description",
            description:
              "Clone a criteria/component as well as Commission Plan from the already created commission plan and verify if its cloned or not.",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The cloning of criteria as well as the commission plan should be successful as expected.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commission = new CanvasCommission(page);

        await commission.goToPlans("/plans");
        await commission.clickCloneMenu();
        await commission.hoverAndCloneCriteria("Simple_Rule", "Tier_Rule");
        await commission.exitCanvas();
      }
    );

    test(
      "Add an Invalid criteria",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7944" },
          {
            type: "Description",
            description:
              "Add an invalid criteria to the Components of a Drafted Commission plan and verify its addition.",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The user should not be able to add and save invalid criteria to the components of a drafted Commission plan.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commission = new CanvasCommission(page);

        await commission.goToPlans("/plans");
        await commission.clickCommissionPlan(
          "Automation_Commission_Plan_edited_Copy"
        );
        await commission.clickComponentByText("Simple_Rule (Copy)");
        await commission.clearComponentTextbox();
        await commission.waitForTimeout(5000);
        await commission.waitForErrorIconVisible();
        await commission.hoverAndCloneInvalidCriteria("Simple_Rule (Copy)");
        await commission.exitCanvas();
        await commission.ExitFull();
      }
    );

    test(
      "Add SPIFF plan in Commission Canvas",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7944" },
          {
            type: "Description",
            description:
              "Add SPIFF plan in Commission Canvas UI and verify its creation. ",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The creation of the SPIFF Plan should be successful as expected.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commission = new CanvasCommission(page);

        await commission.goToPlans("/plans");
        await commission.fillPlanDetails(
          "Automation_SPIFF_Plan",
          `Jan 1, ${COM_VARIABLE.currentYear}`,
          `Dec 31, ${COM_VARIABLE.currentYear}`,
          "Quarterly"
        );
        await commission.clickBuildPlanButton();
        await commission.clickAddComponentButton();
        await commission.clickGetStartedButton();
        await commission.selectConditional();
        await commission.fillComponentDetails("Simple_Condition");
        await commission.selectDataBook("REG_Book");
        await commission.selectDataSheet("Reg_sheet");
        await commission.selectEmailField("email");
        await commission.selectDateField("DateOfSale");
        await commission.createComponentRule();
        await commission.waitForTimeout(2000);
        await commission.clickSaveButton();
        await commission.clickManagePayees();
        await commission.clickAddPayeesButton();
        await commission.savePayees();
        await commission.exitCanvas();
      }
    );

    test(
      "Create a Settlement Commission plan",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7944" },
          {
            type: "Description",
            description:
              "Create a Settlement Commission plan with valid settlement criteria/components and verify its creation.",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The creation of the Settlement Plan with valid criteria should be successful as expected.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commission = new CanvasCommission(page);

        await commission.goToPlans("/plans");
        await commission.clickBuildPlanButtonSettlement();
        await commission.enterComponentName("Z_Automation_Settlement_Plan");
        await commission.fillDateInput(
          "From",
          `Jan 1, ${COM_VARIABLE.currentYear}`
        );
        await commission.fillDateOutput(
          "To",
          `Jun 30, ${COM_VARIABLE.currentYear}`
        );
        await commission.selectPayoutFrequency("Quarterly");
        await commission.toggleSwitch();
        await commission.selectSettlementDate(
          `Jun 30, ${COM_VARIABLE.currentYear}`,
          "Select Date"
        );
        await commission.clickBuildPlanButton();
        await commission.clickAddComponentButton();
        await commission.checkCheckbox("Import an existing");
        await commission.clickGetStartedButton();
        await commission.clickmodal();
        await commission.addComponentWithvalidCriteria(
          `${COM_VARIABLE.currentYear}`,
          "Automation_Commission_Plan_editedPublished",
          "Simple_Rule"
        );
        await commission.clickNextButton();
        await commission.clickCreateButton();
        await commission.clickAddComponentButton();
        await commission.clickGetStartedButton();
        await commission.checkCheckbox("SettlementChoose this");
        await commission.fillSettlementName("Enter name", "Settlement_Rule");
        await commission.clickNextButton();
        await commission.selectSettlementComponent("Simple_Rule");
        await commission.selectDataBook("REG_Book");
        await commission.selectDataSheet("Reg_sheet");
        await commission.selectTriggerbasedpayouts("DateOfInvoice");
        await commission.selectForCommissioncomponents("id");
        await commission.selectTriggerPayouts("id");
        await commission.clickCreateButton();
        await commission.selectSettlementvalues(
          "Press Ctrl + H for help",
          "True"
        );
        await commission.selectSettlementvalues2(
          "auto-suggestion-view",
          "amount"
        );
        await commission.waitForTimeout(3000);
        await commission.waitForSettlementSuccess();
        await commission.clickSaveButton();
        await commission.waitForComponentSavedMessage(22000);
        await commission.exitCanvas();
        await commission.clickCommissionPlan("Z_Automation_Settlement_Plan");
        await commission.hoverAndCloneInvalidSettlementCriteria(
          "Settlement_Rule"
        );
        await commission.exitCanvas();
        await commission.ExitFull();
      }
    );

    test(
      "Build and Simulate consistency",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7944" },
          {
            type: "Description",
            description:
              "Change the values in the Build and Simulate section and verify the consistency.",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "Build and Simulate should retain the data even after switching components",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commission = new CanvasCommission(page);

        await commission.goToPlans("/plans");
        await commission.clickCommissionPlan(
          "Automation_Commission_Plan_edited_Copy"
        );
        await commission.clickComponent("Simple_Rule");
        await commission.clickSimulateButton();
        await commission.fillEndDate(`Feb 29, ${COM_VARIABLE.currentYear}`);
        await commission.clickSaveButton();
        const Enddate = "(//input[@placeholder='End date'])[1]";
        await commission.waitForComponentSavedMessage(30000);
        await commission.clickComponent("Tier_Rule");
        await commission.clickSimulateButton();
        await commission.assertDateRetained(
          `Feb 29, ${COM_VARIABLE.currentYear}`,
          Enddate
        );
        await commission.clickComponent("Tier_Rule");
        await commission.clickSimulateButton();
        await commission.waitForTimeout(8000);
        const Enddate2 = "(//input[@placeholder='End date'])[1]";
        await commission.assertDateRetained(
          `Feb 29, ${COM_VARIABLE.currentYear}`,
          Enddate2
        );
        await commission.exitCanvas();
      }
    );

    test(
      "Guide section Rule Validation",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7944" },
          {
            type: "Description",
            description:
              "Check if all the rules are present under the Guide's section",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "All rules should be located within the Guide's section of the Component/Criteria",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commission = new CanvasCommission(page);

        await commission.goToPlans("/plans");
        await commission.clickCommissionPlan("Automation_SPIFF_Plan");
        await commission.clickComponentByText("Simple_Condition");
        await commission.clickGuidesButton();
        const Guideelements = await commission.getAllGuideElementsText();
        const expectedList = [
          "AVG",
          "COUNTIF",
          "Concat",
          "Config",
          "Contains",
          "CountNotNull",
          "CurrentPayoutPeriod",
          "DATEDIFF",
          "DistinctCount",
          "Find",
          "GetDate",
          "GetUserProperty",
          "IsEmpty",
          "IsNotEmpty",
          "Lower",
          "MAX",
          "MIN",
          "NotContains",
          "Quota",
          "Quota Erosion",
          "QuotaAttainment",
          "Round",
          "RoundDown",
          "RoundUp",
          "SUM",
          "SUMIF",
          "TEAM-AVG",
          "TEAM-COUNT",
          "TEAM-COUNT-NOT-NULL",
          "TEAM-COUNTIF",
          "TEAM-DISTINCT-COUNT",
          "TEAM-MAX",
          "TEAM-MIN",
          "TEAM-Quota",
          "TEAM-Quota Erosion",
          "TEAM-QuotaAttainment",
          "TEAM-SUM",
          "TEAM-SUMIF",
          "TieredPercentage",
          "TieredValue",
        ];
        const sortedGuideElements = Guideelements.slice().sort();
        const sortedExpectedList = expectedList.slice().sort();
        if (
          Guideelements.length === 40 &&
          sortedGuideElements.every(
            (value, index) => value === sortedExpectedList[index]
          )
        ) {
          console.log("All the Guide elements are present in the section");
        } else {
          console.log("All the Guide cases are not present in the section");
        }
        await commission.exitCanvas();
      }
    );

    test(
      "SPIFF, Active Filters and Sort by Ascending and Descending",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7944" },
          {
            type: "Description",
            description:
              "Check whether the View settings CTA is returning the accurate results after operations such as sorting, filtering by SPIFF plans, and filtering by Active plans.",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "View settings CTA should accurately reflect sorting, SPIFF plans filtering, and Active plans filtering operations.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commission = new CanvasCommission(page);

        await commission.goToPlans("/plans");
        await commission.hoverViewSettings();

        // Toggle Show Spiff Plans
        await commission.toggleShowSpiffPlans();
        await commission.waitForTimeout(10000);
        await commission.clickMainPage();
        const SPIFFfilterresults = await commission.checkTextContent(
          "div.relative div.absolute div.px-2.py-2 div.relative span.text-lg"
        );
        console.log(SPIFFfilterresults);
        expect(SPIFFfilterresults[0]).toBe("Automation_SPIFF_Plan");
        await commission.hoverViewSettings();
        await commission.toggleShowSpiffPlans();
        await commission.waitForTimeout(5000);
        await commission.hoverViewSettings();

        // Toggle Show Active Plans
        await commission.toggleShowActivePlans();
        await commission.waitForTimeout(10000);
        const Activefilterresults = await commission.checkTextContent(
          "div.relative div.absolute div.px-2.py-2 div.relative span.text-lg"
        );
        console.log(Activefilterresults);
        expect(Activefilterresults[0]).toBe(
          "Automation_Commission_Plan_edited"
        );
        await commission.hoverViewSettings();
        await commission.toggleShowActivePlans();
        await commission.waitForTimeout(5000);

        // Sort Descending
        await commission.hoverViewSettings();
        await commission.clickSortAscendingButton();
        await commission.waitForTimeout(10000);
        const Sortfilterresults = await commission.checkTextContent(
          "div.relative div.absolute div.px-2.py-2 div.relative span.text-lg"
        );
        console.log(Sortfilterresults);
        expect(Sortfilterresults[0]).toBe("Z_Automation_Settlement_Plan");

        // Sort Ascending
        await commission.hoverViewSettings();
        await commission.clickSortDescendingButton();
        await commission.waitForTimeout(10000);
        const Sortfilterresults2 = await commission.checkTextContent(
          "div.relative div.absolute div.px-2.py-2 div.relative span.text-lg"
        );
        console.log(Sortfilterresults2);
        expect(Sortfilterresults2[0]).toBe("Automation_Commission_Plan_edited");
      }
    );

    test(
      "Delete the Main Commission Plan",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7944" },
          {
            type: "Description",
            description:
              "Delete the Main Commission Plan using Delete CTA and verify its deletion",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description: "The Commission plan should be deleted successfully.",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commission = new CanvasCommission(page);

        await commission.goToPlans("/plans");
        await commission.clickclonedelete("Z_Automation_Settlement_Plan");
        await commission.ClickCloneMenuDelete();
        await commission.exitCanvas();
        await commission.clickclonedelete("Z_Automation_Settlement_Plan_Copy");
        await commission.clickDeleteMenuItem();
        await commission.clickConfirmDeleteButton();
      }
    );

    test(
      "Add / Remove a Plan Document",
      {
        annotation: [
          { type: "Test ID", description: "INTER-7944" },
          {
            type: "Description",
            description:
              "Add / remove a Plan Document to an existing Commission Plan and check if its getting added / removed",
          },
          { type: "Precondition", description: "None" },
          {
            type: "Expected Behaviour",
            description:
              "The Plan Document should be successfully added / removed",
          },
        ],
      },
      async ({ adminPage }) => {
        const page = adminPage.page;
        const commission = new CanvasCommission(page);

        await commission.goToPlans("/plans");
        await commission.clickCommissionPlan(
          "Automation_Commission_Plan_edited_Copy"
        );
        await commission.clickDraftQuarterlyButton();
        await commission.clickUploadButton();
        await commission.setInputFiles("./upload-files/H1-Sample-Plan_doc.pdf");
        await commission.clickUpdateButton();
        await commission.waitForTimeout(7000);

        await commission.clickRemovePlanDocumentButton();
        await commission.clickUpdateButton();
        await commission.exitCanvas();
      }
    );
  }
);
