import random

from commission_engine.utils.general_data import UpstreamSyncModes


def generate_test_data(  # noqa: PLR0913
    mode,
    file_mtime,
    file_name,
    ids=None,
    deleted_ids=None,
    snapshots=None,
    include_edge_cases=False,
):
    """
    Generate test data records for testing file deduplication.

    Args:
        mode (str): 'snapshot' or 'changes' to determine structure of data
        file_mtime (datetime): Timestamp for the file
        file_name (str): Name of the file
        ids (list): List of IDs to generate data for (if None, no records generated)
        deleted_ids (list): List of IDs to mark as deleted (changes mode)
        snapshots (dict): Dict mapping ID to snapshot value (snapshot mode)
        include_edge_cases (bool): Whether to include edge case records

    Returns:
        list: List of data records for testing
    """
    data = []

    # Generate regular records
    if ids:
        for record_id in ids:
            # Determine snapshot value for snapshot mode
            snapshot_value = None
            if mode == UpstreamSyncModes.SNAPSHOT.value and snapshots:
                snapshot_value = snapshots.get(record_id, f"Q{int(record_id) % 4 + 1}")

            # Determine if record should be marked as deleted
            is_deleted = "false"
            if (
                mode != UpstreamSyncModes.SNAPSHOT.value
                and deleted_ids
                and record_id in deleted_ids
            ):
                is_deleted = "true"

            # Generate base record
            record = {
                "id": record_id,
                "amount": f"{random.uniform(1000, 15000):.2f}",  # noqa
                "name": f"REP{random.randint(1, 20):02d}",  # noqa
                "close_date": f"2025-01-0{random.randint(1, 9)}",  # noqa
            }

            # Add mode-specific fields
            if mode == UpstreamSyncModes.SNAPSHOT.value and snapshot_value:
                record["snapshot_quarter"] = snapshot_value
            elif mode != UpstreamSyncModes.SNAPSHOT.value and is_deleted:
                record["is_deleted"] = is_deleted

            data.append(record)

    # Add edge cases if requested
    if include_edge_cases:
        if mode == UpstreamSyncModes.SNAPSHOT.value:
            # Edge cases for snapshot mode
            data.extend(
                [
                    {
                        "id": "097",
                        "amount": "",  # Empty value
                        "name": f"REP{random.randint(21, 25):02d}",  # noqa
                        "close_date": f"2025-01-0{random.randint(1, 9)}",  # noqa
                        "snapshot_quarter": "Q1",
                    },
                    {
                        "id": "098",
                        "amount": None,  # Null value
                        "name": f"REP{random.randint(21, 25):02d}",  # noqa
                        "close_date": f"2025-01-0{random.randint(1, 9)}",  # noqa
                        "snapshot_quarter": "Q2",
                    },
                    {
                        "id": "099",
                        "amount": "$12,345.67",  # Special characters
                        "name": f"REP{random.randint(21, 25):02d}",  # noqa
                        "close_date": f"2025-01-0{random.randint(1, 9)}",  # noqa
                        "snapshot_quarter": "Q3",
                    },
                    {
                        "id": "101",
                        "amount": "7500.00",
                        "name": f"REP{random.randint(21, 25):02d}",  # noqa
                        "close_date": "January 15th, 2023",  # Unusual date format
                        "snapshot_quarter": "Q1",
                    },
                    {
                        "id": "100",
                        "amount": "5000.00",
                        "name": f"REP{random.randint(21, 25):02d}",  # noqa
                        "close_date": f"2025-01-0{random.randint(1, 9)}",  # noqa
                        "snapshot_quarter": "",  # Empty Snapshot value
                    },
                    {
                        "id": "100",
                        "amount": "5000.00",
                        "name": f"REP{random.randint(21, 25):02d}",  # noqa
                        "close_date": f"2025-01-0{random.randint(1, 9)}",  # noqa
                        "snapshot_quarter": None,  # Null Snapshot value
                    },
                ]
            )
        elif mode != UpstreamSyncModes.SNAPSHOT.value:
            # Edge cases for changes mode
            data.extend(
                [
                    {
                        "id": "097",
                        "amount": "",  # Empty value
                        "name": f"REP{random.randint(21, 25):02d}",  # noqa
                        "close_date": f"2025-01-0{random.randint(1, 9)}",  # noqa
                        "is_deleted": "false",
                    },
                    {
                        "id": "098",
                        "amount": None,  # Null value
                        "name": f"REP{random.randint(21, 25):02d}",  # noqa
                        "close_date": f"2025-01-0{random.randint(1, 9)}",  # noqa
                        "is_deleted": "false",
                    },
                    {
                        "id": "099",
                        "amount": "$12,345.67",  # Special characters
                        "name": f"REP{random.randint(21, 25):02d}",  # noqa
                        "close_date": f"2025-01-0{random.randint(1, 9)}",  # noqa
                        "is_deleted": "false",
                    },
                    {
                        "id": "100",
                        "amount": "5000.00",
                        "name": f"REP{random.randint(21, 25):02d}",  # noqa
                        "close_date": "January 15th, 2023",  # Unusual date format
                        "is_deleted": "false",
                    },
                    {
                        "id": "101",
                        "amount": "7500.00",
                        "name": f"REP{random.randint(21, 25):02d}",  # noqa
                        "close_date": f"2025-01-0{random.randint(1, 9)}",  # noqa
                        "is_deleted": "tRuE",  # Different capitalization of "true" - Deleted irrespective of casing
                    },
                    {
                        "id": "102",
                        "amount": "8250.34",
                        "name": f"REP{random.randint(21, 25):02d}",  # noqa
                        "close_date": f"2025-01-0{random.randint(1, 9)}",  # noqa
                        "is_deleted": "",  # Empty is_deleted value - Inserted
                    },
                ]
            )

    return {"records": data, "file_mtime": file_mtime, "file_name": file_name}


def get_expected_file_records(
    input_files: list[dict], mode: str
) -> tuple[list[dict], list[dict]]:
    """
    Get expected records for a given set of input files and sync mode.
    input_files is a list of dicts, each representing a file.
    Each dict has:
    - 'file_name': str
    - 'file_mtime': datetime
    - 'records': dict
    """
    expected_records_synced = []
    deleted_records = []

    # Start from latest file and work backwards
    sorted_files = sorted(input_files, key=lambda x: x["file_mtime"], reverse=True)
    processed_ids = set()
    processed_snapshots = set()
    for file in sorted_files:
        if mode in [UpstreamSyncModes.CHANGES.value, UpstreamSyncModes.ALL.value]:
            current_file_ids_processed = set()
            for record in file["records"]:
                # Skip if we've already processed this id as latest file record will be considered
                if record["id"] in processed_ids:
                    continue
                # Add to expected result and mark as processed in current file's ids
                if record.get("is_deleted", "false").lower() == "true":
                    deleted_records.append(
                        {
                            **record,
                            "FILE_MTIME": file["file_mtime"],
                            "FILE_NAME": file["file_name"],
                        }
                    )
                else:
                    expected_records_synced.append(
                        {
                            **record,
                            "FILE_MTIME": file["file_mtime"],
                            "FILE_NAME": file["file_name"],
                        }
                    )
                current_file_ids_processed.add(record["id"])
            # If new ids are processed from current file, add to global processed ids
            if current_file_ids_processed:
                processed_ids.update(current_file_ids_processed)
        elif mode == UpstreamSyncModes.SNAPSHOT.value:
            # Store current file's snapshots
            current_file_snapshots_processed = set()
            for record in file["records"]:
                # Skip if we've already processed this snapshot quarter as latest file record will be considered
                if record["snapshot_quarter"] in processed_snapshots:
                    continue
                # Add to expected result and mark as processed in current file's snapshots
                expected_records_synced.append(
                    {
                        **record,
                        "FILE_MTIME": file["file_mtime"],
                        "FILE_NAME": file["file_name"],
                    }
                )
                current_file_snapshots_processed.add(record["snapshot_quarter"])
            # If new snapshot values are processed from current file, add to global processed snapshots
            if current_file_snapshots_processed:
                processed_snapshots.update(current_file_snapshots_processed)
    return expected_records_synced, deleted_records
