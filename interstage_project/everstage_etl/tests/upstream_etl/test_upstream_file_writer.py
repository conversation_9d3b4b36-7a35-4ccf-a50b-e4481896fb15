import json
import uuid
from datetime import datetime, timezone
from unittest.mock import MagicMock, call, patch

import pandas as pd
import pytest

from commission_engine.accessors.etl_config_accessor import ExtractionConfigAccessor
from commission_engine.database.snowflake_connection import (
    create_snowpark_session,
    get_connection,
)
from commission_engine.utils.general_data import UpstreamSyncModes
from everstage_ddd.upstream.extraction.data_lake import UpstreamFileWriter

from .mock_file_data_util import generate_test_data, get_expected_file_records


@pytest.fixture
def mock_key_util():
    """Mock source key fetching"""
    with patch(
        "everstage_ddd.upstream.extraction.data_lake.get_source_key_fields",
        return_value=(
            ["data:id"],
            ["data:snapshot_quarter"],
        ),  # primary_keys, snapshot_keys
    ) as mock_get_keys:
        yield mock_get_keys


@pytest.fixture
def upstream_file_writer(mock_key_util):
    """Creates a UpstreamFileWriter instance for testing with some mocked dependencies."""
    client_id = 1
    integration_id = uuid.uuid4()
    sync_run_id = uuid.uuid4()

    with patch(
        "everstage_ddd.upstream.extraction.data_lake.UpstreamChangesWriter"
    ) as mocked_changes_writer:
        mocked_changes_writer.return_value = MagicMock()
        file_writer = UpstreamFileWriter(
            client_id=client_id, integration_id=integration_id, sync_run_id=sync_run_id
        )
        connection = get_connection(auto_commit=False, client_id=client_id)
        file_writer.connection = connection
        file_writer.snowpark_session = create_snowpark_session(
            client_id=client_id, connection=connection
        )
        yield file_writer


# Changes mode test data
CHANGES_TEST_DATA = {
    "single_file_upload": lambda: [
        generate_test_data(
            mode=UpstreamSyncModes.CHANGES.value,
            file_mtime=datetime(2025, 1, 1, tzinfo=timezone.utc),
            file_name="sales_20250101.csv",
            ids=[f"{i:03d}" for i in range(1, 31)],
        ),
    ],
    "multi_file_upload": lambda: [
        # File 1 - Initial data - 30 records
        generate_test_data(
            mode=UpstreamSyncModes.CHANGES.value,
            file_mtime=datetime(2025, 1, 1, tzinfo=timezone.utc),
            file_name="sales_20250101.csv",
            ids=[f"{i:03d}" for i in range(1, 31)],
        ),
        # File 2 - Updates to 10 records from file 1 and 15 new records
        generate_test_data(
            mode=UpstreamSyncModes.CHANGES.value,
            file_mtime=datetime(2025, 1, 2, tzinfo=timezone.utc),
            file_name="sales_20250102.csv",
            ids=[f"{i:03d}" for i in range(1, 11)]
            + [f"{i:03d}" for i in range(31, 46)],
        ),
        # File 3 - Updates, deletions, and new records
        generate_test_data(
            mode=UpstreamSyncModes.CHANGES.value,
            file_mtime=datetime(2025, 1, 3, tzinfo=timezone.utc),
            file_name="sales_20250103.csv",
            ids=[f"{i:03d}" for i in range(11, 24)]  # Updates and deletions
            + [f"{i:03d}" for i in range(46, 56)],  # New records
            deleted_ids=[f"{i:03d}" for i in range(16, 24)],  # Mark some as deleted
            include_edge_cases=True,
        ),
    ],
}

# Snapshot mode test data - each file uploaded contains data for up to 3 snapshot periods
# with the sync expected to replace existing records for those specific periods
SNAPSHOT_TEST_DATA = {
    # Scenario 1: Single file upload sanity check with data for Q1 and Q2 and some edge cases
    "single_file_upload": lambda: [
        generate_test_data(
            mode=UpstreamSyncModes.SNAPSHOT.value,
            file_mtime=datetime(2023, 1, 1, tzinfo=timezone.utc),
            file_name="sales_20230101.csv",
            ids=[f"{i:03d}" for i in range(1, 41)],
            snapshots={f"{i:03d}": "Q1" if i <= 20 else "Q2" for i in range(1, 41)},
            include_edge_cases=True,
        ),
    ],
    # Scenario 2: Sequential uploads where each file contains different subset of snapshots
    "sequential_snapshots": lambda: [
        # Day 1: Initial upload with data for Q1 and Q2
        generate_test_data(
            mode=UpstreamSyncModes.SNAPSHOT.value,
            file_mtime=datetime(2023, 1, 1, tzinfo=timezone.utc),
            file_name="sales_20230101.csv",
            ids=[f"{i:03d}" for i in range(1, 41)],
            snapshots={f"{i:03d}": "Q1" if i <= 20 else "Q2" for i in range(1, 41)},
        ),
        # Day 2: Upload with data for Q2 and Q3 (Q2 will replace previous Q2 data)
        generate_test_data(
            mode=UpstreamSyncModes.SNAPSHOT.value,
            file_mtime=datetime(2023, 1, 2, tzinfo=timezone.utc),
            file_name="sales_20230102.csv",
            ids=[f"{i:03d}" for i in range(21, 61)],
            snapshots={f"{i:03d}": "Q2" if i <= 40 else "Q3" for i in range(21, 61)},
        ),
        # Day 3: Upload with data for Q3 and Q4 (Q3 will replace previous Q3 data)
        generate_test_data(
            mode=UpstreamSyncModes.SNAPSHOT.value,
            file_mtime=datetime(2023, 1, 3, tzinfo=timezone.utc),
            file_name="sales_20230103.csv",
            ids=[f"{i:03d}" for i in range(41, 81)],
            snapshots={f"{i:03d}": "Q3" if i <= 60 else "Q4" for i in range(41, 81)},
        ),
        # Day 4: Upload with data for Q1 and Q4 (replaces previous Q1 and Q4)
        generate_test_data(
            mode=UpstreamSyncModes.SNAPSHOT.value,
            file_mtime=datetime(2023, 1, 4, tzinfo=timezone.utc),
            file_name="sales_20230104.csv",
            ids=[f"{i:03d}" for i in range(1, 21)]
            + [f"{i:03d}" for i in range(61, 81)],
            snapshots={
                **{f"{i:03d}": "Q1" for i in range(1, 21)},
                **{f"{i:03d}": "Q4" for i in range(61, 81)},
            },
        ),
    ],
    # Scenario 3: Multiple snapshot periods in a single file with overlaps
    "multi_period_per_file": lambda: [
        # Day 1: Initial upload with Q1, Q2, Q3 snapshots
        generate_test_data(
            mode=UpstreamSyncModes.SNAPSHOT.value,
            file_mtime=datetime(2023, 1, 1, tzinfo=timezone.utc),
            file_name="sales_20230101.csv",
            ids=[f"{i:03d}" for i in range(1, 61)],
            snapshots={
                **{f"{i:03d}": "Q1" for i in range(1, 21)},
                **{f"{i:03d}": "Q2" for i in range(21, 41)},
                **{f"{i:03d}": "Q3" for i in range(41, 61)},
            },
        ),
        # Day 2: Upload with Q2, Q3, Q4 snapshots (Q2 and Q3 replace previous ones)
        generate_test_data(
            mode=UpstreamSyncModes.SNAPSHOT.value,
            file_mtime=datetime(2023, 1, 2, tzinfo=timezone.utc),
            file_name="sales_20230102.csv",
            ids=[f"{i:03d}" for i in range(21, 81)],
            snapshots={
                **{f"{i:03d}": "Q2" for i in range(21, 41)},
                **{f"{i:03d}": "Q3" for i in range(41, 61)},
                **{f"{i:03d}": "Q4" for i in range(61, 81)},
            },
        ),
        # Day 3: Upload with just Q1 records (replaces previous Q1)
        generate_test_data(
            mode=UpstreamSyncModes.SNAPSHOT.value,
            file_mtime=datetime(2023, 1, 3, tzinfo=timezone.utc),
            file_name="sales_20230103.csv",
            ids=[f"{i:03d}" for i in range(1, 21)],
            snapshots={f"{i:03d}": "Q1" for i in range(1, 21)},
        ),
    ],
    # Scenario 4: Different record count per snapshot period with some empty periods
    "varied_records_per_snapshot": lambda: [
        # Day 1: Upload with all periods but different record counts
        # IDs 001-015 are Q1, 101-115 are Q2, 201-220 are Q3, 301-320 are Q4
        generate_test_data(
            mode=UpstreamSyncModes.SNAPSHOT.value,
            file_mtime=datetime(2023, 1, 1, tzinfo=timezone.utc),
            file_name="sales_20230101.csv",
            ids=[f"{i:03d}" for i in range(1, 16)]  # 15 IDs for Q1
            + [f"{i:03d}" for i in range(101, 116)]  # 15 IDs for Q2
            + [f"{i:03d}" for i in range(201, 221)]  # 20 IDs for Q3
            + [f"{i:03d}" for i in range(301, 321)],  # 20 IDs for Q4
            snapshots={
                **{f"{i:03d}": "Q1" for i in range(1, 16)},
                **{f"{i:03d}": "Q2" for i in range(101, 116)},
                **{f"{i:03d}": "Q3" for i in range(201, 221)},
                **{f"{i:03d}": "Q4" for i in range(301, 321)},
            },
        ),
        # Day 2: Upload with only Q1, Q3 - partial data for each period plus NEW records for Q1
        # Some existing Q1 records (001-010), NEW Q1 records (016-020), and some Q3 records (201-215)
        generate_test_data(
            mode=UpstreamSyncModes.SNAPSHOT.value,
            file_mtime=datetime(2023, 1, 2, tzinfo=timezone.utc),
            file_name="sales_20230102.csv",
            ids=[
                f"{i:03d}" for i in range(1, 11)
            ]  # 10 IDs for Q1 (subset of day 1 Q1 data)
            + [f"{i:03d}" for i in range(16, 21)]  # 5 NEW IDs for Q1 (not in day 1)
            + [
                f"{i:03d}" for i in range(201, 216)
            ],  # 15 IDs for Q3 (subset of day 1 Q3 data)
            snapshots={
                **{f"{i:03d}": "Q1" for i in range(1, 11)},
                **{f"{i:03d}": "Q1" for i in range(16, 21)},  # NEW Q1 records
                **{f"{i:03d}": "Q3" for i in range(201, 216)},
            },
        ),
        # Day 3: Upload with only Q2 and Q4 - partial data for each period plus NEW records for Q4
        # Some Q2 records (101-106), some Q4 records (301-310), and NEW Q4 records (321-325)
        generate_test_data(
            mode=UpstreamSyncModes.SNAPSHOT.value,
            file_mtime=datetime(2023, 1, 3, tzinfo=timezone.utc),
            file_name="sales_20230103.csv",
            ids=[
                f"{i:03d}" for i in range(101, 107)
            ]  # 6 IDs for Q2 (subset of day 1 Q2 data)
            + [
                f"{i:03d}" for i in range(301, 311)
            ]  # 10 IDs for Q4 (subset of day 1 Q4 data)
            + [f"{i:03d}" for i in range(321, 326)],  # 5 NEW IDs for Q4 (not in day 1)
            snapshots={
                **{f"{i:03d}": "Q2" for i in range(101, 107)},
                **{f"{i:03d}": "Q4" for i in range(301, 311)},
                **{f"{i:03d}": "Q4" for i in range(321, 326)},  # NEW Q4 records
            },
        ),
    ],
}


@pytest.mark.django_db
@pytest.mark.usefixtures("snowflake_setup")
@patch.object(ExtractionConfigAccessor, "get_object_for_integration_id")
@pytest.mark.usefixtures("upstream_file_writer")
class TestUpstreamFileWriter:
    """
    Test Data Extraction logic from file uploads across various scenarios and sync modes
    """

    def collect_table_data(self, snowpark_session, table_name):
        """Helper method to collect data from a table and convert to list of dicts."""
        df = snowpark_session.table(table_name).to_pandas()
        df["DATA"] = df["DATA"].apply(
            lambda json_string: (
                json.loads(json_string) if pd.notna(json_string) else None
            )
        )
        result = df.to_dict(orient="records")
        return result

    @pytest.mark.parametrize(
        "sync_mode, test_id",
        [
            (UpstreamSyncModes.SNAPSHOT.value, "test_empty_input_snapshot"),
            (UpstreamSyncModes.CHANGES.value, "test_empty_input_changes"),
            (UpstreamSyncModes.ALL.value, "test_empty_input_all"),
        ],
    )
    def test_empty_input(
        self,
        mock_extraction_config,
        upstream_file_writer,
        sync_mode,
        test_id,
    ):
        """
        Test empty input handling across different sync modes.
        """
        # Setup
        mock_extraction_config.return_value.sync_type = sync_mode

        # No test data - simulate empty/non-existent input
        upstream_file_writer.set_meta("Test", datetime.now(timezone.utc))
        with patch.object(
            UpstreamFileWriter, "_write_to_upstream_table"
        ) as mock_write_to_upstream_table:
            # Save with empty input
            upstream_file_writer.save()

            # Verify _write_to_upstream_table was not called
            mock_write_to_upstream_table.assert_not_called()

    @pytest.mark.parametrize(
        "sync_mode, test_case",
        [
            (UpstreamSyncModes.CHANGES.value, "single_file_upload"),
            (UpstreamSyncModes.CHANGES.value, "multi_file_upload"),
            (UpstreamSyncModes.ALL.value, "single_file_upload"),
            (UpstreamSyncModes.ALL.value, "multi_file_upload"),
        ],
    )
    def test_changes_all_mode(
        self,
        mock_extraction_config,
        upstream_file_writer,
        sync_mode,
        test_case,
    ):
        """
        Test File Upload Extraction in Changes and All mode
        """
        # Setup sync mode
        mock_extraction_config.return_value.sync_type = sync_mode

        # Generate test data for this case
        test_data = CHANGES_TEST_DATA[test_case]()
        expected_changes, expected_deleted = get_expected_file_records(
            test_data, sync_mode
        )

        snowpark_session = upstream_file_writer.snowpark_session

        for test_file in test_data:
            df = pd.DataFrame(test_file["records"], dtype=str)
            upstream_file_writer.set_meta(
                file_name=test_file["file_name"],
                file_mtime=test_file["file_mtime"],
                delete_field="is_deleted",
                delete_value="true",
            )
            assert not upstream_file_writer.middleware_meta
            upstream_file_writer.save_in_temp_table(df)

        upstream_file_writer.save()

        # Get output from staging tables
        changes_records_table_name = (
            f"{upstream_file_writer.staging_table_name}_changes"
        )
        changes_records = self.collect_table_data(
            snowpark_session, changes_records_table_name
        )

        deleted_records_table_name = (
            f"{upstream_file_writer.staging_table_name}_deletes"
        )
        deleted_records = self.collect_table_data(
            snowpark_session, deleted_records_table_name
        )

        # Compare changes records
        sorted_actual_changes = sorted(changes_records, key=lambda x: str(x["ROW_KEY"]))
        sorted_expected_changes = sorted(expected_changes, key=lambda x: str(x["id"]))

        assert len(sorted_actual_changes) == len(sorted_expected_changes)
        for actual, expected in zip(sorted_actual_changes, sorted_expected_changes):
            assert (
                actual["ROW_KEY"] == expected["id"]
            ), f"Failed {test_case.upper()} - Mismatch in changes records"
            for field, value in actual["DATA"].items():
                if field != "FILE_MTIME":
                    assert (
                        value == expected[field]
                    ), f"Failed {test_case.upper()} - Mismatch in expected value for field {field}"

        # Compare deleted records
        sorted_actual_deleted = sorted(deleted_records, key=lambda x: str(x["ROW_KEY"]))
        sorted_expected_deleted = sorted(expected_deleted, key=lambda x: str(x["id"]))

        assert len(sorted_actual_deleted) == len(sorted_expected_deleted)
        for actual, expected in zip(sorted_actual_deleted, sorted_expected_deleted):
            assert (
                actual["ROW_KEY"] == expected["id"]
            ), f"Failed {test_case.upper()} - Mismatch in changes records"
            for field, value in actual["DATA"].items():
                if field != "FILE_MTIME":
                    assert (
                        value == expected[field]
                    ), f"Failed {test_case.upper()} - Mismatch in expected value for field {field}"

    @pytest.mark.parametrize(
        "sync_mode, test_case",
        [
            (UpstreamSyncModes.SNAPSHOT.value, "single_file_upload"),
            (UpstreamSyncModes.SNAPSHOT.value, "sequential_snapshots"),
            (UpstreamSyncModes.SNAPSHOT.value, "multi_period_per_file"),
            (UpstreamSyncModes.SNAPSHOT.value, "varied_records_per_snapshot"),
        ],
    )
    def test_snapshot_mode(
        self,
        mock_extraction_config,
        upstream_file_writer,
        sync_mode,
        test_case,
    ):
        """
        Test File Upload Extraction in Snapshot mode
        """
        # Setup sync mode
        mock_extraction_config.return_value.sync_type = sync_mode

        # Generate test data for this case
        test_data = SNAPSHOT_TEST_DATA[test_case]()
        expected_result, _ = get_expected_file_records(test_data, sync_mode)

        snowpark_session = upstream_file_writer.snowpark_session

        for test_file in test_data:
            df = pd.DataFrame(test_file["records"], dtype=str)
            upstream_file_writer.set_meta(
                file_name=test_file["file_name"],
                file_mtime=test_file["file_mtime"],
            )
            assert not upstream_file_writer.middleware_meta
            upstream_file_writer.save_in_temp_table(df)

        upstream_file_writer.save()
        assert (
            call(is_deleted=True)
            not in upstream_file_writer.upstream_writer.set_meta.call_args_list
        )

        result = self.collect_table_data(
            snowpark_session, upstream_file_writer.staging_table_name
        )
        sorted_result = sorted(result, key=lambda x: str(x["ROW_KEY"]))
        sorted_expected = sorted(expected_result, key=lambda x: str(x["id"]))

        assert len(sorted_result) == len(expected_result)
        for actual, expected in zip(sorted_result, sorted_expected):
            assert (
                actual["ROW_KEY"] == expected["id"]
            ), f"Failed {test_case.upper()} - Mismatch in changes records"
            for field, value in actual["DATA"].items():
                if field != "FILE_MTIME":
                    assert (
                        value == expected[field]
                    ), f"Failed {test_case.upper()} - Mismatch in expected value for field {field}"
