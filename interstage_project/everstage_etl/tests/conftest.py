import json
import os
import platform
import sys
import time
import uuid
from pathlib import Path

import pytest
from rich import print

from commission_engine.database.snowflake_connection import (
    create_snowpark_session_client_agnostic,
)
from interstage_project.db_setup import (
    close_django_db_setup,
    close_snowflake_db_setup,
    run_django_db_setup,
    run_snowflake_db_setup,
)
from interstage_project.local_db_setup import setup_local_unit_test_db
from interstage_project.snowflake_db_pool_setup import (
    database_clone_name,
    get_snowflake_test_db_from_pool,
)
from scripts.reset_test_snowflake_tables import reset_snowflake_db

conf_path = Path(__file__).parent / "snowflake_local_setup.conf"


def pytest_addoption(parser):
    parser.addoption(
        "--everstage_etl-test-db-name",
        action="store",
        default="unit-test-template-db",
        help="Custom database to run ETL tests",
    )
    parser.addoption(
        "--reset-db",
        action="store_true",
        default=False,
        help="Reset the database to state before running tests",
    )
    parser.addoption(
        "--reset-clients",
        action="store",
        default="",
        help="Clients to reset after test run",
    )
    parser.addoption(
        "--performance-test",
        action="store_true",
        default=False,
        help="run etl performance tests that take a long time",
    )


def pytest_configure(config):
    bucket_name = "everstage-coverage"
    dump_name = "unit-test-template.gz"
    setup_local_unit_test_db(bucket_name, dump_name)
    # initialize a marker "performance" to skip them during normal pytest
    # add @pytest.mark.performance on top of any etl tests that you need to skip
    config.addinivalue_line("markers", "performance: mark test as performance test")
    printenv()


def printenv():
    # Create a colorful banner
    banner = "=" * 30 + "\n[bold cyan]Environment Information[/bold cyan]\n" + "=" * 30
    print(banner)
    print(f"[bold green]Python Version:[/bold green] {sys.version}")
    print(f"[bold green]Operating System:[/bold green] {platform.system()}")
    print(f"[bold green]Current Directory:[/bold green] {os.getcwd()}")


@pytest.fixture(scope="session")
def django_db_setup(request):
    db_name = request.config.getoption("--everstage_etl-test-db-name")
    test_instance_db_name = f"test_instance_db_{str(uuid.uuid4())[:8]}"
    os.environ["DB_NAME"] = test_instance_db_name
    run_django_db_setup(template_db_name=db_name, test_db_name=test_instance_db_name)
    yield
    close_django_db_setup(template_db_name=db_name, test_db_name=test_instance_db_name)


def reset_snowflake_database(test_instance_db_name, reset_clients, conf_data):
    conf_data["LAST_RESET_STATUS"] = "STARTED"
    try:
        reset_snowflake_db(
            test_instance_db_name, reset_clients, conf_data["REFRESH_TIMESTAMP"]
        )
        conf_data["LAST_RESET_STATUS"] = "SUCCESS"
    except Exception as e:
        conf_data["LAST_RESET_STATUS"] = "FAILED"
        raise e
    finally:
        with open(conf_path, "w") as f:
            json.dump(conf_data, f, indent=4)


# Do not reduce scope.("@fixture snowpark_session" will be executed before test session is created)
@pytest.fixture(scope="session")
def snowflake_setup(request):
    db_name = request.config.getoption("--everstage_etl-test-db-name")
    reset_db = request.config.getoption("--reset-db")
    reset_clients = request.config.getoption("--reset-clients")
    if db_name != "unit-test-template-db":
        template_name = "EVERSTAGE_LOCAL"
    else:
        template_name = "ETL_TEST_TEMPLATE"
    if os.getenv("CI_CHECK"):
        template_name = "ETL_TEST_TEMPLATE"

    use_existing_db = False

    if conf_path.exists():
        with open(conf_path, "r") as f:
            conf_data = json.load(f)
        last_refresh_timestamp = time.mktime(
            time.strptime(conf_data["REFRESH_TIMESTAMP"], "%Y-%m-%d %H:%M:%S")
        )
        if (
            conf_data["LAST_RESET_STATUS"] == "SUCCESS"
            and last_refresh_timestamp < time.time() - 1000 * 60 * 60 * 24
        ):
            use_existing_db = True
    else:
        conf_data = {"TEMPLATE_NAME": template_name, "RUNS": []}

    #   Create a test id for the test run
    test_id = f"etl_test_{str(uuid.uuid4())[:8]}".upper()

    if use_existing_db:
        test_instance_db_name = conf_data["SNOWFLAKE_DATABASE"]
        print(
            f"[bold blue] Using existing database: [/bold blue] {test_instance_db_name}"
        )
        print(f"[bold blue] Database: [/bold blue] {test_instance_db_name}")
    else:
        #   Create a default test instance db name
        test_instance_db_name = database_clone_name(prefix="ETL_TEST")
        print(f"[bold blue] Database: [/bold blue] {test_instance_db_name}")
        #   If the pool is not available, create a new database by cloning the template
        print(
            "Error connecting to snowflake database pool. Creating clone on run time."
        )
        run_snowflake_db_setup(
            template_db_name=template_name, test_db_name=test_instance_db_name
        )
        os.environ["SNOWFLAKE_DATABASE"] = conf_data["SNOWFLAKE_DATABASE"] = (
            test_instance_db_name
        )
    # Update refresh timestamp
    conf_data["REFRESH_TIMESTAMP"] = time.strftime("%Y-%m-%d %H:%M:%S")
    conf_data["RUNS"].append(
        {
            "TEST_ID": test_id,
            "TEST_INSTANCE_DB_NAME": test_instance_db_name,
            "RESET_DB": reset_db,
            "RESET_CLIENTS": reset_clients,
            "RUN_TIME": conf_data["REFRESH_TIMESTAMP"],
        }
    )
    # Save updated config
    conf_path.parent.mkdir(parents=True, exist_ok=True)
    with open(conf_path, "w") as f:
        json.dump(conf_data, f, indent=4)
    yield
    if reset_db:
        reset_snowflake_db(test_instance_db_name, reset_clients, conf_data)
    else:
        close_snowflake_db_setup(test_db_name=test_instance_db_name)


@pytest.fixture(scope="module")
def snowpark_session():
    return create_snowpark_session_client_agnostic()


@pytest.fixture()
def performance_check(request):
    """
    Add this fixture to any function and supply the time to be taken as seconds to this function

    Add the following annotations on any function:
    @pytest.mark.parametrize("performance_check", [<time_to_be_taken>], indirect=["performance_check"])
    @pytest.mark.usefixtures("performance_check")

    Eg: refer spm/tests/test_data_import_services.py::test_create_update_performance
    """
    start_time = time.time()
    yield
    end_time = time.time()
    total_time = end_time - start_time
    assert total_time < request.param


def pytest_collection_modifyitems(config, items):
    """
    if --performance-test marker is provided, run only the performance test
    else run all other tests
    """
    skip_test_marker = pytest.mark.skip()
    if config.getoption("--performance-test"):
        return
    for item in items:
        if "performance" in item.keywords:
            item.add_marker(skip_test_marker)
