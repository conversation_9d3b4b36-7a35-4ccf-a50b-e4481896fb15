from everstage_ddd.stormbreaker.types import QuerySpecType, SimulationCustomFilterType


class EverstageQuerySpecBuilder:
    def __init__(self):
        self.query_spec = QuerySpecType()
        self.log_context = {}

    def get_query_spec(self) -> QuerySpecType:
        return self.query_spec

    def filter(self, column, operator, value):
        self.query_spec.filters.append(
            (
                column,
                operator,
                value,
            )
        )
        return self

    def distinct_values(self):
        self.query_spec.distinct_values = True
        return self

    def reset_distinct_values(self):
        self.query_spec.distinct_values = False
        return self

    def set_simulation_custom_filters(
        self, filter_id, sorted_col, sorted_col_value, row_key_value
    ):
        # TODO: These explicit filters must be replaced when stormbreaker supports OR and Nested filters
        custom_filters = SimulationCustomFilterType(
            filter_id=filter_id,
            sorted_col=sorted_col,
            sorted_col_value=sorted_col_value,
            row_key_value=row_key_value,
        )
        self.query_spec.simulation_custom_filters = custom_filters
        return self

    def reset_simulation_custom_filters(self):
        self.query_spec.simulation_custom_filters = None
        return self

    def reset_filter(self):
        self.query_spec.filters = []
        return self

    def reset_last_filter(self):
        if self.query_spec.filters:
            self.query_spec.filters.pop()
        return self

    def group_by(self, columns_to_groupby: list):
        self.query_spec.group_bys.extend(columns_to_groupby)
        return self

    def reset_group_by(self):
        self.query_spec.group_bys = []
        return self

    def select(self, columns_to_select: list):
        self.query_spec.selects.extend(columns_to_select)
        return self

    def reset_select(self):
        self.query_spec.selects = []
        return self

    def order_by(self, order_details: list):
        self.query_spec.order_bys.extend(order_details)
        return self

    def reset_order_by(self):
        self.query_spec.order_bys = []
        return self

    def reset_query_spec(self):
        self.query_spec = QuerySpecType(
            selects=[],
            filters=[],
            group_bys=[],
            having_bys=[],
            order_bys=[],
            infix_filters=[],
            distinct_values=False,
            simulation_custom_filters=None,
            case_insensitive_infix_filters=False,
        )
        return self

    def apply_infix_filter(self, infix: list):
        # TODO - @sukanya/@sriramtr - Move out the relative import
        from everstage_ddd.datasheet.data_models import InfixV1ListModel

        if infix:
            self.query_spec.infix_filters.append(InfixV1ListModel(infix))
        return self

    def set_date_trunc_precision(self, date_trunc_precision: str):
        self.query_spec.date_trunc_precision = date_trunc_precision
        return self

    def reset_infix_filters(
        self,
    ):
        self.query_spec.infix_filters = []
        return self

    def set_case_insensitive_infix_filters(self):
        self.query_spec.case_insensitive_infix_filters = True
        return self

    def reset_case_insensitive_infix_filters(self):
        self.query_spec.case_insensitive_infix_filters = False
        return self
