import logging
import math

import numpy as np
from vectorized_evaluate import vectorized_evaluate

from commission_engine.accessors.client_accessor import get_client_settings
from commission_engine.utils import create_ast
from everstage_ddd.cpq.price_book import (
    PriceBookDatasheetTable,
    PriceBookDatasheetTableParams,
)
from everstage_ddd.cpq.quote import get_data_for_rules
from everstage_ddd.cpq.quote.utils import get_table_variables

logger = logging.getLogger(__name__)


def prepare_quote_line_item_data(client_id, quote_id):
    from everstage_ddd.cpq.quote import QuoteSelector

    client_settings = get_client_settings(client_id)
    primary_sku = client_settings.get("cpq_settings", {}).get("primary_sku")
    quote_line_items = get_data_for_rules(client_id, quote_id)

    quote_selector = QuoteSelector(client_id=client_id)
    quote_data = quote_selector.get_quote_by_id(quote_id)
    quote_duration = quote_data.duration_value
    quote_duration_type = quote_data.duration_type

    if quote_duration_type == "years":
        quote_duration = quote_duration * 12
    elif quote_duration_type == "forever":
        quote_duration = None

    sku_quote_dict = {}
    for quote_line_item in quote_line_items:
        sku = quote_line_item["sku"]
        quantity = quote_line_item["quantity"]
        list_unit_price = quote_line_item["list_unit_price"]
        net_unit_price = quote_line_item["net_unit_price"]
        prorated_net_total = quote_line_item["prorated_net_total"]
        discount_percent = quote_line_item["discount_percent"]
        if sku not in sku_quote_dict:
            sku_quote_dict[sku] = quote_line_item
            sku_quote_dict[sku]["quote_duration"] = quote_duration
            sku_quote_dict[sku]["tier_qty"] = {}
        else:
            qli = sku_quote_dict[sku]
            qli["quantity"] = max(qli["quantity"], quantity)
            qli["list_unit_price"] = min(qli["list_unit_price"], list_unit_price)
            qli["net_unit_price"] = min(qli["net_unit_price"], net_unit_price)
            qli["prorated_net_total"] += prorated_net_total
            qli["discount_percent"] = max(qli["discount_percent"], discount_percent)
            sku_quote_dict[sku] = qli

        if sku == primary_sku:
            qty = quote_line_item.get("quantity", 0)
            tier_data = quote_line_item["pricepoint_data"].get("tier_data", [])
            if tier_data:
                last_tier = tier_data[-1]
                lower_bound = last_tier.get("lower_bound", 0)
                if lower_bound > qty:
                    sku_quote_dict[sku]["tier_qty"][qty] = {
                        "bound": lower_bound,
                        "price": last_tier.get("list_price", 0),
                    }
    return sku_quote_dict


def get_approval_matrix_data(client_id, matrix_sheet_id, matrix_book_id):
    pricebook_variables = get_table_variables(client_id, matrix_sheet_id)
    # TODO:@shankar use separate class for this instead of PriceBookDatasheetTable
    matrix = PriceBookDatasheetTable(
        params=PriceBookDatasheetTableParams(
            client_id=client_id,
            table_columns=pricebook_variables,
            primary_key_column="row_key",
            unique_table_id=matrix_sheet_id,
            unique_schema_id=matrix_book_id,
        ),
    )
    records = matrix.get_table_df(use_semantic_tag=True)
    return records.execute().to_dict(orient="records")


# ruff: noqa: PLR0912
def prepare_qli_approval_rule_data(
    matrix_data,
    quote_line_items,
    qli_field_data,
    **kwargs,
):
    logger.info("BEGIN: Evaluate QLI approval rule for - %s", qli_field_data["sku"])
    qty = kwargs.get("bound")
    price = kwargs.get("price")
    primary_sku = kwargs.get("primary_sku")
    field_value = ""
    primary_qli = quote_line_items.get(primary_sku)
    if not primary_qli:
        logger.info("No primary qli found for sku - %s", primary_sku)
        return "No Approval"
    sku = qli_field_data["sku"]
    price_column = qli_field_data["price_column"]
    price_column_value = (
        price if price else quote_line_items.get(sku, {}).get(price_column, math.inf)
    )
    logger.info("SKU - %s", sku)
    if sku != primary_sku:
        req_matrix = [data for data in matrix_data if data["sku"] == sku]
    else:
        pricing_method = primary_qli["pricepoint_data"].get("pricing_method")
        logger.info("Pricing method of primary qli - %s", pricing_method)
        if price_column != "quote_duration" and not pricing_method:
            logger.info("No pricing method found for sku - %s", sku)
            return "No Approval"
        pricing_method = pricing_method or "Tiered"
        req_matrix = []
        for data in matrix_data:
            # logger.info("Pricing method from matrix - %s", data["pricing_method"])
            if data["sku"] == sku and (
                (
                    price_column != "quote_duration"
                    and data["pricing_method"] == pricing_method
                )
                or (
                    price_column == "quote_duration"
                    and data["pricing_method"] == pricing_method
                )
            ):
                req_matrix.append(data)

    req_matrix.sort(key=lambda x: x["lower_bound"])

    if not req_matrix:
        logger.info("No matrix data found for sku - %s", sku)
        return "No Approval"

    qty = primary_qli["quantity"]
    selected_row = None
    for data in req_matrix:
        data["lower_bound"] = int(data["lower_bound"])
        data["upper_bound"] = (
            int(data["upper_bound"])
            if not np.isnan(data["upper_bound"])
            else float("inf")
        )
        if data["lower_bound"] <= qty <= data["upper_bound"]:
            selected_row = data
            break
    if not selected_row:
        logger.info("No matching row found for sku - %s", sku)
        selected_row = req_matrix[-1]
    matrix_column = qli_field_data["matrix_column"]
    if selected_row[matrix_column]:
        if (
            not np.isnan(selected_row[matrix_column])
            and (price_column_value is not None)
            and float(price_column_value) < float(selected_row[matrix_column])
        ):
            field_value = selected_row["quantity_range"]
        else:
            logger.info("No Approval as price is greater than threshold")
            field_value = "No Approval"
    else:
        logger.info("No threshold found for sku - %s", sku)
        field_value = "No Approval"
    logger.info("END: Evaluate QLI approval rule")
    logger.info(f"END: Evaluate QLI approval rule for - {qli_field_data['sku']} ")
    return field_value


def get_hidden_field_value(
    qli_rule_fields, matrix_data, quote_line_items, fields_df, **kwargs
):
    bound = kwargs.get("bound")
    price = kwargs.get("price")
    primary_sku = kwargs.get("primary_sku")
    for qli_rule_field, qli_field_data in qli_rule_fields.items():
        logger.info("Evaluate QLI approval rule for field - %s", qli_rule_field)
        field_value = prepare_qli_approval_rule_data(
            matrix_data,
            quote_line_items,
            qli_field_data,
            bound=bound,
            price=price,
            primary_sku=primary_sku,
        )
        logger.info(
            "Evaluate QLI approval rule for field- %s - %s", qli_rule_field, field_value
        )
        fields_df[qli_rule_field] = field_value
    return fields_df


def evaluate_qli_approval_rules(
    client_id, rule, fields_df, matrix_data, quote_line_items
):
    logger.info("BEGIN: Evaluate QLI approval rule")
    client_settings = get_client_settings(client_id)
    primary_sku = client_settings.get("cpq_settings", {}).get("primary_sku")
    qli_rule_fields = rule["qli_rule_fields"]
    if rule.get("is_show_all_tiers"):
        response = []
        for tier_data in (
            quote_line_items.get(primary_sku, {}).get("tier_qty", {}).values()
        ):
            bound = tier_data["bound"]
            price = tier_data["price"]
            fields_df = get_hidden_field_value(
                qli_rule_fields,
                matrix_data,
                quote_line_items,
                fields_df,
                bound=bound,
                price=price,
                primary_sku=primary_sku,
            )
            res = evaluate_rule(rule, fields_df)
            response.append(res)
        return any(response), fields_df

    fields_df = get_hidden_field_value(
        qli_rule_fields,
        matrix_data,
        quote_line_items,
        fields_df,
        primary_sku=primary_sku,
    )

    res = evaluate_rule(rule, fields_df)
    return res, fields_df


def evaluate_rule(rule, fields_df):
    logger.info("Evaluating qli rule")
    actions = rule["actions"]
    for action in actions:
        if "condition" in action:
            condition = action["condition"]
            ast = create_ast(condition)
            res = vectorized_evaluate(fields_df, ast["ast"], {})
            return res.any()
    return False
