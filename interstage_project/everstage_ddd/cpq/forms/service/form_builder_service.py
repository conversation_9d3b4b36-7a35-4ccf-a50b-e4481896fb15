import logging
import re
from copy import deepcopy

from django.db import transaction
from django.forms.models import model_to_dict
from django.utils import timezone

from everstage_ddd.cpq.forms.accessors.form_accessor import (
    AutoSaveFormAccessor,
    EverstageFormAccessor,
)
from everstage_ddd.cpq.forms.accessors.form_spec_selector import FormSpecSelector
from everstage_ddd.cpq.quote.quote_status.enums import QuoteStatusEnum
from everstage_ddd.cpq.quote.quote_status.quote_status_selector import (
    QuoteStatusSelector,
)
from everstage_ddd.cpq.quote.selectors.quote_selector import QuoteSelector

from ..accessors.form_accessor import FormBuilderAccessor
from .form_spec_navigator import FormSpecNavigator

logger = logging.getLogger(__name__)


def get_all_forms_builders(client_id: int, **kwargs: dict) -> list[dict]:
    """
    Get all form builders for a client.

    Args:
        client_id (int): The client id.
        search_term (str): The search term.
        limit_value (int): The limit value.
        page_number (int): The page number.

    Returns:
        list[dict]: The list of form builders.

    Example:
        get_all_forms_builders(1, search_term="test", limit_value=10, page_number=1)

        [
            {
                "form_builder_id": "1234",
                "form_builder_name": "Test Form Builder",
                "form_builder_description": "Test Form Builder Description",
                "status": "active"
            },
            {
                "form_builder_id": "1235",
                "form_builder_name": "Test Form Builder 2",
                "form_builder_description": "Test Form Builder Description 2",
                "status": "inactive"
        ]
    """
    logger.info("BEGIN: Get all form builders for clinet_id: %s", client_id)
    search_term = kwargs.get("search_term")
    limit = kwargs.get("limit_value", 100)
    page_number = kwargs.get("page_number", 1)
    status = kwargs.get("status")
    offset = (page_number - 1) * limit
    form_builders = FormBuilderAccessor(client_id).get_all_form_builders(
        search_term=search_term, limit=limit, offset=offset, status=status
    )
    logger.info("END: Get all form builders for client_id: %s", client_id)
    return form_builders


def _get_all_form_builder_names(client_id: int) -> list[str]:
    """
    Get all form builder names for a given client.

    Args:
        client_id (int): The ID of the client.

    Returns:
        list[str]: A list of form builder names in lowercase.

    Example:
        _get_all_form_builder_names(1)
        >>> ["test form builder", "sample form", "quotation form"]
    """
    # Fetch all form builders for the client and extract names
    form_builders = FormBuilderAccessor(client_id).get_all_form_builders()
    # Convert names to lowercase for case-insensitive comparison
    all_form_builder_names = [
        form_builder["form_builder_name"].lower() for form_builder in form_builders
    ]
    return all_form_builder_names


def _get_new_form_builder_name(client_id: int, form_builder_name: str) -> str:
    """
    Generate a unique name for a form builder by appending 'Copy(n)' if name already exists.

    Args:
        client_id (int): The ID of the client.
        form_builder_name (str): The original form builder name.

    Returns:
        str: A unique form builder name.

    Example:
        _get_new_form_builder_name(1, "Sample Form")
        >>> "Sample Form Copy(1)"  # if "Sample Form" already exists
        >>> "Sample Form Copy(2)"  # if "Sample Form Copy(1)" also exists
    """
    logger.info("BEGIN: Get new form builder name for client_id: %s", client_id)
    all_form_builder_names = _get_all_form_builder_names(client_id)
    # Regex to match the pattern 'Copy(<number>)' at the end of the quote name
    copy_pattern = re.compile(r"(.*?)( Copy\((\d+)\))?$")

    # Match the quote name to see if it already contains a 'Copy <number>'
    match = copy_pattern.match(form_builder_name)
    if match:
        base_name = match.group(1)  # Original quote name without 'Copy <number>'
        copy_number = (
            int(match.group(3)) if match.group(3) else 0
        )  # Existing copy number

    new_form_builder_name = form_builder_name
    counter = (
        copy_number + 1
    )  # Start incrementing from the next number if it already exists
    while new_form_builder_name.lower() in all_form_builder_names:
        new_form_builder_name = f"{base_name} Copy({counter})"
        counter += 1
    return new_form_builder_name


def _get_deafult_form_builder_name(form_builder_names: list[str]) -> str:
    """
    Generate a default name for a new form builder in format 'Form_n'.

    Args:
        form_builder_names (list[str]): List of existing form builder names.

    Returns:
        str: A new default form builder name.

    Example:
        _get_deafult_form_builder_name(["Form_1", "Form_2"])
        >>> "Form_3"
    """
    # Find the highest number in existing "untitled form(n)" names
    # If no such names exist, start with 1
    untitled_count = 1 + max(
        [
            int(name.split("(")[-1].split(")")[0])
            for name in form_builder_names
            if name.startswith("untitled form(")
        ],
        default=0,
    )
    return f"Untitled Form({untitled_count})"


def _get_default_form_builder_data(client_id: int) -> dict:
    """
    Get default data structure for a new form builder.

    Args:
        client_id (int): The ID of the client.

    Returns:
        dict: Default form builder data with structure:
            {
                "form_builder_name": str,
                "form_builder_description": str,
                "status": str,
                "form_spec": dict
            }

    Example:
        _get_default_form_builder_data(1)
        >>> {
            "form_builder_name": "Form_1",
            "form_builder_description": "New Form Builder Description",
            "status": "inactive",
            "form_spec": {...}
        }
    """
    from everstage_ddd.cpq.forms.everstage_form import default_form

    return {
        "form_builder_name": _get_deafult_form_builder_name(
            _get_all_form_builder_names(client_id)
        ),
        "form_builder_description": "",
        "status": "inactive",
        "form_spec": default_form,
    }


@transaction.atomic
def clone_form_builder_service(
    client_id: int, logged_in_user: str, **kwargs: dict
) -> dict:
    """
    Clone an existing form builder with a new name.

    Args:
        client_id (int): The ID of the client.
        logged_in_user (str): Username of the user performing the action.
        **kwargs: Additional arguments containing:
            - form_builder_id (str): ID of the form builder to clone.

    Returns:
        dict: Cloned form builder data or empty dict if source not found.

    Example:
        clone_form_builder_service(1, "john.doe", form_builder_id="abc123")
        >>> {
            "form_builder_id": "xyz789",
            "form_builder_name": "Original Name Copy(1)",
            "form_builder_description": "Original Description",
            "status": "inactive",
            "created_by": "john.doe",
            "created_at": "2024-01-01T00:00:00Z"
        }
    """
    form_builder_id = kwargs.get("form_builder_id")
    logger.info("BEGIN: Clone form builder for client_id: %s", client_id)

    form_builder = FormBuilderAccessor(client_id).get_form_builder(
        form_builder_id=form_builder_id
    )
    if not form_builder:
        logger.error("Form builder not found for client_id: %s", client_id)
        return {}

    cloned_form_builder = {
        "form_builder_name": _get_new_form_builder_name(
            client_id, form_builder.form_builder_name
        ),
        "form_builder_description": form_builder.form_builder_description,
        "status": "inactive",
        "form_spec": form_builder.form_spec,
        "created_by": logged_in_user,
        "created_at": timezone.now(),
        "knowledge_begin_date": timezone.now(),
    }
    cloned_form_builder = FormBuilderAccessor(client_id).insert_object(
        cloned_form_builder
    )

    obj_dict = model_to_dict(cloned_form_builder)
    obj_dict["source_fields"] = get_source_fields(
        client_id, form_builder.form_builder_id
    )
    obj_dict["default_fields"] = _get_default_fields()
    logger.info("END: Clone form builder for client_id: %s", client_id)

    return obj_dict


@transaction.atomic
def create_form_builder_service(client_id: int, logged_in_user: str, **kwargs) -> dict:
    """
    Create a new form builder with default values.

    Args:
        client_id (int): The ID of the client.
        logged_in_user (str): Username of the user creating the form builder.

    Returns:
        dict: Newly created form builder data.

    Example:
        create_form_builder_service(1, "john.doe")
        >>> {
            "form_builder_id": "abc123",
            "form_builder_name": "Form_1",
            "form_builder_description": "New Form Builder Description",
            "status": "inactive",
            "created_by": "john.doe",
            "created_at": "2024-01-01T00:00:00Z"
        }
    """
    logger.info("BEGIN: Create form builder for client_id: %s", client_id)
    form_builder_name = kwargs.get("form_builder_name")
    form_builder_description = kwargs.get("form_builder_description")
    default_form_builder_data = _get_default_form_builder_data(client_id)
    if form_builder_name:
        default_form_builder_data["form_builder_name"] = form_builder_name
    if form_builder_description:
        default_form_builder_data["form_builder_description"] = form_builder_description
    form_builder = {
        "form_builder_name": default_form_builder_data["form_builder_name"],
        "form_builder_description": default_form_builder_data[
            "form_builder_description"
        ],
        "status": default_form_builder_data["status"],
        "form_spec": default_form_builder_data["form_spec"],
        "created_by": logged_in_user,
        "created_at": timezone.now(),
        "knowledge_begin_date": timezone.now(),
    }
    form_builder = FormBuilderAccessor(client_id).insert_object(form_builder)

    logger.info("END: Create form builder for client_id: %s", client_id)
    form_builder_dict = model_to_dict(form_builder)
    form_builder_dict["source_fields"] = get_source_fields(
        client_id, form_builder.form_builder_id
    )
    form_builder_dict["default_fields"] = _get_default_fields()
    return form_builder_dict


@transaction.atomic
def delete_form_builder_service(client_id: int, **kwargs: dict) -> dict:
    """
    Delete a form builder by ID.

    Args:
        client_id (int): The ID of the client.
        **kwargs: Additional arguments containing:
            - form_builder_id (str): ID of the form builder to delete.

    Returns:
        dict: Status message indicating success or failure.

    Example:
        delete_form_builder_service(1, form_builder_id="abc123")
        >>> {
            "status": "success",
            "message": "Form builder deleted successfully"
        }
    """
    form_builder_id = kwargs.get("form_builder_id")
    logger.info("BEGIN: Delete form builder for client_id: %s", client_id)

    # Check if form builder exists
    form_builder = FormBuilderAccessor(client_id).get_form_builder(form_builder_id)
    if not form_builder:
        logger.error(
            "Form builder not found for client_id: %s, form_builder_id: %s",
            client_id,
            form_builder_id,
        )
        return {"status": "error", "message": "Form builder not found"}

    FormBuilderAccessor(client_id).delete_object(form_builder_id)
    logger.info("END: Delete form builder for client_id: %s", client_id)
    return {"status": "success", "message": "Form builder deleted successfully"}


def _get_default_fields():
    return [
        "field1",
        "field2",
        "field3",
        "field4",
        "field5",
        "field6",
        "field7",
        "field8",
        "field9",
        "field10",
        "field11",
    ]


def get_form_builder_by_id_service(client_id: int, form_builder_id: str) -> dict:
    """
    Retrieve a form builder by its ID.

    Args:
        client_id (int): The client identifier.
        form_builder_id (str): The unique identifier of the form builder.

    Returns:
        dict: Form builder details with structure:
            {
                "form_builder_id": str,
                "form_builder_name": str,
                "form_builder_description": str,
                "form_spec": dict,
                "created_at": datetime,
                "created_by": str,
                "updated_by": str,
                "status": str
            }

    Example:
        get_form_builder_by_id_service(1, "abc123")
        >>> {
            "form_builder_id": "abc123",
            "form_builder_name": "Sample Form",
            "form_builder_description": "A sample form builder",
            "form_spec": {...},
            "created_at": "2024-01-01T00:00:00Z",
            "created_by": "john.doe",
            "updated_by": "jane.doe",
            "status": "active"
        }
    """
    logger.info("BEGIN: Get form builder by ID for client_id: %s", client_id)
    form_builder = FormBuilderAccessor(client_id).get_form_builder(form_builder_id)

    if not form_builder:
        logger.error(
            "Form builder not found for client_id: %s, form_builder_id: %s",
            client_id,
            form_builder_id,
        )
        return {"status": "error", "message": "Form builder not found"}

    logger.info("END: Get form builder by ID for client_id: %s", client_id)
    return {
        "status": "success",
        "message": "Form builder fetched successfully",
        "form_builder": {
            "form_builder_id": form_builder.form_builder_id,
            "form_builder_name": form_builder.form_builder_name,
            "form_builder_description": form_builder.form_builder_description,
            "form_spec": form_builder.form_spec,
            "created_at": form_builder.created_at,
            "created_by": form_builder.created_by,
            "updated_by": form_builder.updated_by,
            "status": form_builder.status,
            "source_fields": get_source_fields(client_id, form_builder.form_builder_id),
            "default_fields": _get_default_fields(),
        },
    }


@transaction.atomic
def update_form_builder_service(
    client_id: int, logged_in_user: str, **kwargs: dict
) -> dict:
    """
    Update an existing form builder.

    Args:
        client_id (int): The ID of the client.
        logged_in_user (str): Username of the user updating the form builder.
        **kwargs: Additional arguments containing:
            - form_builder_id (str): ID of the form builder to update.
            - updated_fields (dict): Fields to update and their new values.

    Returns:
        dict: Status message indicating success or failure.

    Example:
        update_form_builder_service(1, "john.doe",
            form_builder_id="abc123",
            updated_fields={
                "form_builder_name": "Updated Name",
                "status": "active"
            }
        )
        >>> {
            "message": "Form builder updated successfully",
            "status": "success"
        }
    """
    form_builder_id = kwargs.get("form_builder_id")
    updated_fields = kwargs.get("updated_fields")
    logger.info("BEGIN: Update form builder for client_id: %s", client_id)

    # Fetch existing form builder
    form_builder = FormBuilderAccessor(client_id).get_form_builder(
        form_builder_id=form_builder_id
    )
    if not form_builder:
        logger.error("Form builder not found for client_id: %s", client_id)
        return {}
    if not updated_fields:
        logger.error("No fields to update for client_id: %s", client_id)
        return {}

    # Create a deep copy to avoid modifying the original object
    updated_form_builder = deepcopy(form_builder)
    # Set audit fields
    updated_form_builder.knowledge_begin_date = timezone.now()
    updated_form_builder.updated_by = logged_in_user

    # Update fields with new values
    for key, value in updated_fields.items():
        if value is not None or key == "form_builder_description":
            setattr(updated_form_builder, key, value)

    # Invalidate the old version
    FormBuilderAccessor(client_id).invalidate_object(form_builder_id)
    form_navigator = FormSpecNavigator(updated_form_builder.form_spec)
    updated_form_spec = form_navigator.convert_lookup_filters_to_camel_case()

    # Prepare new values for insertion
    new_values = {
        "form_builder_id": updated_form_builder.form_builder_id,
        "form_builder_name": updated_form_builder.form_builder_name,
        "form_builder_description": updated_form_builder.form_builder_description,
        "form_spec": updated_form_spec,
        "status": updated_form_builder.status,
        "created_by": updated_form_builder.created_by,
        "updated_by": updated_form_builder.updated_by,
        "created_at": updated_form_builder.created_at,
        "knowledge_begin_date": updated_form_builder.knowledge_begin_date,
    }

    # Insert the new version
    form_builder = FormBuilderAccessor(client_id).insert_object(new_values)

    logger.info("END: Update form builder for client_id: %s", client_id)
    return {
        "message": "Form builder updated successfully",
        "status": "success",
    }


def get_form_spec_by_quote_id(client_id: int, quote_id: str) -> dict:
    """
    Get the form spec by quote id.
    """
    from everstage_ddd.cpq.forms import prepare_form_spec

    quote_selector = QuoteSelector(client_id=client_id)
    quote = quote_selector.get_quote_by_id(quote_id=quote_id)

    quote_status_selector = QuoteStatusSelector(client_id=client_id)
    quote_status = quote_status_selector.get_quote_status(quote_id=quote_id)

    if quote_status and quote_status.status == QuoteStatusEnum.DRAFT.value:
        auto_save_form = AutoSaveFormAccessor(client_id).get_last_saved_form(
            quote.form_id, projection=["form_data", "knowledge_begin_date"]
        )
        auto_save_form_data = auto_save_form["form_data"]
        form_spec_change = FormSpecSelector(client_id).get_form_spec_change_by_id(
            quote.form_id, projection=["form_spec"]
        )
        form_spec = form_spec_change["form_spec"]
        form_spec = prepare_form_spec(auto_save_form_data, form_spec)
    else:
        everstage_form = EverstageFormAccessor(client_id).get_object(quote.form_id)
        form_spec = everstage_form.form_spec
    return form_spec


def get_source_fields(client_id: int, form_builder_id: str):
    """
    Get the source fields for a form builder.
    """
    # TODO: Implement this in form rules PR - #10598
    return []


def delete_field_service(client_id: int, form_builder_id: str, field_id: str):
    """
    Delete a field from a form builder.
    """

    source_fields = get_source_fields(client_id, form_builder_id)
    if field_id not in source_fields:
        return {"status": "success", "message": "Field can be deleted"}
    return {"status": "error", "message": "Field cannot be deleted"}
