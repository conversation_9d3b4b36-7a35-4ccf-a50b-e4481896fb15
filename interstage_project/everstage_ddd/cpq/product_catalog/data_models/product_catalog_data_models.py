"""
Data models for price book
"""

from datetime import datetime
from typing import Any, Dict, List, Literal, Optional

from pydantic import UUID4, BaseModel

from common.ever_table import EverTableCommonParams, IbisBackend


class ProductCatalogDatasheetTableParams(EverTableCommonParams):
    unique_table_id: str
    unique_schema_id: str | None = None
    backend_type: IbisBackend = IbisBackend.PARQUET


class ProductCatalogTableParams(EverTableCommonParams):
    backend_type: IbisBackend = IbisBackend.POSTGRES


class Product(BaseModel):
    """
    Represents a product entity.
    """

    product_id: UUID4
    name: str
    description: str
    status: str
    created_by: str
    created_at: datetime
    price_factors: Dict[str, List[str]]
    additional_details: Dict[str, Any]


class ProductDetails(BaseModel):
    """
    Represents the details of a product.
    """

    product_id: UUID4
    name: str
    sku: str
    billing_type: str
    category: str
    description: str | None = None
    charge_unit: str | None = None
    status: str
    schedule_start_date: datetime | None = None
    created_on: datetime
    last_modified: datetime
    active_till: datetime | None = None
    # managed_by: str | None = None
    # price_points: List[Dict[str, Any]]


class CreateProductRequest(BaseModel):
    """
    Request model for creating a new product.
    """

    client_id: int
    name: str
    description: str
    price_factors: Dict[str, List[str]]
    created_by: str
    audit_data: Dict[str, Any]


class CreateProductResponse(BaseModel):
    """
    Response model for creating a new product.
    """

    success: bool
    message: str
    product_id: UUID4 | None = None


class GetProductResponse(BaseModel):
    """
    Response model for retrieving a product.
    """

    product_id: UUID4
    name: str
    status: str
    description: str
    created_by: str
    created_at: datetime
    price_factors: Dict[str, List[str]]
    additional_details: Dict[str, Any]


class UpdateProductRequest(BaseModel):
    """
    Request model for updating a product.
    """

    client_id: int
    product_id: UUID4
    data: Dict[str, Any]
    audit_data: Dict[str, Any]


class UpdateProductResponse(BaseModel):
    """
    Response model for updating a product.
    """

    product_id: UUID4
    status: str
    name: str
    description: str
    price_factors: Dict[str, List[str]]
    updated_by: str
    updated_at: datetime


class CloneProductRequest(BaseModel):
    """
    Request model for cloning a product.
    """

    product_id: UUID4
    clone_pricepoints: bool = False


class CloneProductResponse(BaseModel):
    """
    Response model for cloning a product.
    """


class DeleteProductRequest(BaseModel):
    """
    Request model for deleting a product.
    """

    client_id: int
    product_id: UUID4
    audit_data: Dict[str, Any]
    knowledge_begin_date: datetime | None = None


class DeleteProductResponse(BaseModel):
    """
    Response model for deleting a product.
    """

    product_id: UUID4
    name: str
    status: str


class ActivateProductRequest(BaseModel):
    """
    Request model for activating a product.
    """

    product_id: UUID4
    schedule_start_date: str | None = None
    delete_schedule: bool = False


class ActivateProductResponse(BaseModel):
    """
    Response model for activating a product.
    """

    success: bool
    message: str


class DeactivateProductRequest(BaseModel):
    """
    Request model for deactivating a product.
    """

    product_id: UUID4
    schedule_end_date: str | None = None
    delete_schedule: bool = False


class DeactivateProductResponse(BaseModel):
    """
    Response model for deactivating a product.
    """

    success: bool
    message: str


class ProductListItem(BaseModel):
    """
    Represents a single item in a list of products.
    """

    id: UUID4
    name: str
    sku: str
    status: str
    billing_type: str
    category: str
    description: str
    managed_by: str | None = None
    charge_unit: str | None = None


class ListProductsRequest(BaseModel):
    """
    Request model for listing products.
    """

    status: Optional[str] = None


class ListProductsResponse(BaseModel):
    """
    Response model for listing products.
    """

    products: List[ProductListItem]


class CreateProductPayload(BaseModel):
    """
    Payload model for creating a new product.
    """

    product_id: UUID4 | None = None
    name: str
    sku: str
    billing_type: Literal["one_time", "recurring"]
    category: str
    description: str | None = None
    charge_unit: str | None = None


class UpdateProductPayload(BaseModel):
    """
    Payload model for updating a product.
    """

    product_id: UUID4
    data: Dict[str, Any]


class DeleteProductPayload(BaseModel):
    """
    Payload model for deleting a product.
    """

    product_id: UUID4


class CloneProductPayload(BaseModel):
    """
    Payload model for cloning a price book.
    """

    price_book_id: UUID4
