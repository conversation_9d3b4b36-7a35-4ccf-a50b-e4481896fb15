import logging
from typing import <PERSON><PERSON>
from uuid import UUID

from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from commission_engine.models.settlement_models import SettlementRule, SettlementType
from commission_engine.services.datasheet_data_services.variable_extractor import (
    VariableExtractor,
)
from commission_engine.utils.criteria_calculator_utils import create_ast
from commission_engine.utils.databook_utils import get_datasheet_snowflake_data_type_map
from everstage_ddd.settlement_v3.common.exceptions import InvalidSettlementTypeError
from everstage_ddd.settlement_v3.common.param_types import CriteriaInfo, SyncInfo
from everstage_ddd.settlement_v3.query_providers.settlement_on_booking_query_provider import (
    SettlementOnBookingQueryProvider,
)
from everstage_ddd.settlement_v3.query_providers.settlement_on_collection_query_provider import (
    SettlementOnCollectionQueryProvider,
)
from everstage_ddd.settlement_v3.task_engine.utils import (
    get_escaped_single_quoted_string,
    get_snowflake_plan_payee_period_table_name,
    get_snowflake_temp_settlement_table_name,
)

logger = logging.getLogger(__name__)


class SettlementQueryManager:
    """
    This class is responsible for managing the query providers for different settlement types.
    """

    def __init__(
        self,
        sync_info: SyncInfo,
        criteria_info: CriteriaInfo,
    ):
        self.client_id = sync_info.client_id
        self.e2e_sync_run_id = sync_info.e2e_sync_run_id
        self.sync_run_id = sync_info.sync_run_id
        self.criteria_info = criteria_info
        self.period_start_date = sync_info.period_start_date
        self.period_end_date = sync_info.period_end_date
        self.knowledge_date = sync_info.knowledge_date
        self.log_context = sync_info.log_context

        self.payee_email_table_name = get_snowflake_plan_payee_period_table_name(
            self.client_id, self.e2e_sync_run_id
        )
        self.payout_snapshot_table_name = f"PAYOUT_SNAPSHOT_DATA_{self.client_id}"

        self.providers = {
            SettlementType.SETTLEMENT_ON_BOOKING: SettlementOnBookingQueryProvider(
                sync_info=sync_info,
                criteria_info=criteria_info,
            ),
            SettlementType.SETTLEMENT_ON_COLLECTION: SettlementOnCollectionQueryProvider(
                sync_info=sync_info,
                criteria_info=criteria_info,
            ),
        }

    def get_insert_query_for_settlement_temp_table(
        self, settlement_rule: SettlementRule, table_name: str
    ) -> Tuple[str, list]:
        """
        Returns the query and params for the settlement temp table based on the settlement rule type.
        """
        provider = self.providers.get(settlement_rule.settlement_type)
        if not provider:
            raise InvalidSettlementTypeError(settlement_rule.settlement_type)
        return provider.get_query_and_params(settlement_rule, table_name)

    # TODO: fix sql injection error
    def get_payees_without_line_items_query(self):
        """
        This function returns the query and params to get the payees without any line items for the criteria.
        """

        query = f"""
            SELECT 
                ps.payee_email_id as payee_email_id, 
                ps.additional_data:conversion_rate::double as conversion_rate,
                ps.additional_data:criteria_name::string as criteria_name,
                ps.additional_data:data_pay_currency::string as payee_currency,
                ps.additional_data:data_full_name::string as payee_name,
                ps.additional_data:payout_frequency::string as payout_freq,
                ps.additional_data:plan_name::string as plan_name,
                ps.additional_data:plan_type::string as plan_type,
                ps.additional_data:data_variable_pay_as_per_period::double as variable_pay,
            FROM 
                table(?) ps
            JOIN 
                {self.payee_email_table_name} ppe on ppe.plan_id = ps.plan_id
                AND ppe.payee_email_id = ps.payee_email_id
                AND ppe.period_end_date::DATETIME = ?::DATETIME
            WHERE 
                ps.plan_id = ? 
                AND ps.criteria_id = ? 
                AND ps.period_start_date::DATETIME = ?::DATETIME
                AND ps.period_end_date::DATETIME = ?::DATETIME
                AND ps.line_item_id IS NULL 
                AND ps.tier_id_split[0]:tier_id::string <> '-'
                AND ps.commission = 0
        """  # noqa: S608
        query_params = [
            self.payout_snapshot_table_name,
            self.period_end_date,
            str(self.criteria_info.plan_id),
            str(self.criteria_info.criteria_id),
            self.period_start_date,
            self.period_end_date,
        ]
        return query, query_params

    def get_select_query_for_settlement_temp_table(  # noqa: PLR0913
        self,
        settlement_rule: SettlementRule,
        period_label: str,
        settlement_databook_name: str,
        settlement_datasheet_name: str,
        org_currency: str,
    ):
        """
        Returns the query to select the data from the settlement temp table.
        """
        temp_settlement_table_name = get_snowflake_temp_settlement_table_name(
            self.client_id, settlement_rule.settlement_rule_id, self.sync_run_id
        )
        required_columns = get_variables_used_in_rule_expressions(
            settlement_rule.settlement_flag_expr,
            settlement_rule.amount_expr,
        )
        datasheet_columns = get_typecasted_columns_from_datasheet(
            self.client_id,
            settlement_rule.databook_id,  # type: ignore
            settlement_rule.datasheet_id,  # type: ignore
            "st",
            "settlement_sheet_data",
            required_columns,
        )
        columns = [
            "st.commission_row_key",
            "st.line_item_id",
            "st.commission::double as commission",
            f"st.commission_sheet_data:{self.criteria_info.date_field}::datetime as commission_date",
            f"st.settlement_sheet_data:{settlement_rule.date_field}::datetime as settlement_date",
            "st.payee_email_id as payee_email_id",
            f"'{str(self.criteria_info.plan_id)}'::string as commission_plan_id",
            f"'{str(self.criteria_info.criteria_id)}'::string as criteria_id",
            "st.period_start_date",
            "st.period_end_date",
            "st.additional_data:comm_secondary_kd::datetime as secondary_kd",
            "st.commission::double as comm_amount",
            "st.additional_data:conversion_rate::double as conversion_rate",
            "st.additional_data:amount_payee_currency::double as comm_amount_payee_currency",
            "st.additional_data:period_label::string as comm_period_label",
            "st.additional_data:criteria_name::string as criteria_name",
            "st.additional_data:data_pay_currency::string as payee_currency",
            "st.additional_data:data_full_name::string as payee_name",
            "st.additional_data:payout_frequency::string as payout_freq",
            f"'{get_escaped_single_quoted_string(period_label)}'::string as period_label",
            "st.additional_data:plan_name::string as plan_name",
            "st.additional_data:plan_type::string as plan_type",
            f"'{get_escaped_single_quoted_string(settlement_databook_name)}'::string as settlement_databook",
            f"'{get_escaped_single_quoted_string(settlement_datasheet_name)}'::string as settlement_datasheet",
            "'Settlement'::string as record_type",
            f"'{get_escaped_single_quoted_string(org_currency)}'::string as org_currency",
            "st.additional_data:data_variable_pay_as_per_period::double as variable_pay",
            f"'{get_escaped_single_quoted_string(settlement_rule.name)}'::string as settlement_criteria_name",
            *datasheet_columns,
        ]
        query = (
            "SELECT "  # noqa: S608
            + ", ".join(columns)
            + f" FROM {temp_settlement_table_name} st"
        )
        return query

    # TODO: fix sql injection error
    @staticmethod
    def get_count_query_for_settlement_temp_table(table_name: str):
        """
        Returns the query to count the number of rows in the settlement temp table.
        """
        return f"SELECT COUNT(*) FROM {table_name}"  # noqa: S608

    @staticmethod
    def get_query_to_create_settlement_temp_table(table_name: str):
        """
        Returns the query to create the settlement temp table.
        """
        return f"""
            CREATE OR REPLACE TABLE {table_name} (
                id integer,
                payee_email_id string,
                period_start_date datetime,
                period_end_date datetime,
                commission_row_key string,
                line_item_id string,
                commission_sheet_data variant,
                settlement_sheet_data variant,
                additional_data variant,
                commission double
            )
        """

    @staticmethod
    def get_query_to_drop_settlement_temp_table(table_name: str):
        """
        Returns the query to drop the settlement temp table.
        """
        return f"DROP TABLE IF EXISTS {table_name}"


def get_typecasted_columns_from_datasheet(  # noqa: PLR0913
    client_id: int,
    databook_id: UUID,
    datasheet_id: UUID,
    table_alias: str,
    data_column_name: str,
    required_columns: list[str],
) -> list[str]:
    """
    This function returns the list of required snowflaketypecasted columns from the datasheet.
    """
    if not required_columns:
        return []
    logger.info(
        f"BEGIN: Fetching datasheet variables type for datasheet_id: {datasheet_id}"
    )

    json_col_name = f"{table_alias}.{data_column_name}"

    # Retrieve variable definitions and their data types
    variables = DatasheetVariableAccessor(
        client_id
    ).get_latest_system_name_and_dtype_for_datasheet(
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        variables=required_columns,
    )
    col_type_map = get_datasheet_snowflake_data_type_map(
        client_id, databook_id, datasheet_id
    )
    columns = [
        f"{json_col_name}:{variable['system_name']}::{col_type_map[variable['system_name']]} as {variable['system_name']}"
        for variable in variables
    ]

    logger.info(
        f"END: Fetching datasheet variables type for datasheet_id: {datasheet_id}"
    )
    return columns


def get_variables_used_in_rule_expressions(
    settlement_flag_expr: dict, amount_expr: dict
) -> list[str]:
    """
    This function extracts the variables used in the settled_flag_expression and settlement_rule_expression.
    """
    amount_ast = create_ast(amount_expr["ast"])["ast"]
    settlement_flag_ast = create_ast(settlement_flag_expr["ast"])["ast"]

    variables_used = []
    variables_used.extend(VariableExtractor().get_variables_used(amount_ast))
    variables_used.extend(VariableExtractor().get_variables_used(settlement_flag_ast))
    return list(set(variables_used))
