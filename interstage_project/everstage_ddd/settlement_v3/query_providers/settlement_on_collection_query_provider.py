import logging

from commission_engine.models.settlement_models import SettlementRule
from commission_engine.utils.databook_utils import (
    get_datasheet_data_table_name,
    get_datasheet_snowflake_data_type_map,
)
from commission_engine.utils.log_utils import merge_log_context
from everstage_ddd.settlement_v3.common.exceptions import ColumnNotFoundError
from everstage_ddd.settlement_v3.common.param_types import CriteriaInfo, SyncInfo
from everstage_ddd.settlement_v3.query_providers.abstract_settlement_query_provider import (
    AbstractSettlementQueryProvider,
)
from everstage_ddd.settlement_v3.task_engine.utils import (
    get_snowflake_plan_payee_period_table_name,
)
from interstage_project.threadlocal_log_context import set_threadlocal_context

logger = logging.getLogger(__name__)


class SettlementOnCollectionQueryProvider(AbstractSettlementQueryProvider):
    """
    This class is responsible for generating the SQL query and parameters required for settlement on collection calculations.
    """

    def __init__(
        self,
        sync_info: SyncInfo,
        criteria_info: CriteriaInfo,
    ):
        self.client_id = sync_info.client_id
        self.criteria_id = criteria_info.criteria_id
        self.plan_id = criteria_info.plan_id
        self.period_start_date = sync_info.period_start_date
        self.period_end_date = sync_info.period_end_date
        self.knowledge_date = sync_info.knowledge_date
        self.e2e_sync_run_id = sync_info.e2e_sync_run_id
        self.sync_run_id = sync_info.sync_run_id

        log_context_local = {
            "client_id": self.client_id,
            "plan_id": self.plan_id,
            "criteria_id": self.criteria_id,
            "period_start_date": self.period_start_date,
            "period_end_date": self.period_end_date,
            "knowledge_date": self.knowledge_date,
        }
        log_context = merge_log_context(sync_info.log_context, log_context_local)
        set_threadlocal_context(log_context)

        self.commission_date_field = criteria_info.date_field
        self.commission_databook_id = criteria_info.databook_id
        self.commission_datasheet_id = criteria_info.datasheet_id

        self.payee_email_table_name = get_snowflake_plan_payee_period_table_name(
            self.client_id, self.e2e_sync_run_id
        )
        self.payout_snapshot_table_name = f"PAYOUT_SNAPSHOT_DATA_{self.client_id}"
        self.settlement_snapshot_table_name = (
            f"SETTLEMENT_SNAPSHOT_DATA_{self.client_id}"
        )

    # TODO: fix sql injection error
    def get_query_and_params(self, settlement_rule: SettlementRule, table_name: str):
        logger.info(
            f"Getting query and params for settlement rule {settlement_rule.settlement_rule_id}"
        )
        data_sheet_name = get_datasheet_data_table_name(
            self.client_id, settlement_rule.datasheet_id
        )
        join_query = self.construct_join_condition(
            commission_ds_alias="ps",
            settlement_ds_alias="ds",
            settlement_rule=settlement_rule,
        )

        query = f""" 
            INSERT INTO {table_name} (
                payee_email_id, 
                period_start_date,
                period_end_date,
                commission_row_key, 
                line_item_id,
                commission_sheet_data, 
                settlement_sheet_data, 
                additional_data, 
                commission,
                id
            )
            SELECT 
               ps.payee_email_id,
               ps.period_start_date,
               ps.period_end_date,
               ps.line_item_id as commission_row_key,
               ds.row_key as line_item_id,
               ps.datasheet_data as commission_sheet_data,
               ds.data as settlement_sheet_data,
               ps.additional_data,
               ps.commission,
               row_number() over (order by 0) as id
            FROM 
                table(?) ps
            JOIN 
                {self.payee_email_table_name} ppe on ppe.plan_id = ps.plan_id
                AND ppe.payee_email_id = ps.payee_email_id
                AND ppe.period_end_date::DATETIME = ?::DATETIME
            JOIN 
                {data_sheet_name} ds on {join_query}
            LEFT JOIN 
                {self.settlement_snapshot_table_name} ss on 
                    ss.plan_id = ps.plan_id 
                    AND ss.criteria_id = ps.criteria_id
                    AND ss.settlement_rule_id = ?
                    AND ss.payee_email_id = ps.payee_email_id
                    AND ss.line_item_id = ds.data:row_key
                    AND ss.is_locked = TRUE
                    AND ss.period_start_date < ?::DATETIME
            WHERE 
                ps.plan_id = ? 
                AND ps.criteria_id = ?
                AND ps.tier_id_split[0]:tier_id::string <> '-'
                AND ds.knowledge_begin_date <= ?::DATETIME
                AND (ds.knowledge_end_date IS NULL OR ds.knowledge_end_date > ?::DATETIME)
                AND ds.is_deleted = FALSE 
                AND ds.data:{settlement_rule.date_field}::DATETIME >= ?::DATETIME
                AND ds.data:{settlement_rule.date_field}::DATETIME <= ?::DATETIME
                AND ds.data is not null
                AND ss.is_locked is null
        """  # noqa: S608
        query_params = [
            self.payout_snapshot_table_name,
            self.period_end_date,
            str(settlement_rule.settlement_rule_id),
            self.period_start_date,
            str(self.plan_id),
            str(self.criteria_id),
            self.knowledge_date,
            self.knowledge_date,
            self.period_start_date,
            self.period_end_date,
        ]
        return query, query_params

    def construct_join_condition(
        self,
        commission_ds_alias: str,
        settlement_ds_alias: str,
        settlement_rule: SettlementRule,
    ) -> str:
        """
        This function constructs the join condition for the payout snapshot and settlement datasheet.
        Similar to datasheets here joins are null safe hence equal_null is used.

        Args:
            commission_ds_alias: alias for the payout snapshot
            settlement_ds_alias: alias for the settlement datasheet
        """
        join_condition = ""
        datasheet_json_col_name = "data"
        snapshot_json_col_name = "datasheet_data"

        com_col_type_map = get_datasheet_snowflake_data_type_map(
            self.client_id, self.commission_databook_id, self.commission_datasheet_id
        )
        sett_col_type_map = get_datasheet_snowflake_data_type_map(
            self.client_id, settlement_rule.databook_id, settlement_rule.datasheet_id  # type: ignore
        )

        for i in range(len(settlement_rule.commission_join_keys)):  # type: ignore
            com_key = settlement_rule.commission_join_keys[i]  # type: ignore
            inv_key = settlement_rule.settlement_join_keys[i]  # type: ignore

            if com_key not in com_col_type_map:
                raise ColumnNotFoundError(com_key, self.commission_datasheet_id)
            if inv_key not in sett_col_type_map:
                raise ColumnNotFoundError(inv_key, settlement_rule.datasheet_id)

            com_key_with_type = f"{commission_ds_alias}.{snapshot_json_col_name}:{com_key}::{com_col_type_map[com_key]}"
            inv_key_with_type = f"{settlement_ds_alias}.{datasheet_json_col_name}:{inv_key}::{sett_col_type_map[inv_key]}"

            if join_condition == "" and i == 0:
                join_condition = f"EQUAL_NULL({com_key_with_type}, {inv_key_with_type})"
            else:
                join_condition = f"{join_condition} AND EQUAL_NULL({com_key_with_type}, {inv_key_with_type})"

        return join_condition
