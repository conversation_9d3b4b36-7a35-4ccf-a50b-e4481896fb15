import logging

from commission_engine.models.settlement_models import SettlementRule
from commission_engine.utils.log_utils import merge_log_context
from everstage_ddd.settlement_v3.common.param_types import CriteriaInfo, SyncInfo
from everstage_ddd.settlement_v3.query_providers.abstract_settlement_query_provider import (
    AbstractSettlementQueryProvider,
)
from everstage_ddd.settlement_v3.task_engine.utils import (
    get_snowflake_plan_payee_period_table_name,
)
from interstage_project.threadlocal_log_context import set_threadlocal_context

logger = logging.getLogger(__name__)


class SettlementOnBookingQueryProvider(AbstractSettlementQueryProvider):
    """
    This class is responsible for generating the SQL query and parameters required for settlement on booking calculations.
    """

    def __init__(
        self,
        sync_info: SyncInfo,
        criteria_info: CriteriaInfo,
    ):
        log_context_local = {
            "client_id": sync_info.client_id,
            "plan_id": criteria_info.plan_id,
            "criteria_id": criteria_info.criteria_id,
            "period_start_date": sync_info.period_start_date,
            "period_end_date": sync_info.period_end_date,
        }
        log_context = merge_log_context(sync_info.log_context, log_context_local)
        set_threadlocal_context(log_context)

        self.client_id = sync_info.client_id
        self.plan_id = criteria_info.plan_id
        self.criteria_id = criteria_info.criteria_id
        self.period_start_date = sync_info.period_start_date
        self.period_end_date = sync_info.period_end_date
        self.e2e_sync_run_id = sync_info.e2e_sync_run_id

        self.payee_email_table_name = get_snowflake_plan_payee_period_table_name(
            self.client_id, self.e2e_sync_run_id
        )
        self.payout_snapshot_table_name = f"PAYOUT_SNAPSHOT_DATA_{self.client_id}"

    # TODO: fix sql injection error
    def get_query_and_params(self, settlement_rule: SettlementRule, table_name: str):
        """
        This function generates the SQL query and parameters for inserting data into the settlement table.
        """

        logger.info(
            f"Generating query for settlement on booking for rule: {settlement_rule.settlement_rule_id}"
        )
        query = f"""
            INSERT INTO {table_name} (
                payee_email_id, 
                period_start_date,
                period_end_date,
                commission_row_key, 
                line_item_id,
                commission_sheet_data, 
                settlement_sheet_data, 
                additional_data, 
                commission,
                id
            )
            SELECT 
               ps.payee_email_id,
               ps.period_start_date,
               ps.period_end_date,
               ps.line_item_id as commission_row_key,
               ps.line_item_id as line_item_id,
               ps.datasheet_data as commission_sheet_data,
               ps.datasheet_data as settlement_sheet_data,
               ps.additional_data,
               ps.commission,
               row_number() over (order by 0) as id
            FROM 
                table(?) ps
            JOIN 
                {self.payee_email_table_name} ppe on ppe.plan_id = ps.plan_id
                AND ppe.payee_email_id = ps.payee_email_id
                AND ppe.period_end_date::DATETIME = ?
            WHERE 
                ps.plan_id = ? 
                AND ps.criteria_id = ? 
                AND ps.period_start_date::DATETIME = ?::DATETIME
                AND ps.period_end_date::DATETIME = ?::DATETIME
                AND ps.line_item_id IS NOT NULL
                AND ps.tier_id_split[0]:tier_id::string <> '-'
        """  # noqa: S608
        query_params = [
            self.payout_snapshot_table_name,
            self.period_end_date,
            str(self.plan_id),
            str(self.criteria_id),
            self.period_start_date,
            self.period_end_date,
        ]
        return query, query_params
