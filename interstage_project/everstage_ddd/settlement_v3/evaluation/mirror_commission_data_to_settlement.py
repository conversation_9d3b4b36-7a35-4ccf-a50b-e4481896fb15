import logging
from datetime import datetime
from uuid import UUID

from commission_engine.database.snowflake_query_utils import (
    snowflake_run_sql,
    snowflake_writer_handler,
)
from commission_engine.utils.general_utils import log_time_taken
from commission_engine.utils.snowflake_commission_utils import (
    get_commission_table_name_sf,
)
from everstage_ddd.settlement_v3.common.enums import ExecutionMode
from everstage_ddd.settlement_v3.task_engine.utils import (
    get_settlement_sf_table_name,
    get_temp_non_settlement_criteria_ids_table_name,
    get_temp_payee_period_table_name,
)

logger = logging.getLogger(__name__)


class MirrorCommissionDataToSettlement:
    """
    This class is used to mirror commission data to settlement.
    When the commission plan criteria not associated with any settlement rules,
    we need to mirror the commission data to settlement.
    This should not be run directly, since it needs some meta data to be setup.
    The required meta data is setup by the SettlementSyncPlanBuilder.
    """

    def __init__(  # noqa: PLR0913
        self,
        client_id: int,
        e2e_sync_run_id: UUID,
        sync_run_id: UUID,
        knowledge_date: datetime,
        execution_mode: ExecutionMode,
    ):
        self.client_id = client_id
        self.e2e_sync_run_id = e2e_sync_run_id
        self.sync_run_id = sync_run_id
        self.knowledge_date = knowledge_date
        self.execution_mode = execution_mode
        self.criteria_temp_table_name = get_temp_non_settlement_criteria_ids_table_name(
            client_id=self.client_id, e2e_sync_run_id=self.e2e_sync_run_id
        )
        self.payee_period_temp_table_name = get_temp_payee_period_table_name(
            client_id=self.client_id, e2e_sync_run_id=self.e2e_sync_run_id
        )
        self.payee_periods = []
        self.criteria_without_settlement_rules = []
        self.settlement_table_name = get_settlement_sf_table_name(
            client_id=self.client_id, execution_mode=self.execution_mode
        )
        self.commission_table_name = get_commission_table_name_sf(
            client_id=self.client_id
        )
        self.base_currency = "USD"

    @log_time_taken()
    def mirror_commission_data_to_settlement(self):
        """
        This method is used to mirror commission data to settlement.
        """
        logger.info("BEGIN: MIRROR COMMISSION DATA TO SETTLEMENT")
        settlement_invalidation_query = self._get_settlement_invalidation_query()
        settlement_insert_query = self._get_settlement_insert_query()

        with snowflake_writer_handler(client_id=self.client_id) as snowpark_session:
            snowflake_run_sql(
                snowpark_session=snowpark_session,
                query=settlement_invalidation_query,
                client_id=self.client_id,
            )
            snowflake_run_sql(
                snowpark_session=snowpark_session,
                query=settlement_insert_query,
                client_id=self.client_id,
            )

        logger.info("END: MIRROR COMMISSION DATA TO SETTLEMENT")

    # TODO: fix sql injection error
    def _get_settlement_insert_query(self) -> str:
        """
        This method is used to get the settlement insert query.
        The query reads the commission table for the criterias without settlement rules.
        The base_data has 2 parts:
            1. For line item level data or split summation to line item records
            2. For non line item level data (without split summation)
        The final query is a union of the 2 parts.

        Note: For summation level records, commission records with show_do_nothing as true are not excluded.
        This is done to maintain the existing behavior.
        """
        kbd_str = self.knowledge_date.isoformat()
        return f"""
            INSERT INTO {self.settlement_table_name} (
                client_id,
                knowledge_begin_date,
                commission_date,
                settlement_date,
                period_start_date,
                period_end_date,
                comm_period_start_date,
                comm_period_end_date,
                line_item_id,
                commission_row_key,
                plan_id,
                criteria_id,
                payee_email_id,
                amount,
                settlement_flag,
                additional_data
            )
            WITH base_data AS (
                SELECT 
                    c.payee_email_id,
                    c.period_start_date,
                    c.period_end_date,
                    c.criteria_id,
                    c.line_item_id,
                    SUM(c.amount) AS amount,
                    ANY_VALUE(c.commission_plan_id) AS plan_id,
                    ANY_VALUE(c.commission_date) AS commission_date,
                    COALESCE(ANY_VALUE(c.additional_data:conversion_rate)::DOUBLE, 1) AS fx_rate,
                    ROUND(SUM(c.amount), 6) * COALESCE(ANY_VALUE(c.additional_data:conversion_rate)::DOUBLE, 1)::double AS amount_payee_currency,
                    ANY_VALUE(c.additional_data:period_label)::STRING AS period_label,
                    ANY_VALUE(c.additional_data:data_pay_currency)::STRING AS payee_currency,
                    ANY_VALUE(c.additional_data:data_full_name)::STRING AS payee_name,
                    ANY_VALUE(c.additional_data:payout_frequency)::STRING AS payout_freq,
                    ANY_VALUE(c.additional_data:plan_name)::STRING AS plan_name,
                    ANY_VALUE(c.additional_data:plan_type)::STRING AS plan_type,
                    ANY_VALUE(c.additional_data:criteria_name)::STRING AS criteria_name,
                    ANY_VALUE(c.additional_data:databook_name)::STRING AS settlement_databook,
                    ANY_VALUE(c.additional_data:datasheet_name)::STRING AS settlement_datasheet,
                    ANY_VALUE(c.additional_data:data_variable_pay_as_per_period) AS variable_pay
                FROM {self.commission_table_name} c
                JOIN {self.payee_period_temp_table_name} p
                    ON c.payee_email_id = p.payee_email_id
                    AND c.period_start_date = p.period_start_date
                    AND c.period_end_date = p.period_end_date
                JOIN {self.criteria_temp_table_name} cp
                    ON c.criteria_id = cp.criteria_id
                WHERE c.knowledge_end_date is null
                    AND is_deleted is distinct from true
                    AND show_do_nothing is distinct from true
                    AND (
                        c.additional_data:is_line_item_level::BOOLEAN
                        OR c.additional_data:split_summation_to_li::BOOLEAN
                    )
                GROUP BY
                    c.criteria_id,
                    c.payee_email_id,
                    c.period_start_date,
                    c.period_end_date,
                    c.line_item_id

                UNION ALL
                
                SELECT 
                    c.payee_email_id,
                    c.period_start_date,
                    c.period_end_date,
                    c.criteria_id,
                    null as line_item_id,
                    ANY_VALUE(c.amount) AS amount,
                    ANY_VALUE(c.commission_plan_id) AS plan_id,
                    ANY_VALUE(c.commission_date) AS commission_date,
                    COALESCE(ANY_VALUE(c.additional_data:conversion_rate)::DOUBLE, 1) AS fx_rate,
                    ROUND(ANY_VALUE(c.amount), 6) * COALESCE(ANY_VALUE(c.additional_data:conversion_rate)::DOUBLE, 1)::double AS amount_payee_currency,
                    ANY_VALUE(c.additional_data:period_label)::STRING AS period_label,
                    ANY_VALUE(c.additional_data:data_pay_currency)::STRING AS payee_currency,
                    ANY_VALUE(c.additional_data:data_full_name)::STRING AS payee_name,
                    ANY_VALUE(c.additional_data:payout_frequency)::STRING AS payout_freq,
                    ANY_VALUE(c.additional_data:plan_name)::STRING AS plan_name,
                    ANY_VALUE(c.additional_data:plan_type)::STRING AS plan_type,
                    ANY_VALUE(c.additional_data:criteria_name)::STRING AS criteria_name,
                    ANY_VALUE(c.additional_data:databook_name)::STRING AS settlement_databook,
                    ANY_VALUE(c.additional_data:datasheet_name)::STRING AS settlement_datasheet,
                    ANY_VALUE(c.additional_data:data_variable_pay_as_per_period) AS variable_pay
                FROM {self.commission_table_name} c
                JOIN {self.payee_period_temp_table_name} p
                    ON c.payee_email_id = p.payee_email_id
                    AND c.period_start_date = p.period_start_date
                    AND c.period_end_date = p.period_end_date
                JOIN {self.criteria_temp_table_name} cp
                    ON c.criteria_id = cp.criteria_id
                WHERE c.knowledge_end_date is null
                    AND is_deleted is distinct from true
                    AND (
                        NOT c.additional_data:is_line_item_level::BOOLEAN
                        AND (NOT c.additional_data:split_summation_to_li::BOOLEAN
                            OR c.additional_data:split_summation_to_li IS NULL
                        )
                    )
                GROUP BY
                    c.criteria_id,
                    c.payee_email_id,
                    c.period_start_date,
                    c.period_end_date
            )
            SELECT 
                {self.client_id} as client_id,
                '{kbd_str}'::Datetime as knowledge_begin_date,
                commission_date as commission_date,
                commission_date as settlement_date,
                period_start_date as period_start_date, 
                period_end_date as period_end_date,
                period_start_date as comm_period_start_date,
                period_end_date as comm_period_end_date,
                line_item_id as line_item_id,
                line_item_id as commission_row_key,
                plan_id as plan_id,
                criteria_id as criteria_id,
                payee_email_id as payee_email_id,
                round(amount, 6) as amount,
                true as settlement_flag,
                OBJECT_CONSTRUCT(
                    'amount_payee_currency', round(amount_payee_currency, 6),
                    'comm_amount', round(amount, 6),
                    'comm_amount_payee_currency', round(amount_payee_currency, 6),
                    'comm_period_label', period_label,
                    'conversion_rate', fx_rate,
                    'criteria_name', criteria_name,
                    'is_locked', FALSE,
                    'org_currency', '{self.base_currency}',
                    'payee_currency', payee_currency,
                    'payee_name', payee_name,
                    'payout_freq', payout_freq,
                    'period_label', period_label,
                    'plan_name', plan_name,
                    'plan_type', plan_type,
                    'record_type', 'Settlement',
                    'settlement_databook', settlement_databook,
                    'settlement_datasheet', settlement_datasheet,
                    'variable_pay', variable_pay
                ) AS additional_data
            FROM base_data;
        """  # noqa: S608

    # TODO: fix sql injection error
    def _get_settlement_invalidation_query(self) -> str:
        """
        Returns the settlement invalidation query for the criteria without settlement rules.
        """
        iso_knowledge_date = self.knowledge_date.isoformat()

        return f"""
            MERGE INTO {self.settlement_table_name} s
            USING (
                SELECT p.payee_email_id, p.period_end_date, c.criteria_id
                FROM {self.criteria_temp_table_name} c
                JOIN {self.payee_period_temp_table_name} p
                ON c.criteria_id IS NOT NULL
            ) t
            ON s.payee_email_id = t.payee_email_id
            AND s.period_end_date = t.period_end_date
            AND s.criteria_id = t.criteria_id
            AND s.knowledge_end_date IS NULL
            AND s.is_deleted IS DISTINCT FROM true
            WHEN MATCHED THEN
                UPDATE SET knowledge_end_date = '{iso_knowledge_date}';
        """  # noqa: S608
