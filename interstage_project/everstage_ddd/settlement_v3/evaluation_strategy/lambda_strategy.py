import asyncio
import copy
import logging
import os
from time import time

import requests

from commission_engine.accessors.client_accessor import (
    get_client,
    get_ever_comparison,
    get_settlement_lambda_batch_size,
)
from commission_engine.database.snowflake_connection import (
    get_database_name,
    get_snowflake_connection_params,
)
from commission_engine.models.settlement_models import SettlementRule
from commission_engine.utils.criteria_calculator_utils import create_ast
from everstage_ddd.settlement_snowflake_utils.write_utils import (
    get_s3_path_to_write_settlement_v3_snowflake,
)
from everstage_ddd.settlement_v3.common.exceptions import (
    LambdaSettlementEvaluationError,
)
from everstage_ddd.settlement_v3.common.param_types import SettlementStrategyConfig
from everstage_ddd.settlement_v3.evaluation_strategy.abstract_settlement_strategy import (
    AbstractSettlementStrategy,
)
from everstage_ddd.settlement_v3.task_engine.utils import (
    append_batch_clause_to_query,
    calculate_batch_ranges,
)
from interstage_project.global_utils.lambda_utils import (
    AsyncLambdaInvoker,
    get_compute_settlement_lambda_name,
)
from spm.accessors.commission_plan_accessor import CommissionPlanAccessor
from spm.accessors.config_accessors.employee_accessor import PlanDetailsAllAccessor
from spm.services.custom_calendar_services import get_all_periods

logger = logging.getLogger(__name__)

# pylint: disable=logging-fstring-interpolation

HTTP_SUCCESS_STATUS_CODE = 200


class LambdaSettlementStrategy(AbstractSettlementStrategy):
    """
    This class is responsible for evaluating the settlement using lambda.
    If run in local environment, it makes request to flask app, else it calls lambda function.
    Batching can be done by constant value using feature flag "settlement_lambda_batch_size"
    If feature flag is not set, default batch size of 100,000 records is used
    """

    def __init__(
        self,
        config: SettlementStrategyConfig,
        log_context: dict,
    ):
        super().__init__(
            config,
            log_context,
        )

        client = get_client(self.client_id)
        self.ever_comparison = get_ever_comparison(self.client_id)
        self.custom_periods = get_all_periods(self.client_id)
        self.fiscal_start_month = client.fiscal_start_month
        self.plan_additional_config = CommissionPlanAccessor(
            self.client_id
        ).get_plan_additional_config(self.plan_id)
        sed_map = PlanDetailsAllAccessor(
            self.client_id
        ).get_payees_with_sed_less_than_date(self.plan_id, self.period_end_date)
        self.sed_less_than_date_map = {}

        for payee_email_id, sed_date in sed_map.items():
            self.sed_less_than_date_map[payee_email_id] = sed_date.isoformat()
        self.batch_size = get_settlement_lambda_batch_size(self.client_id)

    def calculate_settlement_for_rule(
        self,
        settlement_rule: SettlementRule,
        query: str,
        record_count: int,
    ):
        """
        This function is used to evaluate the settlement for a given rule using lambda

        Args:
            query: str
            query_params: dict
            settlement_rule: SettlementRule

        Returns:
            bool: True if all the lambda calls are successful, False otherwise
            list: List of results of all

        """
        batch_ranges = calculate_batch_ranges(record_count, self.batch_size)
        lambda_params = self.get_lambda_params(query, settlement_rule)

        lambda_param_list = []
        if len(batch_ranges) > 1:
            lambda_params["query_string"] = append_batch_clause_to_query(query, "id")
            for batch_number, batch in enumerate(batch_ranges):
                current_params = copy.deepcopy(lambda_params)
                current_params["query_params"] = [
                    batch[0],
                    batch[1],
                ]
                s3_path = get_s3_path_to_write_settlement_v3_snowflake(
                    client_id=self.client_id,
                    e2e_sync_run_id=self.e2e_sync_run_id,
                    sync_run_id=self.sync_run_id,
                    execution_mode=self.execution_mode,
                    operation="INSERT",
                    batch_number=batch_number + 1,
                )
                current_params["s3_path"] = s3_path
                lambda_param_list.append(current_params)
        else:
            s3_path = get_s3_path_to_write_settlement_v3_snowflake(
                client_id=self.client_id,
                e2e_sync_run_id=self.e2e_sync_run_id,
                sync_run_id=self.sync_run_id,
                operation="INSERT",
                execution_mode=self.execution_mode,
            )
            lambda_params["s3_path"] = s3_path
            lambda_param_list.append(lambda_params)

        logger.debug(f"Params for lambda calls - {lambda_params}")
        lambda_st_time = time()
        results = []
        # if local environment make request to flask app , else call lambda function
        is_local_env = os.environ.get("ENV") == "LOCALDEV"

        # TODO: handle flag
        if not is_local_env:
            logger.info("Invoking Lambda for Calculated Fields execution")
            evaluate_lambda_name = get_compute_settlement_lambda_name()
            logger.info(f"Lambda function name - {evaluate_lambda_name}")
            invoker = AsyncLambdaInvoker()
            invoker.set_lambda_function_name(evaluate_lambda_name)
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            results = loop.run_until_complete(
                invoker.invoke_multiple(lambda_param_list)
            )
            logger.info(f"Results from Lambda - {results}")
            # Check if all the lambda calls are successful

            success = all(
                result["status_code"] == HTTP_SUCCESS_STATUS_CODE for result in results
            )
            invoker.close()
            lamda_e_time = round(time() - lambda_st_time, 2)
        else:
            logger.info("Invoking local Flask App for Settlement execution")
            for params in lambda_param_list:
                res = requests.post(  # noqa: S113
                    "http://host.docker.internal:5052/calculate_settlement_lambda",
                    json=params,
                )
                results.append(res.json())

            # Check if all the lambda calls are successful
            success = all(
                result["status_code"] == HTTP_SUCCESS_STATUS_CODE for result in results
            )
            lamda_e_time = round(time() - lambda_st_time, 2)

        logger.info("Time taken for Lambda calls - %s", lamda_e_time)
        if not success:
            raise LambdaSettlementEvaluationError()

    def get_lambda_params(self, query: str, settlement_rule: SettlementRule):
        """
        This function returns the lambda parameters for the given query and settlement rule.
        """
        settlement_meta_data = self.get_settlement_meta_data(settlement_rule)
        snowflake_connection_params = get_snowflake_connection_params()
        snowflake_db_name = get_database_name(
            self.client_id, snowflake_connection_params.get("database")
        )

        evaluate_settlement_params = {
            "client_id": self.client_id,
            "source": "snowflake",
            "query_string": query,
            "query_params": [],
            "settlement_meta_data": settlement_meta_data,
            "snowflake_db_name": snowflake_db_name,
        }
        logger.debug(f"Evaluate settlement params - {evaluate_settlement_params}")
        return evaluate_settlement_params

    def get_settlement_meta_data(self, settlement_rule: SettlementRule):
        amount_ast = create_ast(settlement_rule.amount_expr["ast"])["ast"]
        settlement_flag_ast = create_ast(settlement_rule.settlement_flag_expr["ast"])[
            "ast"
        ]
        return {
            "settlement_flag_ast": settlement_flag_ast,
            "amount_ast": amount_ast,
            "sed_less_than_date_map": self.sed_less_than_date_map,
            "ever_comparison": self.ever_comparison,
            "custom_periods": self.custom_periods,
            "period_start_date": self.period_start_date.isoformat(),
            "period_end_date": self.period_end_date.isoformat(),
            "knowledge_date": self.knowledge_date.isoformat(),
            "fiscal_start_month": self.fiscal_start_month,
            "plan_additional_config": (
                self.plan_additional_config if self.plan_additional_config else []
            ),
            "plan_id": str(self.plan_id),
            "criteria_id": str(self.criteria_id),
            "settlement_rule_id": str(settlement_rule.settlement_rule_id),
        }
