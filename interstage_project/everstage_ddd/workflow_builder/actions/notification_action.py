import json
import logging
import os
import uuid
from pathlib import Path
from time import time
from typing import List, Optional

import pandas as pd
from pydantic import BaseModel

from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from commission_engine.utils.s3_utils import S3Uploader
from everstage_ddd.stormbreaker.query_builder.flattened_table_query_builder import (
    FlattenedTableQueryBuilder,
)
from everstage_ddd.workflow_builder.exceptions import (
    EmptyWorkflowNotificationError,
    TooManyNotificationGroupsError,
    TooManyNotificationRecordsError,
)
from everstage_ddd.workflow_builder.service.workflow_in_app_notification_service import (
    send_in_app_notification,
)
from slack_everstage.services.commission_services.workflow_notification_service import (
    post_then_message,
)
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.rbac_accessors import RolePermissionsAccessor
from spm.services.email_services.email_services import send_workflow_email
from spm.services.rbac_services import get_payee_role_id
from spm.services.user_group_service import UserGroupMemberService

from ..service.types import LogicalStructType
from ..smart_tags.resolver import SmartResolver
from ..utils.enums import ChannelEnum
from ..utils.utils import (
    estimate_chunk_size,
    excecute_query,
    generate_fake_datasheet_data,
    get_users_in_slack_channels,
    replace_nan_nat_with_none_dataframe,
)
from .action import Action

logger = logging.getLogger(__name__)
MAX_EMAIL_ROWS = 100


class SendNotificationParams(BaseModel):
    """
    SendNotificationParams is a Pydantic model to validate the input parameters for NotificationAction
    """

    notification_type: str
    mode: str
    slack_template: Optional[str] = None
    sg_template: Optional[dict] = {}
    partition_by: Optional[dict] = {}
    selected_columns: list = []
    column_config: list = []
    to_emails: List[str] = []
    cc_emails: List[str] = []
    bcc_emails: List[str] = []
    to_ds_cols: List[str] = []
    cc_ds_cols: List[str] = []
    bcc_ds_cols: List[str] = []
    to_user_groups: List[str] = []
    cc_user_groups: List[str] = []
    bcc_user_groups: List[str] = []
    slack_channels: List[str] = []
    subject: str = ""
    message: str = ""
    reply_to: str = ""
    attachment_filename: str = "data.csv"
    test_email: str = ""
    owner_email_id: str = "<EMAIL>"
    databook_id: str
    datasheet_id: str
    context: str
    one_time: bool = False
    one_time_keys: List[str] = []
    in_app_params: Optional[dict] = {}


class NotificationAction(Action):
    """
    NotificationAction is a class to send notifications to users

    Args:
        block (dict): The block object containing information about the action.
        instance (Instance): The instance object containing information about the instance.
        config_params (dict, optional): Configuration parameters for the action. Defaults to {}.

    Attributes:
        block (dict): The block object containing information about the action.
        file_location (str): The data file location of the instance.
        client_id (str): The client ID.
        resolver (SmartResolver): The resolver object for resolving smart tags.

    Methods:
        _get_test_header(emails, cc_emails=None, bcc_emails=None, notification_type=ChannelEnum.EMAIL.value):
            Returns the test header for the notification.
        _get_email_mutex(to_emails, cc_emails, bcc_emails):
            Returns the mutually exclusive sets of to, cc, and bcc emails.
        act(test_params={}):
            Performs the notification action.
    """

    def __init__(self, block, rule, instance, config_params=None):
        # If the instance is not provided, the action is in test mode

        self.parse_file_location = False
        if not config_params:
            config_params = {}

        if not instance:
            self.then_id = uuid.uuid4().hex
            base_folder = (
                os.environ.get("EFS_BASE_PATH", "/mnt/efs")
                + "/workflow_builder/"
                + str(config_params.get("client_id", 1))
                + "/test/"
                + str(self.then_id)
                + "/"
            )
            self.block = None
            self.client_id = config_params.get("client_id")
            self.folder_location = base_folder
            self.input_file_location = f"{self.folder_location}test_input.parquet"
        else:
            self.block = block
            self.rule = rule
            self.block_id = block["id"]
            self.client_id = instance.client_id
            self.instance = instance
            self.then_id = str(self.block_id).replace("-", "_")
            logical_structure_obj = LogicalStructType(**self.rule.logical_structure)
            then_comp_if_block_id_map = logical_structure_obj.then_comp_if_block_id_map
            then_block_id = str(self.block_id).replace("-", "_")
            if self.block_id in then_comp_if_block_id_map:
                if_block_id = str(then_comp_if_block_id_map[self.block_id]).replace(
                    "-", "_"
                )
                file_folder = instance.file_folder
                self.input_file_location = f"{file_folder}{if_block_id}/*.parquet"
                self.folder_location = f"{file_folder}{if_block_id}/{then_block_id}/"
                self.parse_file_location = True
            else:
                self.input_file_location = instance.file_location
                self.folder_location = f"{instance.file_folder}{then_block_id}/"
        self.rbac_file_location = f"{self.folder_location}rbac_data.parquet"

        self.resolver = SmartResolver(client_id=self.client_id)
        # Get the system user emails.
        user_emails_objs = EmployeeAccessor(self.client_id).get_all_employee_email()
        self.system_user_emails = set(
            [x["employee_email_id"] for x in user_emails_objs]
        )
        if not Path(self.folder_location).exists():
            Path(self.folder_location).mkdir(parents=True)

        logger.info(
            "Notification Action initialized with: self.input_file_location=%s, self.folder_location=%s, block=%s, rule=%s, instance=%s",
            self.input_file_location,
            self.folder_location,
            block,
            rule,
            instance,
        )

    @staticmethod
    def validate_block(block):
        params = (
            block.get("data", {})
            .get("component_params", {})
            .get("action", {})
            .get("params", {})
        )
        errors = []
        audience = False
        audience_grp = ["to_emails", "to_ds_cols", "to_user_groups"]
        notification_type = params.get("notification_type")
        mode = params.get("mode")
        attachment_filename = params.get("attachment_filename")
        selected_columns = params.get("selected_columns")
        if notification_type in [
            ChannelEnum.EMAIL.value,
            ChannelEnum.IN_APP.value,
        ] and not params.get("subject"):
            errors.append("Subject not present")
        if notification_type == ChannelEnum.SLACK.value:
            audience_grp.append("slack_channels")
            if not params.get("message"):
                errors.append("Message content not present")
        if notification_type == ChannelEnum.IN_APP.value and not params.get(
            "in_app_params", {}
        ).get("notification_status"):
            errors.append("Notification status not present")

        for recipient in audience_grp:
            if params.get(recipient):
                audience = True
                break
        if not audience:
            errors.append("Recipients not present")
        if (mode in ["consolidated", "consolidated_group"]) and (
            not attachment_filename or attachment_filename == ".csv"
        ):
            errors.append("Attachment file not present")
        if (mode in ["consolidated", "consolidated_group"]) and (
            not selected_columns or len(selected_columns) == 0
        ):
            errors.append("No columns selected")

        if errors:
            return (False, errors)
        return (True, [])

    def _get_test_header(
        self,
        emails,
        cc_emails=None,
        bcc_emails=None,
        notification_type=ChannelEnum.EMAIL.value,
    ):
        if notification_type == ChannelEnum.SLACK.value:
            return f"""**This is a test message based on sample data**\n _The actual recipients based on this sample data would be:_ \n _Users: {emails}_ \n\n"""

        header = f"""
        <h2><b>This is a test email based on sample data</b></h2>
        <h3><i>The actual recipients based on this sample data would be:</i></h3>
        <h3><i>To: {emails}</i></h3><br>
        """

        if cc_emails:
            header += f"<h3><i>Cc: {cc_emails}</i></h3><br>"
        if bcc_emails:
            header += f"<h3><i>Bcc: {bcc_emails}</i></h3><br>"

        return header

    def _get_email_mutex(self, to_emails, cc_emails, bcc_emails):
        # Get the mutually exclusive sets of to, cc, and bcc emails. Priority [Bcc > Cc > To]
        to_emails = to_emails - cc_emails - bcc_emails
        cc_emails = cc_emails - bcc_emails

        return to_emails, cc_emails, bcc_emails

    def _resolve_merge_user_groups(
        self,
        emails_tuple,
        goups_tuple,
    ):
        to_emails, cc_emails, bcc_emails = emails_tuple
        to_user_groups, cc_user_groups, bcc_user_groups = goups_tuple

        to_emails = set(to_emails)
        cc_emails = set(cc_emails)
        bcc_emails = set(bcc_emails)

        # Get the user emails from the user groups
        to_user_emails = set()
        if to_user_groups:
            for group_id in to_user_groups:
                to_user_emails.update(
                    UserGroupMemberService(
                        self.client_id
                    ).get_user_group_members_emails(group_id)
                )

        cc_user_emails = set()
        if cc_user_groups:
            for group_id in cc_user_groups:
                cc_user_emails.update(
                    UserGroupMemberService(
                        self.client_id
                    ).get_user_group_members_emails(group_id)
                )

        bcc_user_emails = set()
        if bcc_user_groups:
            for group_id in bcc_user_groups:
                bcc_user_emails.update(
                    UserGroupMemberService(
                        self.client_id
                    ).get_user_group_members_emails(group_id)
                )

        # Add the user emails to the email sets.
        to_emails = to_emails.union(to_user_emails)
        cc_emails = cc_emails.union(cc_user_emails)
        bcc_emails = bcc_emails.union(bcc_user_emails)

        # Get the mutually exclusive sets of to, cc, and bcc emails.
        to_emails, cc_emails, bcc_emails = self._get_email_mutex(
            to_emails, cc_emails, bcc_emails
        )

        return to_emails, cc_emails, bcc_emails

    def _process_notification(
        self,
        params,
        filtered_df,
        is_test_mode,
        to_emails,
        **kwargs,
    ):
        notification_type = params.notification_type
        slack_template = params.slack_template
        context = json.loads(params.context)
        mode = params.mode
        template = params.sg_template
        template_id = template.get("value", None)
        test_email = params.test_email
        subject = params.subject
        reply_to = params.reply_to
        message = params.message
        slack_channels = params.slack_channels
        owner_email_id = params.owner_email_id
        datasheet_id = params.datasheet_id
        to_ds_cols = params.to_ds_cols
        cc_ds_cols = params.cc_ds_cols
        bcc_ds_cols = params.bcc_ds_cols
        one_time = params.one_time
        one_time_keys = params.one_time_keys
        in_app_params = params.in_app_params

        cc_emails = kwargs.get("cc_emails", set())
        bcc_emails = kwargs.get("bcc_emails", set())
        column_config = kwargs.get("column_config", [])
        cols_to_hide = kwargs.get("cols_to_hide", [])
        attachment_link = kwargs.get("attachment_link", None)

        external_to_emails = set()
        external_cc_emails = set()
        external_bcc_emails = set()

        to_ds_cols = set(to_ds_cols)
        cc_ds_cols = set(cc_ds_cols)
        bcc_ds_cols = set(bcc_ds_cols)

        bcc_ds_cols = bcc_ds_cols - to_ds_cols - cc_ds_cols
        cc_ds_cols = cc_ds_cols - to_ds_cols

        embed_columns = [col for col in column_config if col not in cols_to_hide]

        if mode in {"consolidated", "consolidated_group"}:
            logger.info("Processing consolidated notification")
            resolved_subject = self.resolver.resolve(
                message=subject,
                context_info=context,
                datasheet_id=datasheet_id,
                sheet_ref=filtered_df,
                context_kwargs={
                    "get_value": {
                        "cols_to_hide": cols_to_hide,
                    }
                },
            )

            resolved_message = self.resolver.resolve(
                message=message,
                context_info=context,
                datasheet_id=datasheet_id,
                sheet_ref=filtered_df,
                context_kwargs={
                    "get_embedded_table": {
                        "embed_columns": embed_columns,
                        "cols_to_hide": cols_to_hide,
                    }
                },
            )

            valid_to_emails = to_emails.intersection(self.system_user_emails)
            valid_cc_emails = cc_emails.intersection(self.system_user_emails)
            valid_bcc_emails = bcc_emails.intersection(self.system_user_emails)

            external_to_emails = to_emails - valid_to_emails
            external_cc_emails = cc_emails - valid_cc_emails
            external_bcc_emails = bcc_emails - valid_bcc_emails

            test_header = ""
            if is_test_mode:
                test_header = self._get_test_header(
                    to_emails,
                    cc_emails,
                    bcc_emails,
                    notification_type=notification_type,
                )
                slack_channels = []
                valid_to_emails = set([test_email])
                valid_cc_emails = set([])
                valid_bcc_emails = set([])

            if not valid_to_emails and not slack_channels:
                logger.info("No valid to recipients for the consolidated notification")
                return

            if notification_type == ChannelEnum.EMAIL.value:
                logger.info("Sending consolidated email notification")
                send_workflow_email(
                    template_id=template_id,
                    subject=resolved_subject,
                    to_emails=valid_to_emails,
                    cc_recipients=valid_cc_emails,
                    bcc_recipients=valid_bcc_emails,
                    content=test_header + resolved_message,
                    attachment_link=attachment_link,
                    reply_to=reply_to,
                    test_mode=is_test_mode,
                )
            elif notification_type == ChannelEnum.SLACK.value:
                logger.info("Sending consolidated slack notification")
                post_then_message(
                    client_id=self.client_id,
                    email_ids=valid_to_emails,
                    message=test_header + resolved_message,
                    slack_template=slack_template,
                    slack_channels=slack_channels,
                    owner_email_id=owner_email_id,
                    attachment_link=attachment_link,
                )

        else:
            logger.info(
                "Processing individual notifications for %s rows",
                len(filtered_df.index),
            )
            for index, row in filtered_df.iterrows():
                resolved_message = self.resolver.resolve(
                    message=message,
                    context_info=context,
                    datasheet_id=datasheet_id,
                    sheet_ref=filtered_df,
                    row_index=index,
                    context_kwargs={
                        "get_value": {
                            "cols_to_hide": cols_to_hide,
                        }
                    },
                )
                resolved_subject = self.resolver.resolve(
                    message=subject,
                    context_info=context,
                    datasheet_id=datasheet_id,
                    sheet_ref=filtered_df,
                    row_index=index,
                    context_kwargs={
                        "get_value": {
                            "cols_to_hide": cols_to_hide,
                        }
                    },
                )

                resolved_to_emails = set([row[col] for col in to_ds_cols]) | to_emails
                resolved_cc_emails = set([row[col] for col in cc_ds_cols]) | cc_emails
                resolved_bcc_emails = (
                    set([row[col] for col in bcc_ds_cols]) | bcc_emails
                )

                (
                    resolved_to_emails,
                    resolved_cc_emails,
                    resolved_bcc_emails,
                ) = self._get_email_mutex(
                    resolved_to_emails, resolved_cc_emails, resolved_bcc_emails
                )

                valid_to_emails = resolved_to_emails.intersection(
                    self.system_user_emails
                )
                valid_cc_emails = resolved_cc_emails.intersection(
                    self.system_user_emails
                )
                valid_bcc_emails = resolved_bcc_emails.intersection(
                    self.system_user_emails
                )

                external_to_emails = resolved_to_emails - valid_to_emails
                external_cc_emails = resolved_cc_emails - valid_cc_emails
                external_bcc_emails = resolved_bcc_emails - valid_bcc_emails

                test_message = ""
                if is_test_mode:
                    test_message = self._get_test_header(
                        resolved_to_emails,
                        resolved_cc_emails,
                        resolved_bcc_emails,
                        notification_type,
                    )
                    slack_channels = []
                    valid_to_emails = set([test_email])
                    valid_cc_emails = set([])
                    valid_bcc_emails = set([])

                if not valid_to_emails and not slack_channels:
                    logger.info("No valid to recipients for the row")
                    continue

                if notification_type == ChannelEnum.EMAIL.value:
                    logger.info("Sending individual email notification")
                    send_workflow_email(
                        template_id=template_id,
                        to_emails=valid_to_emails,
                        cc_recipients=valid_cc_emails,
                        bcc_recipients=valid_bcc_emails,
                        subject=resolved_subject,
                        content=test_message + resolved_message,
                        reply_to=reply_to,
                        test_mode=is_test_mode,
                    )

                elif notification_type == ChannelEnum.SLACK.value:
                    logger.info("Sending individual slack notification")
                    post_then_message(
                        client_id=self.client_id,
                        email_ids=valid_to_emails,
                        message=test_message + resolved_message,
                        slack_template=slack_template,
                        slack_channels=slack_channels,
                        owner_email_id=owner_email_id,
                    )

                elif notification_type == ChannelEnum.IN_APP.value:
                    logger.info("Sending in-app notification")
                    send_in_app_notification(
                        client_id=self.client_id,
                        to_users=valid_to_emails,
                        subject=resolved_subject,
                        message=resolved_message,
                        in_app_params=in_app_params,
                        one_time=one_time,
                        one_time_keys=one_time_keys,
                        then_id=self.then_id,
                        row=row,
                    )

        logger.info("Invalid to emails: %s", external_to_emails)
        logger.info("Invalid cc emails: %s", external_cc_emails)
        logger.info("Invalid bcc emails: %s", external_bcc_emails)

    def _process_rbac_permissions_join(self, emails, dynamic_columns):
        logger.info(
            "Processing RBAC permissions for emails: %s, dynamic_columns: %s",
            emails,
            dynamic_columns,
        )

        emails = set(emails)
        distinct_emails = set()
        for col in dynamic_columns:
            distinct_emails_query = (
                f"SELECT DISTINCT {col} FROM '{self.input_file_location}';"
            )
            logger.info("Distinct emails query(%s): %s", col, distinct_emails_query)
            distinct_emails.update(excecute_query(distinct_emails_query)[col].tolist())

        distinct_emails = distinct_emails.union(emails)
        distinct_emails = distinct_emails.intersection(self.system_user_emails)

        logger.info("Distinct emails: %s", distinct_emails)

        filtered_rows = set()
        cols_to_be_hidden = set()
        for email in distinct_emails:
            (
                query,
                hidden_cols,
            ) = self.query_builder.get_datasheet_permissions_and_hidden_columns(
                logged_in_user_email=email, cache_key=self.then_id
            )
            query = ("where " + query) if query else ""
            logger.debug("RBAC filter query: %s", query)
            permission_df = excecute_query(
                f"SELECT row_key FROM '{self.input_file_location}' {query};"
            )
            row_keys = set(permission_df["row_key"].tolist())
            if not filtered_rows:
                filtered_rows = row_keys
            filtered_rows = filtered_rows.intersection(row_keys)
            cols_to_be_hidden.update(hidden_cols)

        filtered_rows_escaped = []
        for row_key in filtered_rows:
            modified_row_key = row_key
            if "'" in modified_row_key:
                modified_row_key = modified_row_key.replace("'", "''")
            filtered_rows_escaped.append(modified_row_key)

        row_key_filter = ", ".join(
            [f"'{row_key}'" for row_key in filtered_rows_escaped]
        )
        row_key_filter = (
            f"WHERE row_key IN ({row_key_filter})" if row_key_filter else ""
        )
        logger.debug("Row key filter: %s", row_key_filter)
        filtered_df = excecute_query(
            f"SELECT * FROM '{self.input_file_location}' {row_key_filter};"
        )

        filtered_df.to_parquet(
            self.rbac_file_location,
            index=False,
        )
        logger.info(
            "Filtered RBAC data stored in parquet file: %s", self.rbac_file_location
        )

        # Get all columns if any user lacks view_databook permission
        for email in distinct_emails:
            role_permission_ids = get_payee_role_id(self.client_id, email)
            databook_permissions = RolePermissionsAccessor(
                self.client_id
            ).get_databook_permissions(role_permission_ids)

            has_view_databook = False
            if databook_permissions and "view:databook" in databook_permissions:
                has_view_databook = True

            if not has_view_databook:
                # Get all columns and add them to cols_to_be_hidden
                cols_to_be_hidden.update(filtered_df.columns.tolist())
                break

        return list(cols_to_be_hidden)

    def act(self, test_params=None):
        is_test_mode = False

        # If the instance is not provided, the action is in test mode, else get the parameters from the block object
        if not self.block:
            is_test_mode = True
            params = test_params if test_params else {}
        else:
            params = (
                self.block.get("data", {})
                .get("component_params", {})
                .get("action", {})
                .get("params", {})
            )

        params = SendNotificationParams(**params)
        mode = params.mode
        to_emails = params.to_emails
        cc_emails = params.cc_emails
        bcc_emails = params.bcc_emails
        to_ds_cols = params.to_ds_cols
        cc_ds_cols = params.cc_ds_cols
        bcc_ds_cols = params.bcc_ds_cols
        to_user_groups = params.to_user_groups
        cc_user_groups = params.cc_user_groups
        bcc_user_groups = params.bcc_user_groups
        datasheet_id = params.datasheet_id
        databook_id = params.databook_id
        partition_obj = params.partition_by
        partition_column = partition_obj.get("value") if partition_obj else None
        column_config = params.selected_columns
        attachment_name = params.attachment_filename
        logged_in_user_email = params.owner_email_id
        slack_channels = params.slack_channels

        logger.info("Notification Action started with params: %s", params.dict())

        to_emails, cc_emails, bcc_emails = self._resolve_merge_user_groups(
            (to_emails, cc_emails, bcc_emails),
            (to_user_groups, cc_user_groups, bcc_user_groups),
        )

        logger.info(
            "Resolved emails: to_emails=%s, cc_emails=%s, bcc_emails=%s",
            to_emails,
            cc_emails,
            bcc_emails,
        )

        to_ds_cols = set(to_ds_cols)
        cc_ds_cols = set(cc_ds_cols)
        bcc_ds_cols = set(bcc_ds_cols)

        slack_channel_users = get_users_in_slack_channels(
            self.client_id, logged_in_user_email, slack_channels
        )

        logger.info(
            "Resolved slack channel users: slack_channel_users=%s",
            slack_channel_users,
        )

        to_ds_cols = to_ds_cols - cc_ds_cols - bcc_ds_cols
        cc_ds_cols = cc_ds_cols - bcc_ds_cols

        logger.info(
            "Mutually exclusive columns: to_ds_cols=%s, cc_ds_cols=%s, bcc_ds_cols=%s",
            to_ds_cols,
            cc_ds_cols,
            bcc_ds_cols,
        )

        self.query_builder = FlattenedTableQueryBuilder(
            client_id=self.client_id,
            databook_id=uuid.UUID(databook_id),
            datasheet_id=uuid.UUID(datasheet_id),
            is_duckdb_executor=True,
        )

        t1 = time()

        # Get the filtered DataFrame based on the test mode.
        if is_test_mode:
            logger.info("Notification Action in test mode")

            limit = 1 if mode == "individual" else 10
            filtered_df = generate_fake_datasheet_data(
                self.client_id, databook_id, datasheet_id, rows=limit
            )
            if filtered_df is None:
                logger.info("Empty datasheet data, skipping notifications")
                return
            filtered_df.to_parquet(self.input_file_location, index=False)

            logger.info(
                "Stored test data in parquet file: %s", self.input_file_location
            )

        t2 = time()
        logger.debug("Time taken until applying RBAC: %s", t2 - t1)

        cols_to_hide = []
        if not is_test_mode:
            cols_to_hide = self._process_rbac_permissions_join(
                list(to_emails | cc_emails | bcc_emails | slack_channel_users),
                list(to_ds_cols | cc_ds_cols | bcc_ds_cols),
            )
        else:
            logger.info("Skipping RBAC checks since in test mode")
            self.rbac_file_location = self.input_file_location

        logger.info("Columns to be hidden: %s", cols_to_hide)

        t3 = time()
        logger.debug("Time taken to apply RBAC: %s", t3 - t2)

        filtered_df = pd.DataFrame()
        attachment_link = None
        where_clauses = [""]
        if mode == "consolidated_group":
            where_clauses = []
            logger.info(
                "Executing query for distinct partition ids: %s",
                f"SELECT DISTINCT {partition_column} FROM '{self.rbac_file_location}'",
            )
            distinct_partition_ids = excecute_query(
                f"SELECT DISTINCT {partition_column} FROM '{self.rbac_file_location}';"
            )[partition_column].tolist()

            if len(distinct_partition_ids) > MAX_EMAIL_ROWS:
                logger.info(
                    "Distinct partition ids greater than 100, Skipping consolidated notifications"
                )
                raise TooManyNotificationGroupsError(
                    [{"block": self.block if self.block else {}}]
                )
            for partition_id in distinct_partition_ids:
                if partition_id is None:
                    continue

                where_clauses.append(f"WHERE {partition_column} = '{partition_id}'")

        logger.info("Where clauses: %s", where_clauses)

        s3_handler = S3Uploader(
            bucket_name=os.environ.get("S3_PVT_ASSETS_BUCKET", None)
        )
        buffer_file = None
        response = None

        all_no_records_errors = []
        all_more_than_100_records_errors = []
        for where_clause in where_clauses:
            if_where = ""
            if self.parse_file_location:
                if_where = (
                    f"AND res LIKE '%{str(self.block_id)}%'"
                    if where_clause
                    else f"WHERE res LIKE '%{str(self.block_id)}%'"
                )
                base_query = f"SELECT * FROM '{self.rbac_file_location}' {where_clause} {if_where}"
            else:
                base_query = f"SELECT * FROM '{self.rbac_file_location}' {where_clause}"

            unique_id = str(uuid.uuid4())
            s3_location = f"workflow_builder/then_attachments/{self.client_id}/{self.then_id}/{unique_id}.csv"
            rows, limit = estimate_chunk_size(
                self.rbac_file_location, f"{where_clause} {if_where}"
            )

            if rows == 0:
                logger.info("No rows to process for %s", where_clause)
                all_no_records_errors.append(
                    {
                        "block": self.block,
                        "record": extract_partition_id(where_clause),
                    }
                )
                continue

            if rows > MAX_EMAIL_ROWS and mode == "individual":
                logger.info(
                    "Rows greater than 100, Skipping individual notifications for %s",
                    where_clause,
                )
                all_more_than_100_records_errors.append(
                    {
                        "block": self.block,
                        "record": extract_partition_id(where_clause),
                    }
                )
                continue

            upload_id = s3_handler.initiate_multi_part_upload(s3_location)
            buffer_file = self.folder_location + "/temp.csv"
            parts = []

            logger.info("Base query: %s", base_query)
            for offset in range(0, rows, limit):
                filtered_df = excecute_query(
                    f" {base_query} LIMIT {limit} OFFSET {offset};"
                )
                filtered_df = replace_nan_nat_with_none_dataframe(filtered_df)
                partition_value = set()
                if mode == "consolidated_group" and partition_column:
                    partition_value = (
                        set([filtered_df[partition_column].iloc[0]])
                        if not filtered_df.empty
                        else set()
                    )
                if mode == "individual":
                    self._process_notification(
                        params=params,
                        is_test_mode=is_test_mode,
                        filtered_df=filtered_df,
                        to_emails=to_emails,
                        cc_emails=cc_emails,
                        bcc_emails=bcc_emails,
                        column_config=column_config,
                        cols_to_hide=cols_to_hide,
                    )
                else:
                    ds_var_obj = DatasheetVariableAccessor(self.client_id)
                    if not column_config:
                        ds_vars = list(
                            ds_var_obj.get_ds_variables_for_datasheet(datasheet_id)
                        )
                        column_config = [var["system_name"] for var in ds_vars]
                    else:
                        ds_vars = list(
                            ds_var_obj.get_datatype_for_given_datasheet_variables(
                                datasheet_id, column_config
                            )
                        )

                    df_cols = set(filtered_df.columns)
                    var_sys_display_map = {
                        var["system_name"]: var["display_name"] for var in ds_vars
                    }

                    attachment_df = pd.DataFrame()
                    cols_to_show = []
                    for col in column_config:
                        if col in cols_to_hide or col not in df_cols:
                            continue
                        cols_to_show.append(col)
                        display_name = var_sys_display_map.get(col, col)
                        attachment_df[display_name] = filtered_df[col]

                    logger.info("Columns to be attached: %s", filtered_df.columns)

                    parts = s3_handler.collect_upload_multi_part(
                        parts, upload_id, s3_location, attachment_df, buffer_file
                    )

            t4 = time()
            logger.debug("Time taken to preprocess: %s", t4 - t3)

            if mode != "individual" and filtered_df.shape[0] > 0:
                file_path = Path(buffer_file)
                if len(parts) > 1:
                    logger.info("Completing multi part upload in %s", s3_location)
                    if file_path.exists():
                        csv_data = pd.read_csv(buffer_file).to_csv(index=False)
                        last_part = s3_handler.upload_part(
                            len(parts) + 1, upload_id, s3_location, csv_data
                        )
                        parts.append(last_part)

                    response = s3_handler.complete_multi_part_upload(
                        upload_id, s3_location, parts
                    )

                else:
                    logger.info("Uploading as single part in %s", s3_location)
                    attachment_df.to_csv(buffer_file, index=False)
                    response = s3_handler.upload_file_direct(
                        local_file=buffer_file, s3_file_path=s3_location
                    )
                if file_path.exists():
                    file_path.unlink()

                if response:
                    logger.info(
                        "Generating presigned url for attachment file %s", s3_location
                    )
                    attachment_link = s3_handler.generate_presigned_url(
                        file_path=s3_location,
                        download_file_name=attachment_name,
                        expiration=60 * 60 * 24 * 30,
                    )

                add_partition_to_email = partition_column in to_ds_cols
                add_partition_to_cc = partition_column in cc_ds_cols
                add_partition_to_bcc = partition_column in bcc_ds_cols

                self._process_notification(
                    params=params,
                    is_test_mode=is_test_mode,
                    filtered_df=filtered_df,
                    to_emails=(
                        (to_emails | partition_value)
                        if add_partition_to_email
                        else to_emails
                    ),
                    cc_emails=(
                        (cc_emails | partition_value)
                        if add_partition_to_cc
                        else cc_emails
                    ),
                    bcc_emails=(
                        (bcc_emails | partition_value)
                        if add_partition_to_bcc
                        else bcc_emails
                    ),
                    attachment_link=attachment_link,
                    column_config=cols_to_show,
                )
            else:
                logger.info("No rows to process for %s mode", mode)
            logger.debug("Time taken to process notifications: %s", time() - t4)

        if all_no_records_errors:
            raise EmptyWorkflowNotificationError(all_no_records_errors)
        if all_more_than_100_records_errors:
            raise TooManyNotificationRecordsError(all_more_than_100_records_errors)


def extract_partition_id(where_clause):
    """Extract partition_id (email) from where clause or return None if empty"""
    if not where_clause:
        return None

    # Simple string split approach instead of regex
    # Example where_clause: "WHERE email = '<EMAIL>'"
    try:
        # Split on = and get the right part
        value_part = where_clause.split("=")[1].strip()
        # Remove quotes and extra whitespace
        partition_id = value_part.strip("' ")
        return partition_id
    except (IndexError, AttributeError):
        return None
