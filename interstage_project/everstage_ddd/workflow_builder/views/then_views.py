import os

import requests
from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from everstage_ddd.workflow_builder.exceptions import WorkflowApiError
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import handle_ever_exception
from slack_everstage.services.commission_services.workflow_notification_service import (
    get_slack_context,
)

from ..actions.notification_action import NotificationAction
from ..smart_tags.resolver import SmartResolver

# class ThenApiError(Exception):
#     pass


class TestNotificationView(APIView):
    """
    Send a test notification to the user
    params: {
        notification_type: "email" or "slack",
        ...other params for NotificationAction
        }
    """

    @handle_ever_exception
    def post(self, request):
        params = request.data
        client_id = request.client_id
        try:
            NotificationAction(
                block=None,
                rule=None,
                instance=None,
                config_params={"client_id": client_id},
            ).act(test_params=params)
            return Response({"status": "SUCCESS"}, status=status.HTTP_200_OK)
        except Exception as err:
            return Response({"error": str(err)}, status=status.HTTP_400_BAD_REQUEST)


class SmartTagView(APIView):
    """
    Get the smart schema for the given entity
    params: {
        entity: "datasheet_data",
        datasheet_id: "datasheet_id",
        }
    response: {
        context_list: ["context1", "context2", ...],
        meta: {
            context_col_map: {"context1": "col1", "context2": "col2", ...}
        }
    }
    """

    @handle_ever_exception
    def get(self, request):
        client_id = request.client_id
        entity = request.GET.get("entity")
        datasheet_id = request.GET.get("datasheet_id", None)
        context = SmartResolver.generate_smart_schema(
            entity=entity,
            client_id=client_id,
            datasheet_id=datasheet_id,
            flat=True,
        )
        return Response(context, status=status.HTTP_200_OK)


class SmartTagResolveView(APIView):
    """
    Resolves the smart tags in the `messages` with fake data generated based on
    the datasheet schema

    params: {
        "databook_id": "...",
        "datasheet_id": "...",
        "context": "...",
        "messages": [
            "...", "..."
        ],
    }

    response: {
        "resolved_messages": [
            "...", "..."
        ]
    }
    """

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ROLES.value), name="dispatch"
    )
    @handle_ever_exception
    def post(self, request):
        from ..types import IN_APP_MESSAGE_LENGTH_LIMIT, IN_APP_SUBJECT_LENGTH_LIMIT
        from ..utils.enums import ChannelEnum

        try:
            resolved_messages = SmartResolver(request.client_id).resolve_multiple(
                request.data.get("databook_id"),
                request.data.get("datasheet_id"),
                request.data.get("context"),
                request.data.get("messages"),
            )
            if request.data.get("notification_type") == ChannelEnum.IN_APP.value:
                # Truncate the subject and message to the maximum allowed length
                subject, message = resolved_messages[0], resolved_messages[1]
                subject = subject[:IN_APP_SUBJECT_LENGTH_LIMIT]
                message = message[:IN_APP_MESSAGE_LENGTH_LIMIT]
                resolved_messages = [subject, message]

            return Response({"resolved_messages": resolved_messages})
        except WorkflowApiError as e:
            return Response(
                {
                    "error": {
                        "code": e.code,
                        "message": e.message,
                        "payload": e.payload,
                    }
                },
                status=e.status,
            )


class SendGridTemplateView(APIView):
    """
    Get the list of dynamic templates from SendGrid
    response: {
        template_id: "template_name",
        ...
    }
    """

    @handle_ever_exception
    def get(self, request):  # noqa: ARG002
        api_key = os.environ.get("SENDGRID_API_KEY")
        url = "https://api.sendgrid.com/v3/templates?generations=dynamic"

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }

        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code == status.HTTP_200_OK:
            dynamic_templates = response.json().get("templates", [])
            template_map = {}

            if dynamic_templates:
                for template in dynamic_templates:
                    template_map[template["id"]] = template["name"]
        else:
            return Response(
                {"error": "Error while fetching templates"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        return Response(template_map, status=status.HTTP_200_OK)


class SlackView(APIView):
    """
    Get the list of channels where the bot is a member.
    params: {
        email_id: "user_email_id",
        }
    response: [
        {
            name: "channel_name",
            id: "channel_id",
            is_private: true/false
        },
        ...
    ]
    """

    @handle_ever_exception
    def get(self, request):
        client_id = request.client_id
        email_id = request.GET.get("email_id")

        context = get_slack_context(client_id, email_id)

        if not isinstance(context, tuple):
            return Response({"error": context}, status=status.HTTP_404_NOT_FOUND)

        client, _ = context

        # Fetch the list of private and public channels for the user
        response = client.conversations_list(types="private_channel,public_channel")

        channels = [
            {
                "name": channel["name"],
                "id": channel["id"],
                "is_private": channel["is_private"],
            }
            for channel in response["channels"]
            if channel["is_member"]
        ]

        return Response(channels, status=status.HTTP_200_OK)
