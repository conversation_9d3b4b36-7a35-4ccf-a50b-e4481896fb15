import logging

from django.db import transaction
from django.utils.decorators import method_decorator
from pydantic import ValidationError
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import handle_ever_exception

from ..service.workflow_history import get_all_workflow_instances
from ..service.workflow_service import (
    clone_workflow,
    create_workflow,
    delete_workflow,
    edit_workflow,
    edit_workflow_name,
    get_more_details,
    get_workflow,
    get_workflows,
    publish_workflow,
    run_workflow,
    validate_workflow,
    workflow_activation,
)
from ..types import WorkflowHistoryParams

logger = logging.getLogger(__name__)


class Triggers(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    def get(self):
        datasheet_changes = {
            "label": "Datasheet Changes",
            "key": "1",
            "triggers": [
                {"label": "variable added", "value": "variable added"},
                {"label": "spec changed", "value": "spec changed"},
                {"label": "variable deleted", "value": "variable deleted"},
            ],
        }
        datasheet_data_changes = {
            "label": "Datasheet Data Changes",
            "key": "2",
            "triggers": [
                {"label": "records added", "value": "records added"},
                {"label": "records deleted", "value": "records deleted"},
            ],
        }

        trigger_list = [datasheet_changes, datasheet_data_changes]
        return Response(trigger_list, status=status.HTTP_200_OK)


class Workflow(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @transaction.atomic
    @handle_ever_exception
    def post(self, request):
        params = request.data
        client_id = request.client_id
        user = request.user.username
        params["additional_details"] = request.audit
        params["created_by"] = user
        res = create_workflow(client_id, params)
        if res["status"] == "ERROR":
            return Response(res, status=status.HTTP_400_BAD_REQUEST)
        return Response(res, status=status.HTTP_200_OK)

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    def get(self, request):
        client_id = request.client_id
        workflow_id = request.GET.get("wf_id")
        if workflow_id is None:
            res = get_workflows(client_id, order_by="knowledge_begin_date")
            return Response(res, status=status.HTTP_200_OK)
        res = get_workflow(client_id, workflow_id, as_dict=True)
        nodes = res["components"]["nodes"]
        edges = res["components"]["edges"]
        (validated, validate_errors) = validate_workflow(client_id, nodes, edges)
        result = {
            "data": res,
            "validate": validated,
            "validate_errors": validate_errors,
        }
        return Response(result, status=status.HTTP_200_OK)

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @transaction.atomic
    @handle_ever_exception
    def patch(self, request):
        params = request.data
        client_id = request.client_id
        params["additional_details"] = request.audit
        res = edit_workflow(client_id, params)
        if res["status"] == "ERROR":
            return Response(res, status=status.HTTP_400_BAD_REQUEST)
        return Response(res, status=status.HTTP_200_OK)


class WorkflowDetails(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    def get(self, request):
        client_id = request.client_id
        workflow_id = request.GET.get("wf_id")
        if workflow_id:
            res = get_more_details(client_id, workflow_id)
            return Response(res, status=status.HTTP_200_OK)
        return Response({"status": "ERROR"}, status=status.HTTP_400_BAD_REQUEST)


class WorkflowHistory(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    def post(self, request):
        """
        Get all workflow instances and their status count.
        """
        client_id = request.client_id
        params = request.data

        try:
            validated_params = WorkflowHistoryParams(**params)
            res = get_all_workflow_instances(client_id, validated_params)
            return Response(res, status=status.HTTP_200_OK)
        except ValidationError:
            logger.exception("Workflow History Params Validation Error")
            return Response(
                {"status": "ERROR", "message": "Invalid params"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class WorkflowActivation(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @transaction.atomic
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        params = request.data
        workflow_id = params.get("wf_id")
        activation_flag = params.get("is_active")
        if workflow_id:
            res = workflow_activation(client_id, workflow_id, activation_flag)
            if res["status"] == "ERROR":
                return Response(res, status=status.HTTP_400_BAD_REQUEST)
            return Response(res, status=status.HTTP_200_OK)
        return Response({"status": "ERROR"}, status=status.HTTP_400_BAD_REQUEST)


class PublishWorkflow(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @transaction.atomic
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        params = request.data
        workflow_id = params.get("wf_id")
        draft_flag = params.get("is_draft")
        if workflow_id:
            res = publish_workflow(client_id, workflow_id, draft_flag)
            return Response(res, status=status.HTTP_200_OK)
        return Response({"status": "ERROR"}, status=status.HTTP_400_BAD_REQUEST)


class DeleteWorkflow(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @transaction.atomic
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        params = request.data
        workflow_id = params.get("wf_id")
        if workflow_id:
            res = delete_workflow(client_id, workflow_id)
            return Response(res, status=status.HTTP_200_OK)
        return Response({"status": "ERROR"}, status=status.HTTP_400_BAD_REQUEST)


class CloneWorkflow(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @transaction.atomic
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        params = request.data
        user = request.user.username
        params["additional_details"] = request.audit
        params["created_by"] = user
        workflow_id = params.get("wf_id")
        if workflow_id:
            res = clone_workflow(client_id, workflow_id, params)
            if res["status"] == "ERROR":
                return Response(res, status=status.HTTP_400_BAD_REQUEST)
            return Response(res, status=status.HTTP_200_OK)
        return Response({"status": "ERROR"}, status=status.HTTP_400_BAD_REQUEST)


class EditWorkflowName(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @transaction.atomic
    @handle_ever_exception
    def post(self, request):
        params = request.data
        client_id = request.client_id
        params["additional_details"] = request.audit
        res = edit_workflow_name(client_id, params)
        if res["status"] == "ERROR":
            return Response(res, status=status.HTTP_400_BAD_REQUEST)
        return Response(res, status=status.HTTP_200_OK)


class RunWorkflow(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    def post(self, request):
        client_id = request.client_id
        params = request.data
        res = run_workflow(client_id, params)
        return Response(res, status=status.HTTP_200_OK)
