from django.db import transaction
from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import handle_ever_exception

from ..service.custom_trigger_services import (
    create_custom_trigger,
    create_trigger_category,
    edit_custom_trigger,
    get_category_wise_triggers,
    get_custom_trigger,
    get_custom_triggers,
    get_trigger_categories,
)

# class CustomTriggerApiError(Exception):
#     pass


class CustomTrigger(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @transaction.atomic
    @handle_ever_exception
    def post(self, request):
        params = request.data
        client_id = request.client_id
        user = request.user.username
        params["additional_detail"] = request.audit
        params["created_by"] = user
        res = create_custom_trigger(client_id, params)
        if res["status"] == "ERROR":
            return Response(res, status=status.HTTP_400_BAD_REQUEST)
        return Response(res, status=status.HTTP_200_OK)

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    def get(self, request):
        client_id = request.client_id
        custom_trigger_id = request.GET.get("custom_trigger_id")
        if custom_trigger_id is None:
            res = get_custom_triggers(client_id)
        else:
            res = get_custom_trigger(client_id, custom_trigger_id)
        return Response(res, status=status.HTTP_200_OK)

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @transaction.atomic
    @handle_ever_exception
    def patch(self, request):
        params = request.data
        client_id = request.client_id
        params["additional_detail"] = request.audit
        res = edit_custom_trigger(client_id, params)
        if res["status"] == "ERROR":
            return Response(res, status=status.HTTP_400_BAD_REQUEST)
        return Response(res, status=status.HTTP_200_OK)


class TriggerCategory(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @transaction.atomic
    @handle_ever_exception
    def post(self, request):
        params = request.data
        client_id = request.client_id
        user = request.user.username
        params["additional_detail"] = request.audit
        params["created_by"] = user
        res = create_trigger_category(client_id, params)
        if res["status"] == "ERROR":
            return Response(res, status=status.HTTP_400_BAD_REQUEST)
        return Response(res, status=status.HTTP_200_OK)

    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    def get(self, request):
        client_id = request.client_id
        res = get_trigger_categories(client_id)
        return Response(res, status=status.HTTP_200_OK)


class CategoryWiseTrigger(APIView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_ROLES.value,
            ]
        ),
        name="dispatch",
    )
    @handle_ever_exception
    def get(self, request):
        client_id = request.client_id
        res = get_category_wise_triggers(client_id)
        return Response(res, status=status.HTTP_200_OK)
