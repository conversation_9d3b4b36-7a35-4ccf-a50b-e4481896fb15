from .decorators import ROW_SELECTOR_INDEX


def parse_col_key(key):
    return "col" + "".join(e for e in key if e.isalnum()) + "#row"


# Converts a list of keys to a nested dictionary
def list_to_dict(keys, parent):
    current_dict = parent
    for key in keys[:-1]:
        current_dict = current_dict.setdefault(key, {})

    return current_dict


# Transforms a context dictionary to a list of tuples
def transform_context_dict_to_list(data, parent_key="", result=None):
    if result is None:
        result = []

    # Recursively process nested dictionaries and append the results to the list
    for key, value in data.items():
        new_key = f"{parent_key}.{key}" if parent_key else key

        if isinstance(value, dict):
            transform_context_dict_to_list(value, new_key, result)
        else:
            new_key = "{{" + new_key + "}}"
            result.append((new_key, value))

    return result


# Transforms a list of tuples to a context dictionary
def transform_context_list_to_dict(data_list, message, context_kwargs):
    result = {}

    # Process each tuple in the list and build the dictionary
    for item in data_list:
        current_dict = result
        key = item[0]
        value = item[1][0]
        key = key.replace("{{", "").replace("}}", "")
        keys = key.split(".", 1)
        for k in keys[:-1]:
            # If the key contains a double quote, assume it's a column key and parse it, replacing it in the message
            if '"' in k:
                new_key = parse_col_key(k)
                message = message.replace(k, new_key)
            current_dict = current_dict.setdefault(k, {})

        if '"' in keys[-1]:
            new_key = parse_col_key(keys[-1])
            message = message.replace(keys[-1], new_key)
            keys[-1] = new_key

        if value["name"] in context_kwargs:
            value["kwargs"] = context_kwargs[value["name"]]

        current_dict[keys[-1]] = [value]

    return result, message


# Processes the context and returns a dictionary with resolved values
def process_context(schema_object, context):
    result = {}
    for key, value in context.items():
        con_key = key
        if ROW_SELECTOR_INDEX in key:
            con_key = key.replace(ROW_SELECTOR_INDEX, "")
        if isinstance(value, list):
            # If the value is a list, assume it's a callable and call the corresponding method
            item = value[0]
            method_name = item.get("name")
            method_args = item.get("args", [])
            method_kwargs = item.get("kwargs", {})
            method = getattr(schema_object, method_name, None)
            if callable(method):
                result[con_key] = method(*method_args, **method_kwargs)
            else:
                result[con_key] = None
        elif isinstance(value, dict):
            # Recursively process nested dictionaries
            result[con_key] = process_context(schema_object, value)
        else:
            # Otherwise, assume it's a string and leave it unchanged
            result[con_key] = value

    return result
