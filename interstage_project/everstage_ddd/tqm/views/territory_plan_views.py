import logging
import traceback

from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from everstage_ddd.tqm.services.territory_plan_services import (
    clone_territory_plan,
    create_territory_plan,
    delete_territory_plan,
    get_all_territory_plans,
    get_territory_plan,
    implement_territory_plan,
    update_territory_plan,
)
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import add_log_context_view

logger = logging.getLogger(__name__)


class TerritoryPlansView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_TERRITORY_PLANS.value), name="dispatch"
    )
    @add_log_context_view("TerritoryPlanList")
    def get(self, request):
        try:
            client_id = request.client_id
            selected_year = request.query_params.get("selectedYear", None)
            selected_status = request.query_params.get("selectedStatus", None)
            filter_params = {}
            if selected_year is not None:
                filter_params["selected_year"] = selected_year
            if selected_status is not None:
                filter_params["selected_status"] = selected_status
            res = get_all_territory_plans(client_id, **filter_params)
            return Response(res)
        except Exception:
            traceback.print_exc()
            return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ALLADMINS.value), name="dispatch"
    )
    @add_log_context_view("CreateTerritoryPlan")
    def post(self, request):
        try:
            client_id = request.client_id
            res = create_territory_plan(client_id, request.data)
            return Response(res, status=status.HTTP_201_CREATED)
        except Exception as e:
            logger.exception(
                "Failed to create territory plan",
                extra={"exception_tag": "TQM", "error": str(e)},
            )
            return Response(
                {"status": "ERROR", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class TerritoryPlanView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_TERRITORY_PLANS.value), name="dispatch"
    )
    @add_log_context_view("ViewTerritoryPlan")
    def get(self, request, plan_id):
        try:
            client_id = request.client_id
            logged_in_user = request.user
            email = request.user.username
            response_data = get_territory_plan(
                client_id,
                {
                    "tplan_id": plan_id,
                    "external_user_id": email,
                    "logged_in_user": logged_in_user,
                },
            )
            return Response(response_data)
        except Exception as e:
            logger.exception(
                "Failed to fetch territory plan embed URL",
                extra={"exception_tag": "TQM", "plan_id": plan_id, "error": str(e)},
            )
            return Response(
                {"status": "ERROR", "message": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ALLADMINS.value), name="dispatch"
    )
    @add_log_context_view("DeleteTerritoryPlan")
    def delete(self, request, plan_id):
        try:
            client_id = request.client_id
            res = delete_territory_plan(client_id, plan_id)
            return Response(res, status=status.HTTP_200_OK)
        except Exception as e:
            logger.exception(
                "Failed to delete territory plan",
                extra={"exception_tag": "TQM", "plan_id": plan_id, "error": str(e)},
            )
            return Response(
                {"status": "ERROR", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ALLADMINS.value), name="dispatch"
    )
    @add_log_context_view("CloneTerritoryPlan")
    def post(self, request, plan_id):
        try:
            client_id = request.client_id
            res = clone_territory_plan(client_id, plan_id)
            return Response(res, status=status.HTTP_201_CREATED)
        except Exception as e:
            logger.exception(
                "Failed to clone territory plan",
                extra={"exception_tag": "TQM", "error": str(e)},
            )
            return Response(
                {"status": "ERROR", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ALLADMINS.value), name="dispatch"
    )
    @add_log_context_view("UpdateTerritoryPlan")
    def patch(self, request, plan_id):
        try:
            client_id = request.client_id
            update_territory_plan(client_id, plan_id, request.data)
            return Response(status=status.HTTP_201_CREATED)
        except Exception as e:
            logger.exception(
                "Failed to update territory plan",
                extra={"exception_tag": "TQM", "plan_id": plan_id, "error": str(e)},
            )
            return Response(
                {"status": "ERROR", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class ImplementTerritoryPlanView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ALLADMINS.value), name="dispatch"
    )
    @add_log_context_view("ImplementTerritoryPlan")
    def post(self, request, tplan_id):
        """
        Implement a territory plan by setting up necessary user attributes and returning the workbook URL.
        """
        try:
            client_id = request.client_id
            implementer_email = request.query_params.get("implementer_email")
            workbook_url = implement_territory_plan(
                client_id, tplan_id, implementer_email=implementer_email
            )
            return Response(
                {"status": "SUCCESS", "workbook_url": workbook_url},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logger.exception(
                "Failed to implement territory plan",
                extra={"exception_tag": "TQM", "tplan_id": tplan_id, "error": str(e)},
            )
            return Response(
                {"status": "ERROR", "message": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )
