{"info": {"name": "Everstage TQM APIs", "description": "Collection of APIs for Territory Quota Management (TQM)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Territory Plans", "description": "Endpoints for managing territory plans", "item": [{"name": "Get All Territory Plans", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/tqm/territory-plans?selectedYear={{selectedYear}}&selectedStatus={{selectedStatus}}", "host": ["{{base_url}}"], "path": ["tqm", "territory-plans"], "query": [{"key": "selected<PERSON>ear", "value": "{{selectedYear}}", "description": "Filter by year (optional)"}, {"key": "selectedStatus", "value": "{{selectedStatus}}", "description": "Filter by status (optional)"}]}, "description": "Retrieve a list of all territory plans, with optional filtering by year and status"}}, {"name": "Create Territory Plan", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/tqm/territory-plans", "host": ["{{base_url}}"], "path": ["tqm", "territory-plans"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Territory Plan 2023\",\n  \"description\": \"Annual territory plan for 2023\",\n  \"year\": 2023\n}", "options": {"raw": {"language": "json"}}}, "description": "Create a new territory plan"}}, {"name": "Get Territory Plan Details", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/tqm/territory-plans/{{plan_id}}", "host": ["{{base_url}}"], "path": ["tqm", "territory-plans", "{{plan_id}}"]}, "description": "Get detailed information about a specific territory plan"}}, {"name": "Update Territory Plan", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/tqm/territory-plans/{{plan_id}}", "host": ["{{base_url}}"], "path": ["tqm", "territory-plans", "{{plan_id}}"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Territory Plan 2023\",\n  \"description\": \"Updated description for territory plan\",\n  \"status\": \"draft\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Update an existing territory plan"}}, {"name": "Delete Territory Plan", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/tqm/territory-plans/{{plan_id}}", "host": ["{{base_url}}"], "path": ["tqm", "territory-plans", "{{plan_id}}"]}, "description": "Delete a territory plan"}}, {"name": "Clone Territory Plan", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/tqm/territory-plans/{{plan_id}}/copy", "host": ["{{base_url}}"], "path": ["tqm", "territory-plans", "{{plan_id}}", "copy"]}, "description": "Create a copy of an existing territory plan"}}, {"name": "Implement Territory Plan (TEMPORARY)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/tqm/territory-plans/{{tplan_id}}/implement?implementer_email={{implementer_email}}", "host": ["{{base_url}}"], "path": ["tqm", "territory-plans", "{{tplan_id}}", "implement"], "query": [{"key": "implementer_email", "value": "{{implementer_email}}", "description": "Email of the user implementing the plan (optional)"}]}, "description": "Implement a territory plan by setting up necessary user attributes and returning the workbook URL"}}]}]}