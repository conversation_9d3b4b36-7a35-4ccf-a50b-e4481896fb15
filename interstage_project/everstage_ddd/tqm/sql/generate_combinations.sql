CREATE OR REPLACE PROCEDURE GENERATE_COMBINATIONS(
    CLIENT_ID NUMBER,
    CLIENT_DB VARCHAR,
    TPLAN_ID VARCHAR,
    TABLE_NAME VARCHAR,
    COLUMNS VARCHAR
)
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
PACKAGES = ('snowflake-snowpark-python')
HANDLER = 'generate_combinations'
AS
$$
import traceback
import uuid
import re

MAX_LEVELS = 10

def resolve_string(string: str) -> str:
    string = string.replace("-", "_")
    string = re.sub(r"([a-z])([0-9])", r"\1_\2", string)
    string = re.sub(r"([0-9])([a-z])", r"\1_\2", string)
    return string

class GenerateCombinationsError(Exception):
    def __init__(self, reason):
        super().__init__(reason)

def generate_combinations(
    session,
    client_id: int,
    client_db: str,
    tplan_id: str,
    table_name: str,
    columns: str
):
    log_id = str(uuid.uuid4())
    log_table = f'{client_db}.public."sigma_logs_{client_id}_{tplan_id}"'
    session.sql(f"""
        INSERT INTO {log_table} (log_id, action_name, action_args)
        select
            '{log_id}',
            'generate_combinations',
            object_construct(
                'client_id', {client_id},
                'client_db', '{client_db}',
                'tplan_id', '{tplan_id}',
                'table_name', '{table_name}',
                'columns', '{columns}'
            )
    """).collect()

    try:
        session.sql("BEGIN").collect()
        
        # Validate inputs
        if not table_name:
            raise GenerateCombinationsError("Table name cannot be empty")
            
        if not columns:
            raise GenerateCombinationsError("Columns cannot be empty")
        
        # Split columns into a list
        columns_list = [col.strip() for col in columns.split(',') if col]
        
        if len(columns_list) > MAX_LEVELS:
            raise GenerateCombinationsError(f"Cannot process more than {MAX_LEVELS} columns")
            
        table_name = table_name.upper()
        if table_name not in ["USERS", "HIERARCHY"]:
            raise GenerateCombinationsError(f"Table '{table_name}' is not supported")
        
        # Create combinations table if it doesn't exist
        combinations_table = f'{client_db}.public."combinations_{client_id}_{tplan_id}"'

        # Generate SQL for cross join to create all combinations
        # First create CTEs for each column's distinct values
        cte_list = []
        for i, column in enumerate(columns_list):
            alias = f"col{i+1}_values"
            _table = None
            _col = None
            if table_name == "USERS":
                _table = f'"ds_users_{client_id}_{tplan_id}"'
                _col = column
            elif table_name == "HIERARCHY":
                _table, _col = column.split(".")
            cte_list.append(f"""
            {alias} AS (
                SELECT DISTINCT {_col} AS val
                FROM {client_db}.public.{_table}
                WHERE {_col} IS NOT NULL
                ORDER BY {_col}
            )""")
        
        # Build the FROM clause with cross joins
        from_clause_parts = []
        for i in range(len(columns_list)):
            alias = f"col{i+1}_values"
            table_alias = f"c{i+1}"
            if i == 0:
                from_clause_parts.append(f"{alias} {table_alias}")
            else:
                from_clause_parts.append(f"CROSS JOIN {alias} {table_alias}")
        
        # Build the SELECT clause
        select_clause_parts = []
        for i in range(MAX_LEVELS):
            if i < len(columns_list):
                select_clause_parts.append(f"c{i+1}.val AS L_{i+1}")
            else:
                select_clause_parts.append(f"NULL AS L_{i+1}")
        
        # Create temp table with all combinations
        temp_table = f'temp_combinations_{log_id.replace("-", "_")}'
        cross_join_query = f"""
        CREATE TABLE {temp_table} AS
        WITH 
        {', '.join(cte_list)}
        SELECT
            '{table_name}' AS TABLE_NAME,
            '{columns}' AS COLUMNS,
            {', '.join(select_clause_parts)}
        FROM
            {' '.join(from_clause_parts)}
        """
        session.sql(cross_join_query).collect()
        
        # Execute merge operation using the temp table
        merge_query = f"""
        MERGE INTO {combinations_table} target
        USING {temp_table} source
        ON target.TABLE_NAME = source.TABLE_NAME AND target.COLUMNS = source.COLUMNS
        WHEN MATCHED THEN
            UPDATE SET
                {', '.join([f'L_{i+1} = source.L_{i+1}' for i in range(MAX_LEVELS)])}
        WHEN NOT MATCHED THEN
            INSERT (TABLE_NAME, COLUMNS, {', '.join([f'L_{i+1}' for i in range(MAX_LEVELS)])})
            VALUES (source.TABLE_NAME, source.COLUMNS, {', '.join([f'source.L_{i+1}' for i in range(MAX_LEVELS)])})
        """
        session.sql(merge_query).collect()
        
        # Drop temporary table
        session.sql(f"DROP TABLE IF EXISTS {temp_table}").collect()
        
        session.sql("COMMIT").collect()
        
        # Return success message
        res = f"Successfully generated combinations"
        
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'success',
                action_result = object_construct(
                    'message', '{res}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        
        return res

    except GenerateCombinationsError as e:
        session.sql("ROLLBACK").collect()
        res = f"Error while generating combinations: {e}"

        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'message', '{res.replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return res

    except Exception:
        session.sql("ROLLBACK").collect()
        err = traceback.format_exc()
        res = f"Unexpected error while generating combinations: {err}"

        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'message', '{res.replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return res
$$; 