create or replace procedure derive_partial_scenario_v2(        
    client_id int,
    client_db varchar,
    tplan_id varchar,
    scenario_id varchar
)
returns string
language python
runtime_version = '3.9'
packages = ('snowflake-snowpark-python')
handler = 'derive_partial_scenario'
as
$$
def derive_partial_scenario(
    session,
    client_id: int,
    client_db: str,
    tplan_id: str,
    scenario_id: str
) -> str:
    scenario_table = f'{client_db}.public."scenarios_{client_id}_{tplan_id}"'
    query = f"select * from {scenario_table} where scenario_id = '{scenario_id}'"
    result = session.sql(query).collect()
    if not result:
        raise Exception(f"Scenario {scenario_id} not found")
    scenario = result[0]
    if scenario['IS_PARTIALLY_IMPLEMENTED']:
        return scenario_id
    get_query = f"""
    select * from {scenario_table}
    where source_scenario_id = '{scenario_id}' and is_partially_implemented = true
    """
    res = session.sql(get_query).collect()
    if res:
        return res[0]["SCENARIO_ID"]
    res = session.sql(f"""
        call clone_scenario_v2(
            {client_id},
            '{client_db}',
            '{tplan_id}',
            '{scenario_id}'
        )
    """).collect()
    new_scenario_id = res[0][0]
    return new_scenario_id
$$;