CREATE OR R<PERSON>LACE PROCEDURE GENERATE_HIERARCHY(
    CLIENT_ID NUMBER,
    CLIENT_DB VARCHAR,
    TPLAN_ID VARCHAR,
    <PERSON><PERSON><PERSON>IO_ID VARCHAR,
    L_1 VARCHAR,
    L_2 VARCHAR,
    L_3 VARCHAR,
    L_4 VARCHAR,
    L_5 VARCHA<PERSON>,
    L_6 VARCHAR,
    L_7 VARCHAR,
    L_8 VARCHAR,
    L_9 VARCHAR,
    L_10 VARCHAR,
    WRITE_DB VARCHAR,
    WRITE_SCHEMA VARCHAR,
    ACCOUNTS_PRIMARY_KEY_COLUMN VARCHAR
)
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
PACKAGES = ('snowflake-snowpark-python', 'networkx', 'pandas')
HANDLER = 'generate_hierarchy'
AS
$$
import json
import traceback
import networkx as nx
import pandas as pd
import re
import uuid

MAX_LEVELS = 10


def resolve_string(string: str) -> str:
    string = string.replace("-", "_")
    string = re.sub(r"([a-z])([0-9])", r"\1_\2", string)
    string = re.sub(r"([0-9])([a-z])", r"\1_\2", string)
    return string


class GenerateHierarchyError(Exception):
    def __init__(self, reason):
        super().__init__(reason)


def generate_hierarchy(
    session,
    client_id: int,
    client_db: str,
    tplan_id: str,
    scenario_id: str,
    l_1: str,
    l_2: str,
    l_3: str,
    l_4: str,
    l_5: str,
    l_6: str,
    l_7: str,
    l_8: str,
    l_9: str,
    l_10: str,
    write_db: str,
    write_schema: str,
    accounts_primary_key_column: str
):
    log_id = str(uuid.uuid4())
    log_table = f'{client_db}.public."sigma_logs_{client_id}_{tplan_id}"'
    session.sql(f"""
        INSERT INTO {log_table} (log_id, action_name, action_args)
        select
            '{log_id}',
            'generate_hierarchy',
            object_construct(
                'client_id', {client_id},
                'client_db', '{client_db}',
                'tplan_id', '{tplan_id}',
                'scenario_id', '{scenario_id}',
                'l_1', {f"'{l_1}'" if l_1 else 'null'},
                'l_2', {f"'{l_2}'" if l_2 else 'null'},
                'l_3', {f"'{l_3}'" if l_3 else 'null'},
                'l_4', {f"'{l_4}'" if l_4 else 'null'},
                'l_5', {f"'{l_5}'" if l_5 else 'null'},
                'l_6', {f"'{l_6}'" if l_6 else 'null'},
                'l_7', {f"'{l_7}'" if l_7 else 'null'},
                'l_8', {f"'{l_8}'" if l_8 else 'null'},
                'l_9', {f"'{l_9}'" if l_9 else 'null'},
                'l_10', {f"'{l_10}'" if l_10 else 'null'},
                'write_db', '{write_db}',
                'write_schema', '{write_schema}',
                'accounts_primary_key_column', '{accounts_primary_key_column}'
            )
    """).collect()

    try:
        session.sql("BEGIN").collect()

        table_columns = [
            table_col.split(".") if table_col else None
            for table_col in [l_1, l_2, l_3, l_4, l_5, l_6, l_7, l_8, l_9, l_10]
        ]
        
        if not l_1:
            raise GenerateHierarchyError("L 1 cannot be empty")
            
        first_level = 0
        last_level = MAX_LEVELS - 1
        while table_columns[last_level] is None:
            last_level -= 1
        
        empty_levels = []
        for i in range(first_level, last_level + 1):
            if table_columns[i] is None:
                empty_levels.append(i)
                
        if empty_levels:
            level_names = [f"L {i + 1}" for i in empty_levels]
            raise GenerateHierarchyError(f"Missing levels: {', '.join(level_names)}")
                
        hlo_table = f'{client_db}.public."hierarchy_level_options_v2_{client_id}_{tplan_id}"'
        get_query = f"select system_name, display_name from {hlo_table}"
        res = session.sql(get_query).collect()
        hierarchy_level_options = {row['SYSTEM_NAME']: row['DISPLAY_NAME'] for row in res}
        
        valid_table_columns = [i for i in table_columns if i]
        visited_columns = set()
        for table, column in valid_table_columns:
            column_key = f"{table}.{column}"
            if column_key in visited_columns:
                option_name = hierarchy_level_options.get(column_key)
                raise GenerateHierarchyError(f"Cannot map column '{option_name}' to multiple levels")
            visited_columns.add(column_key)

        new_scenario_id = session.sql(f"""
            call derive_partial_scenario_v2(
                {client_id},
                '{client_db}',
                '{tplan_id}',
                '{scenario_id}'
            )
        """).collect()[0][0]

        hc_table = f'{client_db}.public."hierarchy_configuration_{client_id}_{tplan_id}"'
        session.sql(f"DELETE FROM {hc_table} where scenario_id = '{new_scenario_id}'").collect()
        sql = f"""
            INSERT INTO {hc_table} (SCENARIO_ID, LEVEL_NUMBER, TABLE_NAME, COLUMN_NAME)
            VALUES
            {', '.join([f"('{new_scenario_id}', {i+1}, '{table}', '{column}')" 
                        for i, (table, column) in enumerate(valid_table_columns)])}
            """
        session.sql(sql).collect()

        select_clauses = []
        from_clause = []
        leaf_key_parts = []
        table_aliases = {}
        lvl_no = 1
        for table, column in valid_table_columns:
            if table in table_aliases:
                table_alias = table_aliases[table]
            else:
                table_alias = f"t{len(table_aliases) + 1}"
                table_aliases[table] = table_alias
                from_clause.append(f"{client_db}.public.{table} {table_alias}")
            select_clauses.append(f"cast({table_alias}.{column} as varchar) as L_{lvl_no}")
            leaf_key_parts.append(f"{table_alias}.{column}")
            lvl_no += 1
        for i in range(lvl_no, MAX_LEVELS + 1):
            select_clauses.append(f"NULL as L_{i}")
        sql = f"""
            select 
                {', '.join(select_clauses)}
            from {' cross join '.join(from_clause)}
            order by {', '.join(leaf_key_parts)}
            """
        hierarchy = session.sql(sql).collect()

        G = nx.DiGraph()
        root_id = "ROOT"
        G.add_node(root_id, level=0, name="ROOT", path=[])
        for row in hierarchy:
            path = []
            for i in range(1, MAX_LEVELS + 1):
                val = row[f"L_{i}"]
                if pd.isna(val):
                    break
                path.append(val)
                node_id = "".join(path)
                if node_id not in G.nodes:
                    G.add_node(
                        node_id,
                        level = i,
                        name = val,
                        path = path[:]
                    )
                if i == 1:
                    G.add_edge(root_id, node_id)
                else:
                    G.add_edge("".join(path[:-1]), node_id)
        data = json.dumps(nx.node_link_data(G))

        hg_table = f'{client_db}.public."hierarchy_graph_{client_id}_{tplan_id}"'
        session.sql(f"""
            merge into {hg_table} target
            using (select '{new_scenario_id}' as scenario_id, '{data}' as data) as src
            on src.scenario_id = target.scenario_id
            when matched then
                update set data = src.data
            when not matched then 
                insert (scenario_id, data) values (src.scenario_id, src.data)
        """).collect()

        h_table = f'{client_db}.public."hierarchy_{client_id}_{tplan_id}"'
        session.sql(f"DELETE FROM {h_table} where scenario_id = '{new_scenario_id}'").collect()
        insert_values = []
        for node in G.nodes():
            if node == "ROOT":
                continue
            node_data = G.nodes[node]
            path = node_data["path"]
            row = [f"'{p}'" for p in path]
            if len(row) < MAX_LEVELS:
                row.extend(['NULL'] * (MAX_LEVELS - len(row)))
            row.append(f"'{node}'")
            row = [f"'{new_scenario_id}'"] + row
            insert_values.append(f"({', '.join(row)})")
        session.sql(
            f"""
            INSERT INTO {h_table} (
                SCENARIO_ID, {', '.join([f'L_{i}' for i in range(1, MAX_LEVELS + 1)])}, hierarchy_id
            )
            VALUES {', '.join(insert_values)}
            """
        ).collect()

        ct_table = f'{client_db}.public."custom_territories_{client_id}_{tplan_id}"'
        session.sql(f"DELETE FROM {ct_table} where scenario_id = '{new_scenario_id}'").collect()

        ta_table = f'{client_db}.public."territory_assignments_{client_id}_{tplan_id}"'
        session.sql(f"DELETE FROM {ta_table} where scenario_id = '{new_scenario_id}'").collect()

        # session.sql(
        #     f"""
        #     UPDATE {client_db}.public."ds_accounts_{client_id}_{tplan_id}"
        #     SET
        #         {', '.join([f"L_{i} = NULL" for i in range(1, MAX_LEVELS + 1)])},
        #         HIERARCHY_ID = NULL
        #     WHERE CLIENT_ID = {client_id}
        #     """
        # ).collect()

        har_table_name = f'hierarchy_accounts_relations_{client_id}_{resolve_string(tplan_id)}'
        har_table = f'{write_db}.{write_schema}.{har_table_name}'
        
        # Check if the hierarchy_accounts_relations table exists
        table_exists_query = f"""
        SELECT COUNT(*) as table_count
        FROM {write_db}.INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = '{write_schema.upper()}'
        AND TABLE_NAME = '{har_table_name.upper()}'
        """
        table_exists_result = session.sql(table_exists_query).collect()
        if table_exists_result[0]['TABLE_COUNT'] == 0:
            raise GenerateHierarchyError("Please create a warehouse view for 'Hierarchy Accounts Relations'")
            
        # Check if accounts_column is empty for all rows
        empty_accounts_column_query = f"""
        SELECT COUNT(*) as non_empty_count
        FROM {har_table}
        WHERE accounts_column IS NOT NULL AND TRIM(accounts_column) != ''
        """
        empty_accounts_column_result = session.sql(empty_accounts_column_query).collect()
        if empty_accounts_column_result[0]['NON_EMPTY_COUNT'] == 0:
            raise GenerateHierarchyError("Please populate the 'Hierarchy Accounts Relations' table")
        
        get_query = f"""
        select
            system_name,
            hierarchy_column,
            accounts_column
        from {har_table} har
        """
        res = session.sql(get_query).collect()
        hierarchy_accounts_col_map = {row['SYSTEM_NAME']: row for row in res}

        ahc_table = f'{client_db}.public."accounts_hierarchy_configuration_{client_id}_{tplan_id}"'
        session.sql(f"DELETE FROM {ahc_table} where scenario_id = '{new_scenario_id}'").collect()
        insert_values = []
        accounts_cols = []
        for i, (table, column) in enumerate(valid_table_columns):
            hierarchy_col = ".".join([table, column])
            info = hierarchy_accounts_col_map.get(hierarchy_col)
            if not info:
                raise Exception(f"Hierarchy column '{hierarchy_col}' not found in 'Hierarchy Accounts Relations' table")
            if not info["ACCOUNTS_COLUMN"]:
                raise GenerateHierarchyError(f"Hierarchy column '{info['HIERARCHY_COLUMN']}' not mapped to any account column")
            insert_values.append(f"('{new_scenario_id}', {i+1}, '{info['ACCOUNTS_COLUMN']}')")
            accounts_cols.append(info['ACCOUNTS_COLUMN'])
        insert_query = f"""
            INSERT INTO {ahc_table} (SCENARIO_ID, LEVEL_NUMBER, COLUMN_NAME)
            VALUES {', '.join(insert_values)}
        """
        session.sql(insert_query).collect()

        aho_table = f'{client_db}.public."accounts_hierarchy_overrides_{client_id}_{tplan_id}"'
        dsa_table = f'{client_db}.public."ds_accounts_{client_id}_{tplan_id}"'
        session.sql("COMMIT").collect()
        merge_query = f"""
            merge into {aho_table} target
            using (
                select
                    {accounts_primary_key_column} as account_id,
                    {" || ".join(
                        [f"TO_VARCHAR({col})" for col in accounts_cols]
                    )} as new_hierarchy_id
                from {dsa_table}
            ) as src
            on target.scenario_id = '{new_scenario_id}'
            and target.account_id = src.account_id
            when matched then
                update set
                    hierarchy_id = src.new_hierarchy_id,
                    ct_id = null
            when not matched then
                insert (scenario_id, account_id, hierarchy_id)
                values ('{new_scenario_id}', src.account_id, src.new_hierarchy_id)
        """
        session.sql(merge_query).collect()

        am_table = f'{client_db}.public."accounts_metrics_{client_id}_{tplan_id}"'
        session.sql(f"DELETE FROM {am_table} where scenario_id = '{new_scenario_id}'").collect()

        session.sql("COMMIT").collect()
        res = [
            new_scenario_id,
            "Successfully generated hierarchy"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'success',
                action_result = object_construct(
                    'new_scenario_id', '{res[0]}',
                    'message', '{res[1]}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)

    except GenerateHierarchyError as e:
        session.sql("ROLLBACK").collect()
        res = [
            scenario_id,
            f"Error while generating hierarchy: {e}"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{res[0]}',
                    'message', '{res[1].replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)

    except Exception:
        session.sql("ROLLBACK").collect()
        err = traceback.format_exc()
        res = [
            scenario_id,
            f"Unexpected error while generating hierarchy: {err}"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{res[0]}',
                    'message', '{res[1].replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)
$$;