create or replace procedure save_scenario_v2(
    client_id int,
    client_db varchar,
    tplan_id varchar,
    scenario_id varchar,
    scenario_name varchar
)
returns string
language sql
as
$$
DECLARE
    log_id STRING;
    log_table_name STRING;
BEGIN
    -- Generate log ID
    SELECT REPLACE(UUID_STRING(), '-', '') INTO :log_id;
    log_table_name := :CLIENT_DB || '.public."sigma_logs_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    
    -- Initial log entry
    EXECUTE IMMEDIATE 
    'INSERT INTO ' || :log_table_name || ' (log_id, action_name, action_args)
    SELECT 
        ''' || :log_id || ''',
        ''save_scenario'',
        OBJECT_CONSTRUCT(
            ''client_id'', ' || :CLIENT_ID || ',
            ''client_db'', ''' || :CLIENT_DB || ''',
            ''tplan_id'', ''' || :TPLAN_ID || ''',
            ''scenario_id'', ''' || :SCENARIO_ID || ''',
            ''scenario_name'', ''' || :SCENARIO_NAME || '''
        )';

    BEGIN
        EXECUTE IMMEDIATE
        'UPDATE ' || :CLIENT_DB || '.public."scenarios_' || :CLIENT_ID || '_' || :TPLAN_ID || '"
        SET
            scenario_name = ''' || :SCENARIO_NAME || ''',
            is_partially_implemented = false,
            source_scenario_id = null
        WHERE scenario_id = ''' || :SCENARIO_ID || '''';

        -- Log success
        EXECUTE IMMEDIATE 
        'UPDATE ' || :log_table_name || '
        SET action_status = ''success'',
            action_result = OBJECT_CONSTRUCT(
                ''scenario_id'', ''' || :SCENARIO_ID || ''',
                ''message'', ''Successfully saved scenario''
            ),
            end_timestamp = CURRENT_TIMESTAMP()
        WHERE log_id = ''' || :log_id || '''';

        RETURN 'Successfully saved scenario';
        
        EXCEPTION 
            WHEN OTHER THEN
                -- Log failure
                EXECUTE IMMEDIATE 
                'UPDATE ' || :log_table_name || '
                SET action_status = ''failed'',
                    action_result = OBJECT_CONSTRUCT(
                        ''scenario_id'', ''' || :SCENARIO_ID || ''',
                        ''message'', ''Unexpected error while saving scenario''
                    ),
                    end_timestamp = CURRENT_TIMESTAMP()
                WHERE log_id = ''' || :log_id || '''';
                
                RETURN 'Unexpected error while saving scenario';
    END;
END;
$$;