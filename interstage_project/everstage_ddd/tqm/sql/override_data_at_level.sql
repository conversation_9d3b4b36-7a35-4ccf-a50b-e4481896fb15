CREATE OR REPLACE PROCEDURE OVERRIDE_DATA_AT_LEVEL(
    CLIENT_ID NUMBER,
	CLIENT_DB VARCHAR,
	TPLAN_ID VARCHAR,
    SCENARIO_ID VARCHAR,
    TABLE_PREFIX VARCHAR,
    COLUMN_NAME VARCHAR,
    HIERARCHY_KEY VARCHAR,
    NEW_VALUE NUMBER,
    WRITE_DB VARCHAR,
    WRITE_SCHEMA VARCHAR
)
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
PACKAGES = ('snowflake-snowpark-python')
HANDLER = 'override_data_at_level'
AS
$$
import traceback
import re
import uuid


class OverrideDataAtLevelError(Exception):
    def __init__(self, reason):
        super().__init__(reason)


def resolve_string(string: str) -> str:
    string = string.replace("-", "_")
    string = re.sub(r"([a-z])([0-9])", r"\1_\2", string)
    string = re.sub(r"([0-9])([a-z])", r"\1_\2", string)
    return string


def snake_case(s):
    s = re.sub(r'[\-.\s]+', '_', s)              # Replace separators with _
    s = re.sub(r'([a-z0-9])([A-Z])', r'\1_\2', s) # Add _ between camelCase
    return s


def override_data_at_level(
    session,
    client_id: int,
    client_db: str,
    tplan_id: str,
    scenario_id: str,
    table_prefix: str,
    column_name: str,
    hierarchy_key: str,
    new_value: int,
    write_db: str,
    write_schema: str
):
    log_id = str(uuid.uuid4())
    log_table = f'{client_db}.public."sigma_logs_{client_id}_{tplan_id}"'
    session.sql(f"""
        INSERT INTO {log_table} (log_id, action_name, action_args)
        select
            '{log_id}',
            'override_data_at_level',
            object_construct(
                'client_id', {client_id},
                'client_db', '{client_db}',
                'tplan_id', '{tplan_id}',
                'scenario_id', '{scenario_id}',
                'table_prefix', '{table_prefix}',
                'column_name', '{column_name}',
                'hierarchy_key', '{hierarchy_key}',
                'new_value', {new_value},
                'write_db', '{write_db}',
                'write_schema', '{write_schema}'
            )
    """).collect()
    
    try:
        session.sql("BEGIN TRANSACTION").collect()

        table_name = f"{table_prefix}_{client_id}_{resolve_string(tplan_id)}"
        table = f"{write_db}.{write_schema}.{table_name}"
        table_exists_query = f"""
        SELECT COUNT(*) as table_count
        FROM {write_db}.INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = '{write_schema.upper()}'
        AND TABLE_NAME = '{table_name.upper()}'
        """
        table_exists_result = session.sql(table_exists_query).collect()
        if table_exists_result[0]['TABLE_COUNT'] == 0:
            raise OverrideDataAtLevelError(f"Couldn't find warehouse view for '{table_prefix}'")
        columns_query = f"""
        select column_name
        from {write_db}.information_schema.columns
        where table_schema = '{write_schema.upper()}'
        and table_name = '{table_name.upper()}'
        """
        res = session.sql(columns_query).collect()
        needed_columns = [
            'JOIN_KEY',
            snake_case(column_name).upper()
        ]
        for c in needed_columns:
            if c not in [r['COLUMN_NAME'] for r in res]:
                raise OverrideDataAtLevelError(f"Couldn't find column '{c}' in the '{table_prefix}' table")

        new_scenario_id = session.sql(f"""
            call derive_partial_scenario_v2(
                {client_id},
                '{client_db}',
                '{tplan_id}',
                '{scenario_id}'
            )
        """).collect()[0][0]

        overrides_table = f'{client_db}.public."overrides_{client_id}_{tplan_id}"'
        absolute_value = new_value 
        lower_level_sum_query = f"""
        select
            sum(lower_level_sum) as lower_level_sum
        from (
            select
                sum({column_name}) as lower_level_sum
            from {table}
            where join_key like '{hierarchy_key}%' and join_key != '{hierarchy_key}'
            union all
            select
                sum(delta_value) as lower_level_sum
            from {overrides_table}
            where scenario_id = '{new_scenario_id}'
            and table_prefix = '{table_prefix}'
            and column_name = '{column_name}'
            and join_key like '{hierarchy_key}%' and join_key != '{hierarchy_key}'
        )
        """
        res = session.sql(lower_level_sum_query).collect()
        lower_level_sum = res[0]['LOWER_LEVEL_SUM'] or 0
        delta_value = absolute_value - lower_level_sum

        merge_query = f"""
        merge into {overrides_table} target
        using (
            select
                '{new_scenario_id}' as scenario_id,
                '{table_prefix}' as table_prefix,
                '{column_name}' as column_name,
                '{hierarchy_key}' as join_key,
                {absolute_value} as absolute_value,
                {delta_value} as delta_value
        ) source
        on target.scenario_id = source.scenario_id
        and target.table_prefix = source.table_prefix
        and target.column_name = source.column_name
        and target.join_key = source.join_key
        when matched then
            update set absolute_value = source.absolute_value, delta_value = source.delta_value
        when not matched then
            insert (scenario_id, table_prefix, column_name, join_key, absolute_value, delta_value)
            values (
                source.scenario_id,
                source.table_prefix,
                source.column_name,
                source.join_key,
                source.absolute_value,
                source.delta_value
            );
        """
        session.sql(merge_query).collect()

        session.sql("COMMIT").collect()
        res = [
            new_scenario_id,
            "Successfully overridden data"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'success',
                action_result = object_construct(
                    'new_scenario_id', '{res[0]}',
                    'message', '{res[1]}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)

    except OverrideDataAtLevelError as e:
        session.sql("ROLLBACK").collect()
        res = [
            scenario_id,
            f"Error while overriding data: {str(e)}"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{res[0]}',
                    'message', '{res[1].replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)

    except Exception:
        session.sql("ROLLBACK").collect()
        exc = traceback.format_exc()
        res = [
            scenario_id,
            f"Unexpected error while overriding data: {exc}"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{res[0]}',
                    'message', '{res[1].replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)
$$;