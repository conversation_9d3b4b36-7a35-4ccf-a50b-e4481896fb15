CREATE OR <PERSON><PERSON>LACE PROCEDURE DELETE_SCENARIO_V2(
    CLIENT_ID NUMBER,
    CLIENT_DB VARCHAR,
    TPLAN_ID VARCHAR,
    SCENARIO_ID VARCHAR
)
RETURNS STRING
LANGUAGE SQL
AS
$$
DECLARE
    scenario_count INTEGER;
    total_scenarios INTEGER;
    scenarios_table_name STRING;
    hierarchy_config_table_name STRING;
    hierarchy_table_name STRING;
    hierarchy_graph_table_name STRING;
    accounts_hierarchy_config_table_name STRING;
    custom_territories_table_name STRING;
    territory_assignments_table_name STRING;
    accounts_hierarchy_overrides_table_name STRING;
    accounts_metrics_table_name STRING;
    overrides_table_name STRING;
    log_id STRING;
    log_table_name STRING;
BEGIN
    -- Generate log ID
    SELECT REPLACE(UUID_STRING(), '-', '') INTO :log_id;
    log_table_name := :CLIENT_DB || '.public."sigma_logs_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    
    -- Initial log entry
    EXECUTE IMMEDIATE 
    'INSERT INTO ' || :log_table_name || ' (log_id, action_name, action_args)
    SELECT 
        ''' || :log_id || ''',
        ''delete_scenario'',
        OBJECT_CONSTRUCT(
            ''client_id'', ' || :CLIENT_ID || ',
            ''client_db'', ''' || :CLIENT_DB || ''',
            ''tplan_id'', ''' || :TPLAN_ID || ''',
            ''scenario_id'', ''' || :SCENARIO_ID || '''
        )';

    -- Construct table names
    scenarios_table_name := :CLIENT_DB || '.public."scenarios_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    hierarchy_config_table_name := :CLIENT_DB || '.public."hierarchy_configuration_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    hierarchy_table_name := :CLIENT_DB || '.public."hierarchy_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    hierarchy_graph_table_name := :CLIENT_DB || '.public."hierarchy_graph_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    accounts_hierarchy_config_table_name := :CLIENT_DB || '.public."accounts_hierarchy_configuration_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    custom_territories_table_name := :CLIENT_DB || '.public."custom_territories_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    territory_assignments_table_name := :CLIENT_DB || '.public."territory_assignments_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    accounts_hierarchy_overrides_table_name := :CLIENT_DB || '.public."accounts_hierarchy_overrides_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    accounts_metrics_table_name := :CLIENT_DB || '.public."accounts_metrics_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    overrides_table_name := :CLIENT_DB || '.public."overrides_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    
    -- Validate if scenario exists
    SELECT COUNT(*) INTO :scenario_count 
    FROM IDENTIFIER(:scenarios_table_name)
    WHERE scenario_id = :SCENARIO_ID;
    
    IF (scenario_count = 0) THEN
        -- Log error
        EXECUTE IMMEDIATE 
        'UPDATE ' || :log_table_name || '
        SET action_status = ''failed'',
            action_result = OBJECT_CONSTRUCT(
                ''scenario_id'', ''' || :SCENARIO_ID || ''',
                ''message'', ''Error while deleting scenario: Scenario does not exist''
            ),
            end_timestamp = CURRENT_TIMESTAMP()
        WHERE log_id = ''' || :log_id || '''';
        
        RETURN 'Error while deleting scenario: Scenario does not exist';
    END IF;
    
    -- Check if this is the last scenario
    SELECT COUNT(*) INTO :total_scenarios 
    FROM IDENTIFIER(:scenarios_table_name);
    
    IF (total_scenarios = 1) THEN
        -- Log error
        EXECUTE IMMEDIATE 
        'UPDATE ' || :log_table_name || '
        SET action_status = ''failed'',
            action_result = OBJECT_CONSTRUCT(
                ''scenario_id'', ''' || :SCENARIO_ID || ''',
                ''message'', ''Error while deleting scenario: Cannot delete the last scenario''
            ),
            end_timestamp = CURRENT_TIMESTAMP()
        WHERE log_id = ''' || :log_id || '''';
        
        RETURN 'Error while deleting scenario: Cannot delete the last scenario';
    END IF;
    
    -- Proceed with deletion if scenario exists and is not the last one
    BEGIN
        DELETE FROM IDENTIFIER(:scenarios_table_name)
        WHERE scenario_id = :SCENARIO_ID;
        
        DELETE FROM IDENTIFIER(:hierarchy_config_table_name)
        WHERE scenario_id = :SCENARIO_ID;
        
        DELETE FROM IDENTIFIER(:hierarchy_table_name)
        WHERE scenario_id = :SCENARIO_ID;
        
        DELETE FROM IDENTIFIER(:hierarchy_graph_table_name)
        WHERE scenario_id = :SCENARIO_ID;
        
        DELETE FROM IDENTIFIER(:accounts_hierarchy_config_table_name)
        WHERE scenario_id = :SCENARIO_ID;
        
        DELETE FROM IDENTIFIER(:custom_territories_table_name)
        WHERE scenario_id = :SCENARIO_ID;
        
        DELETE FROM IDENTIFIER(:territory_assignments_table_name)
        WHERE scenario_id = :SCENARIO_ID;
        
        DELETE FROM IDENTIFIER(:accounts_hierarchy_overrides_table_name)
        WHERE scenario_id = :SCENARIO_ID;
        
        DELETE FROM IDENTIFIER(:accounts_metrics_table_name)
        WHERE scenario_id = :SCENARIO_ID;
        
        DELETE FROM IDENTIFIER(:overrides_table_name)
        WHERE scenario_id = :SCENARIO_ID;
        
        -- Log success
        EXECUTE IMMEDIATE 
        'UPDATE ' || :log_table_name || '
        SET action_status = ''success'',
            action_result = OBJECT_CONSTRUCT(
                ''scenario_id'', ''' || :SCENARIO_ID || ''',
                ''message'', ''Successfully deleted scenario''
            ),
            end_timestamp = CURRENT_TIMESTAMP()
        WHERE log_id = ''' || :log_id || '''';
        
        RETURN 'Successfully deleted scenario';
        
        EXCEPTION 
            WHEN OTHER THEN
                -- Log failure
                EXECUTE IMMEDIATE 
                'UPDATE ' || :log_table_name || '
                SET action_status = ''failed'',
                    action_result = OBJECT_CONSTRUCT(
                        ''scenario_id'', ''' || :SCENARIO_ID || ''',
                        ''message'', ''Unexpected error while deleting scenario''
                    ),
                    end_timestamp = CURRENT_TIMESTAMP()
                WHERE log_id = ''' || :log_id || '''';
                
                RETURN 'Unexpected error while deleting scenario';
    END;
END;
$$;