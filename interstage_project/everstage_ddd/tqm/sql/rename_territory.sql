CREATE OR REPLACE PROCEDURE RENAME_TERRITORY_v2(
    CLIENT_ID NUMBER,
    CLIENT_DB VARCHAR,
    TPLAN_ID VARCHAR,
    SCENARIO_ID VARCHAR,
    territory_key VARCHAR,
    NEW_TERRITORY_NAME VARCHAR
)
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
PACKAGES = ('snowflake-snowpark-python', 'networkx')
HANDLER = 'rename_territory'
AS
$$
import traceback
import json
import networkx as nx
import uuid


class RenameTerritoryError(Exception):
    def __init__(self, reason):
        super().__init__(reason)


def rename_territory(
    session,
    client_id: int,
    client_db: str,
    tplan_id: str,
    scenario_id: str,
    territory_key: str,
    new_territory_name: str
) -> str:
    log_id = str(uuid.uuid4())
    log_table = f'{client_db}.public."sigma_logs_{client_id}_{tplan_id}"'
    session.sql(f"""
        INSERT INTO {log_table} (log_id, action_name, action_args)
        select
            '{log_id}',
            'rename_territory',
            object_construct(
                'client_id', {client_id},
                'client_db', '{client_db}',
                'tplan_id', '{tplan_id}',
                'scenario_id', '{scenario_id}',
                'territory_key', '{territory_key}',
                'new_territory_name', '{new_territory_name}'
            )
    """).collect()
    
    try:
        session.sql("BEGIN").collect()

        if new_territory_name == "Unmapped":
            raise RenameTerritoryError("Unmapped territory name is not allowed")

        new_scenario_id = session.sql(f"""
            call derive_partial_scenario_v2(
                {client_id},
                '{client_db}',
                '{tplan_id}',
                '{scenario_id}'
            )
        """).collect()[0][0]

        # Update the hierarchy graph.
        hg_table = f'{client_db}.public."hierarchy_graph_{client_id}_{tplan_id}"'
        res = session.sql(f"SELECT data FROM {hg_table} WHERE SCENARIO_ID = '{new_scenario_id}'").collect()
        graph_data = res[0][0]
        G = nx.node_link_graph(json.loads(graph_data))

        node = G.nodes[territory_key]
        if not node.get("ct_id"):
            raise RenameTerritoryError(f"Not allowed to rename {node['name']}")
        if node["name"] == "Unmapped":
            raise RenameTerritoryError("Unmapped territory cannot be renamed")
        
        parent_id = list(G.predecessors(territory_key))[0]
        
        # Check only siblings (direct children of parent) for name conflict
        for sibling in G.successors(parent_id):
            if sibling != territory_key:
                if G.nodes[sibling].get("name") == new_territory_name:
                    raise RenameTerritoryError(f"Territory name '{new_territory_name}' already exists at the same level")

        new_node_id = parent_id + new_territory_name
        if new_node_id in G.nodes:
            raise RenameTerritoryError(f"Territory {new_territory_name} already exists")
        G.remove_node(territory_key)
        node["name"] = new_territory_name
        node["path"][-1] = new_territory_name
        G.add_node(new_node_id, **node)
        G.add_edge(parent_id, new_node_id)

        # Persist the updated graph back to the database.
        new_graph_data = json.dumps(nx.node_link_data(G))
        session.sql(f"""
            UPDATE {hg_table}
            SET DATA = '{new_graph_data}'
            WHERE SCENARIO_ID = '{new_scenario_id}'
        """).collect()

        # update territories table
        t_table = f'{client_db}.public."custom_territories_{client_id}_{tplan_id}"'
        session.sql(f"""
            UPDATE {t_table}
            SET ct_name = '{new_territory_name}'
            WHERE scenario_id = '{new_scenario_id}' AND ct_id = '{node["ct_id"]}'
        """).collect()

        # update territory assignments table
        ta_table = f'{client_db}.public."territory_assignments_{client_id}_{tplan_id}"'
        session.sql(f"""
            UPDATE {ta_table}
            SET TERRITORY_KEY = '{new_node_id}'
            WHERE scenario_id = '{new_scenario_id}' AND TERRITORY_KEY = '{territory_key}'
        """).collect()

        session.sql("COMMIT").collect()
        res = [
            new_scenario_id,
            "Successfully renamed territory"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'success',
                action_result = object_construct(
                    'new_scenario_id', '{res[0]}',
                    'message', '{res[1]}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)
        
    except RenameTerritoryError as e:
        session.sql("ROLLBACK").collect()
        res = [
            scenario_id,
            f"Error while renaming territory: {e}"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{res[0]}',
                    'message', '{res[1].replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)
        
    except Exception:
        session.sql("ROLLBACK").collect()
        err = traceback.format_exc()
        res = [
            scenario_id,
            f"Unexpected error while renaming territory: {err}"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{res[0]}',
                    'message', '{res[1].replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)
$$; 