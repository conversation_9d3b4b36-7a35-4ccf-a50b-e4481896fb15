CREATE OR REPLACE PROCEDURE DISTRIBUTE_ACCOUNTS_v2(
    CLIENT_ID NUMBER,
    C<PERSON>IENT_DB VARCHAR,
    TPLAN_ID VARCHAR,
    <PERSON>ENARIO_ID VARCHAR,
    accounts_primary_key_column VARCHAR,
    HIE<PERSON>RCHY_ID VARCHAR,
    ONLY_UNMAPPED BOOLEAN,
    NO_OF_SPLITS NUMBER,
    MAX_ACCOUNTS_PER_TERRITORY NUMBER,
    DISTRIBUTION_LOGIC VARCHAR,
    SORT_COLUMN VARCHAR,
    EQUAL_BY_COLUMNS VARCHAR,
    HAS_CONDITIONS BOOLEAN,
    WRITE_DB VARCHAR,
    WRITE_SCHEMA VARCHAR
)
RETURNS VARCHAR
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
PACKAGES = ('snowflake-snowpark-python', 'pandas', 'numpy', 'networkx')
HANDLER = 'distribute_accounts'
as
$$
import copy
import re
import traceback
import uuid
import json
import networkx as nx
from datetime import datetime

import numpy as np
import pandas as pd
from snowflake.snowpark import functions as F


global_session = None
MAX_LEVELS = 10


def resolve_string(string: str) -> str:
    string = string.replace("-", "_")
    string = re.sub(r"([a-z])([0-9])", r"\1_\2", string)
    string = re.sub(r"([0-9])([a-z])", r"\1_\2", string)
    return string


def run_ddl_actions(session, client_db: str) -> str:
    temp_table = f'{client_db}.public."temp_distribute_results_{int(datetime.now().timestamp())}_{uuid.uuid4().hex}"'
    session.sql(f"""
        CREATE TABLE {temp_table} (ACCOUNT_ID VARCHAR, CT_ID VARCHAR)
    """).collect()
    return temp_table

def run_ddl_rollback(session, temp_table: str):
    session.sql(f"drop table {temp_table}").collect()


class DistributeAccountsException(Exception):
    def __init__(self, reason):
        super().__init__(reason)


class RoundRobinDistribution:
    def __init__(self, df, no_of_splits, max_accounts_per_territory):
        self.df = df
        self.no_of_splits = no_of_splits
        self.max_accounts_per_territory = max_accounts_per_territory

    def distribute(self):
        n = len(self.df)
        k = self.no_of_splits
        m = self.max_accounts_per_territory
        q = n // k
        distribution = None
        if m is None or q < m:
            distribution = np.arange(n) % k
        else:
            assignable = m * k
            assignment = np.arange(assignable) % k
            remaining_assignment = np.full(n - assignable, -1)
            distribution = np.concatenate([assignment, remaining_assignment])
        assignments = {i: self.df[distribution == i] for i in range(-1, self.no_of_splits)}
        return assignments


class SnakeDistribution:
    def __init__(self, df, no_of_splits, max_accounts_per_territory):
        self.df = df
        self.no_of_splits = no_of_splits
        self.max_accounts_per_territory = max_accounts_per_territory

    def distribute(self):
        n = len(self.df)
        k = self.no_of_splits
        m = self.max_accounts_per_territory
        q = n // k
        distribution = None
        fwd_seq = np.arange(k)
        rev_seq = fwd_seq[::-1]
        full_seq = np.concatenate([fwd_seq, rev_seq])
        repeats = n // len(full_seq)
        pattern = np.concatenate([np.tile(full_seq, repeats), full_seq[:n % len(full_seq)]])
        if m is None or q < m:
            distribution = pattern
        else:
            assignable = m * k
            assignment = pattern[:assignable]
            remaining_assignment = np.full(n - assignable, -1)
            distribution = np.concatenate([assignment, remaining_assignment])
        assignments = {i: self.df[distribution == i] for i in range(-1, self.no_of_splits)}
        return assignments


class ConditionalDistribution:
    def __init__(
        self, df, no_of_splits, max_accounts_per_territory, equal_by_columns, conditions
    ):
        self.df = df
        self.no_of_splits = no_of_splits
        self.max_accounts_per_territory = max_accounts_per_territory
        self.equal_by_columns = equal_by_columns
        self.conditions = conditions

    def update_metrics(self, row, metrics):
        for col, value in metrics.items():
            if col != "TOTAL_ACCOUNTS":
                for agg, _ in value.items():
                    if agg == "SUM":
                        value[agg] += row[col.upper()]
                    elif agg == "COUNT":
                        value[agg] += 1
        metrics["TOTAL_ACCOUNTS"] += 1
        return metrics

    def score_by_max_accounts_per_territory(self, running_metrics):
        scores = np.zeros(self.no_of_splits)
        if self.max_accounts_per_territory is None:
            return scores
        for i in range(self.no_of_splits):
            if (
                running_metrics[i]["TOTAL_ACCOUNTS"] - 1
                >= self.max_accounts_per_territory
            ):
                scores[i] = float("-inf")
        return scores

    def score_by_equal_by_columns(self, running_metrics):
        scores = np.zeros(self.no_of_splits)
        for col in self.equal_by_columns:
            col_values = [metrics[col]["SUM"] for _, metrics in running_metrics.items()]
            scores[col_values.index(min(col_values))] = 1
        return scores

    def score_by_conditions(self, running_metrics, row_no):
        scores = np.zeros(self.no_of_splits)
        for i in range(self.no_of_splits):
            meets_all_conditions = True
            for condition in self.conditions:
                agg = condition["AGGREGATION"]
                col = condition["COLUMN_NAME"]
                op = condition["OPERATOR"]
                val = float(condition["VALUE"])
                current_agg = running_metrics[i][col][agg]
                if op == "<":
                    if current_agg >= val:
                        meets_all_conditions = False
                        break
                elif op == "<=":
                    if current_agg > val:
                        meets_all_conditions = False
                        break
                elif row_no == len(self.df) - 1:
                    if op == ">":
                        if current_agg <= val:
                            raise DistributeAccountsException(f"Condition not met: {agg}({col}) > {val}")
                    elif op == ">=":
                        if current_agg < val:
                            raise DistributeAccountsException(f"Condition not met: {agg}({col}) >= {val}")
            if meets_all_conditions:
                scores[i] = 1
            else:
                scores[i] = float("-inf")
        return scores

    def identify_best_territory(self, row, running_metrics, row_no):
        updated_metrics = {
            i: self.update_metrics(row, metrics)
            for i, metrics in copy.deepcopy(running_metrics).items()
        }
        total_scores = np.zeros(self.no_of_splits)
        # add scores based on max accounts per territory
        scores_1 = self.score_by_max_accounts_per_territory(updated_metrics)
        total_scores += scores_1
        # add scores based on equal by columns
        scores_2 = self.score_by_equal_by_columns(updated_metrics)
        total_scores += scores_2
        # add scores based on conditions
        scores_3 = self.score_by_conditions(updated_metrics, row_no)
        total_scores += scores_3
        # identify the territory with the highest score
        max_score = total_scores.max()
        if max_score == float("-inf"):
            return -1
        candidates = np.where(total_scores == max_score)[0]
        # Break ties by choosing the one with the fewest accounts
        if len(candidates) > 1:
            top_scorer = min(
                candidates, key=lambda x: running_metrics[x]["TOTAL_ACCOUNTS"]
            )
        else:
            top_scorer = candidates[0]
        pairs = set(
            [(col, "SUM") for col in self.equal_by_columns]
            + [(col["COLUMN_NAME"], col["AGGREGATION"]) for col in self.conditions]
        )
        for col, agg in pairs:
            vals = [running_metrics[i][col][agg] for i in range(self.no_of_splits)]
            vals = list(map(int, vals))
        return top_scorer

    def generate_base_metrics(self):
        base_metrics = {"TOTAL_ACCOUNTS": 0}
        for col in self.equal_by_columns:
            if col not in base_metrics:
                base_metrics[col] = {}
            base_metrics[col]["SUM"] = 0
        for condition in self.conditions:
            col = condition["COLUMN_NAME"]
            agg = condition["AGGREGATION"]
            if col not in base_metrics:
                base_metrics[col] = {}
            base_metrics[col][agg] = 0
        return base_metrics

    def distribute(self):
        base_metrics = self.generate_base_metrics()
        running_metrics = {
            i: copy.deepcopy(base_metrics) for i in range(self.no_of_splits)
        }
        territory_assignments = []
        for row_no, row in self.df.iterrows():
            territory = self.identify_best_territory(row, running_metrics, row_no)
            territory_assignments.append(territory)
            if territory != -1:
                running_metrics[territory] = self.update_metrics(
                    row, running_metrics[territory]
                )
        self.df["TERRITORY"] = territory_assignments
        assignments = {i: self.df[self.df["TERRITORY"] == i] for i in range(self.no_of_splits)}
        assignments[-1] = self.df[self.df["TERRITORY"] == -1]
        return assignments


class Distributor:
    def __init__(
        self,
        session,
        client_id,
        client_db,
        tplan_id,
        scenario_id,
        accounts_primary_key_column,
        hierarchy_id,
        only_unmapped,
        no_of_splits,
        max_accounts_per_territory,
        distribution_logic,
        sort_column,
        equal_by_columns,
        has_conditions,
        write_db,
        write_schema
    ):
        self.session = session
        self.client_id = client_id
        self.client_db = client_db
        self.tplan_id = tplan_id
        self.scenario_id = scenario_id
        self.new_scenario_id = None
        self.accounts_primary_key_column = accounts_primary_key_column
        self.hierarchy_id = hierarchy_id
        self.only_unmapped = only_unmapped
        self.no_of_splits = no_of_splits
        self.max_accounts_per_territory = max_accounts_per_territory
        self.distribution_logic = distribution_logic
        self.sort_column = sort_column
        self.equal_by_columns = equal_by_columns
        self.has_conditions = has_conditions
        self.write_db = write_db
        self.write_schema = write_schema
        self.conditions = []
        self.G = None
        self.hierarchy_node = None
        self.node_name = None
        self.node_name_counter = 0
        # ----------------------------
        self.ct_table = f'{self.client_db}.public."custom_territories_{self.client_id}_{self.tplan_id}"'    
        self.dar_table_name = f'distribute_accounts_rules_{self.client_id}_{resolve_string(self.tplan_id)}'
        self.dar_table = f'{self.write_db}.{self.write_schema}.{self.dar_table_name}'
        self.hg_table = f'{self.client_db}.public."hierarchy_graph_{self.client_id}_{self.tplan_id}"'
        self.dsa_table = f'{self.client_db}.public."ds_accounts_{self.client_id}_{self.tplan_id}"'
        self.ah_view = f'{self.client_db}.public."accounts_hierarchy_view_{self.client_id}_{self.tplan_id}"'
        self.aho_table = f'{self.client_db}.public."accounts_hierarchy_overrides_{self.client_id}_{self.tplan_id}"'
        self.custom_territories = {}
        self.log_id = str(uuid.uuid4())
        self.log_table = f'{self.client_db}.public."sigma_logs_{self.client_id}_{self.tplan_id}"'

    def get_hierarchy_graph(self):
        res = self.session.sql(f"SELECT DATA FROM {self.hg_table} WHERE SCENARIO_ID = '{self.new_scenario_id}'").collect()
        self.G = nx.node_link_graph(json.loads(res[0][0]))
        self.hierarchy_node = self.G.nodes[self.hierarchy_id]
        self.node_name = self.hierarchy_node["name"]
        rg = "^.+([0-9]+)$"
        successors = list(self.G.successors(self.hierarchy_id))
        self.custom_territories = {}
        for node_id in successors:
            node = self.G.nodes[node_id]
            self.custom_territories[node["name"]] = node["ct_id"]
            m = re.match(rg, node["name"])
            if m:
                self.node_name_counter = max(self.node_name_counter, int(m.group(1)))

    def get_data(self):
        columns = set([c["COLUMN_NAME"] for c in self.conditions] + self.equal_by_columns)
        columns = [
            f"dsa.{col}"
            for col in columns
        ]
        filters = [f"dsa.client_id = {self.client_id}"]
        if self.only_unmapped:
            filters.append(f"ahv.join_key = '{self.hierarchy_id}Unmapped'")
        else:
            filters.append(f"ahv.hierarchy_id = '{self.hierarchy_id}'")
        sorts = []
        if self.distribution_logic == "CONDITIONAL" and columns:
            sorts.extend([
                f"{col} DESC" for col in columns
            ])
        elif self.sort_column:
            sorts.append(f"dsa.{self.sort_column} ASC")
        else:
            sorts.append(f"dsa.{self.accounts_primary_key_column} ASC")
        get_query = f"""
            select
                dsa.{self.accounts_primary_key_column} as ID,
                {", ".join(columns)}
            from {self.dsa_table} dsa
            left join {self.ah_view} ahv
            on ahv.scenario_id = '{self.new_scenario_id}'
            and ahv.account_id = dsa.{self.accounts_primary_key_column}
            where {' and '.join(filters)}
            order by {", ".join(sorts)};
        """
        res = self.session.sql(get_query).collect()
        df = pd.DataFrame(res)
        if df.empty:
            raise DistributeAccountsException("No accounts to distribute")
        return df

    def get_conditions(self):
        if self.has_conditions:
            # Check if the distribute_accounts_rules table exists
            table_exists_query = f"""
            SELECT COUNT(*) as table_count
            FROM {self.write_db}.INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = '{self.write_schema.upper()}'
            AND TABLE_NAME = '{self.dar_table_name.upper()}'
            """
            table_exists_result = self.session.sql(table_exists_query).collect()
            if table_exists_result[0]['TABLE_COUNT'] == 0:
                raise DistributeAccountsException(f"Please create a warehouse view for 'Distribute Accounts Rules'")
                
            # Get all conditions
            get_query = f"select * from {self.dar_table}"
            res = self.session.sql(get_query).collect()
            
            if len(res) == 0:
                raise DistributeAccountsException(f"Please define conditions in the 'Distribute Accounts Rules' table")
            
            # Filter out rows where all required fields are empty
            required_columns = ['AGGREGATION', 'COLUMN_NAME', 'OPERATOR', 'VALUE']
            valid_conditions = []
            
            for i, row in enumerate(res):
                missing_fields = []
                for col in required_columns:
                    if row[col] is None or str(row[col]).strip() == '':
                        missing_fields.append(col)
                
                if len(missing_fields) == len(required_columns):
                    # Skipping empty rows
                    continue

                if missing_fields:
                    raise DistributeAccountsException(f"Missing required values for columns {', '.join(missing_fields)} in row {i + 1} of 'Distribute Accounts Rules' table")
                
                valid_conditions.append(row)
                
            self.conditions = valid_conditions
        else:
            self.conditions = []

    def update_dependencies(self, assignments, temp_table: str):
        added_territories = [
            # (ct_id, ct_name)
        ]
        removed_territories = [
            # (ct_id, ct_name)
        ]
        if not assignments[-1].empty and "Unmapped" not in self.custom_territories:
            # create unmapped territory
            added_territories.append(
                (uuid.uuid4().hex, "Unmapped")
            )
        elif assignments[-1].empty and "Unmapped" in self.custom_territories:
            # remove unmapped territory
            removed_territories.append(
                (self.custom_territories["Unmapped"], "Unmapped")
            )
        required_territories = set(assignments.keys()) - set([-1])
        existing_territories = set(self.custom_territories.keys()) - set(["Unmapped"])
        if len(required_territories) > len(existing_territories):
            count = len(required_territories) - len(existing_territories)
            for i in range(count):
                added_territories.append(
                    (uuid.uuid4().hex, f"{self.node_name}{self.node_name_counter + i + 1}")
                )

        # update hierarchy graph
        for ct_id, ct_name in added_territories:
            new_node_id = self.hierarchy_id + ct_name    
            self.G.add_node(
                new_node_id,
                level=self.hierarchy_node["level"] + 1,
                name=ct_name,
                path=self.hierarchy_node["path"] + [ct_name],
                ct_id=ct_id
            )
            self.G.add_edge(self.hierarchy_id, new_node_id)
        for _, ct_name in removed_territories:
            node_id = self.hierarchy_id + ct_name
            self.G.remove_node(node_id)

        data = json.dumps(nx.node_link_data(self.G))
        self.session.sql(f"""
            UPDATE {self.hg_table}
            SET DATA = '{data}'
            WHERE SCENARIO_ID = '{self.new_scenario_id}'
        """).collect()

        # update territories
        if added_territories:
            insert_vals = [
                f"('{self.new_scenario_id}', '{self.hierarchy_id}', '{ct_id}', '{ct_name}')"
                for ct_id, ct_name in added_territories
            ]
            self.session.sql(f"""
                INSERT INTO {self.ct_table} (
                    SCENARIO_ID,
                    HIERARCHY_ID,
                    ct_id,
                    ct_name
                )
                VALUES {", ".join(insert_vals)}
            """).collect()
            self.custom_territories.update({
                ct_name: ct_id
                for ct_id, ct_name in added_territories
            })
        if removed_territories:
            self.session.sql(f"""
                DELETE FROM {self.ct_table}
                WHERE SCENARIO_ID = '{self.new_scenario_id}'
                AND HIERARCHY_ID = '{self.hierarchy_id}'
                AND ct_id IN ({", ".join([f"'{ct_id}'" for ct_id, _ in removed_territories])})
            """).collect()
            for _, ct_name in removed_territories:
                del self.custom_territories[ct_name]

        # update data source accounts
        all_assignments = []
        ct_names = sorted(ct_name for ct_name in self.custom_territories.keys() if ct_name != "Unmapped")
        ct_ids = [
            self.custom_territories[ct_name]
            for ct_name in ct_names
        ]
        for i, accounts_df in assignments.items():
            if accounts_df.empty:
                continue
            ct_id = (
                self.custom_territories["Unmapped"]
                if i == -1
                else ct_ids[i]
            )
            all_assignments.append(
                pd.DataFrame({
                    "ACCOUNT_ID": accounts_df["ID"],
                    "CT_ID": ct_id
                })
            )
        df = pd.concat(all_assignments)
        insert_query = f"""
            insert into {temp_table} (account_id, ct_id)
            values {", ".join([
                f"('{row['ACCOUNT_ID']}', '{row['CT_ID']}')" for _, row in df.iterrows()
            ])}
        """
        self.session.sql(insert_query).collect()
        sql = f"""
            merge into {self.aho_table} target
            using (
                select 
                    ah.scenario_id,
                    ah.account_id,
                    tmp.ct_id
                from {self.ah_view} ah
                inner join {temp_table} tmp
                on ah.account_id = tmp.account_id
                and ah.scenario_id = '{self.new_scenario_id}'
                and ah.hierarchy_id = '{self.hierarchy_id}'
            ) source
            on target.scenario_id = source.scenario_id
            and target.account_id = source.account_id
            when matched then
                update set ct_id = source.ct_id
            when not matched then
                insert (scenario_id, account_id, ct_id)
                values ('{self.new_scenario_id}', source.account_id, source.ct_id)
        """
        self.session.sql(sql).collect()

    
    def distribute(self):
        temp_table = run_ddl_actions(self.session, self.client_db)
        try:
            self.session.sql("BEGIN").collect()
            self.new_scenario_id = self.session.sql(f"""
                call derive_partial_scenario_v2(
                    {self.client_id},
                    '{self.client_db}',
                    '{self.tplan_id}',
                    '{self.scenario_id}'
                )
            """).collect()[0][0]
            self.get_hierarchy_graph()
            self.get_conditions()
            df = self.get_data()
            distributor = None
            if self.distribution_logic == "ROUND_ROBIN":
                distributor = RoundRobinDistribution(
                    df, self.no_of_splits, self.max_accounts_per_territory
                )
            elif self.distribution_logic == "SNAKE":
                distributor = SnakeDistribution(
                    df, self.no_of_splits, self.max_accounts_per_territory
                )
            elif self.distribution_logic == "CONDITIONAL":
                if not self.conditions and not self.equal_by_columns:
                    distributor = RoundRobinDistribution(
                        df, self.no_of_splits, self.max_accounts_per_territory
                    )
                else:
                    distributor = ConditionalDistribution(
                        df,
                        self.no_of_splits,
                        self.max_accounts_per_territory,
                        self.equal_by_columns,
                        self.conditions,
                    )
            else:
                raise DistributeAccountsException("Invalid distribution logic")
            if distributor:
                assignments = distributor.distribute()
                self.update_dependencies(assignments, temp_table)
            self.session.sql("COMMIT").collect()
        except Exception:
            self.session.sql("ROLLBACK").collect()
            raise
        finally:
            run_ddl_rollback(self.session, temp_table)
        
    def distribute_wrapper(self):
        # Add validation for no_of_splits and max_accounts_per_territory
        try:
            # Log the start of the operation
            self.session.sql(f"""
                INSERT INTO {self.log_table} (log_id, action_name, action_args)
                select
                    '{self.log_id}',
                    'distribute_accounts',
                    object_construct(
                        'client_id', {self.client_id},
                        'client_db', '{self.client_db}',
                        'tplan_id', '{self.tplan_id}',
                        'scenario_id', '{self.scenario_id}',
                        'accounts_primary_key_column', '{self.accounts_primary_key_column}',
                        'hierarchy_id', '{self.hierarchy_id}',
                        'only_unmapped', {str(self.only_unmapped).lower()},
                        'no_of_splits', {self.no_of_splits},
                        'max_accounts_per_territory', {self.max_accounts_per_territory if self.max_accounts_per_territory is not None else 'null'},
                        'distribution_logic', '{self.distribution_logic}',
                        'sort_column', {f"'{self.sort_column}'" if self.sort_column else 'null'},
                        'equal_by_columns', {f"'{','.join(self.equal_by_columns)}'" if self.equal_by_columns else 'null'},
                        'has_conditions', {str(self.has_conditions).lower()},
                        'write_db', '{self.write_db}',
                        'write_schema', '{self.write_schema}'
                    )
            """).collect()
            
            # Validate parameters
            if self.no_of_splits <= 0:
                raise DistributeAccountsException("Number of territories must be greater than 0")
            
            if self.max_accounts_per_territory is not None and self.max_accounts_per_territory <= 0:
                raise DistributeAccountsException("Maximum accounts per territory must be greater than 0")
                
            # Execute distribution
            self.distribute()
            
            # Log success
            res = [
                self.new_scenario_id,
                "Successfully distributed accounts"
            ]
            conditions_used = ''
            if self.conditions:
                conditions_used = ', '.join(
                    [
                        f"{c['AGGREGATION']}({c['COLUMN_NAME']}) {c['OPERATOR']} {c['VALUE']}"
                        for c in self.conditions
                    ]
                )
            self.session.sql(f"""
                UPDATE {self.log_table}
                SET action_status = 'success',
                    action_result = object_construct(
                        'new_scenario_id', '{res[0]}',
                        'message', '{res[1]}',
                        'conditions_used', '{conditions_used}'
                    ),
                    end_timestamp = current_timestamp()
                WHERE log_id = '{self.log_id}'
            """).collect()
            return '###'.join(res)
            
        except DistributeAccountsException as e:
            self.session.sql(f"""
                UPDATE {self.log_table}
                SET action_status = 'failed',
                    action_result = object_construct(
                        'scenario_id', '{self.scenario_id}',
                        'message', 'Error while distributing accounts: {str(e).replace("'", "''")}'
                    ),
                    end_timestamp = current_timestamp()
                WHERE log_id = '{self.log_id}'
            """).collect()
            res = [
                self.scenario_id,
                f"Error while distributing accounts: {e}"
            ]
            return '###'.join(res)
            
        except Exception:
            err_trace = traceback.format_exc()
            self.session.sql(f"""
                UPDATE {self.log_table}
                SET action_status = 'failed',
                    action_result = object_construct(
                        'scenario_id', '{self.scenario_id}',
                        'message', 'Unexpected error while distributing accounts: {err_trace.replace("'", "''")}'
                    ),
                    end_timestamp = current_timestamp()
                WHERE log_id = '{self.log_id}'
            """).collect()
            res = [
                self.scenario_id,
                f"Unexpected error while distributing accounts: {err_trace}"
            ]
            return '###'.join(res)


def distribute_accounts(
    session,
    client_id,
    client_db,
    tplan_id,
    scenario_id,
    accounts_primary_key_column,
    hierarchy_id,
    only_unmapped,
    no_of_splits,
    max_accounts_per_territory,
    distribution_logic,
    sort_column,
    equal_by_columns,
    has_conditions,
    write_db,
    write_schema
):
    # Create distributor and delegate all work to it
    equal_by_columns_list = equal_by_columns.split(",") if equal_by_columns else []
    d = Distributor(
        session,
        client_id,
        client_db,
        tplan_id,
        scenario_id,
        accounts_primary_key_column,
        hierarchy_id,
        only_unmapped,
        no_of_splits,
        max_accounts_per_territory,
        distribution_logic,
        sort_column,
        equal_by_columns_list,
        has_conditions,
        write_db,
        write_schema
    )
    return d.distribute_wrapper()
$$;