create or replace procedure override_source_scenario_v2(
    client_id int,
    client_db varchar,
    tplan_id varchar,
    scenario_id varchar
)
returns varchar
language python
runtime_version = '3.9'
packages = ('snowflake-snowpark-python')
handler = 'override_source_scenario'
as
$$
import traceback
import uuid


class OverrideSourceScenarioError(Exception):
    def __init__(self, reason):
        super().__init__(reason)


def override_source_scenario(
    session,
    client_id: int,
    client_db: str,
    tplan_id: str,
    scenario_id: str
) -> str:
    log_id = str(uuid.uuid4())
    log_table = f'{client_db}.public."sigma_logs_{client_id}_{tplan_id}"'
    session.sql(f"""
        INSERT INTO {log_table} (log_id, action_name, action_args)
        select
            '{log_id}',
            'override_source_scenario',
            object_construct(
                'client_id', {client_id},
                'client_db', '{client_db}',
                'tplan_id', '{tplan_id}',
                'scenario_id', '{scenario_id}'
            )
    """).collect()
    
    try:
        session.sql("begin").collect()

        table_name = f'{client_db}.public."scenarios_{client_id}_{tplan_id}"'

        res = session.sql(f"select * from {table_name} where scenario_id = '{scenario_id}'").collect()
        current_scenario = res[0]

        if not current_scenario['IS_PARTIALLY_IMPLEMENTED'] or not current_scenario['SOURCE_SCENARIO_ID']:
            raise OverrideSourceScenarioError("No new changes found in the scenario")
        
        res = session.sql(f"""
            select scenario_name 
            from {table_name} 
            where scenario_id = '{current_scenario['SOURCE_SCENARIO_ID']}'
        """).collect()
        source_scenario_name = res[0][0]

        session.sql(f"""
            update {table_name}
            set is_partially_implemented = false,
            scenario_name = '{source_scenario_name}',
            source_scenario_id = null
            where scenario_id = '{scenario_id}'
        """).collect()

        session.sql(f"""
            call delete_scenario_v2(
                {client_id},
                '{client_db}',
                '{tplan_id}',
                '{current_scenario['SOURCE_SCENARIO_ID']}'
            )
        """).collect()

        session.sql("commit").collect()
        
        message = f'Successfully saved to scenario "{source_scenario_name}"'
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'success',
                action_result = object_construct(
                    'scenario_id', '{scenario_id}',
                    'message', '{message}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return message

    except OverrideSourceScenarioError as e:
        session.sql("rollback").collect()
        error_message = f'Error: {e}'
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{scenario_id}',
                    'message', '{error_message.replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return error_message
    
    except Exception:
        session.sql("rollback").collect()
        err = traceback.format_exc()
        error_message = f'Unexpected error while saving scenario: {err}'
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{scenario_id}',
                    'message', '{error_message.replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return error_message
$$;