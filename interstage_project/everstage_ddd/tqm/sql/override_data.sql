CREATE OR REPLACE PROCEDURE OVERRIDE_DATA(
    CLIENT_ID NUMBER,
	CLIENT_DB VARCHAR,
	TPLAN_ID VARCHAR,
    SC<PERSON>ARIO_ID VARCHAR,
    TERRITORY_KEY VARCHAR,
    METRIC_ID VARCHAR,
    NEW_VALUE NUMBER,
    WRITE_DB VARCHAR,
    WRITE_SCHEMA VARCHAR,
    ACCOUNTS_PRIMARY_KEY_COLUMN VARCHAR
)
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
PACKAGES = ('snowflake-snowpark-python', 'networkx')
HANDLER = 'override_data'
AS
$$
import traceback
import networkx as nx
import json
import re
import uuid


class OverrideDataError(Exception):
    def __init__(self, reason):
        super().__init__(reason)


def resolve_string(string: str) -> str:
    string = string.replace("-", "_")
    string = re.sub(r"([a-z])([0-9])", r"\1_\2", string)
    string = re.sub(r"([0-9])([a-z])", r"\1_\2", string)
    return string


def override_data(
    session,
    client_id: int,
    client_db: str,
    tplan_id: str,
    scenario_id: str,
    territory_key: str,
    metric_id: str,
    new_value: int,
    write_db: str,
    write_schema: str,
    accounts_primary_key_column: str
):
    log_id = str(uuid.uuid4())
    log_table = f'{client_db}.public."sigma_logs_{client_id}_{tplan_id}"'
    session.sql(f"""
        INSERT INTO {log_table} (log_id, action_name, action_args)
        select
            '{log_id}',
            'spread_hierarchy_metric',
            object_construct(
                'client_id', {client_id},
                'client_db', '{client_db}',
                'tplan_id', '{tplan_id}',
                'scenario_id', '{scenario_id}',
                'territory_key', '{territory_key}',
                'metric_id', '{metric_id}',
                'new_value', {new_value},
                'write_db', '{write_db}',
                'write_schema', '{write_schema}',
                'accounts_primary_key_column', '{accounts_primary_key_column}'
            )
    """).collect()
    
    try:
        session.sql("BEGIN TRANSACTION").collect()

        spmm_whv_table_name = f"spread_hierarchy_metrics_mappings_whv_{client_id}_{resolve_string(tplan_id)}"
        spmm_whv_table = f"{write_db}.{write_schema}.{spmm_whv_table_name}"
        table_exists_query = f"""
        SELECT COUNT(*) as table_count
        FROM {write_db}.INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = '{write_schema.upper()}'
        AND TABLE_NAME = '{spmm_whv_table_name.upper()}'
        """
        table_exists_result = session.sql(table_exists_query).collect()
        if table_exists_result[0]['TABLE_COUNT'] == 0:
            raise OverrideDataError(f"Please create a warehouse view for 'Spread Hierarchy Metrics Mappings'")
        get_query = f"select hierarchy_column from {spmm_whv_table} where metric_id = '{metric_id}'"
        res = session.sql(get_query).collect()
        if not res:
            raise OverrideDataError(f"Couldn't find metric {metric_id} in the 'Spread Hierarchy Metrics Mappings' table")
        hierarchy_column = res[0]['HIERARCHY_COLUMN']
        if hierarchy_column is None:    
            raise OverrideDataError(f"{metric_id} is not mapped to any hierarchy column")
        
        h_whv_table_name = f"hierarchy_whv_{client_id}_{resolve_string(tplan_id)}"
        table_exists_query = f"""
        SELECT COUNT(*) as table_count
        FROM {write_db}.INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = '{write_schema.upper()}'
        AND TABLE_NAME = '{h_whv_table_name.upper()}'
        """
        table_exists_result = session.sql(table_exists_query).collect()
        if table_exists_result[0]['TABLE_COUNT'] == 0:
            raise OverrideDataError(f"Please create a warehouse view for 'Hierarchy'")
        column_exists_query = f"""
        select count(*) > 0 as column_exists
        from {write_db}.information_schema.columns
        where table_schema = '{write_schema.upper()}'
        and table_name = '{h_whv_table_name.upper()}'
        and column_name = '{hierarchy_column.upper()}'
        """
        column_exists_result = session.sql(column_exists_query).collect()
        if not column_exists_result[0]['COLUMN_EXISTS']:
            raise OverrideDataError(f"Couldn't find hierarchy column {hierarchy_column} in the 'Hierarchy' table")


        new_scenario_id = session.sql(f"""
            call derive_partial_scenario_v2(
                {client_id},
                '{client_db}',
                '{tplan_id}',
                '{scenario_id}'
            )
        """).collect()[0][0]

        hg_table = f'{client_db}.public."hierarchy_graph_{client_id}_{tplan_id}"'
        res = session.sql(f"select data from {hg_table} where scenario_id = '{new_scenario_id}'").collect()
        G = nx.node_link_graph(json.loads(res[0][0]))
        if G.out_degree(territory_key) == 0:
            leaf_keys = [f"'{territory_key}'"]
        else:
            leaf_keys = [f"'{node}'" for node in nx.descendants(G, territory_key) if G.out_degree(node) == 0]

        if not leaf_keys:
            raise OverrideDataError(f"Territory key {territory_key} has no leaf nodes")

        dsa_table = f'{client_db}.public."ds_accounts_{client_id}_{tplan_id}"'
        am_table = f'{client_db}.public."accounts_metrics_{client_id}_{tplan_id}"'
        ah_view = f'{client_db}.public."accounts_hierarchy_view_{client_id}_{tplan_id}"'
        merge_query = f"""
        merge into {am_table} target
        using (
            select
                dsa.{accounts_primary_key_column} as account_id,
                cast({new_value} as decimal(38,10)) / cast(count(*) over() as decimal(38,10)) as metric_value
            from {dsa_table} dsa
            inner join {ah_view} ahv
            on ahv.scenario_id = '{new_scenario_id}'
            and ahv.account_id = dsa.{accounts_primary_key_column}
            where ahv.join_key in ({', '.join(leaf_keys)})
        ) source
        on target.scenario_id = '{new_scenario_id}'
        and target.account_id = source.account_id
        when matched then
            update set {metric_id} = source.metric_value
        when not matched then
            insert (scenario_id, account_id, {metric_id})
            values ('{new_scenario_id}', source.account_id, source.metric_value)
        """
        session.sql(merge_query).collect()

        session.sql("COMMIT").collect()
        res = [
            new_scenario_id,
            "Successfully overridden data"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'success',
                action_result = object_construct(
                    'new_scenario_id', '{res[0]}',
                    'message', '{res[1]}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)

    except OverrideDataError as e:
        session.sql("ROLLBACK").collect()
        res = [
            scenario_id,
            f"Error while overriding data: {str(e)}"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{res[0]}',
                    'message', '{res[1].replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)

    except Exception:
        session.sql("ROLLBACK").collect()
        exc = traceback.format_exc()
        res = [
            scenario_id,
            f"Unexpected error while overriding data: {exc}"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{res[0]}',
                    'message', '{res[1].replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)
$$;