create or replace procedure assign_territory_v2(
    client_id number,
    client_db varchar,
    tplan_id varchar,
    scenario_id varchar,
    territory_key varchar,
    employee_email_id varchar
)
returns string
language python
runtime_version = '3.9'
packages = ('snowflake-snowpark-python')
handler = 'assign_territory'
as
$$
import traceback
import uuid


def assign_territory(
    session,
    client_id: int,
    client_db: str,
    tplan_id: str,
    scenario_id: str,
    territory_key: str,
    employee_email_id: str
) -> str:
    log_id = str(uuid.uuid4())
    log_table = f'{client_db}.public."sigma_logs_{client_id}_{tplan_id}"'
    session.sql(f"""
        INSERT INTO {log_table} (log_id, action_name, action_args)
        select
            '{log_id}',
            'assign_territory',
            object_construct(
                'client_id', {client_id},
                'client_db', '{client_db}',
                'tplan_id', '{tplan_id}',
                'scenario_id', '{scenario_id}',
                'territory_key', '{territory_key}',
                'employee_email_id', '{employee_email_id}'
            )
    """).collect()
    
    try:
        session.sql("BEGIN").collect()

        new_scenario_id = session.sql(f"""
            call derive_partial_scenario_v2(
                {client_id},
                '{client_db}',
                '{tplan_id}',
                '{scenario_id}'
            )
        """).collect()[0][0]

        session.sql(f"""
            MERGE INTO {client_db}.public."territory_assignments_{client_id}_{tplan_id}" target
            USING (
                SELECT '{territory_key}' AS TERRITORY_KEY
            ) AS source
            ON target.scenario_id = '{new_scenario_id}' AND target.TERRITORY_KEY = source.TERRITORY_KEY
            WHEN NOT MATCHED THEN
                INSERT (scenario_id, TERRITORY_KEY, EMPLOYEE_EMAIL_ID)
                VALUES ('{new_scenario_id}', source.TERRITORY_KEY, '{employee_email_id}')
            WHEN MATCHED THEN
                UPDATE SET EMPLOYEE_EMAIL_ID = '{employee_email_id}'
        """).collect()

        session.sql("COMMIT").collect()

        res = [
            new_scenario_id,
            'Successfully assigned territory'
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'success',
                action_result = object_construct(
                    'new_scenario_id', '{res[0]}',
                    'message', '{res[1]}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)

    except Exception:
        session.sql("ROLLBACK").collect()
        err = traceback.format_exc()
        res = [scenario_id, f"Unexpected error while assigning territory: {err}"]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{res[0]}',
                    'message', '{res[1].replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)
$$;