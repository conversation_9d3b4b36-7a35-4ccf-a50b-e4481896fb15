CREATE OR REPLACE PROCEDURE RESET_DATA_v2(
    CLIENT_ID NUMBER,
    CLIENT_DB VARCHAR,
    TPLAN_ID VARCHAR
)
RETURNS VARCHAR
LANGUAGE SQL
AS
$$
DECLARE
    log_id VARCHAR;
    log_table VARCHAR;
BEGIN
    -- Generate a UUID for logging
    log_id := REPLACE(UUID_STRING(), '-', '');
    log_table := :CLIENT_DB || '.public."sigma_logs_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    
    -- Log the start of the procedure execution
    EXECUTE IMMEDIATE 
    'INSERT INTO ' || :log_table || ' (log_id, action_name, action_args)
    SELECT
        ''' || :log_id || ''',
        ''reset_data'',
        OBJECT_CONSTRUCT(
            ''client_id'', ' || :CLIENT_ID || ',
            ''client_db'', ''' || :CLIENT_DB || ''',
            ''tplan_id'', ''' || :TPLAN_ID || '''
        )';

    EXECUTE IMMEDIATE 
    'TRUNCATE TABLE ' || :CLIENT_DB || '.public."hierarchy_configuration_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';

    EXECUTE IMMEDIATE 
    'TRUNCATE TABLE ' || :CLIENT_DB || '.public."hierarchy_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';

    EXECUTE IMMEDIATE 
    'TRUNCATE TABLE ' || :CLIENT_DB || '.public."hierarchy_graph_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';

    EXECUTE IMMEDIATE 
    'TRUNCATE TABLE ' || :CLIENT_DB || '.public."accounts_hierarchy_configuration_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';

    EXECUTE IMMEDIATE 
    'TRUNCATE TABLE ' || :CLIENT_DB || '.public."custom_territories_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';

    EXECUTE IMMEDIATE 
    'TRUNCATE TABLE ' || :CLIENT_DB || '.public."territory_assignments_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';

    EXECUTE IMMEDIATE 
    'TRUNCATE TABLE ' || :CLIENT_DB || '.public."scenarios_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';

    EXECUTE IMMEDIATE 
    'TRUNCATE TABLE ' || :CLIENT_DB || '.PUBLIC."accounts_hierarchy_overrides_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';

    EXECUTE IMMEDIATE 
    'TRUNCATE TABLE ' || :CLIENT_DB || '.PUBLIC."accounts_metrics_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';

    EXECUTE IMMEDIATE 
    'TRUNCATE TABLE ' || :CLIENT_DB || '.PUBLIC."overrides_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';
    
    EXECUTE IMMEDIATE 
    'TRUNCATE TABLE ' || :CLIENT_DB || '.PUBLIC."combinations_' || :CLIENT_ID || '_' || :TPLAN_ID || '"';

    EXECUTE IMMEDIATE 
    'INSERT INTO ' || :CLIENT_DB || '.public."scenarios_' || :CLIENT_ID || '_' || :TPLAN_ID || '"
    (SCENARIO_ID, SCENARIO_NAME) VALUES (''' || REPLACE(UUID_STRING(), '-', '') || ''', ''Scenario 1'')';

    -- Log the successful completion of the procedure
    EXECUTE IMMEDIATE 
    'UPDATE ' || :log_table || '
    SET action_status = ''success'',
        action_result = OBJECT_CONSTRUCT(
            ''message'', ''Successfully reset data''
        ),
        end_timestamp = CURRENT_TIMESTAMP()
    WHERE log_id = ''' || :log_id || '''';

    RETURN 'Successfully reset data';
EXCEPTION
    WHEN OTHER THEN
        -- Log the error
        EXECUTE IMMEDIATE 
        'UPDATE ' || :log_table || '
        SET action_status = ''failed'',
            action_result = OBJECT_CONSTRUCT(
                ''message'', ''Error while resetting data: ' || REPLACE(SQLERRM, '''', '''''') || '''
            ),
            end_timestamp = CURRENT_TIMESTAMP()
        WHERE log_id = ''' || :log_id || '''';
        
        RETURN 'Error while resetting data: ' || SQLERRM;
END;
$$;