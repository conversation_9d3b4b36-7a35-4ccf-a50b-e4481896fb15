CREATE OR REPLACE PROCEDURE CREATE_EMPTY_TERRITORIES_v2(
    CLIENT_ID NUMBER,
    CLIENT_DB VARCHAR,
    TPLAN_ID VARCHAR,
    SCENARIO_ID VARCHAR,
    hierarchy_id VARCHAR,
    COUNT NUMBER
)
RETURNS STRING
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
PACKAGES = ('snowflake-snowpark-python', 'networkx')
HANDLER = 'create_empty_territories'
AS
$$
import traceback
import uuid
import networkx as nx
import json
import re

MAX_LEVELS = 10


class CreateEmptyTerritoriesError(Exception):
    def __init__(self, reason):
        super().__init__(reason)


def create_empty_territories(
    session,
    client_id: int,
    client_db: str,
    tplan_id: str,
    scenario_id: str,
    hierarchy_id: str,
    count: int
) -> str:
    log_id = str(uuid.uuid4())
    log_table = f'{client_db}.public."sigma_logs_{client_id}_{tplan_id}"'
    session.sql(f"""
        INSERT INTO {log_table} (log_id, action_name, action_args)
        select
            '{log_id}',
            'create_empty_territories',
            object_construct(
                'client_id', {client_id},
                'client_db', '{client_db}',
                'tplan_id', '{tplan_id}',
                'scenario_id', '{scenario_id}',
                'hierarchy_id', '{hierarchy_id}',
                'count', {count}
            )
    """).collect()
    
    try:
        # Add validation for count
        if count <= 0:
            raise CreateEmptyTerritoriesError("Number of territories to create must be greater than 0")

        session.sql("BEGIN").collect()

        new_scenario_id = session.sql(f"""
            call derive_partial_scenario_v2(
                {client_id},
                '{client_db}',
                '{tplan_id}',
                '{scenario_id}'
            )
        """).collect()[0][0]

        # update hierarchy graph
        hg_table = f'{client_db}.public."hierarchy_graph_{client_id}_{tplan_id}"'
        res = session.sql(f"SELECT data FROM {hg_table} WHERE SCENARIO_ID = '{new_scenario_id}'").collect()
        G = nx.node_link_graph(json.loads(res[0][0]))
        node = G.nodes[hierarchy_id]
        successors = list(G.successors(hierarchy_id))
        successor_nodes = [G.nodes[s] for s in successors]

        has_unmapped = any(s["name"] == "Unmapped" for s in successor_nodes)
        ah_view = f'{client_db}.public."accounts_hierarchy_view_{client_id}_{tplan_id}"'
        get_query = f"""
            select count(*)
            from {ah_view}
            where scenario_id = '{new_scenario_id}'
            and hierarchy_id = '{hierarchy_id}'
            and hierarchy_id = join_key
        """
        res = session.sql(get_query).collect()
        unmapped_count = res[0][0]
        create_unmapped = not has_unmapped and unmapped_count > 0

        rg = "^.+([0-9]+)$"
        node_name_counter = 0
        for s in successor_nodes:
            m = re.match(rg, s["name"])
            if m:
                node_name_counter = max(node_name_counter, int(m.group(1)))
        added_nodes = []
        for i in range(count):
            ct_name = f"{node['name']}-{node_name_counter + i + 1}"
            ct_id = uuid.uuid4().hex
            new_node_id = hierarchy_id + ct_name    
            G.add_node(
                new_node_id,
                level=node["level"] + 1,
                name=ct_name,
                path=node["path"] + [ct_name],
                ct_id=ct_id
            )
            added_nodes.append(G.nodes[new_node_id])
            G.add_edge(hierarchy_id, new_node_id)
        unmapped_node = None
        if create_unmapped:
            ct_id = uuid.uuid4().hex
            new_node_id = hierarchy_id + "Unmapped"
            G.add_node(
                new_node_id,
                level=node["level"] + 1,
                name="Unmapped",
                path=node["path"] + ["Unmapped"],
                ct_id=ct_id
            )
            unmapped_node = G.nodes[new_node_id]
            added_nodes.append(unmapped_node)
            G.add_edge(hierarchy_id, new_node_id)
        data = json.dumps(nx.node_link_data(G))
        sql = f"""
            UPDATE {hg_table} SET DATA = '{data}' WHERE SCENARIO_ID = '{new_scenario_id}'
        """
        session.sql(sql).collect()

        ct_table = f'{client_db}.public."custom_territories_{client_id}_{tplan_id}"'
        session.sql(f"""
            INSERT INTO {ct_table} (SCENARIO_ID, HIERARCHY_ID, ct_id, ct_name)
            VALUES {', '.join([
                f"('{new_scenario_id}', '{hierarchy_id}', '{node['ct_id']}', '{node['name']}')"
                for node in added_nodes
            ])}
        """).collect()

        if create_unmapped:
            ct_id = unmapped_node["ct_id"]
            aho_table = f'{client_db}.public."accounts_hierarchy_overrides_{client_id}_{tplan_id}"'
            ah_view = f'{client_db}.public."accounts_hierarchy_view_{client_id}_{tplan_id}"'
            session.sql(f"""
                merge into {aho_table} target
                using (
                    select
                        scenario_id,
                        account_id,
                        hierarchy_id
                    from {ah_view}
                    where scenario_id = '{new_scenario_id}'
                    and hierarchy_id = '{hierarchy_id}'
                ) source
                on target.scenario_id = source.scenario_id
                and target.account_id = source.account_id
                when matched then
                    update set ct_id = '{ct_id}'
                when not matched then
                    insert (scenario_id, account_id, ct_id)
                    values ('{new_scenario_id}', source.account_id, '{ct_id}')
            """).collect()

        session.sql("COMMIT").collect()
        res = [
            new_scenario_id,
            "Successfully created empty territories"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'success',
                action_result = object_construct(
                    'new_scenario_id', '{res[0]}',
                    'message', '{res[1]}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)

    except CreateEmptyTerritoriesError as e:
        session.sql("ROLLBACK").collect()   
        err = traceback.format_exc()
        res = [
            scenario_id,
            f"Error while creating empty territories: {e}"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{res[0]}',
                    'message', '{res[1].replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)

    except Exception:
        session.sql("ROLLBACK").collect()   
        err = traceback.format_exc()
        res = [
            scenario_id,
            f"Unexpected error while creating empty territories: {err}"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{res[0]}',
                    'message', '{res[1].replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)
$$;