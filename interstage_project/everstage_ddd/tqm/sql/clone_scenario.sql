create or replace procedure clone_scenario_v2(
    client_id int,
    client_db varchar,
    tplan_id varchar,
    scenario_id varchar
)
returns string
language python
runtime_version = '3.9'
packages = ('snowflake-snowpark-python')
handler = 'clone_scenario'
as
$$
import uuid

MAX_LEVELS = 10
MAX_METRICS = 10

def clone_scenario(
    session,
    client_id: int,
    client_db: str,
    tplan_id: str,
    scenario_id: str
) -> str:

    s_table = f'{client_db}.public."scenarios_{client_id}_{tplan_id}"'
    res = session.sql(f"select * from {s_table} where scenario_id = '{scenario_id}'").collect()
    scenario = res[0]
    new_scenario_id = uuid.uuid4().hex
    session.sql(f"""
        insert into {s_table} (scenario_id, scenario_name, is_partially_implemented, source_scenario_id)
        values (
            '{new_scenario_id}',
            '{scenario['SCENARIO_NAME']}(yet to save)',
            true,
            '{scenario_id}'
        )
    """).collect()

    hc_table = f'{client_db}.public."hierarchy_configuration_{client_id}_{tplan_id}"'
    session.sql(f"""
        insert into {hc_table} (scenario_id, level_number, table_name, column_name)
        select '{new_scenario_id}', level_number, table_name, column_name
        from {hc_table} where scenario_id = '{scenario_id}'
    """).collect()

    h_table = f'{client_db}.public."hierarchy_{client_id}_{tplan_id}"'
    level_cols = [f'l_{i + 1}' for i in range(MAX_LEVELS)]
    session.sql(f"""
        insert into {h_table} (scenario_id, {', '.join(level_cols)}, hierarchy_id)
        select '{new_scenario_id}', {', '.join(level_cols)}, hierarchy_id
        from {h_table} where scenario_id = '{scenario_id}'
    """).collect()

    hg_table = f'{client_db}.public."hierarchy_graph_{client_id}_{tplan_id}"'
    session.sql(f"""
        insert into {hg_table} (scenario_id, data)
        select '{new_scenario_id}', data
        from {hg_table} where scenario_id = '{scenario_id}'
    """).collect()

    ahc_table = f'{client_db}.public."accounts_hierarchy_configuration_{client_id}_{tplan_id}"'
    session.sql(f"""
        insert into {ahc_table} (scenario_id, level_number, column_name)
        select '{new_scenario_id}', level_number, column_name
        from {ahc_table} where scenario_id = '{scenario_id}'
    """).collect()

    ct_table = f'{client_db}.public."custom_territories_{client_id}_{tplan_id}"'
    session.sql(f"""
        insert into {ct_table} (scenario_id, hierarchy_id, ct_id, ct_name)
        select '{new_scenario_id}', hierarchy_id, ct_id, ct_name
        from {ct_table} where scenario_id = '{scenario_id}'
    """).collect()
    
    ta_table = f'{client_db}.public."territory_assignments_{client_id}_{tplan_id}"'
    session.sql(f"""
        insert into {ta_table} (scenario_id, territory_key, employee_email_id)
        select '{new_scenario_id}', territory_key, employee_email_id
        from {ta_table} where scenario_id = '{scenario_id}'
    """).collect()

    aho_table = f'{client_db}.public."accounts_hierarchy_overrides_{client_id}_{tplan_id}"'
    session.sql(f"""
        insert into {aho_table} (scenario_id, account_id, hierarchy_id, ct_id)
        select '{new_scenario_id}', account_id, hierarchy_id, ct_id
        from {aho_table} where scenario_id = '{scenario_id}'
    """).collect()

    am_table = f'{client_db}.public."accounts_metrics_{client_id}_{tplan_id}"'
    session.sql(f"""
        insert into {am_table} (scenario_id, account_id, {', '.join([f'metric_{i + 1}' for i in range(MAX_METRICS)])})
        select '{new_scenario_id}', account_id, {', '.join([f'metric_{i + 1}' for i in range(MAX_METRICS)])}
        from {am_table} where scenario_id = '{scenario_id}'
    """).collect()

    overrides_table = f'{client_db}.public."overrides_{client_id}_{tplan_id}"'
    session.sql(f"""
        insert into {overrides_table} (scenario_id, table_prefix, column_name, join_key, absolute_value, delta_value)
        select '{new_scenario_id}', table_prefix, column_name, join_key, absolute_value, delta_value
        from {overrides_table} where scenario_id = '{scenario_id}'
    """).collect()

    return new_scenario_id
$$;