CREATE OR REPLACE PROCEDURE MERGE_TERRITORIES_v2(
    CLIENT_ID NUMBER,
    CLIENT_DB VARCHAR,
    TPLAN_ID VARCHAR,
    SCENARIO_ID VARCHAR,
    TERRITORIES VARCHAR
)
RETURNS VARCHAR
LANGUAGE PYTHON
RUNTIME_VERSION = '3.9'
PACKAGES = ('snowflake-snowpark-python', 'pandas', 'networkx')
HANDLER = 'merge_territories'
as
$$
import traceback
import networkx as nx
import json
import uuid
from datetime import datetime

MAX_LEVEL = 10

class MergeTerritoriesError(Exception):
    def __init__(self, reason):
        super().__init__(reason)


def run_ddl_actions(session, client_db: str) -> str:
    temp_table = f'{client_db}.public."temp_merge_results_{int(datetime.now().timestamp())}_{uuid.uuid4().hex}"'
    session.sql(f"""
        CREATE TABLE {temp_table} (OLD_TERRITORY_KEY VARCHAR, NEW_HIERARCHY_ID VARCHAR, NEW_CT_ID VARCHAR)
    """).collect()
    return temp_table

def run_ddl_rollback(session, temp_table: str):
    session.sql(f"drop table {temp_table}").collect()

def merge_territories(
    session,
    client_id: int,
    client_db: str,
    tplan_id: str,
    scenario_id: str,
    territories: str
) -> str:
    log_id = str(uuid.uuid4())
    log_table = f'{client_db}.public."sigma_logs_{client_id}_{tplan_id}"'
    session.sql(f"""
        INSERT INTO {log_table} (log_id, action_name, action_args)
        select
            '{log_id}',
            'merge_territories',
            object_construct(
                'client_id', {client_id},
                'client_db', '{client_db}',
                'tplan_id', '{tplan_id}',
                'scenario_id', '{scenario_id}',
                'territories', '{territories}'
            )
    """).collect()
    
    temp_table = run_ddl_actions(session, client_db)
    try:
        session.sql("BEGIN").collect()

        new_scenario_id = session.sql(f"""  
            call derive_partial_scenario_v2(
                {client_id},
                '{client_db}',
                '{tplan_id}',
                '{scenario_id}'
            )
        """).collect()[0][0]

        territory_ids = territories.split(",")
        hg_table = f'{client_db}.public."hierarchy_graph_{client_id}_{tplan_id}"'
        res = session.sql(f"SELECT data FROM {hg_table} WHERE scenario_id = '{new_scenario_id}'").collect()
        G = nx.node_link_graph(json.loads(res[0][0]))

        # Validate that no two territories are in the same hierarchical line
        for i in range(len(territory_ids)):
            for j in range(i + 1, len(territory_ids)):
                territory1 = territory_ids[i]
                territory2 = territory_ids[j]
                # Check if one territory is ancestor of another
                if nx.has_path(G, territory1, territory2) or nx.has_path(G, territory2, territory1):
                    raise MergeTerritoriesError(f"Cannot merge territories in same hierarchical line: {territory1} and {territory2}")

        # Find the lowest common ancestor (LCA)
        common_ancestor = territory_ids[0]
        for i in range(1, len(territory_ids)):
            common_ancestor = nx.lowest_common_ancestor(
                G, common_ancestor, territory_ids[i]
            )
        common_ancestor_node = G.nodes[common_ancestor]

        # Determine all successor of LCA
        all_successors = list(G.successors(common_ancestor))

        # Filter successors to only include the territories to be merged
        successors = [
            successor
            for successor in all_successors
            if any(territory.startswith(successor) for territory in territory_ids)
        ]
        successor_nodes = [G.nodes[successor] for successor in successors]
        merging_only_custom_territories = all(node.get("ct_id") for node in successor_nodes)

        # Create the new node
        to_be_merged = [node["name"] for node in successor_nodes]
        merge_node_name = " & ".join(sorted(to_be_merged))
        merge_node_id = "".join(common_ancestor_node["path"]) + merge_node_name
        merge_level = common_ancestor_node["level"] + 1
        ct_id = uuid.uuid4().hex if merging_only_custom_territories else None
        G.add_node(
            merge_node_id,
            level=merge_level,
            name=merge_node_name,
            path=common_ancestor_node["path"] + [merge_node_name],
            ct_id=ct_id,
        )
        removed_nodes = []

        def is_hierarchy_leaf_node(node_id):
            successors = list(G.successors(node_id))
            return len(successors) == 0 or all(G.nodes[successor].get("ct_id") for successor in successors)

        old_new_hierarchy_map = { 
            # old_territory_key: {
            #     "new_hierarchy_id": "<new_hierarchy_id>",
            #     "new_ct_id": "<new_ct_id>"
            # }
        }
        if merging_only_custom_territories:
            for node in successor_nodes:
                old_territory_key = "".join(node["path"])
                old_new_hierarchy_map[old_territory_key] = {
                    "new_hierarchy_id": common_ancestor,
                    "new_ct_id": ct_id
                }
        else:
            for node_id in successors:
                if not list(G.successors(node_id)):
                    node = G.nodes[node_id]
                    old_territory_key = "".join(node["path"][:merge_level])
                    old_new_hierarchy_map[old_territory_key] = {
                        "new_hierarchy_id": "".join(node["path"][:merge_level - 1]) + merge_node_name,
                        "new_ct_id": None
                    }

        # Connect the LCA to the new node
        G.add_edge(common_ancestor, merge_node_id)

        def attach_sub_tree(G, sub_tree, new_parent, merge_lvl_0_indexed, merge_name):
            for original_child in G.successors(sub_tree):
                child_node = G.nodes[original_child]
                original_path = child_node["path"]
                new_path = (
                    original_path[:merge_lvl_0_indexed]
                    + [merge_name]
                    + original_path[merge_lvl_0_indexed + 1 :]
                )
                new_child = "".join(new_path)
                new_ct_id = None
                if new_child not in G.nodes:
                    new_ct_id = child_node.get("ct_id")
                    G.add_node(
                        new_child,
                        level=child_node["level"],
                        name=child_node["name"],
                        path=new_path,
                        ct_id=new_ct_id
                    )
                else:
                    new_ct_id = G.nodes[new_child].get("ct_id")
                G.add_edge(new_parent, new_child)
                if not list(G.successors(original_child)):
                    old_new_hierarchy_map[original_child] = {
                        "new_hierarchy_id": new_child if not new_ct_id else "".join(new_path[:-1]),
                        "new_ct_id": new_ct_id
                    }
                attach_sub_tree(
                    G, original_child, new_child, merge_lvl_0_indexed, merge_name
                )

        def delete_sub_tree(G, sub_tree):
            removed_nodes.append(sub_tree)
            current_node = sub_tree
            while True:
                parents = list(G.predecessors(current_node))
                if not parents:
                    break
                parent = parents[0]
                if G.out_degree(parent) > 1:
                    break
                current_node = parent
            recursive_delete(G, current_node)

        def recursive_delete(G, sub_tree):
            children = list(G.successors(sub_tree))
            for child in children:
                recursive_delete(G, child)
            G.remove_node(sub_tree)

        merge_lvl_0_indexed = merge_level - 1
        for node_id in territory_ids:
            node = G.nodes[node_id]
            path = node["path"]

            # Recreate create the territory path with merge node
            itr_node = merge_node_id
            for i in range(merge_lvl_0_indexed + 1, len(path), 1):
                original_path = "".join(path[: i + 1])
                original_node = G.nodes[original_path]
                new_path = (
                    path[:merge_lvl_0_indexed]
                    + [merge_node_name]
                    + path[merge_lvl_0_indexed + 1 : i + 1]
                )
                new_node_id = "".join(new_path)
                new_ct_id = None
                if new_node_id not in G.nodes:
                    new_ct_id = original_node.get("ct_id")
                    G.add_node(
                        new_node_id,
                        level=i + 1,
                        name=original_node["name"],
                        path=new_path,
                        ct_id=new_ct_id
                    )
                else:
                    new_ct_id = G.nodes[new_node_id].get("ct_id")
                G.add_edge(itr_node, new_node_id)
                itr_node = new_node_id
                if not list(G.successors(node_id)):
                    old_new_hierarchy_map[node_id] = {
                        "new_hierarchy_id": new_node_id if not new_ct_id else "".join(new_path[:-1]),
                        "new_ct_id": new_ct_id
                    }

            # Attach the sub tree of the territory to the merge node
            attach_sub_tree(G, node_id, itr_node, merge_lvl_0_indexed, merge_node_name)

            # Delete the original sub tree of the territory
            delete_sub_tree(G, node_id)

        # update hierarchy graph table
        session.sql(
            f"UPDATE {hg_table} SET DATA = ? where scenario_id = ?",
            params=[json.dumps(nx.node_link_data(G)), new_scenario_id],
        ).collect()

        insert_query = f"""
            insert into {temp_table} (old_territory_key, new_hierarchy_id, new_ct_id)
            values {", ".join([
                f'''(
                    '{old_territory_key}',
                    '{row['new_hierarchy_id']}',
                    {f"'{row['new_ct_id']}'" if row['new_ct_id'] else 'NULL'}
                )'''
                for old_territory_key, row in old_new_hierarchy_map.items()
            ])}
        """
        session.sql(insert_query).collect()

        aho_table = f'{client_db}.public."accounts_hierarchy_overrides_{client_id}_{tplan_id}"'
        ah_view = f'{client_db}.public."accounts_hierarchy_view_{client_id}_{tplan_id}"'
        merge_query = f"""
            merge into {aho_table} target
            using (
                select
                    ahv.scenario_id,
                    ahv.account_id,
                    tmp.new_hierarchy_id,
                    tmp.new_ct_id
                from {ah_view} ahv
                inner join {temp_table} tmp
                on ahv.scenario_id = '{new_scenario_id}'
                and ahv.join_key = tmp.old_territory_key
            ) source
            on target.scenario_id = source.scenario_id
            and target.account_id = source.account_id
            when matched then
                update set hierarchy_id = source.new_hierarchy_id, ct_id = source.new_ct_id
            when not matched then
                insert (scenario_id, account_id, hierarchy_id, ct_id)
                values (source.scenario_id, source.account_id, source.new_hierarchy_id, source.new_ct_id)
        """
        session.sql(merge_query).collect()

        if not merging_only_custom_territories:
            # update hierarchy table
            h_table = f'{client_db}.public."hierarchy_{client_id}_{tplan_id}"'
            session.sql(f"delete from {h_table} where scenario_id = '{new_scenario_id}'").collect()
            insert_values = []
            for node in G.nodes():
                if node == "ROOT":
                    continue
                node_data = G.nodes[node]
                if node_data.get("ct_id"):
                    continue
                path = node_data["path"]
                row = [f"'{p}'" for p in path]
                if len(row) < MAX_LEVEL:
                    row.extend(['NULL'] * (MAX_LEVEL - len(row)))
                row = [f"'{new_scenario_id}'"] + row + [f"'{node}'"]
                insert_values.append(f"({', '.join(row)})")
            session.sql(
                f"""
                INSERT INTO {h_table} (scenario_id, {', '.join([f'L_{i}' for i in range(1, MAX_LEVEL + 1)])}, HIERARCHY_ID)
                VALUES {', '.join(insert_values)}
                """
            ).collect()
        
        ct_table = f'{client_db}.public."custom_territories_{client_id}_{tplan_id}"'
        session.sql(f"delete from {ct_table} where scenario_id = '{new_scenario_id}'").collect()
        insert_values = []
        for node_id in G.nodes():
            node = G.nodes[node_id]
            if node.get("ct_id"):
                hierarchy_id = "".join(node["path"][:-1])
                insert_values.append(f"('{new_scenario_id}', '{hierarchy_id}', '{node['ct_id']}', '{node['name']}')")
        if insert_values:
            insert_query = f"""
                insert into {ct_table} (scenario_id, hierarchy_id, ct_id, ct_name)
                values {', '.join(insert_values)}
            """
            session.sql(insert_query).collect()

        
        # update territory assignments table
        ta_table = f'{client_db}.public."territory_assignments_{client_id}_{tplan_id}"'
        session.sql(f"""
            DELETE FROM {ta_table}
            WHERE scenario_id = '{new_scenario_id}' AND
                TERRITORY_KEY NOT IN (
                    {', '.join([f"'{t}'" for t in removed_nodes])}
                )
        """).collect()

        session.sql("COMMIT").collect()

        res = [
            new_scenario_id,
            "Successfully merged territories"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'success',
                action_result = object_construct(
                    'new_scenario_id', '{res[0]}',
                    'message', '{res[1]}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)

    except MergeTerritoriesError as e:
        session.sql("ROLLBACK").collect()
        res = [
            scenario_id,
            f"Error while merging territories: {e}"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{res[0]}',
                    'message', '{res[1].replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)

    except Exception:
        session.sql("ROLLBACK").collect()
        err = traceback.format_exc()
        res = [
            scenario_id,
            f"Unexpected error while merging territories: {err}"
        ]
        session.sql(f"""
            UPDATE {log_table}
            SET action_status = 'failed',
                action_result = object_construct(
                    'scenario_id', '{res[0]}',
                    'message', '{res[1].replace("'", "''")}'
                ),
                end_timestamp = current_timestamp()
            WHERE log_id = '{log_id}'
        """).collect()
        return '###'.join(res)

    finally:
        run_ddl_rollback(session, temp_table)
$$;