"""
This script sets up the prerequisite Snowflake objects for Sigma integration.
To run this script:
    - Activate everstage environment
    - Source "interstage_project/vars.sh" file
    - Add interstage_project path to PYTHONPATH

Example usage:
    python prerequisites_setup.py

"""

import os
import traceback

import django
import snowflake.connector
import typer
from rich.console import Console

django.setup()

from everstage_ddd.tqm import utils as tqm_utils
from everstage_ddd.tqm.services.sigma_connector import SigmaConnector

# Rich setup
console = Console()
app = typer.Typer()


def get_env(key: str) -> str:
    val = os.getenv(key)
    if val is None:
        raise ValueError(f"Missing value for {key}")
    return val


class SigmaSetup:
    def __init__(self, env: str):
        self.env = env
        self.conn_params = {
            "user": get_env("SNOWFLAKE_USER"),
            "password": get_env("SNOWFLAKE_PASSWORD"),
            "account": get_env("SNOWFLAKE_ACCOUNT"),
            "warehouse": get_env("SNOWFLAKE_WAREHOUSE"),
            "role": "ACCOUNTADMIN",
        }
        self.sigma_procs_db = tqm_utils.get_snowflake_procedures_db()
        self.sigma_write_db = tqm_utils.get_snowflake_write_db()
        self.sigma_write_schema = tqm_utils.get_snowflake_write_schema()
        self.sigma_warehouse = tqm_utils.get_snowflake_warehouse()
        self.sigma_role = tqm_utils.get_snowflake_role()
        self.sigma_user = tqm_utils.get_snowflake_user()
        self.sigma_user_password = get_env("SIGMA_SNOWFLAKE_USER_PASSWORD")
        self.conn = None
        self.cursor = None

    def connect(self) -> None:
        """Establish connection to Snowflake."""
        try:
            self.conn = snowflake.connector.connect(**self.conn_params)
            self.cursor = self.conn.cursor()
            console.print(
                f"Successfully connected to Snowflake for [bold][blue]{self.env}[/blue][/bold]"
            )
        except Exception as e:
            console.print(f"[red]Failed to connect to Snowflake: {str(e)}[/red]")
            raise

    def close(self) -> None:
        """Close Snowflake connection."""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
            console.print(f"Snowflake connection closed for {self.env}")

    def execute_query(self, query: str, description: str) -> None:
        """Execute a query with error handling and logging."""
        try:
            console.print(f"[blue]{description}[/blue]")
            self.cursor.execute(query)
            console.print("[green]Done[/green]")
        except Exception as e:
            console.print(f"[red]Failed: {str(e)}[/red]")
            raise

    def setup_sigma_infrastructure(self) -> None:
        """Set up all required Sigma infrastructure."""
        # Create databases
        self.execute_query(
            f"CREATE DATABASE IF NOT EXISTS {self.sigma_procs_db}",
            f"Creating {self.sigma_procs_db} database",
        )
        self.execute_query(
            f"CREATE DATABASE IF NOT EXISTS {self.sigma_write_db}",
            f"Creating {self.sigma_write_db} database",
        )

        # Create warehouse
        self.execute_query(
            f"""
            CREATE WAREHOUSE IF NOT EXISTS {self.sigma_warehouse}
            WITH WAREHOUSE_SIZE = 'XSMALL'
            AUTO_SUSPEND = 60
            AUTO_RESUME = TRUE
            """,
            f"Creating {self.sigma_warehouse} warehouse",
        )

        # Create role
        self.execute_query(
            f"CREATE ROLE IF NOT EXISTS {self.sigma_role}",
            f"Creating {self.sigma_role} role",
        )

        # Create user with key pair auth
        self.execute_query(
            f"""
            CREATE USER IF NOT EXISTS {self.sigma_user}
            PASSWORD = '{self.sigma_user_password}'
            DEFAULT_ROLE = {self.sigma_role}
            DEFAULT_WAREHOUSE = {self.sigma_warehouse}
            """,
            f"Creating {self.sigma_user} user",
        )

        # Grant warehouse access
        self.execute_query(
            f"""
            GRANT USAGE ON WAREHOUSE {self.sigma_warehouse}
            TO ROLE {self.sigma_role}
            """,
            "Granting warehouse access",
        )
        self.execute_query(
            f"""
            GRANT USAGE ON WAREHOUSE {self.sigma_warehouse}
            TO ROLE ACCOUNTADMIN
            """,
            "Granting warehouse access to accountadmin",
        )

        # Grant stored procedures database access
        self.execute_query(
            f"GRANT USAGE ON DATABASE {self.sigma_procs_db} TO ROLE {self.sigma_role}",
            "Granting database access",
        )
        self.execute_query(
            f"GRANT USAGE ON ALL SCHEMAS IN DATABASE {self.sigma_procs_db} TO ROLE {self.sigma_role}",
            "Granting all schemas access",
        )
        self.execute_query(
            f"GRANT USAGE ON ALL PROCEDURES IN DATABASE {self.sigma_procs_db} TO ROLE {self.sigma_role}",
            "Granting all procedures access",
        )
        self.execute_query(
            f"GRANT USAGE ON FUTURE PROCEDURES IN DATABASE {self.sigma_procs_db} TO ROLE {self.sigma_role}",
            "Granting future procedures access",
        )

        # Grant all tables, future tables access
        self.execute_query(
            f"GRANT SELECT ON ALL TABLES IN DATABASE {self.sigma_procs_db} TO ROLE {self.sigma_role}",
            "Granting all tables access",
        )
        self.execute_query(
            f"GRANT SELECT ON FUTURE TABLES IN DATABASE {self.sigma_procs_db} TO ROLE {self.sigma_role}",
            "Granting future tables access",
        )

        # Grant write database access
        self.execute_query(
            f"USE DATABASE {self.sigma_write_db}",
            "Using write database",
        )
        self.execute_query(
            f"CREATE SCHEMA IF NOT EXISTS {self.sigma_write_schema}",
            f"Creating {self.sigma_write_schema} schema",
        )
        self.execute_query(
            f"GRANT USAGE ON DATABASE {self.sigma_write_db} TO ROLE {self.sigma_role}",
            "Granting database access",
        )
        self.execute_query(
            f"GRANT USAGE ON DATABASE {self.sigma_write_db} TO ROLE ACCOUNTADMIN",
            "Granting database access to accountadmin",
        )
        self.execute_query(
            f"GRANT ALL ON SCHEMA {self.sigma_write_schema} TO ROLE {self.sigma_role}",
            "Granting all schemas access",
        )
        self.execute_query(
            f"GRANT ALL ON SCHEMA {self.sigma_write_schema} TO ROLE ACCOUNTADMIN",
            "Granting all schemas access to accountadmin",
        )
        self.execute_query(
            f"GRANT SELECT ON ALL VIEWS IN DATABASE {self.sigma_write_db} TO ROLE {self.sigma_role}",
            "Granting all views access",
        )
        self.execute_query(
            f"GRANT SELECT ON ALL VIEWS IN DATABASE {self.sigma_write_db} TO ROLE ACCOUNTADMIN",
            "Granting all views access to accountadmin",
        )
        self.execute_query(
            f"GRANT SELECT ON FUTURE VIEWS IN DATABASE {self.sigma_write_db} TO ROLE {self.sigma_role}",
            "Granting future views access",
        )
        self.execute_query(
            f"GRANT SELECT ON FUTURE VIEWS IN DATABASE {self.sigma_write_db} TO ROLE ACCOUNTADMIN",
            "Granting future views access to accountadmin",
        )

        # Grant role to user
        self.execute_query(
            f"GRANT ROLE {self.sigma_role} TO USER {self.sigma_user}",
            "Granting role to user",
        )

    def create_sigma_connection(self) -> None:
        """Create connection in Sigma Computing using the SigmaConnector."""
        try:
            console.print("[blue]Creating connection in Sigma Computing...[/blue]")

            # Get account name from SNOWFLAKE_ACCOUNT env variable
            account_name = get_env("SNOWFLAKE_ACCOUNT")

            # Create connection name based on environment
            connection_name = self.env

            sigma_connector = SigmaConnector()
            connection_id = sigma_connector.create_connection(
                account_name=account_name,
                client_user=self.sigma_user,
                client_password=self.sigma_user_password,
                client_warehouse=self.sigma_warehouse,
                client_role=self.sigma_role,
                write_database=self.sigma_write_db,
                write_schema=self.sigma_write_schema,
                client_connection_name=connection_name,
            )

            console.print(
                f"[green]Successfully created Sigma connection {connection_name} ({connection_id})[/green]"
            )
        except Exception as e:
            traceback.print_exc()
            console.print(f"[red]Failed to create Sigma connection: {str(e)}[/red]")
            raise


@app.command()
def setup_sigma():
    """
    Sets up Sigma prerequisites in specified Snowflake environments.
    """
    env = tqm_utils.get_current_env()
    setup = SigmaSetup(env)
    setup.connect()
    setup.setup_sigma_infrastructure()
    setup.create_sigma_connection()
    setup.close()


if __name__ == "__main__":
    app()
