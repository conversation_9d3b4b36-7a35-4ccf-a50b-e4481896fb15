MAX_HIERARCHY_LEVELS = 10
MAX_METRICS = 10

SIGMA_SNOWFLAKE = {
    "STORED_PROCEDURES_DB_PATTERN": "SIGMA_PROCS_DB_{env}",
    "WRITE_DB_PATTERN": "SIGMA_WRITE_DB_{env}",
    "WRITE_SCHEMA_PATTERN": "SIGMA_WRITE_{env}",
    "READ_DB_PATTERN": "SIGMA_READ_DB_{env}_{client_id}",
    "COMMON_DB_PATTERN": "SIGMA_PROCS_DB_{env}",
    "WAREHOUSE_PATTERN": "SIGMA_WH_{env}",
    "ROLE_PATTERN": "SIGMA_ROLE_{env}",
    "USER_PATTERN": "SIGMA_USER_{env}",
}
