import json
import os
from typing import Literal, Optional

import requests
from django.core.cache import cache

# HTTP status codes
HTTP_UNAUTHORIZED = 401


def reauthenticate(func):
    def wrapper(self, *args, **kwargs):
        try:
            return func(self, *args, **kwargs)
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == HTTP_UNAUTHORIZED:
                self.authenticate()
                return func(self, *args, **kwargs)
            else:
                raise

    return wrapper


class SigmaConnector:

    def __init__(self):
        self.access_token = None

    def authenticate(self):
        sigma_client_id = os.getenv("SIGMA_API_CLIENT_ID")
        sigma_client_secret = os.getenv("SIGMA_API_CLIENT_SECRET")
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/auth/token"
        payload = f"grant_type=client_credentials&client_id={sigma_client_id}&client_secret={sigma_client_secret}"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        response = requests.request(
            "POST", url, headers=headers, data=payload, timeout=10
        )
        self.access_token = response.json().get("access_token")
        cache.set("SIGMA_API_ACCESS_TOKEN", self.access_token)

    def get_auth_token(self):
        token = cache.get("SIGMA_API_ACCESS_TOKEN")
        if not token:
            self.authenticate()
            return self.access_token
        return token

    @reauthenticate
    def create_workspace(self, workspace_name: str) -> str:
        """
        Create a new workspace for the client with specified workspace_name
        workspace_name : workspace_<client_id>_<client_name>
        """
        token = self.get_auth_token()
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/workspaces"
        payload = json.dumps({"name": workspace_name, "noDuplicates": True})
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}",
        }
        response = requests.request(
            "POST", url, headers=headers, data=payload, timeout=10
        )
        response.raise_for_status()
        workspace_id = response.json().get("workspaceId")
        return workspace_id

    @reauthenticate
    def create_team(self, team_name: str) -> str:
        """
        Create a new team for the client with specified team_name
        team_name : embed_users_<client_id>_<client_name>_viewers
        or
        team_name : embed_users_<client_id>_<client_name>_explorers
        """
        token = self.get_auth_token()
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/teams"
        payload = json.dumps(
            {
                "name": team_name,
                "description": "",
                "members": [],
                "createTeamFolder": False,
                "visibility": "public",
            }
        )
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}",
        }
        response = requests.request(
            "POST", url, headers=headers, data=payload, timeout=10
        )
        response.raise_for_status()
        team_id = response.json().get("teamId")
        return team_id

    @reauthenticate
    def create_connection(
        self,
        account_name: str,
        client_user: str,
        client_password: str,
        client_warehouse: str,
        client_role: str,
        write_database: str,
        write_schema: str,
        client_connection_name: str,
    ) -> str:
        """
        Create a connection for the client with specified snowflake entity

        Snowflake details of the client like account_name ["String"], client_user ["String"],
        client_password ["String"], client_warehouse ["String"],
        client_role ["String"] (role describing the permission applicable to the user),
        write_database ["String"] (nter the name of the database where Sigma should store write-back data),
        write_schema ["String"] (enter the database schema where Sigma should store write-back data),
        client connection name : <env>_<client_id>_<client_name>_conn
        """
        token = self.get_auth_token()
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/connections"
        payload = json.dumps(
            {
                "details": {
                    "type": "snowflake",
                    "account": account_name,
                    "host": f"{account_name}.snowflakecomputing.com",
                    "user": client_user,
                    "password": client_password,
                    "warehouse": client_warehouse,
                    "useTls": False,
                    "role": client_role,
                    "writeAccess": {
                        "writeDatabase": write_database,
                        "writeSchema": write_schema,
                    },
                },
                "name": client_connection_name,
                "timeoutSecs": 600,
                "useFriendlyNames": True,
            }
        )
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "authorization": f"Bearer {token}",
        }

        response = requests.request(
            "POST", url, headers=headers, data=payload, timeout=10
        )
        response.raise_for_status()
        connection_id = response.json().get("connectionId")
        return connection_id

    @reauthenticate
    def create_folder(self, folder_name: str, workspace_id: str) -> str:
        """
        Create a new folder for the client with specified folder_name
        folder_name : <tqm_plan_name>
        workspace_id ["String"] (The workspace inside which the folder is to be created)
        """
        token = self.get_auth_token()
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/files"

        payload = {"type": "folder", "parentId": workspace_id, "name": folder_name}
        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "authorization": f"Bearer {token}",
        }

        response = requests.post(url, json=payload, headers=headers, timeout=10)
        response.raise_for_status()
        folder_id = response.json().get("id")
        return folder_id

    @reauthenticate
    def copy_workbook(
        self, source_workbook_id: str, workbook_name: str, folder_id: str
    ) -> str:
        """
        Create a new workbook for the client with specified workbook_name
        by cloning the souce workbook

        source_workbook_id ["String"] (The workbook which is to be cloned)
        workbook_name : workbook_<client_id>_<client_name>
        folder_id ["String"] (The folder inside which the workbook is to be copied)
        """
        token = self.get_auth_token()
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/workbooks/{source_workbook_id}/copy"
        payload = {"destinationFolderId": folder_id, "name": workbook_name}
        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "authorization": f"Bearer {token}",
        }

        response = requests.post(url, json=payload, headers=headers, timeout=10)
        response.raise_for_status()
        workbook_id = response.json().get("workbookId")
        return workbook_id

    @reauthenticate
    def share_workbook(
        self,
        workbook_id: str,
        share_id: str,
        share_type: Literal["team", "member"],
        permission_type: Literal["view", "edit", "explore"],
    ) -> None:
        """
        Share workbook to specific users or teams

        workbook_id ["String"] (The workbook which is to be shared)
        share_id ["String"] (The member or team to whom it to be shared)
        share_type ["member" or "team"] (To whom it to be shared)
        permission_type ["edit" or "view" or "explore"] (What actions can be performed after sharing)
        """
        token = self.get_auth_token()
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/workbooks/{workbook_id}/grants"
        if share_type.lower() == "member":
            payload = {
                "grants": [
                    {
                        "grantee": {"memberId": f"{share_id}"},
                        "permission": f"{permission_type}",
                    }
                ]
            }
        else:
            payload = {
                "grants": [
                    {
                        "grantee": {"teamId": f"{share_id}"},
                        "permission": f"{permission_type}",
                    }
                ]
            }

        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "authorization": f"Bearer {token}",
        }

        response = requests.post(url, json=payload, headers=headers, timeout=10)
        response.raise_for_status()

    @reauthenticate
    def create_embed_path(self, workbook_id: str) -> str:
        """
        Get the embed path from Sigma API for a workbook

        Args:
            workbook_id (str): The ID of the workbook to embed

        Returns:
            str: The embed path from Sigma API
        """
        token = self.get_auth_token()
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/workbooks/{workbook_id}/embeds"

        payload = {"embedType": "secure", "sourceType": "workbook"}

        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "authorization": f"Bearer {token}",
        }

        response = requests.post(url, json=payload, headers=headers, timeout=10)
        response.raise_for_status()

        return response.json().get("embedUrl")

    @reauthenticate
    def get_user_attributes(self) -> list:
        """
        Get all user attributes from Sigma API.

        Returns:
            list: A list of user attributes and their configurations
        """
        token = self.get_auth_token()
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/user-attributes"

        headers = {
            "accept": "application/json",
            "authorization": f"Bearer {token}",
        }

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        return response.json()["entries"]

    @reauthenticate
    def set_user_attribute(
        self, user_attribute_id: str, user_id: str, value: str, value_type: str
    ) -> None:
        """
        Set a user attribute value for a specific user.

        Args:
            user_attribute_id (str): The ID of the user attribute to set
            user_id (str): The ID of the user to set the attribute for
            value (str): The value to set for the attribute
            value_type (str): The type of the value being set

        Returns:
            None
        """
        token = self.get_auth_token()
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/user-attributes/{user_attribute_id}/users"

        payload = {
            "assignments": [
                {"userId": user_id, "value": {"val": value, "type": value_type}}
            ]
        }

        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "authorization": f"Bearer {token}",
        }

        response = requests.post(url, json=payload, headers=headers, timeout=10)
        response.raise_for_status()

        return response.json()

    @reauthenticate
    def get_workbook(self, workbook_id: str) -> dict:
        """
        Get details of a specific workbook from Sigma API.

        Args:
            workbook_id (str): The ID of the workbook to retrieve

        Returns:
            dict: The workbook details including metadata, permissions, and configuration
        """
        token = self.get_auth_token()
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/workbooks/{workbook_id}"

        headers = {
            "accept": "application/json",
            "authorization": f"Bearer {token}",
        }

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        return response.json()

    @reauthenticate
    def get_members(self, search: Optional[str] = None) -> list:
        """
        Get all members from Sigma API with optional search filter.

        Args:
            search (str, optional): Search string to filter members. Defaults to None.

        Returns:
            list: A list of members matching the search criteria
        """
        token = self.get_auth_token()
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/members"

        if search:
            url = f"{url}?search={search}"

        headers = {
            "accept": "application/json",
            "authorization": f"Bearer {token}",
        }

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        return response.json()["entries"]

    @reauthenticate
    def search_connections(self, search: Optional[str] = None) -> list:
        """
        Search for connections in Sigma API with optional search filter.

        Args:
            search (str, optional): Search string to filter connections. Defaults to None.

        Returns:
            list: A list of connections matching the search criteria
        """
        token = self.get_auth_token()
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/connections"

        if search:
            url = f"{url}?search={search}"

        headers = {
            "accept": "application/json",
            "authorization": f"Bearer {token}",
        }

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        return response.json()["entries"]

    @reauthenticate
    def add_grants_to_connection(
        self, connection_id: str, team_ids: list[str], permission: str = "usage"
    ) -> dict:
        """
        Grant access to a connection for specific teams.

        Args:
            connection_id (str): The ID of the connection to grant access to
            team_ids (list[str]): A list of team IDs to grant access to
            permission (str, optional): The permission to grant. Defaults to "usage".

        Returns:
            dict: The API response
        """
        token = self.get_auth_token()
        sigma_base_url = os.getenv("SIGMA_BASE_URL")
        url = f"{sigma_base_url}/v2/connections/{connection_id}/grants"

        grants = []
        for team_id in team_ids:
            grants.append({"grantee": {"teamId": team_id}, "permission": permission})

        payload = {"grants": grants}

        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "authorization": f"Bearer {token}",
        }

        response = requests.post(url, json=payload, headers=headers, timeout=10)
        response.raise_for_status()

        return response.json()
