import hashlib
import hmac
import logging
import os
import re
import time
import traceback
import uuid
from typing import Dict, Optional
from urllib.parse import quote

import pydash

from commission_engine.accessors.client_accessor import (
    get_client,
    get_client_settings,
    update_tqm_settings,
)
from commission_engine.services.commission_calculation_service.reference_data_type import (
    get_datatype_id_name_map,
)
from commission_engine.utils.databook_utils import resolve_snowflake_data_type
from everstage_ddd.datasheet.selectors import (
    DatasheetSelector,
    DatasheetVariableSelector,
)
from everstage_ddd.stormbreaker.query_builder.flattened_table_query_builder import (
    FlattenedTableQueryBuilder,
)
from everstage_ddd.tqm import utils as tqm_utils
from everstage_ddd.tqm.selectors.territory_plan_selector import TerritoryPlanSelector
from everstage_ddd.tqm.services.sigma_connector import SigmaConnector
from everstage_ddd.tqm.services.sigma_snowflake_services import SigmaSnowflakeServices
from spm.services.rbac_services import get_ui_permissions

logger = logging.getLogger(__name__)


# Custom exception classes
class TerritoryPlanException(Exception):
    """Base exception for territory plan errors"""

    pass


class PlanNameExistsError(TerritoryPlanException):
    """Exception raised when a plan name already exists"""

    def __init__(self):
        super().__init__("Plan name already exists")


class DuplicateDataSourceError(TerritoryPlanException):
    """Exception raised when duplicate data sources are found"""

    def __init__(self):
        super().__init__("Duplicate datasources")


class InvalidDataSourceTagError(TerritoryPlanException):
    """Exception raised when an invalid data source tag is found"""

    def __init__(self):
        super().__init__("Invalid data source tag")


class MissingDataSourceError(TerritoryPlanException):
    """Exception raised when a required data source is missing"""

    def __init__(self, source_type):
        super().__init__(f"Please add an '{source_type}' data source")


class MissingHierarchyDataSourceError(TerritoryPlanException):
    """Exception raised when no hierarchy data source is found"""

    def __init__(self):
        super().__init__("Please add at least one 'hierarchy' data source")


class TooManyDataSourcesError(TerritoryPlanException):
    """Exception raised when too many data sources of a type are added"""

    def __init__(self, source_type, max_count):
        super().__init__(
            f"Cannot add more than {max_count} '{source_type}' data sources"
        )


class SigmaAPIError(TerritoryPlanException):
    """Exception raised when Sigma API fails"""

    def __init__(self, status_code, response_body):
        super().__init__(
            f"Sigma API failed with status code {status_code}: {response_body}"
        )


# Maximum number of data sources allowed
MAX_HIERARCHY_SOURCES = 5
MAX_OTHERS_SOURCES = 5


def get_all_territory_plans(client_id: int, **filter_params) -> Dict:
    """
    Get all territory plans for a given client
    Args:
        client_id (int): Client ID
        **filter_params: Optional filters including:
            - selected_year (str, optional): Year to filter by
            - selected_status (str, optional): Status to filter by
    Returns:
        dict: List of territory plans with requested fields
    """
    projection = [
        "tplan_id",
        "name",
        "plan_stage",
        "effective_start_date",
        "effective_end_date",
    ]

    selector = TerritoryPlanSelector(client_id)
    plans = selector.get_all_territory_plans(
        projection=projection,
        selected_year=filter_params.get("selected_year"),
        selected_status=filter_params.get("selected_status"),
        order_by=["-knowledge_begin_date"],
    )
    return plans


def get_territory_plan(client_id, data):
    plan_id = data["tplan_id"]
    external_user_id = data["external_user_id"]
    logged_in_user = data["logged_in_user"]
    tplan = TerritoryPlanSelector(client_id).get_territory_plan(
        plan_id,
        projection=[
            "tplan_id",
            "name",
            "sigma_attributes",
            "data_sources",
            "plan_stage",
            "effective_start_date",
            "effective_end_date",
        ],
    )
    embed_path = tplan.get("sigma_attributes", {}).get("embed_url")

    if not tplan:
        return None
    user_permissions = get_ui_permissions(client_id, logged_in_user)
    embed_secret = os.getenv("SIGMA_EMBED_CLIENT_SECRET")
    sigma_client_id = os.getenv("SIGMA_EMBED_CLIENT_ID")
    client_settings = get_client_settings(client_id)
    tqm_settings = client_settings.get("tqm_settings", {})
    viewers_team_name = tqm_settings.get("viewers_team", {}).get("name")
    explorers_team_id = tqm_settings.get("explorers_team", {}).get("name")
    params = {
        ":nonce": str(uuid.uuid4()),
        ":client_id": sigma_client_id,
        ":email": external_user_id,
        ":external_user_id": external_user_id,
        ":external_user_team": explorers_team_id,
        # (
        #     explorers_team_id
        #     if (RbacPermissions.EXPLORE_TERRITORY_PLANS.value) in user_permissions
        #     else viewers_team_name
        # ),
        ":account_type": "plan-viewer",
        ":mode": "userbacked",
        ":session_length": str(24 * 60 * 60),
        ":time": str(int(time.time())),
        # ":show_footer": "false",
        ":hide_schedule": "true",
        ":hide_sheet_interactions": "true",
        ":hide_send": "true",
        ":theme": quote("Everstage Brand Theme"),
        ":hide_folder_navigation": "true",
        ":hide_menu": "true",
    }
    user_attributes = {
        "client_id": client_id,
        "client_db": SigmaSnowflakeServices(client_id).get_sigma_read_db(),
        "tplan_id": tplan["tplan_id"],
        "data_source_hierarchy1_columns": None,
        "data_source_hierarchy1_filters": None,
        "data_source_hierarchy2_columns": None,
        "data_source_hierarchy2_filters": None,
        "data_source_hierarchy3_columns": None,
        "data_source_hierarchy3_filters": None,
        "data_source_hierarchy4_columns": None,
        "data_source_hierarchy4_filters": None,
        "data_source_hierarchy5_columns": None,
        "data_source_hierarchy5_filters": None,
        "data_source_accounts_columns": None,
        "data_source_accounts_filters": None,
        "data_source_accounts_primary_key_column": None,
        "data_source_users_columns": None,
        "data_source_users_filters": None,
        "data_source_others1_columns": None,
        "data_source_others1_filters": None,
        "data_source_others2_columns": None,
        "data_source_others2_filters": None,
        "data_source_others3_columns": None,
        "data_source_others3_filters": None,
        "data_source_others4_columns": None,
        "data_source_others4_filters": None,
        "data_source_others5_columns": None,
        "data_source_others5_filters": None,
        "write_db": tqm_utils.get_snowflake_write_db(),
        "write_schema": tqm_utils.get_snowflake_write_schema(),
        "common_db": tqm_utils.get_snowflake_common_db(),
    }
    hierarchy_counter = 1
    others_counter = 1
    datasheet_ids = pydash.map_(tplan["data_sources"], "datasheet_id")
    all_ds_vars = DatasheetVariableSelector(client_id).get_variables_for_datasheets(
        datasheet_ids,
        projection=["system_name", "data_type_id", "display_name", "datasheet_id"],
    )
    datatype_id_name_map = get_datatype_id_name_map()
    all_ds_vars_map = {}
    for ds_var in all_ds_vars:
        ds_var["snowflake_type"] = resolve_snowflake_data_type(
            datatype_id_name_map[ds_var["data_type_id"]], access_store="stormbreaker"
        )
        ds_var["sigma_name"] = pydash.snake_case(ds_var["display_name"])
        if str(ds_var["datasheet_id"]) not in all_ds_vars_map:
            all_ds_vars_map[str(ds_var["datasheet_id"])] = {}
        all_ds_vars_map[str(ds_var["datasheet_id"])][ds_var["system_name"]] = ds_var
    for data_source in tplan["data_sources"]:
        databook_id = data_source["databook_id"]
        datasheet_id = data_source["datasheet_id"]
        tag = data_source["tag"]
        table_name = tag[:]
        if tag == "hierarchy":
            table_name += f"{hierarchy_counter}"
            hierarchy_counter += 1
        elif tag == "others":
            table_name += f"{others_counter}"
            others_counter += 1
        q = FlattenedTableQueryBuilder(client_id, datasheet_id, databook_id)
        ds_vars_map = all_ds_vars_map[datasheet_id]
        ds_var_snowflake_type_map = {
            "row_key": "STRING",
            **{
                sys_name: ds_var["snowflake_type"]
                for sys_name, ds_var in ds_vars_map.items()
            },
        }
        filters_query_string, hidden_columns = (
            q.get_datasheet_permissions_and_hidden_columns(
                external_user_id, ds_var_snowflake_type_map
            )
        )
        if filters_query_string:
            for sys_name, ds_var in ds_vars_map.items():
                filters_query_string = filters_query_string.replace(
                    sys_name, ds_var["sigma_name"]
                )
        else:
            filters_query_string = "1=1"
        all_columns_sigma_names = set(pydash.map_(ds_vars_map, "sigma_name"))
        hidden_columns_sigma_names = set(
            [ds_vars_map[col]["sigma_name"] for col in hidden_columns]
        )
        select_columns = list(all_columns_sigma_names - hidden_columns_sigma_names)
        if tag == "accounts":
            select_columns = ["dsa." + col for col in select_columns]
        select_columns += [
            quote(
                f"cast(null as {ds_vars_map[col]['snowflake_type']}) as {ds_vars_map[col]['sigma_name']}"
            )
            for col in hidden_columns
        ]
        user_attributes[f"data_source_{table_name}_columns"] = ",".join(select_columns)
        user_attributes[f"data_source_{table_name}_filters"] = quote(
            filters_query_string
        )
        if tag == "accounts":
            datasheet = DatasheetSelector(client_id, datasheet_id).get_datasheet(
                fields=["primary_key"]
            )
            primary_key = datasheet["primary_key"][0]
            user_attributes["data_source_accounts_primary_key_column"] = (
                pydash.snake_case(ds_vars_map[primary_key]["sigma_name"])
            )
    if user_attributes:
        for key, value in user_attributes.items():
            if value:
                params[":ua_" + key] = value
    query_string = "&".join([f"{k}={v}" for k, v in params.items()])
    env = tqm_utils.get_current_env()
    tag = f"/tag/{env}"
    url_with_params = f"{embed_path}{tag}?{query_string}"
    signature = hmac.new(
        embed_secret.encode("utf-8"),
        url_with_params.encode("utf-8"),
        hashlib.sha256,
    ).hexdigest()
    embed_url = f"{url_with_params}&:signature={signature}"

    response_data = {
        "tplan_id": tplan["tplan_id"],
        "plan_name": tplan["name"],
        "data_sources": tplan["data_sources"],
        "plan_stage": tplan["plan_stage"],
        "effective_start_date": tplan["effective_start_date"],
        "effective_end_date": tplan["effective_end_date"],
        "embedUrl": embed_url,
    }
    return response_data


def delete_territory_plan(client_id, plan_id):
    try:
        TerritoryPlanSelector(client_id).delete_territory_plan(plan_id)
        return {"status": "SUCCESS", "message": "Territory plan deleted successfully"}
    except Exception as e:
        traceback.print_exc()
        return {"status": "ERROR", "message": str(e)}


def update_territory_plan(client_id, plan_id, updated_data):

    tplan = TerritoryPlanSelector(client_id).get_territory_plan(
        plan_id,
        projection=[
            "temporal_id",
            "name",
        ],
    )
    temporal_id = tplan["temporal_id"]
    tplan_name = tplan["name"]
    try:
        TerritoryPlanSelector(client_id).update_territory_plan(
            temporal_id, tplan_name, updated_data
        )
    except ValueError as e:
        raise ValueError(str(e))


def clone_territory_plan(client_id, plan_id):
    tplan = TerritoryPlanSelector(client_id).get_territory_plan(
        plan_id,
        projection=[
            "name",
            "sigma_attributes",
            "data_sources",
            "effective_start_date",
            "effective_end_date",
        ],
    )

    source_workbook_id = tplan.get("sigma_attributes", {}).get("workbook").get("id")
    base_name = f"Copy of {tplan['name']}"

    # Get all plans with similar names in one database call
    selector = TerritoryPlanSelector(client_id)
    similar_plans = selector.get_plans_with_name_pattern(base_name, projection=["name"])

    # Find the highest suffix number for exact matches only
    max_suffix = 0

    # Use a pattern that matches the exact base name followed by an optional suffix
    # This ensures we don't confuse "Copy of Plan 2025" with "Copy of Plan"
    pattern = re.compile(rf"^{re.escape(base_name)}(?:\s*\((\d+)\))?$")

    exact_name_exists = False
    for plan in similar_plans:
        match = pattern.match(plan["name"])
        if match:
            if plan["name"] == base_name:
                exact_name_exists = True
            # If there's a suffix group captured
            if match.group(1):
                suffix = int(match.group(1))
                max_suffix = max(max_suffix, suffix)

    # Create a new name with the next suffix if needed
    if exact_name_exists:
        clone_name = f"{base_name} ({max_suffix + 1})"
    else:
        clone_name = base_name

    payload_data = {
        "name": clone_name,
        "data_sources": tplan["data_sources"],
        "plan_stage": "Draft",
        "effective_start_date": tplan["effective_start_date"],
        "effective_end_date": tplan["effective_end_date"],
        "workbook_id": source_workbook_id,
    }
    return create_territory_plan(client_id, payload_data)


def create_territory_plan(client_id, payload_data):
    try:
        # Gets TQM settings from client settings table
        client_settings = get_client_settings(client_id)
        tqm_settings = client_settings.get("tqm_settings", {})
        # if not tqm_settings: (error handle)
        workspace_id = tqm_settings.get("workspace", {}).get("id")
        viewers_team_id = tqm_settings.get("viewers_team", {}).get("id")
        explorers_team_id = tqm_settings.get("explorers_team", {}).get("id")

        # Creates a folder in Sigma using the workspace ID from TQM settings.
        plan_name = pydash.snake_case(payload_data["name"]).lower()
        if TerritoryPlanSelector(client_id).check_plan_name_exists(
            payload_data["name"]
        ):
            raise PlanNameExistsError()

        # Validate duplicate datasources
        data_sources = payload_data.get("data_sources", [])
        datasource_ids = [
            (ds["databook_id"], ds["datasheet_id"], ds["tag"]) for ds in data_sources
        ]
        if len(datasource_ids) != len(set(datasource_ids)):
            raise DuplicateDataSourceError()

        # Validate the datasources before creating the territory plan
        data_sources = payload_data.get("data_sources", [])
        (
            hierarchy_source_count,
            accounts_source_count,
            users_source_count,
            others_source_count,
        ) = (0, 0, 0, 0)
        for data_source in data_sources:
            if data_source.get("tag") == "hierarchy":
                hierarchy_source_count += 1
            elif data_source.get("tag") == "accounts":
                accounts_source_count += 1
            elif data_source.get("tag") == "users":
                users_source_count += 1
            elif data_source.get("tag") == "others":
                others_source_count += 1
            else:
                raise InvalidDataSourceTagError()

        if users_source_count != 1:
            raise MissingDataSourceError("users")

        if accounts_source_count != 1:
            raise MissingDataSourceError("accounts")

        if hierarchy_source_count == 0:
            raise MissingHierarchyDataSourceError()

        if hierarchy_source_count > MAX_HIERARCHY_SOURCES:
            raise TooManyDataSourcesError("hierarchy", MAX_HIERARCHY_SOURCES)

        if others_source_count > MAX_OTHERS_SOURCES:
            raise TooManyDataSourcesError("others", MAX_OTHERS_SOURCES)

        sigma_client = SigmaConnector()
        folder_name = f"territory_plan_{plan_name}"
        folder_id = sigma_client.create_folder(
            folder_name=folder_name, workspace_id=workspace_id
        )

        client = get_client(client_id)
        client_name = pydash.snake_case(client.name).lower()
        cloned_workbook_name = f"workbook_{client_id}_{client_name}_{plan_name}"
        source_workbook_id = payload_data.get("workbook_id") or os.getenv(
            "SIGMA_TEMPLATE_WORKBOOK_ID"
        )

        # Clones a workbook into the folder, and shares it with viewer and explorer teams.
        workbook_id = sigma_client.copy_workbook(
            source_workbook_id=source_workbook_id,
            workbook_name=cloned_workbook_name,
            folder_id=folder_id,
        )

        sigma_client.share_workbook(
            workbook_id=workbook_id,
            share_id=viewers_team_id,
            share_type="team",
            permission_type="view",
        )

        sigma_client.share_workbook(
            workbook_id=workbook_id,
            share_id=explorers_team_id,
            share_type="team",
            permission_type="explore",
        )

        # Creates a secure embed-path for the workbook.
        embed_path = sigma_client.create_embed_path(workbook_id)

        data = {
            **payload_data,
            "sigma_attributes": {
                "workbook": {"id": workbook_id, "name": cloned_workbook_name},
                "folder": {"id": folder_id, "name": folder_name},
                "embed_url": embed_path,
            },
        }

        tplan = TerritoryPlanSelector(client_id).create_territory_plan(data)
        tplan_id = tplan.tplan_id

        SigmaSnowflakeServices(client_id).create_sigma_tables_for_new_tplan(
            tplan_id, payload_data
        )

        return {"status": "SUCCESS", "plan_id": tplan.tplan_id}

    except Exception as e:
        traceback.print_exc()
        if hasattr(e, "response"):
            status_code = e.response.status_code
            response_body = e.response.json()
            raise SigmaAPIError(status_code, response_body) from e
        logger.exception(
            "Failed to create territory plan",
            extra={
                "client_id": client_id,
                "error": str(e),
            },
        )
        # Re-raise the exception
        if isinstance(e, TerritoryPlanException):
            raise
        raise TerritoryPlanException(
            f"Failed to create territory plan: {str(e)}"
        ) from e


def setup_sigma_for_client(client_id: int, client_name: str) -> None:
    try:
        env = tqm_utils.get_current_env()
        client_name = pydash.snake_case(client_name).lower()
        client_settings = get_client_settings(client_id)
        tqm_settings = client_settings.get("tqm_settings", {})
        if not tqm_settings:
            # creating names accordingly
            workspace_name = f"workspace_{env}_{client_id}_{client_name}"
            team_viewers_name = f"embed_users_{env}_{client_id}_{client_name}_viewers"
            team_explorers_name = (
                f"embed_users_{env}_{client_id}_{client_name}_explorers"
            )

            # create sigma connector and call functions
            sigma_client = SigmaConnector()
            workspace_id = sigma_client.create_workspace(workspace_name)
            viewers_team_id = sigma_client.create_team(team_viewers_name)
            explorers_team_id = sigma_client.create_team(team_explorers_name)

            # create client-specific sigma read db and grant access
            SigmaSnowflakeServices(client_id).setup_sigma_read_db()

            # search for the connection for this environment and grant access to teams
            connections = sigma_client.search_connections(search=env)
            if connections:
                # Find connection with exact match to environment name
                connection_id = None
                for connection in connections:
                    # Check if connection name exactly matches the environment
                    # Assuming connection has a 'name' field - adjust if it's different
                    if connection.get("name") == env:
                        connection_id = connection["connectionId"]
                        break

                if connection_id:
                    sigma_client.add_grants_to_connection(
                        connection_id=connection_id,
                        team_ids=[viewers_team_id, explorers_team_id],
                        permission="usage",
                    )
                else:
                    logger.warning(
                        f"No exact match found for connection with environment {env}",
                        extra={"client_id": client_id},
                    )

            data = {
                "workspace": {"id": f"{workspace_id}", "name": f"{workspace_name}"},
                "viewers_team": {
                    "id": f"{viewers_team_id}",
                    "name": f"{team_viewers_name}",
                },
                "explorers_team": {
                    "id": f"{explorers_team_id}",
                    "name": f"{team_explorers_name}",
                },
            }
            update_tqm_settings(client_id, data)
    except Exception as e:
        traceback.print_exc()
        if hasattr(e, "response"):
            status_code = e.response.status_code
            response_body = e.response.json()
            raise SigmaAPIError(status_code, response_body) from e
        logger.exception(
            "Failed to setup Sigma for client",
            extra={
                "client_id": client_id,
                "error": str(e),
            },
        )
        raise TerritoryPlanException(
            f"Failed to setup Sigma for client: {str(e)}"
        ) from e


def implement_territory_plan(
    client_id: int, tplan_id: str, implementer_email: Optional[str] = None
) -> str:
    try:
        sigma_connector = SigmaConnector()
        user_attributes = sigma_connector.get_user_attributes()
        user_attributes_map = {ua["name"]: ua for ua in user_attributes}
        implementer_email = implementer_email or "<EMAIL>"
        needed_attributes = {
            "client_id": str(client_id),
            "client_db": SigmaSnowflakeServices(client_id).get_sigma_read_db(),
            "tplan_id": tplan_id,
            "data_source_hierarchy1_columns": None,
            "data_source_hierarchy1_filters": "1=1",
            "data_source_hierarchy2_columns": None,
            "data_source_hierarchy2_filters": "1=1",
            "data_source_hierarchy3_columns": None,
            "data_source_hierarchy3_filters": "1=1",
            "data_source_hierarchy4_columns": None,
            "data_source_hierarchy4_filters": "1=1",
            "data_source_hierarchy5_columns": None,
            "data_source_hierarchy5_filters": "1=1",
            "data_source_accounts_columns": None,
            "data_source_accounts_filters": "1=1",
            "data_source_accounts_primary_key_column": None,
            "data_source_users_columns": None,
            "data_source_users_filters": "1=1",
            "data_source_others1_columns": None,
            "data_source_others1_filters": "1=1",
            "data_source_others2_columns": None,
            "data_source_others2_filters": "1=1",
            "data_source_others3_columns": None,
            "data_source_others3_filters": "1=1",
            "data_source_others4_columns": None,
            "data_source_others4_filters": "1=1",
            "data_source_others5_columns": None,
            "data_source_others5_filters": "1=1",
            "write_db": tqm_utils.get_snowflake_write_db(),
            "write_schema": tqm_utils.get_snowflake_write_schema(),
            "common_db": tqm_utils.get_snowflake_common_db(),
        }
        tplan = TerritoryPlanSelector(client_id).get_territory_plan(
            tplan_id, projection=["data_sources", "sigma_attributes"]
        )
        datasheet_ids = pydash.map_(tplan["data_sources"], "datasheet_id")
        datasheet_vars = DatasheetVariableSelector(
            client_id
        ).get_variables_for_datasheets(
            datasheet_ids,
            projection=["display_name", "datasheet_id", "system_name"],
        )
        ds_vars_map = {}
        for ds_var in datasheet_vars:
            if str(ds_var["datasheet_id"]) not in ds_vars_map:
                ds_vars_map[str(ds_var["datasheet_id"])] = {}
            ds_vars_map[str(ds_var["datasheet_id"])][ds_var["system_name"]] = ds_var
        hierarchy_counter = 1
        others_counter = 1
        for data_source in tplan["data_sources"]:
            datasheet_id = data_source["datasheet_id"]
            tag = data_source["tag"]
            ua_name = f"data_source_{tag}"
            if tag == "hierarchy":
                ua_name += f"{hierarchy_counter}"
                hierarchy_counter += 1
            elif tag == "others":
                ua_name += f"{others_counter}"
                others_counter += 1
            if tag == "accounts":
                datasheet = DatasheetSelector(client_id, datasheet_id).get_datasheet(
                    fields=["primary_key"]
                )
                primary_key = pydash.snake_case(datasheet["primary_key"][0])
                needed_attributes["data_source_accounts_primary_key_column"] = (
                    pydash.snake_case(
                        ds_vars_map[datasheet_id][primary_key]["display_name"]
                    )
                )
            ua_name += "_columns"
            ds_vars = list(
                map(
                    pydash.snake_case,
                    pydash.map_(ds_vars_map[datasheet_id].values(), "display_name"),
                )
            )
            if tag == "accounts":
                ds_vars = ["dsa." + col for col in ds_vars]
            needed_attributes[ua_name] = ",".join(ds_vars)
        members = sigma_connector.get_members(search=implementer_email)
        if not members:
            raise Exception(f"User {implementer_email} not found in Sigma")
        implementer_member_id = members[0]["memberId"]
        for ua_name, value in needed_attributes.items():
            if value:
                ua = user_attributes_map[ua_name]
                sigma_connector.set_user_attribute(
                    ua["userAttributeId"],
                    implementer_member_id,
                    value,
                    ua["defaultValue"]["type"],
                )
        sigma_workbook_id = tplan["sigma_attributes"]["workbook"]["id"]
        workbook = sigma_connector.get_workbook(sigma_workbook_id)
        return workbook["url"]
    except Exception as e:
        traceback.print_exc()
        if hasattr(e, "response"):
            status_code = e.response.status_code
            response_body = e.response.json()
            raise Exception(
                f"Sigma API failed with status code {status_code}: {response_body}"
            )
        logger.exception(
            "Failed to implement territory plan",
            extra={"exception_tag": "TQM", "client_id": client_id, "error": str(e)},
        )
        raise Exception(f"Failed to implement territory plan: {str(e)}")


def update_sigma_table_for_databook(client_id, databook_id, databook_name):
    plan_ids = TerritoryPlanSelector(client_id).get_territory_plans_using_databook(
        databook_id=databook_id
    )
    if plan_ids:
        sigma_update_data = {
            "databook_name": databook_name,
            "databook_id": databook_id,
        }
        sigma_service = SigmaSnowflakeServices(client_id)
        sigma_service.connect()
        sigma_service.use_sigma_read_db()  # Ensure we're using the correct database

        for plan_id in plan_ids:
            logger.info(f"plan_id: {plan_id}")
            logger.info(f"sigma_update_data: {sigma_update_data}")
            sigma_service.update_hierarchy_level_options_v2(
                tplan_id=plan_id, updated_data=sigma_update_data
            )
        sigma_service.close()


def update_sigma_table_for_datasheet(client_id, datasheet_id, datasheet_name):
    plan_ids = TerritoryPlanSelector(client_id).get_territory_plans_using_datasheet(
        datasheet_id=datasheet_id
    )
    if plan_ids:
        sigma_update_data = {
            "datasheet_name": datasheet_name,
            "datasheet_id": datasheet_id,
        }
        sigma_service = SigmaSnowflakeServices(client_id)
        sigma_service.connect()
        sigma_service.use_sigma_read_db()  # Ensure we're using the correct database

        for plan_id in plan_ids:
            logger.info(f"plan_id: {plan_id}")
            logger.info(f"sigma_update_data: {sigma_update_data}")

            sigma_service.update_hierarchy_level_options_v2(
                tplan_id=plan_id, updated_data=sigma_update_data
            )
        sigma_service.close()
