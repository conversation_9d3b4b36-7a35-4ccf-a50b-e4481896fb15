import os

import pydash

from everstage_ddd.tqm.constants import SIGMA_SNOWFLAKE


def get_current_env() -> str:
    env = os.environ.get("ENV").lower()
    if env == "localdev":
        return env
    key = "DEPLOY_ENV"
    try:
        env = os.environ[key]
        return pydash.snake_case(env)
    except KeyError as err:
        raise ValueError(f"{key} is not set") from err


def _resolve_sigma_snowflake_setting(key: str, **resolve_params) -> str:
    pattern = SIGMA_SNOWFLAKE[key]
    env = get_current_env()
    return pattern.format(env=env, **resolve_params).upper()


def get_snowflake_procedures_db() -> str:
    return _resolve_sigma_snowflake_setting("STORED_PROCEDURES_DB_PATTERN")


def get_snowflake_write_db() -> str:
    return _resolve_sigma_snowflake_setting("WRITE_DB_PATTERN")


def get_snowflake_write_schema() -> str:
    return _resolve_sigma_snowflake_setting("WRITE_SCHEMA_PATTERN")


def get_snowflake_read_db(client_id: int) -> str:
    return _resolve_sigma_snowflake_setting("READ_DB_PATTERN", client_id=client_id)


def get_snowflake_common_db() -> str:
    return _resolve_sigma_snowflake_setting("COMMON_DB_PATTERN")


def get_snowflake_warehouse() -> str:
    return _resolve_sigma_snowflake_setting("WAREHOUSE_PATTERN")


def get_snowflake_role() -> str:
    return _resolve_sigma_snowflake_setting("ROLE_PATTERN")


def get_snowflake_user() -> str:
    return _resolve_sigma_snowflake_setting("USER_PATTERN")
