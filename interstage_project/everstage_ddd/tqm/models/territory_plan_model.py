from uuid import uuid4

from django.db.models import Index

from commission_engine.models.common_models import MultiTenantTemporal
from interstage_project.db.models import (
    EsCharField,
    EsDateTimeField,
    EsJSONField,
    EsUUIDField,
)


class TerritoryPlan(MultiTenantTemporal):
    """
    Represents a territory plan that defines sales territory assignments and rules.

    This model stores territory planning data including effective dates, data source
    configurations, and sigma calculations for territory management.
    """

    tplan_id = EsUUIDField(default=uuid4, null=False, db_index=True, is_sensitive=False)
    name = EsCharField(max_length=255, null=False, is_sensitive=False)
    plan_stage = EsCharField(max_length=255, null=False, is_sensitive=False)
    effective_start_date = EsDateTimeField(
        null=False, db_index=True, is_sensitive=False
    )
    effective_end_date = EsDateTimeField(null=False, db_index=True, is_sensitive=False)
    data_sources = EsJSONField(null=False, default=dict, is_sensitive=False)
    sigma_attributes = EsJSONField(null=False, default=dict, is_sensitive=False)

    class Meta(MultiTenantTemporal.Meta):
        db_table = "territory_plans"
        indexes = MultiTenantTemporal.Meta.indexes + [
            Index(
                fields=[
                    "client_id",
                    "tplan_id",
                ],
                name="tpi_idx",
            ),
        ]
