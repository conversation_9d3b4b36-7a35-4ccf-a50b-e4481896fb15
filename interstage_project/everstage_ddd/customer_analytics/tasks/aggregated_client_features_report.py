import logging
import os
from datetime import datetime, timedelta

import pytz
from celery import shared_task
from snowflake.connector.errors import DatabaseError
from snowflake.snowpark import Session
from snowflake.snowpark.functions import lit

from common.celery.celery_base_task import EverCeleryBaseTask
from everstage_ddd.customer_analytics.snowflake_database.snowflake_connection import (
    create_snowpark_session,
)
from everstage_ddd.customer_analytics.snowflake_database.snowflake_schemas import (
    CLIENT_FEATURES_ANALYTICS_TABLE_NAME,
)

PROD_INTERNAL_DB_NAME = os.getenv("ANALYTICS_SNOWFLAKE_PROD_REPLICATION_DATABASE")
PROD_EU_INTERNAL_DB_NAME = os.getenv("ANALYTICS_SNOWFLAKE_INTERNAL_DATABASE")
AGGREGATED_DB_NAME = os.getenv("ANALYTICS_SNOWFLAKE_AGGREGATED_DATABASE")
SCHEMA_NAME = "PUBLIC"

snapshot_date = datetime.now(pytz.UTC)
last_day_of_prev_month = snapshot_date.replace(
    day=1, hour=23, minute=59, second=59
) - timedelta(days=1)
snapshot_month = last_day_of_prev_month.strftime("%B %Y")

REFRESH_QUERY = "ALTER REPLICATION GROUP customer_analytics_rg REFRESH;"

logger = logging.getLogger(__name__)

DELETE_TABLE_QUERY = f"""
DROP TABLE IF EXISTS {AGGREGATED_DB_NAME}.{SCHEMA_NAME}.{CLIENT_FEATURES_ANALYTICS_TABLE_NAME};
"""


def get_columns_from_table(database, schema, table, session: Session):
    """Get column names from internal table"""

    query = """
        SELECT COLUMN_NAME
        FROM IDENTIFIER(:1)
        WHERE TABLE_SCHEMA ILIKE :2
          AND TABLE_NAME ILIKE :3
    """

    df = session.sql(
        query, params=[f"{database}.INFORMATION_SCHEMA.COLUMNS", schema, table]
    )
    return [row["COLUMN_NAME"] for row in df.collect()]


def insert_data_from_table(
    session: Session,
    source_table: str,
    target_table: str,
):
    """Transfer data from source to target table"""

    source_df = session.table(source_table)
    target_df = session.table(target_table)

    source_cols = set(source_df.columns)
    target_cols = target_df.columns

    common_cols = [col for col in target_cols if col in source_cols]
    missing_cols = [col for col in target_cols if col not in source_cols]

    # Select only common columns from source
    aligned_df = source_df.select(*common_cols)

    for col in missing_cols:
        aligned_df = aligned_df.with_column(col, lit(None))

    aligned_df = aligned_df.select(*target_cols)

    # Insert into target table
    aligned_df.write.mode("append").save_as_table(target_table)


def run_insert_query(session: Session):
    """Run insert queries to add data to aggregated table"""

    insert_data_from_table(
        session,
        f"{PROD_INTERNAL_DB_NAME}.{SCHEMA_NAME}.{CLIENT_FEATURES_ANALYTICS_TABLE_NAME}",
        f"{AGGREGATED_DB_NAME}.{SCHEMA_NAME}.{CLIENT_FEATURES_ANALYTICS_TABLE_NAME}",
    )

    insert_data_from_table(
        session,
        f"{PROD_EU_INTERNAL_DB_NAME}.{SCHEMA_NAME}.{CLIENT_FEATURES_ANALYTICS_TABLE_NAME}",
        f"{AGGREGATED_DB_NAME}.{SCHEMA_NAME}.{CLIENT_FEATURES_ANALYTICS_TABLE_NAME}",
    )


def run_create_query(session, all_columns):
    column_defs = ",\n".join(f"{col} STRING" for col in all_columns)
    create_query = f"""
    CREATE OR REPLACE TABLE {AGGREGATED_DB_NAME}.{SCHEMA_NAME}.{CLIENT_FEATURES_ANALYTICS_TABLE_NAME} (
    {column_defs}
    )
    """
    session.sql(create_query).collect()


@shared_task(base=EverCeleryBaseTask)
def push_aggregated_client_features_data():
    """Push aggregated data to db"""

    logger.info("Pushing the data to Snowflake...")

    try:
        with create_snowpark_session(aggregated=True) as session:
            logger.info("Refreshing Replication Group")
            session.sql(REFRESH_QUERY).collect()

            logger.info("Running delete query - %s", DELETE_TABLE_QUERY)
            session.sql(DELETE_TABLE_QUERY).collect()

            logger.info("Create table for aggregated db")
            prod_internal_columns = set(
                get_columns_from_table(
                    PROD_INTERNAL_DB_NAME,
                    SCHEMA_NAME,
                    CLIENT_FEATURES_ANALYTICS_TABLE_NAME,
                    session,
                )
            )
            prod_eu_internal_columns = set(
                get_columns_from_table(
                    PROD_EU_INTERNAL_DB_NAME,
                    SCHEMA_NAME,
                    CLIENT_FEATURES_ANALYTICS_TABLE_NAME,
                    session,
                )
            )
            all_columns = sorted(prod_internal_columns.union(prod_eu_internal_columns))
            run_create_query(session, all_columns)

            logger.info("Running insert query for client features")
            run_insert_query(session)

            logger.info("Inserted successfully!")
    except DatabaseError as error:
        logger.exception("Couldn't connect to Snowflake: %s", error.msg)
