import logging
import os

from celery import shared_task
from snowflake.connector.errors import DatabaseError
from snowflake.snowpark import Session
from snowflake.snowpark.exceptions import SnowparkSQLException

from common.celery.celery_base_task import EverCeleryBaseTask
from everstage_ddd.customer_analytics.snowflake_database.snowflake_connection import (
    create_snowpark_session,
)
from everstage_ddd.customer_analytics.snowflake_database.snowflake_schemas import (
    ACTIVE_USERS_TABLE_NAME,
    USER_MODULE_ACCESS_TABLE_NAME,
)

PROD_INTERNAL_DB_NAME = os.getenv("ANALYTICS_SNOWFLAKE_PROD_REPLICATION_DATABASE")
PROD_EU_INTERNAL_DB_NAME = os.getenv("ANALYTICS_SNOWFLAKE_INTERNAL_DATABASE")
AGGREGATED_DB_NAME = os.getenv("ANALYTICS_SNOWFLAKE_AGGREGATED_DATABASE")
SCHEMA_NAME = "PUBLIC"

TABLE_NAME_COLUMNS_MAP = {
    ACTIVE_USERS_TABLE_NAME: """
    snapshot_date,
    snapshot_month,
    region,
    client_name,
    client_id,
    analytics_user_id,
    designation,
    employment_country,
    payout_currency,
    added_date,
    status,
    payout_frequency,
    mapped_or_unmapped,
    user_role,
    exit_date,
    last_commission_date,
    spiff,
    main,
    is_active
    """,
    USER_MODULE_ACCESS_TABLE_NAME: """
    snapshot_date,
    snapshot_month,
    region,
    client_name,
    client_id,
    analytics_user_id,
    designation,
    employment_country,
    payout_currency,
    added_date,
    status,
    payout_frequency,
    mapped_or_unmapped,
    user_role,
    exit_date,
    last_commission_date,
    spiff,
    main,
    is_crystal_used,
    is_email_notifications_used,
    is_slack_notifications_used,
    is_msteams_notifications_used,
    is_contracts_used,
    is_analytics_used,
    is_approval_workflows_used
    """,
}

REFRESH_QUERY = "ALTER REPLICATION GROUP customer_analytics_rg REFRESH;"

DELETE_QUERY = "DELETE FROM {db_name}.{schema_name}.{table_name} ;"

BASE_INSERT_QUERY = """
INSERT INTO {db_name}.{schema_name}.{table_name} (
    {columns}
)
SELECT
    {columns}
FROM {prod_db_name}.{schema_name}.{table_name}
UNION
SELECT
    {columns}
FROM {prod_eu_db_name}.{schema_name}.{table_name} ;
"""

logger = logging.getLogger(__name__)


def delete_and_insert_records(session: Session, table_name: str):
    """
    Deletes existing records from the table and inserts new records
    """
    try:
        session.sql("BEGIN;").collect()

        # Delete existing records
        logger.info(f"Deleting existing records from {table_name}...")
        session.sql(
            DELETE_QUERY.format(
                db_name=AGGREGATED_DB_NAME,
                schema_name=SCHEMA_NAME,
                table_name=table_name,
            )
        ).collect()
        logger.info(f"Deleted existing records from {table_name} successfully!")

        # Insert new records
        logger.info(f"Inserting records into {table_name}...")
        insert_query = BASE_INSERT_QUERY.format(
            db_name=AGGREGATED_DB_NAME,
            schema_name=SCHEMA_NAME,
            table_name=table_name,
            columns=TABLE_NAME_COLUMNS_MAP[table_name],
            prod_db_name=PROD_INTERNAL_DB_NAME,
            prod_eu_db_name=PROD_EU_INTERNAL_DB_NAME,
        )
        session.sql(insert_query).collect()

        session.sql("COMMIT;").collect()
        logger.info(f"Inserted records into {table_name} successfully!")
    except SnowparkSQLException as e:
        session.sql("ROLLBACK;").collect()
        logger.warning(f"Transaction rolled back due to the following error:\n{e}")


@shared_task(base=EverCeleryBaseTask)
def push_aggregated_data_to_snowflake():
    logger.info("Pushing the data to Snowflake...")

    try:
        with create_snowpark_session(aggregated=True, autocommit=False) as session:
            logger.info("Refreshing Replication Group")
            session.sql("BEGIN;").collect()
            session.sql(REFRESH_QUERY).collect()
            session.sql("COMMIT;").collect()
            delete_and_insert_records(session, ACTIVE_USERS_TABLE_NAME)
            delete_and_insert_records(session, USER_MODULE_ACCESS_TABLE_NAME)
            logger.info("Completed pushing data to Snowflake successfully!")
    except DatabaseError as e:
        logger.exception(f"Couldn't connect to Snowflake: {e.msg}")
