import json
from collections import defaultdict

from everstage_ddd.datasheet_builder.constants import DEFAULT_SELECTED_SALESFORCE_FIELDS
from everstage_ddd.datasheet_builder.exceptions import (
    JoinValidationFieldNotFoundError,
    JoinValidationTypeMismatchError,
)


def filter_selected_fields(nodes):
    nodes = json.loads(nodes)
    for node in nodes:
        node["data"]["fields"] = [
            field for field in node["data"]["fields"] if field["isSelected"]
        ]
    return json.dumps(nodes)


def compare_alias_names(name1, name2):
    if len(name1) < len(name2):
        return -1
    elif len(name1) > len(name2):
        return 1

    if name1 < name2:
        return -1
    elif name1 > name2:
        return 1
    else:
        return 0


def get_highest_alias_name(alias_names):
    if not alias_names:
        return None

    highest = next(iter(alias_names))
    for name in alias_names:
        if compare_alias_names(highest, name) < 0:
            highest = name

    return highest


def get_next_alias_name(alias):
    if not alias:
        return "A"

    chars = list(alias)
    pos = len(chars) - 1

    while pos >= 0:
        if chars[pos] < "Z":
            chars[pos] = chr(ord(chars[pos]) + 1)
            return "".join(chars)
        else:
            chars[pos] = "A"
            pos -= 1

    return "A" + "".join(chars)


def construct_prompt_for_node_creation_agent(
    user_prompt, nodes, edges, access_token_config_id, frequent_objects
):
    return f"""
    Goal/User-prompt: {user_prompt}
    Context:
    accessTokenConfigId: {access_token_config_id}
    nodes: {nodes}
    edges: {edges}
    Conditional system prompt:
    If user specifies you to create nodes for the frequently used objects or Quick picks or Most used objects or Favourite objects, then execute bring in the objects {frequent_objects} and all these nodeOrder 0 nodes should be generated.
    """


def construct_prompt_for_datasheet_creation_agents(
    current_step: int, node: dict, databook_id: int, databook_name: str
):
    return (
        f"This node below belongs to nodeOrder = {current_step}"
        f"node: {node}"
        f"databookId: {databook_id}"
        f"databookName: {databook_name}"
    )


def group_nodes_by_order(nodes: list[dict]):
    grouped_nodes = defaultdict(list)

    for node in nodes:
        node_order = node.get("data", {}).get("node_order")
        if node_order is not None:
            grouped_nodes[node_order].append(node)

    return dict(grouped_nodes)


def parse_node_operations(node_operations):
    if isinstance(node_operations, str):
        return json.loads(node_operations)
    return node_operations


def combine_fields(parent_datasheets, nodes):
    return [
        {
            **field,
            "name": f"{parent_datasheet}::{field['name']}",
        }
        for parent_datasheet in parent_datasheets
        for node in nodes
        if node["data"]["label"] == parent_datasheet
        for field in node["data"]["fields"]
        if field["isSelected"]
    ]


def get_parent_datasheets(sources, nodes):
    return [
        node["data"]["label"]
        for source_id in sources
        for node in nodes
        if node["id"] == source_id
    ]


def set_selected_fields(_unused_object_id, field):
    return (
        field["name"] in DEFAULT_SELECTED_SALESFORCE_FIELDS
        or field["type"] == "reference"
    )


def validate_join_condition(transformation: dict, field_names: set):
    if transformation["lhsField"] not in field_names:
        raise JoinValidationFieldNotFoundError(
            {"field": transformation.get("lhsField", ""), "source_type": "LHS"}
        )
    if transformation["rhsField"] not in field_names:
        raise JoinValidationFieldNotFoundError(
            {"field": transformation.get("rhsField", ""), "source_type": "RHS"}
        )
    if transformation["lhsDataType"] != transformation["rhsDataType"]:
        raise JoinValidationTypeMismatchError()
