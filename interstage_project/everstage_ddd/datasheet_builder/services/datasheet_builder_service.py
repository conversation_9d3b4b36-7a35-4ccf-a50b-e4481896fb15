import datetime
import json
import logging
import uuid

import interstage_project.utils as iputils
from commission_engine.accessors.client_accessor import get_client_subscription_plan
from commission_engine.accessors.custom_object_accessor import CustomObjectAccessor
from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from commission_engine.accessors.etl_config_accessor import IntegrationAccessor
from commission_engine.services import etl_sync_status_service
from commission_engine.services.etl_global_sync_status_service import (
    EtlGlobalSyncStatusService,
)
from commission_engine.services.etl_tasks_service import (
    ETLSync,
    run_databook_commission_async,
)
from commission_engine.utils import ETL_ACTIVITY, ETL_STATUS, make_aware
from everstage_ddd.datasheet_builder import utils
from everstage_ddd.datasheet_builder.constants import (
    DATATYPE_ID_MAPPINGS,
    VARIABLES_DATA_TYPE_MAP,
    EdgeOperations,
    NodeOperations,
    SkillTags,
)
from everstage_ddd.datasheet_builder.exceptions import DatasheetBuilder<PERSON>rror
from everstage_ddd.datasheet_builder.utils import (
    combine_fields,
    get_highest_alias_name,
    get_next_alias_name,
    get_parent_datasheets,
    set_selected_fields,
    validate_join_condition,
)
from everstage_ddd.llm_agent import mannai_interface
from everstage_ddd.self_service_integration.services.self_service_integration_service import (
    create_self_serve_integrated_object,
    get_fields_for_an_object,
)
from everstage_ddd.upstream.extraction.utils import set_upstream_timestamps
from interstage_project.celery import TaskGroupEnum

logger = logging.getLogger(__name__)


def generate_nodes_for_sheet_builder(  # noqa: PLR0912
    client_id, node_operations, service_name, access_token_config_id, nodes
):
    nodes = json.loads(nodes)
    alias_names = {node["data"]["aliasName"] for node in nodes}
    for operation in node_operations:
        if operation["operationType"] in {
            NodeOperations.ADD_NODE.value,
            NodeOperations.UPDATE_NODE.value,
        }:
            if operation["operationType"] == NodeOperations.ADD_NODE.value:
                if alias_names:
                    highest_letter = get_highest_alias_name(alias_names)
                    next_alias_name = get_next_alias_name(highest_letter)
                else:
                    next_alias_name = "A"
                alias_names.add(next_alias_name)
                operation["node"]["data"]["alias_name"] = next_alias_name
            operation["node"]["type"] = "tableNode"
            node_data = operation["node"]["data"]
            if node_data["nodeOrder"] == 0:
                new_object_name = operation["node"]["data"]["label"]
                itr = 1
                while CustomObjectAccessor(
                    client_id=client_id
                ).does_custom_object_name_exists(new_object_name):
                    new_object_name = f"{operation['node']['data']['label']}_{str(itr)}"
                    itr += 1
                operation["node"]["data"]["destination_name"] = new_object_name
                object_id = operation["node"]["id"]
                all_fields = get_fields_for_an_object(
                    client_id=client_id,
                    object_id=object_id,
                    service_name=service_name,
                    access_token_config_id=access_token_config_id,
                )
                node_data["primary_key"] = []
                node_data["fields"] = []
                for field in all_fields:
                    node_data["fields"].append(
                        {
                            "name": field["name"],
                            "type": VARIABLES_DATA_TYPE_MAP.get(
                                field["type"], "String"
                            ),
                            "is_primary_key": field["type"] == "id",
                            "is_selected": set_selected_fields(object_id, field),
                        }
                    )
                    if field["type"] == "id":
                        node_data["primary_key"].append(field["name"])

            elif "sources" in node_data:
                node_data["parentDatasheets"] = get_parent_datasheets(
                    node_data["sources"], nodes
                )
                node_data["fields"] = combine_fields(
                    node_data["parentDatasheets"], nodes
                )
            if node_data["nodeOrder"] > 0:
                field_names = {field["name"] for field in node_data["fields"]}
                for transformation in node_data["transformations"]:
                    if transformation["type"] == "JOIN":
                        validate_join_condition(transformation, field_names)

        elif operation["operationType"] in {
            EdgeOperations.ADD_EDGE.value,
            EdgeOperations.UPDATE_EDGE.value,
        }:
            operation["edge"]["type"] = "customEdge"
            operation["edge"]["key"] = (
                operation["edge"]["source"] + "_to_" + operation["edge"]["target"]
            )

    return node_operations


def get_parent_datasheet_variables(client_id, datasheet_id):
    variables = DatasheetVariableAccessor(
        client_id=client_id
    ).get_ds_variables_for_databook_builder(datasheet_id)
    variables = list(variables)
    for variable in variables:
        variable["data_type"] = variable.pop("data_type__data_type")
        variable["data_type_id"] = variable.pop("data_type__id")
    return variables


def create_custom_objects(
    client_id, nodes, access_token_config_id, request
) -> list[int]:
    created_custom_object_ids = []
    for node in nodes:
        field_mappings = [
            {
                "source_field": field["name"],
                "destination_field": field["name"],
                "field_type": field["type"],
                "is_association": False,
                "applied_function": "",
            }
            for field in node["data"]["fields"]
            if field["is_selected"]
        ]
        service_name = "salesforce"
        object_id = node["id"]
        pipeline_type = ""
        has_associations = False
        hyperlinked_field = None
        hyperlinked_url = None
        associated_objects = []
        include_hard_delete_config = False
        changes_sync_field = "{}"
        delete_sync_field = "{}"
        sync_type = "changes"
        object_data = {
            "name": node["data"]["destination_name"],
            "primary_key": node["data"]["primary_key"],
            "snapshot_key": node["data"]["primary_key"],
            "variables": [
                {
                    "display_name": field["name"],
                    "id": DATATYPE_ID_MAPPINGS[field["type"]],
                }
                for field in node["data"]["fields"]
                if field["is_selected"]
            ],
        }
        custom_object_id = create_self_serve_integrated_object(
            client_id=client_id,
            request=request,
            create_custom_object=True,
            service_name=service_name,
            object_id=object_id,
            field_mappings=field_mappings,
            access_token_config_id=access_token_config_id,
            object_data=object_data,
            pipeline_type=pipeline_type,
            has_associations=has_associations,
            hyperlinked_field=hyperlinked_field,
            hyperlinked_url=hyperlinked_url,
            include_hard_delete_config=include_hard_delete_config,
            changes_sync_field=changes_sync_field,
            delete_sync_field=delete_sync_field,
            associated_objects=associated_objects,
            syncType=sync_type,
        )
        created_custom_object_ids.append(custom_object_id)
    return created_custom_object_ids


def get_integration_ids(client_id, destination_object_ids):
    integrations = IntegrationAccessor(
        client_id=client_id
    ).get_records_by_destination_object_ids(
        destination_object_ids=destination_object_ids
    )
    integration_ids = [integration.integration_id for integration in integrations]
    return integration_ids


def set_upstream_timestamp(client_id, integration_ids):
    yrt = datetime.datetime.now() - datetime.timedelta(days=90)  # noqa: DTZ005
    for integration_id in integration_ids:
        set_upstream_timestamps(
            client_id=client_id,
            integration_id=integration_id,
            timestamps={
                "api_changes_synced_till": yrt,
                "api_deletes_synced_till": yrt,
                "upstream_source_synced_till": yrt,
                "historic_sync_date": yrt,
            },
        )


def create_datasheets(nodes, databook_id, databook_name):  # Yet to be implemented
    try:
        create_datasheets_payload = [
            {
                "expressionBoxVersion": "v2",
                "databookId": databook_id,
                "databookName": databook_name,
                "datasheetId": None,
                "name": node["data"]["destination_name"],
                "description": f"This is a datasheet for the custom object {node['data']['destination_name']}",  # Should be generated by agent
                "sourceType": "object",
                "sourceId": "",  # TODO: get the custom object id from the custom object metadata
                "sourceName": node["data"]["destination_name"],
                "dataOrigin": "custom_object",
                "sourceDatabookId": None,
                "with_databook_id": databook_id,
                "transformationSpec": [],
                "variables": [
                    {
                        "systemName": "",  # TODO: get the system name from the custom object metadata
                        "displayName": field["name"],
                        "dataTypeId": DATATYPE_ID_MAPPINGS[field["type"]],
                        "sourceCfMetaData": None,
                        "id": "",  # TODO: get the id from the custom object metadata
                    }
                    for field in node["data"]["fields"]
                ],
                "primaryKey": [
                    ""  # TODO: get the primary key from the custom object metadata
                ],
            }
            for node in nodes
            if node["data"]["nodeOrder"]
        ]
        logger.info(f"Create datasheets payload: {create_datasheets_payload}")
        # TODO: Create datasheets with this payload and return the response accordingly
        return {"status": "success", "message": "Datasheets created successfully"}
    except Exception as e:
        return {"status": "failed", "message": str(e)}


def can_run_sync(client_id: int):
    return (
        EtlGlobalSyncStatusService.is_global_sync_enabled_for_client(client_id)
        and not etl_sync_status_service.is_end_to_end_sync_running(client_id)
        and not etl_sync_status_service.is_upstream_sync_running(client_id)
    )


def refresh_databook(
    client_id: int, databook_params: dict, queue_name: str, audit: dict
) -> list | None:

    if not can_run_sync(client_id=client_id):
        logger.info("Unable to refresh databook")
        return

    e2e_sync_run_id = databook_params["e2e_sync_run_id"]
    activity = ETL_ACTIVITY.REFRESH_DATABOOK.value
    databook_ids = databook_params["databook_ids"]

    etl_status_params = dict()
    etl_status_params["run_report_sync"] = 0
    etl_status_params["databook_ids"] = (
        [str(databook_id) for databook_id in databook_ids] if databook_ids else None
    )
    additional_info = {
        "is_triggered_from_datasheet_builder": True,
    }

    etl_sync_status_service.insert_etl_sync_status(
        client_id,
        e2e_sync_run_id,
        activity,
        ETL_STATUS.STARTED.value,
        make_aware(datetime.datetime.now()),  # noqa: DTZ005
        audit,
        etl_status_params,
        additional_info=additional_info,
    )

    refresh_databook = run_databook_commission_async.si(
        params=databook_params, is_flow_eligible_for_trigger_only_associated_ds=True
    ).set(queue=queue_name)

    refresh_databook.apply_async(compression="lzma", serializer="pickle")


def run_upstream_sync(
    client_id: int,
    e2e_sync_run_id: uuid.UUID,
    log_context: dict,
    client_info: dict,
    integration_ids: list[uuid.UUID],
) -> None:

    if not can_run_sync(client_id=client_id):
        logger.info("Unable to run upstream sync")
        return

    ETLSync(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        log_context=log_context,
        notification_email_id=None,
        skip_archived_books=True,
        run_previous_period_sync=False,
        post_upstream_disabled=True,
    ).run_daily_sync_wrapper(
        all_objects_selected=False,
        integration_ids=list(map(str, integration_ids)),
        include_upstream_hard_delete_sync=False,
        audit=client_info["audit"],
    )


def create_datasheets_using_agent(
    client_id: int,
    params: dict,
    client_info: dict,
    *,
    show_data_sources_v2: bool = False,
) -> None:
    email_id: str = client_info["email"]
    databook_id: int = params["databook_id"]
    databook_name: str = params["databook_name"]
    nodes: list[dict] = params["nodes"]
    auth: str = client_info["auth"]

    if show_data_sources_v2:
        skill_tag = SkillTags.DATASHEET_CREATION_AGENT_V2.value
    else:
        skill_tag = SkillTags.DATASHEET_CREATION_AGENT.value

    logger.info(
        f"BEGIN: Datasheet Builder for client_id={client_id}, databook_id={databook_id}",
    )
    grouped_nodes = utils.group_nodes_by_order(nodes)
    levels = sorted(grouped_nodes.keys())[1:]

    for level in levels:
        logger.info(f"Processing step {level} with {len(grouped_nodes[level])} nodes.")
        for node in grouped_nodes[level]:
            logger.info(f"Creating datasheet name: {node['data']['label']}")
            node["data"]["fields"] = [
                field for field in node["data"]["fields"] if field["is_selected"]
            ]
            user_prompt = utils.construct_prompt_for_datasheet_creation_agents(
                current_step=level,
                node=node,
                databook_id=databook_id,
                databook_name=databook_name,
            )

            retry, failed_response = 3, True
            while retry:
                ai_generated_content, llm_status = mannai_interface.execute_skill(
                    client_id=client_id,
                    skill_tag=skill_tag,
                    employee_email_id=email_id,
                    user_prompt=user_prompt,
                    tools_required=True,
                    user_access_token=auth,
                    return_result=True,
                )
                logger.info(
                    f'Output from {skill_tag} \
                    skill: {llm_status} {ai_generated_content.get("json")} \
                    type: {type(ai_generated_content.get("json"))}'
                )
                final_json_status = (
                    ai_generated_content.get("ai_generated_content", {})
                    .get("json", {})
                    .get("status")
                )
                failed_response = llm_status.lower() != "success" or (
                    not isinstance(final_json_status, str)
                    or final_json_status.lower() != "success"
                )

                if not failed_response:
                    break
                retry -= 1

            if failed_response:
                logger.error(f"Step {level} failed: LLM status: {llm_status}")
                raise DatasheetBuilderError("Failed")

        logger.info(f"Step {level} processed successfully.")

    logger.info(
        f"END: Datasheet Builder for client_id={client_id}, databook_id={databook_id}",
    )


def sheet_builder_service(
    client_info: dict,
    params: dict,
) -> None:
    """
    Runs the datasheet builder sync process with five main tasks:
    1. Create Custom Object
    2. Create Databook
    3. Upstream sync for custom objects
    4. Commission sync for the created objects
    5. Create datasheets for the objects
    """
    from everstage_ddd.datasheet_builder.tasks.sheet_builder_wrapper import (
        sheet_builder_wrapper,
    )

    try:
        client_id = client_info["client_id"]
        upstream_e2e_sync_run_id = uuid.uuid4()
        audit = client_info["audit"]

        logger_context = {
            "client_id": client_id,
            "task_id": upstream_e2e_sync_run_id,
            "task": "Datasheet Builder",
        }

        upstream_params = {
            "client_id": client_id,
            "log_context": logger_context,
            "e2e_sync_run_id": upstream_e2e_sync_run_id,
            "all_objects_selected": False,
        }
        databook_e2e_sync_run_id = uuid.uuid4()
        databook_params = {
            "client_id": client_id,
            "e2e_sync_run_id": databook_e2e_sync_run_id,
            "log_context": {
                "client_id": client_id,
                "e2e_sync_run_id": databook_e2e_sync_run_id,
                "run_databook_sync_only": True,
                "task": "Datasheet Builder",
            },
            "notification_email_id": None,
            "refresh_databook": False,
            "payee_list": None,
            "audit": audit,
            "curr_date": None,
            "end_date": None,
            "skip_archived_books": False,
            "run_databook_sync_only": True,
            "run_previous_period_sync": False,
            "logger": logger,
        }

        subscription_plan = get_client_subscription_plan(client_id)
        queue_name = iputils.get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.MISC.value
        )
        params["queue_name"] = queue_name

        builder_task = sheet_builder_wrapper.si(
            client_id=client_id,
            params=params,
            databook_params=databook_params,
            upstream_params=upstream_params,
            client_info=client_info,
        ).set(queue=queue_name)

        builder_task.apply_async(compression="lzma", serializer="pickle")

    except DatasheetBuilderError as e:
        logger.exception(
            f"Datasheet builder sync error: {e.message}",
            extra={"client_id": client_id},
        )
        raise DatasheetBuilderError(str(e)) from e
    except Exception as e:
        logger.exception(
            "Unexpected error in datasheet builder sync",
            extra={
                "client_id": client_id,
            },
        )
        raise DatasheetBuilderError(str(e)) from e
