from logging import Logger, getLogger
from typing import Any

from celery import shared_task
from django.db import transaction
from django.utils import timezone

from common.celery.celery_base_task import EverCeleryBaseTask
from everstage_ddd.datasheet_builder.exceptions import DatasheetBuilderError
from everstage_ddd.datasheet_builder.models import CustomObjectRequest, DatabookRequest
from interstage_project.threadlocal_log_context import set_threadlocal_context

logger: Logger = getLogger(__name__)


def validate_response(databook_response: Any) -> str:
    """
    Validate the databook creation response and extract the databook ID.
    """
    if not hasattr(databook_response, "data"):
        raise DatasheetBuilderError(message="Invalid databook response format")

    response_data = databook_response.data
    if not response_data:
        raise DatasheetBuilderError(message="Databook Creation Failed")

    status_value = response_data.get("status")
    if not status_value or status_value.lower() != "success":
        raise DatasheetBuilderError(message="Databook Creation Failed")

    return extract_databook_id(response_data)


def extract_databook_id(response_data: dict) -> str:
    databook_id = response_data.get("databookId")
    if not databook_id:
        raise DatasheetBuilderError(
            message="Invalid databook response: missing databookId"
        )

    return str(databook_id)


@shared_task(base=EverCeleryBaseTask)
def sheet_builder_wrapper(
    client_id: int,
    params: dict,
    databook_params: dict,
    upstream_params: dict,
    client_info: dict,
):
    from everstage_ddd.datasheet.services import (
        databook_services as databook_services_v2,
    )
    from everstage_ddd.datasheet_builder.services import datasheet_builder_service
    from spm.services import databook_services as databook_services_v1

    try:
        custom_object = params["custom_object"]
        databook_name = params["databook"]["name"]
        datasheet_data = params["datasheet"]
        e2e_sync_run_id = upstream_params["e2e_sync_run_id"]
        log_context = upstream_params["log_context"]
        show_data_sources_v2 = params["show_data_sources_v2"]
        queue_name = params["queue_name"]

        logger_context = {
            "client_id": client_id,
            "e2e_sync_run_id": e2e_sync_run_id,
            "task": "Datasheet Builder",
        }
        set_threadlocal_context(logger_context)
        logger.info("BEGIN: Databook Builder task")

        zero_order_nodes = list(
            filter(
                lambda node: node["data"].get("node_order") == 0, custom_object["nodes"]
            )
        )

        with transaction.atomic():
            logger.info("BEGIN: Custom Object Creation")

            custom_object_info = {
                "audit": dict(client_info["audit"]),
                "user": client_info["user"],
                "logger": client_info["logger"],
            }

            co_request = CustomObjectRequest(**custom_object_info)
            custom_object_ids = datasheet_builder_service.create_custom_objects(
                client_id=client_id,
                nodes=zero_order_nodes,
                access_token_config_id=custom_object["access_token_config_id"],
                request=co_request,
            )
            logger.info("END: Custom Object Creation")

            logger.info("BEGIN: Databook Creation")
            if show_data_sources_v2:
                time = timezone.now()
                databook_data = {
                    "client_id": client_id,
                    "additional_details": client_info["audit"],
                    "knowledge_begin_date": time,
                    "created_at": time,
                    "created_by": client_info["email"],
                    "name": databook_name,
                }
                databook_response = databook_services_v2.create_databook(
                    databook_data
                ).model_dump()
                databook_id = extract_databook_id(databook_response)
            else:
                databook_data = {
                    "client_id": client_id,
                    "data": params["databook"],
                    "audit": dict(client_info["audit"]),
                    "user": {"username": client_info["email"]},
                    "logger": client_info["logger"],
                }
                request = DatabookRequest(**databook_data)
                databook_response = databook_services_v1.create_databook(
                    request=request
                )
                databook_id = validate_response(databook_response)

            logger.info("END: Databook Creation")

        datasheet_data["databook_id"] = databook_id
        datasheet_data["databook_name"] = databook_name
        integration_ids = datasheet_builder_service.get_integration_ids(
            client_id=client_id, destination_object_ids=custom_object_ids
        )
        datasheet_builder_service.set_upstream_timestamp(client_id, integration_ids)
        databook_params["databook_ids"] = [databook_id]

        etl_status_params = {
            "databook_ids": [str(databook_id)],
            "integration_ids": list(map(str, integration_ids)),
            "run_report_sync": 0,
        }
        databook_params["etl_status_params"] = etl_status_params

        datasheet_builder_service.run_upstream_sync(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            log_context=log_context,
            client_info=client_info,
            integration_ids=integration_ids,
        )

        datasheet_builder_service.create_datasheets_using_agent(
            client_id=client_id,
            params=datasheet_data,
            client_info=client_info,
            show_data_sources_v2=show_data_sources_v2,
        )

        datasheet_builder_service.refresh_databook(
            client_id=client_id,
            databook_params=databook_params,
            queue_name=queue_name,
            audit=client_info["audit"],
        )
        logger.info("END: Databook Builder task")

    except Exception:
        logger.exception("Datasheet Builder failed with error")
