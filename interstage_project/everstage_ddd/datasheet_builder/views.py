import logging

from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.accessors.client_accessor import check_everai_access
from commission_engine.utils.general_data import RbacPermissions
from everstage_ddd.datasheet.models.datasheet_models import Databook
from everstage_ddd.datasheet_builder.constants import SkillTags
from everstage_ddd.datasheet_builder.services import datasheet_builder_service
from everstage_ddd.datasheet_builder.services.datasheet_builder_service import (
    generate_nodes_for_sheet_builder,
    get_parent_datasheet_variables,
)
from everstage_ddd.datasheet_builder.utils import (
    construct_prompt_for_node_creation_agent,
    filter_selected_fields,
    parse_node_operations,
)
from everstage_ddd.llm_agent.mannai_interface import execute_skill
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import add_log_context_view

logger = logging.getLogger(__name__)


class GenerateNodesView(APIView):
    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_DATASETTINGS.value]),
        name="dispatch",
    )
    @add_log_context_view("GenerateNodes")
    def post(self, request):
        client_id = request.client_id
        auth = request.auth
        email_id = request.user.username
        if not check_everai_access(client_id):
            return Response(
                {"message": "You are not authorized to access this feature."},
                status=status.HTTP_403_FORBIDDEN,
            )
        nodes = request.data.get("nodes")
        edges = request.data.get("edges")
        user_prompt = request.data.get("user_prompt")
        service_name = request.data.get("service_name")
        access_token_config_id = request.data.get("access_token_config_id")
        frequent_objects = request.data.get("frequent_objects")
        if "nodes" not in request.data or "edges" not in request.data:
            return Response(
                {"message": "Missing few required fields"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            nodes = filter_selected_fields(nodes)
            user_prompt = construct_prompt_for_node_creation_agent(
                user_prompt, nodes, edges, access_token_config_id, frequent_objects
            )
            retry = 2
            while True:
                ai_generated_content, llm_status = execute_skill(
                    client_id=client_id,
                    skill_tag=SkillTags.NODES_GENERATION_AGENT.value,
                    employee_email_id=email_id,
                    user_prompt=user_prompt,
                    tools_required=True,
                    user_access_token=auth,
                    return_result=True,
                )
                if (
                    ai_generated_content["ai_generated_content"]["json"].get(
                        "outputData"
                    )
                    or retry == 0
                ):
                    break
                retry -= 1

            ai_generated_content = ai_generated_content["ai_generated_content"]
            logger.info(
                f'Output from {SkillTags.NODES_GENERATION_AGENT.value} skill: {llm_status} {ai_generated_content.get("json")} type: {type(ai_generated_content.get("json"))}'
            )
            if llm_status == "Success" and ai_generated_content.get("json"):
                json_content = ai_generated_content["json"]
                node_operations = parse_node_operations(json_content)
                node_operations = generate_nodes_for_sheet_builder(
                    client_id=client_id,
                    node_operations=node_operations["outputData"],
                    service_name=service_name,
                    access_token_config_id=access_token_config_id,
                    nodes=nodes,
                )
                return Response(
                    {"status": "success", "data": node_operations},
                    status=status.HTTP_200_OK,
                )
            return Response(
                {"status": "failed", "message": "Error generating nodes"},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logger.exception("Error generating nodes")
            return Response(
                {"message": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GetParentDatasheetVariablesView(APIView):
    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_DATASETTINGS.value]),
        name="dispatch",
    )
    @add_log_context_view("GetParentDatasheetVariables")
    def post(self, request):
        try:
            client_id = request.client_id
            if not check_everai_access(client_id):
                return Response(
                    {"message": "You are not authorized to access this feature."},
                    status=status.HTTP_403_FORBIDDEN,
                )
            datasheet_id = request.data.get("datasheet_id")
            parent_datasheet_variables = get_parent_datasheet_variables(
                client_id=client_id, datasheet_id=datasheet_id
            )
            return Response(
                {
                    "status": "success",
                    "message": "Parent datasheets fetched successfully",
                    "parent_datasheet_variables": parent_datasheet_variables,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logger.exception("Error getting parent datasheet variables")
            return Response(
                {"message": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class SheetBuilderView(APIView):
    @method_decorator(
        requires_scope([RbacPermissions.MANAGE_DATASETTINGS.value]),
        name="dispatch",
    )
    @add_log_context_view("Sheet Builder")
    def post(self, request):
        try:
            client_id = request.client_id
            created_by = request.user.username

            client_info = {
                "client_id": client_id,
                "created_by": created_by,
                "email": request.user.username,
                "audit": request.audit,
                "logger": request.logger,
                "auth": request.auth,
                "user": request.user,
            }
            databook_name = request.data["databook"]["name"]

            if not check_everai_access(client_id):
                return Response(
                    {
                        "status": "error",
                        "message": "You are not authorized to access this feature.",
                    },
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            databook_name_exist = Databook.objects.filter(
                client_id=client_id,
                name__iexact=databook_name,
                knowledge_end_date__isnull=True,
                is_deleted=False,
            ).exists()

            if databook_name_exist:
                logger.info(
                    "Databook with name {} Already exist ".format(databook_name.lower())
                )
                return Response(
                    {"status": "DATABOOK_NAME_EXISTS"},
                    status=status.HTTP_200_OK,
                )

            datasheet_builder_service.sheet_builder_service(
                client_info=client_info, params=request.data
            )

            return Response(
                {"status": "success"},
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            logger.exception(
                "Error in RunSyncView",
                extra={"client_id": request.client_id, "error": str(e)},
            )
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while processing the request",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
