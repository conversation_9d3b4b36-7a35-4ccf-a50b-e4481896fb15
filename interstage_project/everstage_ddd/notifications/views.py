from django.utils.decorators import method_decorator
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import handle_ever_exception

from .models import NotificationAuditAccessor


class NotificationExistCheckView(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_ACCOUNTNOTIFICATIONS.value),
        name="dispatch",
    )
    @handle_ever_exception
    def post(self, request):
        email_id = request.data.get("email_id")
        notification_name = request.data.get("notification_name")
        channel = request.data.get("channel")
        client_id = request.data.get("client_id")
        triggered_at = request.data.get("triggered_at")
        if (
            not email_id
            or not notification_name
            or not channel
            or not client_id
            or not triggered_at
        ):
            return Response({"error": "Missing required fields."}, status=400)

        exist, records = NotificationAuditAccessor.check_notification_exists_after_time(
            email_id=email_id,
            notification_name=notification_name,
            channel=channel,
            client_id=client_id,
            triggered_at=triggered_at,
        )

        # Prepare the response data
        if records:
            records_data = [
                {
                    "id": record.id,
                    "email_id": record.email_id,
                    "notification_name": record.notification_name,
                    "channel": record.channel,
                    "client_id": record.client_id,
                    "triggered_at": record.triggered_at.isoformat(),
                    "data": record.data,
                }
                for record in records
            ]
        else:
            records_data = []

        return Response({"message": exist, "data": records_data})
