from datetime import datetime
from zoneinfo import ZoneInfo

from django.db import models

from commission_engine.models.client_models import Client
from interstage_project.db.models import (
    EsCharField,
    EsDateTimeField,
    EsEmailField,
    EsForeignKey,
    EsJSONField,
)


class NotificationAudit(models.Model):
    client = EsForeignKey(
        Client, on_delete=models.DO_NOTHING, db_index=False, is_sensitive=False
    )
    notification_name = EsCharField(max_length=200, null=False, is_sensitive=False)
    triggered_at = EsDateTimeField(null=False, auto_now_add=True, is_sensitive=False)
    email_id = EsEmailField(null=False, is_sensitive=False)
    channel = EsCharField(max_length=50, null=False, is_sensitive=False)
    data = EsJSONField(null=False, is_sensitive=False)

    class Meta:
        db_table = "notification_audit"


class NotificationAuditAccessor:
    @staticmethod
    def persist_audit(audit: NotificationAudit) -> None:
        audit.save()

    @staticmethod
    def persist_audits(audits: list[NotificationAudit]) -> None:
        NotificationAudit.objects.bulk_create(audits, batch_size=1000)

    @staticmethod
    def check_notification_exists_after_time(
        email_id: str,
        notification_name: str,
        channel: str,
        client_id: int,
        triggered_at: str,
    ):
        """
        Checks if a notification exists in the NotificationAudit table where the triggered_at
        is after the input triggered_at time.

        :param email_id: The email ID associated with the notification
        :param notification_name: The name of the notification
        :param channel: The channel used for notification
        :param client_id: The ID of the client
        :param triggered_at: The trigger time in YYYYMMDDHHMM format
        :return: (exists: bool, data: QuerySet or None)
        """
        try:
            triggered_at_dt = datetime.strptime(triggered_at, "%Y%m%d%H%M").replace(
                tzinfo=ZoneInfo("Asia/Kolkata")
            )

            queryset = NotificationAudit.objects.filter(
                email_id=email_id,
                notification_name=notification_name,
                channel=channel,
                client_id=client_id,
                triggered_at__gt=triggered_at_dt,
            )

            exists = queryset.exists()
            data = (
                list(queryset) if exists else None
            )  # return list of matching objects if exists

            return exists, data

        except ValueError as e:
            print(f"Error parsing date: {e}")
            return False, None
