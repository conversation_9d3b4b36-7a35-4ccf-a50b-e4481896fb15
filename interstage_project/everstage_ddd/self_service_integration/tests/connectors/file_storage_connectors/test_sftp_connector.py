"""
Unit tests for SFTP Connector implementation
"""

import zipfile
from datetime import datetime, timedelta, timezone
from io import BytesIO
from unittest import mock
from uuid import uuid4

import pandas as pd
import pytest

from everstage_ddd.self_service_integration.connectors.file_storage_connectors.sftp_connector import (
    SFTPConnector,
)
from everstage_ddd.self_service_integration.data_models import SFTPConnectorParams

# Test params
TEST_SFTP_PATH = f"/test/{uuid4()}/"
TEST_CSV_CONTENT = b"col1,col2\nval1,val2\nval3,val4"
TEST_SFTP_PASSWORD = "testpass"  # noqa: S105
TEST_SFTP_USERNAME = "testuser"
TEST_SFTP_HOST = "test.sftp.com"
TEST_SFTP_PORT = 22
TEST_SFTP_SECRET_NAME = "test-secret"  # noqa: S105


class MockSFTPFile:
    def __init__(self, content=b"col1,col2\nval1,val2", size=None):
        self.content = content
        self._size = size or len(content)
        self.file = BytesIO(content)
        self.closed = False
        self.position = 0

    def read(self, size=None):
        """Read at most size bytes from the file (less if the read hits EOF)"""
        if size is None:
            size = self._size
        data = self.file.read(size)
        self.position += len(data)
        return data

    def stat(self):
        attr = mock.MagicMock()
        attr.st_size = self._size
        return attr

    def __enter__(self):
        return self

    def __exit__(self, *args):
        self.close()

    def prefetch(self):
        """Mock prefetch method - no actual implementation needed for tests"""
        pass

    def close(self):
        self.file.close()
        self.closed = True

    # File-like interface methods needed for BufferedReader and BytesIO
    def seekable(self):
        return True

    def seek(self, offset, whence=0):
        return self.file.seek(offset, whence)

    def readable(self):
        return True

    def __iter__(self):
        return self

    def flush(self):
        return

    def readinto(self, b):
        """Read bytes into the supplied buffer b and return number of bytes read"""
        data = self.read(len(b))
        if not data:
            return 0
        for i, byte in enumerate(data):
            b[i] = byte
        return len(data)


class MockSFTPAttributes:
    def __init__(self, filename, st_mtime=None, st_size=100):
        self.filename = filename
        self.st_mtime = st_mtime or int(datetime.now(tz=timezone.utc).timestamp())
        self.st_size = st_size


@pytest.fixture
def connector_params():
    return SFTPConnectorParams(
        sftp_host=TEST_SFTP_HOST,
        sftp_port=TEST_SFTP_PORT,
        username=TEST_SFTP_USERNAME,
        password=TEST_SFTP_PASSWORD,
    )


@pytest.fixture
def key_connector_params():
    return SFTPConnectorParams(
        sftp_host=TEST_SFTP_HOST,
        sftp_port=TEST_SFTP_PORT,
        username=TEST_SFTP_USERNAME,
        secret_name=TEST_SFTP_SECRET_NAME,
    )


@pytest.fixture
def mock_sftp_client():
    with mock.patch("paramiko.SFTPClient") as mock_client:
        mock_instance = mock_client.from_transport.return_value
        yield mock_instance


@pytest.fixture
def mock_transport():
    with mock.patch("paramiko.Transport") as mock_transport:
        yield mock_transport.return_value


@pytest.fixture
def connector(connector_params, mock_sftp_client, mock_transport):
    connector = SFTPConnector(
        client_id=1,
        connector_params=connector_params,
        path=TEST_SFTP_PATH,
        file_format=".csv",
        regex=".*\\.csv$",
        batch_size=1000,
        encoding="utf-8",
    )
    return connector


@pytest.fixture
def mock_test_files(mock_sftp_client):
    """
    Setup mock files in the SFTP server that match the S3 test files:
    - file1.csv: CSV file with test data
    - file2.txt: Same CSV content but with .txt format (for filter tests)
    - file3.zip: ZIP file containing a test.csv file
    - backup_file1.csv: Backup file that should be excluded from filtering
    """
    # Create mock files with attributes
    file1_csv = MockSFTPAttributes("file1.csv")
    file2_txt = MockSFTPAttributes("file2.txt")
    file3_zip = MockSFTPAttributes("file3.zip")
    backup_file1_csv = MockSFTPAttributes("backup_file1.csv")

    # Set up the mock file listing
    mock_sftp_client.listdir_attr.return_value = [
        file1_csv,
        file2_txt,
        file3_zip,
        backup_file1_csv,
    ]

    # Setup file contents for each file
    mock_file_contents = {
        f"{TEST_SFTP_PATH}file1.csv": MockSFTPFile(content=TEST_CSV_CONTENT),
        f"{TEST_SFTP_PATH}file2.txt": MockSFTPFile(content=TEST_CSV_CONTENT),
        f"{TEST_SFTP_PATH}backup_file1.csv": MockSFTPFile(content=TEST_CSV_CONTENT),
    }

    # Create ZIP file content matching S3 test
    zip_buffer = BytesIO()
    with zipfile.ZipFile(zip_buffer, "w") as zip_file:
        zip_file.writestr("test.csv", "col1,col2\nzip1,zip2\nzip3,zip4")
    zip_buffer.seek(0)
    zip_content = zip_buffer.getvalue()

    mock_file_contents[f"{TEST_SFTP_PATH}file3.zip"] = MockSFTPFile(
        content=zip_content,
        size=len(zip_content),
    )

    # Setup open method to return the correct mock file
    def updated_mock_open(path, mode, bufsize=None):
        if path in mock_file_contents:
            return mock_file_contents[path]
        # Fall back to existing mock implementation for other files
        for mock_path, mock_file in mock_file_contents.items():
            if mock_path.endswith(path.split("/")[-1]):
                return mock_file
        # Use original mock file access
        return MockSFTPFile(content=TEST_CSV_CONTENT)

    mock_sftp_client.open.side_effect = updated_mock_open

    return {
        "file1_csv": file1_csv,
        "file2_txt": file2_txt,
        "file3_zip": file3_zip,
        "backup_file1_csv": backup_file1_csv,
    }


class TestSFTPConnector:
    @mock.patch.object(SFTPConnector, "_retrieve_private_key")
    def test_connect_with_password(
        self, mock_retrieve_key, connector, mock_transport, mock_sftp_client
    ):
        result = connector.connect()

        mock_transport.connect.assert_called_once_with(
            username=connector.connector_params.username,
            password=connector.connector_params.password,
        )
        mock_retrieve_key.assert_not_called()
        assert result == mock_sftp_client

    @mock.patch.object(SFTPConnector, "_retrieve_private_key")
    def test_connect_with_key(
        self, mock_retrieve_key, key_connector_params, mock_transport, mock_sftp_client
    ):
        mock_key = mock.MagicMock()
        mock_retrieve_key.return_value = mock_key

        connector = SFTPConnector(
            client_id=1, connector_params=key_connector_params, path=TEST_SFTP_PATH
        )
        result = connector.connect()

        mock_transport.connect.assert_called_once_with(
            username=connector.connector_params.username,
            pkey=mock_key,
        )
        mock_retrieve_key.assert_called_once()
        assert result == mock_sftp_client

    def test_connect_already_connected(self, mock_transport, connector):
        """Connection should be reused if already connected"""
        mock_client = mock.MagicMock()
        connector._sftp_client = mock_client  # noqa: SLF001

        result = connector.connect()

        assert result == mock_client
        mock_transport.connect.assert_not_called()

    def test_disconnect(self, connector):
        """Test disconnecting the SFTP client"""
        mock_client = mock.MagicMock()
        connector._sftp_client = mock_client  # noqa: SLF001

        connector.disconnect()

        mock_client.close.assert_called_once()
        assert connector._sftp_client is None  # noqa: SLF001

        # Test disconnect on already disconnected connection
        connector.disconnect()
        assert connector._sftp_client is None  # noqa: SLF001

    def test_context_manager(self, connector):
        """
        Test the context manager functionality.
        """
        with connector as result:
            assert result is connector
            assert connector._sftp_client is not None  # noqa: SLF001

        # Should be disconnected after exiting context
        assert connector._sftp_client is None  # noqa: SLF001

    def test_list_files(
        self, connector, mock_transport, mock_sftp_client, mock_test_files
    ):
        """
        Test listing files in SFTP directory.
        """
        connector.connect()
        result = connector.list_files()

        # Verify SFTP client is called with the correct path
        connector._sftp_client.listdir_attr.assert_called_once_with(  # noqa: SLF001
            TEST_SFTP_PATH
        )

        # Verify all expected files are listed
        file_names = [file_obj.filename for file_obj in result]
        assert len(file_names) == 4
        assert "file1.csv" in file_names
        assert "backup_file1.csv" in file_names
        assert "file2.txt" in file_names
        assert "file3.zip" in file_names

    def test_filter_files(
        self, connector, mock_transport, mock_sftp_client, mock_test_files
    ):
        """
        Test filtering files by extension and time.

        Tests using the mock files:
        - file1.csv: For CSV filter test
        - backup_file1.csv: For Regex based filtering test
        - file2.txt: For file format filtering test
        - file3.zip: For ZIP filter test
        """
        connector.connect()
        files = connector.list_files()

        # Set regex to filter files
        connector.context.regex = "^.*file.*\\.csv$"

        # Get current time for filtering
        current_time = datetime.now(tz=timezone.utc)

        # --- Test case 1: Test exclusion of already synced files with time filter at a future date
        future_time = current_time.replace(day=current_time.day + 1)
        result = connector.filter_files(files, changes_start_time=future_time)
        assert len(result) == 0

        # --- Test case 2: Test inclusion scenarios of only files with matching file format and regex
        result = connector.filter_files(files)
        # Should include file1.csv and backup_file1.csv
        assert all(
            [
                bool(
                    file.filename == "backup_file1.csv"  # noqa
                    or file.filename == "file1.csv"
                )
                for file in result
            ]
        )
        assert len(result) == 2

        # Set regex to exclude backup files
        connector.context.regex = "^(?!backup_).*\\.csv$"
        result = connector.filter_files(files)

        # Should only include file1.csv (not backup_file1.csv)
        filtered_filenames = [obj.filename for obj in result]
        assert len(filtered_filenames) == 1
        assert not any(
            name == "backup_file1.csv" for name in filtered_filenames
        ) and any(name == "file1.csv" for name in filtered_filenames)

        # Update context to include zip files
        connector.context.file_format = ".zip"
        connector.context.regex = "^.*file.*\\.zip$"
        result = connector.filter_files(files)

        # Should only include file3.zip
        assert len(result) == 1
        assert result[0].filename == "file3.zip"

    def test_read_files(  # noqa: PLR0915
        self, connector, mock_transport, mock_sftp_client, mock_test_files
    ):
        """
        Tests for reading files from SFTP covering:
        1. Reading regular CSV files
        2. Reading ZIP files containing CSVs
        3. Reading multiple files with chunking (combining CSV and ZIP files)

        This test uses:
        - Mocked files from fixture to simulate SFTP:
          - file1.csv: Standard CSV file
          - file3.zip: ZIP file containing a CSV
        - Additional mocked file uploads for chunking tests:
          - large_file.csv: CSV file with 10 rows
          - large_file.zip: ZIP containing a CSV with 10 rows
        """
        connector.connect()
        # --- Test 1: Test reading a CSV file ---
        connector.context.file_format = ".csv"
        connector.context.regex = "^file1\\.csv$"
        csv_results = list(connector.read_files())

        # Verify CSV results
        assert len(csv_results) == 1, "Expected to find one CSV file"
        csv_df, csv_metadata = csv_results[0]

        # Verify CSV data
        assert isinstance(csv_df, pd.DataFrame)
        assert "col1" in csv_df.columns and "col2" in csv_df.columns
        assert len(csv_df) == 2
        assert csv_df["col1"].iloc[0] == "val1" and csv_df["col2"].iloc[0] == "val2"
        assert csv_df["col1"].iloc[1] == "val3" and csv_df["col2"].iloc[1] == "val4"

        # Verify CSV metadata
        assert csv_metadata["file_name"] == "file1.csv"
        assert isinstance(csv_metadata["file_mtime"], datetime)

        # --- Test 2: Test reading a ZIP file ---
        connector.context.file_format = ".zip"
        connector.context.regex = "^file3\\.zip$"
        zip_results = list(connector.read_files())

        # Verify ZIP results
        assert len(zip_results) == 1, "Expected to find one ZIP file"
        zip_df, zip_metadata = zip_results[0]

        # Verify ZIP data
        assert isinstance(zip_df, pd.DataFrame)
        assert "col1" in zip_df.columns and "col2" in zip_df.columns
        assert len(zip_df) == 2
        assert zip_df["col1"].iloc[0] == "zip1" and zip_df["col2"].iloc[0] == "zip2"
        assert zip_df["col1"].iloc[1] == "zip3" and zip_df["col2"].iloc[1] == "zip4"

        # Verify ZIP metadata
        assert zip_metadata["file_name"] == "file3.zip"
        assert isinstance(zip_metadata["file_mtime"], datetime)

        # --- Test 3: Test reading multiple files (CSV + ZIP) with chunking ---
        # Create CSV and ZIP files for chunking tests
        # Setup mock files
        large_csv_content = (
            b"col1,col2\n1,a\n2,b\n3,c\n4,d\n5,e\n6,f\n7,g\n8,h\n9,i\n10,j"
        )

        zip_buffer = BytesIO()
        with zipfile.ZipFile(zip_buffer, "w") as zip_file:
            csv_content = (
                "col1,col2\n11,k\n12,l\n13,m\n14,n\n15,o\n16,p\n17,q\n18,r\n19,s\n20,t"
            )
            zip_file.writestr("test.csv", csv_content)
        zip_buffer.seek(0)
        zip_content = zip_buffer.getvalue()

        # Create mock SFTP file objects for large files
        large_csv_file = MockSFTPFile(
            content=large_csv_content, size=len(large_csv_content)
        )
        large_zip_file = MockSFTPFile(content=zip_content, size=len(zip_content))

        # Create mock SFTP attributes for large files
        # CSV file is created earlier than ZIP file
        csv_time = int(
            (datetime.now(tz=timezone.utc) - timedelta(seconds=1)).timestamp()
        )
        zip_time = int(datetime.now(tz=timezone.utc).timestamp())

        large_csv_attr = MockSFTPAttributes(
            "large_file.csv", st_mtime=csv_time, st_size=len(large_csv_content)
        )
        large_zip_attr = MockSFTPAttributes(
            "large_file.zip", st_mtime=zip_time, st_size=len(zip_content)
        )

        # Add large files to the mock test files
        all_files = list(connector._sftp_client.listdir_attr.return_value)  # noqa
        all_files.extend([large_zip_attr, large_csv_attr])
        connector._sftp_client.listdir_attr.return_value = all_files  # noqa

        # Setup mock file content
        def updated_mock_open(path, mode, bufsize=None):
            if path == f"{TEST_SFTP_PATH}large_file.csv":
                return large_csv_file
            elif path == f"{TEST_SFTP_PATH}large_file.zip":
                return large_zip_file
            # Fall back to existing mock
            return mock_sftp_client.open.return_value

        # Update the mock open function
        connector._sftp_client.open.side_effect = updated_mock_open  # noqa

        # Test processing both file types together with chunking
        connector.context.file_format = ""  # Accept all file formats
        connector.context.regex = "^large_file\\.(csv|zip)$"
        connector.context.batch_size = 3  # Set small batch size to force chunking

        combined_results = list(connector.read_files())

        # Verify we got 8 chunks total (4 from CSV + 4 from ZIP)
        assert len(combined_results) == 8, "Expected 8 total chunks (4 CSV + 4 ZIP)"

        # Extract all rows from both files
        combined_dfs = [df for df, _ in combined_results]
        combined_rows = pd.concat(combined_dfs)

        # Verify we have all 20 rows total
        assert len(combined_rows) == 20, "Expected 20 total rows from both files"

        # Count chunks per file type
        result_file_names = [metadata["file_name"] for _, metadata in combined_results]
        csv_chunks = sum(1 for name in result_file_names if name == "large_file.csv")
        zip_chunks = sum(1 for name in result_file_names if name == "large_file.zip")

        assert csv_chunks == 4, "Expected 4 chunks from CSV file"
        assert zip_chunks == 4, "Expected 4 chunks from ZIP file"

        # Verify file processing order - CSV file should be processed first
        # since it was modified earlier
        first_four_chunks = result_file_names[:4]
        last_four_chunks = result_file_names[4:]

        assert all(
            name == "large_file.csv" for name in first_four_chunks
        ), "CSV chunks should be processed first due to last modified time"
        assert all(
            name == "large_file.zip" for name in last_four_chunks
        ), "ZIP chunks should be processed second due to last modified time"

        # Verify row order is maintained within each file
        csv_rows = pd.concat([df for df, _ in combined_results[:4]])
        zip_rows = pd.concat([df for df, _ in combined_results[4:]])

        # Check CSV rows are in order 1-10
        csv_values = csv_rows["col1"].astype(int).tolist()
        assert csv_values == list(
            range(1, 11)
        ), "CSV rows should maintain original order (1-10)"

        # Check ZIP rows are in order 11-20
        zip_values = zip_rows["col1"].astype(int).tolist()
        assert zip_values == list(
            range(11, 21)
        ), "ZIP rows should maintain original order (11-20)"

        # Verify file metadata timestamps - ZIP file should have later timestamp than CSV
        csv_metadata = combined_results[0][1]
        zip_metadata = combined_results[4][1]

        assert (
            csv_metadata["file_mtime"] < zip_metadata["file_mtime"]
        ), "ZIP file should have later metadata timestamp than CSV file"

        # --- Test 4: Test reading files when no files match the filter criteria ---
        connector.context.regex = "non_existent_pattern"
        results = list(connector.read_files())
        assert len(results) == 0
