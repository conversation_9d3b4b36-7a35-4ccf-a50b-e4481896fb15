"""
Unit tests for AbstractFileStorageConnector base class
"""

import zipfile
from datetime import datetime, timedelta, timezone
from io import BytesIO
from unittest import mock

import pandas as pd
import pytest

from everstage_ddd.self_service_integration.connectors.file_storage_connectors.base import (
    AbstractFileStorageConnector,
)
from everstage_ddd.self_service_integration.exceptions import CSVParsingError


class MockFileStorageConnector(AbstractFileStorageConnector):
    """Mock implementation of AbstractFileStorageConnector for testing"""

    def connect(self):
        return True

    def disconnect(self):
        pass

    def list_files(self) -> list:
        return []

    def filter_files(self, files, changes_start_time=None):
        return []

    def read_files(self):
        yield None, None


@pytest.fixture
def mock_connector():
    connector = MockFileStorageConnector(
        client_id=1,
        connector_params={},
        path="/test/path",
        file_format=".csv",
        regex=".*\\.csv$",
        batch_size=1000,
        file_metadata={"encoding": "utf-8"},
    )
    return connector


@pytest.mark.usefixtures("mock_connector")
class TestAbstractFileStorageConnector:
    def test_init(self, mock_connector):
        """Test metadata initialization"""
        assert mock_connector.client_id == 1
        assert mock_connector.connector_params == {}
        assert mock_connector.context.path == "/test/path"
        assert mock_connector.context.file_format == ".csv"
        assert mock_connector.context.regex == ".*\\.csv$"
        assert mock_connector.context.batch_size == 1000
        assert mock_connector.metadata.encoding == "utf-8"

    def test_context_manager(self, mock_connector):
        """Test __enter__ and __exit__ methods for connection and disconnection"""
        with mock.patch.object(
            mock_connector, "connect"
        ) as mock_connect, mock.patch.object(
            mock_connector, "disconnect"
        ) as mock_disconnect:
            with mock_connector as result:
                assert result is mock_connector
                mock_connect.assert_called_once()

            mock_disconnect.assert_called_once()

    def test_set_file_context(self, mock_connector):
        """Test switching between different file contexts (folder/format)"""
        original_path = mock_connector.context.path
        original_format = mock_connector.context.file_format

        mock_connector.set_file_context(
            "/new/path", file_format=".zip", regex=".*\\.zip$"
        )

        assert mock_connector.context.path == "/new/path"
        assert mock_connector.context.file_format == ".zip"
        assert mock_connector.context.regex == ".*\\.zip$"
        assert mock_connector.context.path != original_path
        assert mock_connector.context.file_format != original_format

    def test_filter_file_csv(self, mock_connector):
        """Test CSV file filtering"""

        discarded_file_modified_time = datetime.now(tz=timezone.utc) - timedelta(
            seconds=1
        )
        changes_start_time = datetime.now(tz=timezone.utc)
        qualified_file_modified_time = datetime.now(tz=timezone.utc)

        # Discarded file - Matched regex and correct file format but already synced
        assert not mock_connector.filter_file(
            "test.csv", discarded_file_modified_time, changes_start_time
        )

        # Qualified file - Matched regex and correct file format and recently modified from last sync time
        assert mock_connector.filter_file(
            "test.csv", qualified_file_modified_time, changes_start_time
        )

        # Discarded file - Incorrect file format and recently modified from last sync time
        assert not mock_connector.filter_file(
            "test.pdf", qualified_file_modified_time, changes_start_time
        )

        # Discarded file - Doesn't match regex
        mock_connector.context.regex = "^(?!test).*\\.csv$"
        assert not mock_connector.filter_file(
            "test.csv", qualified_file_modified_time, changes_start_time
        )

        # Qualified file - Previously discarded valid file without time filter
        mock_connector.context.regex = ".*\\.csv$"
        assert mock_connector.filter_file(
            "test.csv", discarded_file_modified_time, None
        )

    def test_filter_file_zip(self, mock_connector):
        """Test ZIP file filtering"""
        mock_connector.context.file_format = ".zip"
        mock_connector.context.regex = ".*\\.zip$"
        file_modified_time = datetime.now(tz=timezone.utc)

        # Qualified file - Matched regex and correct file format
        assert mock_connector.filter_file("test.zip", file_modified_time, None)

        # Discarded file - Incorrect file format and regex
        assert not mock_connector.filter_file("test.csv", file_modified_time, None)

        # Discarded file - Correct file format but doesn't match regex (backup_ prefix)
        mock_connector.context.regex = "^(?!backup_).*\\.zip$"
        assert not mock_connector.filter_file(
            "backup_test.zip", file_modified_time, None
        )

    def test_stream_file(self, mock_connector):
        """Test streaming files with different batch sizes"""
        # Create a mock CSV file with 20 rows
        headers = "col1,col2"
        rows = "\n".join([f"val{i},val{i+1}" for i in range(1, 41, 2)])
        csv_data = f"{headers}\n{rows}"

        # Test with different batch sizes
        batch_sizes = [3, 5, 7, 9]
        for batch_size in batch_sizes:
            file_stream = BytesIO(csv_data.encode())
            mock_connector.context.batch_size = batch_size

            # Process the stream
            results = list(mock_connector.stream_file(file_stream))

            # Expected number of batches (rows/batch_size, rounded up)
            expected_batches = (20 + batch_size - 1) // batch_size

            # Check results
            assert (
                len(results) == expected_batches
            ), f"Expected {expected_batches} batches with batch size {batch_size}"

            # Verify total number of rows
            total_rows = sum(len(df) for df in results)
            assert (
                total_rows == 20
            ), f"Expected 20 total rows, got {total_rows} with batch size {batch_size}"

            # Verify each batch doesn't exceed the batch size
            for df in results:
                assert len(df) <= batch_size
                assert isinstance(df, pd.DataFrame)
                assert list(df.columns) == ["col1", "col2"]

            # Verify all data is preserved
            all_data = pd.concat(results)
            assert len(all_data) == 20
            assert all_data["col1"].iloc[0] == "val1"
            assert all_data["col2"].iloc[-1] == "val40"

    def test_stream_file_empty(self, mock_connector):
        """Test stream handling of empty CSV files"""
        # Create empty CSV files
        empty_csv1 = ""
        empty_csv2 = "col1,col2"
        file_stream1 = BytesIO(empty_csv1.encode())
        file_stream2 = BytesIO(empty_csv2.encode())

        # Process the stream
        results1 = list(mock_connector.stream_file(file_stream1))
        results2 = list(mock_connector.stream_file(file_stream2))

        # Should have no results
        assert len(results1) == 0
        assert len(results2) == 0

    def test_stream_file_different_encoding(self, mock_connector):
        """Test CSV files with different encodings"""
        csv_data = "col1,col2\nval1,val2"
        file_stream_1 = BytesIO(csv_data.encode("utf-16"))

        # Processing stream with default encoding should raise exception
        with pytest.raises(CSVParsingError):
            list(mock_connector.stream_file(file_stream_1))

        # Processing stream with correct encoding should not raise exception
        mock_connector.metadata.encoding = "utf-16"
        file_stream_2 = BytesIO(csv_data.encode("utf-16"))
        results = list(mock_connector.stream_file(file_stream_2))
        assert len(results) == 1
        assert results[0].equals(pd.DataFrame({"col1": ["val1"], "col2": ["val2"]}))

    def test_process_zip_file(self, mock_connector):
        # Create a zip file in memory with a CSV
        zip_buffer = BytesIO()
        with zipfile.ZipFile(zip_buffer, "w") as zip_file:
            zip_file.writestr("test.csv", "col1,col2\nval1,val2\nval3,val4")
            # Add a non-CSV file
            zip_file.writestr("test.txt", "This is a non-CSV file")
            # Add hidden files
            zip_file.writestr("__MACOSX/test2.csv", "col1,col2\nhidden,file")
            zip_file.writestr(".ignore", "col1,col2\nhidden,file")
        zip_buffer.seek(0)

        # Process the zip file
        csv_files = list(mock_connector.process_zip_file(zip_buffer))

        # Check results - should only return the CSV file
        assert len(csv_files) == 1
        # Verify csv file obj is returned
        assert csv_files[0] is not None
