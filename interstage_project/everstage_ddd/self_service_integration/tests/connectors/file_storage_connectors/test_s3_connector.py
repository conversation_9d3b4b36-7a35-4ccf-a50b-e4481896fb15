"""
Unit tests for S3 Connector implementation

These tests interact with a real S3 bucket rather than using mocks.
To run these tests, you need to have:
1. A test S3 bucket that the test credentials can access
2. AWS credentials with read/write permissions to that bucket

The tests automatically upload required test files to the bucket using the
real_s3_resource fixture and clean them up afterward.
"""

import time
import zipfile
from datetime import datetime, timezone
from io import BytesIO
from uuid import uuid4

import pandas as pd
import pytest

from commission_engine.accessors.etl_config_accessor import ApiAccessConfigAccessor
from everstage_ddd.self_service_integration.connectors.file_storage_connectors.s3_connector import (
    S3Connector,
)
from everstage_ddd.self_service_integration.data_models import S3ConnectorParams

# Test directory
TEST_S3_PREFIX = str(uuid4()) + "/"
# Test file content
TEST_CSV_CONTENT = b"col1,col2\nval1,val2\nval3,val4"


@pytest.fixture
def connector():
    """
    Return an S3 connector from unit-test db for testing.
    """
    api_access_config = ApiAccessConfigAccessor(client_id=2017).get_obj_by_source_id(
        "freshsales-deals"
    )
    additional_data = api_access_config.additional_data or {}  # type: ignore
    connector = S3Connector(
        client_id=2017,
        connector_params=S3ConnectorParams(**additional_data),
        path=f"{additional_data['path']}{TEST_S3_PREFIX}",
    )
    return connector


@pytest.fixture
def s3_files_resource(connector):
    """
    Create a S3 resource from unit test client's API access config
    and simulate files picked up by ETL by uploading the following files
    to the TEST_S3_PREFIX directory in the test bucket:
    - file1.csv: CSV file with test data
    - file2.txt: Same CSV content but with .txt format (for filter tests)
    - file3.zip: ZIP file containing a test.csv file
    - backup_file1.csv: Backup file that should be excluded from filtering

    The files are deleted after the tests finish.
    All tests that need S3 files depend on this fixture.
    """
    s3_resource = connector.connect()

    # Create test directory structure and files
    bucket = s3_resource.Bucket(connector.connector_params.bucket_name)

    # Upload a test CSV file
    bucket.put_object(
        Key=f"{connector.context.path}file1.csv",
        Body=TEST_CSV_CONTENT,
        ContentType="text/csv",
    )

    # Upload a test file with wrong format
    bucket.put_object(
        Key=f"{connector.context.path}file2.txt",
        Body=TEST_CSV_CONTENT,
        ContentType="text/plain",
    )

    # Upload a backup CSV file that should be filtered out
    bucket.put_object(
        Key=f"{connector.context.path}backup_file1.csv",
        Body=TEST_CSV_CONTENT,
        ContentType="text/csv",
    )

    # Create and upload a test zip file
    zip_buffer = BytesIO()
    with zipfile.ZipFile(zip_buffer, "w") as zip_file:
        zip_file.writestr("test.csv", "col1,col2\nzip1,zip2\nzip3,zip4")
    zip_buffer.seek(0)

    bucket.put_object(
        Key=f"{connector.context.path}file3.zip",
        Body=zip_buffer.getvalue(),
        ContentType="application/zip",
    )

    yield s3_resource

    # Clean up after tests
    bucket.objects.filter(Prefix=connector.context.path).delete()


@pytest.mark.django_db
@pytest.mark.usefixtures("s3_files_resource")
class TestS3Connector:
    def test_connect(self, connector):
        """
        Test connecting to S3.
        """
        first_client = connector.connect()

        # Verify we can actually use the connection to list objects
        bucket = first_client.Bucket(connector.connector_params.bucket_name)
        objects = list(bucket.objects.filter(Prefix=connector.context.path))
        # We should have 4 test files that were uploaded in the fixture
        assert len(objects) == 4

        # Test connecting when already connected.
        second_client = connector.connect()
        # Should reuse the same client
        assert first_client is second_client

    def test_disconnect(self, connector):
        """
        Test Resource cleanup on disconnecting from S3.
        """
        # First connect
        connector.connect()
        assert connector._s3_client is not None  # noqa

        # Then disconnect
        connector.disconnect()
        assert connector._s3_client is None  # noqa

        # Test disconnect on already disconnected connection
        connector.disconnect()
        assert connector._s3_client is None  # noqa

    def test_context_manager(self, connector):
        """
        Test the context manager functionality.
        """
        with connector as result:
            assert result is connector
            assert connector._s3_client is not None  # noqa

            # Verify connection works
            files = list(connector.list_files())
            assert len(files) == 4

        # Should be disconnected after exiting context
        assert connector._s3_client is None  # noqa

    def test_list_files(self, connector):
        """
        Test listing files in S3 directory.
        """
        connector.connect()
        result = connector.list_files()

        # Verify all files that were uploaded in the fixture are listed
        files = list(result)
        assert len(files) == 4

        file_keys = [obj.key for obj in files]
        assert f"{connector.context.path}file1.csv" in file_keys
        assert f"{connector.context.path}backup_file1.csv" in file_keys
        assert f"{connector.context.path}file2.txt" in file_keys
        assert f"{connector.context.path}file3.zip" in file_keys

    def test_filter_files(self, connector):
        """
        Test filtering files by extension and time.

        Files used from fixture:
        - file1.csv: For CSV filter test
        - backup_file1.csv: For Regex based filtering test
        - file2.txt: For file format filtering test
        - file3.zip: For ZIP filter test
        """
        connector.connect()
        s3_files = list(connector.list_files())

        # Set regex to filter files
        connector.context.regex = "^.*file.*\\.csv$"

        # Get current time for filtering
        current_time = datetime.now(tz=timezone.utc)

        # --- Test case 1: Test exclusion of already synced files with time filter at a future date
        future_time = current_time.replace(day=current_time.day + 1)
        result = connector.filter_files(s3_files, changes_start_time=future_time)
        assert len(result) == 0

        # --- Test case 2: Test inclusion scenarios of only files with matching file format and regex
        result = connector.filter_files(s3_files)
        # Should include file1.csv and backup_file1.csv
        assert all(
            [
                bool(
                    file.key.endswith("backup_file1.csv")
                    or file.key.endswith("file1.csv")
                )
                for file in result
            ]
        )
        assert len(list(result)) == 2
        # Set regex to exclude backup files
        connector.context.regex = "^(?!backup_).*\\.csv$"
        result = connector.filter_files(s3_files)

        # Should only include file1.csv (not backup_file1.csv)
        filtered_keys = [obj.key for obj in result]
        assert len(filtered_keys) == 1
        assert not any(
            key.endswith("backup_file1.csv") for key in filtered_keys
        ) and any(key.endswith("file1.csv") for key in filtered_keys)

        # Update context to include zip files
        connector.context.file_format = ".zip"
        connector.context.regex = "^.*file.*\\.zip$"
        result = connector.filter_files(s3_files)

        # Should only include file3.zip
        assert result[0].key.endswith("file3.zip")
        assert len(list(result)) == 1

    def test_read_files(self, connector):  # noqa
        """
        Tests for reading files from S3 covering:
        1. Reading regular CSV files
        2. Reading ZIP files containing CSVs
        3. Reading multiple files with chunking (combining CSV and ZIP files)

        This test uses:
        - Files from the fixture:
          - file1.csv: Standard CSV file
          - file3.zip: ZIP file containing a CSV
        - Custom uploaded files for chunking:
          - large_file.csv: CSV with 10 rows
          - large_file.zip: ZIP containing a CSV with 10 rows
        """
        # Connect to S3
        s3_client = connector.connect()
        bucket = s3_client.Bucket(connector.connector_params.bucket_name)

        # --- Test 1: Test reading a CSV file ---
        connector.context.file_format = ".csv"
        connector.context.regex = "^file1\\.csv$"
        csv_results = list(connector.read_files())

        # Verify CSV results
        assert len(csv_results) == 1, "Expected to find one CSV file"
        csv_df, csv_metadata = csv_results[0]

        # Verify CSV data
        assert isinstance(csv_df, pd.DataFrame)
        assert "col1" in csv_df.columns and "col2" in csv_df.columns
        assert len(csv_df) == 2
        assert csv_df["col1"].iloc[0] == "val1" and csv_df["col2"].iloc[0] == "val2"
        assert csv_df["col1"].iloc[1] == "val3" and csv_df["col2"].iloc[1] == "val4"

        # Verify CSV metadata
        assert csv_metadata["file_name"] == "file1.csv"
        assert isinstance(csv_metadata["file_mtime"], datetime)

        # --- Test 2: Test reading a ZIP file ---
        connector.context.file_format = ".zip"
        connector.context.regex = "^file3\\.zip$"
        zip_results = list(connector.read_files())

        # Verify ZIP results
        assert len(zip_results) == 1, "Expected to find one ZIP file"
        zip_df, zip_metadata = zip_results[0]

        # Verify ZIP data
        assert isinstance(zip_df, pd.DataFrame)
        assert "col1" in zip_df.columns and "col2" in zip_df.columns
        assert len(zip_df) == 2
        assert zip_df["col1"].iloc[0] == "zip1" and zip_df["col2"].iloc[0] == "zip2"
        assert zip_df["col1"].iloc[1] == "zip3" and zip_df["col2"].iloc[1] == "zip4"

        # Verify ZIP metadata
        assert zip_metadata["file_name"] == "file3.zip"
        assert isinstance(zip_metadata["file_mtime"], datetime)

        # --- Test 3: Test reading multiple files (CSV + ZIP) with chunking ---
        try:
            # Create large CSV file content (10 rows)
            large_csv_content = (
                b"col1,col2\n1,a\n2,b\n3,c\n4,d\n5,e\n6,f\n7,g\n8,h\n9,i\n10,j"
            )
            # Upload the large CSV file first
            bucket.put_object(
                Key=f"{connector.context.path}large_file.csv",
                Body=large_csv_content,
                ContentType="text/csv",
            )

            time.sleep(1)

            # Create large ZIP file containing CSV (10 rows)
            zip_buffer = BytesIO()
            with zipfile.ZipFile(zip_buffer, "w") as zip_file:
                csv_content = "col1,col2\n11,k\n12,l\n13,m\n14,n\n15,o\n16,p\n17,q\n18,r\n19,s\n20,t"
                zip_file.writestr("test.csv", csv_content)
            zip_buffer.seek(0)
            # Upload the large ZIP file second
            bucket.put_object(
                Key=f"{connector.context.path}large_file.zip",
                Body=zip_buffer.getvalue(),
                ContentType="application/zip",
            )

            # Test processing both file types together
            connector.context.file_format = ""  # Accept all file formats
            connector.context.regex = "^large_file\\.(csv|zip)$"
            connector.context.batch_size = 3  # Set small batch size to force chunking

            combined_results = list(connector.read_files())

            # Verify we got 8 chunks total (4 from CSV + 4 from ZIP)
            assert len(combined_results) == 8, "Expected 8 total chunks (4 CSV + 4 ZIP)"

            # Extract all rows from both files
            combined_dfs = [df for df, _ in combined_results]
            combined_rows = pd.concat(combined_dfs)

            # Verify we have all 20 rows total
            assert len(combined_rows) == 20, "Expected 20 total rows from both files"

            # Count chunks per file
            result_file_names = [
                metadata["file_name"] for _, metadata in combined_results
            ]
            csv_chunks = sum(
                1 for name in result_file_names if name == "large_file.csv"
            )
            zip_chunks = sum(
                1 for name in result_file_names if name == "large_file.zip"
            )

            assert csv_chunks == 4, "Expected 4 chunks from CSV file"
            assert zip_chunks == 4, "Expected 4 chunks from ZIP file"

            # Verify file processing order - CSV file should be processed first
            # since it was uploaded first
            first_four_chunks = result_file_names[:4]
            last_four_chunks = result_file_names[4:]

            assert all(
                name == "large_file.csv" for name in first_four_chunks
            ), "CSV chunks should be processed first due to last modified time"
            assert all(
                name == "large_file.zip" for name in last_four_chunks
            ), "ZIP chunks should be processed second due to last modified time"

            # --- Skipping in ci as time.sleep() has no effect and both files are getting same upload timestamp (Passes Locally) --- # noqa
            # result_file_mtimes = [ # noqa
            #     metadata["file_mtime"] for _, metadata in combined_results # noqa
            # ] # noqa
            # first_four_chunks_mtimes = result_file_mtimes[:4] # noqa
            # last_four_chunks_mtimes = result_file_mtimes[4:] # noqa

            # assert all(
            #     mtime < last_four_chunks_mtimes[0] for mtime in first_four_chunks_mtimes
            # ), "ZIP file should have later metadata timestamp than CSV file due to upload order"

            # Verify row order is maintained within each file
            csv_rows = pd.concat([df for df, _ in combined_results[:4]])
            zip_rows = pd.concat([df for df, _ in combined_results[4:]])

            # Check CSV rows are in order 1-10
            csv_values = csv_rows["col1"].astype(int).tolist()
            assert csv_values == list(
                range(1, 11)
            ), "CSV rows should maintain original order (1-10)"

            # Check ZIP rows are in order 11-20
            zip_values = zip_rows["col1"].astype(int).tolist()
            assert zip_values == list(
                range(11, 21)
            ), "ZIP rows should maintain original order (11-20)"

        finally:
            # Clean up the test files
            bucket.Object(f"{connector.context.path}large_file.csv").delete()
            bucket.Object(f"{connector.context.path}large_file.zip").delete()

        # --- Test 4: Test reading files when no files match the filter criteria ---
        connector.context.regex = "non_existent_pattern"
        results = list(connector.read_files())
        assert len(results) == 0

        # --- Test 5: Test reading files from a empty directory ---
        test_empty_dir = f"{connector.context.path}empty_dir/"
        bucket.put_object(Key=test_empty_dir)
        connector.context.regex = "^.*$"
        connector.context.path = test_empty_dir
        results = list(connector.read_files())
        assert len(results) == 0
