from os import getenv
from typing import Annotated, Optional, Union

from pydantic import BaseModel, <PERSON>, model_validator
from typing_extensions import Self

from everstage_ddd.self_service_integration.constants import DEFAULT_MSSQL_TIMEOUT

from .exceptions import InvalidAccessTokenConfigError


class MSSQLConnectorType(BaseModel):
    db_host: str
    db_port: str
    db_name: str
    db_user: str
    db_password: str
    db_schema: list[str]
    auth_type: Optional[str] = None
    timeout: Optional[int] = DEFAULT_MSSQL_TIMEOUT


class PostgressConnectorType(BaseModel):
    db_host: str
    db_port: str
    db_name: str
    db_user: str
    db_password: str
    db_schema: list[str]
    is_default: Optional[bool] = False  # Use default everstage db or not


class PostgresDefaultConnectorType(BaseModel):
    db_schema: Optional[list[str]] = []
    is_default: Optional[bool] = True
    middleware_name: str
    prefix: Optional[list[str]] = []
    suffix: Optional[list[str]] = []


class SnowflakeConnectorType(BaseModel):
    db_account: str
    db_user: str
    db_password: str
    db_warehouse: str
    db_name: str
    db_schema: list[str]
    db_role: str
    db_region: str


class SuiteAnalyticsConnectorType(BaseModel):
    account_id: str
    consumer_key: str
    service_host: str
    token_secret: str
    consumer_secret: str
    token_id: str
    role_id: str
    port: str


class MYSQLConnectorType(BaseModel):
    db_host: str
    db_port: int
    db_name: str
    db_user: str
    db_password: str
    db_schema: list[str]


class SFTPConnectorParams(BaseModel):
    sftp_host: str
    sftp_port: Annotated[int, Union[int, str]]
    username: str
    password: Optional[str] = None
    secret_name: Optional[str] = None

    @model_validator(mode="after")
    def validate_sftp_params(self) -> Self:
        if all([self.password, self.secret_name]):
            raise InvalidAccessTokenConfigError(
                message="SFTP Connection can use either private key or password, not both"
            )
        try:
            self.sftp_port = int(self.sftp_port)
        except ValueError as e:
            raise InvalidAccessTokenConfigError(message="Invalid port number") from e

        return self


class S3ConnectorParams(BaseModel):
    bucket_name: str
    region_name: Annotated[str, Optional[str]] = getenv("S3_REGION_NAME", "")
    aws_access_key_id: str
    aws_secret_access_key: str


class FileMetadata(BaseModel):
    encoding: Optional[str] = "utf-8-sig"
    delimiter: Optional[str] = None
    newline: Optional[str] = None
    period_field: Optional[str] = None
    delete_field: Optional[str] = None
    delete_value: Optional[str] = None


class FileContext(BaseModel):
    path: str
    file_format: Annotated[str, Optional[str]] = ""
    regex: Annotated[str, Optional[str]] = ""
    password: Annotated[str, Optional[str]] = ""
    batch_size: Annotated[int, Optional[int]] = 50000  # rows
    file_metadata: Annotated[FileMetadata, Optional[FileMetadata]] = Field(
        default_factory=FileMetadata
    )
