from enum import Enum


class SelfServiceConnectors(Enum):
    SQL = "sql"
    ZOHO = "zoho"


class ZohoConnectionTypes(Enum):
    CRM = "crm"
    ANALYTICS = "analytics"


class AccessTokenUrls(Enum):
    ZOHO = "https://accounts.zoho.{data_center}/oauth/v2/token"


class ServerUrls(Enum):
    ZOHOANALYTICS = "https://analyticsapi.zoho.{data_center}"
    ZOHO = "https://www.accounts.zoho.{data_center}"


class ZohoVersion(Enum):
    V2 = "v2"
    V7 = "v7"
    DEFAULT = "v2"


class ZohoApiPaths(Enum):
    CRM = "crm"
    ANALYTICS = "restapi"


class ZohoAccessTypes(Enum):
    GRANTCODE = "grantCode"
    REFRESH = "refresh"


class CacheExpiryTimes(Enum):
    ZOHO_ACCESS_TOKEN = 3600
    ZOHO_SCOPES = 600


class SQLConnectors(Enum):
    POSTGRES = "postgres"
    SNOWFLAKE = "snowflake"
    MSSQL = "mssql"
    SUITE_ANALYTICS = "suiteanalytics"
    MYSQL = "mysql"


class MiddlewareConnectors(Enum):
    FIVETRAN = "fivetran"
    HEVO = "hevo"
    AIRBYTE = "airbyte"


class IntegrationAccessTypes(Enum):
    API_KEY = "API_KEY"
    REFRESH = "REFRESH"
    BASIC_AUTH = "BASIC_AUTH"
    SQL = "SQL"
    UN = "UN"


class SelfServiceIntegrationModes(Enum):
    CONNECTION = "connection"
    OBJECT = "object"


SQL_QUERY_LIMIT = 50000
DEFAULT_MSSQL_TIMEOUT = 30
DEFAULT_SCHEMAS = {
    MiddlewareConnectors.AIRBYTE.value: "airbyte",
    MiddlewareConnectors.HEVO.value: "Hevo_data_prod",
}


SQL_DATE_DATATYPE = ["date"]

SQL_DATETIME_DATATYPE = [
    "datetime",
    "timestamp",
    "timestamp_tz",
    "timestamp_ntz",
    "timestamp_ltz",
    "timestamp with time zone",
    "timestamp without time zone",
]

SQL_NUMBER_DATATYPE = ["bigint", "int", "smallint", "integer"]


class ErrorMessages(Enum):
    """Enumeration of error messages."""

    DEFAULT_ERROR = "Something went wrong.. Please contact support team"
    CONNECTION_NAME_EXISTS = "Connection name already exists"
    INVALID_CONFIG = "Invalid Token ID"
    INVALID_CONNECTION_TYPE = "Connection type is invalid"
    INVALID_SCHEMA_NAME = "Schema name is invalid"
    CONNECTION_NOT_ESTABLISHED = "Connection not established"
    INVALID_SYNC_FIELD_TYPE = "Invalid sync field type"
    SELF_SERVE_CONNECTION_ERROR = "Invalid connection details"
    INVALID_CREDENTIALS_ERROR = "Invalid credentials"
    METADATA_SCOPE_NOT_FOUND_ERROR = "Metadata scope is required"
