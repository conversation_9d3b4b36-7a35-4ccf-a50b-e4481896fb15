from typing import Optional


class SelfServiceConnectorError(Exception):
    """
    Custom exception class for self service connector
    """

    pass


class PrimaryKeyError(Exception):
    """
    Custom exception class for primary key error
    """

    pass


class InvalidSyncFieldTypeError(Exception):
    """
    Custom exception class for invalid sync field type
    """

    pass


class InvalidConnectorTypeError(Exception):
    """
    Custom exception class for invalid connector type
    """

    pass


class ConnectionNameExistsError(Exception):
    """
    Custom exception class for connection name exists
    """

    pass


class InvalidAccessTokenConfigError(Exception):
    """
    Custom exception class for invalid access token config
    """

    def __init__(self, message: Optional[str] = ""):
        super().__init__(message)


class ConnectionNotEstablishedError(Exception):
    """
    Custom exception class for connection not established
    """

    pass


class InvalidSchemaError(Exception):
    """
    Custom exception class for invalid schema
    """

    pass


class QueryNotFoundError(Exception):
    """
    Custom exception class for query not found
    """

    pass


class InvalidColumnNameError(Exception):
    """
    Custom exception class for invalid column name
    """

    pass


class EmptyResponseBodyError(Exception):
    """
    Custom exception class for empty response
    """

    pass


class InvalidScopeError(Exception):
    """
    Custom exception class for invalid scope
    """

    pass


class CSVParsingError(Exception):
    """
    Custom exception class for errors in CSV parsing
    """

    def __init__(self, message: Optional[str] = ""):
        super().__init__(message)
