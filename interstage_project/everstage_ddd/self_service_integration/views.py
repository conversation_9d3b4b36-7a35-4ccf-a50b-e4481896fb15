import logging

from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from commission_engine.utils.general_data import RbacPermissions
from everstage_ddd.self_service_integration.constants import ErrorMessages
from everstage_ddd.self_service_integration.exceptions import (
    ConnectionNameExistsError,
    InvalidConnectorTypeError,
    InvalidSchemaError,
    SelfServiceConnectorError,
)
from everstage_ddd.self_service_integration.services.self_service_integration_service import (
    get_connector_details,
    get_sync_fields,
    validate_and_save_connection,
)
from interstage_project.auth_utils import requires_scope

logger = logging.getLogger(__name__)


class GetConnectionConfig(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATASETTINGS.value), name="dispatch"
    )
    def get(self, request):
        service_name = request.query_params.get("service_name")

        try:
            connector_data = get_connector_details(service_name)
            return Response(
                {"status": "SUCCESS", "data": connector_data}, status=status.HTTP_200_OK
            )
        except Exception as err:
            logger.exception(
                "Error while fetching connection config for %s",
                service_name,
                exc_info=err,
            )
            return Response(
                {"message": ErrorMessages.DEFAULT_ERROR.value},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ValidateSaveConnection(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATASETTINGS.value), name="dispatch"
    )
    def post(self, request):
        client_id = request.client_id
        connection_name = request.data.get("connection_name")
        service_name = request.data.get("service_name")
        is_edit = request.data.get("is_edit")
        connection_data = request.data.get("connection_data")
        access_token_config_id = request.data.get("access_token_config_id")
        try:
            validate_and_save_connection(
                client_id=client_id,
                service_name=service_name,
                connection_name=connection_name,
                is_edit=is_edit,
                access_token_config_id=access_token_config_id,
                connection_data=connection_data,
            )
            return Response({"status": "SUCCESS"}, status=status.HTTP_200_OK)
        except ConnectionNameExistsError:
            logger.exception(
                "Connection name already exists for %s", service_name, exc_info=True
            )
            return Response(
                {"message": ErrorMessages.CONNECTION_NAME_EXISTS.value},
                status=status.HTTP_400_BAD_REQUEST,
            )

        except InvalidSchemaError:
            logger.exception("Invalid schema name for %s", service_name, exc_info=True)
            return Response(
                {"message": ErrorMessages.INVALID_SCHEMA_NAME.value},
                status=status.HTTP_400_BAD_REQUEST,
            )

        except SelfServiceConnectorError:
            logger.exception(
                "Invalid connection details for %s", service_name, exc_info=True
            )
            return Response(
                {"message": ErrorMessages.SELF_SERVE_CONNECTION_ERROR.value},
                status=status.HTTP_400_BAD_REQUEST,
            )

        except InvalidConnectorTypeError:
            logger.exception(
                "Invalid connection type for %s", service_name, exc_info=True
            )
            return Response(
                {"message": ErrorMessages.INVALID_CONNECTION_TYPE.value},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as err:
            logger.exception(
                "Error while validating and saving connection for %s",
                service_name,
                exc_info=err,
            )
            return Response(
                {"message": ErrorMessages.DEFAULT_ERROR.value},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GetSyncFields(APIView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATASETTINGS.value), name="dispatch"
    )
    def get(self, request):
        """
        Get sync fields for a given custom_object_id
        """
        try:
            custom_object_id = request.query_params.get("custom_object_id")

            if not custom_object_id:
                return Response(
                    {"message": "custom_object_id is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            client_id = request.client_id

            sync_fields = get_sync_fields(
                client_id=client_id,
                custom_object_id=int(custom_object_id),
            )

            # Transform the response to match the new structure
            response_data = {
                "changes": sync_fields.get("changes_sync_field", None),
                "deletes": sync_fields.get("delete_sync_field", None),
            }

            return Response(
                {"status": "SUCCESS", "data": response_data}, status=status.HTTP_200_OK
            )
        except Exception as err:
            logger.exception(
                "Error while fetching sync fields for custom_object_id %s",
                custom_object_id,
                exc_info=err,
            )
            return Response(
                {"message": ErrorMessages.DEFAULT_ERROR.value},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
