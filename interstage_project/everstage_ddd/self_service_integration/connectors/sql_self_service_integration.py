import logging
from uuid import UUID, uuid4

import everstage_ddd.self_service_integration.base_factory as base_factory  # noqa: PLR0402
from commission_engine.accessors.custom_object_accessor import (
    CustomObjectAccessor,
    CustomObjectVariableAccessor,
)
from commission_engine.accessors.etl_config_accessor import (
    AccessTokenConfigAccessor,
    ApiAccessConfigAccessor,
    ExtractionConfigAccessor,
    IntegrationAccessor,
    TransformationConfigAccessor,
)
from commission_engine.services.etl_config_service import invalidate_connection_records
from commission_engine.services.etl_config_validation_service import (
    etl_config_validation,
)
from everstage_ddd.self_service_integration.base_class import (
    SelfServiceIntegrationBaseClass,
    SQLConnectorBaseClass,
)
from everstage_ddd.self_service_integration.constants import (
    DEFAULT_SCHEMAS,
    SQL_DATE_DATATYPE,
    SQL_DATETIME_DATATYPE,
    SQL_NUMBER_DATATYPE,
    SQL_QUERY_LIMIT,
    IntegrationAccessTypes,
    MiddlewareConnectors,
    SelfServiceConnectors,
    SelfServiceIntegrationModes,
    SQLConnectors,
)
from everstage_ddd.self_service_integration.exceptions import (
    InvalidConnectorTypeError,
    InvalidSchemaError,
    InvalidSyncFieldTypeError,
)
from everstage_ddd.upstream.fivetran_webhook.services.fivetran_sync_log_service import (
    FivetranSyncLogService,
)

logger = logging.getLogger(__name__)


class SQLSelfServiceIntegration(SelfServiceIntegrationBaseClass):
    """
    Class for SQL Self Service Integration
    """

    def __init__(self, client_id: int, access_token_config_id, self_serve_mode) -> None:
        super().__init__(
            client_id=client_id,
            service_name=SelfServiceConnectors.SQL.value,
            access_token_config_id=access_token_config_id,
            self_serve_mode=self_serve_mode,
        )
        self.connection = None
        self._connector_obj = None

    def get_sql_connector_object(self, db_type, **kwargs):
        if self._connector_obj:
            return self._connector_obj

        self._connector_obj = base_factory.SQLConnectorsFactory(
            client_id=self.client_id
        ).get_connector(db_type=db_type, **kwargs)
        return self._connector_obj

    def validate_save_connection(self, connection_name, db_type, **kwargs):
        sql_connector_object = self.get_sql_connector_object(db_type, **kwargs)
        if not sql_connector_object:
            raise InvalidConnectorTypeError()
        sql_connector_object.connect()

        if not sql_connector_object.validate_if_schema_exists():
            raise InvalidSchemaError()

        access_request_body = sql_connector_object.get_access_request_body(**kwargs)
        access_request_body["db_type"] = db_type
        is_default = kwargs.get("is_default", False)
        middleware_name = kwargs.get("middleware_name", None)
        if is_default and middleware_name in (
            MiddlewareConnectors.AIRBYTE.value,
            MiddlewareConnectors.HEVO.value,
        ):
            access_request_body["db_schema"] = [DEFAULT_SCHEMAS[middleware_name]]
            access_request_body["prefix"] = [
                sql_connector_object.middleware_default_prefix
            ]
            access_request_body["suffix"] = []
        AccessTokenConfigAccessor(client_id=self.client_id).create_record(
            service_name=SelfServiceConnectors.SQL.value,
            access_type=IntegrationAccessTypes.SQL.value,
            payload_type=None,
            access_token_url=None,
            access_request_body=access_request_body,
            domain=None,
            connection_name=connection_name,
        )
        sql_connector_object.disconnect()

    def validate_update_connection(self, db_type, access_token_config_id, **kwargs):
        sql_connector_object = self.get_sql_connector_object(db_type, **kwargs)
        if not sql_connector_object:
            raise InvalidConnectorTypeError()
        sql_connector_object.connect()

        if not sql_connector_object.validate_if_schema_exists():
            raise InvalidSchemaError()

        access_request_body = sql_connector_object.get_access_request_body(**kwargs)
        access_request_body["db_type"] = db_type
        is_default = kwargs.get("is_default", False)
        middleware_name = kwargs.get("middleware_name", None)
        if is_default and middleware_name in (
            MiddlewareConnectors.AIRBYTE.value,
            MiddlewareConnectors.HEVO.value,
        ):
            access_request_body["db_schema"] = [DEFAULT_SCHEMAS[middleware_name]]
            access_request_body["prefix"] = [
                sql_connector_object.middleware_default_prefix
            ]
            access_request_body["suffix"] = []
        new_access_token_config_id = AccessTokenConfigAccessor(
            client_id=self.client_id
        ).update_record_returning_id(
            access_token_config_id=access_token_config_id,
            updated_fields={
                "access_request_body": access_request_body,
                "connection_name": kwargs.get("connection_name"),
            },
        )

        ApiAccessConfigAccessor(client_id=self.client_id).update_access_token_config_id(
            old_access_token_config_id=access_token_config_id,
            new_access_token_config_id=new_access_token_config_id,
        )
        ExtractionConfigAccessor(
            client_id=self.client_id
        ).update_access_token_config_id(
            old_access_token_config_id=access_token_config_id,
            new_access_token_config_id=new_access_token_config_id,
        )
        sql_connector_object.disconnect()

    def delete_connection(self):
        invalidate_connection_records(
            client_id=self.client_id,
            access_token_config_id=self.access_token_config.access_token_config_id,
        )

    def get_all_objects_in_connection(self, db_type=None, **kwargs):
        sql_connector_object = None
        if self.self_serve_mode == SelfServiceIntegrationModes.OBJECT.value:
            db_type, connector_params = (
                self._get_db_type_connector_params_from_access_token()
            )
            sql_connector_object = self.get_sql_connector_object(
                db_type=db_type, **connector_params
            )

        else:
            sql_connector_object = self.get_sql_connector_object(
                db_type=db_type, **kwargs
            )
        if not sql_connector_object:
            raise InvalidConnectorTypeError()
        sql_connector_object.connect()
        tables = sql_connector_object.get_all_tables()
        sql_connector_object.disconnect()
        return tables

    def get_all_fields_in_object(self, object_id: str, db_type=None, **kwargs):
        sql_connector_object = None
        should_fetch_object_name = kwargs.get("should_fetch_object_name", False)
        if self.self_serve_mode == SelfServiceIntegrationModes.OBJECT.value:
            db_type, connector_params = (
                self._get_db_type_connector_params_from_access_token()
            )
            sql_connector_object = self.get_sql_connector_object(
                db_type=db_type, **connector_params
            )
        else:
            sql_connector_object = self.get_sql_connector_object(
                db_type=db_type, **kwargs
            )
        if not sql_connector_object:
            raise InvalidConnectorTypeError()
        sql_connector_object.connect()
        table_name, schema_name = self.get_object_and_schema_name_from_object_id(
            object_id=object_id
        )

        db_type, _ = self._get_db_type_connector_params_from_access_token()
        if (
            db_type != SQLConnectors.SNOWFLAKE.value
            and db_type != SQLConnectors.POSTGRES.value
            and should_fetch_object_name
        ):
            all_tables = sql_connector_object.get_all_tables()
            for table in all_tables:
                if table["label"].lower() == table_name.lower():
                    table_name = table["label"]
                    break
        fields = sql_connector_object.get_all_fields_in_table(
            table_name=table_name, schema_name=schema_name
        )
        sql_connector_object.disconnect()
        return fields

    def get_mapped_and_unmapped_fields_for_integration(
        self, object_id: str, integration_id: UUID
    ):
        records = TransformationConfigAccessor(
            client_id=self.client_id
        ).get_records_by_integration_id(integration_id=integration_id)
        custom_object_id = int(records.first().destination_object_id)  # type: ignore
        destination_fields = list(records.values_list("destination_field", flat=True))
        source_to_destination_field_map = {
            record.source_field: record.destination_field for record in records
        }
        system_name_to_data_type_map = dict(
            CustomObjectVariableAccessor(
                self.client_id
            ).get_data_types_for_system_names(custom_object_id, destination_fields)
        )
        mapped_field_names: set[str] = set(
            records.values_list("source_field", flat=True)
        )
        all_fields = self.get_all_fields_in_object(
            object_id=object_id, should_fetch_object_name=True
        )
        unmapped_fields = []
        mapped_fields = []

        for field in all_fields:
            field_name = field["name"]

            if field_name in mapped_field_names:
                mapped_fields.append(field)
            else:
                unmapped_fields.append(field)

        for source_field in mapped_field_names:
            if not any(field["name"] == source_field for field in mapped_fields):
                mapped_fields.append(
                    {
                        "label": source_field,
                        "name": source_field,
                        "type": system_name_to_data_type_map[
                            source_to_destination_field_map[source_field]
                        ],
                    }
                )

        return {
            "mapped_fields": mapped_fields,
            "unmapped_fields": unmapped_fields,
        }

    def get_object_meta_info(self, object_id: str):
        object_name, schema_name = self.get_object_and_schema_name_from_object_id(
            object_id=object_id
        )
        return (
            {"label": object_name, "name": object_name}
            if not schema_name
            else {
                "label": object_name,
                "name": object_id,
            }
        )

    def get_object_and_schema_name_from_object_id(self, object_id: str, **kwargs):
        sql_connector_object = None
        db_type = None
        if self.self_serve_mode == SelfServiceIntegrationModes.OBJECT.value:
            db_type, connector_params = (
                self._get_db_type_connector_params_from_access_token()
            )
            sql_connector_object = self.get_sql_connector_object(
                db_type=db_type, **connector_params
            )

        else:
            sql_connector_object = self.get_sql_connector_object(
                db_type=db_type, **kwargs
            )
        if not sql_connector_object:
            raise InvalidConnectorTypeError()

        return sql_connector_object.get_object_and_schema_name_from_object_id(
            object_id=object_id
        )

    def generate_object_id(self, **kwargs):
        sql_connector_object = None
        db_type = None
        connector_params = None
        if self.self_serve_mode == SelfServiceIntegrationModes.OBJECT.value:
            db_type, connector_params = (
                self._get_db_type_connector_params_from_access_token()
            )
            sql_connector_object = self.get_sql_connector_object(
                db_type=db_type, **connector_params
            )
        else:
            sql_connector_object = self.get_sql_connector_object(
                db_type=db_type, **kwargs
            )
        if not sql_connector_object:
            raise InvalidConnectorTypeError()
        if not connector_params:
            return InvalidConnectorTypeError()
        return sql_connector_object.generate_object_id(**kwargs)

    def create(  # noqa: PLR0913
        self,
        object_id: str,
        custom_object_id: int,
        field_mappings: list,
        sync_type: str,
        **kwargs,
    ) -> None:

        changes_sync_field = kwargs.get("changes_sync_field", {})
        delete_sync_field = kwargs.get("delete_sync_field", {})

        db_type, connector_params = (
            self._get_db_type_connector_params_from_access_token()
        )
        object_name, schema_name = self.get_object_and_schema_name_from_object_id(
            object_id=object_id
        )
        sql_connector_object = self.get_sql_connector_object(
            db_type=db_type, **connector_params
        )
        is_fivetran_connected = (
            connector_params.get("middleware_name")
            == MiddlewareConnectors.FIVETRAN.value
        )

        object_id = sql_connector_object.generate_object_id(
            schema_name=schema_name, table_name=object_name
        )

        integration_id = self._create_integration_returning_id(
            object_id=object_id.lower(),
            destination_object_id=custom_object_id,
            is_fivetran_connected=is_fivetran_connected,
            sync_fields={
                "changes_sync_field": changes_sync_field["fieldValue"],
                "delete_sync_field": (
                    delete_sync_field["fieldValue"] if delete_sync_field else None
                ),
            },
        )

        self._create_extraction(
            integration_id=integration_id,
            object_id=object_id.lower(),
            custom_object_id=custom_object_id,
            sync_type=sync_type,
        )

        self._create_transformations(
            object_id=object_id.lower(),
            custom_object_id=custom_object_id,
            integration_id=integration_id,
            mappings=field_mappings,
        )

        self.create_api_access_config(
            object_id=object_id.lower(),
            integration_id=integration_id,
            custom_object_id=custom_object_id,
            changes_sync_field=changes_sync_field,
            field_mappings=field_mappings,
            sql_connector_object=sql_connector_object,
            non_lowered_object_id=object_id,
            delete_sync_field=delete_sync_field,
        )
        if delete_sync_field:
            self.create_api_access_delete_config(
                object_id=object_id.lower(),
                integration_id=integration_id,
                changes_sync_field=changes_sync_field,
                delete_sync_field=delete_sync_field,
                custom_object_id=custom_object_id,
                field_mappings=field_mappings,
                non_lowered_object_id=object_id,
                sql_connector_object=sql_connector_object,
            )

        e2e_sync_run_id = uuid4()
        sync_run_id = uuid4()

        self._create_initial_sync_line(
            object_id=object_id.lower(),
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
            integration_id=integration_id,
            sync_type=sync_type,
        )

        etl_config_validation(
            client_id=self.client_id,
            integration_id=integration_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
        )

    def create_api_access_config(
        self,
        object_id: str,
        integration_id: UUID,
        **kwargs,
    ):
        """
        Create the API access config
        """
        custom_object_id: int = kwargs.get("custom_object_id")
        changes_sync_field: dict = kwargs.get("changes_sync_field", {})
        field_mappings: list = kwargs.get("field_mappings", [])
        sql_connector_object: SQLConnectorBaseClass = kwargs.get("sql_connector_object")
        delete_sync_field: dict = kwargs.get("delete_sync_field", {})
        non_lowered_object_id = kwargs.get("non_lowered_object_id")
        fields = {
            "source_object_id": object_id,
            "request_url": None,
            "request_type": "get",
            "request_body": {
                "query": sql_connector_object.construct_api_access_config_query(
                    date_field_name_type=changes_sync_field,
                    delete_sync_field=(
                        delete_sync_field["fieldValue"] if delete_sync_field else None
                    ),
                    object_id=non_lowered_object_id,
                )
            },
            "request_header": None,
            "access_token_config_id": self.access_token_config.access_token_config_id,
            "integration": self.service_name,
            "response_key": "results",
            "additional_data": self.get_additional_data(
                custom_object_id=custom_object_id,
                field_mappings=field_mappings,
                sync_field_data_type=changes_sync_field["fieldType"],
            ),
            "integration_id": integration_id,
        }

        ApiAccessConfigAccessor(client_id=self.client_id).create_and_persist_record(
            fields=fields
        )

    def create_api_access_delete_config(
        self,
        object_id: str,
        integration_id: UUID,
        **kwargs,
    ):
        """
        Create the API access delete config
        """
        changes_sync_field: dict = kwargs.get("changes_sync_field", {})
        delete_sync_field: dict = kwargs.get("delete_sync_field", {})
        custom_object_id: int = kwargs.get("custom_object_id")
        field_mappings: list = kwargs.get("field_mappings", [])
        sql_connector_object: SQLConnectorBaseClass = kwargs.get("sql_connector_object")
        non_lowered_object_id = kwargs.get("non_lowered_object_id")
        fields = {
            "source_object_id": f"{object_id}_delete",
            "request_url": None,
            "request_type": "get",
            "request_body": {
                "query": sql_connector_object.construct_api_access_config_delete_query(
                    date_field_name_type=changes_sync_field,
                    delete_flag_field=delete_sync_field["fieldValue"],
                    object_id=non_lowered_object_id,
                )
            },
            "request_header": None,
            "access_token_config_id": self.access_token_config.access_token_config_id,
            "integration": self.service_name,
            "response_key": "results",
            "additional_data": self.get_additional_data(
                custom_object_id=custom_object_id,
                field_mappings=field_mappings,
                sync_field_data_type=changes_sync_field["fieldType"],
            ),
            "integration_id": integration_id,
        }

        ApiAccessConfigAccessor(client_id=self.client_id).create_and_persist_record(
            fields=fields
        )

    def get_additional_data(
        self, custom_object_id: int, field_mappings: list, sync_field_data_type: str
    ):
        additional_data = {
            "order_by": self.get_order_by_string(custom_object_id, field_mappings),
            "limit_per_query": SQL_QUERY_LIMIT,
            "sync_field_type": self.get_sync_field_type(sync_field_data_type),
        }
        db_type, _ = self._get_db_type_connector_params_from_access_token()
        if db_type == SQLConnectors.SUITE_ANALYTICS.value:
            additional_data["connector"] = "suiteanalytics"
        return additional_data

    def get_sync_field_type(self, data_type: str):
        if data_type.lower() in SQL_DATE_DATATYPE:
            return "date"
        elif data_type.lower() in SQL_NUMBER_DATATYPE:
            return "unix"
        elif data_type.lower() in SQL_DATETIME_DATATYPE:
            return "datetime"
        else:
            raise InvalidSyncFieldTypeError()

    def get_order_by_string(self, custom_object_id: int, field_mappings: list):
        primary_keys = CustomObjectAccessor(client_id=self.client_id).get_primary_keys(
            object_id=custom_object_id
        )["primary_key"]
        order_by_string = ""
        field_mappings_map = {
            field["destination_field"]: field["source_field"]
            for field in field_mappings
        }
        for primary_key in primary_keys:
            if primary_key in field_mappings_map:
                order_by_string += f"{field_mappings_map[primary_key]},"
        if len(order_by_string) > 0:
            return order_by_string[:-1]
        return order_by_string

    def _get_db_type_connector_params_from_access_token(self):
        access_request_body = self.access_token_config.access_request_body.copy()
        db_type = access_request_body.get("db_type")
        access_request_body.pop("db_type")
        return db_type, access_request_body

    def _create_integration_returning_id(
        self,
        object_id: str,
        destination_object_id: int,
        is_fivetran_connected: bool,  # noqa: FBT001
        sync_fields: dict,
    ) -> UUID:
        object_name, _ = self.get_object_and_schema_name_from_object_id(
            object_id=object_id
        )
        integration_record_data = {
            "name": object_name.capitalize(),
            "desc": f"Self-serviced integration for {self.service_name.capitalize()} - {object_id.capitalize()}",
            "service_name": self.service_name,
            "source_object_id": object_id,
            "is_api": True,
            "destination_object_id": destination_object_id,
            "additional_data": {
                "run_snowflake_sync": True,
                "changes_sync_field": sync_fields.get("changes_sync_field"),
                "delete_sync_field": sync_fields.get("delete_sync_field"),
            },
        }
        if is_fivetran_connected:
            fivetran_service = FivetranSyncLogService(client_id=self.client_id)
            schema_strategy = fivetran_service.get_schema_strategy(object_id)
            if schema_strategy:
                integration_record_data.setdefault("additional_data", {})[
                    "schema_strategy"
                ] = schema_strategy

        integration_id = IntegrationAccessor(
            client_id=self.client_id
        ).create_and_persist_record(fields=integration_record_data)

        return integration_id
