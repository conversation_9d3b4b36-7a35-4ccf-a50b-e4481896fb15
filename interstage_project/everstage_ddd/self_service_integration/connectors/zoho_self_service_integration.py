import logging
from operator import itemgetter
from uuid import UUID, uuid4

from requests import request

from commission_engine.accessors.custom_object_accessor import (
    CustomObjectAccessor,
    CustomObjectVariableAccessor,
)
from commission_engine.accessors.etl_config_accessor import (
    AccessTokenConfigAccessor,
    ApiAccessConfigAccessor,
    ExtractionConfigAccessor,
    IntegrationAccessor,
    TransformationConfigAccessor,
)
from commission_engine.custom_types.self_service_integration_types import (
    FieldMappingType,
    SourceObjectFieldType,
    SourceObjectType,
)
from commission_engine.services.etl_config_service import invalidate_connection_records
from commission_engine.services.etl_config_validation_service import (
    etl_config_validation,
)
from everstage_ddd.self_service_integration.connectors.zoho_connector import (
    ZohoConnector,
)
from everstage_ddd.self_service_integration.constants import (
    AccessTokenUrls,
    SelfServiceIntegrationModes,
    ServerUrls,
    ZohoAccessTypes,
    ZohoConnectionTypes,
    ZohoVersion,
)
from everstage_ddd.self_service_integration.exceptions import (
    PrimaryKeyError,
    SelfServiceConnectorError,
)

from ..base_class import SelfServiceIntegrationBaseClass

logger = logging.getLogger(__name__)


class ZohoSelfServiceIntegration(SelfServiceIntegrationBaseClass):
    def __init__(
        self, client_id: int, access_token_config_id: int, self_serve_mode
    ) -> None:
        super().__init__(
            client_id=client_id,
            service_name="zoho",
            access_token_config_id=access_token_config_id,
            self_serve_mode=self_serve_mode,
        )
        self._connector_obj = None
        self.connection = None
        self.access_token_config = None
        self.is_analytics = False
        self.org_id = None
        self.access_token_url = None
        if self.self_serve_mode == SelfServiceIntegrationModes.OBJECT.value:
            record = (
                AccessTokenConfigAccessor(client_id=client_id)
                .get_record_by_id(access_token_config_id=access_token_config_id)
                .last()
            )
            if record is None:
                raise SelfServiceConnectorError("Record not found")  # noqa: TRY003
            self.is_analytics = (record.additional_data or {}).get(
                "zoho_analytics", False
            )
            self.org_id = (record.additional_data or {}).get("org_id", "")
            self.access_token_url = (
                record.access_token_url if record.access_token_url else ""
            )
            self.access_token_config = record
            self._connector_obj = self.__get_api_client(access_token_config_id)

    def _get_credentials(self) -> dict[str, str]:
        if isinstance(self.access_token_config.access_request_body, dict):
            return self.access_token_config.access_request_body
        elif isinstance(self.access_token_config.api_access_key, str):
            return {"api_access_key": self.access_token_config.api_access_key}
        else:
            raise TypeError(  # noqa: TRY003
                "Invalid type for access_request_body or api_access_key"
            )  # noqa: TRY003

    def _get_domain(self) -> str:
        return self.access_token_config.domain or ""

    def __get_api_client(self, access_token_config_id) -> ZohoConnector:
        client_id, client_secret = itemgetter("client_id", "client_secret")(
            self._get_credentials()
        )

        domain = self._get_domain()

        return ZohoConnector(
            client_id=client_id,
            access_request_body=self._get_credentials(),
            domain=domain,
            access_token_config_id=access_token_config_id,
            is_analytics=self.is_analytics,
            org_id=self.org_id,
            access_token_url=self.access_token_url,
        )

    def _create_integration_returning_id(
        self,
        object_id: str,
        destination_object_id: int,
    ) -> UUID:
        integration_record_data = {
            "name": object_id.capitalize(),
            "desc": f"Self-serviced integration for {self.service_name.capitalize()} - {object_id.capitalize()}",
            "service_name": self.service_name,
            "source_object_id": object_id.lower(),
            "is_api": True,
            "destination_object_id": destination_object_id,
            "additional_data": {
                "run_snowflake_sync": True,
                "changes_sync_field": "Modified_Time",
                "delete_sync_field": None,
            },
        }

        integration_id = IntegrationAccessor(
            client_id=self.client_id
        ).create_and_persist_record(fields=integration_record_data)

        return integration_id

    def get_mapped_and_unmapped_fields_for_integration(
        self, object_id: str, integration_id: UUID
    ):
        records = TransformationConfigAccessor(
            client_id=self.client_id
        ).get_records_by_integration_id(integration_id=integration_id)
        custom_object_id = int(records.first().destination_object_id)  # type: ignore
        destination_fields = list(records.values_list("destination_field", flat=True))
        source_to_destination_field_map = {
            record.source_field: record.destination_field for record in records
        }
        system_name_to_data_type_map = dict(
            CustomObjectVariableAccessor(
                self.client_id
            ).get_data_types_for_system_names(custom_object_id, destination_fields)
        )

        mapped_field_names: set[str] = set(
            records.values_list("source_field", flat=True)
        )
        all_fields = self.get_all_fields_in_object(object_id=object_id)

        unmapped_fields: list[SourceObjectFieldType] = []
        mapped_fields: list[SourceObjectFieldType] = []

        for field in all_fields:
            field_name = field["name"]

            if field_name in mapped_field_names:
                mapped_fields.append(field)
            else:
                unmapped_fields.append(field)

        # Handle source deleted fields
        is_type_unknown = any(field["type"] == "unknown" for field in mapped_fields)
        for source_field in mapped_field_names:
            if not any(field["name"] == source_field for field in mapped_fields):
                # Use unknown type if any mapped field has unknown type,
                # otherwise use type from destination field
                field_type = (
                    "unknown"
                    if is_type_unknown
                    else system_name_to_data_type_map[
                        source_to_destination_field_map[source_field]
                    ]
                )
                mapped_fields.append(
                    {
                        "label": source_field,
                        "name": source_field,
                        "type": field_type,
                        "is_association": False,
                        "function_name": "",
                    }
                )

        return {
            "mapped_fields": mapped_fields,
            "unmapped_fields": unmapped_fields,
        }

    def _validate(self, **kwargs):
        object_id = kwargs.get("object_id")
        custom_object_id = kwargs.get("custom_object_id")
        field_mappings = kwargs.get("field_mappings")

        # Validation for objects that do not have IsDeleted field
        fields_in_object = self._connector_obj.get_all_fields_in_object(
            object_id=object_id
        )
        flag = any(
            field.get("name").lower() == "isdeleted" for field in fields_in_object
        )
        if flag is False:
            primary_keys = (
                CustomObjectAccessor(self.client_id)
                .get_primary_keys(custom_object_id)
                .get("primary_key")
            ) or []
            if len(primary_keys) == 0:
                raise PrimaryKeyError(  # noqa: TRY003, TRY002
                    "Primary key is not set for the object"
                )  # noqa: TRY003
            if len(primary_keys) > 1:
                raise PrimaryKeyError(  # noqa: TRY003, TRY002
                    "Composite primary key is not supported for this object"
                )
            dest_pk = primary_keys[0]
            source_pk = next(
                (
                    fm["source_field"]
                    for fm in field_mappings or []
                    if fm["destination_field"] == dest_pk
                ),
                None,
            )
            if source_pk is None or source_pk.lower() != "id" and not self.is_analytics:
                raise PrimaryKeyError(  # noqa: TRY003, TRY002
                    "Only Id field can be set as primary key for this object"
                )

    def create(
        self,
        object_id: str,
        custom_object_id: int,
        field_mappings: list[FieldMappingType],
        **kwargs,
    ) -> None:
        self._validate(
            object_id=object_id,
            custom_object_id=custom_object_id,
            field_mappings=field_mappings,
        )

        integration_id = self._create_integration_returning_id(
            object_id=object_id,
            destination_object_id=custom_object_id,
        )

        self._create_extraction(
            integration_id=integration_id,
            object_id=object_id,
            custom_object_id=custom_object_id,
            sync_type=kwargs.get("sync_type"),
        )

        self._create_transformations(
            object_id=object_id,
            custom_object_id=custom_object_id,
            integration_id=integration_id,
            mappings=field_mappings,
        )

        self.create_api_access_config(
            object_id=object_id, integration_id=integration_id
        )

        self.create_api_access_delete_config(
            object_id=object_id, integration_id=integration_id
        )

        e2e_sync_run_id = uuid4()
        sync_run_id = uuid4()

        self._create_initial_sync_line(
            object_id=object_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
            integration_id=integration_id,
            sync_type=kwargs.get("sync_type"),
        )

        etl_config_validation(
            client_id=self.client_id,
            integration_id=integration_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
        )

    def create_api_access_config(self, object_id: str, integration_id: UUID):
        version = (
            ZohoVersion.DEFAULT.value if self.is_analytics else ZohoVersion.V2.value
        )
        additional_data = {
            "invalid_token": {
                "key": "code",
                "value": "INVALID_TOKEN",
                "http_status": 401,
            }
        }
        fields = {
            "source_object_id": object_id.lower(),
            "request_url": f"{self._get_domain()}/crm/{version}/{object_id}",
            "request_type": "get",
            "request_body": {
                "page": "{page}",
                "sort_by": "Modified_Time",
                "per_page": "{per_page}",
                "sort_order": "desc",
            },
            "request_header": {
                "Content-type": "application/json",
                "Authorization": "Zoho-oauthtoken {access_token}",
                "If-Modified-Since": "{If-Modified-Since}",
            },
            "access_token_config_id": self.access_token_config.access_token_config_id,
            "integration": self.service_name,
            "response_key": "data",
            "additional_data": additional_data,
            "integration_id": integration_id,
        }
        if not self.is_analytics and object_id == "users":
            fields["response_key"] = "users"
        if self.is_analytics:
            workspace_id, view_id = object_id.split("_")
            record = (
                AccessTokenConfigAccessor(client_id=self.client_id)
                .get_record_by_id(access_token_config_id=self.access_token_config_id)
                .last()
            )
            org_id = (record.additional_data or {}).get("org_id", None)
            fields["request_url"] = str(
                f"{self._get_domain()}/restapi/v2/workspaces/{workspace_id}/views/{view_id}/data?CONFIG=%7B%22responseFormat%22%3A%22json%22%7D"
            )

            fields["request_header"] = {
                "Content-type": "application/json",
                "Authorization": "Zoho-oauthtoken {access_token}",
                "ZANALYTICS-ORGID": org_id,
            }

            fields["request_body"] = {}
            fields["additional_data"] = {}
        ApiAccessConfigAccessor(client_id=self.client_id).create_and_persist_record(
            fields=fields
        )

    def create_api_access_delete_config(self, object_id: str, integration_id: UUID):
        if self.is_analytics:
            return
        version = (
            ZohoVersion.DEFAULT.value if self.is_analytics else ZohoVersion.V2.value
        )
        url = f"{self._get_domain()}/crm/{version}/{object_id}/deleted"
        body = {
            "page": "{page}",
            "sort_by": "Modified_Time",
            "per_page": "{per_page}",
            "sort_order": "desc",
        }

        additional_data = {
            "invalid_token": {
                "key": "code",
                "value": "INVALID_TOKEN",
                "http_status": 401,
            }
        }

        fields = {
            "source_object_id": f"{object_id.lower()}_delete",
            "request_url": url,
            "request_type": "get",
            "request_body": body,
            "request_header": {
                "Content-type": "application/json",
                "Authorization": "Zoho-oauthtoken {access_token}",
                "If-Modified-Since": "{If-Modified-Since}",
            },
            "access_token_config_id": self.access_token_config.access_token_config_id,
            "integration": self.service_name,
            "response_key": "data",
            "additional_data": additional_data,
            "integration_id": integration_id,
        }

        if object_id == "users":
            fields["response_key"] = "users"
        ApiAccessConfigAccessor(client_id=self.client_id).create_and_persist_record(
            fields=fields
        )

    def get_all_objects_in_connection(self) -> list[SourceObjectType]:
        return self._connector_obj.get_all_objects()

    def get_all_fields_in_object(self, object_id: str) -> list[SourceObjectFieldType]:
        return self._connector_obj.get_all_fields_in_object(
            object_id=object_id,
        )

    def get_object_meta_info(self, object_id: str) -> SourceObjectType:
        return self._connector_obj.get_object_meta_info(object_id=object_id)

    def delete_connection(self):
        invalidate_connection_records(
            client_id=self.client_id,
            access_token_config_id=self.access_token_config.access_token_config_id,
        )

    def _generate_refresh_token_from_code(self, **kwargs) -> str:
        url = None
        data_center = kwargs.get("data_center", "")
        url = AccessTokenUrls.ZOHO.value.format(data_center=data_center)
        refresh_token: str = ""
        res = request(
            method="post",
            url=url,
            data=kwargs.get("access_request_body"),
        )
        if res.json().get("error"):
            raise SelfServiceConnectorError()

        refresh_token = res.json()["refresh_token"]
        return refresh_token

    def _validate_connection(self, **kwargs) -> str:
        url = None
        domain: str = ""

        data_center = kwargs.get("data_center", "")
        url = AccessTokenUrls.ZOHO.value.format(data_center=data_center)

        res = request(
            method="post",
            url=url,
            data=kwargs.get("access_request_body"),
        )
        if res.json().get("error"):
            raise SelfServiceConnectorError()

        domain = res.json()["api_domain"]
        return domain

    def _generate_access_token(self, **kwargs) -> str:
        url = None
        data_center = kwargs.get("data_center", "")
        url = AccessTokenUrls.ZOHO.value.format(data_center=data_center)

        access_token: str = ""
        res = request(
            method="post",
            url=url,
            data=kwargs.get("access_request_body"),
        )
        if res.json().get("error"):
            raise SelfServiceConnectorError()

        access_token = res.json()["access_token"]
        return access_token

    def _validate_org_id(self, **kwargs) -> bool:
        url = None
        org_id = kwargs.get("org_id")
        data_center = kwargs.get("data_center", "")
        token = kwargs.get("access_token")
        headers = {"Authorization": f"Bearer {token}"}
        url = f"{ServerUrls.ZOHOANALYTICS.value.format(data_center=data_center)}/restapi/{ZohoVersion.DEFAULT.value}/orgs"
        res = request(
            method="get",
            url=url,
            headers=headers,
        )
        success_status_code = 200
        if res.status_code == success_status_code and res.json()["status"] == "success":
            orgs = res.json().get("data", {}).get("orgs", [])
            return any(org.get("orgId") == org_id for org in orgs)
        else:
            raise SelfServiceConnectorError()

    def _preprocess_fields(self, **kwargs):
        fields = {
            "connection_name": kwargs.get("connection_name"),
            "service_name": self.service_name.lower(),
        }
        refresh_token = kwargs.get("refresh_token")

        if kwargs.get("access_type") == ZohoAccessTypes.GRANTCODE.value:
            access_request_body = {
                "client_id": kwargs.get("client_id"),
                "client_secret": kwargs.get("client_secret"),
                "grant_type": "authorization_code",
                "code": kwargs.get("code"),
            }
            refresh_token = self._generate_refresh_token_from_code(
                service_name=self.service_name,
                access_request_body=access_request_body,
                data_center=kwargs.get("data_center"),
            )

        access_request_body = {
            "client_id": kwargs.get("client_id"),
            "client_secret": kwargs.get("client_secret"),
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
        }

        domain = self._validate_connection(
            service_name=self.service_name,
            access_request_body=access_request_body,
            data_center=kwargs.get("data_center"),
            is_analytics=self.is_analytics,
        )

        fields["domain"] = domain
        fields["additional_data"] = {"data_center": kwargs.get("data_center")}
        fields["access_token_url"] = AccessTokenUrls.ZOHO.value.format(
            data_center=kwargs.get("data_center")
        )
        fields["access_request_body"] = access_request_body

        org_id = kwargs.get("org_id", None)
        if self.is_analytics:
            token = self._generate_access_token(
                service_name=self.service_name,
                access_request_body=access_request_body,
                data_center=kwargs.get("data_center"),
            )
            if not self._validate_org_id(
                service_name=self.service_name,
                org_id=org_id,
                data_center=kwargs.get("data_center"),
                access_token=token,
            ):
                raise SelfServiceConnectorError()
            fields["additional_data"] = {
                "zoho_analytics": True,
                "org_id": org_id,
                "data_center": kwargs.get("data_center"),
            }
            fields["domain"] = "https://analyticsapi.zoho." + kwargs.get("data_center")

        return fields

    def validate_save_connection(self, **kwargs):
        """
        Validates and saves a connection to the Zoho service by handling authentication
        using either a refresh token or an authorization grant code.

        Args:
            **kwargs: Arbitrary keyword arguments, expected to contain:
                - connection_type (str): The type of Zoho connection (e.g., Analytics or CRM).
                - connection_name (str): Name of the connection.
                - service_name (str): The name of the Zoho service being accessed.
                - refresh_token (str, optional): Refresh token for authentication.
                - access_type (str, optional): Type of access (e.g., Grant Code or Refresh Token).
                - client_id (str): OAuth client ID.
                - client_secret (str): OAuth client secret.
                - code (str, optional): Authorization code, used for exchanging with a refresh token.
                - data_center (str, optional): The Zoho data center (e.g., "com", "eu").
                - org_id (str, optional): Organization ID for validation (only for Analytics).

        Workflow:
            1. Determines if the connection is for Zoho Analytics.
            2. Builds the required fields dictionary (`fields`).
            3. If `access_type` is "GRANTCODE", exchanges authorization code for a refresh token.
            4. Creates an access request body using `refresh_token`.
            5. Validates the connection by calling `validate_connection()`.
            6. If Analytics, generates an access token and validates `org_id`.
            7. Saves connection details using `AccessTokenConfigAccessor`.

        """
        try:
            self.is_analytics = (
                kwargs.get("connection_type") == ZohoConnectionTypes.ANALYTICS.value
            )
            fields = self._preprocess_fields(**kwargs)
            AccessTokenConfigAccessor(
                client_id=self.client_id
            ).create_and_persist_record(fields=fields)
        except Exception:
            logger.exception("Error when validating and saving connection.")
            raise

    def validate_update_connection(self, **kwargs):
        """
        Validates and updates an existing connection configuration for Zoho services.

        Args:
            **kwargs: Arbitrary keyword arguments, expected to contain:
                - connection_type (str): The type of Zoho connection (e.g., Analytics or CRM).
                - access_token_config_id (str): The ID of the existing access token configuration.
                - refresh_token (str, optional): The new refresh token for authentication.
                - access_type (str, optional): Type of access (e.g., Grant Code or Refresh Token).
                - client_id (str): OAuth client ID.
                - client_secret (str): OAuth client secret.
                - code (str, optional): Authorization code, used for exchanging with a refresh token.
                - data_center (str, optional): The Zoho data center (e.g., "com", "eu").
                - org_id (str, optional): Organization ID for validation (only for Analytics).

        Workflow:
            1. Fetches the existing access token configuration using `access_token_config_id`.
            2. Validates that the existing record is not missing the `zoho_analytics` flag.
            3. If `access_type` is "GRANTCODE", exchanges the authorization code for a refresh token.
            4. Builds an access request body using the `refresh_token`.
            5. Validates the connection by calling `validate_connection()`.
            6. If Analytics, generates an access token and validates `org_id`.
            7. Updates the existing access token configuration and returns the new ID.
            8. Updates related API and extraction configurations with the new access token ID.

        """
        try:
            self.is_analytics = (
                kwargs.get("connection_type") == ZohoConnectionTypes.ANALYTICS.value
            )
            access_token_config_id = kwargs.get("access_token_config_id")
            existing_access_token_config_record = AccessTokenConfigAccessor(
                client_id=self.client_id
            ).get_object_by_id(access_token_config_id)

            if (existing_access_token_config_record.additional_data or {}).get(
                "zoho_analytics"
            ):
                raise SelfServiceConnectorError(  # noqa: TRY301, TRY003
                    "zoho_analytics flag in additional_data is missing"
                )
            fields = self._preprocess_fields(**kwargs)
            new_access_token_config_id = AccessTokenConfigAccessor(
                client_id=self.client_id
            ).update_record_returning_id(
                access_token_config_id=access_token_config_id, updated_fields=fields
            )
            ApiAccessConfigAccessor(
                client_id=self.client_id
            ).update_access_token_config_id(
                old_access_token_config_id=access_token_config_id,
                new_access_token_config_id=new_access_token_config_id,
            )
            ExtractionConfigAccessor(
                client_id=self.client_id
            ).update_access_token_config_id(
                old_access_token_config_id=access_token_config_id,
                new_access_token_config_id=new_access_token_config_id,
            )
        except Exception:
            logger.exception("Error when validating and updating connection.")
            raise

    def generate_object_id(self):
        pass

    def get_object_and_schema_name_from_object_id(self):
        pass
