import csv
import logging
from io import String<PERSON>
from operator import itemgetter

from django.core.cache import cache
from requests import Response, request
from rest_framework import status

from commission_engine.custom_types import SourceObjectFieldType, SourceObjectType
from everstage_ddd.self_service_integration.constants import (
    CacheExpiryTimes,
    SelfServiceConnectors,
    ZohoApiPaths,
    ZohoVersion,
)
from everstage_ddd.self_service_integration.exceptions import (
    EmptyResponseBodyError,
    InvalidScopeError,
    SelfServiceConnectorError,
)
from everstage_ddd.self_service_integration.selectors import ConnectorObjectSelector

logger = logging.getLogger(__name__)


class ZohoConnector:
    def __init__(  # noqa: PLR0913
        self,
        client_id: str,
        access_request_body: dict[str, str],
        domain: str,
        access_token_config_id: int,
        is_analytics: bool,  # noqa: FBT001
        org_id: str,
        access_token_url: str,
    ) -> None:
        self.client_id = client_id
        self.access_request_body = access_request_body
        self.domain = domain
        self.__cache_key = (
            f"zoho_{access_token_config_id}_{self.client_id}_access_token"
        )
        self.__scopes_cache_key = (
            f"zoho_{access_token_config_id}_{self.client_id}_scopes"
        )
        self.scopes = cache.get(self.__scopes_cache_key)
        self.is_analytics = is_analytics
        self.org_id = org_id
        self.access_token_url = access_token_url

    def __make_request(  # noqa: PLR0913
        self,
        method: str,
        endpoint: str,
        params: dict[str, str] = None,
        data: dict[str, str] = None,
        version: str = None,
    ) -> Response:
        zoho_version = version or (
            ZohoVersion.DEFAULT.value if self.is_analytics else ZohoVersion.V2.value
        )
        if params is None:
            params = {}
        if data is None:
            data = {}
        res = request(
            method=method,
            url=f"{self.domain}/{ZohoApiPaths.ANALYTICS.value if self.is_analytics else ZohoApiPaths.CRM.value}/{zoho_version}{endpoint}",
            params=params,
            data=data,
            headers={
                "Authorization": (
                    f"Bearer {cache.get(self.__cache_key)}"
                    if not self.is_analytics
                    else f"Zoho-oauthtoken {cache.get(self.__cache_key)}"
                ),
                **({"ZANALYTICS-ORGID": self.org_id} if self.is_analytics else {}),
            },
        )
        if res.status_code == status.HTTP_204_NO_CONTENT:
            raise EmptyResponseBodyError()
        if res.status_code == status.HTTP_401_UNAUTHORIZED:
            if not self.is_analytics and res.json()["code"] == "OAUTH_SCOPE_MISMATCH":
                raise InvalidScopeError()
            self.__refresh_access_token()

            return self.__make_request(
                method=method, endpoint=endpoint, params=params, data=data
            )
        if res.status_code != status.HTTP_200_OK:
            raise SelfServiceConnectorError()
        return res

    def __refresh_access_token(self) -> None:
        logger.info("Access token expired. Generating access token from refresh token.")
        res = request(
            method="post",
            url=self.access_token_url,
            data=self.access_request_body,
        )
        if res.status_code != status.HTTP_200_OK:
            raise SelfServiceConnectorError()

        access_token: str = res.json()["access_token"]
        scopes: str = res.json()["scope"]
        cache.set(
            self.__cache_key, access_token, CacheExpiryTimes.ZOHO_ACCESS_TOKEN.value
        )
        cache.set(self.__scopes_cache_key, scopes, CacheExpiryTimes.ZOHO_SCOPES.value)

    def __get_analytics_objects(self):
        objects: list[SourceObjectType] = []
        logger.info("Get all objects for Zoho Analytics")
        valid_view_types = ["Table", "Report"]
        workspaces_res = self.__make_request(method="get", endpoint="/workspaces")
        workspaces = workspaces_res.json()["data"]["ownedWorkspaces"]
        for workspace in workspaces:
            workspace_id, workspace_name = itemgetter("workspaceId", "workspaceName")(
                workspace
            )
            views_res = self.__make_request(
                method="get", endpoint=f"/workspaces/{workspace_id}/views"
            )
            views = views_res.json()["data"]["views"]
            for view in views:
                view_id, view_name, view_type = itemgetter(
                    "viewId", "viewName", "viewType"
                )(view)
                if view_type in valid_view_types:
                    label = f"{workspace_name}:{view_name}"
                    name = f"{workspace_id}_{view_id}"

                    object_record: SourceObjectType = {
                        "label": label.strip(),
                        "name": name.strip(),
                    }

                    objects.append(object_record)
        return objects

    def __get_crm_objects_with_settings_scope(self):
        objects: list[SourceObjectType] = []
        logger.info("Get all objects for Zoho CRM through api")
        logger.info("Settings scope present in access token")
        res = self.__make_request(
            method="get", endpoint="/settings/modules", version="v7"
        )

        for s_object in res.json()["modules"]:
            label, name, is_api_supported, is_viewable = itemgetter(
                "module_name", "api_name", "api_supported", "viewable"
            )(s_object)
            if not is_api_supported or not is_viewable:
                continue
            object_record: SourceObjectType = {
                "label": label.strip(),
                "name": name.strip(),
            }

            objects.append(object_record)
        if "users" in self.scopes.lower():
            logger.info("Users scope present in access token")
            objects_list = (
                ConnectorObjectSelector(
                    connector_id=SelfServiceConnectors.ZOHO.value
                ).get_connector_object_list()
                or []
            )
            users_object = next(
                (obj for obj in objects_list if obj.get("name") == "users"), {}
            )
            object_record: SourceObjectType = {
                "label": users_object["label"].strip(),
                "name": users_object["name"].strip(),
            }

            objects.append(object_record)
        return objects

    def __get_crm_objects_without_settings_scope(self):
        objects: list[SourceObjectType] = []
        logger.info("Get all objects for Zoho CRM from connector objects table")
        objects_list = (
            ConnectorObjectSelector(
                connector_id=SelfServiceConnectors.ZOHO.value
            ).get_connector_object_list()
            or []
        )
        for object in objects_list:
            label, name, scopes = itemgetter("label", "name", "scopes")(object)
            if any(scope.lower() in self.scopes.lower() for scope in scopes):
                object_record: SourceObjectType = {
                    "label": label.strip(),
                    "name": name.strip(),
                }
                objects.append(object_record)
        return objects

    def __get_analytics_fields(self, object_id):
        fields: list[SourceObjectFieldType] = []
        logger.info("Get all fields for Zoho Analytics view")
        workspace_id, view_id = object_id.split("_")
        res = self.__make_request(
            method="get",
            endpoint=f"/workspaces/{workspace_id}/views/{view_id}/data",
            params={"CONFIG": '{"responseFormat":"csv"}'},
        )

        csv_text = res.text
        if csv_text.startswith("\ufeff"):
            csv_text = csv_text[1:]
        csv_data = StringIO(csv_text)
        reader = csv.DictReader(csv_data)

        field_names = reader.fieldnames

        for field in field_names:
            label = name = field
            field_record: SourceObjectFieldType = {
                "label": label.strip(),
                "name": name.strip(),
                "type": "unknown",
                "is_association": False,
                "function_name": "",
            }

            fields.append(field_record)
        return fields

    def __get_crm_fields_with_settings_scope(self, object_id):
        fields: list[SourceObjectFieldType] = []
        logger.info("Get all fields for Zoho CRM object")
        logger.info("Settings scope present in access token")
        res = self.__make_request(
            method="get",
            endpoint="/settings/fields",
            params={"module": object_id},
            version="v7",
        )

        for field in res.json()["fields"]:
            label, name, field_type = itemgetter(
                "display_label", "api_name", "data_type"
            )(field)

            field_record: SourceObjectFieldType = {
                "label": label.strip(),
                "name": name.strip(),
                "type": field_type,
                "is_association": False,
                "function_name": "",
            }

            fields.append(field_record)
        return fields

    def __get_crm_fields_without_settings_scope(self, object_id):
        fields: list[SourceObjectFieldType] = []
        logger.info("Get all fields for Zoho CRM object")
        logger.info("Fetching data through api for one record to get fields")
        logger.info("Settings scope not present in access token")
        res = self.__make_request(
            method="get",
            endpoint=f"/{object_id}",
            params={"page": "1", "per_page": "1"},
        )
        response_key = "data" if object_id != "users" else "users"
        if res.json()[response_key]:
            field_data = res.json()[response_key][0]

            for name in field_data:
                if not name.startswith("$"):
                    field_record = {
                        "label": name.strip(),
                        "name": name.strip(),
                        "type": "unknown",
                        "is_association": False,
                        "function_name": "",
                    }
                    fields.append(field_record)
        return fields

    def __get_analytics_object_meta_info(self, object_id):
        object_mf: SourceObjectType = {"label": "", "name": ""}
        logger.info("Object meta info for Zoho Analytics")
        _, view_id = object_id.split("_")
        res = self.__make_request(method="get", endpoint=f"/views/{view_id}")

        object_mf["label"] = res.json()["data"]["views"]["viewName"]
        object_mf["name"] = res.json()["data"]["views"]["viewId"]
        return object_mf

    def __get_crm_object_meta_info_with_settings_scope(self, object_id):
        object_mf: SourceObjectType = {"label": "", "name": ""}
        logger.info("Gettings object meta info for Zoho CRM object")
        logger.info("Settings scope present in access token and object is not users")
        res = self.__make_request(
            method="get", endpoint=f"/settings/modules/{object_id}"
        )

        object_mf["label"] = res.json()["modules"][0]["module_name"].strip()
        object_mf["name"] = res.json()["modules"][0]["api_name"].strip()
        return object_mf

    def __get_crm_object_meta_info_without_settings_scope(self, object_id):
        object_mf: SourceObjectType = {"label": "", "name": ""}
        logger.info("Gettings object meta info for Zoho CRM object")
        logger.info("Settings scope is not present in access token")
        objects = (
            ConnectorObjectSelector(
                connector_id=SelfServiceConnectors.ZOHO.value
            ).get_connector_object_list()
            or []
        )
        for object in objects:
            label, name = itemgetter("label", "name")(object)
            if object_id.lower() == name.lower():
                object_mf["name"] = name
                object_mf["label"] = label
        return object_mf

    def get_all_objects(self) -> list[SourceObjectType]:
        if not self.scopes:
            self.__refresh_access_token()
            self.scopes = cache.get(self.__scopes_cache_key)
        if self.is_analytics:
            return self.__get_analytics_objects()
        elif "settings" in self.scopes.lower():
            return self.__get_crm_objects_with_settings_scope()
        else:
            return self.__get_crm_objects_without_settings_scope()

    def get_all_fields_in_object(self, object_id: str) -> list[SourceObjectFieldType]:
        if not self.scopes:
            self.__refresh_access_token()
            self.scopes = cache.get(self.__scopes_cache_key)
        if self.is_analytics:
            return self.__get_analytics_fields(object_id=object_id)
        elif object_id.lower() != "users" and "settings" in self.scopes.lower():
            return self.__get_crm_fields_with_settings_scope(object_id=object_id)
        else:
            return self.__get_crm_fields_without_settings_scope(object_id=object_id)

    def get_object_meta_info(self, object_id: str) -> SourceObjectType:
        if not self.scopes:
            self.__refresh_access_token()
            self.scopes = cache.get(self.__scopes_cache_key)
        if self.is_analytics:
            return self.__get_analytics_object_meta_info(object_id=object_id)
        elif object_id.lower() != "users" and "settings" in self.scopes.lower():
            return self.__get_crm_object_meta_info_with_settings_scope(
                object_id=object_id
            )
        else:
            return self.__get_crm_object_meta_info_without_settings_scope(
                object_id=object_id
            )
