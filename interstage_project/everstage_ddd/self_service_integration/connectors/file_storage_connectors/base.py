"""
Abstract Base Class for File Storage Service Connectors.
"""

import io
import logging
import re
import zipfile
from abc import ABC, abstractmethod
from datetime import datetime
from typing import IO, Optional

import pandas as pd

from everstage_ddd.self_service_integration.data_models import FileContext
from everstage_ddd.self_service_integration.exceptions import CSVParsingError

logger = logging.getLogger(__name__)


class AbstractFileStorageConnector(ABC):
    """
    Abstract base connector providing common interface for chunk streaming
    files from various sources - SFTP, S3
    """

    def __init__(self, client_id: int, connector_params, path: str, **kwargs):
        self.client_id = client_id
        self.connector_params = connector_params
        self.context: FileContext = FileContext(path=path, **kwargs)
        self.metadata = self.context.file_metadata

    def __enter__(self):
        """Enter the runtime context related to this object."""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the runtime context related to this object."""
        self.disconnect()

    @abstractmethod
    def connect(self):
        """
        Establishes connection to the file storage service.
        """
        pass

    @abstractmethod
    def disconnect(self):
        """
        Close and clean resources associated with any open connections.
        """
        pass

    def set_file_context(self, path: str, **kwargs):
        """
        Replace context to use the open connection to read files
        from different path
        """
        self.context = FileContext(path=path, **kwargs)
        self.metadata = self.context.file_metadata

    @abstractmethod
    def list_files(self) -> list:
        """
        List files from the current context
        """
        pass

    def filter_file(
        self,
        file_name: str,
        file_modified_time: datetime,
        changes_start_time: Optional[datetime] = None,
    ) -> bool:
        """
        Filter files based on the last modified time and file context.
        """
        if not (file_name.endswith(".csv") or file_name.endswith(".zip")):
            logger.info(
                f"Skipping file {file_name} in {self.context.path} is not in csv or zip format."
            )
            return False
        if (
            (file_modified_time >= changes_start_time if changes_start_time else True)
            and file_name.endswith(self.context.file_format)
            and bool(re.match(self.context.regex, file_name))
        ):
            return True
        return False

    @abstractmethod
    def filter_files(
        self,
        files: list,
        changes_start_time: Optional[datetime] = None,
    ) -> list:
        """
        Wrapper for filter_file method to filter files based on the last modified time and file context.
        """
        pass

    def stream_file(self, file_stream: IO[bytes]):
        """
        Stream file in chunks from the current context
        """
        decoded_stream = None
        try:
            decoded_stream = io.TextIOWrapper(
                file_stream, encoding=self.metadata.encoding
            )
            for df in pd.read_csv(
                decoded_stream,
                chunksize=self.context.batch_size,
                dtype=str,  # Do not infer data types to prevent typecasting
            ):
                if not df.empty:
                    yield df
                else:
                    logger.warning("0 Rows Found, Skipping CSV file")
        except pd.errors.EmptyDataError as e:
            logger.error(f"Skipping empty CSV file: {e}")  # noqa: TRY400
            return
        except Exception as e:
            logger.exception("Error parsing CSV file from byte stream")
            raise CSVParsingError(
                message=f"Error parsing CSV file from byte stream - {e}"
            ) from e
        finally:
            if decoded_stream is not None:
                decoded_stream.close()

    def process_zip_file(self, file_obj: io.BytesIO):
        """
        Download zip file to memory and stream csv files in chunks
        """
        zip_password = bytes(self.context.password, "utf-8")
        with zipfile.ZipFile(file_obj) as zip_file:
            for file_name in zip_file.namelist():
                if file_name.startswith("__"):
                    continue
                if not file_name.endswith(".csv"):
                    logger.info(
                        f"Skipping file {file_name} in ZIP is not in csv format."
                    )
                    continue
                logger.info(f"Streaming zipped CSV file: {file_name}")
                with zip_file.open(file_name, pwd=zip_password) as csv_file:
                    yield csv_file

    @abstractmethod
    def read_files(self):
        """
        Read files from the current context
        """
        pass
