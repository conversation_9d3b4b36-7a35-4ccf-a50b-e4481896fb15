"""
S3 Connector Implementation
"""

import logging
from datetime import datetime
from io import BytesIO
from typing import Optional

import boto3

from everstage_ddd.self_service_integration.data_models import S3ConnectorParams

from .base import AbstractFileStorageConnector

logger = logging.getLogger(__name__)


class S3Connector(AbstractFileStorageConnector):
    """Connect and read files from an AWS S3 bucket."""

    def __init__(
        self, client_id: int, connector_params: S3ConnectorParams, path: str, **kwargs
    ):
        super().__init__(client_id, connector_params, path, **kwargs)
        self._s3_client = None

    def connect(self):
        if self._s3_client:
            return self._s3_client
        self._s3_client = boto3.resource(
            service_name="s3",
            region_name=self.connector_params.region_name,
            aws_access_key_id=self.connector_params.aws_access_key_id,
            aws_secret_access_key=self.connector_params.aws_secret_access_key,
        )
        return self._s3_client

    def disconnect(self) -> None:
        if self._s3_client:
            self._s3_client = None

    def list_files(self) -> list:
        if not self._s3_client:
            return []
        return self._s3_client.Bucket(self.connector_params.bucket_name).objects.filter(  # type: ignore
            Prefix=self.context.path
        )

    def filter_files(self, files: list, changes_start_time: Optional[datetime] = None):
        filtered_files = []
        for file_obj in files:
            file_name = file_obj.key.rsplit("/")[-1]
            if file_name and self.filter_file(
                file_name, file_obj.last_modified, changes_start_time
            ):
                filtered_files.append(file_obj)
        filtered_files.sort(key=lambda x: x.last_modified)
        logger.info("Filtered files:")
        for file in filtered_files:
            logger.info(f"- {file.key} (Last Modified: {file.last_modified})")
        return filtered_files

    def read_files(self, changes_start_time: Optional[datetime] = None):
        if not self._s3_client:
            return []
        files = self.list_files()
        filtered_files = self.filter_files(files, changes_start_time)

        logger.info(
            "[BEGIN] Streaming files from S3 bucket %s",
            self.connector_params.bucket_name,
        )
        for file_obj in filtered_files:
            logger.info("Streaming file: %s", file_obj.key)
            metadata = {
                "file_name": file_obj.key.rsplit("/")[-1],
                "file_mtime": file_obj.last_modified,
                "delete_field": self.metadata.delete_field,
                "delete_value": self.metadata.delete_value,
            }
            if file_obj.key.endswith(".csv"):
                streaming_body = file_obj.get()["Body"]
                for chunk in self.stream_file(streaming_body):
                    yield chunk, metadata
            elif file_obj.key.endswith(".zip"):
                # zip file does not support streaming, read into memory before processing
                downloaded_file = file_obj.get()["Body"].read()
                for csv_file in self.process_zip_file(BytesIO(downloaded_file)):
                    for chunk in self.stream_file(csv_file):
                        yield chunk, metadata

        logger.info(
            "[END] Finished streaming files from S3 bucket %s",
            self.connector_params.bucket_name,
        )
