import logging

from django.utils import timezone

from commission_engine.accessors.custom_object_accessor import (
    CustomObjectVariableAccessor,
)
from commission_engine.accessors.etl_config_accessor import (
    AccessTokenConfigAccessor,
    IntegrationAccessor,
)
from commission_engine.accessors.hyperlink_accessor import HyperlinkAccessor
from commission_engine.custom_types.self_service_integration_types import (
    FieldMappingType,
)
from commission_engine.self_service_integrations.self_service_factory import (
    SelfServiceFactory,
)
from commission_engine.services.hyperlink_service import HyperlinkService
from commission_engine.utils.general_data import Services, SQLConnectionTypes
from everstage_ddd.self_service_integration import (
    SelfServiceIntegrationFactory,
    SelfServiceIntegrationModes,
)
from everstage_ddd.self_service_integration.exceptions import ConnectionNameExistsError
from everstage_ddd.self_service_integration.selectors.connector_defintion_selector import (
    ConnectorDefinitionSelector,
)
from spm.services.custom_object_services.custom_object_service import create_object

logger = logging.getLogger(__name__)


def get_connector_details(service_name):
    return ConnectorDefinitionSelector(service_name).get_connector_frontend_data()


def validate_and_save_connection(  # noqa: PLR0913
    client_id,
    service_name,
    connection_name,
    is_edit,
    access_token_config_id,
    connection_data,
):
    db_type = connection_data.pop("db_type", None)

    does_connection_name_exists = check_if_connection_name_exists(
        client_id, connection_name, is_edit, access_token_config_id
    )
    if does_connection_name_exists:
        raise ConnectionNameExistsError()
    if is_edit:
        SelfServiceIntegrationFactory().get_connector(
            client_id=client_id,
            service_name=service_name,
            self_serve_mode=SelfServiceIntegrationModes.CONNECTION.value,
        ).validate_update_connection(
            db_type=db_type,
            access_token_config_id=access_token_config_id,
            connection_name=connection_name,
            **connection_data,
        )
    else:
        SelfServiceIntegrationFactory().get_connector(
            client_id=client_id,
            service_name=service_name,
            self_serve_mode=SelfServiceIntegrationModes.CONNECTION.value,
        ).validate_save_connection(
            connection_name=connection_name, db_type=db_type, **connection_data
        )


def check_if_connection_name_exists(
    client_id, connection_name, is_edit, access_token_config_id
):
    config_records = None
    if is_edit:
        config_records = (
            AccessTokenConfigAccessor(client_id=client_id)
            .client_kd_aware()
            .exclude(access_token_config_id=access_token_config_id)
        )
    else:
        config_records = AccessTokenConfigAccessor(
            client_id=client_id
        ).client_kd_aware()
    connection_names = [record.connection_name.lower() for record in config_records]
    return connection_name.lower() in connection_names


def get_fields_for_an_object(
    client_id, service_name, access_token_config_id, object_id
):
    access_token_config_id = int(access_token_config_id)
    if service_name in set(
        [
            SQLConnectionTypes.MSSQL.value,
            SQLConnectionTypes.POSTGRESQL.value,
            SQLConnectionTypes.SNOWFLAKE.value,
            SQLConnectionTypes.SUITEANALYTICS.value,
        ]
    ):
        service_name = "sql"
    if service_name.upper() in Services.__members__:
        return SelfServiceFactory(
            service=service_name, client_id=client_id, access_token_config_id=access_token_config_id  # type: ignore
        ).get_all_fields_in_object(
            object_id=object_id,
        )
    else:
        return (
            SelfServiceIntegrationFactory()
            .get_connector(
                service_name=service_name,
                client_id=client_id,
                access_token_config_id=access_token_config_id,
                self_serve_mode=SelfServiceIntegrationModes.OBJECT.value,
            )
            .get_all_fields_in_object(object_id=object_id)
        )


def create_self_serve_integrated_object(  # noqa: PLR0913, PLR0912
    client_id,
    request,
    create_custom_object,
    service_name,
    object_id,
    field_mappings,
    access_token_config_id,
    object_data,
    **kwargs,
) -> int:
    dis_sys_map = {}
    if create_custom_object:
        # Create custom object
        custom_object_info = create_object(
            client_id=client_id, request=request, object_data=object_data
        )

        custom_object_id = custom_object_info["object_response"].get("custom_object_id")

        custom_object_variables = CustomObjectVariableAccessor(
            client_id=client_id
        ).all_variables_in_object(object_ids=[custom_object_id], as_dicts=True)

        for cov in custom_object_variables:
            dis_sys_map[cov.get("display_name")] = cov.get("system_name")

        # Change fields_mappings from {sourceField: destinationField (displayName)} to {sourceField: destinationField (systemName)}
        temp_field_mappings: list[FieldMappingType] = []

        for fm in field_mappings:
            temp_fm: FieldMappingType = {}  # type: ignore

            temp_fm["source_field"] = fm["source_field"]
            temp_fm["field_type"] = fm["field_type"]
            temp_fm["destination_field"] = dis_sys_map[fm["destination_field"]]
            temp_fm["is_association"] = fm["is_association"]
            if fm["is_association"]:
                temp_fm["associated_object_id"] = fm["associated_object_id"]
            if fm["applied_function"]:
                temp_fm["applied_function"] = fm["applied_function"]

            temp_field_mappings.append(temp_fm)

        field_mappings = temp_field_mappings
    else:
        custom_object_id: int = kwargs.get("custom_object_id")  # type: ignore

    if service_name == Services.SALESFORCE.value:
        SelfServiceFactory(
            service=service_name,
            client_id=client_id,
            access_token_config_id=access_token_config_id,
        ).validate(
            object_id=object_id,
            custom_object_id=custom_object_id,
            field_mappings=field_mappings,
        )
        SelfServiceFactory(
            service=service_name,
            client_id=client_id,
            access_token_config_id=access_token_config_id,
        ).create(
            object_id=object_id,
            custom_object_id=custom_object_id,
            field_mappings=field_mappings,
            sync_type=kwargs.get("syncType"),
            hyperlinked_field=kwargs.get("hyperlinked_field"),
            include_hard_delete_config=kwargs.get("include_hard_delete_config"),
        )
    elif service_name == Services.HUBSPOT.value:
        SelfServiceFactory(
            service=service_name,
            client_id=client_id,
            access_token_config_id=access_token_config_id,
        ).validate()
        SelfServiceFactory(
            service=service_name,
            client_id=client_id,
            access_token_config_id=access_token_config_id,
        ).create(
            object_id=object_id,
            custom_object_id=custom_object_id,
            field_mappings=field_mappings,
            sync_type=kwargs.get("syncType"),
            pipeline_type=kwargs.get("pipeline_type", ""),
            has_associations=kwargs.get("has_associations"),
            associated_objects=kwargs.get("associated_objects"),
            hyperlinked_field=kwargs.get("hyperlinked_field"),
        )
    else:
        if service_name in set(
            [
                SQLConnectionTypes.MSSQL.value,
                SQLConnectionTypes.POSTGRESQL.value,
                SQLConnectionTypes.SNOWFLAKE.value,
                SQLConnectionTypes.SUITEANALYTICS.value,
            ]
        ):
            service_name = "sql"

        SelfServiceIntegrationFactory().get_connector(
            service_name=service_name,
            client_id=client_id,
            access_token_config_id=access_token_config_id,
            self_serve_mode=SelfServiceIntegrationModes.OBJECT.value,
        ).create(
            object_id=object_id,
            custom_object_id=custom_object_id,
            field_mappings=field_mappings,
            sync_type=kwargs.get("syncType"),
            changes_sync_field=kwargs.get("changes_sync_field"),
            delete_sync_field=kwargs.get("delete_sync_field"),
        )

    hyperlinked_field = None
    source_hyperlinked_field = kwargs.get("hyperlinked_field")
    source_hyperlinked_url = kwargs.get("hyperlinked_url")
    audit_data = request.audit

    create_self_serve_hyperlink: bool = (
        source_hyperlinked_field is not None and create_custom_object
    )

    if create_self_serve_hyperlink:
        # Create Hyperlinked Record for Salesforce or Hubspot
        if (service_name or "").upper() in Services.__members__:
            for item in field_mappings:
                if item.get("source_field") == source_hyperlinked_field:
                    hyperlinked_field = item.get("destination_field")
                    break

            HyperlinkService(client_id).create_self_service_hyperlink(
                hyperlinked_field=hyperlinked_field,  # type: ignore
                source_object_id=object_id,
                custom_object_id=custom_object_id,
                service_name=service_name,
                audit_data=audit_data,
            )
        # Create Hyperlinked Record for other self-service
        else:
            if not source_hyperlinked_url:
                raise ValueError("Hyperlink URL is empty!")  # noqa: TRY003
            HyperlinkService(client_id).create_hyperlink_record(
                hyperlinked_url=source_hyperlinked_url,
                hyperlinked_field=source_hyperlinked_field,
                custom_object_id=custom_object_id,
                audit_data=audit_data,
                display_name_to_system_name_map=dis_sys_map,
            )

    if not create_custom_object and custom_object_id:
        # handle link object to connection, Invalidate existing Hyperlink data if it exists
        time = timezone.now()
        HyperlinkAccessor(client_id).invalidate_hyperlink_records_by_custom_object_id(
            custom_object_id=int(custom_object_id), invalidation_time=time
        )

    return custom_object_id


def get_sync_fields(client_id: int, custom_object_id: int) -> dict:
    """
    Get sync fields for a given custom object
    """
    try:
        # Get integration data using custom_object_id
        integration_data = IntegrationAccessor(
            client_id=client_id
        ).get_integration_by_custom_object_id(custom_object_id=custom_object_id)

        if not integration_data:
            return {
                "changes_sync_field": None,
                "delete_sync_field": None,
            }

        additional_data = integration_data.additional_data or {}

        return {
            "changes_sync_field": additional_data.get("changes_sync_field"),
            "delete_sync_field": additional_data.get("delete_sync_field"),
        }
    except Exception as err:
        logger.exception(
            "Error while fetching sync fields for custom_object_id %s",
            custom_object_id,
            exc_info=err,
        )
        return {
            "changes_sync_field": None,
            "delete_sync_field": None,
        }
