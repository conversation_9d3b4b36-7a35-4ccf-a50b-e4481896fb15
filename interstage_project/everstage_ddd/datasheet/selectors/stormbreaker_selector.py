import gc
from typing import Optional
from uuid import UUID

import pandas as pd

import everstage_ddd.datasheet.selectors as datasheet_selectors
from commission_engine.services.commission_calculation_service.data_generator import (
    remove_nan_nat,
)
from commission_engine.services.datasheet_data_services.datasheet_retrieval_service import (
    pivot_and_sort_data,
)
from commission_engine.utils.general_utils import replace_nan_nat
from everstage_ddd.datasheet.data_models import (
    DatasheetDataRequest,
    ExportDatasheetRequest,
)
from everstage_ddd.datasheet.enums import MAX_COLUMN_LIMIT
from everstage_ddd.datasheet.exceptions import DatasheetException
from everstage_ddd.stormbreaker import StormBreakerDSFactory, StormBreakerDSInitType

# ruff: noqa: PLR0913


class StormBreakerSelector:

    def __init__(
        self,
        client_id: int,
        databook_id: UUID,
        datasheet_id: UUID,
        logged_in_user_email: str,
        view_id: Optional[UUID] = None,
    ):
        self.client_id = client_id
        self.databook_id = databook_id
        self.datasheet_id = datasheet_id
        self.logged_in_user_email = logged_in_user_email
        self.view_id = view_id
        self.sb_class = self._get_stormbreaker_class()

    def fetch_datasheet_data_details(self, request_model: DatasheetDataRequest) -> dict:
        details = self._get_data_and_count(request_model)

        datasheet = datasheet_selectors.DatasheetSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        ).get_datasheet()

        variables = datasheet_selectors.DatasheetVariableSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        ).get_ordered_variables(
            datasheet.ordered_columns or [], self.logged_in_user_email
        )

        hidden_columns = datasheet.hidden_columns or []

        return {
            "data": details["data"],
            "variables": variables,
            "total_records": details["total_records"],
            "hidden_columns": hidden_columns,
            "pivot_columns": details["pivot_columns"],
        }

    def fetch_datasheet_view_data_details(
        self, request_model: DatasheetDataRequest
    ) -> dict:
        details = self._get_data_and_count(request_model)

        datasheet_view = datasheet_selectors.DatasheetViewSelector(
            client_id=self.client_id,
            datasheet_id=self.datasheet_id,
            view_id=self.view_id,
        ).get_datasheet_view()

        variables = datasheet_selectors.DatasheetVariableSelector(
            client_id=self.client_id,
            datasheet_id=self.datasheet_id,
        ).get_ordered_variables(
            datasheet_view.ordered_columns or [], self.logged_in_user_email
        )

        hidden_columns = datasheet_view.hidden_columns or []

        return {
            "data": details["data"],
            "variables": variables,
            "total_records": details["total_records"],
            "hidden_columns": hidden_columns,
            "pivot_columns": details["pivot_columns"],
        }

    def fetch_datasheet_data_for_export(
        self, request_model: ExportDatasheetRequest
    ) -> dict:
        self.sb_class.apply_infix_filter(request_model.filters)
        self.sb_class.set_case_insensitive_infix_filters()
        if request_model.filters:
            # date_trunc_precision is "day" for date comparison for datasheet views / filters in v2.
            self.sb_class.set_date_trunc_precision("day")
        if not request_model.apply_adjustments:
            self.sb_class.set_fetch_data_without_adjustments()
        data = self.sb_class.fetch_datasheet_data_latest(
            as_dataframe=True, limit=None, offset=None
        )
        return data

    def fetch_datasheet_data_for_exportv2(
        self, request_model: ExportDatasheetRequest, *, for_lambda: bool = False
    ) -> str:
        # To know the use of '*' refer https://docs.astral.sh/ruff/rules/boolean-type-hint-positional-argument/
        self.sb_class.apply_infix_filter(request_model.filters)
        if request_model.filters:
            # date_trunc_precision is "day" for date comparison for datasheet views / filters in v2.
            self.sb_class.set_date_trunc_precision("day")
        self.sb_class.set_case_insensitive_infix_filters()
        if not request_model.apply_adjustments:
            self.sb_class.set_fetch_data_without_adjustments()
        if for_lambda:
            self.sb_class.set_add_system_name_alias_in_select()
            query = self.sb_class.get_query_string(
                add_display_name_alias_in_select=False, limit=None, offset=None
            )
        else:
            query = self.sb_class.get_query_string(
                add_display_name_alias_in_select=True, limit=None, offset=None
            )
        if not request_model.apply_adjustments:
            query = f"{query[0]} UNION ALL {query[1]}"
        return query

    def get_records_count(self) -> int:
        return self.sb_class.get_records_count()

    def get_adjusted_records_count(self) -> int:
        sb_class = self._get_stormbreaker_class()
        sb_class.set_fetch_only_adjusted_records()
        return sb_class.get_records_count()

    def get_records_count_with_filter_data(self, filter_data: list) -> int:
        self.sb_class = self._get_stormbreaker_class()
        self.sb_class.apply_infix_filter(filter_data)
        self.sb_class.set_case_insensitive_infix_filters()
        if filter_data:
            # date_trunc_precision is "day" for date comparison for datasheet views / filters in v2.
            self.sb_class.set_date_trunc_precision("day")

        return self.get_records_count()

    def _get_data_and_count(self, request_model: DatasheetDataRequest) -> dict:
        self.sb_class.order_by(request_model.sort_columns_by)
        self.sb_class.apply_infix_filter(request_model.filters)
        self.sb_class.set_case_insensitive_infix_filters()
        if request_model.filters:
            # date_trunc_precision is "day" for date comparison for datasheet views / filters in v2.
            self.sb_class.set_date_trunc_precision("day")
        if request_model.pivot_details:
            result = self._apply_pivot_data(request_model)
            data = result["data"]
            total_records = result["data_count"]
            pivot_columns = result["pivot_columns"]
        else:
            data = self.sb_class.fetch_datasheet_data_latest(
                limit=request_model.page_size, offset=request_model.page_offset
            )
            pivot_columns = {}
            total_records = self.get_records_count()
        return {
            "data": data,
            "total_records": total_records,
            "pivot_columns": pivot_columns,
        }

    def _get_stormbreaker_class(self):  # -> Any | StormBreakerDS:
        params = StormBreakerDSInitType(
            # TODO: @sriram/@sukanya - Add support for infix_filters support in flattened use case
            compute_strategy="snowflake_variant",
            client_id=self.client_id,
            databook_id=self.databook_id,
            datasheet_id=self.datasheet_id,
            logged_in_user_email=self.logged_in_user_email,
        )
        return StormBreakerDSFactory.init(params)

    def _apply_pivot_data(self, request_model: DatasheetDataRequest):
        variables = datasheet_selectors.DatasheetVariableSelector(
            self.client_id, self.datasheet_id
        ).get_selected_variables_with_permission(self.logged_in_user_email)
        pivot_details = request_model.model_dump()["pivot_details"]
        system_names = [variable.system_name for variable in variables]

        pivot_columns = set(
            pivot_details["index"] + pivot_details["columns"] + pivot_details["values"]
        )
        if pivot_columns.difference(system_names):
            raise DatasheetException(
                code="PIVOT_PERMISSION_ERROR",
                message="You don't have permission to view certain columns in this pivot view. Please reach out to your admin.",
            )

        data = self.sb_class.fetch_datasheet_data_latest(
            limit=None,
            offset=None,
            as_dataframe=True,
        )
        total_column_count = data[pivot_details["columns"][0]].nunique() * len(
            pivot_details["aggfunc"]
        )  # column will always be an array of one element always
        if total_column_count > MAX_COLUMN_LIMIT:
            raise DatasheetException(
                code="DATA_PIVOT_MEMORY_ERROR",
                message=f"Cannot apply pivot on a datasheet with more than {MAX_COLUMN_LIMIT} columns",
            )
        var_datatype_map = {var.system_name: var.data_type_id for var in variables}  # type: ignore

        data = pivot_and_sort_data(data, pivot_details, var_datatype_map)
        data.columns = [
            "__".join([str(c) for c in c_list]) for c_list in data.columns.values
        ]
        pivot_columns = data.columns.tolist()  # type: ignore
        index_columns = data.index.names
        pivoted_columns = {
            "index_columns": index_columns,
            "pivot_columns": pivot_columns,
        }
        data.reset_index(inplace=True)
        whole_data_len = len(data.index)  # To get the data length of pivoted data

        # apply pagination on top of the data.
        # This will slice the part from whole data, using the page size and page number.
        if (
            request_model.page_size is not None
            and request_model.page_number is not None
        ):
            page_number = request_model.page_number - 1
            data = data[
                (request_model.page_size * page_number) : (
                    request_model.page_size * page_number
                )
                + request_model.page_size
            ]

        # Now converting this sliced dataFrame to list of dicts
        # and removing nan and nat from the dataFrame to make it readable for frontend.

        data_to_return = None
        if isinstance(data, pd.DataFrame):  # will be dataframe only, always
            data_to_return = remove_nan_nat(data)
            data_to_return = data_to_return.to_dict("records")
        replace_nan_nat(data_to_return)
        del data
        gc.collect()
        return {
            "data": data_to_return,
            "data_count": whole_data_len,
            "pivot_columns": pivoted_columns,
        }
