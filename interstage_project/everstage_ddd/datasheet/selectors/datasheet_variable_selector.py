from typing import List, Optional

from django.db.models.query import QuerySet
from django.utils import timezone

from common.data_selectors import BiTemporalSelector
from everstage_ddd.datasheet.exceptions import DatasheetException
from everstage_ddd.datasheet.models import DatasheetVariable, DatasheetVariableTemp
from spm.services.datasheet_permission_services import (
    get_all_hidden_columns_in_a_datasheet_for_user,
)
from spm.services.rbac_services import does_user_have_databook_manage_permission


class DatasheetVariableSelector(BiTemporalSelector):
    def __init__(self, client_id, datasheet_id=None):
        self.model = DatasheetVariable
        self.datasheet_id = datasheet_id
        super().__init__(
            client_id=client_id,
            model=self.model,
        )

    def get_variables(self) -> QuerySet:
        return self.client_kd_deleted_aware().filter(datasheet_id=self.datasheet_id)

    def get_selected_variables(self) -> QuerySet:
        return self.client_kd_deleted_aware().filter(
            datasheet_id=self.datasheet_id, is_selected=True
        )

    def get_selected_variables_with_permission(self, logged_in_user_email):

        if does_user_have_databook_manage_permission(
            self.client_id, logged_in_user_email
        ):
            return self.get_selected_variables()

        hidden_columns = get_all_hidden_columns_in_a_datasheet_for_user(
            self.client_id, self.datasheet_id, logged_in_user_email
        )
        return self.get_selected_variables().exclude(system_name__in=hidden_columns)

    def get_variable(self, variable_id) -> DatasheetVariable:
        return (
            self.client_kd_deleted_aware()
            .filter(datasheet_id=self.datasheet_id, variable_id=variable_id)
            .first()
        )

    def get_ordered_variables(
        self, ordered_columns, logged_in_user_email
    ) -> list[DatasheetVariable]:

        ordered_columns_map = {
            column: index for index, column in enumerate(ordered_columns)
        }
        sorted_variables = sorted(
            self.get_selected_variables_with_permission(logged_in_user_email),
            key=lambda variable: ordered_columns_map.get(
                variable.system_name, float("inf")
            ),
        )
        return sorted_variables

    def get_variables_for_db_ds(
        self, databook_id, datasheet_ids, as_dicts=True, fetch_only_selected=False
    ):
        datasheet_ids = (
            datasheet_ids if isinstance(datasheet_ids, list) else [datasheet_ids]
        )
        qs = self.client_kd_deleted_aware().filter(
            databook_id=databook_id, datasheet_id__in=datasheet_ids
        )
        if fetch_only_selected:
            qs = qs.filter(is_selected=True)
        return list(qs.values()) if as_dicts else list(qs)

    def get_variables_for_datasheet(self, datasheet_id) -> QuerySet:
        """
        Retrieve all variables for a specific datasheet.

        Args:
            datasheet_id (UUID): The ID of the datasheet.

        Returns:
            QuerySet: A queryset of DatasheetVariable objects for the given datasheet.
        """
        return self.client_kd_deleted_aware().filter(datasheet_id=datasheet_id)

    def get_variables_for_datasheets(
        self, datasheet_ids: List[str], projection: Optional[List[str]] = None
    ) -> List:
        qs = self.client_kd_deleted_aware().filter(datasheet_id__in=datasheet_ids)
        qs = qs.values(*(projection if projection else []))
        return list(qs)

    def save_validated_variables(self):
        variables = list(
            DatasheetVariableTemp.objects.filter(
                client_id=self.client_id, datasheet_id=self.datasheet_id
            ).values()
        )
        date = timezone.now()
        self.get_variables().update(knowledge_end_date=date)
        self.model.objects.bulk_create(
            [
                self.model(**variable, knowledge_begin_date=date)
                for variable in variables
            ]
        )
        DatasheetVariableTemp.objects.filter(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        ).delete()

    def get_ds_var_system_name_and_dtype_superset(
        self, databook_id, datasheet_id, knowledge_date
    ):
        if knowledge_date:
            qs = self.client_kd_deleted_aware(knowledge_date)
        else:
            qs = self.client_kd_deleted_aware()
        qs = qs.filter(databook_id=databook_id, datasheet_id=datasheet_id)
        return tuple(qs.values("system_name", "data_type_id", "display_name"))

    def create_ds_variables(self, records):  # each record is a dict
        datasheet_var_records = []
        if isinstance(records, list):
            for record in records:
                if isinstance(record, dict):
                    record = DatasheetVariable(**record)  # noqa: PLW2901
                record.pk = None
                datasheet_var_records.append(record)
        self.model.objects.bulk_create(datasheet_var_records, batch_size=1000)

    def bulk_update_variables(self, variables, fields_to_update):
        """
        Bulk update the given DatasheetVariable objects or dictionaries.

        Args:
            variables (list): List of DatasheetVariable objects or dictionaries to update
            fields_to_update (list): List of field names to update

        Returns:
            int: The number of objects updated
        """
        datasheet_variables = []
        for var in variables:
            if isinstance(var, dict):
                datasheet_var = DatasheetVariable(**var)
                datasheet_variables.append(datasheet_var)
            elif isinstance(var, DatasheetVariable):
                datasheet_variables.append(var)
            else:
                raise DatasheetException(
                    code="UNSUPPORTED_VARIABLE_TYPE",
                    payload={"datasheet_id": self.datasheet_id},
                    message=f"Unsupported type for variable: {var}",
                )

        return self.model.objects.bulk_update(
            datasheet_variables, fields_to_update, batch_size=1000
        )
