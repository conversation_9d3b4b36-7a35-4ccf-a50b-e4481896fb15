import logging
import time
import uuid
from collections import defaultdict
from copy import deepcopy
from datetime import datetime
from typing import List, Optional

import pydash
from django.db.models import Case, Min, Value, When
from django.db.models.functions import Cast, Lower
from django.forms.models import model_to_dict
from django.utils import timezone

import everstage_ddd.datasheet.selectors as datasheet_selectors
import everstage_ddd.datasheet.selectors.databook_selector as databook_selectors
from commission_engine.accessors.client_accessor import can_run_auto_enrich_report
from commission_engine.accessors.custom_object_accessor import (
    CustomObjectVariableAccessor,
)
from commission_engine.accessors.databook_accessor import DatasheetPermissionsAccessor
from commission_engine.accessors.etl_housekeeping_accessor import (
    DatabookETLStatusReaderAccessor,
    ETLSyncStatusReaderAccessor,
)
from commission_engine.accessors.ever_object_accessor import EverObjectVariableAccessor
from commission_engine.serializers.databook_serializers import (
    DataSheetPermissionBulkSerializer,
)
from commission_engine.services.datasheet_data_services.variable_extractor import (
    VariableExtractor,
)
from commission_engine.services.ever_object_service import (
    get_all_objects as get_all_report_objects,
)
from commission_engine.services.ever_object_service import get_report_object_variable
from commission_engine.services.expression_designer import CriteriaTypes, converter
from commission_engine.utils.criteria_calculator_utils import create_ast
from commission_engine.utils.general_data import COMMISSION_TYPE, ETL_ACTIVITY
from commission_engine.utils.general_utils import log_time_taken
from common.data_selectors.bi_temporal_selector import BiTemporalSelector
from everstage_ddd.datasheet.data_models import (
    Columns,
    DatasheetSourceOptions,
    DatasheetVariablesList,
    DatasheetVariablesListResponse,
)
from everstage_ddd.datasheet.datasheet_graph import DataSheetGraph
from everstage_ddd.datasheet.enums import (
    SAME_SOURCE_VAR,
    DataOrigin,
    DatasheetSourceType,
    TransformationType,
)
from everstage_ddd.datasheet.exceptions import DatasheetException
from everstage_ddd.datasheet.models import Datasheet, DatasheetVariableTemp
from everstage_ddd.datasheet.selectors.datasheet_view_pivot_selector import (
    DatasheetViewPivotSelector,
)
from everstage_ddd.workflow_builder.service.workflow_service import (
    delete_datasheet_permissions_in_cache,
)
from interstage_project.db.models import EsCharField, EsIntegerField
from spm.services.commission_plan_services import (
    get_commission_plan_details_for_datasheet_id,
    get_criteria_data_for_plans,
    get_published_commission_plans_for_payee,
    is_datasheets_used_in_any_commission_plan,
    is_datasheets_used_in_any_settlement_rule,
)
from spm.services.config_services.employee_services import get_employee_full_name
from spm.services.custom_object_services.co_permission_services import (
    get_objects_excluded_for_user,
)
from spm.services.custom_object_services.custom_object_service import (
    get_all_objects as get_all_custom_objects,
)

# TODO: Rewrite the logic and avoid private import functions
from spm.services.databook_services import (
    _check_if_transformations_changed,
    _has_config_changed,
)
from superset.services.dashboard_services import get_dashboard_details_for_datasheet_id

logger = logging.getLogger(__name__)

# ruff: noqa: PLR0911
# ruff: noqa: PLR0912
# ruff: noqa: PLR0915


class DatasheetSelector(BiTemporalSelector):
    def __init__(self, client_id, datasheet_id=None):
        self.datasheet_id = datasheet_id
        self.model = Datasheet
        super().__init__(
            client_id=client_id,
            model=self.model,
        )

    def get_datasheets_for_databooks(self, databook_ids):
        qs = self.client_kd_deleted_aware()
        if isinstance(databook_ids, list):
            qs = qs.filter(databook_id__in=databook_ids)
        else:
            qs = qs.filter(databook_id=databook_ids)
        return list(qs)

    def get_datasheets(
        self, datasheet_ids: List[str], projection: Optional[List[str]] = None
    ) -> List:
        projection = projection or []
        qs = self.client_kd_deleted_aware().filter(datasheet_id__in=datasheet_ids)
        qs = qs.values(*projection)
        return list(qs)

    def get_datasheet(self, as_dict=False, fields=None):  # noqa: FBT002
        if fields:
            datasheet = (
                self.client_kd_deleted_aware()
                .values(*fields)
                .get(datasheet_id=self.datasheet_id)
            )
        else:
            datasheet = self.client_kd_deleted_aware().get(
                datasheet_id=self.datasheet_id
            )
            if as_dict:
                return model_to_dict(datasheet)

        return datasheet

    def datasheet_exists(self):
        return (
            self.client_kd_deleted_aware()
            .filter(datasheet_id=self.datasheet_id)
            .exists()
        )

    def get_primary_key(self, datasheet_id):
        return (
            self.client_kd_deleted_aware()
            .filter(datasheet_id=datasheet_id)
            .values("primary_key")
            .get()
        )

    @log_time_taken()
    def get_all_datasheet_names(self):
        all_datasheets = list(self.client_kd_deleted_aware().values("name"))

        return [ds["name"].lower() for ds in all_datasheets]

    @log_time_taken()
    def get_all_datasheet_names_in_databook(self, databook_id):
        all_datasheets = list(
            self.client_kd_deleted_aware()
            .filter(databook_id=databook_id)
            .values("name")
        )
        return [ds["name"].lower() for ds in all_datasheets]

    @log_time_taken()
    def get_all_datasheets(self):
        return self.client_kd_deleted_aware()

    @log_time_taken()
    def datasheet_bulk_create(self, datasheets):
        self.model.objects.bulk_create(datasheets, batch_size=100)

    @log_time_taken()
    def update_datasheet(self, data):
        # TODO: Refactor this method and move to services
        record = self.get_datasheet()
        databook_id = record.databook_id
        data["transformation_spec"] = self._get_and_flush_validated_transformations()

        if data.get("source_type", "") == DatasheetSourceType.DATASHEET.value:
            data["source_databook_id"] = (
                self.client_kd_deleted_aware()
                .get(datasheet_id=data["source_id"])
                .databook_id
            )

        # if any invalid transformation is present, then raise an error
        if any(
            not transformation["is_valid"]
            for transformation in data["transformation_spec"]
        ):
            raise DatasheetException(
                code="INVALID_TRANSFORMATION",
                message="Some of transformation are not valid, validate before saving it",
            )

        data["is_config_changed"] = False
        if _check_if_transformations_changed(
            {"transformation_spec": record.transformation_spec},
            {
                "transformation_spec": data["transformation_spec"],
            },
        ):
            data["is_config_changed"] = True

        datasheet_variable_selector = datasheet_selectors.DatasheetVariableSelector(
            client_id=self.client_id,
            datasheet_id=self.datasheet_id,
        )

        existing_vars = list(datasheet_variable_selector.get_variables().values())
        new_vars = DatasheetVariablesList(
            DatasheetVariableTemp.objects.filter(
                client_id=self.client_id, datasheet_id=self.datasheet_id
            )
        ).model_dump()

        if not new_vars:
            raise DatasheetException(
                code="STALE_UPDATE_DETECTED",
                message="Current update is stale, please refresh the page and try again",
            )

        # Update system names in filter data before saving
        self._update_system_names_in_view_filters(existing_vars, new_vars)

        # Update system names in datasheet permissions before saving
        self._update_system_names_in_ds_permissions(existing_vars, new_vars)

        from everstage_ddd.datasheet.services.datasheet_service import (
            compute_datasheet_origin,
        )

        data["data_origin"] = compute_datasheet_origin(
            client_id=self.client_id,
            source_data_origin=record.data_origin,
            transformations=data["transformation_spec"],
            variables=new_vars,
        )

        self._validate_datasheet_data_origin(data["data_origin"])

        data["primary_key"] = [
            var["system_name"] for var in new_vars if var["is_primary"]
        ]
        (
            data["is_pk_modified"],
            is_config_changed,
            data["is_calc_field_changed"],
        ) = _has_config_changed(existing_vars=existing_vars, request_vars=new_vars)
        data["is_config_changed"] = data["is_config_changed"] or is_config_changed
        ordered_columns = [
            var["system_name"] for var in new_vars if var["is_selected"] is True
        ]
        data["ordered_columns"] = ordered_columns
        self.bitemporal_update(record_identifier=record.pk, data=data)
        datasheet_variable_selector.save_validated_variables()
        modified_variables = self._get_modified_variables(existing_vars, new_vars)
        self.update_datasheet_view_filters(databook_id, modified_variables)

    @log_time_taken()
    def _get_modified_variables(self, existing_vars, new_vars) -> set:
        new_variables_set = {
            (var["system_name"], var["display_name"]) for var in new_vars
        }
        existing_variables_set = {
            (var["system_name"], var["display_name"]) for var in existing_vars
        }
        return new_variables_set.difference(existing_variables_set)

    @log_time_taken()
    def update_datasheet_view_filters(self, databook_id, modified_variables):
        """
        Update datasheet view filters based on modified variables.

        This method updates the filter data for all views that use any of the modified variables.
        It optimizes the process by only updating filters that are affected by the changes.

        Args:
            databook_id (UUID): The ID of the databook.
            modified_variables (set): Set of tuples containing (system_name, display_name) of modified variables.
        """
        if not modified_variables:
            return

        view_selector = datasheet_selectors.DatasheetViewSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        )
        modified_system_names = {system_name for system_name, _ in modified_variables}
        views = view_selector.get_datasheet_views()

        for view in views:
            # Skip views without filters
            if not view.filter_id:
                continue

            filter_data = view_selector.get_filter_data(view.filter_id) or []

            if not filter_data:
                continue

            # Create AST is modifying the filter_data, so we need to make a copy of it
            filter_data_copy = deepcopy(filter_data)
            ast = create_ast(filter_data_copy)
            used_system_names = set(VariableExtractor().get_variables_used(ast["ast"]))

            # Skip if none of the modified variables are used in this view filter
            if not modified_system_names.intersection(used_system_names):
                continue

            updated_filter_data = self._update_view_filter_tokens(
                databook_id=databook_id, infix=filter_data
            )
            view_filter_selector = datasheet_selectors.DatasheetViewFilterSelector(
                client_id=self.client_id, filter_id=view.filter_id
            )
            view_filter_selector.update_view_filter_details(
                {"filter_data": updated_filter_data}
            )

    @log_time_taken()
    def _update_view_filter_tokens(
        self, databook_id: uuid.UUID, infix: list[dict]
    ) -> list[dict]:
        """
        Convert the infix expression to v2 format and update the tokens.

        This function takes an infix expression in v1 format and converts it to v2 format,
        updating the tokens in the process. It uses the converter function to perform the conversion.

        Args:
            databook_id (UUID): The ID of the databook. Note: This parameter is not used in the function body.
            infix (list[dict]): The infix expression in v1 format to be updated.

        Returns:
            list[dict]: The updated infix expression in v2 format.
        """
        databook_id = self.get_datasheet().databook_id
        infix_expression = {
            "ast": infix,
            "databook_id": databook_id,
            "datasheet_id": self.datasheet_id,
            "type": CriteriaTypes.SIMPLE.value,
        }
        updated_infix_expression = converter(
            client_id=self.client_id,
            expression=infix_expression,
            to_v2=True,
        )

        return updated_infix_expression.get("ast", [])

    def generate_system_name_changes_map(
        self, existing_vars: list, new_vars: list
    ) -> dict:
        """
        Generate a mapping of variable_id to old and new system_names.

        Args:
            existing_vars (list): List of existing variables before the update
            new_vars (list): List of new variables after the update

        Returns:
            dict: A mapping of variable_id to old and new system_names

        Eg:
        {
            "1": {
                "old_system_name": "old_name",
                "new_system_name": "new_name",
                "display_name": "new_name"
            },
            "2": {
                "old_system_name": "old_name",
                "new_system_name": "new_name",
                "display_name": "new_name"
            }
        }
        """
        # Create mapping from variable_id to old and new system_names
        system_name_changes = {}
        existing_var_map = {str(var["variable_id"]): var for var in existing_vars}
        new_var_map = {str(var["variable_id"]): var for var in new_vars}

        # Find variables whose system_names have changed
        for var_id, new_var in new_var_map.items():
            if var_id in existing_var_map:
                old_var = existing_var_map[var_id]
                if old_var["system_name"] != new_var["system_name"]:
                    system_name_changes[var_id] = {
                        "old_system_name": old_var["system_name"],
                        "new_system_name": new_var["system_name"],
                        "display_name": new_var["display_name"],
                    }

        return system_name_changes

    def _update_system_names_in_ds_permissions(
        self, existing_vars: list, new_vars: list
    ):
        # Create mapping from variable_id to old and new system_names
        system_name_changes = self.generate_system_name_changes_map(
            existing_vars, new_vars
        )

        if not system_name_changes:
            return

        databook_id = self.get_datasheet().databook_id
        permission_accessor = DatasheetPermissionsAccessor(client_id=self.client_id)
        # Get all permissions for this datasheet
        datasheet_permissions = permission_accessor.databook_datasheet_aware(
            databook_id=databook_id, datasheet_id=self.datasheet_id
        )

        # convert datasheet_permissions to list of dict
        datasheet_permissions = [
            {
                "permission_set_id": permission.permission_set_id,
                "permission_set_name": permission.permission_set_name,
                "filter_list": permission.filter_list,
                "columns_to_be_hidden": permission.columns_to_be_hidden,
                "additional_details": permission.additional_details,
                "client_id": self.client_id,
                "client": self.client_id,
                "databook_id": databook_id,
                "datasheet_id": self.datasheet_id,
                "knowledge_begin_date": timezone.now(),
            }
            for permission in datasheet_permissions
        ]

        if not datasheet_permissions:
            return

        for permission in datasheet_permissions:
            variable_id_map = permission.get("additional_details", {}).get(
                "variable_id_map", {}
            )
            new_variable_id_map = {}

            if permission.get("filter_list"):
                for filter in permission["filter_list"]:
                    used_column = filter["col_name"]
                    variable_id_of_used_column = variable_id_map.get(used_column)
                    if variable_id_of_used_column:
                        new_system_name = system_name_changes.get(
                            variable_id_of_used_column, {}
                        ).get("new_system_name", used_column)

                        filter["col_name"] = new_system_name
                        new_variable_id_map[new_system_name] = (
                            variable_id_of_used_column
                        )
                    else:
                        new_variable_id_map[used_column] = variable_id_of_used_column

            if permission.get("columns_to_be_hidden"):
                for index, column in enumerate(permission["columns_to_be_hidden"]):
                    variable_id = variable_id_map.get(column)
                    if variable_id:
                        new_system_name = system_name_changes.get(variable_id, {}).get(
                            "new_system_name", column
                        )
                        permission["columns_to_be_hidden"][index] = new_system_name
                        new_variable_id_map[new_system_name] = variable_id

            permission["additional_details"]["variable_id_map"] = new_variable_id_map

        permission_accessor.invalidate_permissions(databook_id, self.datasheet_id)

        permissions_data = DataSheetPermissionBulkSerializer(
            data=datasheet_permissions, many=True
        )

        if permissions_data.is_valid():
            permission_accessor.persist_permissions(permissions_data)

            # Delete the cache for the datasheet as this is used in workflow and should be updated for every user
            delete_datasheet_permissions_in_cache(
                self.client_id, str(databook_id), str(self.datasheet_id)
            )

        logger.info(
            f"Updated system names in datasheet permissions for datasheet_id: {self.datasheet_id}"
        )

    def _update_system_names_in_view_filters(
        self, existing_vars: list, new_vars: list
    ) -> None:
        """
        Update system names in filter data when variables are modified.

        This method finds all views with filters that use variables whose system_names have changed,
        and updates the filter data to use the new system_names using variable_ids.

        Args:
            existing_vars (list): List of existing variables before the update
            new_vars (list): List of new variables after the update
        """
        # Create mapping from variable_id to old and new system_names
        system_name_changes = self.generate_system_name_changes_map(
            existing_vars, new_vars
        )

        # If no system_names have changed, no need to update filters
        if not system_name_changes:
            return

        # Get all views for this datasheet
        view_selector = datasheet_selectors.DatasheetViewSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        )
        views = view_selector.get_datasheet_views()

        # For each view with a filter, check if it uses any of the changed variables
        for view in views:
            # handle pivot view
            if view.pivot_id:
                pivot_details = view_selector.get_pivot_data(view.pivot_id)
                pivot_variable_id_map = pivot_details.get("variable_id_map", None)
                if pivot_variable_id_map:
                    updated = self._transform_pivot_view_system_names(
                        pivot_details, system_name_changes
                    )

                    # If the pivot data was updated, save it
                    if updated and self.datasheet_id is not None:
                        DatasheetViewPivotSelector(
                            client_id=self.client_id,
                        ).create_view_pivot(
                            pivot_details={"pivot_data": pivot_details},
                            view_id=view.view_id,
                            datasheet_id=self.datasheet_id,
                        )

            # handle filter view
            if not view.filter_id:
                continue

            filter_data = view_selector.get_filter_data(view.filter_id)
            if not filter_data:
                continue
            # Process the filter data using DatasheetExpressionBoxUtils
            databook_id = self.get_datasheet().databook_id
            updated = self._transform_filter_system_names(
                databook_id, filter_data, new_vars, system_name_changes
            )

            # If the filter data was updated, save it
            if updated:
                view_filter_selector = datasheet_selectors.DatasheetViewFilterSelector(
                    client_id=self.client_id, filter_id=view.filter_id
                )
                view_filter_selector.update_view_filter_details(
                    {"filter_data": filter_data}
                )

                logger.info(
                    f"Updated system names in datasheet permissions for datasheet_id: {self.datasheet_id}"
                )

    def _transform_filter_system_names(
        self, databook_id, filter_data, variables, system_name_changes
    ):
        """
        Transform system names in filter data using DatasheetExpressionBoxUtils.

        Args:
            databook_id (UUID): The ID of the databook
            filter_data (list): The filter data to transform - this is modified in-place
            variables (list): List of variables
            system_name_changes (dict): Dictionary mapping variable_ids to old and new system_names

        Returns:
            bool: True if any changes were made, False otherwise
        """
        # Check if any of the changed system_names are used in the filter
        filter_data_copy = deepcopy(filter_data)
        ast = create_ast(filter_data_copy)
        used_system_names = set(VariableExtractor().get_variables_used(ast["ast"]))

        # Check if any of the changed system_names are used in this filter
        old_system_names = {
            change["old_system_name"] for change in system_name_changes.values()
        }
        if not old_system_names.intersection(used_system_names):
            return False

        # Create a meta_data structure for DatasheetExpressionBoxUtils
        # Note: filter_data is passed by reference, so changes to meta_data["infix"] will modify filter_data
        meta_data = {"infix": filter_data}

        # Use DatasheetExpressionBoxUtils to transform system_names
        from everstage_ddd.datasheet.helpers.datasheet_expression_box_utils import (
            DatasheetExpressionBoxUtils,
        )

        expression_box_utils = DatasheetExpressionBoxUtils(
            client_id=self.client_id,
            databook_id=str(databook_id),
            datasheet_id=str(self.datasheet_id),
            meta_data=meta_data,
            variables=variables,
        )

        # Transform system_names in the filter data
        # This modifies meta_data["infix"] in-place, which is a reference to filter_data
        expression_box_utils.transform_system_name()

        # At this point, filter_data has been modified in-place and contains the updated system names
        # The caller will use this modified filter_data to update the database
        return True

    def _transform_pivot_view_system_names(self, pivot_data, system_name_changes):
        """
        Transform system names in pivot data based on variable IDs.

        This method updates the system names in pivot data when variables are modified.
        It uses the variable_id_map in the pivot data to identify which system names need to be updated.

        Args:
            pivot_data (dict): The pivot data to transform - this is modified in-place
            system_name_changes (dict): Dictionary mapping variable_ids to old and new system_names

        Returns:
            bool: True if any changes were made, False otherwise

        Example pivot details:
        {
            "index": [
                "active_spiffs",
                "active_quota_categories"
            ],
            "values": [
                "created_date"
            ],
            "aggfunc": {
                "created_date": "nunique"
            },
            "columns": [
                "payee_or_manager"
            ],
            "fill_value": "",
            "variable_id_map": {
                "created_date": "8dc03374-efad-4fa2-b19b-44f2553e9f3b",
                "active_spiffs": "ef297205-4cc4-4133-b975-e9f5d3143cb1",
                "payee_or_manager": "30eb63da-9dba-44d3-9f47-6712f9eda0e6",
                "active_quota_categories": "8282f496-50bb-4902-8d3e-e247ab47c3b1"
            }
        }
        """
        if not pivot_data or not system_name_changes:
            return False

        # Check if variable_id_map exists in pivot_data
        if "variable_id_map" not in pivot_data:
            logger.warning("No variable_id_map found in pivot data")
            return False

        variable_id_map = pivot_data.get("variable_id_map", {})
        updated = False

        # Create a reverse mapping from variable_id to system_name
        reverse_map = {}

        # Process all variable IDs outside of the loop to avoid try-except in loop
        for system_name, variable_id in variable_id_map.items():
            var_id = str(variable_id)
            if var_id in system_name_changes:
                change_info = system_name_changes[var_id]
                old_system_name = change_info["old_system_name"]
                new_system_name = change_info["new_system_name"]
                # Only update if the system name actually changed
                if (
                    system_name == old_system_name
                    and old_system_name != new_system_name
                ):
                    reverse_map[system_name] = new_system_name
                    updated = True

        # If no changes needed, return early
        if not updated:
            return False

        # Update system names in all relevant pivot data sections
        for section in ["index", "values", "columns"]:
            if section in pivot_data and isinstance(pivot_data[section], list):
                for i, system_name in enumerate(pivot_data[section]):
                    if system_name in reverse_map:
                        pivot_data[section][i] = reverse_map[system_name]

        # Update system names in aggfunc dictionary
        if "aggfunc" in pivot_data and isinstance(pivot_data["aggfunc"], dict):
            new_aggfunc = {}
            for system_name, agg_func in pivot_data["aggfunc"].items():
                if system_name in reverse_map:
                    new_aggfunc[reverse_map[system_name]] = agg_func
                else:
                    new_aggfunc[system_name] = agg_func
            pivot_data["aggfunc"] = new_aggfunc

        # Create a new variable_id_map with updated system names
        new_variable_id_map = {}
        for system_name, variable_id in variable_id_map.items():
            if system_name in reverse_map:
                new_variable_id_map[reverse_map[system_name]] = variable_id
            else:
                new_variable_id_map[system_name] = variable_id
        pivot_data["variable_id_map"] = new_variable_id_map

        return True

    @log_time_taken()
    def _validate_datasheet_data_origin(self, data_origin):
        if data_origin in [
            DataOrigin.COMMISSION_OBJECT.value,
            DataOrigin.FORECAST_OBJECT.value,
            DataOrigin.INTER_FORECAST_OBJECT.value,
        ] and (
            is_datasheets_used_in_any_commission_plan(
                client_id=self.client_id,
                datasheet_ids=[self.datasheet_id],
                commission_type=COMMISSION_TYPE.COMMISSION,
            )
            or is_datasheets_used_in_any_settlement_rule(
                client_id=self.client_id, datasheet_ids=[self.datasheet_id]
            )
            or is_datasheets_used_in_any_commission_plan(
                client_id=self.client_id,
                datasheet_ids=[self.datasheet_id],
                commission_type=COMMISSION_TYPE.FORECAST,
            )
        ):
            raise DatasheetException(
                code="DATASHEET_USED_IN_COMMISSION_PLAN",
                message="As Datasheet is used in commission plan or settlement rule or forecast plan, cannot use datasheet as source/transformation source with commission data origin",
            )

    def delete_datasheet(self, invalidation_date: datetime | None = None):
        if not invalidation_date:
            invalidation_date = timezone.now()
        record = self.get_datasheet()
        return self.bitemporal_delete(
            record_identifier=record.pk, invalidation_date=invalidation_date
        )

    @log_time_taken()
    def get_all_datasheets_name_databook_id_for_client(self, databook_ids=None):
        if not databook_ids:
            return self.client_kd_deleted_aware().values(
                "databook_id",
                "datasheet_id",
                "name",
                "knowledge_begin_date",
                "is_datasheet_generated",
            )
        return (
            self.client_kd_deleted_aware()
            .filter(databook_id__in=databook_ids)
            .values(
                "databook_id",
                "datasheet_id",
                "name",
                "knowledge_begin_date",
                "is_datasheet_generated",
            )
        )

    @log_time_taken()
    def get_datasheet_names_by_datasheet_ids(self, datasheet_ids):
        return (
            self.client_kd_deleted_aware()
            .filter(datasheet_id__in=datasheet_ids)
            .values(
                "datasheet_id",
                "name",
                "knowledge_begin_date",
                "is_datasheet_generated",
                "databook_id",
            )
        )

    @log_time_taken()
    def get_databook_groups(self, user_id: str):
        """
        Get Databook groups

        Example output
        [
            {
                "id": UUID("e0b1192d-8348-44a9-a9d5-a2f860ddd030"),
                "name": "Approvals-DB",
                "datasheets": [
                    {
                        "databook_id": UUID("e0b1192d-8348-44a9-a9d5-a2f860ddd030"),
                        "datasheet_id": UUID("a7c0f882-45ee-4f69-bfc1-612bec81ba94"),
                        "name": "Approvals-DS",
                    },
                    {
                        "databook_id": UUID("e0b1192d-8348-44a9-a9d5-a2f860ddd030"),
                        "datasheet_id": UUID("b2459f51-8b04-40a1-afcd-791e7c8c1f8a"),
                        "name": "Global",
                    },
                ],
            },
        ]
        """
        databooks = databook_selectors.DatabookSelector(
            client_id=self.client_id
        ).get_active_databooks()

        return self._construct_databook_groups(databooks, user_id)

    @log_time_taken()
    def get_archived_databook_groups(self, user_id: str):
        databooks = databook_selectors.DatabookSelector(
            client_id=self.client_id
        ).get_archived_databooks()

        return self._construct_databook_groups(databooks, user_id)

    @log_time_taken()
    def get_commission_plan_groups(self, login_user_id: str):
        """
        Get Commission Plan Groups

        Example output
        [
            {
                "id": "cd1af239-81df-472d-9a31-44315e20f21a",
                "name": "Approvals",
                "datasheets": [
                    {
                        "datasheet_id": "a7c0f882-45ee-4f69-bfc1-612bec81ba94",
                        "name": "Approvals-DS",
                        "last_generated_at": "2023-05-01T12:00:00Z"
                    }
                ]
            },
        ]
        """
        # Fetch all commission plans for the client
        plans_list = get_published_commission_plans_for_payee(
            self.client_id, login_user_id
        )

        # Extract plan_ids for filtering criteria in the next step
        plan_ids = [plan.plan_id for plan in plans_list]

        # Fetch criteria for these plan_ids
        criteria_data = get_criteria_data_for_plans(self.client_id, plan_ids)

        # Extract unique datasheet IDs
        datasheet_ids = set(item["datasheet_id"] for item in criteria_data)

        # Query Datasheet model for details using extracted datasheet IDs
        datasheets_details = self.get_datasheet_names_by_datasheet_ids(datasheet_ids)

        last_generated_at_map = datasheet_selectors.DbkdPkdMapSelector(
            self.client_id
        ).get_multiple_datasheet_last_generation_times(datasheet_ids=datasheet_ids)

        datasheet_map_with_co_permission = self.get_datasheets_map_with_co_permission(
            datasheet_ids=datasheet_ids, user_id=login_user_id
        )

        # Convert datasheet details into a lookup dictionary
        datasheets_map = {
            str(detail["datasheet_id"]): {
                **detail,
                "last_generated_at": last_generated_at_map.get(detail["datasheet_id"]),
                "does_user_has_co_permission": datasheet_map_with_co_permission[
                    str(detail["datasheet_id"])
                ],
            }
            for detail in datasheets_details  # Convert to string to match the key type
        }

        # Organizing the datasheet_ids by plan_id using a dictionary of sets to avoid duplicates
        plan_criteria_map = defaultdict(list)
        # Initialize a dictionary to track unique datasheet_ids for each plan_id
        plan_datasheets_set = defaultdict(set)

        # Iterate over the criteria data to organize it by plan_id
        for item in criteria_data:
            plan_id = item["plan_id"]
            datasheet_id = str(
                item["datasheet_id"]
            )  # Ensure datasheet_id is a string for consistent handling

            if (
                datasheet_id in datasheets_map
            ):  # Check if the datasheet details are available
                # Only append datasheet details if datasheet_id is not already processed for this plan
                if datasheet_id not in plan_datasheets_set[plan_id]:
                    plan_criteria_map[plan_id].append(datasheets_map[datasheet_id])
                    # Add datasheet_id to the set to prevent future duplication
                    plan_datasheets_set[plan_id].add(datasheet_id)
            else:
                raise ValueError(  # noqa:TRY003
                    f"Datasheet details not found for datasheet_id: {datasheet_id}"
                )

        # Combine the plans with their corresponding datasheets into the final structure
        commission_plans = []
        for plan in plans_list:
            plan_id = plan.plan_id
            commission_plans.append(
                {
                    "id": plan_id,
                    "name": plan.plan_name,
                    "datasheets": plan_criteria_map.get(plan_id, []),
                }
            )

        return commission_plans

    @log_time_taken()
    def get_datasheet_details(self, logged_in_user):
        record = self.get_datasheet(as_dict=True)

        common_details = self._get_common_datasheet_details(record)
        is_stale = common_details["is_stale"]

        sb_selector = datasheet_selectors.StormBreakerSelector(
            client_id=self.client_id,
            databook_id=record["databook_id"],
            datasheet_id=record["datasheet_id"],
            logged_in_user_email=logged_in_user,
        )
        total_records = sb_selector.get_records_count()

        last_generated_at = datasheet_selectors.DbkdPkdMapSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        ).get_datasheet_last_generation_time()

        commission_plan_details = get_commission_plan_details_for_datasheet_id(
            self.client_id, self.datasheet_id
        )

        dashboard_details = get_dashboard_details_for_datasheet_id(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        )

        databook_record = databook_selectors.DatabookSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        ).get_databook(record["databook_id"])
        databook_name = databook_record.name

        total_adjustments = sb_selector.get_adjusted_records_count()

        view_details = self.get_view_details(sb_selector, record["databook_id"])

        updated_by = get_employee_full_name(
            self.client_id, record["additional_details"].get("updated_by")
        )

        creation_details = self._get_creation_details()
        is_pinned = datasheet_selectors.DatasheetPinSelector(
            client_id=self.client_id
        ).is_datasheet_pinned(self.datasheet_id)

        return (
            record
            | {
                "total_records": total_records,
                "last_updated_at": record["knowledge_begin_date"],
                "last_generated_at": last_generated_at,
                "is_stale": is_stale["is_datasheet_stale"],
                "is_report_data_stale": is_stale["is_report_data_stale"],
                "total_connected_commission_plans": len(commission_plan_details),
                "views": view_details,
                "total_adjustments": total_adjustments,
                "variables": common_details["variables"],
                "additional_source_variables": common_details[
                    "additional_source_variables"
                ],
                "client_id": self.client_id,
                "updated_by": updated_by,
                "is_active_sync_present": common_details["is_active_sync_present"],
                "is_pinned": is_pinned,
                "databook_name": databook_name,
                "is_archived": databook_record.is_archived,
            }
            | self._build_dependency_sheet_details()
            | {"commission_plan_details": commission_plan_details}
            | {"dashboard_details": dashboard_details}
            | creation_details
        )

    @log_time_taken()
    def _get_common_datasheet_details(self, record):
        datasheet_graph = DataSheetGraph(
            client_id=self.client_id,
            include_stale_information_query=True,
        )

        is_stale = datasheet_graph.is_datasheet_data_stale(
            datasheet_id=str(record["datasheet_id"])
        )

        is_active_sync_present = False

        if is_stale.get("is_datasheet_stale") or is_stale.get("is_report_data_stale"):
            is_active_sync_present = self._is_active_sync_present(
                client_id=self.client_id,
                databook_id=record["databook_id"],
                datasheet_id=record["datasheet_id"],
            )

        variables = datasheet_selectors.DatasheetVariableSelector(
            client_id=self.client_id,
            datasheet_id=self.datasheet_id,
        ).get_variables()

        additional_source_variables = self.get_additional_source_variables(
            record, variables
        )

        # remove unselected variable that are not present in parent sheet
        variables = [
            variable
            for variable in variables
            if not self._can_remove_variable_in_sheet(variable)
        ]

        variables = self._update_variables_primary_key(variables, record)

        from everstage_ddd.datasheet.services.datasheet_service import (
            fetch_source_name_of_variable,
        )

        source_name = fetch_source_name_of_variable(
            client_id=self.client_id,
            source_id=record["source_id"],
            source_type=record["source_type"],
        )

        if can_run_auto_enrich_report(self.client_id):
            variables = self.update_warnings_for_auto_enrich_variables(variables)

        return {
            "is_active_sync_present": is_active_sync_present,
            "is_stale": is_stale,
            "variables": variables,
            "additional_source_variables": additional_source_variables,
            "source_name": source_name,
        }

    @log_time_taken()
    def get_datasheet_details_for_edit(self):
        record = self.get_datasheet(
            fields=[
                "databook_id",
                "source_databook_id",
                "datasheet_id",
                "source_type",
                "name",
                "source_id",
                "transformation_spec",
                "additional_details",
            ]
        )

        common_details = self._get_common_datasheet_details(record)

        return (
            record
            | {
                "additional_source_variables": common_details[
                    "additional_source_variables"
                ],
                "variables": common_details["variables"],
                "client_id": self.client_id,
                "is_active_sync_present": common_details["is_active_sync_present"],
                "source_name": common_details["source_name"],
            }
            | self._build_dependency_sheet_details()
        )

    @log_time_taken()
    def get_datasheet_details_for_canvas(self, logged_in_user):
        record = self.get_datasheet(
            fields=[
                "databook_id",
                "datasheet_id",
                "data_last_updated_at",
                "data_origin",
                "is_calc_field_changed",
                "is_config_changed",
                "is_datasheet_generated",
                "name",
                "source_type",
                "source_id",
                "transformation_spec",
                "additional_details",
                "knowledge_begin_date",
            ]
        )
        common_details = self._get_common_datasheet_details(record)
        is_stale = common_details["is_stale"]

        sb_selector = datasheet_selectors.StormBreakerSelector(
            client_id=self.client_id,
            databook_id=record["databook_id"],
            datasheet_id=record["datasheet_id"],
            logged_in_user_email=logged_in_user,
        )

        start_time = time.monotonic()
        last_generated_at = datasheet_selectors.DbkdPkdMapSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        ).get_datasheet_last_generation_time()
        logger.info(
            "Time taken to get last generated at: %s seconds",
            time.monotonic() - start_time,
        )

        start_time = time.monotonic()
        commission_plan_details = get_commission_plan_details_for_datasheet_id(
            self.client_id, self.datasheet_id
        )
        logger.info(
            "Time taken to get commission plan details: %s seconds",
            time.monotonic() - start_time,
        )

        start_time = time.monotonic()
        dashboard_details = get_dashboard_details_for_datasheet_id(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        )
        logger.info(
            "Time taken to get dashboard details: %s seconds",
            time.monotonic() - start_time,
        )

        start_time = time.monotonic()
        databook_record = databook_selectors.DatabookSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        ).get_databook(record["databook_id"])
        logger.info(
            "Time taken to get databook name: %s seconds", time.monotonic() - start_time
        )
        databook_name = databook_record.name

        view_details = self.get_view_details(sb_selector, record["databook_id"])

        updated_by = get_employee_full_name(
            self.client_id, record["additional_details"].get("updated_by")
        )

        creation_details = self._get_creation_details()

        return (
            record
            | {
                "total_records": 0,
                "last_updated_at": record["knowledge_begin_date"],
                "last_generated_at": last_generated_at,
                "is_stale": is_stale["is_datasheet_stale"],
                "is_report_data_stale": is_stale["is_report_data_stale"],
                "total_connected_commission_plans": len(commission_plan_details),
                "views": view_details,
                "total_adjustments": 0,
                "variables": common_details["variables"],
                "additional_source_variables": common_details[
                    "additional_source_variables"
                ],
                "client_id": self.client_id,
                "updated_by": updated_by,
                "is_active_sync_present": common_details["is_active_sync_present"],
                "databook_name": databook_name,
                "is_archived": databook_record.is_archived,
                "source_name": common_details["source_name"],
            }
            | self._build_dependency_sheet_details()
            | {"commission_plan_details": commission_plan_details}
            | {"dashboard_details": dashboard_details}
            | creation_details
        )

    @log_time_taken()
    def switch_transformation_spec_version(self, version):
        """
        Update the transformation spec of all the datasheets
        if the argument version is v1,
            then the datasheet transformation spec is updated to the value inside "additional_details" with key "transformation_spec_v1"
            and transformation_spec is updated to the value inside "additional_details" with key "transformation_spec_v1"
            clearing the value inside "additional_details" with key "transformation_spec_v2"
        else
            the datasheet transformation spec is updated to the value inside "additional_details" with key "transformation_spec_v2"
            and transformation_spec is updated to the value inside "additional_details" with key "transformation_spec_v2"
            clearing the value inside "additional_details" with key "transformation_spec_v1"
        """

        records = self.client_kd_deleted_aware()
        prev_version = "v1" if version == "v2" else "v2"

        for record in records:
            transformation_spec = record.additional_details[
                f"transformation_spec_{version}"
            ]
            record.additional_details[f"transformation_spec_{prev_version}"] = (
                record.transformation_spec
            )
            record.transformation_spec = transformation_spec
            record.additional_details.pop(f"transformation_spec_{version}")

        # bulk update the records
        self.model.objects.bulk_update(
            records, ["transformation_spec", "additional_details"]
        )

    @log_time_taken()
    def get_view_details(self, sb_selector, databook_id):
        view_selector = datasheet_selectors.DatasheetViewSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        )
        records = view_selector.get_datasheet_views().order_by("created_at")
        result = []
        for record in records:
            try:
                filter_data = view_selector.get_filter_data(record.filter_id)
                updated_filter_data = self._update_view_filter_tokens(
                    databook_id=databook_id, infix=filter_data
                )
                pivot_data = view_selector.get_pivot_data(record.pivot_id)
                total_records = sb_selector.get_records_count_with_filter_data(
                    filter_data
                )
            except Exception:
                # Since there is a lag in conversion from old to new view for some cases,
                # Temporarily skipping the view details for the datasheet for failure cases
                logger.exception(
                    "Error fetching view details for datasheet %s %s",
                    self.datasheet_id,
                    record.view_id,
                )
                continue
            result.append(
                model_to_dict(record)
                | {
                    "filter_data": updated_filter_data,
                    "pivot_data": pivot_data,
                    "total_records": total_records,
                    "hidden_columns": record.hidden_columns or [],
                    "ordered_columns": record.ordered_columns or [],
                }
            )
        return result

    def get_datasheet_source_options(self) -> DatasheetSourceOptions:
        custom_objects = get_all_custom_objects(self.client_id)
        report_objects = get_all_report_objects(self.client_id)
        databook_details = list(
            databook_selectors.DatabookSelector(client_id=self.client_id)
            .get_active_databooks()
            .order_by(Lower("name"))
            .values()
        )
        datasheet_details = self._get_datasheet_details(databook_details)
        data = {
            DatasheetSourceType.CUSTOM_OBJECT.value: custom_objects,
            DatasheetSourceType.REPORT_OBJECT.value: report_objects,
            DatasheetSourceType.DATABOOK.value: databook_details,
            DatasheetSourceType.DATASHEET.value: datasheet_details,
        }
        return DatasheetSourceOptions(**data)

    @log_time_taken()
    def _get_creation_details(self) -> dict:
        initial_record = (
            self.client_aware()
            .filter(datasheet_id=self.datasheet_id)
            .order_by("temporal_id")
            .first()
        )
        created_at = initial_record.knowledge_begin_date
        created_by = get_employee_full_name(
            self.client_id, initial_record.additional_details.get("updated_by")
        )
        return {"created_on": created_at, "created_by": created_by}

    @log_time_taken()
    def _get_datasheet_details(self, databook_details) -> list[dict]:
        databook_ids = [databook["databook_id"] for databook in databook_details]

        return list(
            self.get_all_datasheets()
            .exclude(datasheet_id=self.datasheet_id)
            .filter(databook_id__in=databook_ids)
            .order_by(Lower("name"))
            .values()
        )

    @log_time_taken()
    def _build_dependency_sheet_details(self) -> dict:
        datasheet_graph = DataSheetGraph(
            client_id=self.client_id,
            include_stale_information_query=False,
        )

        descendants_datasheets: set = datasheet_graph.descendants(
            datasheet_id=str(self.datasheet_id)
        )

        ancestors_datasheets: dict = datasheet_graph.ancestors_by_type(
            datasheet_id=str(self.datasheet_id)
        )

        return {
            "upstream_sheet_details": self._construct_url_for_datasheets(
                ancestors_datasheets["datasheet"]
            ),
            "downstream_sheet_details": self._construct_url_for_datasheets(
                descendants_datasheets
            ),
            "total_dependent_sheets": len(descendants_datasheets)
            + len(ancestors_datasheets["datasheet"]),
        }

    @log_time_taken()
    def _construct_url_for_datasheets(self, datasheets: set) -> list[dict]:
        sheet_details = [
            {
                "datasheet_id": datasheet.node_id,
                "name": datasheet.name,
                "url": f"/datasheet?id={datasheet.node_id}",
            }
            for datasheet in datasheets
        ]
        sheet_details.sort(key=lambda x: x["name"].lower())
        return sheet_details

    @log_time_taken()
    def get_additional_source_variables(self, record, variables) -> list[dict]:
        """
        Get additional source variables
        """
        from everstage_ddd.datasheet.services.datasheet_service import (
            datasheet_fetch_source_variables,
        )

        source_variables = datasheet_fetch_source_variables(
            client_id=self.client_id,
            source_id=record["source_id"],
            source_type=record["source_type"],
            only_selected=True,
        )

        source_variable_ids = set(
            [variable.source_variable_id for variable in variables]
        )
        display_names_set = set(
            [variable.display_name.lower() for variable in variables]
        )

        transformation_spec = record["transformation_spec"]

        if not transformation_spec:
            return list(
                map(
                    lambda variable: self._construct_variable_details_from_source(
                        variable, record, display_names_set
                    ),
                    filter(
                        lambda variable: str(variable["variable_id"])
                        not in source_variable_ids,
                        source_variables,
                    ),
                )
            )

        prev_transformation_variables = source_variables
        additional_transformation_variables = []
        for transformation in transformation_spec:
            output_columns = transformation["output_columns"]
            transformation_variable_ids = [
                str(var["variable_id"]).split(SAME_SOURCE_VAR)[0]
                for var in output_columns
            ]
            for variable in prev_transformation_variables:
                variable["variable_id"] = str(variable["variable_id"]).split(
                    SAME_SOURCE_VAR
                )[0]
            if transformation["type"] == TransformationType.JOIN.value:
                rhs_source_variables = datasheet_fetch_source_variables(
                    client_id=self.client_id,
                    source_id=transformation["with"],
                    source_type=DatasheetSourceType.DATASHEET.value,
                    only_selected=True,
                )

                lhs_additional_variables = []
                rhs_additional_variables = []
                last_lhs_var = transformation["columns"]["lhs"][-1]
                last_lhs_var_index = next(
                    (
                        index
                        for index, col in enumerate(transformation["output_columns"])
                        if col["system_name"] == f"lhs_{last_lhs_var['system_name']}"
                    ),
                    None,
                )

                for variable in prev_transformation_variables:
                    if (
                        str(variable["variable_id"]) not in transformation_variable_ids
                        and variable["is_primary"]
                    ):
                        lhs_column = Columns(**variable).model_dump()
                        transformation["columns"]["lhs"].append(deepcopy(lhs_column))
                        lhs_column["system_name"] = f"lhs_{variable['system_name']}"
                        lhs_additional_variables.append(deepcopy(lhs_column))
                        transformation["used_variable_ids"].append(
                            str(lhs_column["variable_id"])
                        )

                if lhs_additional_variables and last_lhs_var_index is not None:
                    # Insert the additional lhs variables after the last lhs variable
                    transformation["output_columns"][
                        last_lhs_var_index + 1 : last_lhs_var_index + 1
                    ] = deepcopy(lhs_additional_variables)

                rhs_source_count = transformation["transformation_source_map"][
                    transformation["with"] + "_" + DatasheetSourceType.DATASHEET.value
                ]
                same_source = rhs_source_count > 0
                for variable in rhs_source_variables:
                    if (
                        str(variable["variable_id"]) not in transformation_variable_ids
                        and variable["is_primary"]
                    ):
                        rhs_column = Columns(**variable).model_dump()
                        transformation["columns"]["rhs"].append(deepcopy(rhs_column))

                        rhs_column["system_name"] = f"rhs_{variable['system_name']}"
                        if same_source:
                            rhs_column["variable_id"] = (
                                f"{rhs_column['variable_id']}{SAME_SOURCE_VAR}{rhs_source_count}"
                            )

                        transformation["used_variable_ids"].append(
                            str(rhs_column["variable_id"])
                        )
                        rhs_column["source_id"] = transformation["with"]
                        rhs_column["source_type"] = DatasheetSourceType.DATASHEET.value
                        rhs_column["is_rhs_variable"] = True
                        rhs_additional_variables.append(deepcopy(rhs_column))

                if rhs_additional_variables:
                    # Insert the additional rhs variables at the end of the output_columns
                    transformation["output_columns"].extend(
                        deepcopy(rhs_additional_variables)
                    )

                additional_transformation_variables = deepcopy(
                    lhs_additional_variables + rhs_additional_variables
                )

            elif transformation["type"] == TransformationType.TEMPORAL_SPLICE.value:
                additional_transformation_variables = []
                datasource = transformation["meta"][0]

                used_variable_ids = [
                    variable.split(SAME_SOURCE_VAR)[0]
                    for variable in transformation["used_variable_ids"]
                ]
                for variable in prev_transformation_variables:
                    if str(variable["variable_id"]) not in (
                        transformation_variable_ids + used_variable_ids
                    ):
                        # Temporal splice variable index starts from 1 , Example: ts_1_co_1_name
                        variable["system_name"] = f"ts_1_{variable['system_name']}"
                        variable["is_primary"] = False
                        datasource["input_columns"].append(deepcopy(variable))
                        datasource["output_columns"].append(deepcopy(variable))
                        additional_transformation_variables.append(deepcopy(variable))

                for index, datasource in enumerate(transformation["meta"][1:]):
                    rhs_variables = datasheet_fetch_source_variables(
                        client_id=self.client_id,
                        source_id=datasource["source_id"],
                        source_type=datasource["source_type"],
                        only_selected=True,
                    )

                    for variable in rhs_variables:
                        if str(variable["variable_id"]) not in (
                            transformation_variable_ids
                            + transformation["used_variable_ids"]
                        ):
                            # since index is 0 based, we add 2
                            variable["system_name"] = (
                                f"ts_{index + 2}_{variable['system_name']}"
                            )
                            variable["source_id"] = datasource["source_id"]
                            variable["source_type"] = datasource["source_type"]
                            variable["is_rhs_variable"] = True
                            variable["is_primary"] = False
                            datasource["input_columns"].append(deepcopy(variable))
                            datasource["output_columns"].append(deepcopy(variable))
                            additional_transformation_variables.append(
                                deepcopy(variable)
                            )

            elif transformation["type"] in [
                TransformationType.UNION.value,
                TransformationType.GROUP_BY.value,
            ]:
                additional_transformation_variables = []

            elif transformation["type"] in [
                TransformationType.FILTER.value,
                TransformationType.ADVANCED_FILTER.value,
                TransformationType.ADVANCED_FILTER_V2.value,
                TransformationType.FLATTEN.value,
                TransformationType.GET_USER_PROPERTIES.value,
            ]:
                additional_transformation_variables = []
                for variable in prev_transformation_variables:
                    if variable["variable_id"] not in transformation_variable_ids:
                        additional_transformation_variables.append(deepcopy(variable))

            else:
                raise DatasheetException(
                    code="INVALID_TRANSFORMATION_TYPE",
                    message=f"Invalid transformation type: {transformation['type']}",
                )
            if transformation["type"] != TransformationType.JOIN.value:
                # For join, output_columns is already extended.
                transformation["output_columns"].extend(
                    deepcopy(additional_transformation_variables)
                )

            prev_transformation_variables = deepcopy(transformation["output_columns"])

        return list(
            map(
                lambda variable: self._construct_variable_details_from_source(
                    variable, record, display_names_set
                ),
                deepcopy(additional_transformation_variables),
            )
        )

    @log_time_taken()
    def _construct_variable_details_from_source(
        self, variable, record, display_names_set
    ):
        variable["source_variable_id"] = str(variable["variable_id"])
        variable["variable_id"] = uuid.uuid4()
        variable["is_selected"] = variable.get("is_primary", False)
        variable["client_id"] = self.client_id
        if not variable.get("is_rhs_variable", False):
            variable["source_id"] = record["source_id"]
            variable["source_type"] = record["source_type"]
        else:
            del variable["is_rhs_variable"]
        # For source as object, we need to add datasheet_id and databook_id
        variable["datasheet_id"] = record["datasheet_id"]
        variable["databook_id"] = record["databook_id"]
        variable["display_name"] = self.construct_variable_display_name(
            self.client_id, variable, display_names_set
        )
        variable["meta_data"] = None
        variable["field_order"] = 0
        return variable

    @staticmethod
    def construct_variable_display_name(client_id, variable, display_names_set):
        if variable["display_name"].lower() in display_names_set:
            # Add source name to display name source_name :: display_name
            from everstage_ddd.datasheet.services.datasheet_service import (
                fetch_source_name_of_variable,
            )

            source_name = fetch_source_name_of_variable(
                client_id=client_id,
                source_id=variable["source_id"],
                source_type=variable["source_type"],
            )
            variable["display_name"] = f"{source_name} :: {variable['display_name']}"
            while variable["display_name"].lower() in display_names_set:
                variable["display_name"] = (
                    f"{source_name} :: {variable['display_name']}"
                )
        display_names_set.add(variable["display_name"].lower())
        return variable["display_name"]

    @log_time_taken()
    def _get_primary_keys_from_source(self, source_id: uuid.UUID) -> set:
        """
        Fetch primary key variable IDs from a source datasheet.

        Args:
            source_id (UUID): The ID of the source datasheet

        Returns:
            set: A set of variable IDs (as strings) that are marked as primary keys
        """
        selector = datasheet_selectors.DatasheetVariableSelector(
            client_id=self.client_id, datasheet_id=source_id
        )

        return set(
            str(variable_id)
            for variable_id in selector.get_variables()
            .filter(is_primary=True)
            .annotate(variable_id_str=Cast("variable_id", EsCharField()))
            .values_list("variable_id_str", flat=True)
        )

    @log_time_taken()
    def _update_variables_primary_key(self, variables: list, record: dict) -> list:
        """
        Create a new list of variables with updated primary key status based on source datasheets.

        This method creates a new list of variables with their 'is_primary' attribute updated
        based on their source datasheets. It identifies valid source datasheets, fetches their
        primary key information, and sets the primary key status accordingly.

        Args:
            variables (list): A list of variable objects to process
            record (dict): The datasheet record containing transformation specifications

        Returns:
            list: A new list of variable objects with updated primary key status
        """
        # If no variables, return empty list
        if not variables:
            return []

        # STEP 1: Identify source IDs that should be excluded
        # -----------------------------------------------------
        # Always exclude the current datasheet
        source_ids_to_exclude = set([self.datasheet_id])

        # Process transformations to identify additional sources to exclude
        for transformation in record.get("transformation_spec", []):
            # Handle temporal splice transformations
            if transformation.get("type") == TransformationType.TEMPORAL_SPLICE.value:
                for datasource in transformation.get("meta", []):
                    source_ids_to_exclude.add(datasource.get("source_id"))

            # Handle union and group by transformations
            if transformation.get("type") in [
                TransformationType.UNION.value,
                TransformationType.GROUP_BY.value,
            ]:
                source_ids_to_exclude.update(transformation.get("source_ids", []))

        # STEP 2: Find all valid source datasheets from variables
        # ------------------------------------------------------
        valid_source_ids = set(
            var.source_id
            for var in variables
            if var.source_id not in source_ids_to_exclude
            and var.source_type == DatasheetSourceType.DATASHEET.value
        )

        # If no valid sources found, return variables unchanged
        if not valid_source_ids:
            return variables

        # STEP 3: Collect primary keys from all valid source datasheets
        # -----------------------------------------------------------
        all_primary_keys = set()
        for source_id in valid_source_ids:
            primary_keys = self._get_primary_keys_from_source(source_id)
            all_primary_keys.update(primary_keys)

        updated_variables = []

        # STEP 4: Update primary key status for each variable
        # -------------------------------------------------
        for variable in variables:
            new_variable = deepcopy(variable)  # Create a new variable to avoid mutation
            # Only update variables from valid source datasheets
            if (
                new_variable.source_type == DatasheetSourceType.DATASHEET.value
                and new_variable.source_id not in source_ids_to_exclude
            ):
                # Extract the base source variable ID
                source_variable_id = new_variable.source_variable_id
                if SAME_SOURCE_VAR in source_variable_id:
                    source_variable_id = source_variable_id.split(SAME_SOURCE_VAR)[0]

                # Set primary key status based on presence in collected primary keys
                new_variable.is_primary = source_variable_id in all_primary_keys

            updated_variables.append(new_variable)

        return updated_variables

    def _can_remove_variable_in_sheet(self, variable):
        if variable.is_selected:
            return False
        if variable.meta_data is not None:
            return False
        if variable.source_id == self.datasheet_id:
            return False

        source_variable_id = variable.source_variable_id
        # Remove the __ss__0, __ss__1, etc from the source variable id
        if SAME_SOURCE_VAR in source_variable_id:
            source_variable_id = variable.source_variable_id.split(SAME_SOURCE_VAR)[0]

        if variable.source_type == DatasheetSourceType.DATASHEET.value:
            datasheet_variable_selector = datasheet_selectors.DatasheetVariableSelector(
                client_id=self.client_id,
                datasheet_id=variable.source_id,
            )

            source_variable = (
                datasheet_variable_selector.get_variables()
                .filter(variable_id=source_variable_id)
                .first()
            )

            return source_variable is None or not source_variable.is_selected

        if variable.source_type == DatasheetSourceType.CUSTOM_OBJECT.value:
            return (
                CustomObjectVariableAccessor(
                    client_id=self.client_id
                ).get_variable_by_system_name(variable.source_id, source_variable_id)
                is None
            )

        if variable.source_type == DatasheetSourceType.REPORT_OBJECT.value:
            return (
                get_report_object_variable(
                    client_id=self.client_id,
                    ever_object_id=variable.source_id,
                    variable_id=source_variable_id,
                )
                is None
            )

        return False

    @log_time_taken()
    def update_datasheet_meta(self, data: dict):
        """
        Rename the datasheet
        """
        datasheet = self.get_datasheet()
        return model_to_dict(
            self.bitemporal_update(record_identifier=datasheet.pk, data=data)
        )

    @log_time_taken()
    def create_datasheet(
        self,
        data: dict,
    ):
        """
        Create a new datasheet
        """
        return self.bitemporal_create(data=data)

    @log_time_taken()
    def bulk_datasheet_order_update(self, records: dict[str, int]) -> None:
        """
        This function updates the order of the datasheets in bulk for the given records.
        records is a mapping of datasheet_id to order
        """
        self.client_kd_deleted_aware().filter(datasheet_id__in=records.keys()).update(
            order=Case(
                *[
                    When(datasheet_id=datasheet_id, then=Value(order))
                    for datasheet_id, order in records.items()
                ],
                output_field=EsIntegerField(),
            )
        )

    @log_time_taken()
    def _get_and_flush_validated_transformations(self):
        transformation_objs = (
            datasheet_selectors.DatasheetTransformationSelector(
                client_id=self.client_id, datasheet_id=self.datasheet_id
            )
            .get_transformations()
            .order_by("order")
        )
        spec_values = [transformation.spec for transformation in transformation_objs]
        transformation_objs.delete()
        return spec_values

    @log_time_taken()
    def get_transfromation_spec_for_datasheet(self):
        return (
            self.client_kd_deleted_aware()
            .filter(datasheet_id=self.datasheet_id)
            .values("transformation_spec")
        )

    @log_time_taken()
    def get_datasheets_map_with_co_permission(
        self, datasheet_ids: list[uuid.UUID], user_id: str
    ) -> dict[uuid.UUID, bool]:
        """
        Returns a map of datasheet_id to a boolean value indicating whether the user has permission to access the datasheet.
        """
        excluded_objects = get_objects_excluded_for_user(self.client_id, user_id)
        datasheet_map = {}
        for datasheet_id in datasheet_ids:
            objects_involved_in_ds_creation = self._get_objects_involved_in_ds_creation(
                datasheet_id
            )
            if objects_involved_in_ds_creation.intersection(excluded_objects):
                datasheet_map[datasheet_id] = False
            else:
                datasheet_map[datasheet_id] = True
        return datasheet_map

    @log_time_taken()
    def _get_objects_involved_in_ds_creation(self, datasheet_id: uuid.UUID):
        datasheet_variable_selector = datasheet_selectors.DatasheetVariableSelector(
            client_id=self.client_id, datasheet_id=datasheet_id
        )
        return set(
            datasheet_variable_selector.get_variables()
            .exclude(source_type=DatasheetSourceType.DATASHEET.value)
            .values_list("source_id", flat=True)
        )

    def _is_active_sync_present(self, client_id, databook_id, datasheet_id):
        # Check if there is an active sync for the datasheet
        is_active_sync_present = DatabookETLStatusReaderAccessor(
            client_id=self.client_id
        ).is_datasheet_sync_in_progress(
            databook_id=databook_id, datasheet_id=datasheet_id
        )
        if is_active_sync_present:
            return True

        # Check if there is an latest sync that is in queue
        latest_record = ETLSyncStatusReaderAccessor(
            client_id=client_id
        ).get_latest_sync_info_for_datasheet(
            datasheet_id=datasheet_id, databook_id=databook_id
        )

        if latest_record and latest_record["sync_end_time"] is None:
            return True

        return False

    def _is_databook_active_sync_present(self, client_id, databook_id):
        latest_record = ETLSyncStatusReaderAccessor(
            client_id=client_id
        ).get_latest_sync_info_by_db_id(
            activity=ETL_ACTIVITY.REFRESH_DATABOOK.value,
            databook_id=str(databook_id),
        )

        if latest_record and latest_record["sync_end_time"] is None:
            return True

        return DatabookETLStatusReaderAccessor(
            client_id=client_id
        ).is_databook_sync_in_progress(databook_id=databook_id)

    @log_time_taken()
    def _construct_databook_groups(self, databooks, user_id: str):
        databook_names_order = databooks.order_by("created_at").values(
            "databook_id", "name", "datasheet_order"
        )

        if not databook_names_order:
            return []

        databook_id_name_map = {}
        databook_id_order_map = defaultdict(list)
        databook_order_list = []  # List to maintain the order of databook IDs

        for d in databook_names_order:
            databook_id = d["databook_id"]
            databook_id_name_map[databook_id] = d["name"]
            databook_id_order_map[databook_id] = d["datasheet_order"]
            databook_order_list.append(
                databook_id
            )  # Append the databook_id to maintain the order

        datasheets = self.get_all_datasheets_name_databook_id_for_client(
            databook_ids=databook_order_list
        )

        datasheet_map_with_co_permission = self.get_datasheets_map_with_co_permission(
            datasheet_ids=[sheet["datasheet_id"] for sheet in datasheets],
            user_id=user_id,
        )
        last_generated_at_map = datasheet_selectors.DbkdPkdMapSelector(
            self.client_id
        ).get_multiple_datasheet_last_generation_times(
            datasheet_ids=[
                sheet["datasheet_id"]
                for sheet in datasheets
                if sheet.get("is_datasheet_generated")
            ]
        )

        grouped_datasheets = pydash.group_by(datasheets, "databook_id")

        # Sorting the datasheets within each databook based on the order defined in databook_id_order_map
        for databook_id, sheets in grouped_datasheets.items():
            # databook_id_order_map[databook_id] gives the correctly ordered list of datasheet_ids
            ordered_sheets = databook_id_order_map[databook_id]
            # Creating a mapping from datasheet_id to its index in the correct order for fast lookup
            if ordered_sheets:
                order_index_map = {
                    str(datasheet_id): index
                    for index, datasheet_id in enumerate(ordered_sheets)
                }

                # Sorting the sheets based on their index in the order list
                # If a datasheet_id is not in the correct order, it is placed at the top (handled by order_index_map.get with a low default index)
                sheets.sort(
                    key=lambda sheet: order_index_map.get(
                        str(sheet["datasheet_id"]), float("-inf")
                    ),
                )

                # Add 'last_generated_at' field to each datasheet
                for sheet in sheets:
                    sheet["last_generated_at"] = last_generated_at_map.get(
                        sheet["datasheet_id"], None
                    )
                    sheet["does_user_has_co_permission"] = (
                        datasheet_map_with_co_permission[sheet["datasheet_id"]]
                    )

                # No need to assign back to grouped_datasheets[databook_id] as sorting is in-place

        # Merge the sorted datasheets with the databook names and construct the final structure
        merged_and_sorted = pydash.objects.merge_with(
            grouped_datasheets,
            databook_id_name_map,
            lambda x, y, key: {"id": key, "name": y} | {"datasheets": x if x else []},
        ).values()

        # Sort the merged structure based on the recently created order of databook IDs
        return sorted(
            merged_and_sorted,
            key=lambda x: databook_order_list.index(x["id"]),
            reverse=True,
        )

    @log_time_taken()
    def get_all_datasheet_ids_in_current_fiscal_year(self, min_kbd: datetime):
        result = (
            self.client_aware()
            .values("datasheet_id")
            .annotate(min_kb_date=Min("knowledge_begin_date"))
            .filter(min_kb_date__gt=min_kbd)
            .values_list("datasheet_id")
        )
        return [str(x[0]) for x in result]

    @log_time_taken()
    def update_warnings_for_auto_enrich_variables(self, variables):
        """
        Update the warnings for auto enrich variables.

        This method updates the warnings for auto enrich variables based on the current state of the datasheet.

        Args:
            variables (list): List of variables to update.

        Returns:
            variables (list): The updated list of variables.
        """
        from everstage_ddd.datasheet.services.datasheet_variables import (
            get_auto_enrich_variables,
        )

        # 1. Extract the system_name of the comm report variables of opened sheet
        ds_comm_report_variables = set()
        for variable in variables:
            if variable.source_type == DatasheetSourceType.REPORT_OBJECT.value and (
                variable.source_id in {"commission", "inter_commission"}
            ):
                ds_comm_report_variables.add(variable.system_name)

        # Return the variables if there are no comm report variables in the opened sheet
        if not ds_comm_report_variables:
            return variables

        # 2. Extract the system_name of the predefined and enrichment variables
        comm_report_variables_set = set()
        predefined_comm_report_variables = (
            EverObjectVariableAccessor().get_ever_object_variable(
                object_id="commission"
            )
        )
        cre_variables = get_auto_enrich_variables(self.client_id)
        comm_report_variables_set.update(
            {var["system_name"] for var in predefined_comm_report_variables}
        )
        comm_report_variables_set.update({var["system_name"] for var in cre_variables})

        # 3. Find the deleted or removed comm report vars that are prsent in the opened sheet
        set_diff_ds_comm_report_variables = ds_comm_report_variables.difference(
            comm_report_variables_set
        )

        if len(set_diff_ds_comm_report_variables) > 0:
            extracted_variables = DatasheetVariablesListResponse(variables).model_dump(
                by_alias=True
            )

            for variable in extracted_variables:
                if variable["system_name"] in set_diff_ds_comm_report_variables:
                    variable["warning"] = (
                        "This variable is not present in the Commission Report object."
                    )
            return extracted_variables

        return variables
