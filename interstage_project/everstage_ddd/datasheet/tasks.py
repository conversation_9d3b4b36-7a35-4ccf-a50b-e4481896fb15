from celery import shared_task

from common.celery.celery_base_task import EverC<PERSON>ryBaseTask
from everstage_ddd.tqm.services.sigma_snowflake_services import (
    update_sigma_table_for_databook,
    update_sigma_table_for_datasheet,
)
from interstage_project.threadlocal_log_context import set_threadlocal_context


@shared_task(base=EverCeleryBaseTask)
def async_update_sigma_table_for_databook(client_id, databook_id, databook_name):
    set_threadlocal_context({"client_id": client_id})
    update_sigma_table_for_databook(client_id, databook_id, databook_name)


@shared_task(base=EverCeleryBaseTask)
def async_update_sigma_table_for_datasheet(client_id, datasheet_id, datasheet_name):
    set_threadlocal_context({"client_id": client_id})
    update_sigma_table_for_datasheet(client_id, datasheet_id, datasheet_name)
