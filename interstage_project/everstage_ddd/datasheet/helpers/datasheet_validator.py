import logging
from copy import deepcopy
from uuid import uuid4

import pydash

import everstage_ddd.datasheet.selectors as datasheet_selectors
from commission_engine.accessors.client_accessor import can_run_auto_enrich_report
from commission_engine.accessors.ever_object_accessor import EverObjectVariableAccessor
from commission_engine.services.commission_calculation_service.reference_data_type import (
    get_data_types,
)
from commission_engine.services.datasheet_data_services import datasheet_validation
from commission_engine.services.datasheet_data_services.variable_extractor import (
    VariableExtractor,
)
from commission_engine.services.expression_designer import (
    AstMetaContext,
    add_meta_to_advanced_filter_infix,
    convert_window_func_cf_variables,
)
from commission_engine.services.hris_integration_services.hris_integration_services import (
    validate_variables_used_in_hris,
)
from commission_engine.types import HierarchyUtils
from commission_engine.utils.criteria_calculator_utils import create_ast
from everstage_ddd.datasheet.data_models import DatasheetVariablesList
from everstage_ddd.datasheet.enums import (
    SAME_SOURCE_VAR,
    DatasheetSourceType,
    GroupByAggDatatypesMap,
    TransformationType,
)
from everstage_ddd.datasheet.exceptions import DatasheetException
from everstage_ddd.datasheet.helpers.datasheet_expression_box_utils import (
    DatasheetExpressionBoxUtils,
)
from everstage_ddd.datasheet.models import DatasheetVariableTemp
from everstage_ddd.datasheet.selectors import (
    DatasheetPermissionSelector,
    DatasheetSelector,
    DatasheetTransformationSelector,
    DatasheetVariableSelector,
    DatasheetViewSelector,
)
from everstage_ddd.datasheet.services.datasheet_variables import (
    datasheet_variables_fetch,
)
from everstage_ddd.workflow_builder.service import workflow_service
from spm.services.databook_services import (
    _has_config_changed,
    is_hierarchy_calculated_field,
)
from spm.services.datasheet_graph.datasheet_graph import DataSheetGraph
from spm.services.datasheet_variable_services import insert_ast_in_meta_data
from spm.services.datasheet_variables_graph import DatasheetVariablesGraph

# ruff: noqa: ERA001
# ruff: noqa: PLR0913
# ruff: noqa: PLR0912

module_logger = logging.getLogger(__name__)


class DatasheetValidator:
    def __init__(
        self,
        *,
        client_id,
        datasheet_id,
        variables,
        transformations,
        source_variables,
        source_id,
        source_type,
        has_source_changed,
        expression_box_version="v2",
    ):
        self.client_id = client_id
        self.datasheet_id = datasheet_id
        self.variables = variables
        self.orig_variables = variables
        self.expression_box_version = expression_box_version
        self.transformations = transformations
        self.datasheet = self.get_datasheet_record()
        self.databook_id = self._populate_databook_id()
        self.selected_variables = list(
            filter(lambda x: x["is_selected"], self.variables)
        )
        self.unselected_variables = list(
            filter(lambda x: not x["is_selected"], self.variables)
        )
        self.cf_variables = list(
            filter(
                lambda x: x["source_id"] == str(self.datasheet_id)
                and x.get("meta_data") is not None,
                self.variables,
            )
        )
        self.source_id = source_id
        self.source_type = source_type
        self.source_variables = source_variables
        self.transformation_output_variables = deepcopy(source_variables)
        # Flag to check if the variables are reseted, If true in case of join like transformations
        self.is_variable_reseted = False
        # Flag to check if the variables are reseted, If true in case of user property like transformations
        self.is_variable_added = False
        # Flag to check if the transformations are validated, will be True in case of invalid transformation
        # This flag is used to set the final variables
        self.is_transformation_invalid = False
        # Flag to check if the transformations are deleted, will be True in case of deleted transformations
        self.is_transformation_deleted = False
        self.used_variable_ids = set()
        # Aggregation of rhs source_ids used in datasheet transformation
        self.source_ids = {self.source_id}
        self.existing_selected_variables = []
        self.flatten_system_name = ""
        self.transformation_source_map = {f"{self.source_id}_{self.source_type}": 0}
        self.has_source_changed = has_source_changed
        self.datasheet_graph = None

    def validate(self):
        if self.has_source_changed:
            self._handle_source_change()
            self.validate_deleted_variables()
            return

        self._validate_datasheet_transformations()
        if (
            self.is_variable_added
            or self.is_variable_reseted
            or self.is_transformation_deleted
        ):
            self._construct_variables_from_transformations()
            self._transform_calculated_fields()

        self._update_output_columns_display_name()
        # We will compute the AST for calculated fields to ensure that it is generated using the
        # transformed system names in case of any transformations. Additionally, this removes the
        # frontend dependency on the AST, which was causing issues since the middleware converts
        # the binding operator (AND or OR) to lowercase in the AST.
        self._add_ast_to_calculated_fields()

        ## Validate the variables that deleted in the transformations or calculated fields
        ## self.variables will have new variables added
        ## selected and unselected variables will have the existing variables
        self.validate_deleted_variables()

        self.existing_selected_variables = self.selected_variables

        # Recalculate the selected variables after transformation

        self.selected_variables = list(
            filter(lambda x: x["is_selected"], self.variables)
        )
        self.unselected_variables = list(
            filter(lambda x: not x["is_selected"], self.variables)
        )
        self.cf_variables = list(
            filter(
                lambda x: x["source_id"] == str(self.datasheet_id)
                and x.get("meta_data") is not None,
                self.variables,
            )
        )

        # Skip the variable validation if transformation is invalid or deleted
        if self.is_transformation_invalid or self.is_transformation_deleted:
            self._validate_selected_variables()
            convert_window_func_cf_variables(
                self.client_id,
                self.existing_selected_variables,
                databook_id=self.databook_id,
                datasheet_id=self.datasheet_id,
                requires_intermediate_variables=True,
            )
            self.validate_calculated_fields()

            # This will populate the variable details
            # including field order and evaluation context for calculated fields
            self.populate_variable_details()
            return

        self.validate_datasheet_variables()
        self.populate_variable_details()
        self._validate_circular_dependencies()

    def populate_validated_details(self):
        self._populate_validated_transformations()
        self._populate_validated_variables()

    def has_config_changed(self) -> bool:
        """
        This function is used to check if the datasheet config has changed.
        """
        is_part_of_workflow = (
            workflow_service.is_databook_datasheet_part_of_any_workflow(
                client_id=self.client_id,
                databook_id=str(self.databook_id),
                datasheet_id=str(self.datasheet_id),
            )
        )

        if not is_part_of_workflow:
            return False

        datasheet_variable_selector = datasheet_selectors.DatasheetVariableSelector(
            client_id=self.client_id,
            datasheet_id=self.datasheet_id,
        )
        existing_vars = list(datasheet_variable_selector.get_variables().values())

        request_vars = DatasheetVariablesList(self.variables).model_dump()

        is_pk_modified, is_config_changed, is_calc_field_changed = _has_config_changed(
            existing_vars, request_vars
        )

        return is_pk_modified or is_config_changed or is_calc_field_changed

    def get_draft_details(self) -> dict:
        variables = self._get_variables()
        transformations = self._get_transformations()
        return {
            "variables": variables,
            "transformations": transformations,
            "source_variables": self.source_variables,
        }

    def populate_variable_details(self):
        self._transform_calculated_fields()
        self._convert_window_calculated_fields()
        self._add_field_order_to_variables()
        self._add_source_cf_meta_data_to_variables()
        self._update_warnings_for_auto_enriched_variables()

    def populate_temp_tables_from_source(self, additional_variables: list):
        self._populate_temp_variables_from_source(additional_variables)
        self._populate_transformations_from_source()

    def get_datasheet_record(self):
        return DatasheetSelector(
            client_id=self.client_id,
            datasheet_id=self.datasheet_id,
        ).get_datasheet()

    def _populate_databook_id(self):
        return self.datasheet.databook_id

    def validate_datasheet_variables(self):
        self._validate_selected_variables()
        self._validate_unselected_variables()
        self._is_primary_keys_unselected()
        self.validate_calculated_fields()

    def validate_deleted_variables(self):
        ## Validate the variables that deleted in the transformations
        ## self.variables will have new variables added
        ## selected and unselected variables will have the existing variables
        selected_variables = list(
            filter(lambda x: x["is_selected"], self.get_draft_details()["variables"])
        )
        variable_id_map = self._build_variable_map(
            list(filter(lambda x: x["is_selected"], self.variables))
        )
        unselected_variables = [
            var
            for var in selected_variables
            if variable_id_map.get(str(var["variable_id"])) is None
        ]
        if unselected_variables:
            self.unselected_variables, unselected_variables = (
                unselected_variables,
                self.unselected_variables,
            )
            self.selected_variables = list(
                filter(lambda x: x["is_selected"], self.variables)
            )
            self._validate_unselected_variables(context="DELETE")
            self.validate_calculated_fields()
            self.unselected_variables = unselected_variables

    def is_calculated_field(self, variable):
        return (
            variable["source_id"] == str(self.datasheet_id)
            and variable.get("meta_data") is not None
        )

    def is_rank_calculated_field(self, variable):
        return pydash.get(variable, "meta_data.rank") is not None

    def is_rolling_calculated_field(self, variable):
        return pydash.get(variable, "meta_data.rolling") is not None

    def validate_calculated_fields(self):
        variable_ids = set()
        for variable in self.cf_variables:
            if variable["is_selected"]:
                variable_ids.update(
                    DatasheetExpressionBoxUtils(
                        client_id=self.client_id,
                        databook_id=self.databook_id,
                        datasheet_id=self.datasheet_id,
                        meta_data=variable["meta_data"],
                        variables=self.variables,
                    ).extract_variable_ids()
                )

        diff_variables = variable_ids.difference(
            set([variable["variable_id"] for variable in self.selected_variables])
        )
        if diff_variables:
            display_names = self._get_display_names_from_variable_ids(diff_variables)
            raise DatasheetException(
                code="INVALID_VARIABLES",
                message=f"Variable/Variables {display_names} used in calculated field are either unselected or invalid",
            )

    def _handle_source_change(self):
        if self.datasheet_graph is None:
            self.datasheet_graph = DataSheetGraph(
                client_id=self.client_id,
                include_stale_information_query=False,
            )

        descendants_datasheets: set = self.datasheet_graph.descendants(
            datasheet_id=str(self.datasheet_id)
        )
        if descendants_datasheets:
            raise DatasheetException(
                code="INVALID_SOURCE",
                message="Cannot change source of datasheet with dependent sheets",
            )
        self._unselect_calculated_fields()
        self._construct_variables_from_source()
        self._invalidate_transformations()

    def _unselect_calculated_fields(self):
        for variable in self.cf_variables:
            variable["is_selected"] = False

    def _invalidate_transformations(self):
        for transformation in self.transformations:
            transformation["is_valid"] = False
            transformation["output_columns"] = []
            if transformation["type"] == TransformationType.JOIN.value:
                transformation["on"]["lhs"] = []
                transformation["columns"]["lhs"] = []
                transformation["on"]["rhs"] = []
                transformation["columns"]["rhs"] = []

            elif transformation["type"] == TransformationType.GET_USER_PROPERTIES.value:
                transformation["email_column"] = {}
                if transformation["as_of_date_column"]["variable_id"] is not None:
                    transformation["as_of_date_column"] = {}

            elif transformation["type"] in [
                TransformationType.ADVANCED_FILTER.value,
                TransformationType.ADVANCED_FILTER_V2.value,
            ]:
                transformation["infix"] = []

            elif transformation["type"] in [
                TransformationType.FLATTEN.value,
                TransformationType.FILTER.value,
            ]:
                transformation["col_name"] = None
                transformation["variable_id"] = None

            elif transformation["type"] == TransformationType.UNION.value:
                for spec in transformation["on"]:
                    spec["lhs"] = {}

            elif transformation["type"] == TransformationType.GROUP_BY.value:
                transformation["by"] = []
                transformation["aggregations"] = []

            elif transformation["type"] == TransformationType.TEMPORAL_SPLICE.value:
                meta = transformation["meta"][0]
                meta["source_id"] = self.source_id
                meta["source_type"] = self.source_type
                meta["with_databook_id"] = str(
                    (
                        DatasheetSelector(
                            client_id=self.client_id,
                            datasheet_id=self.source_id,
                        )
                        .get_datasheet()
                        .databook_id
                    )
                    if self.source_type == DatasheetSourceType.DATASHEET.value
                    else None
                )

                for index, meta in enumerate(transformation["meta"]):
                    reset = False
                    if meta["source_id"] == self.source_id and index != 0:
                        meta["source_id"] = None
                        meta["source_type"] = None
                        meta["with_databook_id"] = None
                        reset = True

                    if index == 0 or reset:
                        meta["email_id_column"] = {}
                        if meta["has_effective_dates"]:
                            if meta["start_date_column"]["variable_id"] is not None:
                                meta["start_date_column"] = {}
                            if meta["end_date_column"]["variable_id"] is not None:
                                meta["end_date_column"] = {}

    def _validate_variables_after_reset(self):
        ## Reset the selected variables and unselected variables for validation
        selected_variables, self.selected_variables = (
            self.selected_variables,
            self.cf_variables,
        )
        variable_id_map = self._build_variable_map(self.variables)
        unselected_variables = self.unselected_variables
        self.unselected_variables = [
            var
            for var in selected_variables
            if not self.is_calculated_field(var)
            and variable_id_map.get(var["variable_id"]) is None
        ]
        self._validate_unselected_variables()
        self.selected_variables, self.unselected_variables = (
            selected_variables,
            unselected_variables,
        )

    def _validate_selected_variables(self):
        self._validate_system_names_uniqueness()
        self._validate_variable_id_uniqueness()
        self._validate_dependent_sheet_system_names()
        self._is_unselected_variables_used_in_permissions(context="MODIFY")
        self._is_unselected_variables_used_in_views(context="MODIFY")
        self._is_unselected_variables_used_in_adjustments(context="MODIFY")
        self._validate_3p_system_names()
        self._validate_display_name_uniqueness()
        result = validate_variables_used_in_hris(
            client_id=self.client_id,
            selected_vars=[
                variable["system_name"] for variable in self.selected_variables
            ],
            datasheet_id=self.datasheet_id,
        )
        if not result["is_valid"]:
            raise DatasheetException(
                code="HRIS_ERROR",
                message=result["errors"],
            )

    def _validate_dependent_sheet_system_names(self):
        """
        Check if the selected variables system names used in dependent datasheets are valid
        """
        modified_system_names = self._get_modified_system_names()
        if not modified_system_names:
            return

        if self.datasheet_graph is None:
            self.datasheet_graph = DataSheetGraph(
                client_id=self.client_id,
                include_stale_information_query=False,
            )

        descendants_datasheets: set = self.datasheet_graph.descendants(
            datasheet_id=str(self.datasheet_id)
        )
        if descendants_datasheets:
            display_names = self._get_display_names_from_system_names(
                modified_system_names
            )
            raise DatasheetException(
                code="DEPENDENCY_EXISTS",
                message=f"Cannot Modify column {display_names} since some of the columns are used in dependent sheets",
            )

    def _get_modified_system_names(self):
        selected_system_names = set(
            [variable["system_name"] for variable in self.selected_variables]
        )
        variable_ids = set(
            [variable["variable_id"] for variable in self.selected_variables]
        )
        existing_system_names = set(
            [
                variable["system_name"]
                for variable in self.orig_variables
                if variable["variable_id"] in variable_ids
            ]
        )
        return existing_system_names.difference(selected_system_names)

    def _validate_3p_system_names(self):
        """
        This function is used to validate the variables that are
        used in external source like commission plans, settlements rule etc
        """
        modified_system_names = self._get_modified_system_names()
        if modified_system_names:
            selected_system_names = [
                variable["system_name"] for variable in self.selected_variables
            ]
            result = datasheet_validation.validate_deleted_datasheet_variables(
                self.client_id, str(self.datasheet_id), selected_system_names
            )

            if not result["is_valid"]:
                raise DatasheetException(
                    code="MODIFIED_VARIABLES",
                    message=result["errors"],
                )

    def _validate_system_names_uniqueness(self):
        system_names = [variable["system_name"] for variable in self.selected_variables]
        if len(system_names) != len(set(system_names)):
            raise DatasheetException(
                code="INVALID_NAME",
                message="Multiple columns have the same internal name",
            )

    def _validate_variable_id_uniqueness(self):
        variable_ids = [variable["variable_id"] for variable in self.selected_variables]
        if len(variable_ids) != len(set(variable_ids)):
            raise DatasheetException(
                code="INVALID_VARIABLE_ID",
                message="Variables are not unique, cannot have same source multiple times",
            )

    def _validate_display_name_uniqueness(self):
        display_names = [
            variable["display_name"].lower() for variable in self.selected_variables
        ]
        duplicates = set(
            name for name in display_names if display_names.count(name) > 1
        )

        if duplicates:
            raise DatasheetException(
                code="INVALID_NAME",
                message=f"Multiple columns have the same name: {', '.join(duplicates)}",
            )

    def _validate_unselected_variables(self, context="VALIDATE"):
        self._is_unselected_variables_used_in_dependent_sheets(context=context)
        self._is_unselected_variables_used_in_permissions()
        self._is_unselected_variables_used_in_views()
        self._is_unselected_variables_used_in_adjustments()
        selected_system_names = [
            variable["system_name"] for variable in self.selected_variables
        ]
        result = datasheet_validation.validate_deleted_datasheet_variables(
            self.client_id, str(self.datasheet_id), selected_system_names
        )

        workflow_variables_errors = (
            workflow_service.get_errors_for_missing_datasheet_columns(
                self.client_id,
                str(self.datasheet_id),
                selected_system_names,
            )
        )
        if workflow_variables_errors:
            result["is_valid"] = False
            result["errors"].extend(workflow_variables_errors)

        if not result["is_valid"]:
            raise DatasheetException(
                code="DELETED_VARIABLES",
                message=result["errors"],
            )

    def get_variable_id_to_system_name_map(self):
        variable_id_to_system_name_map = {}
        for variable in self.variables:
            variable_id_to_system_name_map[variable["variable_id"]] = str(
                variable["system_name"]
            )
        return variable_id_to_system_name_map

    def _is_unselected_variables_used_in_permissions(self, context="DELETE"):
        all_permissions = DatasheetPermissionSelector(
            self.client_id, self.datasheet_id
        ).get_all_active_permissions()

        variable_id_to_system_name_map = self.get_variable_id_to_system_name_map()
        used_variable_ids = set()
        used_system_names = set()

        for permission in all_permissions:
            """
            {
            "variable_id_map": {
                "lhs_time_zone": "0cb1c9ff-9883-4e2e-9fda-1cdfc1c7ff30",
                "lhs_joining_date": "51804501-4aed-4572-b450-623a8a0cdb9a"
                }
            }
            """
            additional_details = permission.additional_details
            variable_id_map = {}

            if additional_details:
                variable_id_map = additional_details.get("variable_id_map", {})

            if variable_id_map:
                used_variable_ids.update(variable_id_map.values())
            else:
                if permission.filter_list:
                    for filter in permission.filter_list:
                        used_system_names.add(filter["col_name"])
                if permission.columns_to_be_hidden:
                    used_system_names.update(permission.columns_to_be_hidden)

        diff_system_names = set()

        if used_system_names:
            diff_system_names = used_system_names
        elif used_variable_ids:
            diff_system_names = set(
                [
                    variable_id_to_system_name_map.get(var_id)
                    for var_id in used_variable_ids
                ]
            )

        diff_variables = set(diff_system_names).difference(
            set([variable["system_name"] for variable in self.selected_variables])
        )

        if diff_variables:
            self._handle_invalid_variables(diff_variables, context, "permissions")

    def _is_unselected_variables_used_in_views(self, context="DELETE"):
        view_selector = DatasheetViewSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        )
        views = view_selector.get_datasheet_views()
        columns_used = set()
        columns_used_in_pivot = set()
        variable_id_to_system_name_map = self.get_variable_id_to_system_name_map()

        # Initialize set to collect variable IDs from filters
        filter_view_variable_ids = set()

        for view in views:
            filter_data = view_selector.get_filter_data(view.filter_id)
            if filter_data:
                # Extract variable IDs directly from filter data using DatasheetExpressionBoxUtils
                variable_ids = self._extract_variable_ids_from_filter_data(
                    deepcopy(filter_data)
                )
                filter_view_variable_ids.update(variable_ids)

                ast = create_ast(filter_data)
                used_system_names = VariableExtractor().get_variables_used(ast["ast"])
                columns_used.update(used_system_names)

            if view.pivot_id:
                pivot_details = view_selector.get_pivot_data(view.pivot_id)
                pivot_variable_id_map = pivot_details.get("variable_id_map", None)
                if pivot_variable_id_map:
                    filter_view_variable_ids.update(pivot_variable_id_map.values())
                else:
                    columns_used_in_pivot.update(
                        pivot_details["index"]
                        + pivot_details["columns"]
                        + pivot_details["values"]
                        + list(pivot_details["aggfunc"].keys())
                    )

        diff_variables = set(columns_used).difference(
            set([variable["system_name"] for variable in self.selected_variables])
        )

        # Check if we have any variable IDs extracted from filter views
        if filter_view_variable_ids:
            # Get all variable IDs from selected variables
            all_variable_ids = set(
                [variable["variable_id"] for variable in self.selected_variables]
            )

            # Find variable IDs that are used in filters but not in selected variables
            # This handles complex cases like functions in filters where variable IDs are directly available
            diff_variable_ids = filter_view_variable_ids.difference(all_variable_ids)

            # Convert variable IDs back to system names for error reporting
            # This uses the mapping created earlier to maintain consistent error messages
            diff_variables_from_filters = [
                variable_id_to_system_name_map[variable_id]
                for variable_id in diff_variable_ids
                if variable_id in variable_id_to_system_name_map
            ]

            if len(diff_variables_from_filters) != len(diff_variable_ids):
                # this means user is trying to remove variables from transformation that are used in views
                # can't resolve system names from variable ids
                # because if the variable is removed from transformation, it will not be available in the variable_id_to_system_name_map
                raise DatasheetException(
                    code="INVALID_VARIABLES",
                    message="Cannot remove variables since it is used in views",
                )

            # Convert back to a set
            diff_variables = set(diff_variables_from_filters)

        # Check for unselected variables in pivot views
        if columns_used_in_pivot:
            diff_variables_from_pivot = columns_used_in_pivot.difference(
                set([variable["system_name"] for variable in self.selected_variables])
            )
            # Add unselected pivot variables to diff_variables
            diff_variables.update(diff_variables_from_pivot)

        if diff_variables:
            self._handle_invalid_variables(diff_variables, context, "views")

    def _extract_variable_ids_from_filter_data(self, filter_data):
        """
        Extract variable IDs from filter data using DatasheetExpressionBoxUtils.

        This handles complex filter data including functions with nested variables.

        Args:
            filter_data (list): The filter data to extract variable IDs from

        Returns:
            set: A set of variable IDs used in the filter
        """
        if not filter_data:
            return set()

        # Create meta_data structure expected by DatasheetExpressionBoxUtils
        meta_data = {"infix": filter_data}

        # Use DatasheetExpressionBoxUtils to extract variable IDs
        variable_ids = DatasheetExpressionBoxUtils(
            client_id=self.client_id,
            databook_id=self.databook_id,
            datasheet_id=self.datasheet_id,
            meta_data=meta_data,
            variables=self.variables,
            module="views",
        ).extract_variable_ids()

        return variable_ids

    def _is_unselected_variables_used_in_adjustments(self, context="DELETE"):
        columns_used = set()

        adjustment_data = list(
            datasheet_selectors.DatasheetAdjustmentSelector(client_id=self.client_id)
            .client_kd_deleted_aware()
            .filter(datasheet_id=self.datasheet_id)
            .values_list("data", flat=True)
        )

        for data in adjustment_data:
            if data:
                columns_used.update(data.keys())

        diff_variables = set(columns_used).difference(
            set([variable["system_name"] for variable in self.selected_variables])
        )
        if diff_variables:
            self._handle_invalid_variables(diff_variables, context, "adjustments")

    def _handle_invalid_variables(self, diff_variables, context, module):
        display_names = self._get_display_names_from_system_names(diff_variables)
        singular = False
        if len(diff_variables) == 1:
            singular = True
        context_name = "Modify" if context == "MODIFY" else "Remove"
        if singular:
            message = f"Cannot {context_name} Variable {display_names} since it is used in {module}"
        else:
            message = f"Cannot {context_name} Variables {display_names} since they are used in {module}"

        raise DatasheetException(
            code="INVALID_VARIABLES",
            message=message,
        )

    def _validate_circular_dependencies(self):
        cyclic_dependencies_res = DatasheetVariablesGraph(
            client_id=self.client_id,
            databook_id=self.databook_id,
            datasheet_id=self.datasheet_id,
            variables=self.selected_variables,
            expression_box_version=self.expression_box_version,
        ).get_cyclic_dependencies()

        if cyclic_dependencies_res:
            raise DatasheetException(
                code="CIRCULAR_DEPENDENCY",
                message=f"Circular dependency found between columns {cyclic_dependencies_res[0]} and {cyclic_dependencies_res[1]}",
            )

    def _is_unselected_variables_used_in_dependent_sheets(self, context="VALIDATE"):
        variable_ids = set(
            [str(variable["variable_id"]) for variable in self.unselected_variables]
        )
        if self.datasheet_graph is None:
            self.datasheet_graph = DataSheetGraph(
                client_id=self.client_id,
                include_stale_information_query=False,
            )

        descendants_datasheets: set = self.datasheet_graph.descendants(
            datasheet_id=str(self.datasheet_id)
        )
        datasheet_ids = [datasheet.node_id for datasheet in descendants_datasheets]

        dependency_variables = (
            DatasheetVariableSelector(client_id=self.client_id)
            .client_kd_deleted_aware()
            .filter(is_selected=True, source_variable_id__in=variable_ids)
            .filter(datasheet_id__in=datasheet_ids)
        )

        if not dependency_variables:
            # Check if the current variables on validation used in dependent datasheets
            dependency_variables = DatasheetVariableTemp.objects.filter(
                client_id=self.client_id,
                source_variable_id__in=variable_ids,
                is_selected=True,
                datasheet_id__in=datasheet_ids,
            )

        context_name = "delete" if context == "DELETE" else "unselect"

        if dependency_variables:
            display_names = {variable.display_name for variable in dependency_variables}
            raise DatasheetException(
                code="DEPENDENCY_EXISTS",
                message=f"Cannot {context_name} column {','.join(display_names)} since it's used in dependent sheets",
            )

        # check the used variable ids in transformations of dependent sheets
        datasheets = (
            DatasheetSelector(client_id=self.client_id)
            .client_kd_aware()
            .filter(datasheet_id__in=datasheet_ids)
        )

        for datasheet in datasheets:
            if datasheet.transformation_spec and (
                str(self.datasheet_id)
                in set(datasheet.transformation_spec[-1]["source_ids"])
                or str(self.datasheet_id) == str(datasheet.source_id)
            ):
                diff_variable_ids = variable_ids.intersection(
                    set(datasheet.transformation_spec[-1]["used_variable_ids"])
                )
                if diff_variable_ids:
                    diff_variables = [
                        variable
                        for variable in self.unselected_variables
                        if str(variable["variable_id"]) in diff_variable_ids
                    ]
                    display_names = self._get_display_names_from_variables(
                        diff_variables
                    )
                    raise DatasheetException(
                        code="DEPENDENCY_EXISTS",
                        message=f"Cannot {context_name} column {display_names} since it's used in dependent sheet {datasheet.name}",
                    )

            used_system_names = (
                self._get_system_names_from_hierarchy_variables_for_datasheet(datasheet)
            )

            diff_variables = used_system_names.intersection(
                set([variable["system_name"] for variable in self.unselected_variables])
            )
            if diff_variables:
                display_names = self._get_display_names_from_system_names(
                    diff_variables
                )
                raise DatasheetException(
                    code="DEPENDENCY_EXISTS",
                    message=f"Cannot {context_name} column {display_names} since it's used in dependent sheet {datasheet.name}",
                )

    def _get_system_names_from_hierarchy_variables_for_datasheet(self, datasheet):
        calculated_variables = list(
            (
                DatasheetVariableSelector(
                    client_id=self.client_id, datasheet_id=datasheet.datasheet_id
                )
                .get_selected_variables()
                .filter(field_order__gt=0)
            ).values()
        )
        hierarchy_variables = [
            variable
            for variable in calculated_variables
            if is_hierarchy_calculated_field(variable)
            and variable["meta_data"]["hierarchy"][
                HierarchyUtils.REFERENCE_SHEET_KEY.value
            ]
            == str(self.datasheet_id)
        ]
        used_system_names = set()
        for variable in hierarchy_variables:
            hierarchy_meta_data = variable["meta_data"]["hierarchy"]
            used_system_names.update(
                [
                    hierarchy_meta_data[HierarchyUtils.PARENT_COLUMN_KEY.value],
                    hierarchy_meta_data[HierarchyUtils.CHILD_COLUMN_KEY.value],
                    hierarchy_meta_data[HierarchyUtils.START_TIME_COLUMN_KEY.value],
                    hierarchy_meta_data[HierarchyUtils.END_TIME_COLUMN_KEY.value],
                ]
            )
        return used_system_names

    def _is_primary_keys_unselected(self):
        primary_keys = [
            variable for variable in self.unselected_variables if variable["is_primary"]
        ]
        if primary_keys:
            display_names = self._get_display_names_from_variables(primary_keys)
            raise DatasheetException(
                code="INVALID_VARIABLES",
                message=f"Primary columns {display_names} need to be selected",
            )

    def _validate_datasheet_transformations(self):
        # List of validation checks
        # 1. Check configured new transformations/modified transformations are valid
        # 2. Validate each transformations depending on the type
        # 3. Construct system names for transformation variables
        if not self.transformations:
            # if all the transformations are deleted
            if self._get_transformations():
                self.is_transformation_deleted = True
            return

        # Check if the some of transformations are deleted
        if len(self.transformations) < len(self._get_transformations()):
            self.is_transformation_deleted = True

        if all([transformation["is_valid"] for transformation in self.transformations]):
            self.transformation_output_variables = deepcopy(
                self.transformations[-1]["output_columns"]
            )
            flattened_variables = [
                variable
                for variable in self.transformation_output_variables
                if variable.get("flattened_col_name", None) is not None
            ]
            if flattened_variables:
                self.flatten_system_name = flattened_variables[0]["system_name"]

            return

        # Two flatten transformations cannot be present in the same datasheet
        if (
            len(
                [
                    transformation
                    for transformation in self.transformations
                    if transformation["type"] == TransformationType.FLATTEN.value
                ]
            )
            > 1
        ):
            raise DatasheetException(
                code="TRANSFORMATION_ERROR",
                message="Cannot add more than one Flatten Hierarchy transformation to a sheet",
            )

        ## if any of the transformation is invalid, validate all the subsequent transformations
        skip_validate_transformation = True
        self.is_transformation_invalid = True
        for transformation in self.transformations:
            skip_validate_transformation = (
                transformation["is_valid"] and skip_validate_transformation
            )
            if skip_validate_transformation:
                self.transformation_output_variables = deepcopy(
                    transformation["output_columns"]
                )
                self.used_variable_ids = set(transformation["used_variable_ids"])
                self.transformation_source_map = deepcopy(
                    transformation["transformation_source_map"]
                )
                self.source_ids = set(transformation["source_ids"])
                continue
            self.get_transformation_handler(transformation["type"])(transformation)
            transformation["is_valid"] = True
            self.transformation_output_variables = deepcopy(
                transformation["output_columns"]
            )
            self.transformation_source_map = deepcopy(
                transformation["transformation_source_map"]
            )
            transformation["output_columns"] = self._append_source_history_to_variables(
                transformation["output_columns"]
            )

        # Set is_transformation_invalid to True if ANY transformation is invalid (is_valid=False)
        # This flag prevents further processing and variable updates when transformations have errors
        # this change is to fix the issue where the evaluation context of window calc field set to NULL
        # Issue link: https://everstage.slack.com/archives/C04F5S9T2RL/p1740485864769769
        self.is_transformation_invalid = not all(
            t["is_valid"] for t in self.transformations
        )

    def _append_source_history_to_variables(self, variables):
        for variable in variables:
            if variable.get("source_name_history", None) is None:
                from everstage_ddd.datasheet.services.datasheet_service import (
                    fetch_source_display_name_history,
                )

                variable["source_name_history"] = fetch_source_display_name_history(
                    client_id=self.client_id,
                    source_id=variable["source_id"],
                    source_type=variable["source_type"],
                    source_variable_id=variable["source_variable_id"],
                    display_name=variable["display_name"],
                )

        return variables

    def get_transformation_handler(self, type):
        function_map = {
            TransformationType.JOIN.value: self._handle_join_transformation,
            TransformationType.FILTER.value: self._handle_filter_transformation,
            TransformationType.ADVANCED_FILTER.value: self._handle_advanced_filter_transformation,
            TransformationType.ADVANCED_FILTER_V2.value: self._handle_advanced_filter_transformation,
            TransformationType.SORT.value: self._handle_sort_transformation,
            TransformationType.GROUP_BY.value: self._handle_group_by_transformation,
            TransformationType.UNION.value: self._handle_union_transformation,
            TransformationType.FLATTEN.value: self._handle_flatten_transformation,
            TransformationType.TEMPORAL_SPLICE.value: self._handle_temporal_splice_transformation,
            TransformationType.GET_USER_PROPERTIES.value: self._handle_get_user_properties_transformation,
        }

        return function_map[type]

    def _handle_join_transformation(self, spec):
        self._validate_join_transformation(spec)
        self._construct_output_columns_for_join(spec)
        self.is_variable_reseted = True
        self.source_ids.add(str(spec["with"]))
        spec["source_ids"] = list(self.source_ids)

    def _validate_join_transformation(self, spec):
        if len(spec["on"]["lhs"]) != len(spec["on"]["rhs"]):
            raise DatasheetException(
                code="COLUMN_MISMATCH",
                message="Number of join keys and their data types should match in both datasheets",
            )
        for index, lhs_val in enumerate(spec["on"]["lhs"]):
            if lhs_val["data_type_id"] != spec["on"]["rhs"][index]["data_type_id"]:
                raise DatasheetException(
                    code="DATA_TYPE_MISMATCH",
                    message="Columns used to join sheets must have the same datatypes",
                )

        if spec["with"] is None:
            raise DatasheetException(
                code="INVALID_PARAMETER", message="Cannot join with empty datasheet"
            )

        used_variable_ids = set(
            [column["variable_id"] for column in spec["columns"]["lhs"]]
            + [column["variable_id"] for column in spec["on"]["lhs"]]
        )
        self._validate_variable_ids_present_in_transformation_output(used_variable_ids)
        rhs_used_variable_ids = set(
            [column["variable_id"] for column in spec["columns"]["rhs"]]
            + [column["variable_id"] for column in spec["on"]["rhs"]]
        )
        self.used_variable_ids = self.used_variable_ids.union(used_variable_ids).union(
            rhs_used_variable_ids
        )
        spec["used_variable_ids"] = list(self.used_variable_ids)

    def _construct_output_columns_for_join(self, spec):
        spec["output_columns"] = []
        self._handle_lhs_join_columns(spec)
        self._handle_rhs_join_columns(spec)

    def _handle_lhs_join_columns(self, spec):
        transformation_output_variable_id_map = self._build_variable_map(
            self.transformation_output_variables
        )

        for column in spec["columns"]["lhs"]:
            output_variable = transformation_output_variable_id_map[
                column["variable_id"]
            ]

            column["system_name"] = output_variable["system_name"]
            column["system_generated"] = output_variable.get("system_generated", False)
            column["source_id"] = output_variable["source_id"]
            column["source_type"] = output_variable["source_type"]
            column["is_primary"] = output_variable["is_primary"]

        for column in spec["on"]["lhs"]:
            output_variable = transformation_output_variable_id_map[
                column["variable_id"]
            ]
            column["system_name"] = output_variable["system_name"]
            column["source_id"] = output_variable["source_id"]
            column["source_type"] = output_variable["source_type"]
            column["is_primary"] = output_variable["is_primary"]

        columns = deepcopy(spec["columns"]["lhs"])

        for column in columns:
            column["system_name"] = f"lhs_{column['system_name']}"

        spec["output_columns"].extend(columns)

    def _handle_rhs_join_columns(self, spec):
        for column in spec["columns"]["rhs"]:
            if SAME_SOURCE_VAR in column["variable_id"]:
                column["variable_id"] = column["variable_id"].split(SAME_SOURCE_VAR)[0]

        columns = deepcopy(spec["columns"]["rhs"])
        rhs_source = f"{spec['with']}_{DatasheetSourceType.DATASHEET.value}"
        same_source = False
        if rhs_source not in self.transformation_source_map:
            self.transformation_source_map[rhs_source] = 0
        else:
            self.transformation_source_map[rhs_source] += 1
            same_source = True

        spec["transformation_source_map"] = deepcopy(self.transformation_source_map)

        variables = datasheet_variables_fetch(
            client_id=self.client_id,
            datasheet_id=spec["with"],
            only_selected=True,
        )
        variable_id_map = self._build_variable_map(variables.model_dump(by_alias=True))

        for column in columns:
            variable = variable_id_map[column["variable_id"]]
            column["system_name"] = f"rhs_{variable['system_name']}"
            if same_source:
                ## This is to avoid duplicate same variable ids in the output columns
                ## __ss__ -> same source
                column["variable_id"] = (
                    f"{column['variable_id']}{SAME_SOURCE_VAR}{self.transformation_source_map[rhs_source]}"
                )

            column["source_id"] = spec["with"]
            column["source_type"] = DatasheetSourceType.DATASHEET.value
            column["source_name_history"] = variable["source_name_history"]
        spec["output_columns"].extend(columns)

    def _handle_filter_transformation(self, spec):
        transformation_output_variable_id_map = self._build_variable_map(
            self.transformation_output_variables
        )
        output_variable = transformation_output_variable_id_map[spec["variable_id"]]
        if not output_variable:
            raise DatasheetException(
                code="MISSING_VARIABLE",
                message=f"Following columns need to be added to this sheet {spec['display_name']}",
            )

        spec["col_name"] = output_variable["system_name"]
        spec["output_columns"] = deepcopy(self.transformation_output_variables)
        self.used_variable_ids = self.used_variable_ids.union({spec["variable_id"]})
        spec["used_variable_ids"] = list(self.used_variable_ids)
        spec["transformation_source_map"] = deepcopy(self.transformation_source_map)
        spec["source_ids"] = list(self.source_ids)

    def _handle_advanced_filter_transformation(self, spec):
        add_meta_to_advanced_filter_infix(
            client_id=self.client_id,
            transformation_spec=[spec],
            context=AstMetaContext(
                databook_id=str(self.databook_id),
                datasheet_id=str(self.datasheet_id),
                additional_variables=self.transformation_output_variables,
            ),
        )
        ds_expression_box_utils = DatasheetExpressionBoxUtils(
            client_id=self.client_id,
            databook_id=str(self.databook_id),
            datasheet_id=str(self.datasheet_id),
            variables=self.transformation_output_variables,
            meta_data=spec,
        )

        used_variable_ids = ds_expression_box_utils.extract_variable_ids()

        output_variable_ids = set(
            [column["variable_id"] for column in self.transformation_output_variables]
        )
        diff_variables = used_variable_ids.difference(output_variable_ids)

        if diff_variables:
            display_names = self._get_display_names_from_source_variable_ids(
                diff_variables
            )
            raise DatasheetException(
                code="MISSING_VARIABLES",
                message=f"Variables {display_names} used in advanced fields are invalid",
            )

        spec["output_columns"] = deepcopy(self.transformation_output_variables)
        spec["source_ids"] = list(self.source_ids)
        self.used_variable_ids = self.used_variable_ids.union(used_variable_ids)
        spec["used_variable_ids"] = list(self.used_variable_ids)
        ds_expression_box_utils.transform_system_name()

    def _handle_sort_transformation(self, spec):
        spec["output_columns"] = deepcopy(self.transformation_output_variables)
        spec["transformation_source_map"] = deepcopy(self.transformation_source_map)
        spec["source_ids"] = list(self.source_ids)
        return True

    def _handle_group_by_transformation(self, spec):
        self._validate_group_by_transformation(spec)
        self._construct_columns_for_group_by_transformation(spec)
        spec["transformation_source_map"] = deepcopy(self.transformation_source_map)
        spec["source_ids"] = list(self.source_ids)

    def _validate_group_by_transformation(self, spec):
        used_variable_ids = set(
            [column["variable_id"] for column in spec["by"]]
            + [aggregation["variable_id"] for aggregation in spec["aggregations"]]
        )
        self._validate_variable_ids_present_in_transformation_output(used_variable_ids)
        self.used_variable_ids = self.used_variable_ids.union(used_variable_ids)
        spec["used_variable_ids"] = list(self.used_variable_ids)

    def _construct_columns_for_group_by_transformation(self, spec):
        self.is_variable_reseted = True
        trans_variable_map = self._build_variable_map(
            self.transformation_output_variables
        )
        # This is for existing transformation
        transformation_system_name_map = self._build_system_name_map(
            spec["output_columns"]
        )
        for column in spec["by"]:
            column["is_primary"] = True
            column["system_name"] = trans_variable_map[column["variable_id"]][
                "system_name"
            ]
            column["system_generated"] = trans_variable_map[column["variable_id"]].get(
                "system_generated", False
            )

        output_columns = deepcopy(spec["by"])
        system_name_map = self._build_system_name_map(self.variables)
        for aggregation in spec["aggregations"]:
            column_name = trans_variable_map[aggregation["variable_id"]]["system_name"]
            display_name = trans_variable_map[aggregation["variable_id"]][
                "display_name"
            ]
            aggregation["of"] = column_name
            aggregation["col_name"] = f"{aggregation['function'].lower()}_{column_name}"
            data_type_id = (
                get_data_types()[
                    GroupByAggDatatypesMap[aggregation["function"]].upper()
                ]
                if GroupByAggDatatypesMap[aggregation["function"]] is not None
                else trans_variable_map[aggregation["variable_id"]]["data_type_id"]
            )
            aggregation["data_type_id"] = data_type_id
            variable_id = (
                transformation_system_name_map.get(aggregation["col_name"], {}).get(
                    "variable_id"
                )
                or system_name_map.get(aggregation["col_name"], {}).get("variable_id")
                or uuid4()
            )
            display_name = (
                system_name_map.get(aggregation["col_name"], {}).get("display_name")
                or transformation_system_name_map.get(aggregation["col_name"], {}).get(
                    "display_name"
                )
                or f"{aggregation['function']}::{display_name}"
            )
            output_columns.append(
                {
                    "system_name": aggregation["col_name"],
                    "column_name": aggregation["col_name"],
                    "display_name": display_name,
                    "data_type_id": data_type_id,
                    "variable_id": variable_id,
                    "source_id": str(self.datasheet_id),
                    "source_type": DatasheetSourceType.DATASHEET.value,
                    "source_variable_id": None,
                    "is_selected": True,
                    "is_primary": False,
                    "source_cf_meta_data": None,
                    "meta_data": None,
                    "system_generated": True,
                }
            )
        spec["output_columns"] = output_columns

    def _handle_union_transformation(self, spec):
        if any(
            [
                len(column["lhs"]) == 0 or len(column["rhs"]) == 0
                for column in spec["on"]
            ]
        ):
            raise DatasheetException(
                code="COLUMN_MISMATCH",
                message="Number of look up columns on lhs and rhs should be same in union transformation",
            )

        for column in spec["on"]:
            lhs_column = column["lhs"]
            rhs_column = column["rhs"]
            if lhs_column["data_type_id"] != rhs_column["data_type_id"]:
                raise DatasheetException(
                    code="DATA_TYPE_MISMATCH",
                    message=f"Data types of {lhs_column['display_name']} and {rhs_column['display_name']} should be same in union",
                )

        used_variable_ids = set([column["lhs"]["variable_id"] for column in spec["on"]])
        self._validate_variable_ids_present_in_transformation_output(used_variable_ids)
        rhs_variable_ids = set([column["rhs"]["variable_id"] for column in spec["on"]])
        self.used_variable_ids = self.used_variable_ids.union(used_variable_ids).union(
            rhs_variable_ids
        )
        spec["used_variable_ids"] = list(self.used_variable_ids)
        trans_variable_map = self._build_variable_map(
            self.transformation_output_variables
        )
        spec["transformation_source_map"] = deepcopy(self.transformation_source_map)
        for column in spec["on"]:
            variable = trans_variable_map[column["lhs"]["variable_id"]]
            column["lhs"]["system_name"] = variable["system_name"]
            column["col_name"] = variable["system_name"]
            column["lhs"]["system_generated"] = variable.get("system_generated", False)

        spec["output_columns"] = [
            column["lhs"] | {"is_primary": True} for column in spec["on"]
        ]
        self.is_variable_reseted = True
        self.source_ids.add(str(spec["with"]))
        spec["source_ids"] = list(self.source_ids)

    def _handle_flatten_transformation(self, spec):
        self._validate_variable_ids_present_in_transformation_output(
            {spec["variable_id"]}
        )
        self.used_variable_ids = self.used_variable_ids.union({spec["variable_id"]})
        spec["used_variable_ids"] = list(self.used_variable_ids)
        spec["output_columns"] = deepcopy(self.transformation_output_variables)
        spec["source_ids"] = list(self.source_ids)
        self._construct_flatten_output_column_name(spec)
        self._construct_flatten_variables(spec)
        self.is_variable_added = True

    def _construct_flatten_output_column_name(self, spec):
        hierarchy_source_id = list(
            filter(
                lambda variable: variable["system_name"] == spec["col_name"],
                self.transformation_output_variables,
            )
        )[0]["source_id"]
        source_id = hierarchy_source_id[-4:]
        spec["flattened_col_name"] = (
            f"{spec['col_name']}_{spec['output_data_type'].lower()}_{source_id}"
        )
        self.flatten_system_name = spec["flattened_col_name"]

    def _construct_flatten_variables(self, spec):
        trans_variable_map = self._build_variable_map(
            self.transformation_output_variables
        )
        spec["col_name"] = trans_variable_map[spec["variable_id"]]["system_name"]
        # ToDo: change to reliable logic without removing lhs_ or rhs_
        system_name_map = self._build_system_name_map_without_lhs_rhs(self.variables)
        existing_variable = system_name_map.get(
            spec["flattened_col_name"].replace("lhs_", "").replace("rhs_", ""), {}
        )
        variable_id = existing_variable.get("variable_id") or uuid4()
        spec["transformation_source_map"] = deepcopy(self.transformation_source_map)

        hierarchy_display_name = list(
            filter(
                lambda variable: variable["system_name"] == spec["col_name"],
                self.transformation_output_variables,
            )
        )[0]["display_name"]

        display_name = (
            existing_variable["display_name"]
            if existing_variable.get("display_name")
            else hierarchy_display_name
            + " as "
            + spec["output_data_type"]
            + "-flattened"
        )

        spec["output_columns"].append(
            {
                "system_name": spec["flattened_col_name"],
                "display_name": display_name,
                "data_type_id": get_data_types()[spec["output_data_type"].upper()],
                "variable_id": variable_id,
                "is_selected": True,
                "is_primary": True,
                "source_id": str(self.datasheet_id),
                "source_type": DatasheetSourceType.DATASHEET.value,
                "source_variable_id": None,
                "source_cf_meta_data": None,
                "meta_data": None,
                "system_generated": True,
            }
        )

    def _handle_temporal_splice_transformation(self, spec):
        self._validate_temporal_splice_transformation(spec)
        self._construct_variable_for_temporal_splice_transformations(spec)
        self.is_variable_reseted = True
        self.used_variable_ids = self.used_variable_ids.union(
            set(spec["used_variable_ids"])
        )
        spec["used_variable_ids"] = list(self.used_variable_ids)
        spec["source_ids"] = list(self.source_ids)

    def _validate_temporal_splice_transformation(self, spec):
        data_source = spec["meta"][0]
        used_variable_ids = [data_source["email_id_column"]["variable_id"]]
        if data_source["has_effective_dates"]:
            if (
                data_source["start_date_column"]["value"] is None
                and data_source["end_date_column"]["value"] is None
            ):
                raise DatasheetException(
                    code="INVALID_PARAMETER",
                    message="Start date and end date columns are not selected",
                )

            if data_source["start_date_column"]["value"]:
                used_variable_ids.append(
                    data_source["start_date_column"]["variable_id"]
                )
            if data_source["end_date_column"]["value"]:
                used_variable_ids.append(data_source["end_date_column"]["variable_id"])

        self._validate_variable_ids_present_in_transformation_output(
            set(used_variable_ids)
        )
        spec["used_variable_ids"] = used_variable_ids

    def _construct_variable_for_temporal_splice_transformations(self, spec):
        # ToDo: change to reliable logic without removing lhs_ or rhs_
        system_name_map = self._build_system_name_map_without_lhs_rhs(self.variables)
        # This is for existing transformation
        transformation_system_name_map = self._build_system_name_map(
            spec["output_columns"]
        )

        spec["output_columns"] = [
            {
                "system_name": "ts_employee_email_id",
                "display_name": system_name_map.get("ts_employee_email_id", {}).get(
                    "display_name"
                )
                or transformation_system_name_map.get("ts_employee_email_id", {}).get(
                    "display_name"
                )
                or "TS::Employee Email Id",
                "data_type_id": 12,
                "variable_id": transformation_system_name_map.get(
                    "ts_employee_email_id", {}
                ).get("variable_id")
                or system_name_map.get("ts_employee_email_id", {}).get("variable_id")
                or uuid4(),
                "is_selected": True,
                "is_primary": True,
                "source_id": str(self.datasheet_id),
                "source_type": DatasheetSourceType.DATASHEET.value,
                "source_variable_id": None,
                "source_cf_meta_data": None,
                "meta_data": None,
                "system_generated": True,
            },
            {
                "system_name": "ts_effective_start_date",
                "display_name": system_name_map.get("ts_effective_start_date", {}).get(
                    "display_name"
                )
                or transformation_system_name_map.get(
                    "ts_effective_start_date", {}
                ).get("display_name")
                or "TS::Effective Start Date",
                "data_type_id": 2,
                "variable_id": transformation_system_name_map.get(
                    "ts_effective_start_date", {}
                ).get("variable_id")
                or system_name_map.get("ts_effective_start_date", {}).get("variable_id")
                or uuid4(),
                "is_selected": True,
                "is_primary": True,
                "source_id": str(self.datasheet_id),
                "source_type": DatasheetSourceType.DATASHEET.value,
                "source_variable_id": None,
                "source_cf_meta_data": None,
                "meta_data": None,
                "system_generated": True,
            },
            {
                "system_name": "ts_effective_end_date",
                "display_name": system_name_map.get("ts_effective_end_date", {}).get(
                    "display_name"
                )
                or transformation_system_name_map.get("ts_effective_end_date", {}).get(
                    "display_name"
                )
                or "TS::Effective End Date",
                "data_type_id": 2,
                "variable_id": transformation_system_name_map.get(
                    "ts_effective_end_date", {}
                ).get("variable_id")
                or system_name_map.get("ts_effective_end_date", {}).get("variable_id")
                or uuid4(),
                "is_selected": True,
                "is_primary": True,
                "source_id": str(self.datasheet_id),
                "source_type": DatasheetSourceType.DATASHEET.value,
                "source_variable_id": None,
                "source_cf_meta_data": None,
                "meta_data": None,
                "system_generated": True,
            },
        ]

        temporal_id_variable_ids = set(
            [variable["variable_id"] for variable in spec["output_columns"]]
        )

        from everstage_ddd.datasheet.services.datasheet_service import (
            datasheet_fetch_source_variables,
        )

        for index, data_source in enumerate(spec["meta"]):
            if index == 0:
                data_source["input_columns"] = deepcopy(
                    self.transformation_output_variables
                )
                output_variable_id_map = self._build_variable_map(
                    data_source["input_columns"]
                )
                data_source["email_id_column"]["value"] = output_variable_id_map[
                    data_source["email_id_column"]["variable_id"]
                ]["system_name"]

                if data_source["has_effective_dates"]:
                    if data_source["start_date_column"]["value"]:
                        data_source["start_date_column"]["value"] = (
                            output_variable_id_map[
                                data_source["start_date_column"]["variable_id"]
                            ]["system_name"]
                        )

                    if data_source["end_date_column"]["value"]:
                        data_source["end_date_column"]["value"] = (
                            output_variable_id_map[
                                data_source["end_date_column"]["variable_id"]
                            ]["system_name"]
                        )

            else:
                data_source["input_columns"] = datasheet_fetch_source_variables(
                    client_id=self.client_id,
                    source_id=data_source["source_id"],
                    source_type=data_source["source_type"],
                    only_selected=True,
                )
                self.source_ids.add(str(data_source["source_id"]))
            source = f"{data_source['source_id']}_{data_source['source_type']}"
            if source not in self.transformation_source_map:
                self.transformation_source_map[source] = 0
            self.transformation_source_map[source] += 1
            spec["transformation_source_map"] = deepcopy(self.transformation_source_map)

            variable_ids_to_be_removed = set(
                [data_source["email_id_column"]["variable_id"]]
            )

            if data_source["has_effective_dates"]:
                if data_source["start_date_column"]["value"]:
                    variable_ids_to_be_removed.add(
                        data_source["start_date_column"]["variable_id"]
                    )

                if data_source["end_date_column"]["value"]:
                    variable_ids_to_be_removed.add(
                        data_source["end_date_column"]["variable_id"]
                    )

            spec["used_variable_ids"].extend(list(variable_ids_to_be_removed))

            data_source["output_columns"] = []
            for variable in data_source["input_columns"]:
                variable["system_name"] = f"ts_{index + 1}_{variable['system_name']}"
                variable["is_primary"] = False
                # For the first data source, source_id and source_type will be
                # computed from previous transformation variables
                # For the subsequent data sources, source_id and source_type will be computed
                if index != 0:
                    variable["source_id"] = data_source["source_id"]
                    variable["source_type"] = data_source["source_type"]
                elif variable["variable_id"] in temporal_id_variable_ids:  # for index 0
                    new_variable_id = uuid4()
                    if variable["variable_id"] in variable_ids_to_be_removed:
                        variable_ids_to_be_removed.add(new_variable_id)
                        variable_ids_to_be_removed.remove(variable["variable_id"])

                    variable["variable_id"] = new_variable_id

                if str(variable["variable_id"]) not in variable_ids_to_be_removed:
                    data_source["output_columns"].append(variable)

            spec["output_columns"].extend(data_source["output_columns"])
            spec["transformation_source_map"] = deepcopy(self.transformation_source_map)

    def _handle_get_user_properties_transformation(self, spec):
        self._validate_user_properties_transformation(spec)
        source = f"user_{DatasheetSourceType.REPORT_OBJECT.value}"
        if source not in self.transformation_source_map:
            self.transformation_source_map[source] = 0
        else:
            self.transformation_source_map[source] += 1

        self.source_ids.add("user")
        spec["source_ids"] = list(self.source_ids)
        spec["transformation_source_map"] = deepcopy(self.transformation_source_map)

        self._construct_user_property_output_variable(spec)
        self.is_variable_added = True

    def _validate_user_properties_transformation(self, spec):
        used_variable_ids = [
            spec["email_column"]["variable_id"],
        ]
        if spec["as_of_date_column"]["variable_id"] is not None:
            used_variable_ids.append(spec["as_of_date_column"]["variable_id"])

        self._validate_variable_ids_present_in_transformation_output(
            set(used_variable_ids)
        )
        self.used_variable_ids = self.used_variable_ids.union(set(used_variable_ids))
        spec["used_variable_ids"] = list(self.used_variable_ids)

    def _construct_user_property_output_variable(self, spec):
        output_variable_id_map = self._build_variable_map(
            self.transformation_output_variables
        )
        same_source = False
        source = f"user_{DatasheetSourceType.REPORT_OBJECT.value}"

        if spec["transformation_source_map"][source] > 0:
            same_source = True

        spec["email_column"]["value"] = output_variable_id_map[
            spec["email_column"]["variable_id"]
        ]["system_name"]

        if spec["as_of_date_column"]["variable_id"] is not None:
            spec["as_of_date_column"]["value"] = output_variable_id_map[
                spec["as_of_date_column"]["variable_id"]
            ]["system_name"]

        spec["output_columns"] = deepcopy(self.transformation_output_variables)
        for user_property in spec["user_properties"]:
            user_property["output_variable_system_name"] = "_".join(
                [
                    "up",
                    spec["email_column"]["value"],
                    spec["as_of_date_column"]["value"],
                    user_property["user_property_system_name"],
                    spec["key"][-4:],
                ]
            ).lower()
            spec["output_columns"].append(
                {
                    "system_name": user_property["output_variable_system_name"],
                    "display_name": user_property["display_name"],
                    "data_type_id": user_property["data_type_id"],
                    "variable_id": (
                        user_property["user_property_system_name"] + "_user"
                        if not same_source
                        else user_property["user_property_system_name"]
                        + "_user"
                        + SAME_SOURCE_VAR
                        + str(spec["transformation_source_map"][source])
                    ),
                    "is_selected": True,
                    "is_primary": False,
                    "source_id": "user",
                    "source_type": DatasheetSourceType.REPORT_OBJECT.value,
                    "source_name_history": user_property["display_name"]
                    + "<<"
                    + "user",
                    "source_variable_id": None,
                    "source_cf_meta_data": None,
                    "meta_data": None,
                }
            )

    def _populate_validated_transformations(self):
        datasheet_selectors.DatasheetTransformationSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        ).populate_transformations(self.transformations)

    def _populate_validated_variables(self):
        DatasheetVariableTemp.objects.filter(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        ).delete()
        variables = DatasheetVariablesList(self.variables).model_dump(by_alias=True)
        DatasheetVariableTemp.objects.bulk_create(
            [
                DatasheetVariableTemp(
                    **variable,
                    client_id=self.client_id,
                    datasheet_id=self.datasheet_id,
                    databook_id=self.databook_id,
                )
                for variable in variables
            ]
        )

    def _populate_temp_variables_from_source(self, additional_variables: list):
        DatasheetVariableTemp.objects.filter(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        ).delete()
        variables = self._get_saved_variables()
        DatasheetVariableTemp.objects.bulk_create(
            [
                DatasheetVariableTemp(
                    **variable,
                    client_id=self.client_id,
                    datasheet_id=self.datasheet_id,
                    databook_id=self.databook_id,
                )
                for variable in variables + additional_variables
            ]
        )

    def _get_saved_variables(self):
        variables = DatasheetVariableSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        ).get_variables()
        variables = DatasheetVariablesList(variables).model_dump(by_alias=True)
        return variables

    def _populate_transformations_from_source(self):
        datasheet_selectors.DatasheetTransformationSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        ).populate_transformations(self.datasheet.transformation_spec)

    def delete_transformations_from_temp(self):
        datasheet_selectors.DatasheetTransformationSelector(
            client_id=self.client_id, datasheet_id=self.datasheet_id
        ).get_transformations().delete()

    def _add_field_order_to_variables(self):
        # Don't compute the field order for flattened variable
        variables = [
            variable
            for variable in self.selected_variables
            if variable["system_name"] != self.flatten_system_name
        ]

        datasheet_variables_graph = DatasheetVariablesGraph(
            client_id=self.client_id,
            databook_id=self.databook_id,
            datasheet_id=str(self.datasheet_id),
            variables=variables,
            expression_box_version=self.expression_box_version,
        )

        (
            field_order_records,
            evaluation_context_dict,
        ) = (
            datasheet_variables_graph.compute_variables_field_order_and_evaluation_context()
        )
        for variable in variables:
            variable_meta_data = variable.get("meta_data", None)
            if (
                variable_meta_data
                and variable["system_name"] in evaluation_context_dict
            ):
                variable_meta_data["evaluation_context"] = evaluation_context_dict[
                    variable["system_name"]
                ]
            variable["field_order"] = field_order_records[variable["system_name"]]

    def _add_ast_to_calculated_fields(self):
        non_cf_variables = [
            variable
            for variable in self.variables
            if not self.is_calculated_field(variable)
        ]
        selected_cf_variables = [
            variable for variable in self.cf_variables if variable["is_selected"]
        ]
        insert_ast_in_meta_data(
            client_id=self.client_id,
            databook_id=str(self.databook_id),
            datasheet_id=str(self.datasheet_id),
            variables=non_cf_variables + selected_cf_variables,
            expression_box_version=self.expression_box_version,
        )

    def _add_source_cf_meta_data_to_variables(self):
        for variable in self.selected_variables:
            if is_hierarchy_calculated_field(variable):
                variable["source_cf_meta_data"] = {
                    "datasheet_id": self.datasheet_id,
                    "hierarchy_for_data_type_id": variable["meta_data"]["hierarchy"][
                        "hierarchy_for_data_type_id"
                    ],
                }
            else:
                variable["source_cf_meta_data"] = variable.get("source_cf_meta_data")

    def _get_variables(self):
        return list(
            DatasheetVariableTemp.objects.filter(
                client_id=self.client_id, datasheet_id=self.datasheet_id
            ).values()
        )

    def _get_transformations(self):
        return list(
            DatasheetTransformationSelector(
                client_id=self.client_id, datasheet_id=self.datasheet_id
            )
            .get_transformations()
            .order_by("order")
            .values("spec")
        )

    def _get_display_names_from_source_variable_ids(self, variable_ids: set):
        return self._get_display_names_from_variables(
            [
                variable
                for variable in self.orig_variables
                if variable["source_variable_id"] in variable_ids
            ]
        )

    def _get_display_names_from_variable_ids(self, variable_ids: set):
        return self._get_display_names_from_variables(
            [
                variable
                for variable in self.variables
                if variable["variable_id"] in variable_ids
            ]
        )

    def _get_display_names_from_variables(self, variables: list[dict]):
        return ",".join({variable["display_name"] for variable in variables})

    def _get_display_names_from_system_names(self, system_names: set):
        return ",".join(
            variable["display_name"]
            for variable in self.orig_variables
            if variable["system_name"] in system_names
        )

    def _build_variable_map(self, variables: list[dict]):
        return {
            str(variable["variable_id"]): variable
            for variable in variables
            if variable["variable_id"] is not None
        }

    def _build_system_name_map(self, variables):
        return {variable["system_name"]: variable for variable in variables}

    def _build_system_name_map_without_lhs_rhs(self, variables):
        return {
            variable["system_name"].replace("lhs_", "").replace("rhs_", ""): variable
            for variable in variables
        }

    def _append_prefix_to_system_names(self, columns, prefix):
        for column in columns:
            column["system_name"] = f"{prefix}_{column['system_name']}"

    def _convert_window_calculated_fields(self):
        non_cf_variables = [
            variable
            for variable in self.variables
            if not self.is_calculated_field(variable)
        ]
        selected_cf_variables = [
            variable for variable in self.cf_variables if variable["is_selected"]
        ]
        convert_window_func_cf_variables(
            self.client_id,
            non_cf_variables + selected_cf_variables,
            databook_id=self.databook_id,
            datasheet_id=self.datasheet_id,
            requires_intermediate_variables=True,
        )

    def _build_source_variable_id_map(self, variables):
        return {
            variable["source_variable_id"]: variable
            for variable in variables
            if variable["source_variable_id"] is not None
        }

    def _construct_variables_from_transformations(self):
        source_variable_id_map = self._build_source_variable_id_map(self.variables)
        system_name_map = self._build_system_name_map_without_lhs_rhs(self.variables)
        base_variable_map = self._build_variable_map(self.source_variables)
        used_variable_ids = set()
        used_display_names = set()
        for variable in self.transformation_output_variables:
            # Variable id will not be system_name in case of custom object/report object variables
            variable_id = variable["variable_id"]
            # Flip the source_variable_id and variable_id to make the variable as current datasheet variables
            # System generated variables have temp variable id which cannot be traced back so setting source variable id as None
            # Source id and source type needs to be set correctly for variable at transformation level
            if not variable.get("system_generated", False):
                variable["source_variable_id"] = str(variable_id)
                existing_variable = source_variable_id_map.get(variable_id)
                if not existing_variable:
                    variable["variable_id"] = uuid4()
                    if base_variable_map.get(variable_id):
                        variable["source_id"] = self.source_id
                        variable["source_type"] = self.source_type

                else:
                    # If the variable is already present in the datasheet, then use the existing variable config
                    if existing_variable["variable_id"] in used_variable_ids:
                        variable["variable_id"] = uuid4()
                    else:
                        variable["variable_id"] = existing_variable["variable_id"]
                        variable["display_name"] = existing_variable["display_name"]
                    variable["source_id"] = existing_variable["source_id"]
                    variable["source_type"] = existing_variable["source_type"]
            else:
                existing_variable = system_name_map.get(
                    variable["system_name"].replace("lhs_", "").replace("rhs_", ""), {}
                )
            # if Variable is primary then it should be selected
            # if it is not existing variable then it should be selected
            # if it is existing variable then it should be selected if it is already selected
            # else it would be unselected.
            variable["is_selected"] = variable["is_primary"] or (
                not existing_variable or existing_variable.get("is_selected", False)
            )
            variable["field_order"] = (
                0  # Reset the field order for the variables from transformation
            )
            variable["meta_data"] = None
            if variable["display_name"].lower() in used_display_names:
                # Add source name to display name source_name :: display_name
                variable["display_name"] = (
                    DatasheetSelector.construct_variable_display_name(
                        self.client_id, variable, used_display_names
                    )
                )
            used_variable_ids.add(variable["variable_id"])
            used_display_names.add(variable["display_name"].lower())

        self.variables = (
            deepcopy(self.transformation_output_variables) + self.cf_variables
        )

    def _construct_variables_from_source(self):
        variables = deepcopy(self.source_variables)
        for variable in variables:
            variable["variable_id"], variable["source_variable_id"] = (
                uuid4(),
                str(variable["variable_id"]),
            )
            variable["source_id"] = self.source_id
            variable["source_type"] = self.source_type
            # Reset the field order and meta_data for the variables coming from source
            variable["field_order"] = 0
            variable["meta_data"] = None
        self.variables = variables + self.cf_variables

    def _validate_variable_ids_present_in_transformation_output(
        self, used_variable_ids
    ):
        output_variable_ids = self._build_variable_map(
            self.transformation_output_variables
        )
        diff_keys = used_variable_ids.difference(set(output_variable_ids.keys()))
        if diff_keys:
            get_display_names = self._get_display_names_from_source_variable_ids(
                diff_keys
            )
            raise DatasheetException(
                code="INVALID_VARIABLES",
                message=f"Fields {get_display_names} used in transformation is not found in this sheet",
            )

    def _transform_calculated_fields(self):
        selected_cf_variables = [
            variable for variable in self.cf_variables if variable["is_selected"]
        ]
        if not selected_cf_variables:
            return

        for cf_variable in selected_cf_variables:
            DatasheetExpressionBoxUtils(
                client_id=self.client_id,
                databook_id=self.databook_id,
                datasheet_id=self.datasheet_id,
                meta_data=cf_variable["meta_data"],
                variables=self.variables,
            ).transform_system_name()

    def _update_warnings_for_auto_enriched_variables(self):
        """
        Update the warnings for auto_enrich_variables.

        This method updates the warnings for auto_enrich_variables based on the current state of the datasheet.

        Args:
            variables (list): List of variables to update.

        Returns:
            variables (list): The updated list of variables.
        """
        from everstage_ddd.datasheet.services.datasheet_variables import (
            get_auto_enrich_variables,
        )

        if not can_run_auto_enrich_report(self.client_id):
            return

        # 1. Extract the system_name of the comm report variables of opened sheet
        ds_comm_report_variables = set()
        for variable in self.variables:
            if variable["source_type"] == DatasheetSourceType.REPORT_OBJECT.value and (
                variable["source_id"] in {"commission", "inter_commission"}
            ):
                ds_comm_report_variables.add(variable["system_name"])

        # Return the variables if there are no comm report variables in the opened sheet
        if not ds_comm_report_variables:
            return

        # 2. Extract the system_name of the predefined and enrichment variables
        comm_report_variables_set = set()
        predefined_comm_report_variables = (
            EverObjectVariableAccessor().get_ever_object_variable(
                object_id="commission"
            )
        )
        cre_variables = get_auto_enrich_variables(self.client_id)
        comm_report_variables_set.update(
            {var["system_name"] for var in predefined_comm_report_variables}
        )
        comm_report_variables_set.update({var["system_name"] for var in cre_variables})

        # 3. Find the deleted or removed comm report vars that are prsent in the opened sheet
        set_diff_ds_comm_report_variables = ds_comm_report_variables.difference(
            comm_report_variables_set
        )
        module_logger.info(
            "Variables with Warning : %s", set_diff_ds_comm_report_variables
        )

        if len(set_diff_ds_comm_report_variables) > 0:
            for variable in self.variables:
                if variable["system_name"] in set_diff_ds_comm_report_variables:
                    variable["warning"] = (
                        "This variable is not present in the Commission Report object."
                    )

    def _update_output_columns_display_name(self):
        """
        Updates the display names of output columns based on
        self.variables (current datasheet variables).

        This is to maintain the consistency of the display names
        of the output columns and the variables.
        """
        if not self.transformations:
            return

        system_name_map = self._build_system_name_map(self.variables)

        last_transformation = self.transformations[-1]

        for variable in last_transformation.get("output_columns", []):
            system_name = variable.get("system_name")
            if not system_name:
                continue

            mapped_variable = system_name_map.get(system_name)
            if mapped_variable:
                variable["display_name"] = mapped_variable["display_name"]
