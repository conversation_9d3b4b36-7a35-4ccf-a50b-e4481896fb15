import json
import logging
import os

import requests

from commission_engine.utils.s3_utils import S3Uploader
from everstage_infra.aws_infra.ecs import is_prod_env
from ms_teams_everstage.accessors.msteams_admin_token_details_accessor import (
    MsteamsAdminTokenDetailsAccessor,
)

MSTEAMS_LOGIN_URL = "https://login.microsoftonline.com/{}/oauth2/v2.0/token"
GRAPH_API_URL = "https://graph.microsoft.com/v1.0/{}"

logger = logging.getLogger(__name__)

if is_prod_env():
    S3_BUCKET_NAME = "everstage-msteams-prod"
else:
    S3_BUCKET_NAME = "everstage-msteams"


def get_admin_access_token(code, login_email_id):
    try:
        url = MSTEAMS_LOGIN_URL.format("common")
        payload = f"client_id={os.getenv('MSTEAMS_CLIENT_ID')}&scope=https%3A%2F%2Fgraph.microsoft.com%2F.default&client_secret={os.getenv('MSTEAMS_CLIENT_SECRET')}&grant_type=authorization_code&code={code}&redirect_uri={os.getenv('URL')}/settings/notifications"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        response = requests.post(url, headers=headers, data=payload)
        return response.json().get("access_token", None), response.json().get(
            "refresh_token", None
        )
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Error when ms teams admin access token for %s %s",
            login_email_id,
            e,
        )
        raise Exception from e


def get_admin_access_token_from_refresh_token(
    client_id,
) -> tuple[str | None, str | None]:
    try:
        token_details = MsteamsAdminTokenDetailsAccessor(client_id).get()
        if not token_details:
            logger.error(
                "APP_INT_EXCEPTION: Token not found for client %s, Skipping ms teams install for new users",
                client_id,
            )
            raise Exception(
                f"Token not found for client {client_id}, Skipping ms teams install for new users"
            )
        tenant_id = token_details.tenant_id
        refresh_token = token_details.admin_refresh_token

        url = MSTEAMS_LOGIN_URL.format(tenant_id)
        payload = f"client_id={os.getenv('MSTEAMS_CLIENT_ID')}&scope=https%3A%2F%2Fgraph.microsoft.com%2F.default&client_secret={os.getenv('MSTEAMS_CLIENT_SECRET')}&grant_type=refresh_token&refresh_token={refresh_token}"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        response = requests.post(url, headers=headers, data=payload)
        logger.info(
            "[MS_TEAMS_INSTALL] Admin access token response: %s",
            response.json(),
        )
        return response.json().get("access_token", None), response.json().get(
            "refresh_token", None
        )
    except Exception as e:  # pylint: disable=broad-except
        logger.error(
            "APP_INT_EXCEPTION: Error when ms refreshing admin access token for %s",
            e,
        )
        return None, None


def get_tenant_access_token(tenant_id):
    try:
        url = MSTEAMS_LOGIN_URL.format(tenant_id)
        payload = f"client_id={os.getenv('MSTEAMS_CLIENT_ID')}&scope=https://graph.microsoft.com/.default&client_secret={os.getenv('MSTEAMS_CLIENT_SECRET')}&grant_type=client_credentials"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        response = requests.post(url, headers=headers, data=payload)
        logger.info(
            "[MS_TEAMS_INSTALL] Tenant access token response: %s",
            response.json(),
        )
        if response.status_code == 200:
            return response.json().get("access_token", None)
        else:
            raise Exception(response.json())
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Error when ms teams tenant access token for %s %s",
            tenant_id,
            e,
        )
        raise Exception from e


def publish_app_to_catalog(access_token):
    try:
        url = GRAPH_API_URL.format("appCatalogs/teamsApps?requiresReview=false")
        app_package_file = f"{os.getenv('MSTEAMS_APP_EXTERNAL_ID')}.zip"
        S3Uploader(logger=logger).download_file(
            app_package_file, app_package_file, S3_BUCKET_NAME
        )
        headers = {
            "Content-Type": "application/zip",
            "Authorization": f"Bearer {access_token}",
        }
        app_package = open(app_package_file, "rb").read()
        response = requests.post(url, headers=headers, data=app_package)
        if response.status_code in [200, 201, 409]:
            return response
        else:
            raise Exception(response.json())
    except Exception as e:
        logger.error("APP_INT_EXCEPTION: Error when publishing teams app %s", e)
        raise Exception from e


def update_published_app_to_catalog(access_token, app_catalog_id):
    try:
        url = GRAPH_API_URL.format(
            f"appCatalogs/teamsApps/{app_catalog_id}/appDefinitions?requiresReview=false"
        )
        app_package_file = f"{os.getenv('MSTEAMS_APP_EXTERNAL_ID')}.zip"
        S3Uploader(logger=logger).download_file(
            app_package_file, app_package_file, S3_BUCKET_NAME
        )
        headers = {
            "Content-Type": "application/zip",
            "Authorization": f"Bearer {access_token}",
        }
        app_package = open(app_package_file, "rb").read()
        response = requests.post(url, headers=headers, data=app_package)
        if response.status_code in [200, 201, 409]:
            return response
        else:
            raise Exception(response.json())
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Error when updating publishing teams app %s",
            e,
        )
        raise Exception from e


def update_app_installed_for_user(access_token, user_id, app_catalog_id):
    try:
        url = GRAPH_API_URL.format(
            f"users/{user_id}/teamwork/installedApps?$expand=teamsApp&$filter=teamsApp/id eq '{app_catalog_id}'"
        )
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            if response.json().get("value"):
                app_installation_id = response.json().get("value").pop()["id"]
                url = GRAPH_API_URL.format(
                    f"users/{user_id}/teamwork/installedApps/{app_installation_id}/upgrade"
                )
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                }
                response = requests.post(url, headers=headers)
                if response.status_code in [200, 201, 409, 204]:
                    logger.info(
                        "[MS_TEAMS_INSTALL] App installed for user %s : %s",
                        user_id,
                        response.json(),
                    )
                    return response
                else:
                    raise Exception(response.json())
            else:
                return install_app_for_user(access_token, user_id, app_catalog_id)
        else:
            raise Exception(response.json())
    except Exception as e:
        logger.error("APP_INT_EXCEPTION: Error when publishing teams app %s", e)
        raise Exception from e


def get_app_details(access_token):
    try:
        url = GRAPH_API_URL.format(
            f"/appCatalogs/teamsApps?$filter=externalId eq '{os.getenv('MSTEAMS_APP_EXTERNAL_ID')}'&$expand=appDefinitions"
        )
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(url, headers=headers)

        if response.status_code != 200:
            raise Exception(f"Failed to get app details: {response.json()}")

        response_data = response.json()
        logger.info("[MS_TEAMS_INSTALL] App details response: %s", response_data)
        if not response_data.get("value"):
            raise Exception("No app found with external ID")

        app_details = response_data.get("value").pop()
        return app_details
    except Exception as e:
        logger.error("APP_INT_EXCEPTION: Error when getting app details %s", e)
        raise Exception from e


def get_users(access_token):
    try:
        url = GRAPH_API_URL.format("users?$select=mail,id,userPrincipalName")
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(url, headers=headers)
        logger.info(
            "[MS_TEAMS_INSTALL] Get users response: %s",
            response.json(),
        )
        if response.status_code == 200:
            return response.json().get("value")
        else:
            raise Exception(response.json())
    except Exception as e:
        logger.error("APP_INT_EXCEPTION: Error when getting app details %s", e)
        raise Exception from e


def get_single_user(access_token, email_id):
    try:
        url = GRAPH_API_URL.format(
            f"users?$select=mail,id,userPrincipalName&$filter=mail eq '{email_id}' "
            f"OR userPrincipalName eq '{email_id}'"
        )
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(url, headers=headers, timeout=50)
        if response.status_code == 200:
            return response.json().get("value")
        else:
            raise Exception(response.json())
    except Exception as error:
        logger.error("APP_INT_EXCEPTION: Error when getting app details %s", error)
        raise Exception from error


def install_app_for_user(access_token, msteams_user_id, app_catalog_id):
    part_url = f"/users/{msteams_user_id}/teamwork/installedApps"
    url = GRAPH_API_URL.format(part_url)
    payload = json.dumps(
        {
            "<EMAIL>": f"https://graph.microsoft.com/v1.0/appCatalogs/teamsApps/{app_catalog_id}"
        }
    )
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    install_app_response = requests.post(url, headers=headers, data=payload)
    return install_app_response


def get_conversation_id(access_token, msteams_user_id, app_catalog_id):
    try:
        part_url = f"/users/{msteams_user_id}/teamwork/installedApps?$filter=teamsApp/id eq '{app_catalog_id}'"
        url = GRAPH_API_URL.format(part_url)
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(url, headers=headers)

        if response.status_code != 200:
            raise Exception(response.json())

        response_data = response.json()
        logger.info("[MS_TEAMS] Conversation id response: %s", response_data)

        if not response_data.get("value"):
            raise Exception("No conversation id found")
        return response_data.get("value").pop()["id"]
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Error when getting conversation id for %s details %s",
            msteams_user_id,
            e,
        )
        raise Exception from e


def get_chat_id(access_token, msteams_user_id, conversation_id):
    try:
        part_url = (
            f"/users/{msteams_user_id}/teamwork/installedApps/{conversation_id}/chat"
        )
        url = GRAPH_API_URL.format(part_url)
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            logger.info("[MS_TEAMS] Chat id response: %s", response.json())
            return response.json().get("id")
        else:
            raise Exception(response.json())
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Error when getting chat id for %s details %s",
            msteams_user_id,
            e,
        )
        raise Exception from e


def get_user_data_from_access_token(access_token):
    try:
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        url = GRAPH_API_URL.format("me")

        response = requests.get(url, headers=headers, timeout=30)
        response_data = response.json()
        if response.status_code == 200:
            return response_data
        else:
            raise Exception(response_data)
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: Error when getting user data for access token for %s %s",
            access_token,
            e,
        )
        raise Exception from e
