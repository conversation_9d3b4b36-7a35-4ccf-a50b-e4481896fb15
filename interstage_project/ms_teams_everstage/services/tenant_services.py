import logging
import traceback
from datetime import datetime

from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.utils import timezone

from commission_engine.accessors.client_accessor import set_client_feature
from commission_engine.services.payee_notification_service import (
    add_tasks_for_payee,
    is_msteams_connected,
    is_msteams_integrated,
)
from commission_engine.utils.general_data import (
    IntegrationType,
    Notification,
    NotificationChannelConnectionStatus,
    SegmentEvents,
    SegmentProperties,
)
from everstage_infra.aws_infra.ecs import is_prod_env
from interstage_project.jwt_handler import jwt_hard_decode_token
from ms_teams_everstage.accessors.msteams_admin_token_details_accessor import (
    MsteamsAdminTokenDetailsAccessor,
)
from ms_teams_everstage.accessors.msteams_tenant_details_accessor import (
    MsteamsClientDetailsAccessor,
)
from ms_teams_everstage.serializers.msteams_admin_token_details_serializer import (
    MsteamsAdminTokenDetailsSerializer,
)
from ms_teams_everstage.serializers.msteams_tenant_details_serializer import (
    MsteamsTenantDetailsSerializer,
)
from ms_teams_everstage.services.graph_api_services import (
    get_admin_access_token,
    get_admin_access_token_from_refresh_token,
    get_app_details,
    get_chat_id,
    get_conversation_id,
    get_single_user,
    get_tenant_access_token,
    get_users,
    install_app_for_user,
    publish_app_to_catalog,
    update_app_installed_for_user,
    update_published_app_to_catalog,
)
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.config_accessors.integration_config_accessor import (
    IntegrationConfigAccessor,
)
from spm.models.config_models.employee_models import Employee
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.config_services.slack_config_services import (
    is_notification_allowed_for_user,
)
from spm.services.notification_channel_connection_status_services import (
    update_msteams_notification_channel_connection_status,
)

logger = logging.getLogger(__name__)


def get_admin_token_details(client_id):
    return MsteamsAdminTokenDetailsAccessor(client_id).get()


def add_default_msteams_notifications(client_id, email_ids):
    """
    returns list of default tasks for the payee
        {
            "email_id": "<EMAIL>",
            "tasks": [
                {
                    "name": "COMMISSION_NOTIFICATION",
                    "frequency": "DAILY",
                    "msteams": {
                        "enabled": "true"
                    }
                }
            ]
        }
    """
    from spm.services.rbac_services import get_ui_permissions

    for email_id in email_ids:
        default_msteams_tasks = []
        user_permissions = get_ui_permissions(client_id, email_id)

        for notification in Notification:
            required_permissions = notification.value.get("permissions", [])
            can_show = False
            for permission in required_permissions:
                if user_permissions and permission in user_permissions:
                    can_show = True
                    break

            if (
                can_show
                and notification.value["managed_by_payee"]
                and notification.value.get("enabled_on_connect", False)
            ):
                default_msteams_tasks.append(
                    {
                        "name": notification.name,
                        "frequency": notification.value["frequency"],
                        "ms_teams": {"enabled": True},
                    }
                )
        add_tasks_for_payee(client_id, default_msteams_tasks, email_id)
        logger.info("Added default tasks for payee %s on MS teams", email_id)


def update_tenant_token_details(
    client_id, admin_access_token, admin_refresh_token, token_details=None
):
    if not token_details:
        token_details = MsteamsAdminTokenDetailsAccessor(client_id).get()
    token_details.admin_access_token = admin_access_token
    token_details.admin_refresh_token = admin_refresh_token
    token_details.updated_at = timezone.now()
    token_details.save()


def save_tenant_token_details(
    client_id, tenant_id, admin_access_token, admin_refresh_token
):
    token_details = MsteamsAdminTokenDetailsAccessor(client_id).get()
    if token_details:
        update_tenant_token_details(
            client_id, admin_access_token, admin_refresh_token, token_details
        )
    else:
        token_details = {
            "client_id": client_id,
            "tenant_id": tenant_id,
            "admin_access_token": admin_access_token,
            "admin_refresh_token": admin_refresh_token,
            "updated_at": timezone.now(),
        }
        token_details_ser = MsteamsAdminTokenDetailsSerializer(data=token_details)
        if token_details_ser.is_valid():
            logger.info("Saving admin token for client %s", client_id)
            token_details_ser.save()
        else:
            logger.error(
                "APP_INT_EXCEPTION: Error in ms teams admin token data: %s | Access Token (%s): %s | Refresh Token (%s): %s",
                token_details_ser.errors,
                len(admin_access_token),
                admin_access_token,
                len(admin_refresh_token),
                admin_refresh_token,
            )
            raise Exception(
                f"Error in ms teams admin token data {token_details_ser.errors}"
            )


def persist_msteams_tenant_details(
    client_id,
    tenant_id,
    app_catalog_id,
    app_external_id,
    msteams_admin_email_id,
    login_email_id,
    app_version,
):
    if is_prod_env():
        app_package_s3_url = f"https://everstage-msteams-prod.s3.us-east-2.amazonaws.com/{app_external_id}.zip"
    else:
        app_package_s3_url = f"https://everstage-msteams.s3.us-east-2.amazonaws.com/{app_external_id}.zip"
    logger.info("************Published app to catalog************")

    data = {
        "client_id": client_id,
        "tenant_id": tenant_id,
        "app_catalog_id": app_catalog_id,
        "app_external_id": app_external_id,
        "app_package_s3_url": app_package_s3_url,
        "msteams_installed_by": msteams_admin_email_id,
        "msteams_updated_by": msteams_admin_email_id,
        "installed_by": login_email_id,
        "updated_by": login_email_id,
        "installed_at": timezone.now(),
        "updated_at": timezone.now(),
        "app_version": app_version,
    }
    ser = MsteamsTenantDetailsSerializer(data=data)
    if ser.is_valid():
        MsteamsClientDetailsAccessor(client_id).save(ser)
        logger.info(
            "************Saved tenant details id for %s ************", client_id
        )
    else:
        logger.error("APP_INT_EXCEPTION: Error in ms teams tenant data %s ", ser.errors)
        raise Exception("Error in ms teams tenant data %s" % (ser.errors))


def save_conversation_id_for_employee(
    client_id, tenant_access_token, app_catalog_id, msteams_user_id, employee_email_id
):
    ked = timezone.now()
    conversation_id = get_conversation_id(
        tenant_access_token, msteams_user_id, app_catalog_id
    )
    chat_id = get_chat_id(tenant_access_token, msteams_user_id, conversation_id)
    logger.info("[MS_TEAMS_INSTALL] Got the chat id for %s", employee_email_id)
    IntegrationConfigAccessor().invalidate_msteams_config_for_email_ids(
        client_id, [employee_email_id], ked
    )
    IntegrationConfigAccessor().bulk_create_config_records(
        [
            {
                "client": client_id,
                "employee_email_id": employee_email_id,
                "integration_type": IntegrationType.MS_TEAMS.value,
                "knowledge_begin_date": ked,
                "config": {
                    "msteams_user_id": msteams_user_id,
                    "msteams_conversation_id": chat_id,
                },
            }
        ]
    )
    logger.info(
        "************Saved converstaion id for %s************", employee_email_id
    )
    add_default_msteams_notifications(client_id, employee_email_id)


@transaction.atomic
def save_conversation_id_for_employees(
    client_id,
    tenant_access_token,
    app_catalog_id,
    msteams_user_ids,
    employees,
    should_update_employee_config=True,
):
    employee_email_ids = []
    employee_config_map = {}
    integration_config_map_list = []
    kd = timezone.now()
    for _employee in employees:
        employee_email_id = (
            _employee if isinstance(_employee, str) else _employee.employee_email_id
        )
        msteams_user_id = msteams_user_ids[employee_email_id]
        employee_email_ids.append(employee_email_id)
        conversation_id = get_conversation_id(
            tenant_access_token, msteams_user_id, app_catalog_id
        )
        chat_id = get_chat_id(tenant_access_token, msteams_user_id, conversation_id)
        logger.info("************Got the chat id for %s************", employee_email_id)

        if should_update_employee_config:
            integration_config_map_list.append(
                {
                    "employee_email_id": employee_email_id,
                    "client": client_id,
                    "integration_type": IntegrationType.MS_TEAMS.value,
                    "config": {
                        "msteams_conversation_id": chat_id,
                        "msteams_user_id": msteams_user_id,
                    },
                    "knowledge_begin_date": kd,
                }
            )
        else:
            employee_config_map[employee_email_id] = {
                "msteams_conversation_id": chat_id,
                "msteams_user_id": msteams_user_id,
            }
    if should_update_employee_config:
        IntegrationConfigAccessor().invalidate_msteams_config_for_email_ids(
            client_id, employee_email_ids, kd
        )
        IntegrationConfigAccessor().bulk_create_config_records(
            integration_config_map_list
        )
        add_default_msteams_notifications(client_id, employee_email_ids)
    logger.info(
        "[MS_TEAMS_INSTALL] Saved converstaion id for employess %s",
        employee_email_ids,
    )
    return employee_config_map if not should_update_employee_config else {}


def update_msteams_tenant_details(
    client_id,
    tenant_id,
    app_catalog_id,
    app_external_id,
    msteams_admin_email_id,
    login_email_id,
    app_version,
):
    msteams_tenant_details = MsteamsClientDetailsAccessor(client_id).get()
    if msteams_tenant_details:
        msteams_tenant_details.updated_by = login_email_id
        msteams_tenant_details.msteams_updated_by = msteams_admin_email_id
        msteams_tenant_details.app_version = app_version
        msteams_tenant_details.updated_at = timezone.now()
        msteams_tenant_details.save()
    else:
        persist_msteams_tenant_details(
            client_id,
            tenant_id,
            app_catalog_id,
            app_external_id,
            msteams_admin_email_id,
            login_email_id,
            app_version,
        )


def get_employee_in_msteams_from_email_id(client_id, email_id, notification_type):
    employee_obj = (
        Employee.objects.filter(knowledge_end_date__isnull=True)
        .filter(is_deleted=False)
        .filter(employee_email_id=email_id)
        .filter(client_id=client_id)
    ).first()

    if not employee_obj:
        raise ObjectDoesNotExist(f"Email id {email_id} not found in Everstage.")

    if not is_notification_allowed_for_user(client_id, email_id, notification_type):
        logger.info(
            "User - %s is not allowed to receive notification - %s",
            email_id,
            notification_type,
        )
        return

    employee_deactivation_date = employee_obj.deactivation_date
    if employee_deactivation_date and employee_deactivation_date <= timezone.now():
        logger.info(
            "User - %s got deactivated on %s", email_id, employee_deactivation_date
        )
        return

    return employee_obj


def get_msteams_users_details_for_client(client_id, user_access_token):
    users = get_users(user_access_token)
    logger.info("[MS_TEAMS_INSTALL] Got the users for client %s", client_id)

    ms_teams_users_emails = []
    ms_teams_user_ids = {}

    for user in users:
        logging.info("[MS_TEAMS_INSTALL] MS User details: %s", user)
        ms_team_user_email = (
            user.get("mail", None)
            if user.get("mail", None)
            else user["userPrincipalName"]
        )
        ms_team_user_email = ms_team_user_email.lower()
        ms_teams_users_emails.append(ms_team_user_email)
        ms_teams_user_ids[ms_team_user_email] = user["id"]

    everstage_employees = EmployeeAccessor(client_id).get_all_payees_with_status(
        email_list=ms_teams_users_emails, status=["Active", "Invited"]
    )
    return ms_teams_user_ids, everstage_employees


def install_published_app_for_users(
    client_id,
    user_access_token,
    tenant_access_token,
    app_catalog_id,
    ms_teams_user_ids,
    everstage_employees,
):
    everstage_employees_to_update = []
    for everstage_employee in everstage_employees:
        msteams_user_id = ms_teams_user_ids[everstage_employee.employee_email_id]
        response = install_app_for_user(
            user_access_token, msteams_user_id, app_catalog_id
        )
        logger.info(
            "[MS_TEAMS_INSTALL] Install app response for %s: %s",
            everstage_employee.employee_email_id,
            response.json(),
        )
        if response.status_code in [200, 201, 204]:
            logger.info(
                "************App installed for user %s************",
                everstage_employee.employee_email_id,
            )
            everstage_employees_to_update.append(everstage_employee)

    save_conversation_id_for_employees(
        client_id,
        tenant_access_token,
        app_catalog_id,
        ms_teams_user_ids,
        everstage_employees_to_update,
    )


def integrate_msteams_for_newly_added_users(client_id, everstage_employees):
    try:
        if not is_msteams_connected(client_id):
            logger.info(
                "MS Teams not connected for client, Skipping teams app install for newly added users in client %s",
                client_id,
            )
            return
        if not everstage_employees:
            logger.info("No new employees to update ms teams for")
            return
        user_access_token, refresh_token = get_admin_access_token_from_refresh_token(
            client_id
        )
        # use get_single_user api if only one user is added
        if len(everstage_employees) == 1:
            users = get_single_user(user_access_token, everstage_employees[0])
        else:
            users = get_users(user_access_token)

        ms_teams_users_emails = []
        ms_teams_user_ids = {}

        for user in users:
            logging.info("[MS_TEAMS_INSTALL] MS User details: %s", user)
            ms_team_user_email = (
                user.get("mail", None)
                if user.get("mail", None)
                else user["userPrincipalName"]
            )
            ms_team_user_email = ms_team_user_email.lower()
            ms_teams_users_emails.append(ms_team_user_email)
            ms_teams_user_ids[ms_team_user_email] = user["id"]

        app_details = get_app_details(user_access_token)
        app_catalog_id = app_details.get("id")

        employee_config_map = install_app_for_new_users(
            client_id,
            user_access_token,
            user_access_token,
            app_catalog_id,
            ms_teams_user_ids,
            everstage_employees,
            should_update_employee_config=False,
        )
        update_tenant_token_details(client_id, user_access_token, refresh_token)
        logger.info(
            "Installed teams app install for newly added users in client %s", client_id
        )
        return employee_config_map
    except Exception as error:  # pylint: disable=broad-except
        logger.error(
            "APP_INT_EXCEPTION: INSTALLATION FOR NEWLY ADDED USERS - %s - %s",
            traceback.print_exc(),
            error,
        )
        logger.info("Please disconnect and reconnect to MS Teams")


def install_app_for_new_users(
    client_id,
    user_access_token,
    tenant_access_token,
    app_catalog_id,
    ms_teams_user_ids,
    everstage_employees,
    should_update_employee_config=True,
):
    logger.info(
        "[MS_TEAMS_INSTALL] Installing app for new users for client %s", client_id
    )
    everstage_employees_to_update = []
    for everstage_employee in everstage_employees:
        employee_email_id = (
            everstage_employee
            if isinstance(everstage_employee, str)
            else everstage_employee.employee_email_id
        )
        is_msteams_connected_for_emp = is_msteams_integrated(
            client_id, employee_email_id
        )
        if not is_msteams_connected_for_emp:
            msteams_user_id = ms_teams_user_ids.get(employee_email_id, None)
            if not msteams_user_id:
                logging.info("User with id %s not found on teams", employee_email_id)
                continue
            response = install_app_for_user(
                user_access_token, msteams_user_id, app_catalog_id
            )
            logger.info(
                "[MS_TEAMS_INSTALL] Install app response for %s: %s",
                employee_email_id,
                response.json(),
            )
            if response.status_code in [200, 201, 204, 409]:
                logger.info(
                    "[MS_TEAMS_INSTALL] App installed for new user: %s",
                    employee_email_id,
                )
                everstage_employees_to_update.append(everstage_employee)

    return save_conversation_id_for_employees(
        client_id,
        tenant_access_token,
        app_catalog_id,
        ms_teams_user_ids,
        everstage_employees_to_update,
        should_update_employee_config=should_update_employee_config,
    )


def update_published_app_for_users(
    client_id,
    user_access_token,
    tenant_access_token,
    app_catalog_id,
    ms_teams_user_ids,
    everstage_employees,
):
    everstage_employees_to_update = []
    for everstage_employee in everstage_employees:
        msteams_user_id = ms_teams_user_ids[everstage_employee.employee_email_id]
        if not msteams_user_id:
            logging.info(
                "User with id %s not found on teams",
                everstage_employee.employee_email_id,
            )
            continue
        response = update_app_installed_for_user(
            user_access_token, msteams_user_id, app_catalog_id
        )
        if response.status_code in [200, 201, 204]:
            logger.info(
                "[MS_TEAMS_INSTALL] App updated for user %s",
                everstage_employee.employee_email_id,
            )
            everstage_employees_to_update.append(everstage_employee)

    save_conversation_id_for_employees(
        client_id,
        tenant_access_token,
        app_catalog_id,
        ms_teams_user_ids,
        everstage_employees_to_update,
    )


@transaction.atomic
def install_msteams_app_for_client(client_id, code, login_email_id):
    try:
        logger.info(
            "[MS_TEAMS_INSTALL] Starting MS Teams app installation for client_id=%s, login_email_id=%s",
            client_id,
            login_email_id,
        )
        user_access_token, refresh_token = get_admin_access_token(code, login_email_id)

        if not user_access_token or not refresh_token:
            raise Exception("Failed to get admin access token")

        logger.info(
            "[MS_TEAMS_INSTALL] Successfully obtained admin access token for client %s | user_access_token=%s | refresh_token=%s",
            client_id,
            user_access_token,
            refresh_token,
            extra={
                "user_access_token": user_access_token,
                "refresh_token": refresh_token,
            },
        )

        decoded = jwt_hard_decode_token(user_access_token)
        tenant_id = decoded.get("tid")
        msteams_admin_email_id = decoded.get("unique_name")
        logger.info(
            "[MS_TEAMS_INSTALL] Extracted tenant_id=%s, admin_email=%s",
            tenant_id,
            msteams_admin_email_id,
        )

        tenant_access_token = get_tenant_access_token(tenant_id)
        logger.info(
            "[MS_TEAMS_INSTALL] Successfully obtained tenant access token for client %s",
            client_id,
        )

        app_details = get_app_details(user_access_token)
        logger.info(
            "[MS_TEAMS_INSTALL] App details fetched successfully for client %s",
            client_id,
        )

        ms_teams_user_ids, everstage_employees = get_msteams_users_details_for_client(
            client_id, user_access_token
        )
        logger.info(
            "[MS_TEAMS_INSTALL] Found %d MS Teams users and %d Everstage employees",
            len(ms_teams_user_ids),
            len(everstage_employees),
        )

        if app_details:
            logger.info("[MS_TEAMS_INSTALL] App already published, Updating app")
            app_catalog_id = app_details.get("id")
            app_external_id = app_details.get("externalId")
            app_version = app_details.get("appDefinitions").pop().get("version")
            logger.info(
                "[MS_TEAMS_INSTALL] App details - catalog_id=%s, external_id=%s, version=%s",
                app_catalog_id,
                app_external_id,
                app_version,
            )

            updated_app_response = update_published_app_to_catalog(
                user_access_token, app_catalog_id
            )
            logger.info(
                "[MS_TEAMS_INSTALL] Updated published app to catalog response: %s",
                updated_app_response.json(),
                extra={
                    "api_response": updated_app_response.json(),
                },
            )

            if updated_app_response.status_code in [200, 201]:
                update_msteams_tenant_details(
                    client_id,
                    tenant_id,
                    app_catalog_id,
                    app_external_id,
                    msteams_admin_email_id,
                    login_email_id,
                    app_version,
                )
                logger.info(
                    "[MS_TEAMS_INSTALL] Updated MS Teams tenant details for client_id=%s",
                    client_id,
                )
                update_published_app_for_users(
                    client_id,
                    user_access_token,
                    tenant_access_token,
                    app_catalog_id,
                    ms_teams_user_ids,
                    everstage_employees,
                )
                logger.info("[MS_TEAMS_INSTALL] Updated app for existing users")
            elif updated_app_response.status_code == 409:
                logger.info(
                    "[MS_TEAMS_INSTALL] No new app version found, installing for new users"
                )
                install_app_for_new_users(
                    client_id,
                    user_access_token,
                    tenant_access_token,
                    app_catalog_id,
                    ms_teams_user_ids,
                    everstage_employees,
                )
        else:
            logger.info("[MS_TEAMS_INSTALL] App not found, publishing app")
            publish_app_to_tenant_response = publish_app_to_catalog(user_access_token)

            logger.info(
                "[MS_TEAMS_INSTALL] Publish app response: %s",
                publish_app_to_tenant_response.json(),
            )

            app_details = get_app_details(user_access_token)
            logger.info(
                "[MS_TEAMS_INSTALL] Got app details after publishing: %s",
                app_details,
            )

            app_catalog_id = app_details.get("id")
            app_external_id = app_details.get("externalId")
            app_version = app_details.get("appDefinitions").pop().get("version")
            logger.info(
                "[MS_TEAMS_INSTALL] New app details - catalog_id=%s, external_id=%s, version=%s",
                app_catalog_id,
                app_external_id,
                app_version,
            )

            if publish_app_to_tenant_response.status_code in [200, 201]:
                logger.info(
                    "[MS_TEAMS_INSTALL] Successfully published app. Response: %s",
                    publish_app_to_tenant_response.json(),
                )
                persist_msteams_tenant_details(
                    client_id,
                    tenant_id,
                    app_catalog_id,
                    app_external_id,
                    msteams_admin_email_id,
                    login_email_id,
                    app_version,
                )
                logger.info(
                    "[MS_TEAMS_INSTALL] Persisted MS Teams tenant details for client_id=%s",
                    client_id,
                )
                install_published_app_for_users(
                    client_id,
                    user_access_token,
                    tenant_access_token,
                    app_catalog_id,
                    ms_teams_user_ids,
                    everstage_employees,
                )
                logger.info("[MS_TEAMS_INSTALL] Installed app for all users")

        set_client_feature(client_id, "msteams_connected", True)
        save_tenant_token_details(
            client_id, tenant_id, user_access_token, refresh_token
        )
        update_msteams_notification_channel_connection_status(
            client_id,
            NotificationChannelConnectionStatus.CONNECTED.value,
            login_email_id,
        )

        analytics_data = {
            "user_id": login_email_id,
            "event_name": SegmentEvents.MS_TEAMS_CONNECTED.value,
            "event_properties": {
                SegmentProperties.MS_TEAMS_EMAIL.value: login_email_id,
                SegmentProperties.CONNECT_MS_TEAMS_DATE.value: datetime.now().strftime(
                    "%d-%m-%Y"
                ),
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        logger.info(
            "[MS_TEAMS_INSTALL] MS Teams installation completed for client_id=%s",
            client_id,
        )
    except Exception as e:
        logger.error(
            "APP_INT_EXCEPTION: CALL BACK FLOW - %s",
            traceback.print_exc(),
        )
        raise Exception from e


def disconnect_msteams_for_client(client_id, mail_id):
    set_client_feature(client_id, "msteams_connected", False)
    ked = timezone.now()
    logger.info(
        "************Invalidating all employees in msteams team for client %s************",
        client_id,
    )
    IntegrationConfigAccessor().invalidate_all_employees_in_msteams_team(
        client_id=client_id, end_time=ked
    )

    update_msteams_notification_channel_connection_status(
        client_id, NotificationChannelConnectionStatus.DISCONNECTED.value, mail_id
    )

    analytics_data = {
        "user_id": mail_id,
        "event_name": SegmentEvents.MS_TEAMS_DISCONNECTED.value,
        "event_properties": {
            SegmentProperties.MS_TEAMS_EMAIL.value: mail_id,
            SegmentProperties.DISCONNECT_MS_TEAMS_DATE.value: datetime.now().strftime(
                "%d-%m-%Y"
            ),
        },
    }
    analytics = CoreAnalytics(analyser_type="segment")
    analytics.send_analytics(analytics_data)
