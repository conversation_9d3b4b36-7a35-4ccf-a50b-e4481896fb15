"""
Utilities for crystal related operations.
"""

# pylint: disable-msg=E0611

import json
import logging
import sys
import traceback
from enum import Enum
from functools import wraps
from typing import Dict, List

import numpy as np
import pandas as pd
from django.http import JsonResponse
from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response

from commission_engine.accessors.client_accessor import (
    get_client,
    get_client_fiscal_start_month,
    get_crystal_calc_fields_override_logic_v2,
)
from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from commission_engine.services.commission_calculation_service.reference_data_type import (
    get_datatype_id_name_map,
)
from commission_engine.utils.date_utils import get_period_start_and_end_date
from commission_engine.utils.general_data import (
    Freq,
    RbacPermissions,
    varDatatypePyDatatypeMap,
)
from crystal.exceptions.exceptions import CrystalException
from crystal.types import AuditDataType, CrystalTableSettingsData
from spm.accessors.config_accessors.employee_accessor import EmployeePayrollAccessor
from spm.services.custom_object_services.custom_object_service import (
    get_all_data_types,
    get_display_names_from_system_names_with_data_type,
)
from spm.services.rbac_services import get_ui_permissions

# TODO: Note - for some weird reason, removing the unused import above is causing pylint to go into a circular
# dependency loop - don't remove the line above until we understand - https://github.com/Everstage/everstage-spm/actions/runs/3912607135/jobs/6687462799#step:8:28


# from spm.services.draws_services import get_draw_adjustments

IMPERSONATION_PARAM_NAME = "for_user"
PRIMARY_QUOTA = "Primary $QUOTA"
KEY_NAME_COLUMN = "row_key"  # Should match the KEY_COLUMN_NAME token in the crystal_components/Utils.js file
HIDDEN_CRITERIA_NAME = "everstage_hidden_row"  # Should match the HIDDEN_CRITERIA_NAME token in the crystal_components/Utils.js file
QUOTA = "$QUOTA"

logger = logging.getLogger(__name__)


def get_display_names_map(
    client_id,
    source_type,
    source_id,
    system_column_names,
    databook_id=None,
    crystal_calc_fields_override_logic_v2=False,
):
    """
    return -> {"co_1_id" : ("id", "Integer"), "co_1_amount": ("amount", "Integer").....}
    """
    from spm.services.databook_services import (
        get_databook_id_by_datasheet_id,
        get_datasheet_display_names,
    )

    system_name_display_name_list = None
    if source_type == "custom_object":
        system_name_display_name_list = (
            get_display_names_from_system_names_with_data_type(
                client_id=client_id,
                custom_object_id=source_id,
                system_names=system_column_names,
            )
        )
    elif source_type == "datasheet":
        # The source datasheet need not to be present
        # in the same databook as the current datasheet is present
        databook_id = get_databook_id_by_datasheet_id(
            client_id=client_id, datasheet_id=source_id
        )

        system_name_display_name_list = get_datasheet_display_names(
            client_id=client_id,
            databook_id=databook_id,
            datasheet_id=source_id,
            system_names=system_column_names,
            crystal_calc_fields_override_logic_v2=crystal_calc_fields_override_logic_v2,
        )
    display_names_map = {}
    for sys_display_name in system_name_display_name_list:
        data_type = get_all_data_types(type_id=sys_display_name["data_type"])
        if crystal_calc_fields_override_logic_v2:
            display_names_map[sys_display_name["system_name"]] = (
                sys_display_name["display_name"],
                data_type["data_type"],
                sys_display_name["meta_data"],
            )
        else:
            display_names_map[sys_display_name["system_name"]] = (
                sys_display_name["display_name"],
                data_type["data_type"],
            )

    return display_names_map


def crystal_table_settings_to_ag_grid_columndefs(
    client_id, table_settings: CrystalTableSettingsData
):
    """
    Converts CrystalTableSettings type to ag-grid's columnDefs format - see https://www.ag-grid.com/react-data-grid/column-definitions/
    """
    source_id = table_settings["source_id"]
    source_type = table_settings["source_type"]
    columns = table_settings["display_columns"]
    databook_id = (
        table_settings["databook_id"] if "databook_id" in table_settings else None
    )
    crystal_calc_fields_override_logic_v2 = get_crystal_calc_fields_override_logic_v2(
        client_id
    )
    col_map = get_display_names_map(
        client_id,
        source_type,
        source_id,
        columns,
        databook_id=databook_id,
        crystal_calc_fields_override_logic_v2=crystal_calc_fields_override_logic_v2,
    )
    ag_grid_meta = []
    if col_map:
        for column in columns:
            meta_data = None
            if column not in col_map:
                continue
            if crystal_calc_fields_override_logic_v2:
                header_name, data_type, meta_data = col_map[column]

            else:
                header_name, data_type = col_map[column]
            column_def = {}
            column_def["field"] = column
            column_def["headerName"] = header_name
            column_def["type"] = data_type
            # editable
            if (
                not crystal_calc_fields_override_logic_v2 or not meta_data
            ) and column in table_settings["editable_columns"]:
                column_def["editable"] = True
            # currently not handling sort criteria
            # if column in table_settings["sortable_columns"]:
            #     column_def["sortable"] = True

            ag_grid_meta.append(column_def)

        # Apart from regular fields, always add a row_key field that is hidden by default and acts as the default id field for ag-grid
        ag_grid_meta.append({"field": KEY_NAME_COLUMN, "hide": True})
    return ag_grid_meta


def change_column_name_to_display_name(
    client_id, table_settings: CrystalTableSettingsData, data: List[Dict]
):
    source_id = table_settings["source_id"]
    source_type = table_settings["source_type"]
    system_column_names = table_settings["display_columns"]
    databook_id = (
        table_settings["databook_id"] if "databook_id" in table_settings else None
    )
    display_columns_names_map = get_display_names_map(
        client_id, source_type, source_id, system_column_names, databook_id=databook_id
    )

    updated_data = []

    for deal in data:
        for system_name, (display_name, _) in display_columns_names_map.items():
            deal[display_name] = deal.pop(system_name)
        updated_data.append(deal)

    return updated_data


def handle_crystal_exception(func):
    """
    Decorator that handles exceptions raised by crystal view methods and returns an appropriate response
    TODO: According to django rest framework docs, this should be happening by default and we dont need to
    have another decorator like this - we need to investigate why this is not happening
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except CrystalException as e:
            code = e.code if e.code else "UNSPECIFIED"
            payload = e.payload if e.payload else {}
            logger.exception(code, extra=payload)
            return Response(
                data={"error": {"code": code, "payload": payload}, "status": e.status},
                status=e.status,
            )
        except Exception as _exception:  # pylint: disable=broad-except
            # Print out the exception to be seen in the logs
            exc_info = sys.exc_info()
            error_trace = "".join(traceback.format_exception(*exc_info))
            logger.exception(error_trace)
            return Response(
                data={
                    "error": "Internal Server Error",
                    "status": status.HTTP_500_INTERNAL_SERVER_ERROR,
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    return wrapper


def has_view_crystal_permission(view_crsytal_func):
    """
    Decorator function for an employee to view crystal.

    Args:
        fn (function): The function to be decorated.

    Returns:
        function: Decorated function that checks if the employee is a revenue payee.
    """

    @wraps(view_crsytal_func)
    def _wrapped_view(request, *args, **kwargs):
        client_id = request.client_id
        logged_in_user = request.user
        is_revenue_payee = EmployeePayrollAccessor(client_id).get_payee_role_ed_aware(
            logged_in_user
        )
        user_permissions = get_ui_permissions(client_id, logged_in_user)
        if (RbacPermissions.MANAGE_CRYSTAL.value not in user_permissions) and (
            is_revenue_payee and is_revenue_payee != "Revenue"
        ):
            logger.info("View crystal permission denied for user: %s", logged_in_user)
            response = JsonResponse(
                {"message": "You don't have permission to view crystal"}
            )
            response.status_code = 403
            return response
        logger.info("View crystal permission granted for user: %s", logged_in_user)
        return view_crsytal_func(request, *args, **kwargs)

    return _wrapped_view


def success_response(data):
    data.update({"status": status.HTTP_200_OK})
    return Response(data=data, status=status.HTTP_200_OK)


def get_psd_ped_for_payee(client_id, knowledge_date, payee_email):
    """
    Find the current period start date and end date for the payee
    """
    from spm.services.dashboard_services.payee_commission_services import (  # get_payee_payout_by_end_date,; get_payee_payroll_date,; get_variable_pay,; get_variable_pay_for_given_date,
        get_employee_payroll,
    )

    client = get_client(client_id)
    start_month = client.fiscal_start_month
    given_date = knowledge_date
    user_payout_freq_for_date = get_employee_payroll(
        client_id=client_id, end_date=given_date, employee_email_ids=payee_email
    )
    period_start_date = None
    period_end_date = None
    if user_payout_freq_for_date:
        payout_freq = user_payout_freq_for_date[0]["payout_frequency"].lower()
        calculated_start_end_periods = get_period_start_and_end_date(
            given_date,
            start_month,
            payout_freq,
            client_id=client_id,
        )
        period_start_date = calculated_start_end_periods["start_date"]
        period_end_date = calculated_start_end_periods["end_date"]

    return (period_start_date, period_end_date)


def make_audit_data(client_id=None, user=None, audit=None) -> AuditDataType:
    time = timezone.now()
    audit_data = {
        "client": client_id,
        "knowledge_begin_date": time,
        "last_modified_at": time,
        "last_modified_by": str(user),
        "additional_details": audit,
    }
    return audit_data


class CrystalViewStatus(Enum):
    DRAFT = "draft"
    TRANSIENT_EDIT = "transient_edit"
    PUBLISHED = "published"


def interpret_relative_date_string(label: str, client_id=None, reference_date=None):
    """
    Interprets a string that is in a special format - <time-frame>_<calendar | fiscal>_<period>_<start | end>
    and returns a date formatted as "%d-%m-%Y". Some examples of labels - "current_calendar_monthly_start",
    "next_fiscal_monthly_end", "current_fiscal_quarterly_start" etc
    """

    date_format = "%d-%m-%Y"
    if reference_date is None:
        reference_date = timezone.now()

    try:
        when, calendar_type, schedule_type, boundary = label.split("_")

        start_month = 1
        if calendar_type == "fiscal":
            assert (
                client_id is not None
            ), "client_id is needed to figure out the fiscal calendar"
            start_month = get_client_fiscal_start_month(id=client_id)

        if schedule_type not in [e.value for e in Freq]:
            raise CrystalException(
                code="UNABLE_TO_PARSE_DATE_LABEL", payload={"label": label}
            )

        dates = get_period_start_and_end_date(
            curr_time=reference_date,
            start_month=start_month,
            time_prd=schedule_type,
            next_required=True,
            client_id=client_id,
        )

        date_key = None

        if when == "current":
            date_key = boundary + "_date"
            assert date_key in dates, f"Could not compute {label}"
        elif when == "next":
            date_key = "next_" + boundary + "_date"
            assert date_key in dates, f"Could not compute {label}"

        return dates[date_key].strftime(date_format)

    except Exception as e:
        logger.info("Cannot parse %s", label)
        raise CrystalException(
            code="UNABLE_TO_PARSE_DATE_LABEL", payload={"label": label}
        ) from e


def get_what_if_quota_cache_key(client_id, payee_email, category) -> str:
    cache_key = str(client_id) + "what_if" + "#" + payee_email + "#" + category
    return cache_key


def get_what_if_team_quota_criteria_cache_key(
    client_id, category, team_name, team_type
) -> str:
    return str(client_id) + "#what_if#" + category + "#" + team_name + "#" + team_type


def convert_to_bool(val):
    if isinstance(val, bool):
        return val
    if isinstance(val, str):
        if val.lower() == "true":
            return True
        elif val.lower() == "false":
            return False
    return False


def set_crystal_accurate_column_types(var_list, df):
    datatype_id_name_map = get_datatype_id_name_map()
    colname_type_map = dict()

    for var in var_list:
        if not isinstance(var, dict):
            var = var.__dict__
        colname_type_map[var.get("system_name")] = varDatatypePyDatatypeMap[
            datatype_id_name_map[var.get("data_type_id")]
        ]

    for df_col in df.columns:
        if df_col in colname_type_map:
            df_col_type = colname_type_map[df_col]
            if (
                str(df[df_col].dtypes) == "object"
                and str(df[df_col].dtypes) != df_col_type
            ):
                if df_col_type == "datetime64[ns, UTC]":
                    df[df_col] = pd.to_datetime(df[df_col], errors="coerce", utc=True)
                else:
                    if df_col_type == "bool":
                        df[df_col].fillna(False, inplace=True)
                        df[df_col] = df[df_col].apply(convert_to_bool)
                        continue
                    df[df_col] = df[df_col].astype(df_col_type).replace("nan", np.nan)
            elif str(df[df_col].dtypes) != df_col_type and df_col_type == "str":
                df[df_col] = df[df_col].astype(df_col_type)
    return df


def get_datasheet_variables_used_in_crystal_simulator(
    crystal_table_settings: dict,
) -> set:
    """
    Get List of Datasheet Variables Used in the Crystal View.
    """
    return set.union(
        set(crystal_table_settings.get("display_columns") or []),
        {
            cond["col_name"]
            for cond in crystal_table_settings.get("display_conditions") or []
        },
        {
            action["column_name"]
            for action in crystal_table_settings.get("success_actions") or []
        },
        (
            {crystal_table_settings["date_field"]}
            if crystal_table_settings.get("date_field")
            else set()
        ),
        (
            {crystal_table_settings["email_field"]}
            if crystal_table_settings.get("email_field")
            else set()
        ),
        set(crystal_table_settings.get("editable_columns") or []),
        (
            {crystal_table_settings["row_name"]}
            if crystal_table_settings.get("row_name")
            else set()
        ),
    )


def validate_datasheet_variables_in_crystal(
    client_id: int,
    crystal_table_settings: CrystalTableSettingsData,
) -> bool:
    """
    Validates whether all required columns in crystal_table_settings exist in the datasheet.
    """

    all_columns = get_datasheet_variables_used_in_crystal_simulator(
        crystal_table_settings
    )

    datasheet_id = crystal_table_settings.get("source_id")
    system_names = set(
        DatasheetVariableAccessor(client_id).get_datasheet_vars_system_name(
            datasheet_id
        )
    )

    return all_columns.issubset(system_names)


class PdEncoder(json.JSONEncoder):
    def default(self, obj):  # pylint: disable=arguments-renamed
        if isinstance(obj, pd.Timestamp):
            return str(obj)
        return json.JSONEncoder.default(self, obj)
