# pylint: disable=unused-argument
from django.core.serializers.json import DjangoJSONEncoder
from django.db import models, transaction
from django.db.models import Index
from storages.backends.s3boto3 import S3Boto3Storage

from everstage_admin_backend.services.client_audit_service import (
    log_audit_trail_on_save,
)
from interstage_project.db.models import (
    EsAutoField,
    EsBooleanField,
    EsCharField,
    EsFileField,
    EsForeignKey,
    EsIntegerField,
    EsJSONField,
)
from interstage_project.utils import get_public_s3_bucket


def get_logo_s3_path(instance, filename):
    from commission_engine.accessors.client_accessor import get_valid_filename

    filename = get_valid_filename(instance.name)
    return "Logos/{0}".format(filename)


def get_statement_file_path(filename):
    return "Statement_Logos/{0}".format(filename)


def get_statement_logo_s3_path(instance, filename):
    return get_statement_file_path(instance.name)


class LogosStorage(S3Boto3Storage):
    # pylint: disable=abstract-method
    bucket_name = get_public_s3_bucket()


class Client(models.Model):
    client_id = EsAutoField(primary_key=True, is_sensitive=False)
    name = EsCharField(max_length=254, unique=True, null=False, is_sensitive=False)
    crm_company_id = EsCharField(max_length=254, null=True, is_sensitive=False)
    domain = EsCharField(max_length=254, null=False, is_sensitive=False)
    logo_url = EsCharField(
        max_length=254, null=True, is_sensitive=False
    )  # to store cloudfront path
    auth_connection_name = EsCharField(
        max_length=254, default="email-password", null=True, is_sensitive=False
    )
    connection_type = EsCharField(max_length=254, null=True, is_sensitive=False)
    base_currency = EsCharField(max_length=254, null=True, is_sensitive=False)
    fiscal_start_month = EsIntegerField(null=True, is_sensitive=False)
    secondary_calculator = EsCharField(max_length=254, null=True, is_sensitive=False)
    client_notification = EsBooleanField(default=False, is_sensitive=False)
    payee_notification = EsBooleanField(default=False, is_sensitive=False)
    meta_info = EsJSONField(encoder=DjangoJSONEncoder, null=True)
    logo_s3_path = EsFileField(
        storage=LogosStorage(),
        upload_to=get_logo_s3_path,
        max_length=256,
        null=True,
        is_sensitive=False,
    )
    statement_logo_url = EsCharField(
        max_length=254, null=True, is_sensitive=False
    )  # to store cloudfront path of statement logo
    statement_logo_s3_path = EsFileField(
        storage=LogosStorage(),
        upload_to=get_statement_logo_s3_path,
        max_length=256,
        null=True,
        is_sensitive=False,
    )
    time_zone = EsCharField(max_length=100, null=True, blank=True, is_sensitive=False)
    client_features = EsJSONField(
        encoder=DjangoJSONEncoder, null=True, is_sensitive=False
    )
    is_deleted = EsBooleanField(default=False, is_sensitive=False)

    class Meta:
        db_table = "interstage_clients"
        indexes = [Index(fields=["client_id"], name="cli_id_idx")]

    def get_values(self):
        values = {}
        for field in self._meta.fields:
            value = getattr(self, field.name)
            if field.name in ["logo_s3_path", "statement_logo_s3_path"]:
                value = value.name if value else None
            values[field.name] = value
        return values

    def save(self, *args, **kwargs):
        """
        Override the save method of the Client model to log the changes made to the instance in the client audit trail.

        Args:
            *args: Additional positional arguments.
            **kwargs: Additional keyword arguments.
        """
        with transaction.atomic():
            exists = Client.objects.filter(client_id=self.pk).exists()
            change_type = "Update" if exists else "Insert"

            old_values = {}
            # If this is an update operation, get the current values from the database
            if exists:
                old_instance = Client.objects.get(pk=self.pk)
                old_values = old_instance.get_values()

            # Save the instance
            super().save(*args, **kwargs)

            # Get the new values of the fields in the Client model
            new_values = self.get_values()

            # Log the changes made to the instance in the client audit trail
            try:
                log_audit_trail_on_save(old_values, new_values, change_type)
            except Exception as e:
                # Log the exception
                print(f"Failed to log client audit trail: {e}")
                # Re-raise the exception
                raise


class ClientSettings(models.Model):
    client = EsForeignKey(
        Client, on_delete=models.DO_NOTHING, db_index=False, is_sensitive=False
    )
    icm_settings = EsJSONField(encoder=DjangoJSONEncoder, null=True)
    cpq_settings = EsJSONField(encoder=DjangoJSONEncoder, null=True)
    tqm_settings = EsJSONField(encoder=DjangoJSONEncoder, null=True)

    class Meta:
        db_table = "client_settings"
        indexes = [Index(fields=["client_id"], name="cl_sett_idx")]
