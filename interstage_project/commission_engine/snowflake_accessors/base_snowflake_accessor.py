import logging
import uuid
from dataclasses import dataclass
from datetime import datetime

from django.db.models import Date<PERSON><PERSON>Field

from commission_engine.database.snowflake_connection import (
    create_snowpark_session_wrapper,
)
from commission_engine.snowflake_accessors.utils import (
    SNOWFLAKE_MAX_IN_CLAUSE_PARAMS_LEN,
)
from commission_engine.utils.date_utils import make_aware_wrapper
from everstage_ddd.stormbreaker.types import StrInSingleQuote
from spm.services.stormbreaker.stormbreaker_utils import escape_special_characters

logger = logging.getLogger(__name__)


@dataclass
class ORclause:
    condition: str
    value: list


def temp_table_filter(
    client_id, table_name, filter_values, filter_attribute, data_type="STRING"
) -> str:
    """This function creates a temp table and returns the filter clause for the query.
    This is used when the filter values are too long to be passed as a list in the query.
    """
    with create_snowpark_session_wrapper(
        session=None, client_id=client_id
    ) as current_session:
        # This get the column name from "col_name::type"
        col_without_type = filter_attribute.rsplit("::", 1)[0]
        new_values = []
        is_filter_value_contains_str_in_single_quote = False
        for fv in filter_values:
            if isinstance(fv, str) and ("'" in fv or "\\" in fv):
                is_filter_value_contains_str_in_single_quote = True
                fv = escape_special_characters(fv)  # noqa: PLW2901
                fv = StrInSingleQuote(fv)  # noqa: PLW2901
            new_values.append(fv)

        if is_filter_value_contains_str_in_single_quote:
            # If filter_values include strings with single quotes, a temporary table is generated using raw SQL.
            # The create_dataframe function is avoided because Snowflake handles single quote escaping internally.
            # Since single quotes in filter_values are already escaped, a raw SQL query is used to create the temporary table.
            logger.info(
                "creating transient table with name%s and datatype %s",
                table_name,
                data_type,
            )
            current_session.sql(
                f"""
                CREATE TRANSIENT TABLE {table_name} (
                    {col_without_type} {data_type}
                )
                """
            ).collect()
            # format the values to be inserted into the table
            values_str = ",".join([f"('{value}')" for value in new_values])
            current_session.sql(
                f""" 
                INSERT INTO {table_name} ({col_without_type}) VALUES {values_str}
                """  # noqa: S608
            ).collect()
        else:
            logger.info("creating dataframe with name%s", table_name)
            current_session.create_dataframe(
                data=new_values, schema=[col_without_type]
            ).write.save_as_table(table_name, table_type="transient")

        filter_clause = f"{filter_attribute} IN ( SELECT {col_without_type} FROM {table_name} )"  # noqa: S608
        return filter_clause


class BaseSnowflakeQuery:
    def __init__(self, client_id) -> None:
        self.client_id = client_id
        self.columns = "*"
        self.filters = []
        self.query_params = []
        self.query_str = ""
        self.order_by = ""
        self.annotations = []
        self.group_by = []
        self.limit_count = None
        self.offset_count = None

    def clean_state(self):
        """Function to reset data"""
        self.columns = "*"
        self.filters = []
        self.query_params = []
        self.query_str = ""
        self.order_by = ""
        self.annotations = []
        self.group_by = []
        self.limit_count = None
        self.offset_count = None

    def handle_value(self, value):
        """Function to escape spl characters in str, and other values to form snowflake query"""
        new_value = value
        if isinstance(value, str):
            new_value = escape_special_characters(value)
        if isinstance(value, datetime):
            new_value = str(value)
        return new_value

    def add_filter(self, condition, value=None, column=None):
        """This constructs where clause of query.
        condition: is a string
            eg: 'client_id = ?', 'knowledge_begin_date <= ?, (is_deleted IS NULL OR is_deleted = FALSE)
        value: value that should be replaced with '?' in condition
            eg: 9047, datetime(2025,01,01), None
        column: this is used only for 'IN' operation. we have to send column name on which 'IN' operator is applied.
        """
        if value:
            if isinstance(value, list) and column:
                list_cond = self.handle_list_with_none(column, value)
                self.filters.append(list_cond)
            else:
                self.filters.append(condition)
                value = self.handle_value(value)
                self.query_params.append(value)
        else:
            self.filters.append(condition)
        return self

    def client_filter(self):
        """add basic client_id filter"""
        self.add_filter("client_id = ?", self.client_id)
        return self

    def client_deleted_filter(self):
        """add basic client_id and is_deleted filters"""
        self.add_filter("client_id = ?", self.client_id)
        self.add_filter("(is_deleted IS NULL OR is_deleted = FALSE)")
        return self

    def client_ked_null_filter(self):
        """add basic client_id ,is_deleted and knowledge_end_date = null filters"""
        self.client_deleted_filter()
        self.add_filter("knowledge_end_date IS NULL")
        return self

    def client_kd_filter(self, kd):
        """add basic client_id ,is_deleted and knowledge_date aware filters"""
        self.client_deleted_filter()
        self.add_filter("knowledge_begin_date <= ?::DATETIME", kd)
        self.add_filter(
            "(knowledge_end_date > ?::DATETIME OR knowledge_end_date IS NULL)", kd
        )
        return self

    def handle_list_with_none(self, column, values):
        """function to handle None in list. This is used for IN operator"""
        condition = ""
        snowflake_datatype = "STRING"
        if isinstance(values, list):
            has_none = None in values
            cleaned_values = [
                f"'{self.handle_value(v)}'" for v in values if v is not None
            ]
            # collect snowflake datatype
            if cleaned_values:
                not_none_value = next((v for v in values if v is not None), None)
                if isinstance(not_none_value, datetime):
                    snowflake_datatype = "DATETIME"
            if len(cleaned_values) > SNOWFLAKE_MAX_IN_CLAUSE_PARAMS_LEN - 1:
                not_null_values = [val for val in values if val is not None]
                table_name = "sb_temp_table_" + str(uuid.uuid4()).replace("-", "_")
                temp_condition = temp_table_filter(
                    self.client_id,
                    table_name,
                    tuple(not_null_values),
                    column,
                    data_type=snowflake_datatype,
                )
                if has_none:
                    condition = f"(({temp_condition}) OR {column} IS NULL)"
                else:
                    condition = temp_condition
            elif cleaned_values:
                if has_none:
                    condition = f"({column} IN ({', '.join(cleaned_values)}) OR {column} IS NULL)"
                else:
                    condition = f"{column} IN ({', '.join(cleaned_values)})"
            elif has_none:
                condition = f"({column} IS NULL)"
        else:
            condition = f"{column} = {values}"
        return condition

    def handle_additional_filters(self, additional_filters):
        """handle extra filters. additional_filters: dict with conditon str as key , and param as value"""
        for key, value in additional_filters.items():
            if isinstance(value, list):
                list_condition = self.handle_list_with_none(key, value)
                self.add_filter(list_condition)
            else:
                condition = f"{key} = ?"
                self.add_filter(condition, value)
        return self

    def handle_or_filter(self, or_conditions: list[ORclause]):
        """equivalent to reduce(operator.or_, filter_clauses) in postgres"""
        or_clauses = []
        for or_cond in or_conditions:
            condition = or_cond.condition
            value = or_cond.value
            if value is not None:
                or_clauses.append(condition)
                new_values = [self.handle_value(v) for v in value]
                self.query_params.extend(new_values)
            else:
                or_clauses.append(condition)
        or_cond = f"({' OR '.join(or_clauses)})"
        self.filters.append(or_cond)
        return self

    def annotate(self, alias, function, column):
        self.annotations.append(f"{function}({column}) AS {alias}")
        return self

    def group_by_columns(self, *columns):
        self.group_by.extend(columns)
        return self

    def order(self, column, direction="ASC"):
        self.order_by = f"ORDER BY {column} {direction}"
        return self

    def limit(self, count):
        self.limit_count = count
        return self

    def offset(self, count):
        self.offset_count = count
        return self

    def distinct(self, column):
        self.columns = f"DISTINCT {column}"
        return self

    def select(self, *columns):
        if columns:
            self.columns = ", ".join(columns)
        return self

    def build(self, table_name, query_str=None):
        """function constructs final snowflake query from data attributes"""
        str_columns = "*"
        if isinstance(self.columns, list):
            str_columns = ", ".join(self.columns)
        if isinstance(self.columns, str):
            str_columns = self.columns

        if not query_str:
            self.query_str = f"SELECT {str_columns}"
        else:
            self.query_str = query_str

        if self.annotations:
            self.query_str += ", " + ", ".join(self.annotations)

        self.query_str += f" FROM {table_name}"

        if self.filters:
            self.query_str += " WHERE " + " AND ".join(self.filters)

        if self.group_by:
            self.query_str += " GROUP BY " + ", ".join(self.group_by)

        if self.order_by:
            self.query_str += " " + self.order_by

        if self.limit_count is not None:
            self.query_str += f" LIMIT {self.limit_count}"

        if self.offset_count is not None:
            self.query_str += f" OFFSET {self.offset_count}"

        return self.query_str + ";", self.query_params

    def fetch_result(self, details):  # noqa: PLR0911
        """to execute the snowflake query and params from each accessor function and return results accordingly
        details: dict containing info about result format
            'as_dicts': determines if the results should be dict or Model()
            'model': Django Model
            'return_type': can be number, bool, list etc
            'column_value': this should be present, when return_type is 'list'.
        query_str: snowflake query string
        params: snowflake query params"""
        if self.query_str is None:
            return []
        client_id = details["client_id"]
        model = details["model"]
        model_name = model._meta.model_name  # noqa: SLF001
        model_fields = []
        date_fields = []
        for field in model._meta.get_fields():  # noqa: SLF001
            if not field.auto_created:
                model_fields.append(field.name)
                if isinstance(field, DateTimeField):
                    date_fields.append(field.name)
        logger.info("Snowflake Query - %s", str(self.query_str))
        logger.info("Snowflake Query Params - %s", str(self.query_params))

        with create_snowpark_session_wrapper(
            session=None,
            client_id=client_id,
            tag={"client_id": self.client_id, "object": model_name},
        ) as snowpark_session:
            result = snowpark_session.sql(
                self.query_str, tuple(self.query_params)
            ).collect()

        ##### Convert Snowflake.Row to django model like dict
        result_dicts = [
            {
                k.lower(): (
                    make_aware_wrapper(v)
                    if (k.lower() in date_fields and v is not None)
                    else v
                )
                for k, v in row.as_dict().items()
                if k.lower() in model_fields
            }
            for row in result
        ]
        as_dicts = details.get("as_dicts")
        return_type = details.get("return_type")
        # get object to clean state after getting results from snowflake
        self.clean_state()
        # as_dicts is True, send results as dicts. Else, send result as Model Object.
        if return_type is None:
            if as_dicts is True:
                return result_dicts
            elif as_dicts is False:  # noqa: RET505
                result_objs = [model(**r) for r in result_dicts]
                return result_objs
        elif return_type == "list":
            column = details.get("column_value")
            res = [r.get(column) for r in result_dicts]
            return res
        elif return_type == "number" and len(result) == 1:
            res = list(result[0].as_dict().values())[0]
            return res
        elif return_type == "bool":
            if (
                len(result) == 1 and list(result[0].as_dict().values())[0]
            ):  # noqa: SIM103
                return True
            return False

        return result_dicts
