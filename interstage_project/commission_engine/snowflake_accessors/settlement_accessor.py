import datetime

from commission_engine.accessors.settlement_lock_accessor import SettlementLockAccessor
from commission_engine.models import Settlement
from commission_engine.snowflake_accessors.base_snowflake_accessor import (
    BaseSnowflakeQuery,
    ORclause,
)


class SettlementSnowflakeAccessor(BaseSnowflakeQuery):
    def __init__(self, client_id) -> None:
        self._table_name = "settlement_" + str(client_id)
        self.additional_details = {"client_id": client_id}
        self.additional_details["model"] = Settlement
        super().__init__(client_id)

    def settlement_flag_filter(self, kd=None):
        if kd:
            self.client_kd_filter(kd)
        else:
            self.client_ked_null_filter()
        self.add_filter("settlement_flag = TRUE")
        return self

    def get_all_payees_having_settlement(self, email_ids=None):
        self.additional_details["return_type"] = "list"
        self.additional_details["column_value"] = "payee_email_id"
        self.client_ked_null_filter()
        self.add_filter("payee_email_id = ?", value=email_ids, column="payee_email_id")
        self.distinct("payee_email_id")
        self.build(self._table_name)
        res = self.fetch_result(self.additional_details)
        return res

    def get_kd_settlement_data_for_end_date(
        self, kd, ped, payee_email, as_dicts=False  # noqa: FBT002
    ):
        self.additional_details["as_dicts"] = as_dicts
        self.settlement_flag_filter(kd)
        self.add_filter("period_end_date::datetime = ?", ped)
        self.add_filter("payee_email_id = ?", payee_email)
        self.build(self._table_name)
        return self.fetch_result(self.additional_details)

    def get_kd_settlement_data_for_end_date_for_payees(
        self, kd, ped, payee_emails, as_dicts=False  # noqa: FBT002
    ):
        self.additional_details["as_dicts"] = as_dicts
        self.settlement_flag_filter(kd)
        self.add_filter("period_end_date::datetime = ?", ped)
        self.add_filter("payee_email_id = ?", payee_emails, "payee_email_id")
        self.build(self._table_name)
        return self.fetch_result(self.additional_details)

    def get_settlements_curr_period(  # noqa: PLR0913
        self,
        psd,
        ped,
        payee_email,
        locked_kd=None,
        additional_filters=None,
        fields=None,
    ):

        if isinstance(fields, (list, tuple)):
            self.columns = fields

        self.settlement_flag_filter(locked_kd)
        self.add_filter("payee_email_id = ?", payee_email)
        self.add_filter("comm_period_start_date = ?::DATETIME ", psd)
        self.add_filter("comm_period_end_date = ?::DATETIME", ped)
        self.add_filter("period_start_date = ?::DATETIME", psd)
        self.add_filter("period_end_date = ?::DATETIME", ped)

        if additional_filters:
            self.handle_additional_filters(additional_filters)
        self.order("knowledge_begin_date", "DESC")
        self.build(self._table_name)
        return self.fetch_result(self.additional_details)

    def get_settlements_curr_period_count(  # noqa: PLR0913
        self,
        psd,
        ped,
        payee_email,
        locked_kd=None,
        additional_filters=None,
    ):
        self.additional_details["return_type"] = "number"
        self.columns = ["commission_row_key"]
        self.settlement_flag_filter(locked_kd)
        self.add_filter("payee_email_id = ?", payee_email)
        self.add_filter("comm_period_start_date =?::DATETIME ", psd)
        self.add_filter("comm_period_end_date = ?::DATETIME", ped)
        self.add_filter("period_start_date = ?::DATETIME", psd)
        self.add_filter("period_end_date = ?::DATETIME", ped)

        if additional_filters:
            self.handle_additional_filters(additional_filters)

        (subquery, params) = (
            self.select("commission_row_key")
            .annotate("max_knowledge_begin_date", "MAX", "knowledge_begin_date")
            .group_by_columns("commission_row_key")
            .build(self._table_name)
        )
        # remove ';' from subquery in end
        self.query_str = (
            f"SELECT COUNT(*) FROM ({subquery[:-1]}) AS subquery"  # noqa: S608
        )
        self.query_params = params
        return self.fetch_result(self.additional_details)

    def get_settlements_prev_period(  # noqa: PLR0913
        self,
        psd,
        ped,
        payee_email,
        locked_kd,
        additional_filters=None,
        fields=None,
    ):
        self.settlement_flag_filter(locked_kd)
        self.add_filter("payee_email_id = ?", payee_email)
        self.add_filter("period_start_date::datetime = ?::DATETIME", psd)
        self.add_filter("period_end_date::datetime = ?::DATETIME", ped)
        self.add_filter("comm_period_start_date::datetime < ?::DATETIME", psd)
        if additional_filters:
            self.handle_additional_filters(additional_filters)

        if isinstance(fields, (list, tuple)):
            self.columns = fields
        self.order("knowledge_begin_date", "DESC")
        self.build(self._table_name)
        return self.fetch_result(self.additional_details)

    def _get_settlements_prev_period_unique_commission_row_keys(  # noqa: PLR0913
        self,
        comm_ped,
        psd,
        ped,
        payee_email,
        locked_kd,
        additional_filters=None,
    ):
        self.settlement_flag_filter(locked_kd)
        self.add_filter("payee_email_id = ?", payee_email)
        self.add_filter("comm_period_end_date::datetime = ?::DATETIME", comm_ped)
        self.add_filter("period_start_date::datetime = ?::DATETIME", psd)
        self.add_filter("period_end_date::datetime = ?::DATETIME", ped)

        if additional_filters:
            self.handle_additional_filters(additional_filters)
        self.columns = ["commission_row_key"]
        return self.filters

    def get_settlements_prev_period_unique_commission_row_keys(  # noqa: PLR0913
        self,
        comm_ped,
        psd,
        ped,
        payee_email,
        locked_kd,
        additional_filters=None,
        limit=None,
        offset=None,
    ):
        self.additional_details["return_type"] = "list"
        self.additional_details["column_value"] = "commission_row_key"
        self.filters = self._get_settlements_prev_period_unique_commission_row_keys(
            comm_ped=comm_ped,
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            locked_kd=locked_kd,
            additional_filters=additional_filters,
        )
        if limit is not None and offset is not None:
            self.limit(limit)
            self.offset(limit * offset)

        self.select("commission_row_key").annotate(
            "max_knowledge_begin_date", "MAX", "knowledge_begin_date"
        ).group_by_columns("commission_row_key").build(self._table_name)

        return self.fetch_result(self.additional_details)

    def get_settlements_prev_period_unique_commission_row_keys_count(  # noqa: PLR0913
        self,
        comm_ped,
        psd,
        ped,
        payee_email,
        locked_kd,
        additional_filters=None,
    ):
        self.additional_details["return_type"] = "number"
        self.filters = self._get_settlements_prev_period_unique_commission_row_keys(
            comm_ped=comm_ped,
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            locked_kd=locked_kd,
            additional_filters=additional_filters,
        )
        (subquery, params) = (
            self.select("commission_row_key")
            .annotate("max_knowledge_begin_date", "MAX", "knowledge_begin_date")
            .group_by_columns("commission_row_key")
            .build(self._table_name)
        )
        self.query_str = (
            f"SELECT COUNT(*) FROM ({subquery[:-1]}) AS subquery"  # noqa: S608
        )
        self.query_params = params
        return self.fetch_result(self.additional_details)

    def get_payees_settlement_data(
        self, ped, locked_kd_map, payee_psd_map, prev_period=False  # noqa: FBT002
    ):
        self.client_deleted_filter()
        self.add_filter("settlement_flag = TRUE")
        or_conditions = []
        for payee in locked_kd_map:
            knowledge_date = None
            if locked_kd_map[payee]:
                knowledge_date = locked_kd_map[payee]["locked_knowledge_date"]
            psd = payee_psd_map[payee]
            if knowledge_date:
                cond = "(knowledge_begin_date <= ?::DATETIME AND (knowledge_end_date > ?::DATETIME OR knowledge_end_date IS NULL) AND payee_email_id = ?  AND period_end_date = ?::DATETIME AND period_start_date = ?::DATETIME "
                params = [knowledge_date, knowledge_date, payee, ped, psd]
            else:
                cond = "(knowledge_end_date IS NULL AND payee_email_id = ?  AND period_end_date = ?::DATETIME AND period_start_date = ?::DATETIME "
                params = [payee, ped, psd]
            if prev_period:
                cond += "AND comm_period_start_date < ?::DATETIME)"
                params.extend([psd])
            else:
                cond += "AND comm_period_start_date = ?::DATETIME AND comm_period_end_date = ?::DATETIME)"
                params.extend([psd, ped])
            or_cond = {"condition": cond, "value": params}
            or_conditions.append(ORclause(**or_cond))
        self.handle_or_filter(or_conditions)
        if prev_period:
            self.columns = [
                "payee_email_id",
                "plan_id",
                "criteria_id",
                "amount",
                "comm_period_start_date",
                "comm_period_end_date",
                "commission_row_key",
                "knowledge_begin_date",
            ]
        else:
            self.columns = [
                "payee_email_id",
                "plan_id",
                "criteria_id",
                "amount",
                "commission_row_key",
            ]

        self.build(self._table_name)
        return self.fetch_result(self.additional_details)

    def payees_for_end_date(self, end_date):
        self.additional_details["return_type"] = "list"
        self.additional_details["column_value"] = "payee_email_id"
        self.client_ked_null_filter()
        self.add_filter("period_end_date = ?::DATETIME", end_date)
        self.distinct("payee_email_id")
        self.build(self._table_name)
        return self.fetch_result(self.additional_details)

    def does_settlement_exist_for_payee(self, email_id):
        self.additional_details["return_type"] = "bool"
        self.select("1")
        self.client_ked_null_filter()
        self.add_filter("payee_email_id = ?", email_id)
        self.limit(1)
        self.build(self._table_name)
        return self.fetch_result(self.additional_details)

    def distinct_periods_for_payee(self, email_id):
        self.additional_details["return_type"] = "list"
        self.additional_details["column_value"] = "period_end_date"
        self.columns = ["period_end_date"]
        self.client_ked_null_filter()
        self.add_filter("payee_email_id = ?", email_id)
        self.build(self._table_name)
        return self.fetch_result(self.additional_details)

    def _get_after_lock_records(self, ped, payee_emails):
        self.clean_state()
        self.columns = ["payee_email_id", "line_item_id", "amount"]
        self.add_filter("client_id = ?", self.client_id)
        self.add_filter("period_end_date = ?::DATETIME", ped)
        self.add_filter("payee_email_id = ?", payee_emails, "payee_email_id")
        self.add_filter("knowledge_end_date IS NULL")
        self.build(self._table_name)
        return self.fetch_result(self.additional_details)

    def _get_before_lock_records(self, ped, payee_emails, locked_kd_map):
        self.clean_state()
        or_conditions = []
        for payee in payee_emails:
            locked_date = locked_kd_map.get(payee)
            if locked_date:
                condition = "(payee_email_id = ? AND knowledge_begin_date <= ?::DATETIME AND (knowledge_end_date > ?::DATETIME OR knowledge_end_date IS NULL))"
                or_conditions.append(
                    ORclause(
                        **{
                            "condition": condition,
                            "value": [payee, locked_date, locked_date],
                        }
                    )
                )
            else:
                condition = "(payee_email_id = ? AND knowledge_end_date IS NULL)"
                or_conditions.append(
                    ORclause(**{"condition": condition, "value": [payee]})
                )

        self.columns = ["payee_email_id", "line_item_id", "amount"]
        self.add_filter("client_id = ?", self.client_id)
        self.add_filter("period_end_date = ?::DATETIME", ped)
        self.add_filter("payee_email_id = ?", payee_emails, "payee_email_id")
        self.handle_or_filter(or_conditions)
        self.build(self._table_name)
        res = self.fetch_result(self.additional_details)
        return res

    def get_settlements_before_and_after_lock(self, ped, payee_emails: list[str]):
        #  Fetch locked knowledge dates for the given payees
        locked_kd_records = SettlementLockAccessor(self.client_id).get_payees_locked_kd(
            payee_email_ids=payee_emails,
            ped=ped,
        )

        locked_kd_map = {
            record["payee_email_id"]: record["locked_knowledge_date"]
            for record in locked_kd_records
        }
        # Filter settlement after the lock (where knowledge_end_date is null)
        after_lock_entries = self._get_after_lock_records(ped, payee_emails)
        before_lock_entries = self._get_before_lock_records(
            ped, payee_emails, locked_kd_map
        )
        return (before_lock_entries, after_lock_entries)

    def _get_kbd_gte(self, knowledge_date, exclude_ped_none):
        self.clean_state()
        self.columns = ["payee_email_id", "period_end_date"]
        self.add_filter("client_id = ?", self.client_id)
        self.add_filter("knowledge_begin_date >= ?::DATETIME", knowledge_date)
        if exclude_ped_none:
            self.add_filter("period_end_date IS NOT NULL")
        (subquery, params) = self.build(self._table_name)
        return (subquery, params)

    def _get_ked_gte(self, knowledge_date, exclude_ped_none):
        self.clean_state()
        self.columns = ["payee_email_id", "period_end_date"]
        self.add_filter("client_id = ?", self.client_id)
        self.add_filter("knowledge_end_date >= ?::DATETIME", knowledge_date)
        if exclude_ped_none:
            self.add_filter("period_end_date IS NOT NULL")
        (subquery, params) = self.build(self._table_name)
        return (subquery, params)

    def get_payee_periods_modified_after_kd(
        self,
        knowledge_date: datetime.datetime,
        exclude_ped_none: bool = False,  # noqa: FBT001, FBT002
    ):
        (kbd_query_str, kbd_query_params) = self._get_kbd_gte(
            knowledge_date, exclude_ped_none
        )
        (ked_query_str, ked_query_params) = self._get_ked_gte(
            knowledge_date, exclude_ped_none
        )
        # remove ';' from kbd_query
        self.query_str = f"{kbd_query_str[:-1]} UNION {ked_query_str}"
        self.query_params = kbd_query_params + ked_query_params
        return self.fetch_result(self.additional_details)
