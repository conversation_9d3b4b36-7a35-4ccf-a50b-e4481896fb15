import decimal

import pydash

import commission_engine.services.commission_calculation_service.ast as ast
from commission_engine.services.commission_calculation_service.parser_v2 import (
    BaseInfixParser,
)
from commission_engine.services.expression_designer import get_expression_format
from spm.accessors.config_accessors.employee_accessor import EmployeePayrollAccessor


def create_ast(infix_exp):
    if not isinstance(infix_exp, list):
        infix_exp = [infix_exp]

    infix_exp_version = get_expression_format(infix_exp)
    if infix_exp_version == "v2":
        ast_t = BaseInfixParser().parse_wrapper(infix_exp)
    else:
        ast_t = ast.parse(infix_exp)

    t = ast_t["ast"]
    o = ast_t["os"]
    return {"ast": t, "op_stack": o}


def delete_pay_details(line_item):
    if line_item:
        # Commenting the fixed_pay as we not using in evaluate and reports
        # When the user_report is used as criteria_evaluation_sheet we should not delete the fixed_pay
        # if "fixed_pay" in line_item:
        #     del line_item["fixed_pay"]
        if "variable_pay" in line_item:
            del line_item["variable_pay"]
    return line_item


def delete_pay_add_key_from_merged_data(merged_data, key):
    data = []
    if merged_data:
        for line_item in merged_data:
            li = delete_pay_details(line_item)
            li["key"] = line_item[key]
            data.append(li)
    return data


def get_slab_details(part2_exp):
    slab_bound = {}
    slab_ast = {}
    slab_override = {}
    slab_name = {}
    for ind, item in enumerate(part2_exp):
        key = ind
        lb = decimal.Decimal(item["lower_bound"])
        ub = decimal.Decimal(item["upper_bound"])

        slab_bound[key] = [lb, ub]
        slab_ast[key] = create_ast(item["expression_stack"])["ast"]
        slab_override[key] = (
            item["is_override_slab"] if "is_override_slab" in item else False
        )
        tier_name = (
            item["tier_name"]
            if "tier_name" in item
            and item["tier_name"] is not None
            and len(item["tier_name"]) > 0
            else str(key)
        )
        slab_name[key] = tier_name
    return (slab_bound, slab_ast, slab_override, slab_name)


def get_slab_infix(part2_exp):
    slab_infix = {}
    for ind, item in enumerate(part2_exp):
        key = ind
        slab_infix[key] = item["expression_stack"]

    return slab_infix


def generate_map_of_merged_data(merged_data, key):
    data = {}
    for item in merged_data:
        data_key = pydash.get(item, key)
        if data_key:
            data[data_key] = item
    return data


def generate_hash(data_list, key):
    return generate_map_of_merged_data(data_list, key)


def get_payout_details(client_id, period_end_date, emails):
    ea = EmployeePayrollAccessor(client_id)
    if not isinstance(emails, list):
        emails = [emails]
    emps = ea.get_employee_payroll(period_end_date, emails)
    res = {}
    for emp in emps:
        res[emp["employee_email_id"]] = emp["payout_frequency"]
    return res
