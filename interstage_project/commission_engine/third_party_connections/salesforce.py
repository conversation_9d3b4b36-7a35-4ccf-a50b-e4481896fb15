# pylint: disable=undefined-variable,unused-argument,dangerous-default-value
import logging
from datetime import datetime, timedelta
from importlib import import_module
from typing import List, Optional, TypedDict
from uuid import UUID

from requests import request
from rest_framework import status

from commission_engine.accessors.etl_config_accessor import (
    AccessTokenConfigAccessor,
    ExtractionConfigAccessor,
    IntegrationAccessor,
    TransformationConfigAccessor,
)
from commission_engine.accessors.etl_housekeeping_accessor import (
    UpstreamETLStatusAccessor,
)
from commission_engine.models.etl_config_models import (
    AccessTokenConfig,
    ApiAccessConfig,
)
from commission_engine.self_service_integrations.salesforce_self_service_integration import (
    SalesforceSelfServiceIntegration,
)
from commission_engine.third_party_connections.exceptions.missing_field_exception import (
    MissingFieldException,
)
from commission_engine.third_party_connections.exceptions.record_not_found_exception import (
    RecordNotFoundException,
)
from commission_engine.third_party_connections.third_party_api import ThirdPartyApi
from commission_engine.third_party_connections.utils import (
    check_primary_or_snapshot_key_deleted,
    get_primary_and_snapshot_keys_for_integration,
)
from commission_engine.utils.general_data import (
    SUPPORTED_SALESFORCE_FUNCTIONS,
    UpstreamETLVersions,
)
from commission_engine.utils.log_utils import merge_log_context
from everstage_ddd.upstream import UpstreamChangesWriter
from interstage_project.threadlocal_log_context import (
    get_threadlocal_context,
    set_threadlocal_context,
)

logger = logging.getLogger(__name__)


# Python type declarion class for Access Token Credentials
class Credentials(TypedDict):
    client_id: str
    client_secret: str
    username: str
    password: str


class SObjectMeta(TypedDict):
    name: str
    label: str


class Salesforce(ThirdPartyApi):

    def __init__(self):
        self.source_deleted_fields = []

    def verify_deleted_fields(
        self,
        client_id: int,
        integration_id: UUID,
        sobject: str,
        trans_source_fields: list[str],
    ) -> list[str]:
        """
        Checks if the fields are present in the source object by comparing with the name field.
        Stores the mapping of deleted fields in source_deleted_fields and returns updated field list.

        Args:
            client_id: Client ID
            integration_id: Integration ID
            sobject: Source object name
            trans_source_fields: List of transformation source fields

        Returns:
            List of fields that are present in the source object
        """
        try:
            atc_id = (
                ExtractionConfigAccessor(client_id)
                .get_record_by_integration_id(integration_id)[0]
                .access_token_config_id
            )
            salesforce_self_service = SalesforceSelfServiceIntegration(
                client_id, atc_id
            )
            salesforce_self_service.clear_cached_fields_for_object(sobject)
            sobject_details = salesforce_self_service.get_all_fields_in_object(sobject)
            available_fields = {field["name"] for field in sobject_details}

            # Clean transformation fields and extract api name for comparison
            cleaned_fields = []
            original_to_transformed = {}  # Map original field to transformed field

            for field in trans_source_fields:
                field_lower = field.lower()
                # Check if field contains any Salesforce function
                contains_function = any(
                    func.lower() in field_lower
                    for func in SUPPORTED_SALESFORCE_FUNCTIONS
                )

                if contains_function:
                    # Extract original field name from function call
                    # e.g. convertCurrency(Total_Value__c) Total_Value__c_converted
                    original_field = field[field.find("(") + 1 : field.find(")")]
                    cleaned_fields.append(original_field)
                    original_to_transformed[original_field] = field
                else:
                    cleaned_fields.append(field)
                    original_to_transformed[field] = field

            # Find unique deleted fields (fields in transformation but not in source)
            # There can be duplicate due to SOQL functions
            deleted_fields = list(
                {field for field in cleaned_fields if field not in available_fields}
            )

            # Store deleted fields mapping
            if deleted_fields:
                self.source_deleted_fields.extend(deleted_fields)
                logger.info(
                    "Found deleted fields for object %s: %s", sobject, deleted_fields
                )

            if check_primary_or_snapshot_key_deleted(
                client_id, integration_id, deleted_fields
            ):
                deleted_fields_str = ",".join(deleted_fields)
                raise MissingFieldException(  # noqa: TRY301
                    field=deleted_fields_str,
                    message=f"The primary or snapshot key is deleted at the source. Deleted fields: {deleted_fields_str}",
                    deleted_fields=deleted_fields,
                )

            # Return original transformed fields for available fields
            return [
                original_to_transformed[field]
                for field in cleaned_fields
                if field in available_fields
            ]

        except MissingFieldException:
            logger.exception(
                "Error verifying deleted fields, Primary or snapshot key is deleted at the source."
            )
            raise
        except Exception:
            logger.exception("Error verifying deleted fields")
            return trans_source_fields  # Return original fields in case of error

    def get_fields_for_object(
        self,
        client_id: int,
        integration_id: UUID,
        sobject: str,
        is_validation: bool,  # noqa: FBT001
    ) -> list[str]:
        """
        Returns a list of source field names for a given object.
        """

        trans_configs = list(
            TransformationConfigAccessor(client_id).get_records_by_integration_id(
                integration_id
            )
        )
        field_list = []
        for tc in trans_configs:
            if tc.additional_config:  # Example: {"api_field": convertCurrency(amount)}
                field = tc.additional_config.get("api_field", tc.source_field)
            else:
                field = tc.source_field
            field_list.append(field)

        return (
            self.verify_deleted_fields(client_id, integration_id, sobject, field_list)
            if not is_validation
            else field_list
        )

    def get_query_api_params(
        self,
        client_id: int,
        object_id: str,
        config_object: ApiAccessConfig,
        primary_kd: datetime,
        start_time: datetime,
        integration_id: UUID,
        params: dict = {},
        should_lower_order_by: bool = False,
        is_validation: bool = False,  # noqa: FBT001, FBT002
    ) -> dict:
        """
        Returns a dictionary with query parameters for the /query or /queryAll API.
        """

        additional_data = getattr(config_object, "additional_data", {})
        limit_value = additional_data.get("limit_value", 2000)
        order_by_field = additional_data.get("order_by_field", "Id")

        # sobject is taken from the params in the additional
        # delete sync flow as object_id will be the source object
        sobject = (
            object_id.capitalize()
            if not params.get("sobject")
            else params.get("sobject").capitalize()
        )

        # fields are taken from the params in the additional
        # delete flow as there will be no transformation configs
        field_list = (
            self.get_fields_for_object(
                client_id, integration_id, sobject, is_validation
            )
            if not params.get("fields")
            else params.get("fields").split(",")
        )
        # order_by_field is added to the field list as we
        # need it to get the offset id for the next page
        field_list.append(
            order_by_field.lower() if should_lower_order_by else order_by_field
        )
        fields = ",".join(set(field_list))

        start = (
            start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
            if start_time
            else (primary_kd - timedelta(days=7)).strftime("%Y-%m-%dT%H:%M:%SZ")
        )
        end = primary_kd.strftime("%Y-%m-%dT%H:%M:%SZ")

        _params = params.copy()
        if params.get("fields"):
            del _params["fields"]

        return {
            **_params,
            "sobject": sobject,
            "fields": fields,
            "start": start,
            "end": end,
            "limit_value": limit_value,
            "order_by_field": order_by_field,
        }

    def get_records_from_query_api(
        self,
        client_id: int,
        object_id: str,
        config_object: ApiAccessConfig,
        query_params: dict,
        is_validation: bool,
        e2e_sync_run_id: UUID,
        sync_run_id: UUID,
        mode: str,
        integration_id: UUID,
        adc_params=None,
        upstream_etl_version: str = UpstreamETLVersions.V1.value,
        source_primary_keys: Optional[List[str]] = None,
        snowflake_uploader: Optional[UpstreamChangesWriter] = None,
    ):
        """
        Returns a list of records from the /query or /queryAll API.
        """
        module_name = "everstage_etl.tasks.extraction"
        module = import_module(module_name)
        insert_extract_sync_log = getattr(module, "insert_extract_sync_log")

        if source_primary_keys is None:
            source_primary_keys = []
        request_body = getattr(config_object, "request_body")
        request_url = getattr(config_object, "request_url")
        order_by_field = query_params.get("order_by_field")
        limit_value = query_params.get("limit_value")

        body = {}
        result = {}
        curr_page = 0
        total_so_far = 0
        offset_id = None
        done = False
        records = []

        logger.info(
            f"Starting API calls for {object_id}: Query params - {query_params}"
        )
        while not done:
            # offset_condition is set based on the offset_id, which is the
            # last record's order_by_field value from the previous page
            query_params["offset_condition"] = (
                f" and {order_by_field} > '{offset_id}' " if offset_id else ""
            )
            body["q"] = request_body.get("q").format(
                **query_params,
            )

            logger.info(
                f"Making API call for {object_id} with offset {order_by_field} - {offset_id}"
            )
            result = self.api_call(
                client_id,
                request_url,
                body,
                config_object,
                is_validation=is_validation,
            )

            page_records = result.get("records", [])

            page_size = len(page_records)
            total_so_far += page_size
            if page_size > 0:
                if snowflake_uploader:
                    if mode == "secondary_delete":
                        sec_delete_recs = [
                            {
                                "Id": record.get(
                                    adc_params["fields"].split(",")[0]  # type:ignore
                                )
                            }
                            for record in page_records
                        ]
                        snowflake_uploader.save_in_temp_table(sec_delete_recs)
                    else:  # changes, delete
                        snowflake_uploader.save_in_temp_table(page_records)
                elif upstream_etl_version == UpstreamETLVersions.V1.value:
                    records.extend(page_records)
                else:
                    logger.info(
                        f"BEGIN: Batch insert current page ({curr_page}) {mode} records of length {len(page_records)} to extraction_sync_log"
                    )
                    insert_records = page_records
                    if mode == "delete":
                        source_pks, _ = get_primary_and_snapshot_keys_for_integration(
                            client_id, integration_id
                        )
                        deleted_ids = []
                        for rec in page_records:
                            rec = dict(
                                (key.lower(), value) for key, value in rec.items()
                            )
                            id_val = "#:::#".join(
                                [str(rec[pk]) for pk in source_pks]
                            ).lower()
                            deleted_ids.append({"Id": id_val})
                        insert_records = deleted_ids
                    elif mode == "secondary_delete":
                        deleted_ids = [
                            {
                                "Id": rec.get(
                                    adc_params.get("fields").split(",")[0]
                                ).lower()
                            }
                            for rec in page_records
                        ]
                        insert_records = deleted_ids
                    insert_extract_sync_log(
                        client_id,
                        e2e_sync_run_id,
                        sync_run_id,
                        object_id,
                        insert_records,
                        mode,
                        source_primary_keys=source_primary_keys,
                    )
                    logger.info(
                        f"END: Batch insert current page ({curr_page}) {mode} records of length {len(page_records)} to extraction_sync_log"
                    )

                offset_id = page_records[-1].get(order_by_field)
                curr_page += 1
                logger.info(
                    f"Page {curr_page}: Number of records extracted - {page_size}, Last record's {order_by_field} - {offset_id}"
                )
            done = (
                # totalSize is the size of query result and result["done"]
                # is true when totalSize records are fetched for the query
                (result.get("totalSize", 0) < limit_value and result.get("done", True))
                # No records fetched in the current page
                or (page_size == 0)
                # No offset_id set after the current page
                or (not offset_id)
                # Skip pagination during ETL validation
                or is_validation
            )

        logger.info(
            f"Done making API calls for {object_id}: Total pages - {curr_page}, Total records - {total_so_far}"
        )

        if total_so_far > 0 and snowflake_uploader:
            snowflake_uploader.save()
        if mode == "change":
            UpstreamETLStatusAccessor(
                client_id=client_id,
                e2e_sync_run_id=e2e_sync_run_id,
                sync_run_id=sync_run_id,
            ).update_object({"extracted_records_count": total_so_far})
        else:
            UpstreamETLStatusAccessor(
                client_id=client_id,
                e2e_sync_run_id=e2e_sync_run_id,
                sync_run_id=sync_run_id,
            ).update_object({"deleted_records_count": total_so_far})

        return records, total_so_far

    def get_records_from_sobjects_deleted_api(
        self,
        client_id: int,
        object_id: str,
        config_object: ApiAccessConfig,
        params: dict,
        is_validation: bool,
        e2e_sync_run_id: UUID,
        sync_run_id: UUID,
        upstream_etl_version: str = UpstreamETLVersions.V1.value,
        source_primary_keys: Optional[List[str]] = None,
        snowflake_uploader: Optional[UpstreamChangesWriter] = None,
    ) -> list[dict]:
        """
        Returns a list of records from the /sobjects/deleted API.
        """
        module_name = "everstage_etl.tasks.extraction"
        module = import_module(module_name)
        insert_extract_sync_log = getattr(module, "insert_extract_sync_log")

        if source_primary_keys is None:
            source_primary_keys = []
        next_url = getattr(config_object, "request_url")
        result = {}
        curr_page = 0
        records = []
        total_deleted_so_far = 0

        logger.info(f"Starting API calls for {object_id}: Params - {params}")
        while next_url:
            logger.info(f"Making API call for {object_id} with URL - {next_url}")
            result = self.api_call(
                client_id,
                next_url,
                params,
                config_object,
                is_validation=is_validation,
            )

            page_records = result.get("deletedRecords", [])
            page_size = len(page_records)
            total_deleted_so_far += page_size
            if page_size > 0:
                if snowflake_uploader and page_records:
                    snowflake_uploader.save_in_temp_table(page_records)
                elif upstream_etl_version == UpstreamETLVersions.V1.value:
                    records.extend(page_records)
                else:
                    logger.info(
                        f"BEGIN: Batch insert current page ({curr_page}) deleted records of length {len(page_records)} to extraction_sync_log"
                    )
                    deleted_ids = [
                        {"Id": rec.get("id").lower()} for rec in page_records
                    ]
                    page_records = deleted_ids
                    insert_extract_sync_log(
                        client_id,
                        e2e_sync_run_id,
                        sync_run_id,
                        object_id,
                        page_records,
                        "delete",
                        source_primary_keys=source_primary_keys,
                    )
                    logger.info(
                        f"END: Batch insert current page ({curr_page}) deleted records of length {len(page_records)} to extraction_sync_log"
                    )
                curr_page += 1
                logger.info(
                    f"Page {curr_page}: Number of records extracted - {page_size}"
                )
            # result["nextRecordsUrl"] is none when all records are fetched
            next_url = result.get("nextRecordsUrl", None) if not is_validation else None
            params = {} if next_url else params

        logger.info(
            f"Done making API calls for {object_id}: Total pages - {curr_page}, Total records - {total_deleted_so_far}"
        )

        if snowflake_uploader:
            snowflake_uploader.save()

        UpstreamETLStatusAccessor(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
        ).update_object({"deleted_records_count": total_deleted_so_far})

        return records

    def get_deleted_records(
        self,
        client_id: int,
        object_id: str,
        e2e_sync_run_id: UUID,
        sync_run_id: UUID,
        primary_kd: datetime,
        delete_start_time: datetime,
        sync_mode: str,
        config_object: ApiAccessConfig,
        _unused_destination_object_type: str,
        integration_id: UUID,
        is_validation: bool,
        params: dict = {},
        upstream_etl_version: str = UpstreamETLVersions.V1.value,
        source_primary_keys: Optional[List[str]] = None,
        snowflake_uploader: Optional[UpstreamChangesWriter] = None,
    ) -> dict:
        """
        Returns a list of deleted records from the /queryAll, /sobjects/deleted or /query API.
        """
        if source_primary_keys is None:
            source_primary_keys = []
        old_log_context = get_threadlocal_context()
        new_log_context = {
            "client_id": client_id,
            "object_id": object_id,
            "changes_start_time": delete_start_time,
            "e2e_sync_run_id": e2e_sync_run_id,
            "sync_run_id": sync_run_id,
        }

        merged_log_context = merge_log_context(old_log_context, new_log_context)

        set_threadlocal_context(merged_log_context)

        integration_record = IntegrationAccessor(
            client_id=client_id
        ).get_object_by_integration_id(integration_id=integration_id)
        additional_data = integration_record.additional_data  # type: ignore
        run_snowflake_sync = (additional_data or {}).get("run_snowflake_sync", False)

        if not snowflake_uploader and run_snowflake_sync:
            snowflake_uploader = UpstreamChangesWriter(
                client_id=client_id,
                integration_id=integration_id,
                is_validation=is_validation,
            )
            snowflake_uploader.set_meta(is_deleted=True, updated_on=primary_kd)

        api_type = getattr(config_object, "additional_data").get("api_type")
        deleted_records = []
        deleted_ids = []

        # Regular delete sync flow for objects that have IsDeleted field
        if api_type == "queryAll":
            source_pks, _ = get_primary_and_snapshot_keys_for_integration(
                client_id, integration_id
            )
            query_params = self.get_query_api_params(
                client_id=client_id,
                object_id=object_id,
                config_object=config_object,
                primary_kd=primary_kd,
                start_time=delete_start_time,
                integration_id=integration_id,
            )
            (
                deleted_records,
                _unused_total_records_fetched,
            ) = self.get_records_from_query_api(
                client_id=client_id,
                object_id=object_id,
                config_object=config_object,
                query_params=query_params,
                is_validation=is_validation,
                e2e_sync_run_id=e2e_sync_run_id,
                sync_run_id=sync_run_id,
                upstream_etl_version=upstream_etl_version,
                mode="delete",
                source_primary_keys=source_primary_keys,
                integration_id=integration_id,
                snowflake_uploader=snowflake_uploader,
            )
            for rec in deleted_records:
                rec = dict((key.lower(), value) for key, value in rec.items())
                id_val = "#:::#".join([str(rec[pk]) for pk in source_pks]).lower()
                deleted_ids.append({"Id": id_val})

        # Delete sync flow for objects that don't have IsDeleted field
        elif api_type == "sObjects":
            delete_start_time = max(delete_start_time, primary_kd - timedelta(days=28))
            params = {
                "start": delete_start_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "end": primary_kd.strftime("%Y-%m-%dT%H:%M:%SZ"),
            }
            deleted_records = self.get_records_from_sobjects_deleted_api(
                client_id=client_id,
                object_id=object_id,
                config_object=config_object,
                params=params,
                is_validation=is_validation,
                e2e_sync_run_id=e2e_sync_run_id,
                sync_run_id=sync_run_id,
                upstream_etl_version=upstream_etl_version,
                source_primary_keys=source_primary_keys,
                snowflake_uploader=snowflake_uploader,
            )
            deleted_ids = [{"Id": rec.get("id").lower()} for rec in deleted_records]

        # Additional delete sync flow to retrieve hard deleted records
        elif api_type == "query":
            query_params = self.get_query_api_params(
                client_id=client_id,
                object_id=object_id,
                config_object=config_object,
                primary_kd=primary_kd,
                start_time=delete_start_time,
                params=params,
                integration_id=integration_id,
            )
            (
                deleted_records,
                _unused_total_records_fetched,
            ) = self.get_records_from_query_api(
                client_id=client_id,
                object_id=object_id,
                config_object=config_object,
                is_validation=is_validation,
                query_params=query_params,
                e2e_sync_run_id=e2e_sync_run_id,
                sync_run_id=sync_run_id,
                upstream_etl_version=upstream_etl_version,
                mode="secondary_delete",
                adc_params=params,
                integration_id=integration_id,
                snowflake_uploader=snowflake_uploader,
            )
            deleted_ids = [
                {"Id": rec.get(params.get("fields").split(",")[0]).lower()}
                for rec in deleted_records
            ]

        return {
            "deleted_records": deleted_ids,
            "updated_delete_start_time": delete_start_time,
        }

    def get_changed_records(
        self,
        client_id: int,
        e2e_sync_run_id: UUID,
        sync_run_id: UUID,
        object_id: str,
        primary_kd: datetime,
        changes_start_time: datetime,
        sync_mode: str,
        config_object: ApiAccessConfig,
        _unused_snapshot_key: str,
        _unused_destination_object_type: str,
        integration_id: UUID,
        is_validation: bool,
        upstream_etl_version: str = UpstreamETLVersions.V1.value,
        source_primary_keys: Optional[List[str]] = None,
        snowflake_uploader: Optional[UpstreamChangesWriter] = None,
    ) -> tuple[list[dict], list[str]]:
        """
        Returns a list of inserted and updated records from the /query API.
        """
        if source_primary_keys is None:
            source_primary_keys = []
        old_log_context = get_threadlocal_context()
        new_log_context = {
            "client_id": client_id,
            "object_id": object_id,
            "changes_start_time": changes_start_time,
            "e2e_sync_run_id": e2e_sync_run_id,
            "sync_run_id": sync_run_id,
        }

        merged_log_context = merge_log_context(old_log_context, new_log_context)

        set_threadlocal_context(merged_log_context)

        integration_record = IntegrationAccessor(
            client_id=client_id
        ).get_object_by_integration_id(integration_id=integration_id)
        additional_data = integration_record.additional_data  # type: ignore
        run_snowflake_sync = (additional_data or {}).get("run_snowflake_sync", False)

        if not snowflake_uploader and run_snowflake_sync:
            snowflake_uploader = UpstreamChangesWriter(
                client_id=client_id,
                integration_id=integration_id,
                is_validation=is_validation,
            )
            snowflake_uploader.set_meta(is_deleted=False, updated_on=primary_kd)

        query_params = self.get_query_api_params(
            client_id=client_id,
            object_id=object_id,
            config_object=config_object,
            primary_kd=primary_kd,
            start_time=changes_start_time,
            integration_id=integration_id,
            is_validation=is_validation,
        )
        (
            changed_records,
            _unused_total_records_fetched,
        ) = self.get_records_from_query_api(
            client_id=client_id,
            object_id=object_id,
            config_object=config_object,
            query_params=query_params,
            is_validation=is_validation,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
            upstream_etl_version=upstream_etl_version,
            source_primary_keys=source_primary_keys,
            mode="change",
            integration_id=integration_id,
            snowflake_uploader=snowflake_uploader,
        )

        return changed_records, self.source_deleted_fields

    def save_access_token_credentials(
        self,
        client_id: int,
        credentials: Credentials,
        domain: str,
        connection_name: str,
    ) -> None:
        """
        Description:
            Utility function to save access token credentials.
        Parameters:
            - client_id (required): int
            - credentials (required): dict (Shape provided by `Credentials`)
            - domain (required): str
        Returns:
            Instance of AccessTokenConfig object.
        Exceptions:
            - MissingFieldException: Raised when any of the credential's fields are missing.
        """

        # Check if fields are present in credentials. Raise exception if missing.
        if not credentials.get("client_id"):
            raise MissingFieldException(
                "client_id", "Client ID is missing from credentials."
            )
        if not credentials.get("client_secret"):
            raise MissingFieldException(
                "client_secret", "Client Secret is missing from credentials."
            )
        if not credentials.get("username"):
            raise MissingFieldException(
                "username", "Username is missing from credentials."
            )
        if not credentials.get("password"):
            raise MissingFieldException(
                "password", "Password is missing from credentials."
            )

        # Attach credentials to the request body and set the Grant type as Password.
        request_body = {}

        request_body["grant_type"] = "password"
        request_body.update(credentials)

        # Attempt to make request to the API endpoint for fetching access token.
        res = request(
            method="POST",
            url="https://login.salesforce.com/services/oauth2/token",
            data=request_body,
        )

        if res.status_code == 200:
            created_record = AccessTokenConfigAccessor(
                client_id=client_id
            ).create_record(
                service_name="salesforce",
                access_type="UN-PWD",
                payload_type="params",
                access_token_url="https://login.salesforce.com/services/oauth2/token",
                access_request_body=request_body,
                domain=domain,
                connection_name=connection_name,
            )

            return created_record
        else:
            raise Exception("Invalid credentials")

    def get_all_sobjects(
        self, client_id: int, access_token_config_id: int
    ) -> list[SObjectMeta]:
        """
        Description:
            Utility function to get a list of sobjects.
        Parameters:
            - client_id (required): int.
            - access_token_config_id (required): int.
        Returns:
            - List of sobjects (Shape provided by `SObjectMeta`).
        Exceptions:
            None.
        """

        access_token_config_record: AccessTokenConfig = AccessTokenConfigAccessor(
            client_id=client_id
        ).get_object_by_id(access_token_config_id)

        if access_token_config_record is None:
            raise RecordNotFoundException()

        access_token_res = request(
            method="POST",
            url=access_token_config_record.access_token_url,
            data=access_token_config_record.access_request_body,
        )

        if access_token_res.status_code != 200:
            raise Exception("Invalid credentials")

        access_token = access_token_res.json()["access_token"]
        version = (access_token_config_record.additional_data or {}).get(
            "version", "v54.0"
        )

        sobjects_res = request(
            method="GET",
            url=access_token_config_record.domain
            + f"/services/data/{version}/sobjects/",
            headers={"Authorization": "Bearer " + access_token},
            timeout=120,
        )

        if sobjects_res.status_code == 401:
            raise Exception("Invalid connection")

        elif sobjects_res.status_code != 200:
            raise Exception("Something went wrong.")

        sobjects = []

        for obj in sobjects_res.json()["sobjects"]:
            sobj = {}

            sobj["name"] = obj.get("name")
            sobj["label"] = obj.get("label")

            sobjects.append(sobj)

        return sobjects

    def get_sobject_details(
        self, client_id: int, access_token_config_id: int, sobject: str
    ) -> dict:
        """
        Description:
            Utility function to get details of a sobject.
        Parameters:
            - client_id (required): int.
            - access_token_config_id (required): int.

        Returns:
            - Array of Sobjects (Shape provided by `SObjectMeta`).
        Exceptions:
            None.
        """
        access_token_config_record: AccessTokenConfig = AccessTokenConfigAccessor(
            client_id=client_id
        ).get_object_by_id(access_token_config_id)

        if access_token_config_record is None:
            raise RecordNotFoundException()

        access_token_res = request(
            method="POST",
            url=access_token_config_record.access_token_url,
            data=access_token_config_record.access_request_body,
        )

        if access_token_res.status_code != 200:
            raise Exception("Invalid credentials")

        access_token = access_token_res.json()["access_token"]
        version = (access_token_config_record.additional_data or {}).get(
            "version", "v54.0"
        )

        sobject_details_res = request(
            method="GET",
            url=access_token_config_record.domain
            + f"/services/data/{version}/sobjects/"
            + sobject
            + "/describe",
            headers={"Authorization": "Bearer " + access_token},
            timeout=120,
        )

        if sobject_details_res.status_code == status.HTTP_401_UNAUTHORIZED:
            raise Exception("Invalid connection")

        if sobject_details_res.status_code != status.HTTP_200_OK:
            raise Exception("Something went wrong.")

        sobject_details = {}
        sobject_details["fields"] = []

        for field in sobject_details_res.json().get("fields"):
            sobject_details_field = {}

            sobject_details_field["name"] = field["name"]
            sobject_details_field["type"] = field["type"]
            sobject_details_field["unique"] = field["unique"]

            sobject_details["fields"].append(sobject_details_field)

        return sobject_details
