import logging
from operator import itemgetter
from typing import Optional
from uuid import UUID, uuid4

from django.db import transaction

from commission_engine.accessors.custom_object_accessor import (
    CustomObjectAccessor,
    CustomObjectVariableAccessor,
)
from commission_engine.accessors.etl_config_accessor import (
    ApiAccessConfigAccessor,
    IntegrationAccessor,
    TransformationConfigAccessor,
)
from commission_engine.custom_types.self_service_integration_types import (
    FieldMappingType,
    SourceObjectFieldType,
    SourceObjectType,
)
from commission_engine.self_service_integrations.api_clients.salesforce_api_client import (
    SalesforceApiClient,
)
from commission_engine.services.etl_config_service import invalidate_connection_records
from commission_engine.services.etl_config_validation_service import (
    etl_config_validation,
)
from commission_engine.utils.general_data import (
    SALESFORCE_HARD_DELETE_OBJECTS,
    SUPPORTED_SALESFORCE_FUNCTIONS,
    Services,
)

from .abstract_self_service_integration import AbstractSelfServiceIntegration

logger = logging.getLogger(__name__)


class SalesforceSelfServiceIntegration(AbstractSelfServiceIntegration):
    api_client: SalesforceApiClient

    def __init__(self, client_id: int, access_token_config_id: int) -> None:
        super().__init__(
            client_id=client_id,
            service=Services.SALESFORCE,
            access_token_config_id=access_token_config_id,
        )

        self.api_client = self.__get_api_client(access_token_config_id)

    def __get_api_client(self, access_token_config_id) -> SalesforceApiClient:
        client_id, client_secret = itemgetter("client_id", "client_secret")(
            self.get_credentials()
        )

        domain = self._get_domain()

        salesforce_env = (self.access_token_config.additional_data or {}).get(
            "salesforce_env", "production"
        )

        return SalesforceApiClient(
            client_id=client_id,
            client_secret=client_secret,
            access_request_body=self.get_credentials(),
            domain=domain,
            access_token_config_id=access_token_config_id,
            salesforce_env=salesforce_env,
        )

    def _update_field_mappings(
        self, field_mappings: list[FieldMappingType], source_field_types: dict
    ) -> list[FieldMappingType]:
        """
        Update the field mappings by applying functions and converting source fields.

        Args:
            field_mappings (list[FieldMappingType]): A list of field mappings to be updated.
            source_field_types (dict): A dictionary containing the source field names and their types.

        Returns:
            list[FieldMappingType]: The updated field mappings.
        """
        for field_mapping in field_mappings:
            if field_mapping.get("applied_function"):
                applied_function = field_mapping["applied_function"]
                orig_source_field = field_mapping["source_field"]
                new_source_field = f"{orig_source_field}_converted"
                field_mapping["additional_config"] = {
                    "api_field": f"{applied_function}({orig_source_field}) {new_source_field}",
                    "function_meta": {
                        "field_name": f"{orig_source_field}",
                        "function_name": f"{applied_function}",
                    },
                }
                field_mapping["source_field"] = new_source_field
            elif source_field_types[field_mapping["source_field"]] == "datetime":
                field_mapping["additional_config"] = {
                    "date_format": ["YYYY-MM-DDTHH24:MI:SS.FF3TZHTZM"]
                }

        return field_mappings

    def _create_integration_returning_id(
        self,
        object_id: str,
        destination_object_id: int,
    ) -> UUID:
        integration_record_data = {
            "name": object_id.capitalize(),
            "desc": f"Self-serviced integration for {self.service.value.capitalize()} - {object_id.capitalize()}",
            "service_name": self.service.value,
            "source_object_id": object_id.lower(),
            "is_api": True,
            "destination_object_id": destination_object_id,
            "additional_data": {
                "run_snowflake_sync": True,
                "changes_sync_field": self._extract_time_field(object_id),
                "delete_sync_field": "isDeleted",
            },
        }

        integration_id = IntegrationAccessor(
            client_id=self.client_id
        ).create_and_persist_record(fields=integration_record_data)

        return integration_id

    def add_or_invalidate_hard_delete_config(
        self, object_id: str, integration_id: UUID, include_hard_delete_config: bool
    ) -> None:
        with transaction.atomic():
            existing_record = ApiAccessConfigAccessor(
                client_id=self.client_id
            ).get_obj_by_integration_id_and_source_id(
                integration_id=integration_id,
                source_id=object_id.lower() + "_hard_delete",
            )
            if include_hard_delete_config:
                if existing_record:
                    logger.info("Hard delete config already exists. Skipping creation.")
                else:
                    e2e_sync_run_id = uuid4()
                    sync_run_id = uuid4()
                    time_field = self._extract_time_field(object_id)
                    self.create_api_access_hard_delete_config(
                        object_id=object_id,
                        integration_id=integration_id,
                        time_field=time_field,
                    )
                    etl_config_validation(
                        client_id=self.client_id,
                        integration_id=integration_id,
                        e2e_sync_run_id=e2e_sync_run_id,
                        sync_run_id=sync_run_id,
                    )
                    logger.info(
                        f"Hard delete config created for integration id - {integration_id}"
                    )
            else:
                if not existing_record:
                    logger.info(
                        "Hard delete config does not exist. Skipping invalidation."
                    )
                else:
                    ApiAccessConfigAccessor(
                        client_id=self.client_id
                    ).invalidate_record_by_integration_id_and_source_id(
                        integration_id=integration_id,
                        source_id=object_id.lower() + "_hard_delete",
                    )
                    logger.info(
                        f"Hard delete config invalidated for integration id - {integration_id}"
                    )

    def get_mapped_and_unmapped_fields_for_integration(
        self, object_id: str, integration_id: UUID
    ):
        records = TransformationConfigAccessor(
            client_id=self.client_id
        ).get_records_by_integration_id(integration_id=integration_id)
        custom_object_id = int(records.first().destination_object_id)  # type: ignore

        mapped_field_names: set[str] = set(
            records.values_list("source_field", flat=True)
        )
        destination_fields = list(records.values_list("destination_field", flat=True))
        source_to_destination_field_map = {
            record.source_field: record.destination_field for record in records
        }
        system_name_to_data_type_map = dict(
            CustomObjectVariableAccessor(
                self.client_id
            ).get_data_types_for_system_names(custom_object_id, destination_fields)
        )
        all_fields = self.get_all_fields_in_object(object_id=object_id)

        unmapped_fields: list[SourceObjectFieldType] = []
        mapped_fields: list[SourceObjectFieldType] = []

        for field in all_fields:
            field_name = field["name"]

            if field_name in mapped_field_names:
                mapped_fields.append(field)
            else:
                unmapped_fields.append(field)

        # Create source_field and additional_config mapping
        source_field_to_additional_config_map = {}
        for record in records:
            additional_config = record.additional_config or {}
            source_field_to_additional_config_map[record.source_field] = (
                additional_config
            )

        # handle aliased names & deleted fields
        for source_field in mapped_field_names:
            # Check if this field is already in mapped_fields
            if not any(field["name"] == source_field for field in mapped_fields):
                # Handle aliased names (fields ending with _converted)
                if source_field.endswith("_converted"):
                    additional_config = source_field_to_additional_config_map.get(
                        source_field, {}
                    )
                    function_meta = additional_config.get("function_meta", {})
                    function_name = function_meta.get("function_name") or ""
                    original_field = function_meta.get("field_name")

                    if function_name.lower() not in SUPPORTED_SALESFORCE_FUNCTIONS:
                        continue

                    field_record = next(
                        (
                            field
                            for field in all_fields
                            if field["name"] == original_field
                        ),
                        None,
                    )

                    if not field_record:
                        mapped_fields.append(
                            {
                                "label": source_field,
                                "name": source_field,
                                "type": "currency",
                                "is_association": False,
                                "function_name": function_name,
                            }
                        )
                        continue

                    field_record_copy = field_record.copy()
                    field_record_copy["name"] = source_field
                    field_record_copy["function_name"] = function_name
                    mapped_fields.append(field_record_copy)

                else:
                    # Add deleted field to mapped_fields
                    mapped_fields.append(
                        {
                            "label": source_field,
                            "name": source_field,
                            "type": system_name_to_data_type_map[
                                source_to_destination_field_map[source_field]
                            ],
                            "is_association": False,
                            "function_name": "",
                        }
                    )

        return {
            "mapped_fields": mapped_fields,
            "unmapped_fields": unmapped_fields,
        }

    def create(
        self,
        object_id: str,
        custom_object_id: int,
        field_mappings: list[FieldMappingType],
        sync_type: str,
        **kwargs,
    ) -> None:
        # Extract the time field
        time_field = self._extract_time_field(object_id)

        include_hard_delete_config = kwargs.get("include_hard_delete_config", False)

        if not time_field:
            raise ValueError("No time field found for object")

        ## Update Field Mappings
        source_field_types = {
            item["name"]: item["type"]
            for item in self.get_all_fields_in_object(object_id)
        }
        field_mappings = self._update_field_mappings(field_mappings, source_field_types)

        integration_id = self._create_integration_returning_id(
            object_id=object_id,
            destination_object_id=custom_object_id,
        )

        self._create_extraction(
            integration_id=integration_id,
            object_id=object_id,
            custom_object_id=custom_object_id,
            sync_type=sync_type,
        )

        self._create_transformations(
            object_id=object_id,
            custom_object_id=custom_object_id,
            integration_id=integration_id,
            mappings=field_mappings,
        )

        self.create_api_access_config(
            object_id=object_id, integration_id=integration_id, time_field=time_field
        )

        self.create_api_access_delete_config(
            object_id=object_id, integration_id=integration_id, time_field=time_field
        )

        if (
            object_id.lower() in SALESFORCE_HARD_DELETE_OBJECTS
            and include_hard_delete_config
        ):
            self.create_api_access_hard_delete_config(
                object_id=object_id,
                integration_id=integration_id,
                time_field=time_field,
            )

        e2e_sync_run_id = uuid4()
        sync_run_id = uuid4()

        self._create_initial_sync_line(
            object_id=object_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
            integration_id=integration_id,
            sync_type=sync_type,
        )

        etl_config_validation(
            client_id=self.client_id,
            integration_id=integration_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
        )

    def _extract_time_field(self, object_id: str) -> Optional[str]:
        """
        Extracts the time field name from the object.

        Args:
            object_id (str): The ID of the object.

        Returns:
            Optional[str]: The name of the time field if found, else None.
        """
        fields = self.get_all_fields_in_object(object_id)
        all_names = {item["name"] for item in fields}
        time_field = None

        if "SystemModstamp" in all_names:
            time_field = "SystemModstamp"
        elif "LastModifiedDate" in all_names:
            time_field = "LastModifiedDate"
        elif "CreatedDate" in all_names:
            time_field = "CreatedDate"
        elif "LoginTime" in all_names:
            time_field = "LoginTime"

        return time_field

    def create_api_access_config(self, object_id: str, integration_id: UUID, **kwargs):
        time_field = kwargs.get("time_field")
        fields = {
            "source_object_id": object_id.lower(),
            "request_url": f"{self._get_domain()}/services/data/v58.0/query",
            "request_type": "get",
            "request_body": {
                "q": f"select {{fields}} from {{sobject}} where {time_field} >= {{start}} and {time_field} <= {{end}} {{offset_condition}} order by {{order_by_field}} limit {{limit_value}}"
            },
            "request_header": {
                "Content-type": "application/json",
                "Authorization": "Bearer {access_token}",
            },
            "access_token_config_id": self.access_token_config.access_token_config_id,
            "integration": self.service.value,
            "response_key": "records",
            "additional_data": {
                "api_type": "query",
                "instance_url": self._get_domain(),
                "limit_value": 2000,
                "order_by_field": "Id",
            },
            "integration_id": integration_id,
        }

        ApiAccessConfigAccessor(client_id=self.client_id).create_and_persist_record(
            fields=fields
        )

    def create_api_access_delete_config(
        self, object_id: str, integration_id: UUID, **kwargs
    ):
        time_field = kwargs.get("time_field")
        url = f"{self._get_domain()}/services/data/v58.0/queryAll"
        body = {
            "q": f"select {{fields}} from {{sobject}} where isDeleted = true and {time_field} >= {{start}} and {time_field} <= {{end}} {{offset_condition}} order by {{order_by_field}} limit {{limit_value}}"
        }
        additional_data = {
            "api_type": "queryAll",
            "instance_url": self._get_domain(),
            "limit_value": 2000,
            "order_by_field": "Id",
        }

        supported = self.api_client.is_queryall_api_supported(object_id=object_id)

        if not supported:
            sobject_support = self.api_client.is_sobjects_deleted_api_supported(
                object_id=object_id
            )
            if not sobject_support:
                return
            url = f"{self._get_domain()}/services/data/v58.0/sobjects/{object_id.lower()}/deleted"
            body = {"end": "{end}", "start": "{start}"}
            additional_data = {
                "api_type": "sObjects",
                "instance_url": self._get_domain(),
            }

        fields = {
            "source_object_id": f"{object_id.lower()}_delete",
            "request_url": url,
            "request_type": "get",
            "request_body": body,
            "request_header": {
                "Content-type": "application/json",
                "Authorization": "Bearer {access_token}",
            },
            "access_token_config_id": self.access_token_config.access_token_config_id,
            "integration": self.service.value,
            "response_key": "records",
            "additional_data": additional_data,
            "integration_id": integration_id,
        }

        ApiAccessConfigAccessor(client_id=self.client_id).create_and_persist_record(
            fields=fields
        )

    def create_api_access_hard_delete_config(
        self, object_id: str, integration_id: UUID, **kwargs
    ):
        time_field = kwargs.get("time_field")
        fields = {
            "source_object_id": f"{object_id.lower()}_hard_delete",
            "request_url": f"{self._get_domain()}/services/data/v58.0/query",
            "request_type": "get",
            "request_body": {
                "q": f"select {{fields}} from {{sobject}} where {time_field} >= {{start}} and {time_field} <= {{end}} {{offset_condition}} order by {{order_by_field}} limit {{limit_value}}"
            },
            "request_header": {
                "Content-type": "application/json",
                "Authorization": "Bearer {access_token}",
            },
            "access_token_config_id": self.access_token_config.access_token_config_id,
            "integration": self.service.value,
            "response_key": "records",
            "additional_data": {
                "api_type": "query",
                "instance_url": self._get_domain(),
                "limit_value": 2000,
                "order_by_field": "Id",
            },
            "integration_id": integration_id,
        }

        ApiAccessConfigAccessor(client_id=self.client_id).create_and_persist_record(
            fields=fields
        )

    def get_all_objects(self) -> list[SourceObjectType]:
        return self.api_client.get_all_objects()

    def get_all_fields_in_object(self, object_id: str) -> list[SourceObjectFieldType]:
        return self.api_client.get_all_fields_in_object(
            object_id=object_id,
        )

    def get_object_meta_info(self, object_id: str) -> SourceObjectType:
        return self.api_client.get_object_meta_info(object_id=object_id)

    def clear_cached_fields_for_object(self, object_id: str):
        self.api_client.clear_cached_fields_for_object(object_id=object_id)

    def delete(self):
        invalidate_connection_records(
            client_id=self.client_id,
            access_token_config_id=self.access_token_config.access_token_config_id,
        )

    def validate(self, **kwargs):
        object_id = kwargs.get("object_id")
        custom_object_id = kwargs.get("custom_object_id")
        field_mappings = kwargs.get("field_mappings")

        # Validation for objects that do not have IsDeleted field
        fields_in_object = self.api_client.get_all_fields_in_object(object_id=object_id)
        flag = any(
            field.get("name").lower() == "isdeleted" for field in fields_in_object
        )
        if flag is False:
            primary_keys = (
                CustomObjectAccessor(self.client_id)
                .get_primary_keys(custom_object_id)
                .get("primary_key")
            ) or []
            if len(primary_keys) == 0:
                raise Exception("Primary key is not set for the object")
            elif len(primary_keys) > 1:
                raise Exception(
                    "Composite primary key is not supported for this object"
                )
            else:
                dest_pk = primary_keys[0]
                source_pk = next(
                    (
                        fm["source_field"]
                        for fm in field_mappings or []
                        if fm["destination_field"] == dest_pk
                    ),
                    None,
                )
                if source_pk is None or source_pk.lower() != "id":
                    raise Exception(
                        "Only Id field can be set as primary key for this object"
                    )
