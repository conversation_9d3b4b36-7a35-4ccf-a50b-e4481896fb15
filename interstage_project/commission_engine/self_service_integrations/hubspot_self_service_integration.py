from operator import itemgetter
from uuid import UUID, uuid4

from commission_engine.accessors.custom_object_accessor import (
    CustomObjectVariableAccessor,
)
from commission_engine.accessors.etl_config_accessor import (
    ApiAccessConfigAccessor,
    EnrichmentConfigAccessor,
    IntegrationAccessor,
    TransformationConfigAccessor,
)
from commission_engine.custom_types.self_service_integration_types import (
    AssociatedObjectsType,
    FieldMappingType,
    SourceObjectFieldType,
    SourceObjectType,
)
from commission_engine.self_service_integrations.api_clients.hubspot_api_client import (
    HubspotApiClient,
)
from commission_engine.services.etl_config_service import invalidate_connection_records
from commission_engine.services.etl_config_validation_service import (
    etl_config_validation,
)
from commission_engine.utils.general_data import Services

from .abstract_self_service_integration import AbstractSelfServiceIntegration


class HubspotServiceIntegration(AbstractSelfServiceIntegration):
    api_client: HubspotApiClient

    def __init__(self, client_id: int, access_token_config_id: int) -> None:
        super().__init__(
            client_id=client_id,
            service=Services.HUBSPOT,
            access_token_config_id=access_token_config_id,
        )

        self.api_client = self.__get_api_client(access_token_config_id)

    def __get_api_client(self, access_token_config_id) -> HubspotApiClient:
        api_access_key = itemgetter("api_access_key")(self.get_credentials())
        domain = self._get_domain()

        return HubspotApiClient(
            api_access_key=api_access_key,
            domain=domain,
            access_token_config_id=access_token_config_id,
        )

    def _update_field_mappings(
        self, field_mappings: list[FieldMappingType]
    ) -> list[FieldMappingType]:
        """
        Update the field mappings by setting the associated_object_id based on the is_association flag.

        Args:
            field_mappings (list[FieldMappingType]): A list of field mappings to be updated.

        Returns:
            list[FieldMappingType]: The updated field mappings.
        """
        for field_mapping in field_mappings:
            if field_mapping.get("is_association"):
                field_mapping["additional_config"] = {
                    "associated_object_id": field_mapping.get("associated_object_id"),
                }

        return field_mappings

    def _create_integration_returning_id(
        self,
        object_id: str,
        destination_object_id: int,
        pipeline_type=None,
    ) -> UUID:
        integration_record_data = {
            "name": object_id.capitalize(),
            "desc": f"Self-serviced integration for {self.service.value.capitalize()} - {object_id.capitalize()}",
            "service_name": self.service.value,
            "source_object_id": object_id.lower(),
            "is_api": True,
            "destination_object_id": destination_object_id,
            "additional_data": {
                "run_snowflake_sync": True,
                "changes_sync_field": "hs_lastmodifieddate",
                "delete_sync_field": None,
            },
        }
        if object_id.startswith(("pipelines", "pipeline_stages")):
            integration_record_data["additional_data"]["pipeline_type"] = pipeline_type

        integration_id = IntegrationAccessor(
            client_id=self.client_id
        ).create_and_persist_record(fields=integration_record_data)

        return integration_id

    def get_associated_fields_for_integration(
        self, integration_id: UUID
    ) -> list[SourceObjectFieldType]:
        """
        This method is used to get the associated fields for a given integration.
        The associations for an object have a corresponding record in the transformation config table.
        The source_field is the system name and the label is extracted from the source_field.
        """
        records = TransformationConfigAccessor(
            client_id=self.client_id
        ).get_records_by_integration_id(integration_id=integration_id)

        associated_fields: list[SourceObjectFieldType] = []

        for record in records:
            if record.additional_config is not None:
                # Example:
                # source_field: 'company_object.associations.companies.results.0.id'
                # label: 'Company ID'
                label = (
                    record.source_field.split(".")[0]
                    .replace("_", " ")
                    .replace("object", "")
                    .capitalize()
                    + " ID"
                )
                name = record.source_field
                field = SourceObjectFieldType(
                    label=label,
                    name=name,
                    type="number",
                    is_association=True,
                    is_function=False,
                )
                associated_fields.append(field)

        return associated_fields

    def get_associated_objects_for_integration(
        self, integration_id: UUID
    ) -> list[AssociatedObjectsType]:
        records = TransformationConfigAccessor(
            client_id=self.client_id
        ).get_records_by_integration_id(integration_id=integration_id)

        associated_objects: list[AssociatedObjectsType] = []

        for record in records:
            if record.additional_config is not None:
                source_field = record.source_field
                object_id = record.additional_config["associated_object_id"]
                field = AssociatedObjectsType(
                    source_field=source_field, object_id=object_id
                )
                associated_objects.append(field)

        return associated_objects

    def get_mapped_and_unmapped_fields_for_integration(
        self, object_id: str, integration_id: UUID
    ):
        records = TransformationConfigAccessor(
            client_id=self.client_id
        ).get_records_by_integration_id(integration_id=integration_id)
        custom_object_id = int(records.first().destination_object_id)  # type: ignore
        destination_fields = list(records.values_list("destination_field", flat=True))
        source_to_destination_field_map = {
            record.source_field: record.destination_field for record in records
        }
        system_name_to_data_type_map = dict(
            CustomObjectVariableAccessor(
                self.client_id
            ).get_data_types_for_system_names(custom_object_id, destination_fields)
        )

        mapped_field_names: list[str] = list(
            records.values_list("source_field", flat=True)
        )
        all_fields = self.get_all_fields_in_object(object_id=object_id)

        unmapped_fields: list[SourceObjectFieldType] = []
        mapped_fields: list[SourceObjectFieldType] = []

        for field in all_fields:
            field_name = field["name"]

            if field_name in mapped_field_names:
                mapped_fields.append(field)
            else:
                unmapped_fields.append(field)

        associated_fields = self.get_associated_fields_for_integration(
            integration_id=integration_id
        )
        associated_objects = self.get_associated_objects_for_integration(
            integration_id=integration_id
        )

        # handle source deleted fields
        for source_field in mapped_field_names:
            if not any(
                field["name"] == source_field for field in mapped_fields
            ) and not any(field["name"] == source_field for field in associated_fields):
                mapped_fields.append(
                    {
                        "label": source_field,
                        "name": source_field,
                        "type": system_name_to_data_type_map[
                            source_to_destination_field_map[source_field]
                        ],
                        "is_association": False,
                        "function_name": "",
                    }
                )

        return {
            "mapped_fields": mapped_fields + associated_fields,
            "unmapped_fields": unmapped_fields,
            "associations": associated_objects,
        }

    def create(
        self,
        object_id: str,
        custom_object_id: int,
        field_mappings: list[FieldMappingType],
        sync_type: str,
        **kwargs,
    ) -> None:
        pipeline_type = kwargs.get("pipeline_type", "").lower()
        has_associations = kwargs.get("has_associations", False)
        associated_objects = kwargs.get("associated_objects", [])

        if object_id in ["pipelines", "pipeline_stages"]:
            object_id = f"{object_id.lower()}_{pipeline_type}"

        ## Update Field Mappings
        field_mappings = self._update_field_mappings(field_mappings)

        integration_id = self._create_integration_returning_id(
            object_id=object_id,
            destination_object_id=custom_object_id,
            pipeline_type=pipeline_type,
        )

        self._create_extraction(
            integration_id=integration_id,
            object_id=object_id,
            custom_object_id=custom_object_id,
            sync_type=sync_type,
        )

        self._create_transformations(
            object_id=object_id,
            custom_object_id=custom_object_id,
            integration_id=integration_id,
            mappings=field_mappings,
        )

        self.create_api_access_config(
            object_id=object_id,
            integration_id=integration_id,
            pipeline_type=pipeline_type,
        )

        self.create_api_access_delete_config(
            object_id=object_id, integration_id=integration_id
        )

        if has_associations:
            for associated_object in associated_objects:
                self.create_enrichment_config(
                    object_id=object_id,
                    integration_id=integration_id,
                    associated_object=associated_object,
                )

                self.create_api_access_associations_config(
                    object_id=object_id,
                    integration_id=integration_id,
                    associated_object=associated_object,
                )

        e2e_sync_run_id = uuid4()
        sync_run_id = uuid4()

        self._create_initial_sync_line(
            object_id=object_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
            integration_id=integration_id,
            sync_type=sync_type,
        )

        etl_config_validation(
            client_id=self.client_id,
            integration_id=integration_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
        )

    def create_list_api_access_config(
        self, object_id: str, integration_id: UUID, **kwargs
    ) -> dict:
        url: str = ""
        if object_id == "owners":
            url = f"{self._get_domain()}/crm/v3/{object_id}"
        elif object_id.startswith(("pipelines", "pipeline_stages")):
            pipeline_type = kwargs.get("pipeline_type")
            url = f"{self._get_domain()}/crm/v3/pipelines/{pipeline_type}"
        else:
            url = f"{self._get_domain()}/crm/v3/objects/{object_id}"
        fields = {
            "source_object_id": object_id,
            "request_url": url,
            "request_type": "get",
            "request_header": {
                "Content-type": "application/json",
                "Authorization": "Bearer {access_token}",
            },
            "access_token_config_id": self.access_token_config.access_token_config_id,
            "integration": self.service.value,
            "response_key": "results",
            "integration_id": integration_id,
            "additional_data": {"api_type": "list"},
        }
        return fields

    def create_search_api_access_config(
        self, object_id: str, integration_id: UUID
    ) -> dict:
        url = f"{self._get_domain()}/crm/v3/objects/{object_id}/search"
        request_body = {"last_modified_date_column": "hs_lastmodifieddate"}
        if object_id == "contacts":
            request_body = {"last_modified_date_column": "lastmodifieddate"}
        fields = {
            "source_object_id": object_id.lower(),
            "request_url": url,
            "request_type": "post",
            "request_header": {
                "Content-type": "application/json",
                "Authorization": "Bearer {access_token}",
            },
            "request_body": request_body,
            "access_token_config_id": self.access_token_config.access_token_config_id,
            "integration": self.service.value,
            "response_key": "results",
            "integration_id": integration_id,
            "additional_data": {"api_type": "search"},
        }
        return fields

    def create_api_access_config(self, object_id: str, integration_id: UUID, **kwargs):
        api_type: str = "search"
        for obj in self.api_client.object_list:
            if obj["name"] == object_id or object_id.startswith(obj["name"]):
                api_type = obj["api_type"]
                break

        fields: dict = {}
        if api_type == "list":
            fields = self.create_list_api_access_config(
                object_id=object_id,
                integration_id=integration_id,
                pipeline_type=kwargs.get("pipeline_type"),
            )
        elif api_type == "search":
            fields = self.create_search_api_access_config(
                object_id=object_id, integration_id=integration_id
            )

        ApiAccessConfigAccessor(client_id=self.client_id).create_and_persist_record(
            fields=fields
        )

    def create_api_access_delete_config(self, object_id: str, integration_id: UUID):
        # Deleted records for custom objects are not working
        # Deleted records for Engagements are not supported yet
        support_delete_config: bool = False
        for obj in self.api_client.object_list:
            if obj["name"] == object_id or object_id.startswith(obj["name"]):
                support_delete_config = obj["support_delete_config"]
                break

        if not support_delete_config:
            return
        endpoint: str = ""
        if object_id == "owners":
            endpoint = f"{self._get_domain()}/crm/v3/{object_id}"
        else:
            endpoint = f"{self._get_domain()}/crm/v3/objects/{object_id}"

        fields = {
            "source_object_id": f"{object_id.lower()}_delete",
            "request_url": endpoint,
            "request_type": "get",
            "request_header": {
                "Content-type": "application/json",
                "Authorization": "Bearer {access_token}",
            },
            "access_token_config_id": self.access_token_config.access_token_config_id,
            "integration": self.service.value,
            "response_key": "results",
            "integration_id": integration_id,
            "additional_data": {"api_type": "list"},
        }

        ApiAccessConfigAccessor(client_id=self.client_id).create_and_persist_record(
            fields=fields
        )

    def create_api_access_associations_config(
        self,
        object_id: str,
        integration_id: UUID,
        associated_object: AssociatedObjectsType,
    ):
        fields = {
            "source_object_id": f"ref_{associated_object['object_id']}",
            "request_url": f"https://api.hubapi.com/crm/v4/objects/{object_id}?associations={associated_object['object_id']}&limit=100",
            "request_type": "get",
            "request_header": {
                "Content-type": "application/json",
                "Authorization": "Bearer {access_token}",
            },
            "access_token_config_id": self.access_token_config.access_token_config_id,
            "integration": self.service.value,
            "response_key": "results",
            "integration_id": integration_id,
        }

        ApiAccessConfigAccessor(client_id=self.client_id).create_and_persist_record(
            fields=fields
        )

    def create_enrichment_config(
        self,
        object_id: str,
        integration_id: UUID,
        associated_object: AssociatedObjectsType,
    ):
        fields = {
            "source_object_id": object_id,
            "ref_object_key": "hs_object_id",
            "enrichment_resp_key": "results",
            "enrichment_id_key": "id",
            "source_new_key": associated_object["source_field"].split(".")[0],
            "enrichment_type": "api",
            "ref_api_config_obj": f"ref_{associated_object['object_id']}",
            "integration_id": integration_id,
            "additional_data": {"associated_object_id": associated_object["object_id"]},
        }

        EnrichmentConfigAccessor(client_id=self.client_id).create_and_persist_record(
            fields=fields
        )

    def get_all_objects(self) -> list[SourceObjectType]:
        return self.api_client.get_all_objects()

    def get_all_fields_in_object(self, object_id: str) -> list[SourceObjectFieldType]:
        return self.api_client.get_all_fields_in_object(
            object_id=object_id,
        )

    def get_object_meta_info(self, object_id: str) -> SourceObjectType:
        return self.api_client.get_object_meta_info(object_id=object_id)

    def validate_pipeline(self, object_id: str) -> dict:
        return self.api_client.validate_pipeline(object_id=object_id)

    def validate_association(self, object_id: str, associated_object_id: str) -> dict:
        return self.api_client.validate_association(
            object_id=object_id, associated_object_id=associated_object_id
        )

    def get_pipeline_type(self, integration_id: str, client_id: str) -> dict:
        return self.api_client.get_pipeline_type(
            integration_id=integration_id, client_id=client_id
        )

    def delete(self):
        invalidate_connection_records(
            client_id=self.client_id,
            access_token_config_id=self.access_token_config.access_token_config_id,
        )

    def clear_cached_fields_for_object(self, object_id: str):
        self.api_client.clear_cached_fields_for_object(object_id=object_id)

    def validate(self, **kwargs):
        pass
