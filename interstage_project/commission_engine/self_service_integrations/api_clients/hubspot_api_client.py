from operator import itemgetter
from uuid import UUID

from django.core.cache import cache
from requests import Response, request

from commission_engine.accessors.etl_config_accessor import IntegrationAccessor
from commission_engine.custom_types import SourceObjectFieldType, SourceObjectType
from everstage_ddd.self_service_integration.selectors import (
    ConnectorObjectSelector,
    ConnectorObjectVariableSelector,
)
from spm.services.custom_object_services.custom_object_service import get_all_data_types


class HubspotApiClient:
    # Client credentials
    api_access_key: str
    domain: str
    access_token_config_id: int

    def __init__(
        self,
        api_access_key: str,
        domain: str,
        access_token_config_id: int,
    ) -> None:
        self.api_access_key = api_access_key
        self.domain = domain
        self.access_token_config_id = access_token_config_id
        self._initialize_data()

    def __aggregate_variable_list(self, variables: list[dict]) -> dict[str, list[dict]]:
        """
        Aggregates a list of variables based on their object_id and formats them with additional information.

        Args:
            variables (list[dict]): A list of dictionaries representing variables. Each dictionary should contain keys for 'object_id', 'system_name', 'display_name', and 'data_type_id'.

        Returns:
            dict[str, list[dict]]: A dictionary where keys are object_ids and values are lists of formatted variables.
        """
        data_type_mapping = {var.id: var.data_type for var in get_all_data_types()}  # type: ignore
        aggregated_variables = {}
        for variable in variables:
            data_type_id = variable["data_type_id"]
            if data_type_id in data_type_mapping:
                formatted_variable = {
                    "name": variable["system_name"],
                    "type": data_type_mapping[data_type_id],
                    "label": variable["display_name"],
                }
                aggregated_variables.setdefault(variable["object_id"], []).append(
                    formatted_variable
                )
        return aggregated_variables

    def _initialize_data(self) -> None:
        """Initializes data for the HubspotApiClient instance."""
        self.object_list = (
            ConnectorObjectSelector(connector_id="hubspot").get_connector_object_list()
            or []
        )

        self.label_map = {}
        for obj in self.object_list:
            if obj and "association_map" in obj:
                self.label_map.update(obj["association_map"])

        variable_list = (
            ConnectorObjectVariableSelector(
                connector_id="hubspot"
            ).get_all_connector_object_variables_list()
            or []
        )
        variable_list_map = self.__aggregate_variable_list(variable_list)

        self.owner_field_data = variable_list_map.get("owners", [])  # type: ignore
        self.pipeline_field_data = variable_list_map.get("pipeline", [])  # type: ignore
        self.pipeline_stages_field_data = variable_list_map.get("pipeline_stages", [])  # type: ignore

    def __make_request(
        self,
        method: str,
        endpoint: str,
        params: dict[str, str] = None,
        data: dict[str, str] = None,
    ) -> Response:
        if params is None:
            params = {}
        if data is None:
            data = {}
        res = request(
            method=method,
            url=f"{self.domain}{endpoint}",
            params=params,
            data=data,
            headers={"Authorization": f"Bearer {self.api_access_key}"},
            timeout=30,
        )

        return res

    def __get_access_token_info(self) -> Response:
        res = request(
            method="post",
            url=f"{self.domain}/oauth/v2/private-apps/get/access-token-info",
            json={
                "tokenKey": self.api_access_key,
            },
        )

        return res

    def get_std_objects(self) -> list[SourceObjectType]:
        res = self.__get_access_token_info()
        scopes_list = res.json()["scopes"] if res.status_code == 200 else []

        objects: list[SourceObjectType] = []

        for s_object in self.object_list:
            flag = True

            for scope in s_object["scopes"]:
                if scope not in scopes_list:
                    flag = False
                    break

            if flag:
                label, name = itemgetter("label", "name")(s_object)

                object_record: SourceObjectType = {"label": label, "name": name}
                objects.append(object_record)

        return objects

    def get_custom_objects(self) -> list[SourceObjectType]:
        res = self.__make_request(
            method="get",
            endpoint="/crm/v3/schemas",
        )

        objects: list[SourceObjectType] = []

        if "results" not in res.json():
            return objects

        for s_object in res.json()["results"]:
            name = itemgetter("fullyQualifiedName")(s_object)
            label = itemgetter("plural")(itemgetter("labels")(s_object))

            object_record: SourceObjectType = {
                "label": label.strip(),
                "name": name.strip(),
            }
            objects.append(object_record)

        return objects

    def get_all_objects(self) -> list[SourceObjectType]:
        std_objects = self.get_std_objects()
        custom_objects = self.get_custom_objects()

        return std_objects + custom_objects

    def get_field_data(self, object_id: str) -> list[dict]:
        field_data = []

        if object_id == "owners":
            field_data = self.owner_field_data
        elif object_id.startswith("pipelines"):
            field_data = self.pipeline_field_data
        elif object_id.startswith("pipeline_stages"):
            field_data = self.pipeline_stages_field_data
        else:
            object_cache_key = f"hubspot_{self.access_token_config_id}_{self.api_access_key}_{object_id.lower()}_properties"

            if not cache.get(object_cache_key):
                res = self.__make_request(
                    method="get",
                    endpoint=f"/crm/v3/properties/{object_id}",
                )
                cache.set(object_cache_key, res, 86400)
            else:
                res = cache.get(object_cache_key)

            if "results" not in res.json():
                return []

            field_data = res.json()["results"]

        return field_data

    def get_all_fields_in_object(self, object_id: str) -> list[SourceObjectFieldType]:
        fields: list[SourceObjectFieldType] = []
        field_data = self.get_field_data(object_id)

        for field in field_data:
            label, name, field_type = itemgetter("label", "name", "type")(field)

            field_record: SourceObjectFieldType = {
                "label": label.strip(),
                "name": name.strip(),
                "type": field_type,
                "is_association": False,
                "is_function": False,
            }

            fields.append(field_record)

        return fields

    def get_object_meta_info(self, object_id: str) -> SourceObjectType:
        object_mf: SourceObjectType = {"label": "", "name": ""}

        for object_s in self.object_list:
            if object_s["name"] == object_id:
                object_mf["label"], object_mf["name"] = (
                    object_s["label"],
                    object_s["name"],
                )
                return object_mf

        object_cache_key = f"hubspot_{self.access_token_config_id}_{self.api_access_key}_{object_id.lower()}_schemas"

        if not cache.get(object_cache_key):
            res = self.__make_request(
                method="get",
                endpoint=f"/crm/v3/schemas/{object_id}",
            )
            cache.set(object_cache_key, res, 86400)
        else:
            res = cache.get(object_cache_key)

        if "labels" not in res.json():
            return {"label": object_id.capitalize(), "name": object_id.lower()}

        object_mf["label"] = res.json()["labels"]["plural"].strip()
        object_mf["name"] = res.json()["fullyQualifiedName"].lower()

        return object_mf

    def get_singular_name(self, object_id: str) -> str:
        object_cache_key = f"hubspot_{self.access_token_config_id}_{self.api_access_key}_{object_id.lower()}_schemas"

        if not cache.get(object_cache_key):
            res = self.__make_request(
                method="get",
                endpoint=f"/crm/v3/schemas/{object_id}",
            )
            cache.set(object_cache_key, res, 86400)
        else:
            res = cache.get(object_cache_key)

        if "labels" not in res.json():
            return object_id.capitalize()

        return res.json()["labels"]["singular"].strip()

    def validate_pipeline(self, object_id: str) -> dict:
        res = self.__make_request(
            method="get",
            endpoint=f"/crm/v3/pipelines/{object_id}",
        )
        if res.status_code == 200:
            return {"message": "Success"}
        elif res.status_code == 400:
            return {"message": res.json()["message"]}
        elif res.status_code == 403:
            return {"message": "Required scopes not granted for this object"}
        else:
            return {"message": "Unknown error"}

    def validate_association(self, object_id: str, associated_object_id: str) -> dict:
        res = self.__make_request(
            method="get",
            endpoint=f"/crm/v4/associations/{object_id}/{associated_object_id}/labels",
        )
        if res.status_code == 200:
            if len(res.json()["results"]) == 0:
                return {"message": "No associations found"}
            else:
                associated_object_id = associated_object_id.lower()
                associated_object_id = self.label_map.get(
                    associated_object_id, associated_object_id
                )
                obj_name = self.get_singular_name(associated_object_id)

                return {
                    "message": "Success",
                    "data": {
                        "name": obj_name.replace(" ", "_").lower()
                        + f"_object.associations.{associated_object_id}.results[0].id",
                        "label": obj_name + " ID",
                        "type": "number",
                        "object_name": associated_object_id,
                    },
                }
        elif res.status_code == 400:
            return {"message": res.json()["message"]}
        else:
            return {"message": "Unknown error"}

    def get_pipeline_type(self, integration_id: str, client_id: str) -> dict:
        integration_record = list(
            IntegrationAccessor(client_id=client_id).get_record_by_integration_id(
                integration_id=UUID(integration_id)
            )
        )
        pipeline_type = integration_record[0].additional_data["pipeline_type"]
        return {"message": "Success", "pipeline_type": pipeline_type}

    def clear_cached_fields_for_object(self, object_id: str) -> None:
        object_cache_key = f"hubspot_{self.access_token_config_id}_{self.api_access_key}_{object_id.lower()}_schemas"
        print(f"Clearing cache: {object_cache_key}")
        cache.delete(object_cache_key)

        object_cache_key = f"hubspot_{self.access_token_config_id}_{self.api_access_key}_{object_id.lower()}_properties"
        print(f"Clearing cache: {object_cache_key}")
        cache.delete(object_cache_key)
