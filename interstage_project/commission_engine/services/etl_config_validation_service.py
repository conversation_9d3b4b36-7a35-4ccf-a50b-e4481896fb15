import logging
from datetime import <PERSON><PERSON><PERSON>
from uuid import UUID

from django.utils import timezone
from requests import request

from commission_engine.accessors.etl_config_accessor import (
    AccessTokenConfig,
    AccessTokenConfigAccessor,
    ExtractionConfigAccessor,
    IntegrationAccessor,
)
from commission_engine.database.snowflake_query_utils import drop_table_if_exists
from commission_engine.models.etl_config_models import ExtractionConfig
from commission_engine.utils import service_access_token_url_map
from commission_engine.utils.general_data import ETL_STATUS
from everstage_ddd.upstream import (
    IntegrationErrorHandler,
    UpstreamSnowflakeSync,
    UpstreamSyncParams,
    get_upstream_table_name,
)

logger = logging.getLogger(__name__)


class InvalidCredentialsError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(*message)


def etl_config_validation(
    client_id: int,
    integration_id: UUID,
    e2e_sync_run_id: UUID,
    sync_run_id: UUID,
):
    """
    Service to validate ETL config for given integration id.
    Validation at extraction and transformation level.

    Args:
        client_id (int): Client ID.
        integration_id (UUID): Integration ID of the ETL config.
        e2e_sync_run_id (UUID): Placeholder E2E sync run ID.
        sync_run_id (UUID): Placeholder sync run ID.

    """
    from everstage_etl.tasks.extraction import upstream_data_extractor
    from everstage_etl.tasks.transformation import upstream_transformer

    primary_kd = timezone.now()
    sync_mode = "changes"

    logger.info("BEGIN: Integration validation")

    extraction_record: ExtractionConfig = (
        ExtractionConfigAccessor(client_id=client_id).get_record_by_integration_id(integration_id=integration_id).last()  # type: ignore
    )
    integration_record = IntegrationAccessor(
        client_id=client_id
    ).get_object_by_integration_id(integration_id=integration_id)

    additional_data = integration_record.additional_data  # type: ignore
    run_snowflake_sync = (
        True
        if integration_record.service_name.lower() == "sftp"  # type: ignore
        else (additional_data or {}).get("run_snowflake_sync", False)
    )
    transformation_logic = integration_record.transformation_logic  # type: ignore

    access_token_config_record: AccessTokenConfig = AccessTokenConfigAccessor(
        client_id=client_id
    ).get_object_by_id(extraction_record.access_token_config_id)
    is_analytics = (access_token_config_record.additional_data or {}).get(
        "zoho_analytics", False
    )
    if is_analytics:
        logger.info("Skipping validation for zoho analytics object")
        logger.info("END: Integration validation")
        return
    validation_changes_time = (
        extraction_record.additional_data.get("validation_changes_time")
        if extraction_record.additional_data is not None
        else None
    )

    if validation_changes_time is None:
        changes_start_time = primary_kd - timedelta(days=7)
    else:
        changes_start_time = primary_kd

        hours = validation_changes_time.get("hours")
        days = validation_changes_time.get("days")

        if hours:
            changes_start_time -= timedelta(hours=int(hours))
        if days:
            changes_start_time -= timedelta(days=int(days))

        if not hours and not days:
            changes_start_time -= timedelta(days=7)

    logger.info(
        f"Validating integration setup against records from {changes_start_time.strftime('%Y-%m-%d %H-%M-%S')}"
    )

    try:
        extracted_data = upstream_data_extractor(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
            sync_mode=sync_mode,
            _unused_task=extraction_record.task,
            object_id=extraction_record.source_object_id,
            destination_object_id=extraction_record.destination_object_id,
            destination_object_type=extraction_record.destination_object_type,
            changes_start_time=changes_start_time,
            primary_kd=primary_kd,
            integration_id=integration_id,
            is_validation=True,
        )
    except Exception as err:
        logger.exception("Integration validation: Extraction failed")
        error_handler = IntegrationErrorHandler(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
            object_id=extraction_record.source_object_id,
            integration_id=integration_id,
        )
        parsed_error = error_handler.handle_integration_error(
            raw_exception=str(err),
            etl_step_context=ETL_STATUS.EXTRACTION.value,
        )
        error_message = (
            f"{parsed_error['title']}, {parsed_error['message']}"
            if parsed_error
            else "Extraction failed!"
        )
        logger.info("Parsed error: %s", parsed_error)
        raise Exception(error_message) from err
    try:
        if run_snowflake_sync:
            source_data_table = (
                f"VALIDATION_{get_upstream_table_name(client_id, integration_id)}"
            )
            upstream_snowflake_sync_params = UpstreamSyncParams(
                source_data_table=source_data_table,
                custom_object_id=int(extraction_record.destination_object_id),
                integration_id=integration_id,
                sync_mode=sync_mode,
                client_id=client_id,
                e2e_sync_run_id=e2e_sync_run_id,
                sync_run_id=sync_run_id,
                changes_sync_time=changes_start_time,
                is_source_data_as_variant=True,
                transformation_logic=transformation_logic,
            )

            upstream_snowflake_sync = UpstreamSnowflakeSync(
                upstream_snowflake_sync_params
            )
            try:
                upstream_snowflake_sync.transform()
                logger.info(
                    "Integration validation successfull, Dropping validation table %s",
                    source_data_table,
                )
            except Exception:
                logger.info(
                    "Transformation Failed, Dropping validation table %s",
                    source_data_table,
                )
                raise
            finally:
                drop_table_if_exists(source_data_table)
        else:
            upstream_transformer(
                client_id=client_id,
                e2e_sync_run_id=e2e_sync_run_id,
                sync_run_id=sync_run_id,
                object_id=extraction_record.source_object_id,
                destination_object_id=extraction_record.destination_object_id,
                extracted_objs=extracted_data["result"],
                is_validation=True,
            )
    except Exception as err:
        logger.exception("Integration validation: Transformation failed")
        error_handler = IntegrationErrorHandler(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
            object_id=extraction_record.source_object_id,
            integration_id=integration_id,
        )
        parsed_error = error_handler.handle_integration_error(
            raw_exception=str(err.args),
            etl_step_context=ETL_STATUS.TRANSFORMATION.value,
        )
        error_message = (
            f"{parsed_error['title']}, {parsed_error['message']}"
            if parsed_error
            else "Transformation failed!"
        )
        logger.info("Parsed error: %s", parsed_error)
        raise Exception(error_message) from err

    logger.info("END: Integration validation successfull")


def test_connection_returning_domain(service_name: str, **kwargs) -> str:
    url = service_access_token_url_map[service_name]
    domain: str = ""
    if service_name == "hubspot":
        res = request(
            method="get",
            url=url,
            headers={"Authorization": f"Bearer {kwargs.get('api_access_key')}"},
        )

        if res.status_code != 200:
            raise InvalidCredentialsError("Invalid API Key")

        domain = "https://api.hubapi.com"

    elif service_name == "salesforce":
        salesforce_env = kwargs.get("salesforce_env")
        url = url.replace("login", "test") if salesforce_env == "sandbox" else url
        res = request(
            method="post",
            url=url,
            data=kwargs.get("access_request_body"),
        )

        if res.status_code != 200:
            raise InvalidCredentialsError("Invalid credentials")

        domain = res.json()["instance_url"]
    return domain
