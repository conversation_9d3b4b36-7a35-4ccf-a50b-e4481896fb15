import uuid

from commission_engine.accessors.etl_config_accessor import (
    AccessTokenConfigAccessor,
    ExtractionConfigAccessor,
)
from commission_engine.services.etl_tasks_service import ETLSync


def run_hris_sync_for_config_id(client_id, config_id, audit, trigger=None):
    """
    This function is used to run hris sync for given access_token_config_ids.
    :param client_id: client id
    :param config_ids: config ids
    :return: None
    """
    config_records = ExtractionConfigAccessor(
        client_id=client_id
    ).get_records_by_access_token_config_ids(access_token_config_ids=[config_id])
    integration_ids = []
    if len(config_records) > 0:
        for config_record in config_records:
            integration_ids.append(str(config_record.integration_id))
        e2e_sync_run_id = uuid.uuid4()
        log_context = {"client_id": client_id, "e2e_sync_run_id": e2e_sync_run_id}
        etl_sync_service = ETLSync(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
            log_context=log_context,
            notification_email_id=None,
            skip_archived_books=True,
            post_upstream_disabled=True,
        )
        etl_sync_service.run_daily_sync_wrapper(
            all_objects_selected=False,
            integration_ids=integration_ids,
            audit=audit,
            trigger=trigger,
        )
