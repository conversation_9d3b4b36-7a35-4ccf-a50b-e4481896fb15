from django.utils import timezone

from commission_engine.accessors.databook_accessor import (
    DatasheetAccessor,
    DatasheetVariableAccessor,
)
from commission_engine.accessors.etl_config_accessor import (
    AccessTokenConfigAccessor,
    ExtractionConfigAccessor,
)
from commission_engine.accessors.etl_housekeeping_accessor import (
    UpstreamETLStatusReaderAccessor,
)
from commission_engine.accessors.hris_integration_accessor import HrisConfigAccessor
from commission_engine.serializers.hris_integration_serializers import (
    HrisConfigSerializer,
)
from spm.accessors.custom_field_accessor import CustomFieldsAccessor
from spm.constants import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.services import audit_services
from spm.services.datasheet_permission_services import (
    get_all_hidden_columns_in_a_datasheet_for_user,
)
from spm.services.rbac_services import does_user_have_databook_manage_permission

default_hris_fields = [
    {
        "display_name": "Employee Email Id",
        "user_field": "employee_email_id",
        "field_context": "employee",
        "is_required": True,
        "datatype_id": 12,
        "data_type": "Email",
    },
    {
        "display_name": "Effective Start Date",
        "user_field": "effective_start_date",
        "field_context": "employee",
        "is_required": True,
        "datatype_id": 2,
        "data_type": "Date",
    },
    {
        "display_name": "Effective End Date",
        "user_field": "effective_end_date",
        "field_context": "employee",
        "is_required": True,
        "datatype_id": 2,
        "data_type": "Date",
    },
    {
        "display_name": "First Name",
        "user_field": "first_name",
        "field_context": "employee",
        "is_required": True,
        "datatype_id": 4,
        "data_type": "String",
    },
    {
        "display_name": "Last Name",
        "user_field": "last_name",
        "field_context": "employee",
        "is_required": True,
        "datatype_id": 4,
        "data_type": "String",
    },
    {
        "display_name": "User Role",
        "user_field": "user_role",
        "field_context": "employee",
        "is_required": True,
        "datatype_id": 4,
        "data_type": "String",
    },
    {
        "display_name": "Deactivation/Exit date",
        "user_field": "deactivation_date",
        "field_context": "employee",
        "is_required": False,
        "datatype_id": 2,
        "data_type": "Date",
    },
    {
        "display_name": "Employee Id",
        "user_field": "employee_id",
        "field_context": "employee_payroll",
        "is_required": False,
        "datatype_id": 4,
        "data_type": "String",
    },
    {
        "display_name": "Joining Date",
        "user_field": "joining_date",
        "field_context": "employee_payroll",
        "is_required": True,
        "datatype_id": 2,
        "data_type": "Date",
    },
    {
        "display_name": "Designation",
        "user_field": "designation",
        "field_context": "employee_payroll",
        "is_required": False,
        "datatype_id": 4,
        "data_type": "String",
    },
    {
        "display_name": "Employment Country",
        "user_field": "employment_country",
        "field_context": "employee_payroll",
        "is_required": True,
        "datatype_id": 4,
        "data_type": "String",
    },
    {
        "display_name": "Base Pay",
        "user_field": "fixed_pay",
        "field_context": "employee_payroll",
        "is_required": False,
        "datatype_id": 1,
        "data_type": "Integer",
    },
    {
        "display_name": "Variable Pay",
        "user_field": "variable_pay",
        "field_context": "employee_payroll",
        "is_required": False,
        "datatype_id": 1,
        "data_type": "Integer",
    },
    {
        "display_name": "Pay Currency",
        "user_field": "pay_currency",
        "field_context": "employee_payroll",
        "is_required": True,
        "datatype_id": 4,
        "data_type": "String",
    },
    {
        "display_name": "Payout Frequency",
        "user_field": "payout_frequency",
        "field_context": "employee_payroll",
        "is_required": True,
        "datatype_id": 4,
        "data_type": "String",
    },
    {
        "display_name": "Crystal Access",
        "user_field": "payee_role",
        "field_context": "employee_payroll",
        "is_required": True,
        "datatype_id": 4,
        "data_type": "String",
    },
    {
        "display_name": "Manager Email",
        "user_field": "reporting_manager_email_id",
        "field_context": "hierarchy",
        "is_required": True,
        "datatype_id": 12,
        "data_type": "Email",
    },
]


def get_hris_data(client_id):
    """
    This function is used to get hris data.
    :param client_id: client id
    :return: hris data
    """
    active_hris_connections = AccessTokenConfigAccessor(
        client_id=client_id
    ).get_object_by_connection_type("hris")
    connections = []
    hris_configs = []
    config_ids = []
    connected_objects = []
    config_records = []

    for connection in active_hris_connections:
        config_ids.append(connection.access_token_config_id)
        ext_keys = {
            "connection_name": connection.connection_name,
            "service_name": connection.service_name,
            "access_token_config_id": connection.access_token_config_id,
            "is_fivetran_connection": (connection.access_request_body or {}).get(
                "middleware_name"
            )
            == "fivetran",
        }
        connections.append(ext_keys)

    if config_ids:
        config_records = ExtractionConfigAccessor(
            client_id=client_id
        ).get_records_by_access_token_config_ids(access_token_config_ids=config_ids)

    task_names = []
    for config in config_records:
        task_names.append(config.task)
        connected_objects.append(
            {
                "source_object_id": config.source_object_id,
                "integration_id": config.integration_id,
                "task_name": config.task,
                "access_token_config_id": config.access_token_config_id,
            }
        )

    is_sync_running = UpstreamETLStatusReaderAccessor(
        client_id=client_id
    ).is_upstream_sync_running_for_object(object_id=task_names)

    hris_config = HrisConfigAccessor(client_id=client_id).client_kd_aware()

    for config in hris_config:
        config_obj = {
            "datasheet_id": config.datasheet_id,
            "databook_id": config.databook_id,
            "user_field": config.user_field,
            "source_field": config.source_field,
            "field_context": config.field_context,
        }
        hris_configs.append(config_obj)
    hris_data = {
        "active_hris_connections": connections,
        "hris_config": hris_configs,
        "data_review_type": "manual-review",
        "connected_objects": connected_objects,
        "is_sync_running": is_sync_running,
    }

    return hris_data


def get_config_columns(client_id):
    """
    This function is used to get config columns.
    :param client_id: client id
    :return: config columns
    """

    section_data = [
        {
            "section_name": "employee",
            "display_name": "Employee Details",
            "required": True,
        },
        {
            "section_name": "employee_payroll",
            "display_name": "Payroll Details",
            "required": False,
        },
        {
            "section_name": "hierarchy",
            "display_name": "Reporting Hierarchy",
            "required": False,
        },
    ]
    config_fields = default_hris_fields.copy()
    custom_fields = CustomFieldsAccessor(
        client_id=client_id
    ).get_active_custom_fields_by_client(
        projection=[
            "display_name",
            "system_name",
            "is_mandatory",
            "data_type_id",
            "data_type__data_type",
        ]
    )
    if len(custom_fields) > 0:
        section_data.append(
            {
                "section_name": "custom_fields",
                "display_name": "Custom Fields",
                "required": False,
            }
        )
        for custom_field in custom_fields:
            config_fields.append(
                {
                    "display_name": custom_field["display_name"],
                    "user_field": custom_field["system_name"],
                    "field_context": "custom_fields",
                    "is_required": custom_field["is_mandatory"],
                    "datatype_id": custom_field["data_type_id"],
                    "data_type": custom_field["data_type__data_type"],
                }
            )

    return {"config_columns": config_fields, "section_data": section_data}


def validate_variables_used_in_hris(client_id, selected_vars, datasheet_id):
    result = {"is_valid": True, "errors": []}
    is_datasheet_used_in_hris = HrisConfigAccessor(
        client_id=client_id
    ).is_datasheet_used_in_hris(datasheet_id=datasheet_id)

    if not is_datasheet_used_in_hris:
        return result

    variables_used_in_hris = set(
        HrisConfigAccessor(client_id=client_id).get_mapped_fields_list()
    )
    selected_vars_in_datasheet = set(selected_vars)
    missing_vars = variables_used_in_hris.difference(selected_vars_in_datasheet)
    if len(missing_vars) > 0:
        display_names = DatasheetVariableAccessor(
            client_id
        ).get_datasheet_display_name_for_system_names(
            datasheet_id,
            list(variables_used_in_hris.difference(selected_vars_in_datasheet)),
        )

        result["is_valid"] = False
        result["errors"] = (
            f"Columns {', '.join(display_names)} are used in HRIS integration, please select to continue."
        )
        return result
    return result


def get_usersheet_variables(
    client_id, logged_in_user_email, skip_permission_validation=False
):
    hris_config = HrisConfigAccessor(client_id=client_id).client_kd_aware()
    usersheet_variables = []
    hidden_columns = []

    if not hris_config:
        return {
            "datasheet_id": None,
            "databook_id": None,
            "variables": usersheet_variables,
        }

    datasheet_id = hris_config[0].datasheet_id
    databook_id = hris_config[0].databook_id

    system_name_to_display_name_map = get_ever_fields_to_display_name_datatype_map(
        client_id
    )
    if not skip_permission_validation:
        datasheet_manage_permission = does_user_have_databook_manage_permission(
            client_id, logged_in_user_email
        )
        if not datasheet_manage_permission:
            hidden_columns = get_all_hidden_columns_in_a_datasheet_for_user(
                client_id, datasheet_id, logged_in_user_email
            )
    for config in hris_config:
        if config.source_field not in hidden_columns:
            usersheet_variables.append(
                {
                    "internal_field_name": config.user_field,
                    "display_name": system_name_to_display_name_map[config.user_field][
                        "display_name"
                    ],
                    "system_name": config.source_field,
                    "datatype_id": system_name_to_display_name_map[config.user_field][
                        "datatype_id"
                    ],
                    "data_type": system_name_to_display_name_map[config.user_field][
                        "data_type"
                    ],
                    "field_context": config.field_context,
                }
            )

    return {
        "datasheet_id": datasheet_id,
        "databook_id": databook_id,
        "variables": usersheet_variables,
    }


def save_hris_configs(client_id, user_email, config_data):
    """
    This function is used to save hris configs.
    :param client_id: client id
    :param hris_config: hris config
    :return: None
    """
    configs = []
    fields_to_invalidate = []
    current_time = timezone.now()
    datasheet_id = (
        config_data["configs_to_save"][0]["datasheet_id"]
        if len(config_data["configs_to_save"]) > 0
        else (
            config_data["configs_to_invalidate"][0]["datasheet_id"]
            if len(config_data["configs_to_invalidate"]) > 0
            else ""
        )
    )

    config_fields = get_config_columns(client_id=client_id)["config_columns"]
    datasheet_fields = DatasheetVariableAccessor(
        client_id=client_id
    ).get_ds_variables_for_datasheet(datasheet_id=datasheet_id)
    display_name_map = {
        **{field["user_field"]: field["display_name"] for field in config_fields},
        **{field["system_name"]: field["display_name"] for field in datasheet_fields},
    }
    audit_data_list = []
    field_contexts = set()

    for config in config_data["configs_to_save"]:
        config_obj = {
            "knowledge_begin_date": current_time,
            "knowledge_end_date": None,
            "is_deleted": False,
            "client": client_id,
            "databook_id": config["databook_id"],
            "datasheet_id": config["datasheet_id"],
            "user_field": config["user_field"],
            "source_field": config["source_field"],
            "field_context": config["field_context"],
        }
        audit_data_list.append(
            {
                "event_key": "hris_" + config["user_field"],
                "summary": "Updated " + display_name_map[config["user_field"]],
                "mapped_field": (
                    display_name_map[config["source_field"]]
                    if config["source_field"] in display_name_map
                    else None
                ),
            }
        )
        fields_to_invalidate.append(config["user_field"])
        configs.append(config_obj)

        field_contexts.add(config["field_context"])

    # If there are effective dated field context mappings, then check if datasheet has effective dates
    if len(field_contexts) > 1:
        datasheet = DatasheetAccessor(
            client_id=client_id
        ).get_datasheet_source_and_transformation_spec(datasheet_id=datasheet_id)
        has_effective_dates = False
        for transformation in datasheet[0].get("transformation_spec", []):
            if transformation.get("type") == "TEMPORAL_SPLICE":
                has_effective_dates = has_effective_dates or any(
                    transf_source.get("has_effective_dates", False)
                    for transf_source in transformation.get("meta", [])
                )
        if not has_effective_dates:
            raise Exception("Datasheet does not have effective dates")

    for config in config_data["configs_to_invalidate"]:
        if config["user_field"] not in fields_to_invalidate:
            audit_data_list.append(
                {
                    "event_key": "hris_" + config["user_field"],
                    "summary": "Updated " + display_name_map[config["user_field"]],
                    "mapped_field": (
                        display_name_map[config["source_field"]]
                        if config["source_field"] in display_name_map
                        else None
                    ),
                }
            )
            fields_to_invalidate.append(config["user_field"])

    if len(fields_to_invalidate) > 0:
        query_set = HrisConfigAccessor(
            client_id=client_id
        ).get_configs_for_everstage_fields(everstage_fields=fields_to_invalidate)
        if query_set.exists():
            query_set.update(knowledge_end_date=current_time)

    if len(configs) > 0:
        config_ser = HrisConfigSerializer(data=configs, many=True)
        if config_ser.is_valid():
            HrisConfigAccessor(client_id=client_id).save_hris_config(config_ser)
        else:
            raise Exception(config_ser.errors)

    event_type_code = EVENT["SAVE_HRIS-CONFIG"]["code"]
    audit_services.bulk_log(
        client_id,
        event_type_code,
        user_email,
        current_time,
        audit_data_list,
    )


# def run_hris_sync_for_config_id(client_id, config_id, audit):
#     """
#     This function is used to run hris sync for given access_token_config_ids.
#     :param client_id: client id
#     :param config_ids: config ids
#     :return: None
#     """
#     config_records = ExtractionConfigAccessor(
#         client_id=client_id
#     ).get_records_by_access_token_config_ids(access_token_config_ids=[config_id])
#     integration_ids = []
#     if len(config_records) > 0:
#         for config_record in config_records:
#             integration_ids.append(str(config_record.integration_id))
#         e2e_sync_run_id = uuid.uuid4()
#         log_context = {"client_id": client_id, "e2e_sync_run_id": e2e_sync_run_id}
#         ETLSync(
#             client_id=client_id,
#             e2e_sync_run_id=e2e_sync_run_id,
#             log_context=log_context,
#             notification_email_id=None,
#             skip_archived_books=True,
#         ).run_daily_sync(
#             all_objects_selected=False,
#             post_upstream_disabled=True,
#             integration_ids=integration_ids,
#             audit=audit,
#         )


def get_ever_fields_to_display_name_datatype_map(client_id):
    """
    This function is used to get ever fields to display name map.
    :return: ever fields to display name map
    """
    ever_fields_to_display_name_map = {}
    for field in default_hris_fields:
        ever_fields_to_display_name_map[field["user_field"]] = {
            "display_name": field["display_name"],
            "datatype_id": field["datatype_id"],
            "data_type": field["data_type"],
        }

    custom_fields = CustomFieldsAccessor(
        client_id=client_id
    ).get_active_custom_fields_by_client(
        projection=[
            "display_name",
            "system_name",
            "data_type_id",
            "data_type__data_type",
        ]
    )
    for custom_field in custom_fields:
        ever_fields_to_display_name_map[custom_field["system_name"]] = {
            "display_name": custom_field["display_name"],
            "datatype_id": custom_field["data_type_id"],
            "data_type": custom_field["data_type__data_type"],
        }
    return ever_fields_to_display_name_map
