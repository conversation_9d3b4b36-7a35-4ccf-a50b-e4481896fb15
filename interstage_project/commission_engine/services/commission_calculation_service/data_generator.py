import time

import numpy as np
import pandas as pd
from django.core.cache import caches

from commission_engine.utils import LogWithContext, change_to_base_currency
from commission_engine.utils.payroll_utils import (
    get_periodic_variable_pay_for_employees,
    get_variable_pay_per_period,
)
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import (
    EmployeePayrollAccessor,
    HierarchyAccessor,
)
from spm.accessors.employee_accessor_v2 import EmployeeReadAccessor

cache = caches["default"]


def remove_nan_nat(df):
    df = df.replace({"None": None})
    df = df.replace({np.nan: None})  # removes NaT
    df = df.where(pd.notnull(df), None)  # removes nan
    return df


def create_df(data_list, key_list=None):
    ommi_list = [
        "client_id",
        "temporal_id",
        "knowledge_begin_date",
        "knowledge_end_date",
        "is_deleted",
        "additional_details",
    ]
    if key_list:
        ommi_list += key_list
    if data_list:
        df = pd.DataFrame(data_list)
        df = df.drop(ommi_list, axis=1)
        df = remove_nan_nat(df)
        return df
    else:
        return pd.DataFrame()


def get_merged_data(primary_dict, sec_dict):
    result = {}
    for attr_id, primary_val in primary_dict.items():
        result[attr_id] = primary_val
        if sec_dict and attr_id in sec_dict:
            result[attr_id].update(sec_dict[attr_id])
    return result


def construct_pkey_hash(key, datalist):
    result = {}
    for item in datalist:
        if key in item:
            result[item[key]] = item
    return result


def get_employee_data(
    client_id,
    period_start_date,
    period_end_date,
    email_key,
    payee_email_tuple=None,
    logger=None,
):
    logger = (
        logger
        if logger
        else LogWithContext(
            {
                "client_id": client_id,
                "period_start_date": period_start_date,
                "period_end_date": period_end_date,
                "payee_field": email_key,
                "payee_email_tuple": payee_email_tuple,
            }
        )
    )
    employee_payroll_accessor = EmployeePayrollAccessor(client_id)
    employee_accessor = EmployeeReadAccessor(client_id)
    hierarchy_accessor = HierarchyAccessor(client_id)
    logger.debug("Before: get_all_valid_employees_for_date")
    start = time.time()
    if payee_email_tuple:
        employee_payroll_records = (
            employee_payroll_accessor.get_all_valid_employees_for_date_by_email(
                period_end_date, payee_email_tuple, True
            )
        )
    else:
        employee_payroll_records = (
            employee_payroll_accessor.get_all_valid_employees_for_date(
                period_end_date, True
            )
        )
    end = time.time()
    logger.debug(
        "After: get_all_valid_employees_for_date. Time taken: {} seconds".format(
            end - start
        )
    )

    empList = []
    # conversion_rate and joining_date
    logger.debug("Before: for loop in get_employee_data")
    start = time.time()
    employee_emails = list(
        set(
            payroll_record["employee_email_id"]
            for payroll_record in employee_payroll_records
        )
    )
    employee_to_manager_map = hierarchy_accessor.get_employee_to_manager_dict(
        period_end_date, employee_emails
    )
    manager_to_employee_map = hierarchy_accessor.get_manager_to_employee_map(
        period_end_date, employee_emails
    )
    employee_records = employee_accessor.get_all_employees_including_exit_payees(
        projection=["employee_email_id", "first_name", "last_name"]
    )
    email_to_name_map = {
        emp["employee_email_id"]: {
            "full_name": f"{emp['first_name']} {emp['last_name']}",
            "first_name": emp["first_name"],
            "last_name": emp["last_name"],
        }
        for emp in employee_records
    }
    active_countries = {
        country.country_code: country.country_name
        for country in list(CountriesAccessor(client_id).get_all_active_countries())
    }
    variable_pay_map = get_periodic_variable_pay_for_employees(
        client_id, employee_payroll_records, period_end_date
    )
    for payroll_record in employee_payroll_records:
        if payroll_record["payout_frequency"]:
            h = {}

            varibale_pay = variable_pay_map.get(payroll_record["employee_email_id"], 0)
            varibale_pay = change_to_base_currency(
                client_id, varibale_pay, payroll_record["pay_currency"], period_end_date
            )
            h["variable_pay"] = varibale_pay
            h["data_variable_pay_as_per_period"] = get_variable_pay_per_period(
                client_id,
                payroll_record.get("variable_pay", 0),
                payroll_record["payout_frequency"],
                period_end_date,
            )
            h["data_variable_pay_in_table"] = payroll_record.get("variable_pay", 0)

            actual_variable_pay = payroll_record.get("variable_pay", 0)
            actual_variable_pay = change_to_base_currency(
                client_id,
                actual_variable_pay,
                payroll_record["pay_currency"],
                period_end_date,
            )
            h["data_variable_pay_base_currency"] = actual_variable_pay
            h["data_variable_pay_by_month"] = round(actual_variable_pay / 12, 2)

            h["email"] = payroll_record["employee_email_id"]
            h["actual_variable_pay"] = actual_variable_pay
            h["data_payout_frequency"] = payroll_record["payout_frequency"]
            h["data_pay_currency"] = payroll_record["pay_currency"]
            # Commenting the fixed_pay as we not using in evaluate and reports
            # h["fixed_pay"] = payroll_record["fixed_pay"]
            h["data_joining_date"] = payroll_record["joining_date"]
            h["data_exit_date"] = payroll_record["exit_date"]
            h["data_employment_country"] = payroll_record["employment_country"]
            h["data_employment_country_name"] = active_countries.get(
                payroll_record["employment_country"], ""
            )
            h["data_full_name"] = email_to_name_map.get(
                payroll_record["employee_email_id"], {}
            ).get("full_name", "")
            h["data_first_name"] = email_to_name_map.get(
                payroll_record["employee_email_id"], {}
            ).get("first_name", "")
            h["data_last_name"] = email_to_name_map.get(
                payroll_record["employee_email_id"], {}
            ).get("last_name", "")
            h["data_designation"] = payroll_record["designation"]
            h["data_employee_id"] = payroll_record["employee_id"]
            h["data_manager_email"] = employee_to_manager_map.get(
                payroll_record["employee_email_id"], None
            )
            h["data_manager_name"] = email_to_name_map.get(
                h["data_manager_email"], {}
            ).get("full_name", "")
            h["data_payee_or_manager"] = (
                "Payee Manager" if h["email"] in manager_to_employee_map else "Payee"
            )

            empList.append(h)

    end = time.time()
    logger.debug(
        "After: for loop in get_employee_data. Time taken: {} seconds".format(
            end - start
        )
    )
    return empList
