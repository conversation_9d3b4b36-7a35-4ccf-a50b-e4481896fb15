import logging
import os
import traceback
import uuid
from decimal import ROUND_HALF_UP, Decimal
from functools import lru_cache

import pandas as pd
import pydash
from django.db import transaction
from django.utils import timezone

import commission_engine.services.commission_calculation_service.criteria_calculation_service as ccs
import interstage_project.utils as iputils
from commission_engine.accessors.accessor_factories.commission_accessor_factory import (
    CommissionAccessorFactory,
    InterCommissionAccessorFactory,
    InterQuotaErosionAccessorFactory,
    QuotaErosionAccessorFactory,
)
from commission_engine.accessors.accessor_factories.commission_sec_kd_accessor_factory import (
    CommissionSecondaryKdAccessorFactory,
)
from commission_engine.accessors.accessor_factories.lock_accessor_factory import (
    CommissionLockAccessorFactory,
)
from commission_engine.accessors.client_accessor import (
    can_use_custom_metrics,
    get_client_features,
    get_client_fiscal_start_month,
    get_client_hidden_quota_categories,
    get_ever_comparison,
    is_settlement_v3_enabled,
    is_split_summation_to_li_enabled,
    is_write_settlement_to_snowflake,
)
from commission_engine.accessors.databook_accessor import (
    DatasheetAccessor,
    DatasheetVariableAccessor,
)
from commission_engine.accessors.settlement_accessor import (
    CommissionSettlementMapAccessor,
    SettlementAccessor,
)
from commission_engine.accessors.skd_pkd_map_accessor import DbkdPkdMapAccessor
from commission_engine.models import Settlement
from commission_engine.models.factories.commission_factories import (
    CommissionFactory,
    InterCommissionFactory,
    InterQuotaErosionFactory,
    QuotaErosionFactory,
)
from commission_engine.services.commission_calculation_service.team_calculator import (
    get_team_type,
)
from commission_engine.services.settlement_calculation_service.settlement_calc_etl import (
    SettlementCalcETL,
)
from commission_engine.utils import COMMISSION_TYPE, STATUS_CODE, CommCalcLevels
from commission_engine.utils.cache_utils import (
    delete_commission_cache,
    delete_crystal_cache,
    delete_qe_cache_key,
)
from commission_engine.utils.date_utils import (
    end_of_day,
    find_period,
    get_fiscal_year,
    get_period_label,
    get_period_timeframe,
    make_aware_wrapper,
    start_of_day,
)
from commission_engine.utils.fx_utils import FXRate
from commission_engine.utils.general_data import Freq
from commission_engine.utils.log_utils import merge_log_context
from everstage_ddd.custom_metrics import (
    compute_custom_metrics,
    invalidate_custom_metrics_in_comm_sync,
    persist_custom_metrics,
)
from everstage_ddd.settlement_snowflake_utils import (
    get_settlement_record_key,
    handle_write_with_retry,
    parse_invalidation_columns_and_set_kd_to_settlement_records,
    write_settlement_invalidation_data_to_s3,
)
from spm.accessors.accessor_factories.commission_plan_accessor_factory import (
    CommissionPlanAccessorFactory,
    PlanCriteriaAccessorFactory,
)
from spm.accessors.accessor_factories.plan_details_accessor_factory import (
    PlanDetailsAllAccessorFactory,
)
from spm.accessors.payout_accessor import PayoutAccessor
from spm.accessors.quota_acessors import QuotaAccessor
from spm.services.custom_calendar_services import get_custom_calendar_map
from spm.services.databook_services import get_databook_name, get_datasheet_name

logger = logging.getLogger("django")


@transaction.atomic
def calculate_and_persist_for_payee(
    client_id,
    period_start_date,
    period_end_date,
    sec_kd,
    payee_email,
    params=None,
    version=2,
    level=CommCalcLevels.L1.value,
    commission_type=COMMISSION_TYPE.COMMISSION,
    evaluation_mode_override=None,
    log_context=None,
    s3_batch_id=1,
):
    if version == 3:
        from .commission_calc_etl_v3 import calculate_and_persist_for_payee as v3

        return v3(
            client_id,
            period_start_date,
            period_end_date,
            sec_kd,
            payee_email,
            params,
            evaluation_mode_override=evaluation_mode_override,
        )
    return _calculate_and_persist_commissions(
        client_id,
        period_start_date,
        period_end_date,
        sec_kd,
        payee_email,
        level,
        params,
        commission_type=commission_type,
        log_context=log_context,
        s3_batch_id=s3_batch_id,
    )


def get_input_key_for_what_if(client_id, datasheet_id):
    tags = {"value": "deal_id", "category": "field_label"}
    datasheet_vars = DatasheetVariableAccessor(client_id).get_objects_by_tag(
        datasheet_id, tags
    )
    datasheet_var = datasheet_vars[0] if datasheet_vars else None
    if datasheet_var:
        return datasheet_var.system_name
    return None


def handle_settlement_commission_sync(
    client_id,
    plan_id,
    criteria_id,
    payee_email,
    period_start_date,
    period_end_date,
    sec_kd,
    commissions,
    existing_line_item_ids,
    is_line_item_level,
    log_context=None,
    locked_kd=None,
    e2e_sync_run_id=None,
    sync_run_id=None,
    level=None,
    team_or_payee_sync=None,
    s3_batch_id=1,
):
    """
    Check if the commission criteria has active settlement rules:
    * if True, call settlement ETL sync.
    * Else, Create settlement records for the calculated commission records
    """
    log_context = log_context or {}
    log = iputils.LogWithContext(log_context)

    log.info("BEGIN: handle settlement commission sync")
    commission_line_item_ids = set()
    comm_ids_to_be_deleted = []
    for rec in commissions:
        commission_line_item_ids.add(pydash.get(rec, "id"))
    if existing_line_item_ids:
        comm_ids_to_be_deleted = list(
            set(existing_line_item_ids) - commission_line_item_ids
        )
        log.debug(
            f"Commission line items to be deleted from settlement - {comm_ids_to_be_deleted}"
        )
        # invalidate  settlement on booking records with commission prd this period for this criteria

    # has_settlement_rules = CommissionSettlementMapAccessor(client_id).has_settlement_rules(plan_id, criteria_id)
    settlement_rule_ids = CommissionSettlementMapAccessor(
        client_id
    ).get_settlement_rule_ids_mapped_to_criteria(criteria_id)

    # get records to be invalidated from snowflake and store it in parquet
    write_settlement_to_snowflake = is_write_settlement_to_snowflake(
        client_id=client_id
    )
    if write_settlement_to_snowflake:
        batch_size = 10000
        batches = [
            comm_ids_to_be_deleted[i : i + batch_size]
            for i in range(0, len(comm_ids_to_be_deleted), batch_size)
        ]
        for batch_number, batch in enumerate(batches):
            records_to_invalidate_for_period_by_comm_row_key = SettlementAccessor(
                client_id
            ).get_records_to_invalidate_for_period_by_comm_row_key(
                payee_id=payee_email,
                psd=period_start_date,
                ped=period_end_date,
                plan_id=plan_id,
                criteria_id=criteria_id,
                line_item_ids=batch,
                values=True,
            )
            # set knowledge end date to all records that are to be invalidated
            snowflake_invalidation_records = parse_invalidation_columns_and_set_kd_to_settlement_records(
                records_to_invalidate=records_to_invalidate_for_period_by_comm_row_key,
                knowledge_end_date=sec_kd,
            )

            # insert the invalidation records data to snowflake
            write_settlement_invalidation_data_to_s3(
                snowflake_invalidation_records=snowflake_invalidation_records,
                client_id=client_id,
                e2e_sync_run_id=e2e_sync_run_id,
                sync_run_id=sync_run_id,
                commission_sync=True,
                level=level,
                team_or_payee_sync=team_or_payee_sync,
                batch_number=batch_number,
                s3_batch_id=s3_batch_id,
            )

    # deleting settlement entries for the deleted commission records
    SettlementAccessor(client_id).invalidate_records_for_period_by_comm_row_key(
        payee_email,
        period_start_date,
        period_end_date,
        plan_id,
        criteria_id,
        comm_ids_to_be_deleted,
        sec_kd,
    )

    if settlement_rule_ids:
        log.info(
            f"Total: {len(settlement_rule_ids)}-Settlement rules available for criteria {criteria_id}"
        )
        settlement_calc_obj = SettlementCalcETL(
            client_id,
            period_start_date,
            period_end_date,
            payee_email,
            sec_kd,
            log_context,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
        )
        # When commission is zero because there is no data
        if (
            commissions
            and len(commissions) == 1
            and pydash.get(commissions[0], "commission") == 0
            and not pydash.get(commissions[0], "id")
        ):
            for settlement_rule_id in settlement_rule_ids:
                obj = Settlement(
                    client_id=client_id,
                    knowledge_begin_date=sec_kd,
                    comm_period_start_date=period_start_date,
                    comm_period_end_date=period_end_date,
                    commission_date=pydash.get(commissions[0], "commission_date"),
                    period_start_date=period_start_date,
                    period_end_date=period_end_date,
                    settlement_date=pydash.get(commissions[0], "commission_date"),
                    plan_id=plan_id,
                    criteria_id=criteria_id,
                    payee_email_id=payee_email,
                    settlement_rule_id=settlement_rule_id,
                    line_item_id=None,
                    amount=0.0,
                    settlement_flag=True,
                    commission_row_key=pydash.get(commissions[0], "id"),
                )

                # mapping of settlement record to additional data
                settlement_records_additional_data_map = dict()
                settlement_record_key = get_settlement_record_key(settlement_obj=obj)
                settlement_records_additional_data_map[settlement_record_key] = {
                    "commission_amount": 0.0,
                    "comm_secondary_kd": sec_kd,
                    "locked_kd": locked_kd,
                }

                settlement_calc_obj.process_settlement_objects(
                    plan_id=plan_id,
                    criteria_id=criteria_id,
                    settlement_rule_id=settlement_rule_id,
                    settlement_objects=[obj],
                    is_line_item_level=is_line_item_level,
                    settlement_records_additional_data_map=settlement_records_additional_data_map,
                    commission_sync=True,
                    level=level,
                    team_or_payee_sync=team_or_payee_sync,
                    s3_batch_id=s3_batch_id,
                )
        else:
            settlement_calc_obj.run_sync(
                commission_sync=True,
                commission_plan_ids=[plan_id],
                criteria_ids=[criteria_id],
                level=level,
                team_or_payee_sync=team_or_payee_sync,
                s3_batch_id=s3_batch_id,
            )
    else:
        log.info(
            "Settlement rules NOT available. Creating settlement records with commission values."
        )
        # Group commission records by line_item_ids (commissions from different tier will sum up)
        grouped_records = {}
        for com in commissions:
            amount = round_half_up(pydash.get(com, "commission"))
            line_item_id = pydash.get(com, "id")
            commission_date = pydash.get(com, "commission_date")

            if line_item_id not in grouped_records:
                grouped_records[line_item_id] = {
                    "line_item_id": line_item_id,
                    "commission_date": commission_date,
                    "amount": Decimal(0),
                }
            grouped_records[line_item_id]["amount"] += Decimal(str(amount))

        settlement_calc_obj = SettlementCalcETL(
            client_id,
            period_start_date,
            period_end_date,
            payee_email,
            sec_kd,
            log_context,
            e2e_sync_run_id=e2e_sync_run_id,
            sync_run_id=sync_run_id,
        )

        settlement_objs = list()
        # mapping of settlement record to additional data
        settlement_records_additional_data_map = dict()
        for record in grouped_records.values():
            settlement_obj = Settlement(
                client_id=client_id,
                knowledge_begin_date=sec_kd,
                comm_period_start_date=period_start_date,
                comm_period_end_date=period_end_date,
                commission_date=record["commission_date"],
                period_start_date=period_start_date,
                period_end_date=period_end_date,
                settlement_date=record["commission_date"],
                plan_id=plan_id,
                criteria_id=criteria_id,
                payee_email_id=payee_email,
                settlement_rule_id=None,
                line_item_id=record["line_item_id"],
                amount=record["amount"],
                settlement_flag=True,
                commission_row_key=record["line_item_id"],
            )
            settlement_objs.append(settlement_obj)
            settlement_record_key = get_settlement_record_key(
                settlement_obj=settlement_obj
            )
            settlement_records_additional_data_map[settlement_record_key] = {
                "commission_amount": record["amount"],
                "comm_secondary_kd": sec_kd,
                "locked_kd": locked_kd,
            }

        log.debug(f"Total: {len(settlement_objs)}-Settlement objects created")

        settlement_calc_obj.process_settlement_objects(
            plan_id=plan_id,
            criteria_id=criteria_id,
            settlement_rule_id=None,
            settlement_objects=settlement_objs,
            is_line_item_level=is_line_item_level,
            settlement_records_additional_data_map=settlement_records_additional_data_map,
            commission_sync=True,
            level=level,
            team_or_payee_sync=team_or_payee_sync,
            s3_batch_id=s3_batch_id,
        )
    log.info("END: handle settlement commission sync")


def invalidate_comm_qe_data_by_level(
    client_id,
    criteria_ids,
    psd,
    ped,
    payee_email,
    kd,
    level,
    expose_inter_commission,
    commission_type=COMMISSION_TYPE.COMMISSION,
):
    if level == CommCalcLevels.L1.value and expose_inter_commission:
        InterCommissionAccessorFactory(
            client_id=client_id, commission_type=commission_type
        ).get_accessor().invalidate_for_payee_with_criteria_list(
            psd, ped, payee_email, criteria_ids, kd
        )
        InterQuotaErosionAccessorFactory(
            client_id=client_id, commission_type=commission_type
        ).get_accessor().invalidate_for_payee_with_criteria_list(
            psd, ped, payee_email, criteria_ids, kd
        )

    ca = CommissionAccessorFactory(
        client_id=client_id, commission_type=commission_type
    ).get_accessor()
    ca.invalidate_for_payee_with_criteria_list(
        psd,
        ped,
        payee_email,
        criteria_ids,
        kd,
    )
    qa = QuotaErosionAccessorFactory(
        client_id=client_id, commission_type=commission_type
    ).get_accessor()
    qa.invalidate_for_payee_with_criteria_list(
        psd,
        ped,
        payee_email,
        criteria_ids,
        kd,
    )


def insert_comm_qe_data_by_level(
    client_id,
    comm_data,
    quota_data,
    level,
    expose_inter_commission,
    intermediate_only=False,
    commission_type=COMMISSION_TYPE.COMMISSION,
):
    comm_objs = []
    inter_comm_objs = []
    quota_objs = []
    inter_quota_objs = []
    if level == CommCalcLevels.L1.value and expose_inter_commission:
        for each_data in comm_data:
            inter_comm_objs.append(
                InterCommissionFactory(commission_type).create_data(**each_data)
            )
            if not intermediate_only:
                comm_objs.append(
                    CommissionFactory(commission_type).create_data(**each_data)
                )

        for each_data in quota_data:
            inter_quota_objs.append(
                InterQuotaErosionFactory(commission_type).create_data(**each_data)
            )
            if not intermediate_only:
                quota_objs.append(
                    QuotaErosionFactory(commission_type).create_data(**each_data)
                )
    else:
        for each_data in comm_data:
            comm_objs.append(
                CommissionFactory(commission_type).create_data(**each_data)
            )

        for each_data in quota_data:
            quota_objs.append(
                QuotaErosionFactory(commission_type).create_data(**each_data)
            )

    if inter_comm_objs:
        ca = InterCommissionAccessorFactory(client_id, commission_type).get_accessor()
        ca.insert_new_data(inter_comm_objs)

    if comm_objs:
        ca = CommissionAccessorFactory(client_id, commission_type).get_accessor()
        ca.insert_new_data(comm_objs)

    if inter_quota_objs:
        ca = InterQuotaErosionAccessorFactory(client_id, commission_type).get_accessor()
        ca.insert_new_data(inter_quota_objs)

    if quota_objs:
        ca = QuotaErosionAccessorFactory(client_id, commission_type).get_accessor()
        ca.insert_new_data(quota_objs)


def _get_main_and_spiff_plan_list(comm_plans):
    # creating main and spiff plans list.
    main_comm_plans = []
    spiff_comm_plans = []
    for plan in comm_plans:
        plan_type = plan.plan_type
        if plan_type == "MAIN":
            main_comm_plans.append(plan)
        else:
            spiff_comm_plans.append(plan)
    return spiff_comm_plans, main_comm_plans


def _get_plan_display_order_map(spiff_comm_plans, main_comm_plans):
    # plan_order_map will contain the plan_id and respective plan_display_order, required to get plans in sorted form according to plan display order.
    main_max = 0
    for plan in main_comm_plans:
        if plan.plan_display_order is not None and plan.plan_display_order > main_max:
            main_max = plan.plan_display_order
    plan_order_map = {}
    for plan in main_comm_plans:
        if plan.plan_display_order is None:
            main_max += 1
        order = plan.plan_display_order if plan.plan_display_order else main_max
        plan_order_map[plan.plan_id] = order

    spiff_max = main_max
    for plan in spiff_comm_plans:
        if plan.plan_display_order is not None:
            order = plan.plan_display_order + main_max
            spiff_max = max(spiff_max, order)
            plan_order_map[plan.plan_id] = order

    for plan in spiff_comm_plans:
        if plan.plan_display_order is None:
            spiff_max += 1
            order = spiff_max
            plan_order_map[plan.plan_id] = order
    return plan_order_map


def _update_whatif_results(comm_plans, whatif_results):
    for plan in comm_plans:
        whatif_results[str(plan.plan_id)]["plan_name"] = plan.plan_name
    return whatif_results


def _get_sorted_plan_order_map(comm_plans):
    (
        spiff_comm_plans,
        main_comm_plans,
    ) = _get_main_and_spiff_plan_list(comm_plans)

    plan_order_map = _get_plan_display_order_map(spiff_comm_plans, main_comm_plans)
    return plan_order_map


# params will come from celery task and what_if
def _calculate_and_persist_commissions(
    client_id,
    period_start_date,
    period_end_date,
    secondary_knowledge_date,
    payee_email,
    level,
    params=None,
    commission_type=COMMISSION_TYPE.COMMISSION,
    log_context=None,
    s3_batch_id=1,
):
    client_features = get_client_features(client_id=client_id)
    expose_inter_commission = client_features.get("expose_comm_reports_in_plan", False)
    is_plan_exclusion_enabled = client_features.get("enable_plan_exclusion", False)
    e2e_sync_run_id = pydash.get(params, "e2e_sync_run_id")
    sync_run_id = pydash.get(params, "sync_run_id")
    split_summation_to_li = is_split_summation_to_li_enabled(client_id)
    write_commission_to_snowflake = client_features.get(
        "write_commission_to_snowflake", False
    )
    write_quota_erosion_to_snowflake = client_features.get(
        "write_quota_erosion_to_snowflake", False
    )
    run_settlement_v3 = is_settlement_v3_enabled(client_id)
    local_log_context = {
        "client_id": client_id,
        "e2e_sync_run_id": e2e_sync_run_id,
        "sync_run_id": sync_run_id,
        "psd": period_start_date,
        "ped": period_end_date,
        "payee_email": payee_email,
        "criteria_level": level,
    }
    log_context = log_context or {}
    log_context = merge_log_context(log_context, local_log_context)
    log = iputils.LogWithContext(log_context)
    try:
        hidden_quota_categories: list[str] = get_client_hidden_quota_categories(
            client_id
        )
        fiscal_start = get_client_fiscal_start_month(client_id)
        fiscal_year = get_fiscal_year(fiscal_start, period_start_date)
        from spm.services.quota_services import get_quota_details_for_payee_period

        (
            individual_quota_details,
            team_quota_details,
        ) = get_quota_details_for_payee_period(
            client_id, fiscal_year, payee_email, period_end_date
        )
        what_if = pydash.get(params, "what_if") if params else False
        what_if_params = {} if what_if else None
        if what_if:
            what_if_params = {}
            what_if_params["what_if"] = True
            what_if_params["what_if_overrides"] = pydash.get(
                params, "what_if_overrides"
            )
            what_if_params["what_if_payee"] = pydash.get(params, "what_if_payee")
            what_if_params["what_if_payee_and_team"] = pydash.get(
                params, "what_if_payee_and_team"
            )
            what_if_params["what_if_team_members"] = pydash.get(
                params, "what_if_team_members"
            )
            what_if_params["what_if_uuid"] = pydash.get(params, "what_if_uuid")
            what_if_params["what_if_primary_kd"] = pydash.get(
                params, "what_if_primary_kd"
            )

        pda = PlanDetailsAllAccessorFactory(
            client_id=client_id, commission_type=commission_type
        ).get_accessor()
        ever_comparison = get_ever_comparison(client_id)
        log.debug(f"Is ever_comparison enabled - {ever_comparison}")
        emp_plans = pda.get_employee_plans_for_period(
            period_start_date, period_end_date, payee_email, as_dicts=False
        )
        # Get exclusion periods plan wise based on plan effective dates
        if is_plan_exclusion_enabled:
            plan_exclusion_period_map = get_exclusion_periods_for_plans(
                emp_plans, period_start_date, period_end_date
            )
        else:
            plan_exclusion_period_map = {}
        commission_uuid = (
            pydash.get(params, "sync_run_id")
            if params and pydash.get(params, "sync_run_id")
            else uuid.uuid4()
        )
        #######################################
        primary_kd = None
        plan_ids = []
        criteria_line_items_map = {}
        priority_criteria_map = {1: [], 2: []}
        plan_id_eff_date_map = {}
        whatif_results = {}

        for plan in emp_plans:
            plan_id_eff_date_map[str(plan.plan_id)] = {
                "effective_start_date": plan.effective_start_date,
                "effective_end_date": plan.effective_end_date,
            }
            # If plan has exclusion period, add it to plan_id_eff_date_map
            if str(plan.plan_id) in plan_exclusion_period_map:
                plan_id_eff_date_map[str(plan.plan_id)]["plan_exclusion_periods"] = (
                    plan_exclusion_period_map[str(plan.plan_id)]
                )
            whatif_results[str(plan.plan_id)] = {
                "plan_id": plan.plan_id,
                "criteria_outputs": {},
            }
            plan_ids.append(plan.plan_id)
        plan_id_to_plan_name_type_map = {}
        cpa = CommissionPlanAccessorFactory(
            client_id=client_id, commission_type=commission_type
        ).get_accessor()
        comm_plans = cpa.get_commission_plan_by_plan_id(plan_ids)
        for plan in comm_plans:
            plan_id_to_plan_name_type_map[str(plan.plan_id)] = {
                "plan_name": plan.plan_name,
                "plan_type": plan.plan_type,
            }
        whatif_results = _update_whatif_results(comm_plans, whatif_results)
        plan_order_map = _get_sorted_plan_order_map(comm_plans)
        pca = PlanCriteriaAccessorFactory(
            client_id=client_id, commission_type=commission_type
        ).get_accessor()
        criterias = pca.get_criterias_for_a_plan_by_level(
            plan_ids, level, as_dicts=False
        )
        ######### construct criteria order map
        criteria_order_map = {}
        for c in criterias:
            plan_order = plan_order_map[c.plan_id]
            criteria_order = c.criteria_display_order if c.criteria_display_order else 0
            criteria_order_map[c.criteria_id] = (plan_order, criteria_order)

        criteria_ids = []
        if params and pydash.get(params, "team_task"):
            for c in criterias:
                if (
                    c.criteria_type.lower() in ["team", "customteam"]
                ) and c.criteria_data["type"].lower() == "quota":
                    priority_criteria_map[1].append(c)
                    criteria_ids.append(c.criteria_id)
                elif c.criteria_type.lower() in ["team", "customteam"]:
                    priority_criteria_map[2].append(c)
                    criteria_ids.append(c.criteria_id)
        else:
            for c in criterias:
                if c.criteria_type.lower() in ["quota", "customquota"]:
                    priority_criteria_map[1].append(c)
                    criteria_ids.append(c.criteria_id)
                elif c.criteria_type.lower() in [
                    "simple",
                    "conditional",
                    "tier",
                    "customsimple",
                    "customconditional",
                    "customtier",
                ]:
                    priority_criteria_map[2].append(c)
                    criteria_ids.append(c.criteria_id)
        sorted_keys = [1, 2]
        custom_criterias = [
            "customsimple",
            "customconditional",
            "customtier",
            "customquota",
            "customteam",
        ]
        if not what_if:
            criteria_line_items_map = {}
            ca = CommissionAccessorFactory(client_id, commission_type).get_accessor()
            for criteria_id in criteria_ids:
                criteria_line_items_map[criteria_id] = (
                    ca.get_existing_line_item_ids_for_criteria(
                        period_start_date, period_end_date, criteria_id, payee_email
                    )
                )
            log.debug(
                f"Invalidate existing commission and quota erosion data of criteria_ids - {criteria_ids}"
            )
            invalidate_comm_qe_data_by_level(
                client_id=client_id,
                criteria_ids=criteria_ids,
                psd=period_start_date,
                ped=period_end_date,
                payee_email=payee_email,
                kd=secondary_knowledge_date,
                level=level,
                expose_inter_commission=expose_inter_commission,
                commission_type=commission_type,
            )
            log.info("Invalidated existing commission and quota erosion data")

            if can_use_custom_metrics(client_id):
                invalidate_custom_metrics_in_comm_sync(
                    client_id, plan_ids, payee_email, period_start_date, period_end_date
                )
                for plan_id in plan_ids:
                    computed_custom_metrics = compute_custom_metrics(
                        client_id,
                        plan_id,
                        payee_email,
                        period_start_date,
                        period_end_date,
                        secondary_knowledge_date,
                        ever_comparison,
                        "commission",
                    )
                    persist_custom_metrics(
                        client_id,
                        computed_custom_metrics,
                        payee_email,
                        plan_id,
                        period_start_date,
                        period_end_date,
                        commission_uuid,
                    )

        curr_locked = (
            CommissionLockAccessorFactory(client_id, commission_type)
            .get_accessor()
            .get_locked_kd_for_locked_comm_in_period(
                period_start_date, period_end_date, payee_email
            )
        )
        # Invalidate All commission and quotaerosion entries for payee in this period
        for pri in sorted_keys:
            criteria_list = priority_criteria_map[pri]
            order_map = {}
            for c in criteria_list:
                order = criteria_order_map[c.criteria_id]
                if order not in order_map:
                    order_map[order] = []
                order_map[order].append(c)
            ordered_keys = list(order_map.keys())
            ordered_keys.sort()
            ordered_criteria_list = []
            for order in ordered_keys:
                ordered_criteria_list.append(order_map[order])
            ordered_criteria_list = pydash.flatten(ordered_criteria_list)
            log.update_context({"priority": pri})
            log.info(f"BEGIN: Execution for priority-{pri} criterias")
            for criteria in ordered_criteria_list:
                log.update_context(
                    {
                        "criteria_id": criteria.criteria_id,
                        "criteria_name": criteria.criteria_name,
                    }
                )
                log.info(
                    f"BEGIN: CRITERIA Id: {criteria.criteria_id}, Name: {criteria.criteria_name}"
                )
                comm_objs = []
                snowflake_comm_objs = []
                quota_objs = []
                snowflake_quota_objs = []
                criteria_details = {}
                criteria_details["criteria_name"] = criteria.criteria_name
                criteria_details["criteria_id"] = criteria.criteria_id
                criteria_details["criteria_output"] = {}
                criteria_data = criteria.criteria_data
                intermediate_only = False
                # Flag to track if show do nothing records is enabled for criteria
                show_do_nothing_flag = False

                if (
                    criteria.criteria_config
                    and "intermediate_only" in criteria.criteria_config
                ):
                    intermediate_only = criteria.criteria_config["intermediate_only"]

                if (
                    criteria.criteria_config
                    and "show_do_nothing" in criteria.criteria_config
                ):
                    show_do_nothing_flag = criteria.criteria_config["show_do_nothing"]

                criteria_conditions = {
                    "databook_id": (
                        criteria_data["databook_id"]
                        if "databook_id" in criteria_data
                        else None
                    ),
                    "datasheet_id": (
                        criteria_data["datasheet_id"]
                        if "datasheet_id" in criteria_data
                        else None
                    ),
                    "payee_field": (
                        criteria_data["payee_field"]
                        if "payee_field" in criteria_data
                        else None
                    ),
                    "date_field": (
                        criteria_data["date_field"]
                        if "date_field" in criteria_data
                        else None
                    ),
                }
                criteria_name = criteria.criteria_name
                databook_name = get_databook_name(
                    client_id, str(criteria_conditions["databook_id"])
                )
                datasheet_name = get_datasheet_name(
                    client_id, str(criteria_conditions["datasheet_id"])
                )
                plan_name = plan_id_to_plan_name_type_map[str(criteria.plan_id)][
                    "plan_name"
                ]
                plan_type = plan_id_to_plan_name_type_map[str(criteria.plan_id)][
                    "plan_type"
                ]
                # employee_object = get_employee_object(client_id, payee_email)
                # employee_first_name = employee_object.first_name
                # employee_last_name = employee_object.last_name
                # employee_full_name = f"{employee_first_name} {employee_last_name}"

                if criteria.criteria_type.lower() in custom_criterias:
                    db_id = criteria_conditions["databook_id"]
                    ds_id = criteria_conditions["datasheet_id"]
                    datasheets = DatasheetAccessor(client_id).get_data_by_datasheet_id(
                        db_id, ds_id
                    )
                    line_item_type = datasheets[0].name if datasheets else ""
                    # get primary_kd from map
                    db_pk_acc = DbkdPkdMapAccessor(client_id)
                    db_pk_obj = db_pk_acc.get_rec_for_databook_id_datasheet_id(
                        db_id, ds_id, secondary_knowledge_date
                    )
                    primary_kd = (
                        db_pk_obj.primary_kd if db_pk_obj else secondary_knowledge_date
                    )
                    secondary_snapshot_id = db_id
                is_team = True if "team" in criteria_data else False
                team_id = None
                if is_team:
                    team_name = criteria_data["team"]
                    team_type = get_team_type(team_name)
                    team_id = payee_email + "_" + team_type
                quota_name = (
                    criteria_data["quota_name"]
                    if "quota_name" in criteria_data
                    else None
                )
                is_hidden_quota = (
                    True if quota_name in hidden_quota_categories else False
                )
                sort_cols = criteria.criteria_config.get("sort_cols")
                criteria_data["sort_cols"] = sort_cols
                criteria_data["split_summation_to_li"] = split_summation_to_li
                plan_effective_date = plan_id_eff_date_map[str(criteria.plan_id)]
                response = ccs.evaluate_criteria(
                    client_id,
                    period_start_date,
                    period_end_date,
                    payee_email,
                    criteria.plan_id,
                    criteria.criteria_id,
                    plan_effective_date,
                    secondary_knowledge_date,
                    criteria_data,
                    what_if_params,
                    ever_comparison=ever_comparison,
                    commission_type=commission_type,
                )
                team_owner_detail = response.get("team_owner_detail", None)
                payee_detail_for_snowflake_reports = response.get(
                    "payee_detail_sf_reports", None
                )
                # setting context_ids as empty [] for line item type
                # context_ids will be present only for summation level.
                context_ids = (
                    list(response["context"].keys())
                    if "is_line_item_level" in response
                    and response["is_line_item_level"] == False
                    and split_summation_to_li is False
                    and "context" in response
                    else []
                )
                # Handle Show-Do-Nothing Flag
                if (
                    context_ids
                    and "do_nothing_records" in response
                    and not show_do_nothing_flag
                ):
                    context_ids = list(
                        set(context_ids) - set(response["do_nothing_records"])
                    )
                commissions = response["result"]
                criteria_details["criteria_output"]["commission"] = commissions
                criteria_details["criteria_output"]["summary"] = response["summary"]
                criteria_details["criteria_output"]["is_line_item_level"] = response[
                    "is_line_item_level"
                ]
                criteria_details["criteria_output"]["context"] = response["context"]
                context_data = response["context"]
                total_commission_amount = 0.0
                commission_date = None
                payout_kd = PayoutAccessor(client_id).payout_for_payee_period(
                    period_end_date, payee_email
                )
                other_reference_data = {
                    **criteria_conditions,
                    "is_locked": curr_locked is not None,
                    "locked_kd": curr_locked,
                    "databook_name": databook_name,
                    "datasheet_name": datasheet_name,
                    "criteria_name": criteria_name,
                    "plan_name": plan_name,
                    "plan_type": plan_type,
                    "is_team": is_team,
                    "is_line_item_level": response["is_line_item_level"],
                    "is_hidden_quota": is_hidden_quota,
                    "period_start_date": period_start_date,
                    "period_end_date": period_end_date,
                    "fiscal_start": fiscal_start,
                    "fiscal_year": fiscal_year,
                    "payout_kd": payout_kd,
                    "is_paid": True if payout_kd else False,
                }
                if split_summation_to_li and not response["is_line_item_level"]:
                    other_reference_data["split_summation_to_li"] = (
                        split_summation_to_li
                    )
                if quota_name:
                    (
                        quota_period_details,
                        quota_schedule_details,
                    ) = get_quota_period_and_schedule_details_for_quota(
                        quota_name,
                        period_end_date,
                        fiscal_start,
                        team_quota_details if is_team else individual_quota_details,
                    )
                    period_timeframe = quota_period_details.get("period_timeframe")
                    quota_psd = quota_period_details.get("quota_psd")
                    quota_ped = quota_period_details.get("quota_ped")
                    quota_period = quota_period_details.get("quota_period")
                    quota_schedule_psd = quota_schedule_details.get(
                        "quota_schedule_psd"
                    )
                    quota_schedule_ped = quota_schedule_details.get(
                        "quota_schedule_ped"
                    )
                    quota_schedule = quota_schedule_details.get("quota_schedule")
                    other_reference_data["quota_name"] = quota_name
                    other_reference_data["quota_display_name"] = QuotaAccessor(
                        client_id
                    ).get_display_name_for_quota_category_name(quota_name)
                    other_reference_data["quota_period"] = quota_period
                    other_reference_data["quota_schedule"] = quota_schedule
                    other_reference_data["quota_schedule_start_date"] = (
                        quota_schedule_psd
                    )
                    other_reference_data["quota_schedule_end_date"] = quota_schedule_ped
                    other_reference_data["period_timeframe"] = period_timeframe
                    other_reference_data["quota_period_start_date"] = quota_psd
                    other_reference_data["quota_period_end_date"] = quota_ped
                for com in commissions:
                    tier_id = pydash.get(com, "tier_id")
                    original_tier_id = pydash.get(com, "original_tier_id")
                    amount = round_half_up(pydash.get(com, "commission"))
                    line_id = pydash.get(com, "id")
                    show_do_nothing = pydash.get(com, "show_do_nothing")
                    commission_date = pydash.get(com, "commission_date")
                    tier_name = pydash.get(com, "tier_name")
                    comm_data = {
                        "client_id": client_id,
                        "knowledge_begin_date": secondary_knowledge_date,
                        "commission_snapshot_id": commission_uuid,
                        "period_start_date": period_start_date,
                        "period_end_date": period_end_date,
                        "primary_kd": primary_kd,
                        "secondary_kd": secondary_knowledge_date,
                        "secondary_snapshot_id": secondary_snapshot_id,
                        "payee_email_id": payee_email,
                        "commission_plan_id": criteria.plan_id,
                        "criteria_id": criteria.criteria_id,
                        "line_item_type": line_item_type,
                        "context_ids": context_ids,
                        "line_item_id": line_id,
                        "tier_id": tier_id,
                        "original_tier_id": original_tier_id,
                        "amount": amount,
                        "show_do_nothing": show_do_nothing,
                        "commission_date": commission_date,
                    }
                    total_commission_amount += float(amount)
                    comm_objs.append(comm_data)
                    if write_commission_to_snowflake:
                        context_data_copy = context_data.copy()
                        comm_data_copy = comm_data.copy()
                        context_to_send = (
                            {line_id: context_data_copy[line_id]}
                            if line_id and response["is_line_item_level"]
                            else context_data_copy
                        )
                        snowflake_comm_objs.extend(
                            get_snowflake_commission_or_quota_object(
                                client_id,
                                comm_data_copy,
                                line_id,
                                other_reference_data,
                                context_to_send,
                                tier_name,
                                True,
                                team_owner_detail,
                                payee_detail_for_snowflake_reports,
                            )
                        )

                    if (
                        "quota_erosion" in com and "summary" in response
                    ):  # populate QE table
                        qv = pydash.get(response["summary"], "qv")
                        cum_qe = pydash.get(response["summary"], "cumulative_qe")
                        quota_erosion = pydash.get(com, "quota_erosion")
                        quota_data = {
                            "client_id": client_id,
                            "knowledge_begin_date": secondary_knowledge_date,
                            "commission_snapshot_id": commission_uuid,
                            "period_start_date": period_start_date,
                            "period_end_date": period_end_date,
                            "primary_kd": primary_kd,
                            "secondary_kd": secondary_knowledge_date,
                            "secondary_snapshot_id": secondary_snapshot_id,
                            "payee_email_id": payee_email,
                            "commission_plan_id": criteria.plan_id,
                            "criteria_id": criteria.criteria_id,
                            "quota_category_name": quota_name,
                            "line_item_type": line_item_type,
                            "qv": qv,
                            "cumulative_qe": cum_qe,
                            "quota_erosion": quota_erosion,
                            "is_team": is_team,
                            "team_id": team_id,
                            "context_ids": context_ids,
                            "line_item_id": line_id,
                            "tier_id": tier_id,
                            "original_tier_id": original_tier_id,
                        }
                        quota_objs.append(quota_data)
                        if write_quota_erosion_to_snowflake:
                            context_data_copy = context_data.copy()
                            quota_data_copy = quota_data.copy()
                            round_quota_erosion_keys(quota_data_copy)
                            context_to_send = (
                                {line_id: context_data_copy[line_id]}
                                if line_id and response["is_line_item_level"]
                                else context_data_copy
                            )
                            snowflake_quota_objs.extend(
                                get_snowflake_commission_or_quota_object(
                                    client_id,
                                    quota_data_copy,
                                    line_id,
                                    other_reference_data,
                                    context_to_send,
                                    tier_name,
                                    False,
                                    team_owner_detail,
                                    payee_detail_for_snowflake_reports,
                                )
                            )

                # delete existing quota_erosion cache
                if criteria_data["type"].lower() == "quota":
                    log.debug(f"Deleting Quota Erosion Cache for quota - {quota_name}")
                    delete_qe_cache_key(
                        client_id,
                        period_start_date,
                        period_end_date,
                        payee_email,
                        quota_name,
                        is_team,
                        curr_locked,
                        commission_type=commission_type,
                    )

                if what_if:
                    commissions_bef_round = criteria_details["criteria_output"][
                        "commission"
                    ]
                    for rec in commissions_bef_round:
                        rec["commission"] = round(rec["commission"], 2)
                    criteria_details["criteria_output"][
                        "commission"
                    ] = commissions_bef_round
                    criteria_details["criteria_output"]["input_key"] = (
                        get_input_key_for_what_if(client_id, ds_id)
                    )
                    criteria_details["criteria_output"]["is_team"] = is_team
                whatif_results[str(criteria.plan_id)]["criteria_outputs"][
                    str(criteria.criteria_id)
                ] = criteria_details
                if not what_if:
                    # Persist in DB
                    insert_comm_qe_data_by_level(
                        client_id,
                        comm_objs,
                        quota_objs,
                        level,
                        expose_inter_commission,
                        intermediate_only,
                        commission_type=commission_type,
                    )
                    log.info(
                        "Inserted Records into Commission and Quota Erosion tables"
                    )
                    team_or_payee_sync = "team_sync" if is_team else "payee_sync"
                    criteria_id_str = str(criteria.criteria_id).replace("-", "_")
                    if write_commission_to_snowflake and snowflake_comm_objs:
                        commission_s3_paths_to_write = (
                            get_s3_paths_to_write_commission_quota_data(
                                client_id,
                                criteria_id_str,
                                e2e_sync_run_id,
                                sync_run_id,
                                "commission",
                                level,
                                expose_inter_commission,
                                team_or_payee_sync,
                                intermediate_only,
                                s3_batch_id=s3_batch_id,
                            )
                        )
                        write_frame_as_parquet_to_s3(
                            snowflake_comm_objs, commission_s3_paths_to_write
                        )
                    if write_quota_erosion_to_snowflake and snowflake_quota_objs:
                        quota_s3_paths_to_write = (
                            get_s3_paths_to_write_commission_quota_data(
                                client_id,
                                criteria_id_str,
                                e2e_sync_run_id,
                                sync_run_id,
                                "quotaerosion",
                                level,
                                expose_inter_commission,
                                team_or_payee_sync,
                                intermediate_only,
                                s3_batch_id=s3_batch_id,
                            )
                        )
                        write_frame_as_parquet_to_s3(
                            snowflake_quota_objs, quota_s3_paths_to_write
                        )
                    ########### Insert in HouseKeeping Models

                # if response and "is_line_item_level" in response and response["is_line_item_level"]:
                existing_line_item_ids = pydash.get(
                    criteria_line_items_map, criteria.criteria_id
                )
                # Forecast doesn't have settlement rules. So, no need to sync settlement for forecast
                team_or_payee_sync = "team_sync" if is_team else "payee_sync"
                if (
                    not intermediate_only
                    and commission_type == COMMISSION_TYPE.COMMISSION
                    and not run_settlement_v3
                ):
                    handle_settlement_commission_sync(
                        client_id,
                        criteria.plan_id,
                        criteria.criteria_id,
                        payee_email,
                        period_start_date,
                        period_end_date,
                        timezone.now(),
                        commissions,
                        existing_line_item_ids,
                        criteria_data["is_line_item_level"],
                        log_context,
                        locked_kd=curr_locked,
                        e2e_sync_run_id=e2e_sync_run_id,
                        sync_run_id=sync_run_id,
                        level=level,
                        team_or_payee_sync=team_or_payee_sync,
                        s3_batch_id=s3_batch_id,
                    )
                # if params and not what_if:
                #     e2e_sync_run_id = pydash.get(params, "e2e_sync_run_id")
                #     sync_run_id = pydash.get(params, "sync_run_id")
                #     csa = CommissionETLStatusAccessor(
                #         client_id, e2e_sync_run_id, sync_run_id
                #     )
                #     csa.insert_bulk_objs(house_keeping_objs)

                log.info(
                    f"END: CRITERIA Id: {criteria.criteria_id}, Name: {criteria.criteria_name}"
                )

            log.update_context({"criteria_id": None, "criteria_name": None})
            log.info(f"END: Execution for priority-{pri} criterias")
        log.update_context({"priority": None})
        if what_if:
            return whatif_results
        if not what_if:
            # delete cached commission data for payee
            if commission_type == COMMISSION_TYPE.COMMISSION:
                log.debug("Deleting Commission Cache for payee")
                delete_commission_cache(
                    client_id=client_id,
                    psd=period_start_date,
                    ped=period_end_date,
                    email_id=payee_email,
                )
                log.debug("Deleting Crystal Cache for payee")
                delete_crystal_cache(
                    client_id=client_id,
                    payee_email=payee_email,
                    psd=period_start_date,
                    ped=period_end_date,
                )
            # update sec_kd in L1 criterias
            if level == CommCalcLevels.L2.value:
                l1_criteria_ids = pca.get_criterias_ids_for_a_plan_by_level(
                    plan_ids, CommCalcLevels.L1.value
                )
                log.info(f"Updating Secondary KD for L1 criterias - {l1_criteria_ids}")
                CommissionAccessorFactory(
                    client_id, commission_type
                ).get_accessor().update_l1_sec_kd(
                    payee_email,
                    period_start_date,
                    period_end_date,
                    l1_criteria_ids,
                    secondary_knowledge_date,
                )
                QuotaErosionAccessorFactory(
                    client_id, commission_type
                ).get_accessor().update_l1_sec_kd(
                    payee_email,
                    period_start_date,
                    period_end_date,
                    l1_criteria_ids,
                    secondary_knowledge_date,
                )

        if params and pydash.get(params, "team_task") and not curr_locked:
            log.info("Adding new KD into Secondary KD table")
            CommissionSecondaryKdAccessorFactory(
                client_id, commission_type
            ).get_accessor().update_sec_kd_for_period_payees(
                period_start_date,
                period_end_date,
                payee_email,
                secondary_knowledge_date,
            )
        return STATUS_CODE.SUCCESS
    except Exception as e:
        log.error(
            "\n [COMMISSION ETL] Exception for payee =>{0} , {1}".format(payee_email, e)
        )
        log.error("[COMMISSION ETL] Traceback - {0}".format(traceback.print_exc()))
        raise e


def get_snowflake_commission_or_quota_object(
    client_id,
    data: dict,
    line_id: str | None,
    other_reference_data: dict,
    context_data,
    tier_name,
    is_commission,
    team_owner_detail=None,
    payee_detail_for_snowflake_reports=None,
) -> list:
    context_ids = data["context_ids"]
    data.pop("context_ids")
    ### Snowflake write part - split data for line item and context
    line_item_ids = []
    if len(context_ids) == 0 and line_id is not None:
        line_item_ids.append(line_id)
    elif len(context_ids) == 0 and line_id is None:
        # empty record
        line_item_ids.append(None)
    else:
        line_item_ids.extend(context_ids)
    commission_data_for_line_item = []
    uuid_to_str = [
        "commission_snapshot_id",
        "commission_plan_id",
        "criteria_id",
        "secondary_snapshot_id",
    ]
    for key in line_item_ids:
        snowflake_data = data.copy()
        for uuid_key in uuid_to_str:
            if uuid_key in snowflake_data:
                snowflake_data[uuid_key] = str(snowflake_data[uuid_key])
        snowflake_data["line_item_id"] = key
        if is_commission:
            if key is None:
                data_to_process = (
                    team_owner_detail or payee_detail_for_snowflake_reports or {}
                )
            elif team_owner_detail:
                data_to_process = {**context_data[key], **team_owner_detail}
            else:
                data_to_process = context_data[key]
            (
                datasheet_data,
                payee_reference_data,
            ) = get_datasheet_data_and_reference_data_from_context_data_for_commission(
                data_to_process
            )
            reference_data = {
                **payee_reference_data,
                **other_reference_data,
            }
            if key is None:
                datasheet_data = {"dummy_key": 1}
            snowflake_data["datasheet_data"] = datasheet_data
            snowflake_data["additional_data"] = reference_data
            snowflake_data["additional_data"]["tier_name"] = tier_name
            if (
                "data_payout_frequency" in payee_reference_data
                and "data_pay_currency" in payee_reference_data
            ):
                snowflake_data["additional_data"]["period_label"] = add_period_label(
                    client_id,
                    other_reference_data["period_start_date"],
                    other_reference_data["period_end_date"],
                    payee_reference_data["data_payout_frequency"],
                    other_reference_data["fiscal_start"],
                )
                snowflake_data["additional_data"]["payout_frequency"] = (
                    add_payout_frequency(
                        client_id, payee_reference_data["data_payout_frequency"]
                    )
                )
                conversion_rate = add_conversion_rate(
                    client_id,
                    other_reference_data["period_end_date"],
                    data["secondary_kd"],
                    payee_reference_data["data_pay_currency"],
                )
                snowflake_data["additional_data"]["conversion_rate"] = round(
                    conversion_rate, 16
                )
                if "amount" in data:
                    commission_amount_payee_currency = (
                        add_commission_amount_payee_currency(
                            client_id, data["amount"], conversion_rate
                        )
                    )
                    snowflake_data["additional_data"][
                        "amount_payee_currency"
                    ] = commission_amount_payee_currency

                round_integer_keys_to_fixed_decimals(snowflake_data)

        commission_data_for_line_item.append(snowflake_data)
    return commission_data_for_line_item


def round_half_up(value, decimals=6):
    """
    This function rounds a value to a fixed number of
    decimal places using the round half up method.
    There were some issues with the round function in python
    where it was rounding to the nearest even number.
    822.1995625 when rounded to 6 decimal places was 822.199562
    instead of 822.199563.
    """
    return Decimal(str(value)).quantize(
        Decimal(f"0.{'0' * (decimals - 1)}1"), rounding=ROUND_HALF_UP
    )


def round_integer_keys_to_fixed_decimals(snowflake_data: dict):
    decimal_places = 6

    # if "amount" in snowflake_data:
    #     snowflake_data["amount"] = round_half_up(
    #         snowflake_data["amount"], decimal_places
    #     )

    additional_data = snowflake_data.get("additional_data", {})
    additional_data_integer_keys = [
        "actual_variable_pay",
        "amount_payee_currency",
        "data_variable_pay_as_per_period",
        "data_variable_pay_base_currency",
    ]
    for key in additional_data_integer_keys:
        if key in additional_data:
            additional_data[key] = round_half_up(additional_data[key], decimal_places)


def round_quota_erosion_keys(snowflake_quota_data: dict):
    decimal_places = 6

    quota_integer_keys = ["qv", "cumulative_qe", "quota_erosion"]
    for key in quota_integer_keys:
        if key in snowflake_quota_data:
            snowflake_quota_data[key] = round_half_up(
                snowflake_quota_data[key], decimal_places
            )


def write_frame_as_parquet_to_s3(data, s3_paths: list):
    df = pd.DataFrame(data)
    cols_to_str = ["tier_id", "original_tier_id"]
    # if col from cols_to_str is in df, convert it to string
    for col in cols_to_str:
        if col in df.columns:
            df[col] = df[col].astype(str)

    handle_write_with_retry(df, s3_paths)


def get_s3_paths_to_write_commission_quota_data(
    client_id,
    criteria_id,
    e2e_sync_run_id,
    sync_run_id,
    object_type,
    level,
    expose_inter_commission,
    team_or_payee_sync,
    intermediate_only,
    s3_batch_id=1,
) -> list:
    # Added s3_batch_id to the path for storing files per processed period in multi-period sync while maintaining regular sync support
    e2e_sync_run_id = str(e2e_sync_run_id).replace("-", "_")
    criteria_id = str(criteria_id).replace("-", "_")
    sync_run_id = str(sync_run_id).replace("-", "_")
    pvt_assets_bucket_name = os.environ.get("S3_PVT_ASSETS_BUCKET")
    paths_to_fetch = [object_type]
    if level == CommCalcLevels.L1.value and expose_inter_commission:
        if object_type == "commission":
            if intermediate_only:
                paths_to_fetch = ["intercommission"]
            else:
                paths_to_fetch = [
                    "commission",
                    "intercommission",
                ]
        elif object_type == "quotaerosion":
            if intermediate_only:
                paths_to_fetch = ["interquotaerosion"]
            else:
                paths_to_fetch = ["quotaerosion", "interquotaerosion"]

    s3_paths = []
    for object_type in paths_to_fetch:
        s3_paths.append(
            f"s3://{pvt_assets_bucket_name}/{object_type}/{client_id}/{e2e_sync_run_id}/{level}/{team_or_payee_sync}/{s3_batch_id}/{sync_run_id}__{criteria_id}.parquet"
        )
    return s3_paths


def get_datasheet_data_and_reference_data_from_context_data_for_commission(
    context_data,
):
    payee_level_keys = {
        "data_pay_currency",
        "data_payout_frequency",
        "actual_variable_pay",
        "data_full_name",
        "data_first_name",
        "data_last_name",
        "data_joining_date",
        "data_exit_date",
        "data_designation",
        "data_employee_id",
        "data_employment_country",
        "data_employment_country_name",
        "data_manager_email",
        "data_manager_name",
        "data_payee_or_manager",
        "data_variable_pay_as_per_period",
        "data_variable_pay_base_currency",
        "data_variable_pay_in_table",
        "data_variable_pay_by_month",
    }
    datasheet_data = {
        k: v for k, v in context_data.items() if k not in payee_level_keys
    }
    reference_data = {k: v for k, v in context_data.items() if k in payee_level_keys}
    return datasheet_data, reference_data


@lru_cache
def get_custom_calendar_map_lru(client_id):
    return get_custom_calendar_map(client_id)


@lru_cache(maxsize=1000)
def add_period_label(client_id, psd, ped, payout_freq, fiscal_start):
    if payout_freq == "invalid" or payout_freq is None:
        return None
    custom_calendar_map = get_custom_calendar_map_lru(client_id)
    if payout_freq in custom_calendar_map:
        return psd.strftime("%b %d, %Y") + " - " + ped.strftime("%b %d, %Y")

    payout_period = find_period(int(psd.month), fiscal_start, payout_freq)
    period_index = payout_period.get("index")
    period_label = get_period_label(period_index, payout_freq, fiscal_start)
    fiscal_year = get_fiscal_year(fiscal_start, psd)
    if payout_freq.lower() == Freq.MONTHLY.value:
        fiscal_year = psd.year
    return f"{period_label} {fiscal_year}"


@lru_cache(maxsize=1000)
def add_payout_frequency(client_id, payout_freq):
    custom_calendar_map = get_custom_calendar_map_lru(client_id)
    if payout_freq in custom_calendar_map:
        return custom_calendar_map[payout_freq]
    return payout_freq


@lru_cache(maxsize=1000)
def add_conversion_rate(client_id, period_end_date, secondary_kd, payee_currency):
    if payee_currency:
        return FXRate(client_id).get_fx_rate_kd(
            period_end_date, payee_currency, secondary_kd
        )
    return 1


@lru_cache(maxsize=1000)
def add_commission_amount_payee_currency(client_id, amount, fx_rate):
    return FXRate(client_id).change_to_payee_currency(amount, fx_rate)


def get_quota_period_and_schedule_details_for_quota(
    quota_name,
    period_end_date,
    fiscal_start,
    all_quota_details,
):
    from spm.services.quota_services import get_quota_period_to_dates_map

    quota_period = all_quota_details.get(quota_name, {}).get("quota_type")
    quota_schedule = all_quota_details.get(quota_name, {}).get("quota_schedule_type")
    quota_period_to_dates_map = get_quota_period_to_dates_map(
        period_end_date, fiscal_start
    )
    quota_period_dates = quota_period_to_dates_map.get(quota_period)
    quota_schedule_dates = quota_period_to_dates_map.get(quota_schedule)
    period_timeframe = (
        get_period_timeframe(
            fiscal_start,
            quota_period_dates[0],
            quota_period_dates[1],
            quota_period,
            quota_period_dates[2] + 1,
        )
        if quota_period_dates
        else None
    )
    quota_psd = quota_period_dates[0] if quota_period_dates else None
    quota_ped = quota_period_dates[1] if quota_period_dates else None
    quota_schedule_psd = quota_schedule_dates[0] if quota_schedule_dates else None
    quota_schedule_ped = quota_schedule_dates[1] if quota_schedule_dates else None
    return {
        "period_timeframe": period_timeframe,
        "quota_period": quota_period,
        "quota_psd": quota_psd,
        "quota_ped": quota_ped,
    }, {
        "quota_schedule": quota_schedule,
        "quota_schedule_psd": quota_schedule_psd,
        "quota_schedule_ped": quota_schedule_ped,
    }


def get_exclusion_periods_for_plans(
    plan_objects, period_start_date, period_end_date
) -> dict:
    """
    This function takes in a list of plan objects and returns a list of exclusion periods for each plan.
    Returns a dict with plan_id as key and exclusion period start and end dates as value.
    """
    plan_exclusion_period_map = {}
    for plan in plan_objects:
        plan_exclusion_start_date = (
            make_aware_wrapper(start_of_day(plan.plan_exclusion_start_date))
            if plan.plan_exclusion_start_date
            else None
        )
        plan_exclusion_end_date = (
            make_aware_wrapper(end_of_day(plan.plan_exclusion_end_date))
            if plan.plan_exclusion_end_date
            else None
        )

        # Check if exclusion period overlaps with the given period
        if (
            plan_exclusion_start_date
            and plan_exclusion_end_date
            and (plan_exclusion_end_date >= period_start_date)
            and (plan_exclusion_start_date <= period_end_date)
        ):
            # Adjust start and end dates to be within the given period
            start = max(plan_exclusion_start_date, period_start_date)
            end = min(plan_exclusion_end_date, period_end_date)
            plan_exclusion_period_map[str(plan.plan_id)] = {
                "start_date": start,
                "end_date": end,
            }

    return plan_exclusion_period_map
