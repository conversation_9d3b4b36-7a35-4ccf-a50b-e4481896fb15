"""
This module contains wrapper function to evaluate commissions based
on vectorized evaluation mode
"""

import copy
import math
from decimal import Decimal
from enum import Enum

import numpy as np
import pandas as pd
from vectorized_evaluate import vectorized_evaluate_team

from commission_engine.accessors.client_accessor import (
    can_use_custom_metrics,
    get_evaluation_mode,
)
from commission_engine.services.client_feature_service import has_feature
from commission_engine.services.commission_calculation_service.data_generator import (
    remove_nan_nat,
)
from commission_engine.services.commission_calculation_service.evaluate import (
    functions as evalaute_functions,
)
from commission_engine.services.commission_calculation_service.evaluate import (
    functions_with_commission_type_param,
)
from commission_engine.utils.general_data import COMMISSION_TYPE
from commission_engine.utils.general_utils import log_time_taken
from everstage_ddd.custom_metrics import (
    does_plan_has_custom_metrics,
    get_all_custom_metric_values_for_payee_plan_period,
)
from snowflake_udf_modules.hierarchy.udf_code.data_structure import (
    HierarchyDataStructure,
)
from spm.accessors.variable_accessor import OperatorAccessor, VariableDataTypeAccessor
from spm.services.custom_calendar_services import get_all_periods


class EvaluationLevel(Enum):
    """
    to evaluate in line item level or all item level
    """

    LINE_ITEM_LEVEL = "line_item_level"
    ALL_ITEM_LEVEL = "all_item_level"


class EvaluationMode(Enum):
    """
    different evaluation modes
    """

    SERIAL = "serial"
    VECTORIZED = "vectorize"


QUOTA_FUNCTIONS = (
    "QuotaAttainment",
    "QuotaErosion",
    "Quota",
    "TEAM-QuotaAttainment",
    "TEAM-QuotaErosion",
    "TEAM-Quota",
)


INVALID_VALUES_DECIMAL = [float("-inf"), None, float("inf")]

EMPTY_VALUES_SET = [
    None,
    np.nan,
    pd.NaT,
    math.nan,
    float("inf"),
    "nan",
    "NaN",
]


def get_client_evaluation_mode_with_override(
    client_id: int, evaluation_mode_override: str | None = None
):
    """
    gets the evaluation mode from db for the client
    if a valid override value is provided, returns the overriden value,
    else returns the value from the db
    """
    evaluation_mode = get_evaluation_mode(client_id=client_id)
    if evaluation_mode_override is not None and evaluation_mode_override in [
        e.value for e in EvaluationMode
    ]:
        evaluation_mode = evaluation_mode_override

    return evaluation_mode


def convert_series_decimal(col: pd.Series) -> pd.Series:
    """convert the numbers in given pandas series to decimal"""
    if len(col) > 0:
        dataframe = pd.DataFrame()
        dataframe["col"] = col
        # split into integer and decimal part
        dataframe["decimal_split"] = dataframe["col"].astype(str).str.split(".")
        # get length of the decimal part
        dataframe["decimal_len"] = dataframe["decimal_split"].apply(
            lambda x: len(x[1]) if len(x) > 1 else 0
        )
        # convert to decimal and round to decimal_len number of places
        dataframe["rounded_decimal"] = dataframe.apply(
            lambda x: (
                round(Decimal(x["col"]), x["decimal_len"])
                if x["col"] not in INVALID_VALUES_DECIMAL
                and isinstance(x["col"], (int, float))
                else x["col"]
            ),
            axis=1,
        )
        return dataframe["rounded_decimal"]

    return col


def cleanup_series_result(col: pd.Series) -> pd.Series:
    """
    post processing on the result series
    1. replace empty values with 0
    2. convert to series
    """

    col = col.replace(EMPTY_VALUES_SET, 0)
    return convert_series_decimal(col)


def get_ast_data_type(ast: dict) -> str:
    """
    get the data type of the AST. This is to determine if an AST
    is of type integer, so the result of evaluation could be converted to decimal
    """
    if "type" in ast and ast["type"] == "VARIABLE":
        return ast.get("data_type", "string").lower()

    operator = list(ast.keys())[0]
    operator_type = OperatorAccessor().get_operator_output_type_by_name(
        operator_name=operator
    )
    if len(operator_type) > 0:
        operator_type_id = operator_type[0]["output_type_ids"][0]
        data_type = VariableDataTypeAccessor().get_data_type_by_id(operator_type_id)
        return data_type.get("data_type", "String")

    return "String"


def is_numeric_expression(ast: dict) -> bool:
    """
    returns true if the given ast evaluates to a numeric expression
    """
    output_dtype = get_ast_data_type(ast).lower()
    return output_dtype in ["integer", "percentage"]


def get_all_quota_categories_used_in_ast(
    ast, data, commission_type, evaluated_quota_details: dict
):
    """
    get the list of quota category names and loopback period
    in the crieria ast
    """
    if isinstance(ast, dict) and "type" in ast and ast["type"] == "VARIABLE":
        if "function_name" in ast:
            function = ast["function_name"]
            if function in QUOTA_FUNCTIONS:
                cache_key = (function, ast["args"][0]["name"], ast["args"][1])
                if cache_key not in evaluated_quota_details:
                    if function in functions_with_commission_type_param:
                        result = evalaute_functions[function](
                            *ast["args"], data, commission_type=commission_type
                        )
                    else:
                        result = evalaute_functions[function](*ast["args"], data)

                    evaluated_quota_details[cache_key] = result

            # quota functions are either standalone are present inside conditional functions
            if function in ("IF"):
                condition = ast["args"][0]
                then_clause = ast["args"][1]
                else_clause = ast["args"][2]
                get_all_quota_categories_used_in_ast(
                    ast=condition,
                    data=data,
                    commission_type=commission_type,
                    evaluated_quota_details=evaluated_quota_details,
                )
                get_all_quota_categories_used_in_ast(
                    ast=then_clause,
                    data=data,
                    commission_type=commission_type,
                    evaluated_quota_details=evaluated_quota_details,
                )
                get_all_quota_categories_used_in_ast(
                    ast=else_clause,
                    data=data,
                    commission_type=commission_type,
                    evaluated_quota_details=evaluated_quota_details,
                )
        return

    if ast is None or not isinstance(ast, dict):
        return

    operator = list(ast.keys())[0]
    values = ast[operator]
    if not isinstance(values, list) and not isinstance(values, tuple):
        values = [values]

    for val in values:
        get_all_quota_categories_used_in_ast(
            ast=val,
            data=data,
            commission_type=commission_type,
            evaluated_quota_details=evaluated_quota_details,
        )


@log_time_taken()
def evaluate_vectorized_wrapper(
    client_id: int,
    ast: dict,
    data: dict,
    additional_data: dict | None = None,
    ever_comparison: bool = False,
    evaluation_level: str = "per_line_item",
    extra_params: dict | None = None,
    commission_type=COMMISSION_TYPE.COMMISSION,
):
    """
    Wrapper function to evaluate critera by choosing using vectorized mode
    Args :-
        extra_params :- params required to evaluate criteria apart from data/ast (criteria date field, show_do_nothing etc).

        evaluation_level :- can either be per_line_item/all_line_item
        additional_data :- contains additional columns to be added to all line_items of data that's not already present in merged_data, (for ex. tier value
        and tier percentage)
        example :-
        {
            "rk1" : {
                "tiered_value" : value1,
                "tiered_percentage" : value3
            },
            "rk2" : {
                "tiered_value" : value2,
                "tiered_percentage" : value4
            }
        }
    Returns :- list of line item level commission with row_key, commission value and commission date
        {
            "result" : [
                {
                    "id" : line item id,
                    "commission" : commission amount,
                    "commission_date": date at which commission amount was gained,
                }
            ] # commission value for each line item. In case of evaluation_level="all_line_item", only one value is present
        }
    """
    from spm.services.commission_plan_services import get_plan_additional_config

    if not extra_params:
        extra_params = dict()

    if not additional_data:
        additional_data = dict()

    result = []  # evaluated result
    line_item_key = extra_params.get(
        "key", "row_key"
    )  # key to uniquely identify line items
    commission_date_field = extra_params.get(  # commission date field from criteria
        "commission_date_key", "date_field"
    )

    payee_email = data["payee_email"]

    # if number of rows to evaluate criteria for is 0 (for both line_item level and all_item level)
    # return empty result
    if not data.get("merged_data", None):
        return result

    # extra params to be passed to vectorized evaluate function
    # contains all data apart from "merged_data" and team "merged_data"``
    additional_params = dict()
    additional_params["period_end_date"] = data["period_end_date"]
    additional_params["period_start_date"] = data["period_start_date"]
    additional_params["client_id"] = data["client_id"]
    additional_params["start_month"] = data["start_month"]
    additional_params["plan_id"] = data["plan_id"]
    additional_params["plan_additional_config"] = get_plan_additional_config(
        client_id=client_id, plan_id=data["plan_id"], commission_type=commission_type
    )  # commmission plan's additional config data
    additional_params["payee_email"] = payee_email
    additional_params["payee"] = data["context"]["payee"]
    if len(data["context"]["payee"]) > 0:
        additional_params["payout_frequency"] = data["context"]["payee"][0][
            "data_payout_frequency"
        ]  # there's atleast one payee in context['payee']
    if "schedule" in data:
        additional_params["schedule"] = data["schedule"]
    additional_params["user_properties"] = extra_params["user_properties"]
    additional_params["sec_knowledge_date"] = extra_params["sec_knowledge_date"]
    # add what if params if present
    if "what_if" in data:
        additional_params["what_if"] = data["what_if"]
        additional_params["what_if_params"] = data["what_if_params"]

    # add custom metrics if present
    if can_use_custom_metrics(client_id) and does_plan_has_custom_metrics(
        client_id, data["plan_id"]
    ):
        custom_metrics = get_all_custom_metric_values_for_payee_plan_period(
            client_id,
            payee_email,
            data["period_start_date"],
            data["period_end_date"],
            data["plan_id"],
        )
        additional_data["custom_metrics"] = custom_metrics

    # add additional data like tier_data, tier_value to merged_data
    if additional_data and len(additional_data) > 0:
        evaluate_data = copy.deepcopy(data)
        for row in evaluate_data["merged_data"]:
            if row[line_item_key] in additional_data:
                for key, value in additional_data[row[line_item_key]].items():
                    row[key] = value
            if "custom_metrics" in additional_data:
                for key, value in additional_data["custom_metrics"].items():
                    row[key] = value
    else:
        evaluate_data = data

    # for all item level evaluation, tier data will be present in data
    if "tiered_data" in data:
        additional_params["tiered_data"] = data["tiered_data"]
    if "tiered_percentage" in data:
        additional_params["tiered_percentage"] = data["tiered_percentage"]

    # feature flag for enabling rounding in tier functions
    additional_params["enable_rounding_in_tier_functions"] = has_feature(
        client_id, "enable_rounding_in_tier_functions"
    )

    payee_df = pd.DataFrame(evaluate_data["merged_data"])
    team_df = pd.DataFrame()

    additional_params["is_team"] = False
    if evaluate_data["team_params"] is not None:
        additional_params["is_team"] = True
        additional_params["team_params_team_name"] = evaluate_data["team_params"].get(
            "team_name", None
        )
        additional_params["team_params_team_type"] = evaluate_data["team_params"].get(
            "team_type", None
        )
        additional_params["team_params_team_owner_email_id"] = evaluate_data[
            "team_params"
        ].get("team_owner_email_id", None)
        additional_params["team_params_team_members"] = evaluate_data[
            "team_params"
        ].get("team_members", [])
        additional_params["team_params_effective_date_map"] = evaluate_data[
            "team_params"
        ].get("effective_date_map", None)
        additional_params["team_params_team_context"] = evaluate_data["team_params"][
            "team_context"
        ].get("context", {})
        if (
            "team_context" in evaluate_data["team_params"]
            and "merged_data" in evaluate_data["team_params"]["team_context"]
        ):
            team_df = pd.DataFrame(
                evaluate_data["team_params"]["team_context"]["merged_data"]
            )

    # other internal additional data
    # hierarchy ds delimiter
    hierarchy_function_delimiter = HierarchyDataStructure.delimiter
    additional_params["hierarchy_function_delimiter"] = hierarchy_function_delimiter

    # fetch all quota related values referenced in the ast
    quota_value_details = dict()
    get_all_quota_categories_used_in_ast(
        ast=ast,
        data=data,
        commission_type=commission_type,
        evaluated_quota_details=quota_value_details,
    )
    additional_params["evaluated_quota_values"] = quota_value_details

    # custom periods prefetch
    client_custom_periods = get_all_periods(client_id=client_id)
    additional_params["custom_periods"] = client_custom_periods

    # setting ever comparison
    additional_params["ever_comparison"] = ever_comparison

    if can_use_custom_metrics(client_id) and does_plan_has_custom_metrics(
        client_id, data["plan_id"]
    ):
        additional_params["custom_metrics"] = (
            get_all_custom_metric_values_for_payee_plan_period(
                client_id,
                payee_email,
                data["period_start_date"],
                data["period_end_date"],
                data["plan_id"],
            )
        )

    # remove nan_nat (replace them with None) in both the input dfs
    payee_df = remove_nan_nat(payee_df)
    team_df = remove_nan_nat(team_df)

    if evaluation_level == EvaluationLevel.LINE_ITEM_LEVEL.value:
        series_result = vectorized_evaluate_team(
            ast=ast,
            payee_df=payee_df,
            team_df=team_df,
            extra_params=additional_params,
        )
        if len(series_result) > 0:
            if is_numeric_expression(ast):
                series_result = cleanup_series_result(series_result)

            payee_df["commission"] = (
                series_result  # index of payee_df and series_result must be same
            )
            payee_df["id"] = payee_df[line_item_key]
            if commission_date_field in payee_df.columns:
                payee_df["commission_date"] = payee_df[commission_date_field]
            else:
                payee_df["commission_date"] = pd.Series(pd.NaT, index=payee_df.index)

            # select only id, commission and commission date columns to return in the result
            result_df = payee_df[["id", "commission", "commission_date"]]
            result = result_df.to_dict("records")

    elif evaluation_level == EvaluationLevel.ALL_ITEM_LEVEL.value:
        series_result = vectorized_evaluate_team(
            ast=ast,
            payee_df=payee_df,
            team_df=team_df,
            extra_params=additional_params,
        )
        if len(series_result) > 0:
            if is_numeric_expression(ast):
                series_result = cleanup_series_result(series_result)
            # series_result has the dimension of payee_df but all equal values, hence get first value alone
            if series_result is not None and len(series_result) > 0:
                evaluated_result = series_result[0]
                team_criteria_result = {}
                team_criteria_result["id"] = None
                team_criteria_result["commission"] = evaluated_result
                team_criteria_result["commission_date"] = extra_params.get(
                    "period_end_date"
                )
                result.append(team_criteria_result)

    return result
