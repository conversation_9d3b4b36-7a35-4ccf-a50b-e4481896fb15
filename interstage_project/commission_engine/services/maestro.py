"""
1) Custom Object ETL
2) System Object Report ETL (<PERSON>r, Quota etc)
3) CS Datasheet ETL (Soruce as - Custom Object, System Object)
4) Plan changes ETL
5) L1 Commission ETL
6) L1 Commission Report ETL (InterCommisison,InterQuotaAttainment)
7) L1 Datasheet  ETL (Source as - InterCommisison,InterQuotaAttainment)
8) L2 Commission ETL
9) Settlement ETL
10) Payout Snapshot ETL , Settlement Snapshot ETL
11) L2 Commission Report ETL (Commisison,QuotaAttainment,Settlement)
12) L2 Datasheet ETL (Soruce as - Commisison, QuotaAttainment, Settlement)


Note:
1. should pass same e2e_sync_run_id for all the tasks in a stage
2. should pass same secondary_kd for all the tasks in a stage



How to invoke this script:

1. enter into machine and set python path.
export DJANGO_SETTINGS_MODULE="interstage_project.settings"
export PYTHONPATH=.
2. enter into python django shell - python manage.py shell_plus
3. call the function with params.
eg:

from scripts.maestro import run_sync_stage
from uuid import uuid4
from datetime import datetime
from commission_engine.utils.date_utils import make_aware_wrapper
params = {
    "client_id": 2238,
    "e2e_sync_run_id": str(uuid4()),
    "run_previous_period_sync": False,
    "sync_date": "02/10/2023",
    "secondary_kd" : make_aware_wrapper(datetime.now())
}

run_sync_stage("e2e_prework", params)
run_sync_stage("plan_modification", params)
run_sync_stage("l1_payee_sync", params)
run_sync_stage("l1_team_sync", params)
run_sync_stage("l1_payout_snapshot", params)
run_sync_stage("l1_report_sync", params)
run_sync_stage("l1_databook_sync", params)
run_sync_stage("l2_payee_sync", params)
run_sync_stage("l2_team_sync", params)
run_sync_stage("l2_payout_snapshot_sync", params)
run_sync_stage("settlement_sync", params)
run_sync_stage("settlement_snapshot_sync", params)
run_sync_stage("l2_report_sync", params)
run_sync_stage("l2_databook_etl", params)
run_sync_stage("e2e_postwork", params)

"""

import logging
import time
import traceback
from collections import defaultdict
from datetime import datetime, timezone
from typing import Any, Dict
from uuid import UUID

from celery import chain, group
from dateutil.relativedelta import relativedelta
from django.core.cache import cache

from commission_engine.accessors.client_accessor import (
    can_run_payout_snapshot_etl,
    can_run_settlement_snapshot_etl,
    can_run_sf_payout_snapshot,
    get_client_fiscal_start_month,
    get_client_notification,
    get_client_subscription_plan,
    get_settlement_v3_execution_mode,
)
from commission_engine.accessors.etl_housekeeping_accessor import (
    CommissionETLStatusReaderAccessor,
    ETLSyncStatusReaderAccessor,
)
from commission_engine.services import etl_sync_status_service
from commission_engine.services.client_feature_service import has_feature
from commission_engine.services.etl_tasks_service import (
    ETLSync,
    add_sync_period_to_params,
    dry_run_settlement_v3_sync,
)
from commission_engine.utils import (
    ETL_ACTIVITY,
    ETL_STATUS,
    end_of_day,
    last_day_of_month,
    make_aware,
    make_aware_wrapper,
    parse,
)
from commission_engine.utils.general_data import (
    COMMISSION_TYPE,
    SYNC_OBJECT,
    CommCalcLevels,
)
from commission_engine.utils.report_utils import get_report_object_ids
from everstage_ddd.settlement_v3.common import ExecutionMode, TaskPeriod
from everstage_ddd.settlement_v3.task_engine.settlement_sync_plan_builder import (
    SettlementSyncPlanBuilder,
)
from everstage_etl.tasks import (
    commission_wrapper_task,
    inter_comm_snapshot_sync_task,
    mark_stage_as_completed,
    mark_stage_as_started,
    plan_modifications_sync,
    team_criteria_wrapper,
)
from everstage_etl.tasks.commission_payee_sync import (
    get_cache_key_for_commission_payee_sync,
)
from everstage_etl.tasks.payout_status_wrapper import get_update_payout_tasks
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import get_queue_name_respect_to_task_group
from spm.accessors.config_accessors.employee_accessor import EmployeePayrollAccessor
from spm.accessors.custom_calendar_accessors import CustomPeriodsAccessor
from spm.services.commission_actions_service.commission_slack_services import (
    notify_attainment,
)
from spm.services.config_services.employee_services import get_valid_employees_for_sync

logger = logging.getLogger(__name__)

# Map of tasks to cache key generators
TASK_CACHE_KEY_GENERATORS = {
    SYNC_OBJECT.DATABOOK_INTER_OBJECT_SYNC.value: lambda client_id, e2e_sync_run_id: f"{client_id}_{e2e_sync_run_id}_{SYNC_OBJECT.INTER_OBJECT_WRAPPER_SYNC.value}",
    SYNC_OBJECT.COMMISSION_PAYEE_SYNC.value: lambda client_id, e2e_sync_run_id: get_cache_key_for_commission_payee_sync(
        client_id, e2e_sync_run_id, CommCalcLevels.L2.value, COMMISSION_TYPE.COMMISSION
    ),
}


def get_payees_list_for_manual_commission_sync(client_id, curr_date, batch_size=50):
    curr_date = datetime.strptime(curr_date, "%d/%m/%Y")
    curr_date = make_aware_wrapper(curr_date)
    if has_feature(client_id, "custom_calendar"):
        curr_date = make_aware_wrapper(end_of_day(curr_date))
    else:
        curr_date = make_aware_wrapper(last_day_of_month(curr_date))
    emps = get_valid_employees_for_sync(client_id, curr_date)
    employees = [emp.employee_email_id for emp in emps]
    employees.sort()
    batched_employees = [
        employees[i : i + batch_size] for i in range(0, len(employees), batch_size)
    ]
    print("Total batches: ", len(batched_employees))
    print("Total employees: ", len(employees))
    return batched_employees


def run_e2e_prework(client_id, params, stage_key):
    """
        params = {
        "payee_list": ["<EMAIL>"],
        "refresh_databook": False,
        "run_previous_period_sync": False,
        "req_time": "01/10/2024",
    }
    """
    e2e_sync_run_id = params["e2e_sync_run_id"]
    logger.info(
        f"Running e2e_prework for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    curr_date = params["curr_date"]
    # Todo: extract all databook_ids for selected payees for calculate commission
    try:
        etl_status_params = {"databook_ids": None, "refresh_databooks": False}
        activity = (
            ETL_ACTIVITY.REFRESH_DATABOOK.value
            if params.get("run_databook_sync_only")
            else ETL_ACTIVITY.COMMISSION_CALCULATION.value
        )
        etl_status_params = add_sync_period_to_params(
            client_id, etl_status_params, curr_date
        )
        etl_sync_status_service.insert_etl_sync_status(
            client_id,
            e2e_sync_run_id,
            activity,
            ETL_STATUS.STARTED.value,
            make_aware(datetime.now()),
            {},
            etl_status_params,
        )
        mark_stage_as_completed(stage_key)
        logger.info(
            f"Completed e2e_prework for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
        )
    except Exception:
        logger.exception("Error in e2e_prework")
    return {"e2e_sync_run_id": e2e_sync_run_id}


def run_plan_modification_sync(client_id, params, stage_key):
    e2e_sync_run_id = params["e2e_sync_run_id"]
    logger.info(
        f"Running plan_modification_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    plan_modifications_sync(client_id, e2e_sync_run_id)
    mark_stage_as_completed(stage_key)
    logger.info(
        f"Completed plan_modification_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def run_payee_sync(client_id, e2e_sync_run_id, level, params, stage_key):
    logger.info(
        f"Running payee_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    curr_date = params["curr_date"]
    secondary_kd = make_aware(datetime.now())
    run_previous_period_sync = params.get("run_previous_period_sync", False)
    payee_list = params.get("payee_list", None)
    tasks, _, _ = commission_wrapper_task(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        secondary_kd=secondary_kd,
        level=level,
        curr_date=curr_date,
        payee_list=payee_list,
        notification_email_id=None,
        log_context={},
        is_after_db_refresh=False,
        commission_type=COMMISSION_TYPE.COMMISSION,
        run_previous_period_sync=run_previous_period_sync,
        s3_batch_id=params.get("s3_batch_id", 1),
    )
    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.COMMISSION.value
    )
    tasks.append(mark_stage_as_completed.si(stage_key).set(queue=queue_name))
    if run_previous_period_sync:
        # from commission wrapper task
        # task[0] is prev period sync task
        # task[1] is avalanche task
        # task[2:] are current period commission wrapper tasks, followed load and completion tasks
        stage_key_prev = f"{stage_key}_prev"
        mark_stage_as_started(stage_key_prev)
        prev_period_task = [
            tasks[0],
            mark_stage_as_completed.si(stage_key_prev).set(queue=queue_name),
        ]
        chain(*prev_period_task).apply_async(compression="lzma", serializer="pickle")
        check_stage_completion(stage_key_prev, 1800)
        chain(*tasks[2:]).apply_async(compression="lzma", serializer="pickle")
    else:
        chain(*tasks).apply_async(compression="lzma", serializer="pickle")
    logger.info(
        f"Triggered payee_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def run_team_sync(client_id, e2e_sync_run_id, level, params, stage_key):
    logger.info(
        f"Running team_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    curr_date = params["curr_date"]
    secondary_kd = make_aware(datetime.now())
    run_previous_period_sync = params.get("run_previous_period_sync", False)
    payee_list = params.get("payee_list", None)
    tasks = team_criteria_wrapper(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        secondary_kd=secondary_kd,
        level=level,
        payee_list=payee_list,
        curr_date=curr_date,
        notification_email_id=None,
        log_context={},
        is_after_db_refresh=False,
        run_previous_period_sync=run_previous_period_sync,
        s3_batch_id=params.get("s3_batch_id", 1),
    )
    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.COMMISSION.value
    )
    tasks.append(mark_stage_as_completed.si(stage_key).set(queue=queue_name))
    if run_previous_period_sync:
        stage_key_prev = f"{stage_key}_prev"
        mark_stage_as_started(stage_key_prev)
        prev_period_task = [
            tasks[0],
            mark_stage_as_completed.si(stage_key_prev).set(queue=queue_name),
        ]
        chain(*prev_period_task).apply_async(compression="lzma", serializer="pickle")
        check_stage_completion(stage_key_prev, 1800)
        chain(*tasks[2:]).apply_async(compression="lzma", serializer="pickle")
    else:
        chain(*tasks).apply_async(compression="lzma", serializer="pickle")
    logger.info(
        f"Triggered team_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def run_l1_payee_sync(client_id, params, stage_key):
    """
    Query the table commission_etl_staus with client_id and e2e_sync_run_id
    and wait for all the tasks including wrapper tasks to complete before proceeding to next task
    """
    e2e_sync_run_id = params["e2e_sync_run_id"]
    logger.info(
        f"Trigering l1_payee_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    return run_payee_sync(client_id, e2e_sync_run_id, "L1", params, stage_key)


def run_l1_team_sync(client_id, params, stage_key):
    """
    Query the table commission_etl_staus with client_id and e2e_sync_run_id
    and wait for all the tasks including wrapper tasks to complete before proceeding to next task
    """
    e2e_sync_run_id = params["e2e_sync_run_id"]
    logger.info(
        f"Trigering l1_team_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    return run_team_sync(client_id, e2e_sync_run_id, "L1", params, stage_key)


def run_l2_payee_sync(client_id, params, stage_key):
    """
    Query the table commission_etl_staus with client_id and e2e_sync_run_id
    and wait for all the tasks including wrapper tasks to complete before proceeding to next task
    """
    e2e_sync_run_id = params["e2e_sync_run_id"]
    logger.info(
        f"Trigering l2_payee_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    return run_payee_sync(client_id, e2e_sync_run_id, "L2", params, stage_key)


def run_l2_team_sync(client_id, params, stage_key):
    """
    Query the table commission_etl_staus with client_id and e2e_sync_run_id
    and wait for all the tasks including wrapper tasks to complete before proceeding to next task
    """
    e2e_sync_run_id = params["e2e_sync_run_id"]
    logger.info(
        f"Trigering l2_team_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    return run_team_sync(client_id, e2e_sync_run_id, "L2", params, stage_key)


def run_notify_attainment_task(client_id, params, stage_key):
    if not get_client_notification(client_id):
        logger.info(
            "Notification not enabled for client_id: %s. Skipping notify attainment task",
            client_id,
        )
        mark_stage_as_completed(stage_key)
        return

    e2e_sync_run_id = params["e2e_sync_run_id"]
    records = list(
        CommissionETLStatusReaderAccessor(client_id).get_all_payee_periods_for_e2e(
            e2e_sync_run_id
        )
    )
    commission_period_dict = [
        {
            "period_start_date": record["period_start_date"],
            "period_end_date": record["period_end_date"],
            "payee_email_id": record["payee_email_id"],
        }
        for record in records
    ]

    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.MISC.value
    )

    grp_tasks = [
        notify_attainment.si(  # type: ignore
            client_id=client_id,
            payee_email=record["payee_email_id"],
            period_start_date=record["period_start_date"],
            period_end_date=record["period_end_date"],
            log_context={},
        ).set(queue=queue_name)
        for record in commission_period_dict
    ]

    chain_tasks = [group(grp_tasks)]

    chain_tasks.append(mark_stage_as_completed.si(stage_key).set(queue=queue_name))

    chain(*chain_tasks).apply_async(compression="lzma", serializer="pickle")
    logger.info(
        f"Triggered notify attainment task for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def run_l1_payout_snapshot_sync(client_id, params, stage_key):
    """
    Query the table snapshot_etl_status with client_id and e2e_sync_run_id
    and wait for the task to complete before proceeding to next task
    """
    e2e_sync_run_id = params["e2e_sync_run_id"]
    logger.info(
        f"Trigering l1_payout_snapshot_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    curr_date = params["curr_date"]
    secondary_kd = params["secondary_kd"]
    payee_list = params.get("payee_list", None)
    inter_comm_snapshot_sync_task(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        secondary_kd=secondary_kd,
        curr_date=curr_date,
        payee_list=payee_list,
        log_context={},
        only_curr_period=True,
    )
    mark_stage_as_completed(stage_key)
    logger.info(
        f"Completed l1_payout_snapshot_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def run_l1_report_sync(params, stage_key):
    """
    Next task can be triggered after completion status in UI
    """
    client_id = params["client_id"]
    e2e_sync_run_id = params["e2e_sync_run_id"]
    run_previous_period_sync = params.get("run_previous_period_sync", False)
    logger.info(
        f"Running l1_report_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    etl_obj = ETLSync(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        log_context={},
        notification_email_id=None,
        skip_archived_books=True,
        run_previous_period_sync=run_previous_period_sync,
    )

    etl_obj.chain_tasks.extend(
        etl_obj.get_report_etl_wrapper_tasks(
            report_objects=get_report_object_ids(data_origin=["inter_object"]),
            sync_type="inter_report",
        )
    )
    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.REPORT.value
    )
    etl_obj.chain_tasks.append(
        mark_stage_as_completed.si(stage_key).set(queue=queue_name)
    )
    chain(*etl_obj.chain_tasks).apply_async(compression="lzma", serializer="pickle")
    logger.info(
        f"Triggered l1_report_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def run_l1_databook_sync(params, stage_key):
    """
    Next task can be triggered after completion status in UI
    """
    client_id = params["client_id"]
    e2e_sync_run_id = params["e2e_sync_run_id"]
    run_previous_period_sync = params.get("run_previous_period_sync", False)
    etl_obj = ETLSync(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        log_context={},
        notification_email_id=None,
        skip_archived_books=True,
        run_previous_period_sync=run_previous_period_sync,
    )
    logger.info(
        f"Running l1_databook_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    etl_obj.chain_tasks.extend(
        etl_obj.get_databook_wrapper_tasks(
            sync_type=SYNC_OBJECT.DATABOOK_INTER_OBJECT_SYNC.value
        )
    )
    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.DATABOOK.value
    )
    etl_obj.chain_tasks.append(
        mark_stage_as_completed.si(stage_key).set(queue=queue_name)
    )
    chain(*etl_obj.chain_tasks).apply_async(compression="lzma", serializer="pickle")
    logger.info(
        f"Triggered l1_databook_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def run_l2_report_sync(client_id, params, stage_key):
    """
    Next task can be triggered after completion status in UI
    """
    e2e_sync_run_id = params["e2e_sync_run_id"]
    logger.info(
        f"Running l2_report_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    run_previous_period_sync = params.get("run_previous_period_sync", False)
    etl_sync_obj = ETLSync(
        client_id,
        e2e_sync_run_id,
        log_context={"e2e_sync_run_id": e2e_sync_run_id},
        notification_email_id=None,
        skip_archived_books=True,
        run_previous_period_sync=run_previous_period_sync,
    )
    etl_sync_obj.chain_tasks.extend(
        etl_sync_obj.get_report_etl_wrapper_tasks(
            report_objects=get_report_object_ids(data_origin=["commission_object"]),
            sync_type="commission_report",
        )
    )
    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.REPORT.value
    )
    etl_sync_obj.chain_tasks.append(
        mark_stage_as_completed.si(stage_key).set(queue=queue_name)
    )
    chain(*etl_sync_obj.chain_tasks).apply_async(
        compression="lzma", serializer="pickle"
    )
    logger.info(
        f"Triggered l2_report_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def run_l2_databook_sync(client_id, params, stage_key):
    """
    Next task can be triggered after completion status in UI
    """
    e2e_sync_run_id = params["e2e_sync_run_id"]
    run_previous_period_sync = params.get("run_previous_period_sync", False)
    logger.info(
        f"Running l2_databook_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    etl_sync_obj = ETLSync(
        client_id,
        e2e_sync_run_id,
        log_context={"e2e_sync_run_id": e2e_sync_run_id},
        notification_email_id=None,
        skip_archived_books=True,
        run_previous_period_sync=run_previous_period_sync,
    )
    etl_sync_obj.chain_tasks.extend(
        etl_sync_obj.get_databook_wrapper_tasks(
            sync_type=SYNC_OBJECT.DATABOOK_COMMISSION_OBJECT.value
        )
    )
    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.DATABOOK.value
    )
    etl_sync_obj.chain_tasks.append(
        mark_stage_as_completed.si(stage_key).set(queue=queue_name)
    )
    chain(*etl_sync_obj.chain_tasks).apply_async(
        compression="lzma", serializer="pickle"
    )
    logger.info(
        f"Triggered l2_databook_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def run_l2_payout_snapshot_sync(params, stage_key):
    """
    Next task can be triggered after completion status in UI
    """
    client_id = params["client_id"]
    e2e_sync_run_id = params["e2e_sync_run_id"]
    run_previous_period_sync = params.get("run_previous_period_sync", False)
    payee_list = params.get("payee_list", None)
    curr_date = params["curr_date"]
    logger.info(
        f"Running l2_payout_snapshot_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    etl_sync_obj = ETLSync(
        client_id,
        e2e_sync_run_id,
        log_context={"e2e_sync_run_id": e2e_sync_run_id},
        notification_email_id=None,
        skip_archived_books=True,
        run_previous_period_sync=run_previous_period_sync,
    )
    etl_sync_obj.get_snapshot_tasks(payee_list=payee_list, curr_date=curr_date)
    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.COMMISSION.value
    )
    etl_sync_obj.chain_tasks.append(
        mark_stage_as_completed.si(stage_key).set(queue=queue_name)
    )
    chain(*etl_sync_obj.chain_tasks).apply_async(
        compression="lzma", serializer="pickle"
    )
    logger.info(
        f"Triggered l2_payout_snapshot_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def run_settlement_sync(params, stage_key):
    """
    After completion of settlement tasks wait for update payout tasks to complete
    To check if the the update payout task is complete, do the following:

    1. get the periods for which commission is run and periods from the query below:

    select distinct period_end_date from settlement  where client_id = {client_id} and
    knowledge_begin_date > {sync_start_time} and is_deleted in (true, false)
    union
    select distinct period_end_date from settlement where client_id = {client_id} and
    knowledge_end_date > {sync_start_time} and is_deleted in (true, false)

    2. check for the log "END: update payout details for the period_end_date:" from all the periods
    periods for which commission is run and periods from the query above
    """

    settlement_v3_execution_mode = get_settlement_v3_execution_mode(params["client_id"])
    if settlement_v3_execution_mode == ExecutionMode.DRY_RUN:
        # runs parallel to settlement sync and uses different queue and different stage keys
        dry_run_settlement_v3_sync(params)
    elif settlement_v3_execution_mode == ExecutionMode.ACTUAL_RUN:
        # runs only settlement v3 sync, old settlement sync will be skipped
        run_settlement_v3_sync(params, stage_key, ExecutionMode.ACTUAL_RUN)
        return

    client_id = params["client_id"]
    e2e_sync_run_id = params["e2e_sync_run_id"]
    run_previous_period_sync = params.get("run_previous_period_sync", False)
    payee_list = params.get("payee_list", None)
    curr_date = params["curr_date"]
    logger.info(
        f"Running settlement_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    etl_sync_obj = ETLSync(
        client_id,
        e2e_sync_run_id,
        log_context={"e2e_sync_run_id": e2e_sync_run_id},
        notification_email_id=None,
        skip_archived_books=True,
        run_previous_period_sync=run_previous_period_sync,
    )
    etl_sync_obj.get_settlement_tasks(
        payee_list=payee_list, curr_date=curr_date, is_after_comm_sync=True
    )
    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.SETTLEMENT.value
    )
    etl_sync_obj.chain_tasks.append(
        mark_stage_as_completed.si(stage_key).set(queue=queue_name)
    )
    chain(*etl_sync_obj.chain_tasks).apply_async(
        compression="lzma", serializer="pickle"
    )
    logger.info(
        f"Triggered settlement_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def run_settlement_v3_sync(params, stage_key, execution_mode):
    client_id = params["client_id"]
    e2e_sync_run_id = params["e2e_sync_run_id"]
    run_previous_period_sync = params.get("run_previous_period_sync", False)
    payee_list = params.get("payee_list", None)
    curr_date = params["curr_date"]
    logger.info(
        f"Running settlement_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    subscription_plan = get_client_subscription_plan(client_id)
    task_group = (
        TaskGroupEnum.SETTLEMENT.value
        if execution_mode == ExecutionMode.ACTUAL_RUN
        else TaskGroupEnum.SETTLEMENT_V3_DRY_RUN.value
    )
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, task_group
    )
    task_period = (
        TaskPeriod.CURRENT_AND_PREVIOUS
        if run_previous_period_sync
        else TaskPeriod.CURRENT
    )
    settlement_sync_plan_builder = SettlementSyncPlanBuilder(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        curr_date=curr_date,
        payee_list=payee_list,
        task_period=task_period,
        execution_mode=execution_mode,
    )
    sync_plan = settlement_sync_plan_builder.get_settlement_task_plan()
    logger.info("Starting settlement v3 sync from maestro")
    for task_name, tasks in sync_plan:
        if not tasks:
            logger.info(
                f"No tasks to run for Settlement v3: {task_name} for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
            )
            continue
        logger.info(
            f"Running {task_name} for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
        )
        curr_stage_key = get_stage_key(client_id, e2e_sync_run_id, task_name)
        tasks.append(mark_stage_as_completed.si(curr_stage_key).set(queue=queue_name))
        chain(*tasks).apply_async(compression="lzma", serializer="pickle")
        check_stage_completion(curr_stage_key, 1800)

    mark_stage_as_completed(stage_key)


def run_payout_status_sync(params, stage_key):
    client_id = params["client_id"]
    e2e_sync_run_id = params["e2e_sync_run_id"]
    run_previous_period_sync = params.get("run_previous_period_sync", False)
    logger.info(
        f"Running payout status sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    etl_sync_obj = ETLSync(
        client_id,
        e2e_sync_run_id,
        log_context={"e2e_sync_run_id": e2e_sync_run_id},
        notification_email_id=None,
        skip_archived_books=True,
        run_previous_period_sync=run_previous_period_sync,
    )
    # Get the latest sync start time for commission calculation or e2e sync tasks which got completed
    etl_start_time = ETLSyncStatusReaderAccessor(
        client_id
    ).get_latest_successful_sync_start_time_for_activities(
        e2e_sync_run_id,
        [
            ETL_ACTIVITY.COMMISSION_CALCULATION.value,
            ETL_ACTIVITY.CONNECTOR_E2E_SYNC.value,
        ],
    )
    if etl_start_time:
        update_payouts_tasks = get_update_payout_tasks(
            client_id,
            e2e_sync_run_id,
            etl_start_time,
            etl_sync_obj.commission_queue_name,
            notification_email_id=None,
        )
        etl_sync_obj.chain_tasks.extend(update_payouts_tasks)
    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.SETTLEMENT.value
    )
    etl_sync_obj.chain_tasks.append(
        mark_stage_as_completed.si(stage_key).set(queue=queue_name)
    )
    chain(*etl_sync_obj.chain_tasks).apply_async(
        compression="lzma", serializer="pickle"
    )
    logger.info(
        f"Triggered payout status sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def run_settlement_snapshot_sync(params, stage_key):
    """
    Next task can be triggered after completion status in UI
    """
    client_id = params["client_id"]
    e2e_sync_run_id = params["e2e_sync_run_id"]
    run_previous_period_sync = params.get("run_previous_period_sync", False)
    payee_list = params.get("payee_list", None)
    curr_date = params["curr_date"]
    logger.info(
        f"Running settlement_snapshot_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    etl_sync_obj = ETLSync(
        client_id,
        e2e_sync_run_id,
        log_context={"e2e_sync_run_id": e2e_sync_run_id},
        notification_email_id=None,
        skip_archived_books=True,
        run_previous_period_sync=run_previous_period_sync,
    )
    etl_sync_obj.get_snapshot_tasks(
        payee_list=payee_list, curr_date=curr_date, snapshot_type="settlement"
    )
    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.SETTLEMENT.value
    )
    etl_sync_obj.chain_tasks.append(
        mark_stage_as_completed.si(stage_key).set(queue=queue_name)
    )
    chain(*etl_sync_obj.chain_tasks).apply_async(
        compression="lzma", serializer="pickle"
    )
    logger.info(
        f"Triggered settlement_snapshot_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def run_e2e_postwork(client_id, params, stage_key):
    """
    Last task to be triggered after all the tasks are complete
    """
    e2e_sync_run_id = params["e2e_sync_run_id"]
    logger.info(
        f"Running e2e_postwork for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )
    etl_sync_status_service.update_completion_time_and_send_email_notification(
        client_id,
        e2e_sync_run_id,
        email_id=params.get("notification_email_id", None),
    )
    mark_stage_as_completed(stage_key)
    logger.info(
        f"Triggered e2e_postwork for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
    )


def get_cache_keys(client_id: int, e2e_sync_run_id: UUID, tasks: list) -> list:
    cache_keys = []
    for task in tasks:
        if task in TASK_CACHE_KEY_GENERATORS:
            cache_key = TASK_CACHE_KEY_GENERATORS[task](client_id, e2e_sync_run_id)
            cache_keys.append(cache_key)
        else:
            logger.warning(f"Unknown task: {task}")
    return cache_keys


def run_invalidate_cache_for_tasks(client_id, e2e_sync_run_id, stage_key):
    """
    This function is used to invalidate the cache for specified tasks so that the next sync will be triggered with corresponding updated dates
    """
    list_of_tasks = [
        SYNC_OBJECT.DATABOOK_INTER_OBJECT_SYNC.value,
        SYNC_OBJECT.COMMISSION_PAYEE_SYNC.value,
    ]
    cache_keys = get_cache_keys(client_id, e2e_sync_run_id, list_of_tasks)
    cache.delete_many(cache_keys)
    mark_stage_as_completed(stage_key)
    logger.info(f"Cache invalidated for keys: {cache_keys}")


def get_stage_key(client_id, e2e_sync_run_id, stage_name):
    return f"{client_id}##{e2e_sync_run_id}##{stage_name}"


def generate_period_end_dates(fiscal_start_month, start_date, end_date, frequency):
    date_format = "%d/%m/%Y"
    start_date = datetime.strptime(start_date, date_format)
    end_date = datetime.strptime(end_date, date_format)

    def get_next_period_end_date(current_date):
        if frequency == "Monthly":
            return current_date + relativedelta(months=1) - relativedelta(days=1)
        elif frequency == "Quarterly":
            return current_date + relativedelta(months=3) - relativedelta(days=1)
        elif frequency == "Halfyearly":
            return current_date + relativedelta(months=6) - relativedelta(days=1)
        elif frequency == "Annual":
            return current_date + relativedelta(years=1) - relativedelta(days=1)
        else:
            raise ValueError(f"Invalid frequency: {frequency}")

    fiscal_year_start = datetime(start_date.year, fiscal_start_month, 1)

    if start_date < fiscal_year_start:
        fiscal_year_start -= relativedelta(years=1)

    period_end_dates = []
    while fiscal_year_start <= end_date:
        next_end_date = get_next_period_end_date(fiscal_year_start)
        if next_end_date >= start_date:
            period_end_dates.append(next_end_date.strftime(date_format))
        fiscal_year_start = next_end_date + relativedelta(days=1)

    return period_end_dates


def get_custom_period_end_date(params):
    date_format = "%d/%m/%Y"
    start_date = datetime.strptime(params["start_date"], date_format)
    start_date_utc = start_date.replace(tzinfo=timezone.utc)
    output_start_date = start_date_utc.strftime("%Y-%m-%d %H:%M:%S%z")

    end_date = datetime.strptime(params["end_date"], date_format)
    end_date_utc = end_date.replace(tzinfo=timezone.utc)
    output_end_date = end_date_utc.strftime("%Y-%m-%d %H:%M:%S%z")

    dates_map = CustomPeriodsAccessor(
        params["client_id"]
    ).get_all_custom_periods_between_range(
        params["calendar_id"], output_start_date, output_end_date
    )

    end_dates = []
    for end_date in dates_map:
        date = end_date["period_end_date"].strftime(date_format)
        end_dates.append(date)
    return end_dates


def run_commission_for_multiple_period_prework(params: Dict[str, Any]):
    """
    params = {
            "client_id": 10052,
            "e2e_sync_run_id": str(uuid4()),
            "run_previous_period_sync": False,
            "sync_date": period start date (5/11/2024),
            "sync_end_date": period end date (25/12/2024),
            "secondary_kd": secondary_kd,
            "payee_list": payee_list,
            "notification_email_id": notification_email_id,
        }
    """
    etl_sync_obj = ETLSync(
        params["client_id"],
        params["e2e_sync_run_id"],
        log_context={"e2e_sync_run_id": ["e2e_sync_run_id"]},
        notification_email_id=params["notification_email_id"],
        skip_archived_books=True,
        run_previous_period_sync=params["run_previous_period_sync"],
    )
    try:
        date_format = "%d/%m/%Y"
        sql_start_date = datetime.strptime(params["sync_date"], date_format).strftime(
            "%Y/%m/%d"
        )
        sql_end_date = datetime.strptime(params["sync_end_date"], date_format).strftime(
            "%Y/%m/%d"
        )
        pay_list = (
            tuple(params["payee_list"]) if params["payee_list"] is not None else ()
        )
        new_params = {
            "client_id": params["client_id"],
            "start_date": sql_start_date,
            "end_date": sql_end_date,
            "payee_list": pay_list,
        }

        payee_payout_freq_map = EmployeePayrollAccessor(
            params["client_id"]
        ).get_payee_payout_freq_map(new_params)

        payees_with_plan = list(
            {payee["employee_email_id"] for payee in payee_payout_freq_map}
        )
        if params["payee_list"]:
            all_payees = params["payee_list"]
        else:
            all_payees = EmployeePayrollAccessor(
                params["client_id"]
            ).get_all_employees()
            all_payees = [payee.employee_email_id for payee in all_payees]

        # generating list of payees without a plan b/w start and end date
        payees_without_plan = [
            payee for payee in all_payees if payee not in payees_with_plan
        ]

        # we are creating a dictionary where the key is payout_freq and value is set of email ids, min_payout_freq_start_date and max_payout_freq_end_date
        grouped_data = defaultdict(
            lambda: {"email_ids": set(), "start_date": None, "end_date": None}
        )
        for entry in payee_payout_freq_map:
            freq = entry["payout_frequency"]
            email_id = entry["employee_email_id"]
            start_date = entry["effective_start_date"]
            end_date = entry["effective_end_date"]

            grouped_data[freq]["email_ids"].add(email_id)

            if (
                grouped_data[freq]["start_date"] is None
                or start_date < grouped_data[freq]["start_date"]
            ):
                grouped_data[freq]["start_date"] = start_date

            if (
                grouped_data[freq]["end_date"] is None
                or end_date > grouped_data[freq]["end_date"]
            ):
                grouped_data[freq]["end_date"] = end_date

        # we are converting set of email_ids to list and dates to DD/MM/YYYY format
        results = [
            {
                "payout_frequency": freq,
                "email_ids": list(details["email_ids"]),  # Convert set to list
                "start_date": details["start_date"].strftime(date_format),
                "end_date": details["end_date"].strftime(date_format),
            }
            for freq, details in grouped_data.items()
        ]

        start_date = datetime.strptime(params["sync_date"], date_format)
        end_date = datetime.strptime(params["sync_end_date"], date_format)

        # here we are comparing payout_freq's start and end date with user's start and end date
        # we only want to run commission sync for a particular payout_freq from when they are active
        for result in results:
            freq_start_date = datetime.strptime(result["start_date"], date_format)
            freq_end_date = datetime.strptime(result["end_date"], date_format)

            freq_start_date = max(freq_start_date, start_date)
            freq_end_date = min(freq_end_date, end_date)

            result["start_date"] = freq_start_date.strftime(date_format)
            result["end_date"] = freq_end_date.strftime(date_format)

        final_map = {}

        for result in results:
            email_ids = result["email_ids"]
            start_date = result["start_date"]
            end_date = result["end_date"]
            freq = result["payout_frequency"]

            # generating end dates of periods with standarized payout freq
            if freq in ["Monthly", "Quarterly", "Halfyearly", "Annual"]:
                fiscal_start_month = get_client_fiscal_start_month(params["client_id"])
                end_dates = generate_period_end_dates(
                    fiscal_start_month, start_date, end_date, freq
                )
            # getting the end dates of period with custom payout freq
            else:
                custom_params = {
                    "client_id": params["client_id"],
                    "start_date": start_date,
                    "end_date": end_date,
                    "calendar_id": freq,
                }

                end_dates = get_custom_period_end_date(custom_params)

            for date in end_dates:
                if date in final_map:
                    final_map[date] = list(set(final_map[date] + email_ids))
                else:
                    final_map[date] = email_ids.copy()
        # Finally we are generating a dict with key as dates and value as list of payees who are in a plan at that date
        run_commission_for_multiple_period_postwork(
            params, final_map, payees_without_plan
        )

    except Exception as e:
        logger.exception("Error in run commission for multiple prework stage")
        ETLSync.handle_failure(etl_sync_obj)
        logger.error(e)


def run_commission_for_multiple_period_postwork(params, final_map, payees_without_plan):
    try:
        sorted_dict = {
            key: final_map[key]
            for key in sorted(final_map, key=lambda x: datetime.strptime(x, "%d/%m/%Y"))
        }
        logger.info(
            f"Payee periods processed during multi period commission sync: {sorted_dict}"
        )

        pre_commission_stage = [
            "e2e_prework",
            "system_report_sync",
            "system_custom_databook_sync",
            "plan_modification",
        ]
        commission_stage = [
            "l1_payee_sync",
            "l1_team_sync",
            "l1_payout_snapshot",
            "l1_report_sync",
            "l1_databook_sync",
            "l2_payee_sync",
            "l2_team_sync",
            "invalidate_cache_for_tasks",
            "notify_attainment",
            "l2_payout_snapshot_sync",
        ]
        post_commission_stage = [
            "settlement_sync",
            "payout_status_sync",
            "settlement_snapshot_sync",
            "l2_report_sync",
            "l2_databook_etl",
            "e2e_postwork",
        ]
        # running the pre-work sync stages once
        databook_refresh_params = params.get("databook_refresh_params", None)
        if databook_refresh_params is None:
            raise ValueError(
                "databook_refresh_params is required when is_run_databook_sync_only is True"
            )
        run_sync_stage(
            ",".join(pre_commission_stage),
            {
                "client_id": params["client_id"],
                "e2e_sync_run_id": params["e2e_sync_run_id"],
                "run_previous_period_sync": False,
                "sync_date": params["sync_date"],
                "secondary_kd": params["secondary_kd"],
                "payee_list": params["payee_list"],
                "notification_email_id": params["notification_email_id"],
                "system_report_objects": databook_refresh_params[
                    "system_report_objects"
                ],
                "databook_ids_to_refresh": databook_refresh_params[
                    "databook_ids_to_refresh"
                ],
            },
        )

        # Added batch id to process period wise records in snowflake
        s3_batch_id = 1
        for key, value in sorted_dict.items():
            client_id = params["client_id"]
            e2e_sync_run_id = params["e2e_sync_run_id"]
            period = key
            logger.info(
                f"Running commission sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id} for period: {period}"
            )
            # running the commission sync stages for every end date of periods between start and end date
            run_sync_stage(
                ",".join(commission_stage),
                {
                    "client_id": params["client_id"],
                    "e2e_sync_run_id": params["e2e_sync_run_id"],
                    "run_previous_period_sync": False,
                    "sync_date": key,
                    "secondary_kd": params["secondary_kd"],
                    "payee_list": value,
                    "notification_email_id": params["notification_email_id"],
                    "s3_batch_id": s3_batch_id,
                },
            )
            s3_batch_id += 1
        # running commission sync once for payees without a plan b/w start and end date
        if payees_without_plan:
            logger.info(
                f"Running commission sync for payees without a plan - {payees_without_plan}"
            )
            run_sync_stage(
                ",".join(commission_stage),
                {
                    "client_id": params["client_id"],
                    "e2e_sync_run_id": params["e2e_sync_run_id"],
                    "run_previous_period_sync": False,
                    "sync_date": params["sync_date"],
                    "secondary_kd": params["secondary_kd"],
                    "payee_list": payees_without_plan,
                    "notification_email_id": params["notification_email_id"],
                    "s3_batch_id": s3_batch_id,
                },
            )
        # running the post-work sync stages once
        run_sync_stage(
            ",".join(post_commission_stage),
            {
                "client_id": params["client_id"],
                "e2e_sync_run_id": params["e2e_sync_run_id"],
                "run_previous_period_sync": False,
                "sync_date": params["sync_date"],
                "secondary_kd": params["secondary_kd"],
                "payee_list": params["payee_list"],
                "notification_email_id": params["notification_email_id"],
            },
        )
    except Exception as e:
        logger.exception("Error in run commission for multiple postwork stage")
        print(traceback.format_exc())
        logger.error(e)


def run_sync_stage(stage_names, params):
    """
    Follow the instruction in each stage to trigger the next task
    """
    try:
        client_id = params["client_id"]
        e2e_sync_run_id = params["e2e_sync_run_id"]
        if isinstance(params["sync_date"], str):
            params["curr_date"] = make_aware(parse(params["sync_date"], dayfirst=True))
        else:
            params["curr_date"] = params["sync_date"]
        expose_commission_reports_in_plan = has_feature(
            client_id, "expose_comm_reports_in_plan"
        )
        if stage_names is None:
            stage_names = get_all_stage_names()
        else:
            stage_names = stage_names.split(",")

        for stage_name in stage_names:
            logger.info(
                f"Running stage: {stage_name} for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
            )
            stage_key = get_stage_key(client_id, e2e_sync_run_id, stage_name)
            mark_stage_as_started(stage_key)
            if stage_name == "e2e_prework":
                run_e2e_prework(client_id, params, stage_key)
            elif stage_name == "system_report_sync":
                run_system_report_sync(client_id, params, stage_key)
            elif stage_name == "system_custom_databook_sync":
                run_system_custom_databook_sync(client_id, params, stage_key)
            elif stage_name == "databook_sync_all_types":
                run_databook_sync_all_types(client_id, params, stage_key)
            elif stage_name == "plan_modification":
                run_plan_modification_sync(client_id, params, stage_key)
            elif stage_name == "l1_payee_sync":
                run_l1_payee_sync(client_id, params, stage_key)
            elif stage_name == "l1_team_sync":
                run_l1_team_sync(client_id, params, stage_key)
            elif stage_name == "l1_payout_snapshot":
                if expose_commission_reports_in_plan and can_run_sf_payout_snapshot(
                    client_id
                ):
                    run_l1_payout_snapshot_sync(client_id, params, stage_key)
                else:
                    logger.info(
                        f"Skipping l1_payout_snapshot_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
                    )
                    mark_stage_as_completed(stage_key)
            elif stage_name == "l1_report_sync":
                if expose_commission_reports_in_plan:
                    run_l1_report_sync(params, stage_key)
                else:
                    logger.info(
                        f"Skipping l1_report_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
                    )
                    mark_stage_as_completed(stage_key)
            elif stage_name == "l1_databook_sync":
                if expose_commission_reports_in_plan:
                    run_l1_databook_sync(params, stage_key)
                else:
                    logger.info(
                        f"Skipping l1_databook_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
                    )
                    mark_stage_as_completed(stage_key)
            elif stage_name == "l2_payee_sync":
                if expose_commission_reports_in_plan:
                    run_l2_payee_sync(client_id, params, stage_key)
                else:
                    logger.info(
                        f"Skipping l2_payee_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
                    )
                    mark_stage_as_completed(stage_key)
            elif stage_name == "l2_team_sync":
                if expose_commission_reports_in_plan:
                    run_l2_team_sync(client_id, params, stage_key)
                else:
                    logger.info(
                        f"Skipping l2_payout_snapshot_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
                    )
                    mark_stage_as_completed(stage_key)
            elif stage_name == "invalidate_cache_for_tasks":
                run_invalidate_cache_for_tasks(client_id, e2e_sync_run_id, stage_key)
            elif stage_name == "notify_attainment":
                run_notify_attainment_task(client_id, params, stage_key)
            elif stage_name == "l2_payout_snapshot_sync":
                if can_run_payout_snapshot_etl(client_id):
                    run_l2_payout_snapshot_sync(params, stage_key)
                else:
                    logger.info(
                        f"Skipping l2_payout_snapshot_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
                    )
                    mark_stage_as_completed(stage_key)
            elif stage_name == "settlement_sync":
                run_settlement_sync(params, stage_key)
            elif stage_name == "payout_status_sync":
                run_payout_status_sync(params, stage_key)
            elif stage_name == "settlement_snapshot_sync":
                if can_run_settlement_snapshot_etl(client_id):
                    run_settlement_snapshot_sync(params, stage_key)
                else:
                    logger.info(
                        f"Skipping settlement_snapshot_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
                    )
                    mark_stage_as_completed(stage_key)
            elif stage_name == "l2_report_sync":
                run_l2_report_sync(client_id, params, stage_key)
            elif stage_name == "l2_databook_etl":
                run_l2_databook_sync(client_id, params, stage_key)
            elif stage_name == "e2e_postwork":
                run_e2e_postwork(client_id, params, stage_key)
            check_stage_completion(stage_key, 1800)
            logger.info(
                f"Completed stage: {stage_name} for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
            )

    except Exception as e:
        logger.exception("Error in run stage")
        print(traceback.format_exc())
        logger.error(e)


def run_system_custom_databook_sync(client_id, params, stage_key) -> None:
    """
    Responsible for execution of system report object (user, quota) and custom object related
    datasheet syncs
    """
    client_id = params["client_id"]
    e2e_sync_run_id = params["e2e_sync_run_id"]
    run_previous_period_sync = params.get("run_previous_period_sync", False)
    databook_ids = params.get("databook_ids_to_refresh", None)
    etl_obj = ETLSync(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        log_context={},
        notification_email_id=None,
        skip_archived_books=True,
        run_previous_period_sync=run_previous_period_sync,
    )
    logger.info(
        f"MAESTRO: Running system_custom_databook_sync for client_id: {client_id} ,e2e_sync_run_id: {e2e_sync_run_id} and databook_ids: {databook_ids}"
    )
    etl_obj.chain_tasks.extend(
        etl_obj.get_databook_wrapper_tasks(
            sync_type=SYNC_OBJECT.DATABOOK_SYSTEM_CUSTOM_SYNC.value,
            databook_ids=databook_ids,
        )
    )
    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.DATABOOK.value
    )
    etl_obj.chain_tasks.append(
        mark_stage_as_completed.si(stage_key).set(queue=queue_name)
    )
    chain(*etl_obj.chain_tasks).apply_async(compression="lzma", serializer="pickle")
    logger.info(
        f"Triggered system_custom_databook_sync for client_id: {client_id}, e2e_sync_run_id: {e2e_sync_run_id} and databook_ids: {databook_ids}"
    )


def run_system_report_sync(client_id, params, stage_key) -> None:
    """
    Responsible for execution for User and Quota report objects
    """
    e2e_sync_run_id = params["e2e_sync_run_id"]
    system_report_objects = params.get("system_report_objects", [])
    is_maestro_daily_sync = params.get("is_maestro_daily_sync", False)
    logger.info(
        f"MAESTRO: Running system_report_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id} and is_maestro_daily_sync: {is_maestro_daily_sync}"
    )
    if is_maestro_daily_sync:
        system_report_objects = get_report_object_ids(data_origin=["system_object"])

    if system_report_objects:
        logger.info(
            f"MAESTRO: system_report_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id} and system_report_objects: {system_report_objects}"
        )
        run_previous_period_sync = params.get("run_previous_period_sync", False)
        etl_sync_obj = ETLSync(
            client_id,
            e2e_sync_run_id,
            log_context={"e2e_sync_run_id": e2e_sync_run_id},
            notification_email_id=None,
            skip_archived_books=True,
            run_previous_period_sync=run_previous_period_sync,
        )
        etl_sync_obj.chain_tasks.extend(
            etl_sync_obj.get_report_etl_wrapper_tasks(
                report_objects=system_report_objects,
                sync_type="system_report",
            )
        )
        subscription_plan = get_client_subscription_plan(client_id)
        queue_name = get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.REPORT.value
        )

        etl_sync_obj.chain_tasks.append(
            mark_stage_as_completed.si(stage_key).set(queue=queue_name)
        )
        chain(*etl_sync_obj.chain_tasks).apply_async(
            compression="lzma", serializer="pickle"
        )
        logger.info(
            f"Triggered system_report_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id}"
        )
    else:
        mark_stage_as_completed(stage_key)
        logger.info(
            f"MAESTRO:Skipping system_report_sync for client_id: {client_id} and e2e_sync_run_id: {e2e_sync_run_id} because the system report object list is empty"
        )


def run_databook_sync_all_types(client_id, params, stage_key) -> None:
    """
    Responsible for execution of all types of databook syncs
    """
    client_id = params["client_id"]
    e2e_sync_run_id = params["e2e_sync_run_id"]
    run_previous_period_sync = params.get("run_previous_period_sync", False)
    databook_ids = params.get("databook_ids_to_refresh", None)
    etl_obj = ETLSync(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        log_context={},
        notification_email_id=None,
        skip_archived_books=True,
        run_previous_period_sync=run_previous_period_sync,
    )
    logger.info(
        f"Running databook_sync_all_types for client_id: {client_id} ,e2e_sync_run_id: {e2e_sync_run_id} and databook_ids: {databook_ids}"
    )
    etl_obj.chain_tasks.extend(
        etl_obj.get_databook_wrapper_tasks(databook_ids=databook_ids)
    )
    subscription_plan = get_client_subscription_plan(client_id)
    queue_name = get_queue_name_respect_to_task_group(
        client_id, subscription_plan, TaskGroupEnum.DATABOOK.value
    )
    etl_obj.chain_tasks.append(
        mark_stage_as_completed.si(stage_key).set(queue=queue_name)
    )
    chain(*etl_obj.chain_tasks).apply_async(compression="lzma", serializer="pickle")
    logger.info(
        f"Triggered databook_sync_all_types for client_id: {client_id}, e2e_sync_run_id: {e2e_sync_run_id} and databook_ids: {databook_ids}"
    )


def get_all_stage_names():
    return [
        "e2e_prework",
        "system_report_sync",
        "system_custom_databook_sync",
        "plan_modification",
        "l1_payee_sync",
        "l1_team_sync",
        "l1_payout_snapshot",
        "l1_report_sync",
        "l1_databook_sync",
        "l2_payee_sync",
        "l2_team_sync",
        "notify_attainment",
        "l2_payout_snapshot_sync",
        "settlement_sync",
        "payout_status_sync",
        "settlement_snapshot_sync",
        "l2_report_sync",
        "l2_databook_etl",
        "e2e_postwork",
    ]


def get_stages(task="end_to_end"):
    """
    Returns the list of stages for the given task
    """
    if task == "end_to_end":
        return get_all_stage_names()
    elif task == "databook_sync":
        return ["system_report_sync", "databook_sync_all_types", "e2e_postwork"]
    else:
        return []


def check_stage_completion(stage_key, timeout=1800):
    count = 0
    while cache.get(stage_key) != "completed":
        time.sleep(10)
        count += 1
        if count > timeout:
            logger.info(
                f"Stage: {stage_key} is not completed, timed out after {timeout} seconds"
            )
            raise Exception(
                f"Stage: {stage_key} is not completed, timed out after {timeout} seconds"
            )
    return True
