# pylint: disable-msg=logging-fstring-interpolation
# pylint: disable=redefined-builtin
import logging
import os
import re
import traceback
from ast import literal_eval
from typing import Any, Dict, List, Optional

from celery import current_app as app
from django.db.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, F, Max, Q, Value
from django.db.models.functions import Cast, Coalesce
from django.utils import timezone
from django.utils.encoding import (  # https://docs.djangoproject.com/en/dev/releases/3.0/#features-deprecated-in-3-0
    force_str,
)

from commission_engine.models.client_models import (
    Client,
    ClientSettings,
    get_statement_file_path,
)
from commission_engine.utils.cloudfront_utils import invalidate_cloudfront_cache
from commission_engine.utils.report_enums import RunSettlementReport
from everstage_ddd.settlement_v3.common import ExecutionMode
from everstage_infra.aws_infra.ecs import is_prod_env
from interstage_project.utils import log_me
from spm.custom_types.notification_settings_types import notif_task_req_adapter
from spm.types import ClientMFAData

logger = logging.getLogger(__name__)
DEFAULT_APPROVAL_NOTIFICATIONS = {
    "email": {"enabled": False},
    "slack": {"enabled": False},
    "ms_teams": {"enabled": False},
}

ADMIN_NOTIFICATIONS = [
    "NEW_APPROVAL_REQUEST_NOTIFICATION",
    "EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION",
    "PLAN_UPDATED_NOTIFICATION",
    "REPORTING_MANAGER_UPDATED_NOTIFICATION",
    "TEAM_UPDATED_NOTIFICATION",
    "MONTHLY_COMMISSION_NOTIFICATION",
    "STATEMENTS_LOCKED_NOTIFICATION",
    "PAYOUT_INITIATED_NOTIFICATION",
    "COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD",
    "COMMISSION_REMINDER_NOTIFICATION",
]

CRYSTAL_CUSTOM_PERIODS_DEFAULT = 6


def get_client(id, fields=None):
    if not isinstance(fields, list):
        fields = []
    return Client.objects.only(*fields).get(client_id=id)


def get_clients_for_id_list(id_list: list[int], fields: list[str] = None) -> list[dict]:
    if not isinstance(fields, list):
        fields = [fields]
    if not isinstance(id_list, list):
        id_list = [id_list]
    return list(Client.objects.filter(client_id__in=id_list).values(*fields))


def get_client_id_subscription_plan_map(exclude_client_ids=None):
    if exclude_client_ids is None:
        exclude_client_ids = []
    return dict(
        Client.objects.filter(is_deleted=False)
        .exclude(client_id__in=exclude_client_ids)
        .values_list("client_id", "client_features__subscription_plan")
    )


def get_active_clients_features_excluding_churned(exclude_client_ids=None):
    if exclude_client_ids is None:
        exclude_client_ids = []
    return list(
        Client.objects.filter(is_deleted=False)
        .filter(Q(meta_info__is_excluded=False) | ~Q(meta_info__has_key="is_excluded"))
        .exclude(client_id__in=exclude_client_ids)
        .values_list("client_id", "name", "client_features")
    )


def get_active_clients():
    return list(Client.objects.filter(is_deleted=False).values("client_id", "name"))


def get_auth_connection_name(id):
    return get_client(id).auth_connection_name


def get_client_timezone(id):
    return get_client(id).time_zone


def get_client_notification(id):
    return get_client(id).client_notification


def get_clients_by_name(name):
    return list(Client.objects.filter(name=name))


def get_clients_by_ids(client_ids):
    qs = Client.objects.filter(client_id__in=client_ids)
    return qs


def get_client_for_id(client_id: int) -> Optional[Client]:
    return Client.objects.filter(client_id=client_id).get()


def get_clients_by_domain(domain):
    return list(Client.objects.filter(domain=domain))


def get_client_fiscal_start_month(id):
    return get_client(id).fiscal_start_month


def get_datastore_for_client(id):
    client_features = get_client(id).client_features
    datastore = (
        client_features["datastore"]
        if client_features and "datastore" in client_features
        else "snowflake"
    )
    return datastore


def get_plan_summary_openai_model(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get("plan_summary_model", "gpt-4-1106-preview")


def get_evaluation_mode(client_id: int) -> str:
    """
    returns client's commission evaluation mode
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("evaluation_mode", "vectorize")


def get_profile_picture_permission(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get("profile_picture_permission", "ALL")


def get_warn_on_unlock(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get("warn_on_unlock", "NONE")


def get_client_features(client_id):
    client = get_client(client_id)
    return getattr(client, "client_features")


def get_client_hidden_quota_categories(client_id) -> list[str]:
    client_features = get_client_features(client_id)
    return (client_features or {}).get("hide_categories", [])


def get_snapshot_data_for_statements(client_id) -> bool:
    client_features = get_client_features(client_id)
    return (client_features or {}).get("snapshot_data_for_statements", False)


def get_remove_zero_value_deferred(client_id) -> bool:
    client_features = get_client_features(client_id)
    return (client_features or {}).get("remove_zero_value_deferred", False)


def get_statements_export_in_lock_email(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get("statements_export_in_lock_email", False)


def is_notification_v2(client_id):
    # TODO: Add this toggle in admin UI
    """Is notification V2 enabled for client"""
    client_features = get_client_features(client_id)
    return (client_features or {}).get("notification_v2", False)


def get_crystal_calc_fields_override_logic_v2(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get("crystal_calc_fields_override_logic_v2", False)


def get_ever_comparison(client_id):
    """
    Get the ever_comparison for the given client.
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("enable_ever_comparison", True)


def get_chrome_extension_access(client_id):
    """
    check if chrome extension is enabled or not
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("chrome_extension_enabled", False)


def check_everai_access(client_id: int):
    """
    returns client everai access
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("enable_everai", False)


def get_client_expression_designer_version(client_id):
    """
    Get the expressionbox_version for the given client.
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("expressionbox_version", "v1")


def get_show_data_sources_v2(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get("show_data_sources_v2", False)


def get_sf_etl_warehouse_name(client_id):
    """
    Get the warehouse name for the given client.
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("sf_etl_wh_name", "")


def get_isolated_snowflake_database(client_id):
    """
    Get the isolated snowflake database for the given client.
    """
    client_features = get_client_features(client_id)
    # For etl tests, we need to return True for isolated_snowflake_database
    # remove this once the tests are done
    # client_id = str(client_id)
    # isolated_clients_etl_test = [
    #     "3024",
    #     "1",
    #     "3",
    #     "3008",
    #     "6898",
    #     "10037",
    #     "3018",
    #     "3011",
    #     "5",
    # ]
    # if is_pytest_env() and client_id in isolated_clients_etl_test:
    #     return True
    return (client_features or {}).get("isolated_snowflake_database", False)


def get_celery_sync_strategy(client_id):
    """
    Get the celery_sync_strategy for the given client.
    Valid values are "chain", "batched_linear", "batched_parallel", "group"
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("celery_sync_strategy", "group")


def get_batched_linear_batch_count(client_id):
    """
    Get the no of batched linear batch's count for the given client.
    Controls the batch count for the batched_linear strategy
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("no_of_batched_linear_batches", 4)


def get_batched_parallel_batch_size(client_id):
    """
    Get the batched_parallel_batch_count for the given client.
    Controls the batch count for the batched_parallel strategy
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("batched_parallel_batch_size", 40)


def get_client_databook_expression_designer_version(client_id):
    """
    Get the databook expressionbox_version for the given client.
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("databook_expressionbox_version", "v1")


def get_calc_fields_strategy(client_id):
    """
    Get the calc_fields_strategy for the given client.
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("calc_fields_strategy", "udf")


def get_calc_fields_lambda_chunk_size(client_id):
    """
    Get the calc_fields_lambda_chunk_size for the given client.
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("calc_fields_lambda_chunk_size", 100000)


def get_settlement_in_memory_batch_size(client_id):
    """
    Get the settlement_in_memory_batch_size for the given client.
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("settlement_in_memory_batch_size", 30000)


def get_settlement_lambda_batch_size(client_id):
    """
    Get the settlement_lambda_batch_size for the given client.
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("settlement_lambda_batch_size", 100000)


def can_run_settlement_snapshot_etl(client_id) -> bool:
    """
    Get the settlement_snapshot_etl flag for the given client.
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("settlement_snapshot_etl", False)


def can_run_payout_snapshot_etl(client_id) -> bool:
    """
    Get the payout_snapshot_etl flag for the given client.
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("payout_snapshot_etl", False)


def get_payout_snapshot_strategy(client_id: int) -> str:
    """
    Return the value of the quota attainment report strategy, defaults to "postgres"
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("payout_snapshot_strategy", "postgres")


def can_run_sf_payout_snapshot(client_id) -> bool:
    """
    Returns whether the client can run the SF payout snapshot ETL
    """
    return get_payout_snapshot_strategy(client_id) == "snowflake"


def can_run_auto_enrich_report(client_id) -> bool:
    """
    Returns whether the client can run the auto enrich report
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("is_auto_enrich_report", False)


def can_execute_datasheet_export_lambda(client_id) -> bool:
    """
    Returns whether the client can execute the datasheet export lambda
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("can_execute_datasheet_export_lambda", False)


def can_avoid_locking_with_pending_changes(client_id) -> bool:
    """
    Returns whether the client can avoid locking with pending changes
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("can_avoid_locking_with_pending_changes", False)


def can_use_custom_metrics(client_id):
    """
    Returns whether the client can use custom metrics
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("can_use_custom_metrics", False)


def get_line_item_approval_flag(client_id):
    """
    Get the line_item_approval_flag for the given client.
    """
    client_features = get_client_features(client_id)
    is_line_item_level_approval_enabled = False
    if get_payout_approvals_flag(client_id=client_id):
        is_line_item_level_approval_enabled = (
            client_features.get("approval_config", {})
            .get("payout_approvals", {})
            .get("line_item_level_approval", False)
        )
    return is_line_item_level_approval_enabled


def get_statement_approval_flag(client_id):
    """
    Get the line_item_approval_flag for the given client.
    """
    client_features = get_client_features(client_id)
    is_statement_level_approval_enabled = False
    if get_payout_approvals_flag(client_id=client_id):
        is_statement_level_approval_enabled = (
            client_features.get("approval_config", {})
            .get("payout_approvals", {})
            .get("statement_level_approval", False)
        )
    return is_statement_level_approval_enabled


def get_salesforce_oauth_env(client_id: int) -> str:
    client_features = get_client_features(client_id)
    return (client_features or {}).get("salesforce_env", "production")


def write_commission_to_snowflake(client_id: int) -> bool:
    client_features = get_client_features(client_id)
    return (client_features or {}).get("write_commission_to_snowflake", False)


def is_commission_adjustment_v2_enabled(client_id: int) -> bool:
    client_features = get_client_features(client_id)
    return (client_features or {}).get("is_commission_adjustment_v2_enabled", True)


def write_quota_erosion_to_snowflake(client_id: int) -> bool:
    client_features = get_client_features(client_id)
    return (client_features or {}).get("write_quota_erosion_to_snowflake", False)


def is_write_settlement_to_snowflake(client_id: int) -> bool:
    """
    flag to check if settlement data has to be written to snowflake
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("write_settlement_to_snowflake", False)


def is_read_settlement_from_snowflake(client_id: int) -> bool:
    """
    flag to check if settlement data has to be read from snowflake
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("read_settlement_from_snowflake", False)


def is_settlement_v3_enabled(client_id):
    return get_settlement_v3_execution_mode(client_id) == ExecutionMode.ACTUAL_RUN


def get_settlement_v3_execution_mode(client_id):
    client_features = get_client_features(client_id)
    return ExecutionMode(
        client_features.get(
            "settlement_v3_execution_mode", ExecutionMode.DISABLED.value
        ).upper()
    )


def get_client_subscription_plan(client_id):
    """Get the subscription plan for the given client.

    Args:
        client_id: The ID of the client.

    Returns:
        The subscription plan for the given client.
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("subscription_plan", "BASIC")


def is_msteams_connected(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get("msteams_connected", False)


def is_slack_connected(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get("slack_connected", False)


def is_estimator_enabled_for_client(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get("enable_salesforce_app", True)


def is_quota_effective_dated(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get("quota_effective_dated", False)


def is_freeze_date_set(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get("freeze_date", False)


def is_approval_enabled_for_client(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get("show_approval_feature", False)


def get_client_is_datasheet_sharded(client_id):
    """
    Returns whether the client's datasheet data is sharded
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("shard_datasheet_data", True)


def is_incremental_datasheet_etl_enabled(client_id):
    """
    Returns whether incremental etl is enabled for the client or not
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("is_incremental_etl", False)


def get_client_datasheet_row_limit(client_id) -> int:
    """
    Returns the client datasheet row limit

    Default value is 25,000,000 (25 Million)
    """
    client_features = get_client_features(client_id)
    datasheet_row_limit = (client_features or {}).get("datasheet_row_limit", 25000000)
    if not isinstance(datasheet_row_limit, int):
        raise ValueError("The row limit should be an integer")
    return datasheet_row_limit


def is_mem_regulated_batching_enabled(client_id) -> bool:
    """
    Returns whether the client has mem regulated batching enabled
    """
    client_features = get_client_features(client_id)
    mem_regulated_batching = (client_features or {}).get(
        "mem_regulated_batching", False
    )
    return mem_regulated_batching


def get_object_knowledge_date_query_strategy(client_id: int) -> str:
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get(
        "object_knowledge_date_query_strategy", "postgres"
    )


def is_hris_integration_enabled(client_id: int) -> bool:
    """
    True if the client is using HRIS integration
    else False
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("enable_hris_integration", False)


def is_split_summation_to_li_enabled(client_id):
    """
    Returns a boolean indicating whether spliting summation to line is enabled for the client or not
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("split_summation_to_li", False)


def should_insert_meta_data_to_vec_db(client_id):
    """
    Returns a boolean indicating whether the client should insert meta data to vector db or not
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("insert_meta_data_to_vec_db", False)


def should_take_ds_snapshot(client_id):
    """
    Returns a boolean indicating whether the client should take a datasheet snapshot or not
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("take_ds_snapshot", True)


def should_take_co_snapshot(client_id):
    """
    Returns a boolean indicating whether the client should take a custom obhect snapshot or not
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("take_co_snapshot", False)


def should_use_multi_engine_stormbreaker(client_id):
    """
    Returns a boolean indicating whether the client should use the multi engine supported stormbreaker or not
    """
    client_features = get_client_features(client_id)
    _should_use_multi_engine_stormbreaker = (client_features or {}).get(
        "use_multi_engine_stormbreaker", False
    )
    if _should_use_multi_engine_stormbreaker:
        _should_take_ds_snapshot = should_take_ds_snapshot(client_id)
        if not _should_take_ds_snapshot:
            logger.warning(
                "Cannot use multi engine stormbreaker for client_id %s as `Take Datasheet Snapshot` flag is not set to True",
                client_id,
            )
            return False
        return True
    return False


def use_multi_engine_stormbreaker_for_feature(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get(
        "use_multi_engine_stormbreaker_for_feature", False
    ) or should_use_multi_engine_stormbreaker(client_id)


def should_take_report_snapshot(client_id):
    """
    Returns a boolean indicating whether the client should take a report snapshot or not
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("take_report_snapshot", False)


def get_commission_plan_version(client_id) -> str:
    """
    Returns the commission plan version of the client (classic / canvas)
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("commission_plan_version", "v2")


def should_show_get_user_property_commission(client_id: int) -> bool:
    """
    Returns true if the GetUserProperty commission function feature flag is set to true
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("show_get_user_property_commission", True)


def can_use_get_user_property_function(client_id: int) -> bool:
    """
    Checks if GetUserProperty is supported for the client
    """
    return get_commission_plan_version(
        client_id
    ) == "v2" and should_show_get_user_property_commission(client_id)


def get_run_settlement_report_value(client_id: int) -> RunSettlementReport:
    """
    Retrieves the value of the run_settlement_report feature for a given client.

    Args:
        client_id (int): The ID of the client.

    Returns:
        RunSettlementReport: The value of the run_settlement_report feature for the client.
    """
    client_features = get_client_features(client_id)
    feature_val = (client_features or {}).get(
        "run_settlement_report", RunSettlementReport.ALWAYS.value
    )
    return RunSettlementReport(feature_val)


def get_analytics_default_dashboard_status(client_id: int) -> str:
    """
    Return the value of the analytics_default_dashboard_status flag, defaults to "none"
    """
    from spm.services.analytics_default_dashboard import DefaultDashboardStatus

    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get(
        "analytics_default_dashboard_status", DefaultDashboardStatus.NONE.value
    )


def get_is_plan_exclusion_enabled(client_id: int) -> bool:
    """
    Returns whether the plan exclusion feature is enabled for the client
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("enable_plan_exclusion", False)


def update_analytics_default_dashboard_status(client_id: int, new_status: str):
    """
    Updates the value of the analytics_default_dashboard_status flag
    """
    client = get_client(client_id)
    if not client.client_features:
        client.client_features = {}
    client.client_features["analytics_default_dashboard_status"] = new_status
    client.save()


def get_commission_report_strategy(client_id: int) -> str:
    """
    Return the value of the commission report strategy, defaults to "postgres"
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("commission_report_strategy", "postgres")


def get_settlement_report_strategy(client_id: int) -> str:
    """
    Return the value of the settlement report strategy, defaults to "postgres"
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("settlement_report_strategy", "postgres")


def get_settlement_snapshot_strategy(client_id: int) -> str:
    """
    Return the value of the settlement snapshot strategy, defaults to "postgres"
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("settlement_snapshot_strategy", "postgres")


def get_commission_summary_report_strategy(client_id: int) -> bool:
    """
    Return the value of the commission summary report strategy, defaults to "postgres"
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("commission_summary_report_strategy", "postgres")


def get_quota_attainment_report_strategy(client_id: int) -> bool:
    """
    Return the value of the quota attainment report strategy, defaults to "postgres"
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("quota_attainment_report_strategy", "postgres")


def get_payout_report_object_enabled(client_id: int) -> bool:
    """
    Return the value if payout report object can be accessed and used by client, defaults to False
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("show_payout_report_object", False)


def get_fx_rate_report_object_enabled(client_id: int) -> bool:
    """
    Return the value if fx rate report object can be accessed and used by client, defaults to False
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("show_fx_rate_report_object", False)


def get_approvals_report_object_enabled(client_id: int) -> bool:
    """
    Return the value if approvals report object can be accessed and used by client, defaults to False
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("show_approvals_report_object", False)


def is_clear_pending_tasks_enabled(client_id: int) -> bool:
    """
    Returns true if the client has the clear_pending_tasks feature enabled
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("clear_pending_tasks", False)


def get_base_currency_threshold(client_id: int) -> float:
    client_features = get_client_features(client_id)
    return (client_features or {}).get("payout_base_currency_threshold", 0.01)


def get_payee_currency_threshold(client_id: int) -> float:
    client_features = get_client_features(client_id)
    return (client_features or {}).get("payout_payee_currency_threshold", 1)


def get_datasheet_sync_check_interval(client_id: int) -> int:
    """
    Returns the interval for checking datasheet sync status polling.

    Unit - seconds
    Default value - 10 Seconds
    """

    client_features = get_client_features(client_id)
    datasheet_sync_status_check_interval = (client_features or {}).get(
        "datasheet_sync_check_interval", 10
    )
    return int(datasheet_sync_status_check_interval)


def get_datasheet_sync_check_threshold(client_id: int) -> int:
    """
    Returns the threshold for checking datasheet sync status polling.

    Unit - seconds
    Default value - 12600 seconds (3 Hours and 30 Minutes)
    """
    client_features = get_client_features(client_id)
    datasheet_sync_status_check_threshold = (client_features or {}).get(
        "datasheet_sync_check_threshold", 12600
    )
    return int(datasheet_sync_status_check_threshold)


def is_edit_locked_quota_flag_enabled(client_id: int) -> bool:
    """
    Check if the Quota values in the locked period can be modified.
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("edit_locked_quota", False)


def is_generate_associated_datasheets_only(client_id: int) -> bool:
    """
    Returns whether the client has the generate_associated_datasheets_only feature enabled
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("generate_associated_datasheets_only", False)


def is_run_sync_multiple_period_enabled(client_id: int) -> bool:
    """
    Returns whether the client has the run_sync_for_multiple_period enabled
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("run_sync_for_multiple_period", False)


def is_upload_excel_files_in_custom_object(client_id: int) -> bool:
    """
    Returns wether the client has the access to upload excel file
    """
    client_features = get_client_features(client_id=client_id)
    return (client_features or {}).get("upload_excel_files_in_custom_object", False)


def get_datasheet_ids_for_ltd_cf_temp_table(id) -> list[str]:
    """
    Returns the list of datasheet ids for the client that are used to save the temp dataframe in  LTD CF Evaluation
    If the feature flag is not set, it returns an empty list
    Example:
    client_features["ltd_cf_temp_table"] = ["883f7209-4b1d-43c7-af52-61b2e252b935"]
    """
    client_features = get_client(id).client_features
    datasheet_ids = (
        client_features["ltd_cf_temp_table"]
        if client_features and "ltd_cf_temp_table" in client_features
        else []
    )
    return datasheet_ids


def is_spark_dry_run(client_id: int | str) -> bool:
    client_features = get_client_features(client_id)
    return (client_features or {}).get("is_spark_dry_run", False)


def should_save_spark_output_to_variant(client_id: int | str) -> bool:
    client_features = get_client_features(client_id)
    return (client_features or {}).get("should_save_spark_output_to_variant", False)


def avoid_concurrent_register_payment(client_id: int | str) -> bool:
    client_features = get_client_features(client_id)
    return (client_features or {}).get("avoid_concurrent_register_payment", False)


def get_lite_etl_comparison_mode(client_id: int | str) -> str:
    """
    Flag to control lite ETL comparison run mode
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("lite_etl_comparison_mode", "NONE")


def is_ds_lite_etl_enabled(client_id: int | str) -> bool:
    """
    Flag to run SF Datasheet ETL without intermediate tables (non-dry run)
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("ds_lite_etl", False)


def is_lambda_enabled_for_settlement(client_id: int) -> bool:
    client_features = get_client_features(client_id)
    return (client_features or {}).get("lambda_for_settlement", False)


def is_force_datasheet_skip_enabled(client_id: int) -> bool:
    """
    Flag to check if the client has the force datasheet skip enabled
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get("force_datasheet_skip", False)


def save_client(record):
    record.save()


fiscal_start_month_number_map = {
    "January": 1,
    "February": 2,
    "March": 3,
    "April": 4,
    "May": 5,
    "June": 6,
    "July": 7,
    "August": 8,
    "September": 9,
    "October": 10,
    "November": 11,
    "December": 12,
}


def get_valid_filename(s):
    """
    Returns the given string converted to a string that can be used for a clean
    filename. Specifically, leading and trailing spaces are removed; other
    spaces are converted to underscores; and anything that is not a unicode
    alphanumeric, dash, underscore, or dot, is removed.
    >>> get_valid_filename("john's portrait in 2004.jpg")
    'johns_portrait_in_2004.jpg'
    """
    s = force_str(s).strip().replace(" ", "_")
    return re.sub(r"(?u)[^-\w.]", "", s)


# populate created_at in meta_info, currency drop_down, connection_name drop_down
def create_client(params):
    max_id = Client.objects.filter().aggregate(max_id=Max("pk"))["max_id"]
    id = max_id + 1 if max_id else 1
    d = timezone.now()
    name = params["name"]
    crm_company_id = params["hubspot_company_id"]
    logger.info(f"BEGIN: Create new client with {id} and {name}")
    domain = params["domain"]
    auth_connection_name = params["connection_name"]
    connection_type = params["connection_type"]
    base_currency = params["base_currency"]
    fiscal_start_month = fiscal_start_month_number_map[params["fiscal_start_month"]]
    logo_s3_path = params["logo_s3_path"]
    valid_filename = get_valid_filename(name)
    logo_url = (
        params["logo_url"]
        if "logo_url" in params
        else os.environ.get("S3_CDN") + "/Logos/" + valid_filename
    )
    statement_logo_s3_path = params[
        "statement_logo_s3_path"
    ]  # custom logo for statements pdf
    statement_logo_url = (
        os.environ.get("S3_CDN", "") + "/" + get_statement_file_path(valid_filename)
        if params["statement_logo_s3_path"]
        else None
    )
    time_zone = params["timezone"]
    meta_info = {}
    meta_info["created_at"] = d
    meta_info["created_by"] = params["created_by"]
    meta_info["is_live"] = literal_eval(params["status"].capitalize())
    meta_info["is_test"] = literal_eval(params["type"].capitalize())

    # flag for cron monitoring
    meta_info["is_monitored"] = False

    client_features = {}
    client_features["celery_sync_strategy"] = "group"
    client_features["is_commission_adjustment_v2_enabled"] = True
    client_features["global_run"] = True

    # ETL Report 3.0 related params
    client_features["write_commission_to_snowflake"] = True
    client_features["write_quota_erosion_to_snowflake"] = True
    client_features["write_settlement_to_snowflake"] = True
    client_features["payout_snapshot_etl"] = True
    client_features["settlement_snapshot_etl"] = True
    client_features["payout_snapshot_strategy"] = "snowflake"
    client_features["settlement_snapshot_strategy"] = "snowflake"
    client_features["commission_report_strategy"] = "snowflake"
    client_features["quota_attainment_report_strategy"] = "snowflake"
    client_features["settlement_report_strategy"] = "snowflake"
    client_features["commission_summary_report_strategy"] = "snowflake"

    # Enable payout report object by default
    client_features["show_payout_report_object"] = True

    # Disable fx report object by default
    client_features["show_fx_rate_report_object"] = False

    client_features["show_commission_buddy"] = literal_eval(
        params["show_commission_buddy"].capitalize()
    )

    client_features["show_territory_plan"] = literal_eval(
        params["show_territory_plan"].capitalize()
    )

    client_features["show_commission_percent"] = literal_eval(
        params["show_commission_percent"].capitalize()
    )
    client_features["is_upstream_etl"] = True
    client_features["is_db_comm_etl"] = True
    client_features["show_salesforce_integration"] = True
    client_features["crm_hyperlinks"] = True
    client_features["show_statements_v2"] = True
    client_features["crystal_version"] = "3"
    client_features["expressionbox_version"] = "v2"
    client_features["databook_expressionbox_version"] = "v2"
    client_features["calc_fields_strategy"] = params.get(
        "calc_fields_strategy", "lambda"
    )
    client_features["calc_fields_lambda_chunk_size"] = params.get(
        "calc_fields_lambda_chunk_size", 100000
    )
    client_features["show_data_sources_v2"] = literal_eval(
        params["show_data_sources_v2"].capitalize()
    )
    client_features["enable_ever_comparison"] = literal_eval(
        params["enable_ever_comparison"].capitalize()
    )
    client_features["manager_rollup_ed"] = True
    client_features["show_superset_dashboard"] = literal_eval(
        params["show_superset_dashboard"].capitalize()
    )
    client_features["show_return_v1_button"] = literal_eval(
        params["show_return_v1_button"].capitalize()
    )
    client_features["show_approval_feature"] = literal_eval(
        params["show_approval_feature"].capitalize()
    )
    client_features["expose_comm_reports_in_plan"] = literal_eval(
        params["expose_comm_reports_in_plan"].capitalize()
    )
    client_features["show_roles"] = True
    client_features["show_datasheet_permission"] = True
    client_features["show_custom_object_permission"] = True
    client_features["delete_approvers"] = params["delete_approvers"]
    client_features["is_new_frozen_payroll_etl"] = True
    client_features["show_simulation_v2"] = True
    client_features["enable_concurrent_sessions"] = literal_eval(
        params["enable_concurrent_sessions"].capitalize()
    )
    client_features["datasheet_v2"] = literal_eval(params["datasheet_v2"].capitalize())
    client_features["enable_support_user_access"] = is_prod_env() or literal_eval(
        params["enable_support_user_access"].capitalize()
    )
    client_features["enable_tsar_webapp_custom_roles"] = literal_eval(
        params["enable_tsar_webapp_custom_roles"].capitalize()
    )
    client_features["is_secure_admin_ui_auth0_user_mgmt"] = literal_eval(
        params["is_secure_admin_ui_auth0_user_mgmt"].capitalize()
    )
    client_features["show_chatgpt"] = literal_eval(params["show_chatgpt"].capitalize())
    client_features["show_payout_table_breakdown"] = literal_eval(
        params["show_payout_table_breakdown"].capitalize()
    )
    client_features["isolated_snowflake_database"] = literal_eval(
        params["isolated_snowflake_database"].capitalize()
    )
    client_features["plan_summary_model"] = params["plan_summary_model"]
    client_features["evaluation_mode"] = "vectorize"
    client_features["profile_picture_permission"] = params["profile_picture_permission"]
    client_features["warn_on_unlock"] = params["warn_on_unlock"]
    client_features["upstream_etl_version"] = params["upstream_etl_version"]
    client_features["chrome_extension_enabled"] = literal_eval(
        params["chrome_extension_enabled"].capitalize()
    )
    client_features["enable_everai"] = literal_eval(
        params.get("enable_everai", "false").capitalize()
    )
    client_features["show_advanced_filter"] = True
    client_features["show_statements_pdf"] = True
    client_features["custom_calendar"] = literal_eval(
        params["custom_calendar"].capitalize()
    )
    client_features["commission_plan_version"] = "v2"
    client_features["show_forecast"] = literal_eval(
        params["show_forecast"].capitalize()
    )
    client_features["quota_effective_dated"] = literal_eval(
        params["quota_effective_dated"].capitalize()
    )
    client_features["allow_quota_settings_override"] = literal_eval(
        params["allow_quota_settings_override"].capitalize()
    )
    client_features["allow_annual_quota_effective_dated"] = literal_eval(
        params["allow_annual_quota_effective_dated"].capitalize()
    )
    client_features["notification_v2"] = True

    client_features["crystal_calc_fields_override_logic_v2"] = True

    # Production / Sandbox
    client_features["salesforce_env"] = "production"
    client_features["allow_adjustments_to_frozen_commission"] = literal_eval(
        params["allow_adjustments_to_frozen_commission"].capitalize()
    )
    client_features["split_summation_to_li"] = literal_eval(
        params["split_summation_to_li"].capitalize()
    )
    client_features["show_metrics"] = literal_eval(params["show_metrics"].capitalize())
    client_features["avoid_iframe_in_contracts"] = literal_eval(
        params["avoid_iframe_in_contracts"].capitalize()
    )
    if params["hide_categories"] == "":
        hide_categories = []
    else:
        hide_categories = list(params["hide_categories"].split(","))
    client_features["hide_categories"] = hide_categories

    client_features["subscription_plan"] = (
        params["subscription_plan"] if "subscription_plan" in params else "BASIC"
    )
    subscription_plan = client_features["subscription_plan"]
    logger.info(
        f"BEGIN: Process Customer Change for {id} with {subscription_plan} plan"
    )
    client_features["enable_hris_integration"] = literal_eval(
        params["enable_hris_integration"].capitalize()
    )
    client_features["object_knowledge_date_query_strategy"] = "postgres"
    client_features["snapshot_data_for_statements"] = literal_eval(
        params.get("snapshot_data_for_statements", "false").capitalize()
    )
    client_features["show_get_user_property_commission"] = True
    client_features["use_multi_engine_stormbreaker"] = literal_eval(
        params.get("use_multi_engine_stormbreaker", "false").capitalize()
    )
    client_features["insert_meta_data_to_vec_db"] = literal_eval(
        params.get("insert_meta_data_to_vec_db", "false").capitalize()
    )
    client_features["take_ds_snapshot"] = True

    client_features["enable_custom_workflows"] = literal_eval(
        params["enable_custom_workflows"].capitalize()
    )
    client_features["settlement_v2"] = literal_eval(
        params["settlement_v2"].capitalize()
    )
    client_features["edit_locked_quota"] = literal_eval(
        params["edit_locked_quota"].capitalize()
    )
    client_features["crystal_custom_calendar_future_periods"] = literal_eval(
        params["crystal_custom_calendar_future_periods"]
    )
    client_features["run_settlement_report"] = params.get(
        "run_settlement_report", RunSettlementReport.IF_NEEDED.value
    )
    client_features["global_search"] = {"enabled": True, "modules": {}}
    client_features["enable_kafka_events"] = True
    client_features["enable_rounding_in_tier_functions"] = True
    client_features["enable_custom_theme"] = literal_eval(
        params["enable_custom_theme"].capitalize()
    )
    client_features["enable_sidebar_v3"] = literal_eval(
        params["enable_sidebar_v3"].capitalize()
    )
    client_features["use_aggrid_for_pdf_export"] = literal_eval(
        params["use_aggrid_for_pdf_export"].capitalize()
    )
    client_features["show_g2_review_form"] = params["show_g2_review_form"]
    client_features["allow_only_admins_to_modify_user_name"] = literal_eval(
        params["allow_only_admins_to_modify_user_name"].capitalize()
    )
    client_features["modules"] = list(params["modules"].split(","))
    # run sync for multiple periods
    client_features["run_sync_for_multiple_period"] = literal_eval(
        params["run_sync_for_multiple_period"].capitalize()
    )
    client_features["upload_excel_files_in_custom_object"] = literal_eval(
        params["upload_excel_files_in_custom_object"].capitalize()
    )
    client_features["is_auto_enrich_report"] = literal_eval(
        params["is_auto_enrich_report"].capitalize()
    )
    client_features["async_export_datasheet"] = literal_eval(
        params["async_export_datasheet"].capitalize()
    )
    # handle race condition for register payout
    client_features["avoid_concurrent_register_payment"] = True

    # avoid locking with pending changes
    client_features["can_avoid_locking_with_pending_changes"] = True

    client_features["take_report_snapshot"] = True
    client_features["take_co_snapshot"] = True
    client_features["show_observable_notification"] = literal_eval(
        params.get("show_observable_notification", "false").capitalize()
    )

    if not is_prod_env():
        #  For new clients in non-prod envs, enable datasheet v2 by default
        logger.info(
            f"Enabling datasheet_v2 by default for new client {id} in non-prod env"
        )
        client_features["show_data_sources_v2"] = True

    # Enabling MFA for newly adding clients in prod envs
    if is_prod_env():
        client_features["mfa"] = {"enabled": True, "factors": ["email"]}

    if (
        os.environ.get("ENV") != "CI_SELENIUM"
        and os.environ.get("EXECUTION_ENV") != "PYTEST"
    ):
        app.control.broadcast(
            "process_customer_changes",
            arguments={
                "client_id": str(id),
                "subscription_plan": subscription_plan,
                "operation_type": "create",
            },
        )
    logger.info(f"END: Process Customer Change for {id} with {subscription_plan} plan")
    try:
        obj = Client.objects.create(
            client_id=id,
            name=name,
            crm_company_id=crm_company_id,
            domain=domain,
            logo_s3_path=logo_s3_path,
            auth_connection_name=auth_connection_name,
            connection_type=connection_type,
            base_currency=base_currency,
            fiscal_start_month=fiscal_start_month,
            logo_url=logo_url,
            statement_logo_s3_path=statement_logo_s3_path,
            statement_logo_url=statement_logo_url,
            time_zone=time_zone,
            meta_info=meta_info,
            client_features=client_features,
        )
        logger.info(f"END: Create new client with {id} and {name}")
        return obj
    except Exception as e:
        log_me("EXCEPTION IN CLIENT CREATION - {}".format(traceback.print_exc()))
        log_me("EXcep.. {}".format(e))


def get_client_hidden_categories(client_id):
    client = get_client(client_id)
    return client.client_features.get("hide_categories", [])


def set_client_feature(client_id, label, value):
    client = get_client(client_id)
    client_features = client.client_features or dict()
    client_features[label] = value
    client.client_features = client_features
    client.save()
    return client.client_features[label]


def remove_client_feature(client_id, key):
    """Remove a client feature given its key if present in the client's features."""
    client = get_client(client_id)
    client_features = client.client_features or dict()
    if key not in client_features:
        return False

    client_features.pop(key)
    client.client_features = client_features
    client.save()
    return True


def set_client_meta_info(client_id, label, value):
    client = get_client(client_id)
    meta_info = client.meta_info or dict()
    meta_info[label] = value
    client.meta_info = meta_info
    client.save()
    return client.meta_info[label]


def update_statements_export_in_lock_email(client_id, value):
    return set_client_feature(client_id, "statements_export_in_lock_email", value)


def toggle_client_notification(client_id: int, client_notification: bool):
    """Enable/disable client notification for a given client."""
    client = get_client(client_id)
    client.client_notification = client_notification
    client.save()


def update_or_remove_freeze_date(client_id: int, date: int | None):
    if date is None:
        return remove_client_feature(client_id, "freeze_date")
    return set_client_feature(client_id, "freeze_date", date)


def update_client(params: dict) -> int:
    from spm.services.notification_settings_services import add_client_notifications

    id = params["client_id"]
    d = timezone.now()
    client = get_client(id)
    client.name = params["name"]
    client.crm_company_id = params["hubspot_company_id"]
    logger.info(f"BEGIN: Update client {id} and {client.name}")
    client.domain = params["domain"]
    client.auth_connection_name = params["connection_name"]
    client.connection_type = params["connection_type"]
    client.base_currency = params["base_currency"]
    client.fiscal_start_month = fiscal_start_month_number_map[
        params["fiscal_start_month"]
    ]
    client.time_zone = params["timezone"]
    client.logo_s3_path = params["logo_s3_path"]
    client.statement_logo_s3_path = params[
        "statement_logo_s3_path"
    ]  # custom logo for statements pdf
    valid_filename = get_valid_filename(client.name)
    if "logo_s3_path" in params and params["logo_s3_path"]:
        client.logo_url = os.environ.get("S3_CDN") + "/Logos/" + valid_filename
        invalidate_cloudfront_cache(
            os.environ.get("S3_CDN"), "/Logos/" + valid_filename
        )

    if "statement_logo_s3_path" in params and params["statement_logo_s3_path"]:
        client.statement_logo_url = (
            os.environ.get("S3_CDN", "") + "/" + get_statement_file_path(valid_filename)
        )
    if not client.meta_info:
        client.meta_info = {}
    client.meta_info["is_live"] = literal_eval(params["status"].capitalize())
    client.meta_info["is_test"] = literal_eval(params["type"].capitalize())
    client.meta_info["updated_at"] = d
    if "updated_by" in params and params["updated_by"]:
        client.meta_info["updated_by"] = params["updated_by"]

    if not client.client_features:
        client.client_features = {}
    client.client_features["show_commission_buddy"] = literal_eval(
        params["show_commission_buddy"].capitalize()
    )
    client.client_features["show_territory_plan"] = literal_eval(
        params["show_territory_plan"].capitalize()
    )
    client.client_features["show_commission_percent"] = literal_eval(
        params["show_commission_percent"].capitalize()
    )
    client.client_features["is_upstream_etl"] = True
    client.client_features["is_db_comm_etl"] = True
    if params["hide_categories"] == "":
        client.client_features["hide_categories"] = []
    else:
        client.client_features["hide_categories"] = list(
            params["hide_categories"].split(",")
        )
    client.client_features["show_superset_dashboard"] = literal_eval(
        params.get("show_superset_dashboard", "False").capitalize()
    )
    client.client_features["show_return_v1_button"] = literal_eval(
        params["show_return_v1_button"].capitalize()
    )
    client.client_features["show_salesforce_integration"] = True
    client.client_features["is_commission_adjustment_v2_enabled"] = True
    client.client_features["notification_v2"] = True
    client.client_features["crystal_calc_fields_override_logic_v2"] = True
    client.client_features["crm_hyperlinks"] = True
    client.client_features["show_statements_v2"] = True
    client.client_features["crystal_version"] = "3"
    client.client_features["expressionbox_version"] = "v2"
    client.client_features["databook_expressionbox_version"] = "v2"
    # client.client_features["databook_sync_strategy"] = params["databook_sync_strategy"]
    client.client_features["enable_ever_comparison"] = literal_eval(
        params["enable_ever_comparison"].capitalize()
    )
    client.client_features["manager_rollup_ed"] = client.client_features.get(
        "manager_rollup_ed", False
    )

    client.client_features["expose_comm_reports_in_plan"] = literal_eval(
        params["expose_comm_reports_in_plan"].capitalize()
    )
    client.client_features["show_roles"] = True
    client.client_features["show_datasheet_permission"] = True
    client.client_features["show_custom_object_permission"] = True
    client.client_features["is_new_frozen_payroll_etl"] = True
    client.client_features["show_simulation_v2"] = True
    client.client_features["enable_concurrent_sessions"] = literal_eval(
        params["enable_concurrent_sessions"].capitalize()
    )
    client.client_features["datasheet_v2"] = literal_eval(
        params["datasheet_v2"].capitalize()
    )
    client.client_features["allow_adjustments_to_frozen_commission"] = literal_eval(
        params["allow_adjustments_to_frozen_commission"].capitalize()
    )
    client.client_features["show_chatgpt"] = literal_eval(
        params["show_chatgpt"].capitalize()
    )
    client.client_features["show_payout_table_breakdown"] = literal_eval(
        params["show_payout_table_breakdown"].capitalize()
    )
    client.client_features["isolated_snowflake_database"] = literal_eval(
        params["isolated_snowflake_database"].capitalize()
    )

    client.client_features["plan_summary_model"] = params["plan_summary_model"]
    client.client_features["evaluation_mode"] = "vectorize"
    client.client_features["profile_picture_permission"] = params[
        "profile_picture_permission"
    ]
    client.client_features["warn_on_unlock"] = params["warn_on_unlock"]
    client.client_features["upstream_etl_version"] = params["upstream_etl_version"]
    client.client_features["chrome_extension_enabled"] = literal_eval(
        params["chrome_extension_enabled"].capitalize()
    )
    client.client_features["enable_everai"] = literal_eval(
        params.get("enable_everai", "false").capitalize()
    )
    client.client_features["show_advanced_filter"] = True
    client.client_features["show_statements_pdf"] = True
    client.client_features["documentation_url"] = params["documentation_url"]
    client.client_features["run_sync_for_multiple_period"] = literal_eval(
        params["run_sync_for_multiple_period"].capitalize()
    )
    client.client_features["upload_excel_files_in_custom_object"] = literal_eval(
        params["upload_excel_files_in_custom_object"].capitalize()
    )

    if params["help_doc_user_role"] == "":
        client.client_features["help_doc_user_role"] = []
    else:
        client.client_features["help_doc_user_role"] = list(
            params["help_doc_user_role"].split(",")
        )
    client.client_features["custom_calendar"] = literal_eval(
        params["custom_calendar"].capitalize()
    )
    client.client_features["commission_plan_version"] = "v2"

    client.client_features["show_forecast"] = literal_eval(
        params["show_forecast"].capitalize()
    )
    client.client_features["quota_effective_dated"] = literal_eval(
        params["quota_effective_dated"].capitalize()
    )
    client.client_features["modules"] = list(params["modules"].split(","))
    client.client_features["allow_quota_settings_override"] = literal_eval(
        params["allow_quota_settings_override"].capitalize()
    )
    client.client_features["allow_annual_quota_effective_dated"] = literal_eval(
        params["allow_annual_quota_effective_dated"].capitalize()
    )

    # Check previously set Approval Feature Flag
    previous_approval_notifications = client.client_features.get(
        "show_approval_feature", False
    )
    current_approval_notifications = literal_eval(
        params.get("show_approval_feature", "false").capitalize()
    )
    if previous_approval_notifications != current_approval_notifications:
        logger.info(
            f"Approval Feature Flag changed from {previous_approval_notifications} to {current_approval_notifications} for client: {id}"
        )
        logger.info(f"Setting default approval notifications for client: {id}")

        # Adding default approval notifications to client notifications table
        default_status = {"email": False, "slack": False, "ms_teams": False}
        approval_tasks = [
            {
                "name": "NEW_APPROVAL_REQUEST_NOTIFICATION",
                "status": default_status,
                "frequency": None,
                "is_opted_out": False,
            },
            {
                "name": "EVERYDAY_PENDING_APPROVAL_REQUEST_NOTIFICATION",
                "status": default_status,
                "frequency": None,
                "is_opted_out": False,
            },
        ]

        add_client_notifications(
            client.client_id,
            notif_task_req_adapter.validate_python(approval_tasks),
            timezone.now(),
            audit={"updated_by": params.get("updated_by")},
        )

    client.client_features["show_approval_feature"] = literal_eval(
        params["show_approval_feature"].capitalize()
    )
    client.client_features["snapshot_data_for_statements"] = literal_eval(
        params.get("snapshot_data_for_statements", "false").capitalize()
    )
    client.client_features["split_summation_to_li"] = literal_eval(
        params["split_summation_to_li"].capitalize()
    )
    client.client_features["show_metrics"] = literal_eval(
        params["show_metrics"].capitalize()
    )
    client.client_features["avoid_iframe_in_contracts"] = literal_eval(
        params["avoid_iframe_in_contracts"].capitalize()
    )
    client.client_features["settlement_v2"] = literal_eval(
        params["settlement_v2"].capitalize()
    )
    client.client_features["edit_locked_quota"] = literal_eval(
        params["edit_locked_quota"].capitalize()
    )
    client.client_features["crystal_custom_calendar_future_periods"] = literal_eval(
        params["crystal_custom_calendar_future_periods"]
    )
    client.client_features["run_settlement_report"] = params.get(
        "run_settlement_report", RunSettlementReport.ALWAYS.value
    )
    client.client_features["is_auto_enrich_report"] = literal_eval(
        params["is_auto_enrich_report"].capitalize()
    )
    client.client_features["async_export_datasheet"] = literal_eval(
        params["async_export_datasheet"].capitalize()
    )

    existing_subscription_plan = (client.client_features or {}).get(
        "subscription_plan", "BASIC"
    )
    subscription_plan = (
        params["subscription_plan"]
        if "subscription_plan" in params
        else existing_subscription_plan
    )
    client.client_features["subscription_plan"] = subscription_plan

    if subscription_plan != existing_subscription_plan:
        logger.info(
            f"BEGIN: Process Customer Change for {id} from {existing_subscription_plan} to {subscription_plan}"
        )
        app.control.broadcast(
            "process_customer_changes",
            arguments={
                "client_id": str(id),
                "subscription_plan": subscription_plan,
                "operation_type": "create",
            },
        )
        app.control.broadcast(
            "process_customer_changes",
            arguments={
                "client_id": str(id),
                "subscription_plan": existing_subscription_plan,
                "operation_type": "remove",
            },
        )
        logger.info(
            f"END: Process Customer Change for {id} from {existing_subscription_plan} to {subscription_plan}"
        )

    client.client_features["object_knowledge_date_query_strategy"] = "postgres"

    client.client_features["show_get_user_property_commission"] = True
    client.client_features["use_multi_engine_stormbreaker"] = literal_eval(
        params.get("use_multi_engine_stormbreaker", "false").capitalize()
    )
    client.client_features["insert_meta_data_to_vec_db"] = literal_eval(
        params.get("insert_meta_data_to_vec_db", "false").capitalize()
    )
    client.client_features["take_ds_snapshot"] = True

    client.client_features["enable_hris_integration"] = literal_eval(
        params["enable_hris_integration"].capitalize()
    )
    client.client_features["enable_custom_workflows"] = literal_eval(
        params["enable_custom_workflows"].capitalize()
    )
    client.client_features["enable_custom_theme"] = literal_eval(
        params["enable_custom_theme"].capitalize()
    )
    client.client_features["enable_sidebar_v3"] = literal_eval(
        params["enable_sidebar_v3"].capitalize()
    )

    client.client_features["use_aggrid_for_pdf_export"] = literal_eval(
        params["use_aggrid_for_pdf_export"].capitalize()
    )

    client.client_features["run_sync_for_multiple_period"] = literal_eval(
        params["run_sync_for_multiple_period"].capitalize()
    )

    ############## TSAR FLAG ################
    tsar_curr_state = client.client_features.get(
        "enable_support_user_access", is_prod_env()
    )
    tsar_requested_state = literal_eval(
        params["enable_support_user_access"].capitalize()
    )
    if is_prod_env() and tsar_curr_state is True:
        tsar_new_state = True
    else:
        tsar_new_state = tsar_requested_state

    if tsar_new_state is False and tsar_curr_state is True:
        from everstage_admin_backend.tsar import revoke_tsar_memberships_for_client

        revoke_tsar_memberships_for_client(client.client_id)

    client.client_features["enable_support_user_access"] = tsar_new_state
    #########################################

    client.client_features["enable_tsar_webapp_custom_roles"] = literal_eval(
        params["enable_tsar_webapp_custom_roles"].capitalize()
    )

    client.client_features["is_secure_admin_ui_auth0_user_mgmt"] = literal_eval(
        params["is_secure_admin_ui_auth0_user_mgmt"].capitalize()
    )

    client.client_features["enable_rounding_in_tier_functions"] = (
        client.client_features.get("enable_rounding_in_tier_functions", False)
    )
    client.client_features["show_g2_review_form"] = params["show_g2_review_form"]

    client.client_features["allow_only_admins_to_modify_user_name"] = literal_eval(
        params["allow_only_admins_to_modify_user_name"].capitalize()
    )
    client.client_features["show_observable_notification"] = literal_eval(
        params.get("show_observable_notification", "false").capitalize()
    )

    client.save()
    logger.info(f"END: Update client {id} and {client.name}")
    return client.client_id


def get_all_clients(projection=None):
    qs = Client.objects.filter(is_deleted=False)
    if projection:
        return list(qs.values(*projection))
    return list(qs)


def get_all_client_with_notification(projection=None):
    qs = Client.objects.filter(is_deleted=False, client_notification=True)
    if projection:
        return list(qs.values(*projection))
    return list(qs)


def is_global_sync_run(client_id):
    client_features = get_client_features(client_id)
    if "global_run" in client_features:
        return client_features["global_run"]

    else:
        return True


def save_client_features(client_id, client_features):
    client = Client.objects.get(client_id=client_id)
    client.client_features = client_features
    client.save()


def get_notifications(client_id):
    client_obj = (
        Client.objects.filter(is_deleted=False)
        .filter(client_id=client_id)
        .values("client_notification")
        .first()
    )
    if client_obj:
        return client_obj["client_notification"]
    else:
        return None


def get_admin_tasks(client_id):
    client_features = get_client_features(client_id)
    result = {}
    for task in ADMIN_NOTIFICATIONS:
        result[task] = client_features.get(task, DEFAULT_APPROVAL_NOTIFICATIONS)
    return result


def is_mfa_enabled_for_client(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get("mfa", {}).get("enabled", False)


def is_mfa_enabled_for_any_client(
    client_ids: list[int],
) -> tuple[bool, ClientMFAData | None]:
    """
    Check whether MFA has enabled for any of the clients the user is belong to
    (in multi clients case). Also returns the MFA data which will have the MFA enabled
    status and the MFA factors like "email", "sms" etc.
    """
    clients = get_clients_by_ids(client_ids)
    for client in clients:
        client_feature = client.client_features or {}
        if client_feature.get("mfa", {}).get("enabled", False):
            return True, client_feature.get("mfa", {})
    return False, None


def get_global_search_config(client_id: int) -> dict:
    """
    Get the global search configuration for the client.

    Args:
    - client_id: The client id of the client for which the global search is being used.

    Returns:
    - The global search configuration for the client.

    Sample response:
    {
        "enabled": True,
        "modules": {
            "payouts": True,
            "users": False,
        }
    }
    """
    return get_client_features(client_id).get(
        "global_search", {"enabled": False, "modules": {}}
    )


def set_global_search_config(client_id: int, config: dict) -> dict:
    """
    Update the global search configuration for the client.

    Args:
    - client_id: The client id of the client for which the global search is being used.
    - config: The new global search configuration for the client.

    Returns:
    - The updated global search configuration for the client.

    Sample response:
    {
        "enabled": True,
        "modules": {
            "payouts": True,
            "users": False,
        }
    }
    """
    return set_client_feature(client_id, "global_search", config)


def get_active_global_search_clients() -> list[dict]:
    """
    Get the list of active clients that have global search enabled.

    Args:
    - None

    Returns:
    - The list of active clients that have global search enabled.
    - Each client object contains the client_id and the global_search configuration.

    Sample response:
    [
        {
            "client_id": 10001,
            "config": {
                "enabled": True,
                "modules": {
                    "users": True,
                    "payouts": True,
                },
            },
        }
    ]
    """
    return list(
        Client.objects.filter(is_deleted=False)
        .filter(client_features__contains={"global_search": {"enabled": True}})
        .values(id=F("client_id"), config=F("client_features__global_search"))
    )


def get_tsar_enabled_clients() -> List[Dict]:
    """
    Fetches clients having support access enabled or clients. Flag not present means flag turned off.
    Returns:
        List: The list of clients.
    """
    return list(
        Client.objects.filter(is_deleted=False)
        .filter(Q(client_features__enable_support_user_access=True))
        .values("client_id", "name", "logo_url")
        .order_by("name")
    )


def is_tsar_enabled_for_client(client_id: int) -> bool:
    """
    Returns whether support access is enabled for the client through support-membership or not.
    If the flag is not present, treat this as False.
    """
    client = get_client(client_id)
    is_tsar_enabled = (client.client_features or {}).get(
        "enable_support_user_access", is_prod_env()
    )
    return bool(is_tsar_enabled)


def is_tsar_webapp_custom_roles_enabled_for_client(client_id: int) -> bool:
    """
    Returns whether tsar webapp custom roles is enabled for the client.
    If the flag is not present, treat this as False.
    """
    client_features = get_client_features(client_id) or {}
    return bool(client_features.get("enable_tsar_webapp_custom_roles", False))


def is_secure_admin_ui_auth0_user_mgmt_enabled_for_client(client_id: int) -> bool:
    """
    Returns whether secure admin UI Auth0 user management is enabled for the client.
    """
    client_features = get_client_features(client_id) or {}
    return bool(client_features.get("is_secure_admin_ui_auth0_user_mgmt", False))


def get_in_clause_strategy(client_id):
    client_features = get_client_features(client_id)
    return (client_features or {}).get(
        "in_clause_strategy",
        {
            "limit": 50000,
            "strategy": None,
        },
    )


def get_crystal_custom_calendar_future_periods(client_id):
    """
    Returns the number of future periods to be shown in the dropdown for the custom calendar payees.
    """
    client_features = get_client_features(client_id)
    return (client_features or {}).get(
        "crystal_custom_calendar_future_periods", CRYSTAL_CUSTOM_PERIODS_DEFAULT
    )


def get_client_settings(client_id: int) -> dict[str, dict]:
    client_settings = ClientSettings.objects.filter(client_id=client_id).first()
    if not client_settings:
        return {"icm_settings": {}, "cpq_settings": {}, "tqm_settings": {}}
    return {
        "icm_settings": client_settings.icm_settings or {},
        "cpq_settings": client_settings.cpq_settings or {},
        "tqm_settings": client_settings.tqm_settings or {},
    }


def update_tqm_settings(id: int, data: dict[str, dict[str, str]]) -> None:
    """
    To update the tqm_settings with the id of created entities like
    workspace, teams for the client_id
    """
    ClientSettings.objects.update_or_create(
        client_id=id, defaults={"tqm_settings": data}
    )


def get_client_meta_details(client_ids: list[int]) -> dict[int, dict[str, str | bool]]:
    """
    Get a dictionary mapping client IDs to their names and is_live status for the given client IDs.
    If a client is marked as deleted, is_live will be set to False.
    """
    # Fetch client details including name, meta_info, and is_deleted status
    client_data = Client.objects.filter(client_id__in=client_ids).values(
        "client_id", "name", "meta_info", "is_deleted"
    )

    # Construct the result dictionary
    result = {}
    for client in client_data:
        client_id = client["client_id"]
        name = client["name"]
        is_deleted = client["is_deleted"]

        # Determine the is_live, is_monitored status
        # If the client is deleted, set is_live, is_monitored to False
        # Otherwise, get the is_live, is_monitored status from meta_info, defaulting to False if not present
        is_live = False if is_deleted else client["meta_info"].get("is_live", False)
        is_monitored = (
            False if is_deleted else client["meta_info"].get("is_monitored", False)
        )

        # Add the client details to the result dictionary
        result[client_id] = {
            "name": name,
            "is_monitored": is_monitored,
            "is_live": is_live,
        }

    return result


def enable_monitoring_field(client_id):
    """
    client.meta_info["is_monitored"] field keeps track of cron schedules of client.
    Setting the flag in this function.
    """
    client = Client.objects.get(client_id=client_id)

    if client.meta_info is None:
        client.meta_info = {}

    client.meta_info["is_monitored"] = True

    client.save()

    logger.info("is_monitored flag successfully set.")


def disable_monitoring_field(client_id):
    """
    client.meta_info["is_monitored"] field keeps track of cron schedules of client.
    Resetting the flag in this function.
    """
    client = Client.objects.get(client_id=client_id)

    if client.meta_info is None:
        client.meta_info = {}

    client.meta_info["is_monitored"] = False

    client.save()

    logger.info("is_monitored flag successfully reset.")


def get_clients_by_exclusion_status(is_excluded: bool) -> dict[int, str]:
    """
    Fetches the clients based on the exclusion status.

    Args:
        is_excluded (bool): The exclusion status of the client.

    Returns:
        dict[int, str]: A mapping of client_id to client name.
    """
    clients = (
        Client.objects.annotate(
            is_excluded=Coalesce(
                Cast(F("meta_info__is_excluded"), BooleanField()),
                Value(False),
                output_field=BooleanField(),
            )
        )
        .filter(is_excluded=is_excluded)
        .values_list("client_id", "name")
    )

    id_name_map = dict(clients)

    return id_name_map


def enable_only_show_cron_marked_as_failed(client_id: int) -> bool:
    """
    Enables the feature flag to only show cron jobs marked as failed.
    """
    return set_client_feature(client_id, "only_show_cron_marked_as_failed", True)


def can_only_show_cron_marked_as_failed(client_id: int) -> bool:
    """
    Returns whether the client can only show cron jobs marked as failed.
    This is used to show the failed cron jobs in the show past activities tab
    that has been acknowledged by the admin and marked as failed.
    """
    client_features = get_client_features(client_id)
    return client_features.get("only_show_cron_marked_as_failed", False)


def get_payout_approvals_flag(client_id: int) -> bool:
    """
    Get the Payout Approvals flag for for the given client.
    """
    logger.info(f"Getting Payout Approvals flag for for client_id: {client_id}")
    client_features: Dict[str, Any] = get_client_features(client_id)
    is_payout_approvals_enabled: bool = (
        client_features.get("approval_config", {})
        .get("payout_approvals", {})
        .get(
            "enabled", True
        )  # Reason for returning true by default is, if for new client show approval features is enabled show payout approvals is true by default
    )
    logger.info(
        f"Payout Approvals flag for client_id {client_id}: {is_payout_approvals_enabled}"
    )

    return is_payout_approvals_enabled


def get_commission_plan_approvals_flag(client_id: int) -> bool:
    """
    Get the Commission Plan Approvals flag for for the given client.
    """
    logger.info(
        f"Getting Commission Plan Approvals flag for for client_id: {client_id}"
    )
    client_features: Dict[str, Any] = get_client_features(client_id)
    is_commission_plan_approvals_enabled: bool = (
        client_features.get("approval_config", {})
        .get("commission_plan_approvals", {})
        .get("enabled", False)
    )
    logger.info(
        f"Commission Plan Approvals flag for client_id {client_id}: {is_commission_plan_approvals_enabled}"
    )

    return is_commission_plan_approvals_enabled


def get_ui_access_feature_flags(client_id: int) -> dict:
    """
    Get the UI access feature flags for the given client.
    """
    client_features = get_client_features(client_id) or {}
    return {
        "enable_hris_integration": client_features.get(
            "enable_hris_integration", False
        ),
        "show_salesforce_integration": client_features.get(
            "show_salesforce_integration", True
        ),
        "show_approval_feature": client_features.get("show_approval_feature", False),
        "show_roles": client_features.get("show_roles", True),
        "show_forecast": client_features.get("show_forecast", False),
        "show_data_sources_v2": client_features.get("show_data_sources_v2", False),
        "enable_contract_permissions": client_features.get(
            "enable_contract_permissions", False
        ),
    }
