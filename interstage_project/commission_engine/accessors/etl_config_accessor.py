from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4

from django.db import transaction
from django.db.models import QuerySet
from django.forms.models import model_to_dict
from django.utils import timezone

from commission_engine.custom_types.self_service_integration_types import (
    FieldMappingType,
)
from commission_engine.models.etl_config_models import (
    AccessTokenConfig,
    ApiAccessConfig,
    EnrichmentConfig,
    ExtractionConfig,
    Integration,
    TransformationConfig,
    TransformationLogic,
)
from commission_engine.serializers.etl_config_serializers import (
    AccessTokenConfigSerializer,
    ApiAccessConfigSerializer,
    EnrichmentConfigSerializer,
    ExtractionConfigSerializer,
    IntegrationSerializer,
    TransformationConfigSerializer,
)
from commission_engine.utils import datetime
from commission_engine.utils.general_data import (
    service_access_token_url_map,
    service_access_type_map,
    service_payload_type_map,
)
from spm.utils import clone_object


class ExtractionConfigAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return ExtractionConfig.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time
        )

    def invalidate_records_by_integration_id(self, integration_id) -> None:
        ## Utility function to invalidate one or more records
        current_timestamp = timezone.now()
        if isinstance(integration_id, list):
            self.client_kd_aware().filter(integration_id__in=integration_id).update(
                knowledge_end_date=current_timestamp
            )
        else:
            self.get_record_by_integration_id(integration_id=integration_id).update(
                knowledge_end_date=current_timestamp
            )

    def get_object_for_integration_id(
        self, integration_id: UUID
    ) -> Optional[ExtractionConfig]:
        return self.client_kd_aware().filter(integration_id=integration_id).get()

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(knowledge_end_date__isnull=True)

    def get_records_by_access_token_config_ids(self, access_token_config_ids):
        return self.client_kd_aware().filter(
            access_token_config_id__in=access_token_config_ids, is_disabled=False
        )

    def get_all_extraction_configs(
        self, should_include_disabled_objects, integration_ids=None
    ):
        query_set = self.client_kd_aware()
        if integration_ids is not None:
            integration_ids = list(integration_ids)
            query_set = query_set.filter(integration_id__in=integration_ids)
        if should_include_disabled_objects:
            return list(query_set)
        return list(query_set.filter(is_disabled=False))

    def get_tasks_for_task_group(self, task_group, options=None):
        qs = self.client_kd_aware().filter(task_group=task_group, is_disabled=False)
        if options:
            for attr, value in options.items():
                if isinstance(value, list):
                    attr = attr + "__in"
                qs = qs.filter(**{attr: value})
        return list(qs)

    def get_selected_tasks_for_task_group(self, task_group, integration_ids):
        qs = self.client_kd_aware().filter(
            task_group=task_group, integration_id__in=integration_ids, is_disabled=False
        )
        return list(qs)

    def get_object_by_source(self, source_object_id):
        print("EXT SOURCE.. {}".format(source_object_id))
        qs = self.client_kd_aware()
        if isinstance(source_object_id, list):
            qs = qs.filter(source_object_id__in=source_object_id)
        else:
            qs = qs.filter(source_object_id=source_object_id)
        return list(qs)

    def get_record_by_integration_id(
        self,
        integration_id: Union[UUID, List[Union[UUID, str]]],
    ) -> QuerySet[ExtractionConfig]:
        """
        Utility function to get a single ExtractionConfig record by integration id.

        Args:
            integration_id (Union[UUID, list[UUID]]): Integration id of the record to be retrieved.

        Returns:
            ExtractionConfig
        """
        if isinstance(integration_id, list):
            return self.client_kd_aware().filter(integration_id__in=integration_id)
        return self.client_kd_aware().filter(integration_id=integration_id)

    def get_record_by_integration_id_with_filter(
        self,
        integration_id: UUID,
        projections: List[str],
    ) -> dict:
        """
        Utility function to get a single ExtractionConfig record by integration id and filter using column names.

        Args:
            integration_id (Union[UUID, list[UUID]]): Integration id of the record to be retrieved.
            projections (List[str]): List of columns need to be returned

        Returns:
            ExtractionConfig
        """
        result = (
            self.client_kd_aware()
            .filter(integration_id=integration_id)
            .values(*projections)
            .first()
        )

        return result

    def get_record_by_integration_id_and_access_token_id(
        self,
        integration_id: Union[UUID, List[Union[UUID, str]]],
        access_token_config_ids: List[int],
    ) -> QuerySet[ExtractionConfig]:
        """
        Utility function to get a single ExtractionConfig record by integration id.

        Args:
            integration_id (Union[UUID, list[UUID]]): Integration id of the record to be retrieved.
            access_token_config_ids (List[int]): Access token config id of the record to be retrieved.

        Returns:
            ExtractionConfig
        """
        if isinstance(integration_id, list):
            return self.client_kd_aware().filter(
                integration_id__in=integration_id,
                access_token_config_id__in=access_token_config_ids,
            )
        return self.client_kd_aware().filter(
            integration_id=integration_id,
            access_token_config_id__in=access_token_config_ids,
        )

    def update_sync_status_by_integration_id(self, integration_id, sync_status):
        self.get_record_by_integration_id(integration_id=integration_id).update(
            is_disabled=sync_status
        )

    def get_records_by_custom_object_id(
        self, custom_object_id: int
    ) -> QuerySet[ExtractionConfig]:
        """
        Utility function to get ExtractionConfig records by Custom Object ID.

        Args:
            custom_object_id (int): Custom object ID

        Returns:
            QuerySet[ExtractionConfig]
        """
        return self.client_kd_aware().filter(destination_object_id=custom_object_id)

    def get_records_by_access_token_config_id(
        self, access_token_config_id: int
    ) -> QuerySet[ExtractionConfig]:
        return self.client_kd_aware().filter(
            access_token_config_id=access_token_config_id
        )

    def update_access_token_config_id(
        self, old_access_token_config_id: int, new_access_token_config_id: int
    ) -> None:
        curr_time = timezone.now()
        old_records = self.get_records_by_access_token_config_id(
            access_token_config_id=old_access_token_config_id
        )

        new_records: list[ApiAccessConfig] = []

        for record in old_records:
            record.pk = None
            record.access_token_config_id = new_access_token_config_id
            new_records.append(record)

        old_records.update(knowledge_end_date=curr_time)

        ExtractionConfig.objects.bulk_create(new_records)

    def create_and_persist_record(self, fields: dict) -> None:
        current_timestamp = timezone.now()

        fields["client_id"] = self.client_id
        fields["knowledge_begin_date"] = current_timestamp

        ser = ExtractionConfigSerializer(data=fields)

        if not ser.is_valid():
            raise Exception("Invalid data")

        ser.save()

    def create_bulk_records(self, records: list):
        extraction_records = []
        for record in records:
            ser = ExtractionConfigSerializer(data=record)
            if not ser.is_valid():
                raise Exception("Invalid record")
            extraction_records.append(ser.validated_data)

        ExtractionConfig.objects.bulk_create(
            [ExtractionConfig(**record) for record in extraction_records]
        )

    def create_record(
        self,
        source_object_id: str,
        destination_object_id: str,
        integration_id: str,
        **kwargs,
    ):
        record = ExtractionConfig.objects.create(
            client_id=self.client_id,
            knowledge_begin_date=timezone.now(),
            priority=1,
            task_group="UPSTREAM_WRAPPER_SYNC",
            task=source_object_id.upper() + "_SYNC",
            source_object_id=source_object_id.lower(),
            destination_object_id=destination_object_id,
            sync_type=kwargs.get("sync_type", "changes"),
            destination_object_type=kwargs.get("destination_object_type", "custom"),
            integration_id=integration_id,
        )
        return record

    def get_destination_object_id(self, source_object_id):
        qs = self.client_kd_aware()
        if isinstance(source_object_id, list):
            qs = qs.filter(source_object_id__in=source_object_id)
        else:
            qs = qs.filter(source_object_id=source_object_id)
        return list(qs.values_list("destination_object_id", flat=True))

    def get_query_set_for_destination_object_ids(
        self, destination_object_ids: List[int]
    ) -> QuerySet[ExtractionConfig]:
        return self.client_kd_aware().filter(
            destination_object_id__in=destination_object_ids
        )


class ApiAccessConfigAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return ApiAccessConfig.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time
        )

    def invalidate_records_by_access_token_config_id(
        self, access_token_config_id: int
    ) -> None:
        current_timestamp = timezone.now()

        self.get_records_by_access_token_config_id(
            access_token_config_id=access_token_config_id
        ).update(knowledge_end_date=current_timestamp)

    def invalidate_records_by_integration_id(self, integration_id) -> None:
        ## Utility function to invalidate one or more records
        current_timestamp = timezone.now()
        if isinstance(integration_id, list):
            self.client_kd_aware().filter(integration_id__in=integration_id).update(
                knowledge_end_date=current_timestamp
            )
        else:
            self.get_record_by_integration_id(integration_id=integration_id).update(
                knowledge_end_date=current_timestamp
            )

    def invalidate_records_by_integration_id_and_source(
        self, integration_id: UUID, source_object_id: Union[str, list[str]]
    ) -> None:
        ## Utility function to invalidate one or more records based on integration ID and source object ID.
        current_timestamp = timezone.now()
        if isinstance(source_object_id, list):
            self.get_record_by_integration_id(integration_id=integration_id).filter(
                source_object_id__in=source_object_id
            ).update(knowledge_end_date=current_timestamp)
        else:
            self.get_record_by_integration_id(integration_id=integration_id).filter(
                source_object_id=source_object_id
            ).update(knowledge_end_date=current_timestamp)

    def update_access_token_config_id(
        self, old_access_token_config_id: int, new_access_token_config_id: int
    ) -> None:
        curr_time = timezone.now()
        old_records = self.get_records_by_access_token_config_id(
            access_token_config_id=old_access_token_config_id
        )

        new_records: list[ApiAccessConfig] = []

        for record in old_records:
            record.pk = None
            record.access_token_config_id = new_access_token_config_id
            new_records.append(record)

        old_records.update(knowledge_end_date=curr_time)

        ApiAccessConfig.objects.bulk_create(new_records)

    def delete_all_client_records(self):
        self.client_aware().delete()

    def get_all_api_access_configs(self):
        return list(self.client_kd_aware().filter(integration__isnull=False))

    def client_kd_aware(self):
        return self.client_aware().filter(knowledge_end_date__isnull=True)

    def get_obj_by_source_id(self, source_id):
        print("SR UD.. {}".format(source_id))
        qs = self.client_kd_aware()
        if isinstance(source_id, list):
            qs = qs.filter(source_object_id__in=source_id)
            return qs.all()
        else:
            qs = qs.filter(source_object_id=source_id)
        return qs.last()

    def get_obj_by_integration_id_and_source_id(self, integration_id, source_id):
        return (
            self.client_kd_aware()
            .filter(integration_id=integration_id)
            .filter(source_object_id=source_id)
            .last()
        )

    def invalidate_record_by_integration_id_and_source_id(
        self, integration_id, source_id
    ):
        current_timestamp = timezone.now()
        self.client_kd_aware().filter(integration_id=integration_id).filter(
            source_object_id=source_id
        ).update(knowledge_end_date=current_timestamp)

    def get_record_by_integration_id(self, integration_id: UUID):
        ## Utility function to get a single Integration record by integration_id.
        return self.client_kd_aware().filter(integration_id=integration_id)

    def get_records_by_access_token_config_id(
        self, access_token_config_id: Union[int, list[int]]
    ) -> QuerySet[ApiAccessConfig]:
        """
        Utility function to get `ApiAccessConfig` records based on the
        provided `access_token_config_id`.
        """
        if isinstance(access_token_config_id, list):
            return self.client_kd_aware().filter(
                access_token_config_id__in=access_token_config_id
            )
        return self.client_kd_aware().filter(
            access_token_config_id=access_token_config_id
        )

    def create_and_persist_record(self, fields: dict) -> None:
        current_time = timezone.now()

        fields["client_id"] = self.client_id
        fields["knowledge_begin_date"] = current_time

        ser = ApiAccessConfigSerializer(data=fields)

        if not ser.is_valid():
            print(f"FIELDS: {fields}")
            print(f"ERRORS: {ser.errors}")
            raise Exception("Invalid data")

        ser.save()

    def persist_record(self, record):
        record.pk = None
        record.save()

    def create_bulk_records(self, records: list):
        api_access_records = []
        for record in records:
            ser = ApiAccessConfigSerializer(data=record)
            if not ser.is_valid():
                raise Exception("Invalid record")
            api_access_records.append(ser.validated_data)

        ApiAccessConfig.objects.bulk_create(
            [ApiAccessConfig(**record) for record in api_access_records]
        )

    def create_record(
        self,
        source_object_id: str,
        request_url: str,
        request_type: str,
        request_body: dict,
        request_header: dict,
        access_token_config_id: str,
        additional_data: dict,
        response_key: str,
        integration_id: str,
        integration: str,
    ) -> ApiAccessConfig:
        # Create new record with params
        record: ApiAccessConfig = ApiAccessConfig.objects.create(
            client_id=self.client_id,
            knowledge_begin_date=timezone.now(),
            source_object_id=source_object_id,
            request_url=request_url,
            request_type=request_type,
            request_body=request_body,
            request_header=request_header,
            access_token_config_id=access_token_config_id,
            additional_data=additional_data,
            response_key=response_key,
            integration_id=integration_id,
            integration=integration,
        )

        # Save the record to DB
        # record.save()

        # Return record
        return record


class TransformationConfigAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return TransformationConfig.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(knowledge_end_date__isnull=True)

    def get_all_transformation_configs(self):
        return list(self.client_kd_aware())

    def invalidate_records_by_integration_id(self, integration_id) -> None:
        ## Utility function to invalidate one or more records
        current_timestamp = timezone.now()
        if isinstance(integration_id, list):
            self.client_kd_aware().filter(integration_id__in=integration_id).update(
                knowledge_end_date=current_timestamp
            )
        else:
            self.get_records_by_integration_id(integration_id=integration_id).update(
                knowledge_end_date=current_timestamp
            )

    def invalidate_record(
        self,
        integration_id: UUID,
        source_field: str,
        curr_time: datetime.datetime,
    ) -> None:
        """
        Utility function to invalidate a single TransformationConfig record.

        Args:
            transormation_config_id (int): Id of the record to be invalidated.
            integration_id (UUID): Integration id of the record to be invalidated.
            curr_time (datetime.datetime): Timestamp of invalidation.
        """
        record = self.get_record_by_integration_id_and_source_field(
            integration_id, source_field
        )

        if len(record) == 0:
            raise Exception("Record not found")

        record.update(knowledge_end_date=curr_time)

    def get_objs_by_source(self, source_object_id):
        print("TRANS SOU {}".format(source_object_id))
        qs = self.client_kd_aware()
        if isinstance(source_object_id, list):
            qs = qs.filter(source_object_id__in=source_object_id)
        else:
            qs = qs.filter(source_object_id=source_object_id)
        return list(qs)

    def get_records_by_source_fields(
        self, integration_id: str, source_fields: List[str]
    ) -> QuerySet[TransformationConfig]:
        return self.client_kd_aware().filter(
            integration_id=integration_id, source_field__in=source_fields
        )

    def get_record_by_id(
        self, transformation_config_id: int
    ) -> QuerySet[TransformationConfig]:
        """
        Utility function to get a single TransformationConfig record by id.

        Args:
            transformation_config_id (int): Id of the record to be retrieved.

        Returns:
            Queryset[TransformationConfig]
        """
        return self.client_kd_aware().filter(id=transformation_config_id)

    def get_record_by_integration_id_and_source_field(
        self, integration_id: UUID, source_field: str
    ) -> QuerySet[TransformationConfig]:
        """
        Utility function to get TransformationConfig records by Integration ID and Source field.

        Args:
            integration_id (UUID): Integration ID
            source_field (str): Source field

        Returns:
            QuerySet[TransformationConfig]
        """
        return self.get_records_by_integration_id(integration_id=integration_id).filter(
            source_field=source_field
        )

    def get_records_by_integration_id(
        self, integration_id: UUID
    ) -> QuerySet[TransformationConfig]:
        """
        Utility function to get TransformationConfig records by Integration ID.

        Args:
            integration_id (UUID): Integration ID

        Returns:
            QuerySet[TransformationConfig]
        """
        return self.client_kd_aware().filter(integration_id=integration_id)

    def get_records_by_custom_object_id(
        self, custom_object_id: int
    ) -> QuerySet[TransformationConfig]:
        """
        Utility function to get TransformationConfig records by Custom Object ID.

        Args:
            integration_id (UUID): Integration ID

        Returns:
            QuerySet[TransformationConfig]
        """
        return self.client_kd_aware().filter(destination_object_id=custom_object_id)

    @transaction.atomic
    def update_record(
        self, integration_id: UUID, source_field: str, updated_fields: dict
    ) -> None:
        """
        Utility function to update a single TransformationConfig record.

        Args:
            integration_id (UUID): Integration ID
            source_field (str): Source field
            updated_fields (dict): Record's fields to be updated.

        Raises:
            Exception: If record with given id is not found.

        Returns:
            None
        """
        current_time = timezone.now()

        existing_record = self.get_record_by_integration_id_and_source_field(
            integration_id, source_field
        ).first()

        if existing_record is None:
            raise Exception("Record not found")

        updated_record = clone_object(current_object=existing_record, curr_time=current_time, request_audit=None)  # type: ignore

        self.invalidate_record(
            integration_id=existing_record.integration_id,
            source_field=source_field,
            curr_time=current_time,
        )

        ser = TransformationConfigSerializer(data=updated_record.__dict__)

        ser.update(instance=updated_record, validated_data=updated_fields)

    def get_obj_by_source_and_destination(self, source, destination):
        qs = self.client_kd_aware().filter(
            source_object_id=source, destination_object_id=destination
        )
        return list(qs)

    def get_objects_by_integration_id(self, integration_id):
        qs = self.client_kd_aware()
        if isinstance(integration_id, list):
            qs = qs.filter(integration_id__in=integration_id)
        else:
            qs = qs.filter(integration_id=integration_id)
        return list(qs)

    @transaction.atomic
    def bulk_create_and_persist_record(self, mappings: list[dict]) -> None:
        for mp in mappings:
            fields = mp
            fields["additional_config"] = (
                {"associated_object_id": mp["associated_object_id"]}
                if mp["is_association"]
                else fields.get("additional_config")
            )
            self.create_and_persist_record(fields=mp)

    def create_and_persist_record(self, fields: dict) -> None:
        """
        Utility function to create and save a TransformationConfig record.

        Args:
            fields (dict): Required TransformationConfig fields.

        Returns:
            None
        """
        current_time = timezone.now()

        fields["client_id"] = self.client_id
        fields["knowledge_begin_date"] = current_time

        ser = TransformationConfigSerializer(data=fields)

        if not ser.is_valid():
            raise Exception("Invalid data")

        ser.save()

    def bulk_create_and_persist_records(
        self,
        source_object_id: str,
        destination_object_id: int,
        integration_id: UUID,
        mappings: list[FieldMappingType],
    ) -> None:
        for mp in mappings:
            fields = {
                "source_object_id": source_object_id.lower(),
                "destination_object_id": destination_object_id,
                "integration_id": integration_id,
                "source_field": mp.get("source_field"),
                "destination_field": mp.get("destination_field"),
                "field_type": mp.get("field_type"),
                "additional_config": mp.get("additional_config"),
            }

            self.create_and_persist_record(fields=fields)

    def create_bulk_records(self, records: list):
        transformation_records = []
        for record in records:
            ser = TransformationConfigSerializer(data=record)
            if not ser.is_valid():
                raise Exception("Invalid record")
            transformation_records.append(ser.validated_data)

        TransformationConfig.objects.bulk_create(
            [TransformationConfig(**record) for record in transformation_records]
        )

    def create_record(
        self,
        source_object_id: str,
        destination_object_id: str,
        source_field: str,
        destination_field: str,
        field_type: str,
        integration_id: str,
    ) -> TransformationConfig:
        # Create new record with params
        record: TransformationConfig = TransformationConfig.objects.create(
            client_id=self.client_id,
            knowledge_begin_date=timezone.now(),
            source_object_id=source_object_id,
            destination_object_id=destination_object_id,
            source_field=source_field,
            destination_field=destination_field,
            field_type=field_type,
            integration_id=integration_id,
        )

        # Save the record to DB
        # if save == True:
        # record.save()

        # Return record
        return record

    def invalidate_mapping(self, integration_id, source_field, knowledge_date):
        if isinstance(source_field, list):
            self.client_aware().filter(
                integration_id=integration_id, source_field__in=source_field
            ).update(knowledge_end_date=knowledge_date)
        else:
            self.client_aware().filter(
                integration_id=integration_id, source_field=source_field
            ).update(knowledge_end_date=knowledge_date)

    def get_object(self, integration_id, source_field):
        qs = (
            self.client_kd_aware()
            .filter(integration_id=integration_id, source_field=source_field)
            .last()
        )
        return qs

    def persist_record(self, record):
        record.pk = None
        record.save()

    def get_record_by_destination_fields(
        self, integration_id: UUID, destination_fields: List[str]
    ) -> List[str]:
        return list(
            self.client_kd_aware()
            .filter(
                integration_id=integration_id, destination_field__in=destination_fields
            )
            .values_list("source_field", flat=True)
        )

    def get_transformation_config_by_integration_id(
        self, integration_id: UUID, projections: List[str]
    ) -> list[dict]:
        query_set = self.client_kd_aware().filter(integration_id=integration_id)
        if projections:
            return list(query_set.values(*projections))
        return list(query_set.values())


class TransformationLogicAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return TransformationLogic.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(knowledge_end_date__isnull=True)

    def get_object_by_id(self, logic_id):
        qs = self.client_kd_aware().filter(id=logic_id).last()
        return qs

    def invalidate_object(
        self, transformation_logic_id: int, timestamp: datetime.datetime
    ) -> None:
        self.client_kd_aware().filter(id=transformation_logic_id).update(
            knowledge_end_date=timestamp
        )


class AccessTokenConfigAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return AccessTokenConfig.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(knowledge_end_date__isnull=True)

    def get_object_by_id(self, token_config_id):
        qs = (
            self.client_kd_aware().filter(access_token_config_id=token_config_id).last()
        )
        return qs

    def get_object_by_connection_type(self, connection_type):
        return self.client_kd_aware().filter(connection_type=connection_type)

    def get_record_by_id(
        self,
        access_token_config_id: int,
        client_aware: bool = True,  # noqa: FBT002, FBT001
    ) -> QuerySet[AccessTokenConfig] | AccessTokenConfig:
        if not client_aware:
            return AccessTokenConfig.objects.filter(
                access_token_config_id=access_token_config_id,
                knowledge_end_date__isnull=True,
            ).first()
        return self.client_kd_aware().filter(
            access_token_config_id=access_token_config_id
        )

    def get_object_by_client_id_desc(self):
        return self.client_kd_aware().order_by("knowledge_begin_date").reverse()

    def get_object_for_id(
        self, access_token_config_id: int
    ) -> Optional[AccessTokenConfig]:
        return (
            self.client_kd_aware()
            .filter(access_token_config_id=access_token_config_id)
            .get()
        )

    def get_all_records(self) -> QuerySet[AccessTokenConfig]:
        """
        Utility function to get all AccessTokenConfig records.

        Returns:
            QuerySet[AccessTokenConfig]
        """
        return self.client_kd_aware()

    def get_records_by_service_name(
        self, service_name: str
    ) -> QuerySet[AccessTokenConfig]:
        """
        Utility function to get all AccessTokenConfig records that match the provided service name.

        Args:
        service_name (str): The service name to filter by.

        Returns:
            QuerySet[AccessTokenConfig]
        """
        return self.client_kd_aware().filter(service_name=service_name)

    def invalidate_record(
        self,
        access_token_config_id: int,
        curr_time: datetime.datetime,
    ) -> None:
        """
        Utility function to invalidate a single TransformationConfig record.

        Args:
            transormation_config_id (int): Id of the record to be invalidated.
            integration_id (UUID): Integration id of the record to be invalidated.
            curr_time (datetime.datetime): Timestamp of invalidation.
        """
        record = self.get_record_by_id(access_token_config_id=access_token_config_id)

        if len(record) == 0:
            raise Exception("Record not found")

        record.update(knowledge_end_date=curr_time)

    def create_and_persist_record(self, fields: dict):
        """
        Utility function to create and save a AccessTokenConfig record.

        Args:
            fields (dict): Required AccessTokenConfig fields.

        Returns:
            None
        """
        current_time = timezone.now()

        fields["client_id"] = self.client_id
        fields["knowledge_begin_date"] = current_time

        service_name = fields["service_name"]

        if service_name == "salesforce":
            salesforce_oauth_env = fields["additional_data"].get("salesforce_env")
            fields["access_token_url"] = (
                f"https://{'test' if salesforce_oauth_env == 'sandbox' else 'login'}.salesforce.com/services/oauth2/token"
            )
            grant_type = fields["access_request_body"]["grant_type"]

            if grant_type == "refresh_token":
                fields["access_type"] = "REFRESH"
            else:
                fields["access_type"] = "UN-PWD"

        elif service_name == "hubspot":
            fields["access_type"] = service_access_type_map[service_name]
            fields["access_token_url"] = service_access_token_url_map[service_name]
        elif service_name == "zoho":
            fields["access_type"] = service_access_type_map[service_name]

        fields["payload_type"] = service_payload_type_map[service_name]

        ser = AccessTokenConfigSerializer(data=fields)

        if not ser.is_valid():
            raise Exception("Invalid data")

        ser.save()

    def update_record_returning_id(
        self, access_token_config_id: int, updated_fields: dict
    ) -> int:
        """
        Utility function to update a single AccessTokenConfig record.

        Args:
            access_token_config_id (UUID): AccessTokenConfig ID
            updated_fields (dict): Record's fields to be updated.

        Raises:
            Exception: If record with given id is not found.

        Returns:
            None
        """
        current_time = timezone.now()

        existing_record = self.get_record_by_id(
            access_token_config_id=access_token_config_id
        ).first()

        if existing_record is None:
            raise Exception("Record not found")

        updated_record = clone_object(current_object=existing_record, curr_time=current_time, request_audit=None)  # type: ignore

        self.invalidate_record(
            access_token_config_id=access_token_config_id,
            curr_time=current_time,
        )

        ser = AccessTokenConfigSerializer(data=updated_record.__dict__)

        new_atc = ser.update(instance=updated_record, validated_data=updated_fields)

        return new_atc.access_token_config_id

    def persist_record(self, record):
        record.pk = None
        record.save()

    def create_record(
        self,
        service_name: str,
        access_type: str,
        payload_type: str | None,
        access_token_url: str | None,
        domain: str | None,
        connection_name: str,
        access_request_body: dict | None = None,
        additional_data: dict | None = None,
        **kwargs,
    ) -> AccessTokenConfig:
        """
        Description:
            Utility function to create a new record in access_token_config.
        Parameters:
            - service_name (required): str.
            - access_type (required): str.
            - access_token_url: str.
            - access_request_body: dict[str, str].
            - domain: str.
        Returns:
            Instance of AccessTokenConfig
        Exceptions:
            None
        """

        record: AccessTokenConfig = AccessTokenConfig.objects.create(
            client_id=self.client_id,
            knowledge_begin_date=timezone.now(),
            service_name=service_name,
            access_type=access_type,
            payload_type=payload_type,
            access_token_url=access_token_url,
            api_access_key=kwargs.get("api_access_key"),
            access_request_body=access_request_body,
            domain=domain,
            connection_name=connection_name,
            additional_data=additional_data,
        )

        # Save the record to DB
        record.save()

        # Return record
        return record

    def update_access_token_connection_name(
        self,
        access_token_config_id,
        connection_name,
    ):
        self.client_kd_aware().filter(
            access_token_config_id=access_token_config_id
        ).update(connection_name=connection_name)

    def update_access_token_connection_type(
        self,
        access_token_config_id,
        connection_type,
    ):
        self.client_kd_aware().filter(
            access_token_config_id=access_token_config_id
        ).update(connection_type=connection_type)

    def update_access_token_additional_data(
        self,
        access_token_config_id,
        additional_data,
    ):
        self.client_kd_aware().filter(
            access_token_config_id=access_token_config_id
        ).update(additional_data=additional_data)

    def update_access_request_body_and_status(
        self,
        access_token_config_id,
        access_request_body,
        status=None,
    ):
        if status is None:
            self.client_kd_aware().filter(
                access_token_config_id=access_token_config_id
            ).update(access_request_body=access_request_body)
        else:
            self.client_kd_aware().filter(
                access_token_config_id=access_token_config_id
            ).update(
                access_request_body=access_request_body,
                connection_status=status,
            )


class EnrichmentConfigAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return EnrichmentConfig.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(knowledge_end_date__isnull=True)

    def get_enrichments_by_source_object(self, source_object):
        qs = (
            self.client_kd_aware()
            .filter(enrichment_type__isnull=True)
            .filter(source_object_id=source_object)
        )
        return list(qs)

    def get_enrichments_by_integration_id(
        self, integration_id: UUID
    ) -> List[EnrichmentConfig]:
        """
        Utility function to get all Enrichment configs for a given integration id.
        """
        query_set = self.client_kd_aware().filter(
            enrichment_type__isnull=True, integration_id=integration_id
        )
        return list(query_set)

    def get_api_enrichments_by_integration_id(
        self, integration_id: UUID
    ) -> List[EnrichmentConfig]:
        """
        Utility function to get all API Enrichment configs for a given integration id.
        """
        query_set = self.client_kd_aware().filter(
            enrichment_type="api", integration_id=integration_id
        )
        return list(query_set)

    def get_api_enrichments_by_source_objects(self, source_object):
        qs = (
            self.client_kd_aware()
            .filter(enrichment_type="api")
            .filter(source_object_id=source_object)
        )
        return list(qs)

    def create_and_persist_record(self, fields: dict) -> str:
        current_timestamp = timezone.now()

        fields["client_id"] = self.client_id
        fields["knowledge_begin_date"] = current_timestamp

        ser = EnrichmentConfigSerializer(data=fields)

        if not ser.is_valid():
            raise Exception("Invalid data")

        ser.save()

    def check_enrichment_exists(self, source_object_id, associated_object_id) -> bool:
        return (
            self.client_kd_aware()
            .filter(
                source_object_id=source_object_id,
                additional_data={"associated_object_id": associated_object_id},
            )
            .exists()
        )

    def get_records_by_source_object_id(self, source_object_id: str) -> QuerySet:
        return self.client_kd_aware().filter(source_object_id=source_object_id)

    def invalidate_record_by_integration_id_and_api_config(
        self, integration_id: UUID, api_config: str
    ) -> None:
        current_timestamp = timezone.now()
        self.client_kd_aware().filter(
            integration_id=integration_id,
            ref_api_config_obj=api_config,
        ).update(knowledge_end_date=current_timestamp)


class IntegrationAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return Integration.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time
        )

    def invalidate_record(self, integration_id):
        ## Utility function to invalidate one or more records
        current_timestamp = timezone.now()
        if isinstance(integration_id, list):
            self.client_kd_aware().filter(integration_id__in=integration_id).update(
                knowledge_end_date=current_timestamp
            )
        else:
            self.get_record_by_integration_id(integration_id=integration_id).update(
                knowledge_end_date=current_timestamp
            )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_aware(self):
        return self.client_aware().filter(knowledge_end_date__isnull=True)

    def get_records_by_destination_object_ids(
        self, destination_object_ids: List[int]
    ) -> QuerySet[Integration]:
        return self.client_kd_aware().filter(
            destination_object_id__in=destination_object_ids
        )

    def get_all_records(self) -> list[Integration]:
        """
        Utility function to get all Integration records.

        Returns:
            list[Integration]
        """
        return list(self.client_kd_aware())

    def get_record_by_integration_id(
        self, integration_id: UUID
    ) -> QuerySet[Integration]:
        """
        Utility function to get a single Integration record by integration_id.

        Args:
            integration_id (int): Integration id of the record to be retrieved.

        Returns:
            Integration
        """
        return self.client_kd_aware().filter(integration_id=integration_id)

    def get_integration_by_integration_id(self, integration_id):
        if isinstance(integration_id, list):
            return self.client_kd_aware().filter(integration_id__in=integration_id)
        qs = self.client_kd_aware().filter(integration_id=integration_id)
        if qs.exists():
            qs = qs.last()
        return qs

    def get_record_by_source_object_id(self, source_object_id):
        return self.client_kd_aware().filter(source_object_id=source_object_id)

    def create_and_persist_record(self, fields: dict) -> UUID:
        current_timestamp = timezone.now()

        fields["client_id"] = self.client_id
        fields["knowledge_begin_date"] = current_timestamp
        fields["integration_id"] = fields.get("integration_id", uuid4())

        ser = IntegrationSerializer(data=fields)

        if not ser.is_valid():
            raise Exception("Invalid data")

        ser.save()

        return fields["integration_id"]

    def create_bulk_records(self, records: list):
        integration_records = []
        for record in records:
            ser = IntegrationSerializer(data=record)
            if not ser.is_valid():
                raise Exception("Invalid record")
            integration_records.append(ser.validated_data)

        Integration.objects.bulk_create(
            [Integration(**record) for record in integration_records]
        )

    def create_record(
        self,
        service_name: str,
        name: str,
        desc: str,
        source_object_id: str,
        is_api: bool,
    ) -> Integration:
        """
        Utility function to create a new Integration record.
        Args:
            service_name (str): Service name of the integration.
            name (str): Integration name.
            desc (str): Integration description.
            source_object_id (str): Source object ID.
        Returns:
            An instance of Integration.
        """

        # Create new record with params
        record: Integration = Integration.objects.create(
            client_id=self.client_id,
            knowledge_begin_date=timezone.now(),
            service_name=service_name,
            name=name,
            desc=desc,
            source_object_id=source_object_id,
            is_api=is_api,
        )

        # Save the record to DB
        # record.save()

        # Return record
        return record

    def persist_record(self, record):
        record.pk = None
        record.save()

    def get_integration_by_service_name(
        self, service_name, projection=None
    ) -> List[Union[Dict[str, Any], Any]]:
        qs = self.client_kd_aware().filter(service_name=service_name)
        if projection:
            return list(qs.values(*projection))
        return list(qs)

    def get_batch_etl_page_size_for_integration(self, integration_id: UUID) -> int:
        record = self.get_record_by_integration_id(
            integration_id=integration_id
        ).first()

        if record is None or record.batch_etl_page_size is None:
            return 50000

        return record.batch_etl_page_size

    def update_prev_snapshot_table_name(
        self,
        integration_id: UUID,
        new_table_name: str,
        current_timestamp: datetime.datetime,
    ) -> None:
        record = self.get_object_by_integration_id(integration_id=integration_id)

        if not record:
            return

        record.knowledge_end_date = current_timestamp
        record.save()

        new_record = clone_object(
            current_object=record, curr_time=current_timestamp, request_audit=None
        )

        new_record.preprocessing_metadata["prev_snapshot_table_name"] = new_table_name
        new_record.knowledge_end_date = None
        new_record.save()

    def get_object_by_integration_id(
        self, integration_id: UUID
    ) -> Optional[Integration]:
        return self.client_kd_aware().filter(integration_id=integration_id).get()

    def get_integration_by_custom_object_id(self, custom_object_id: int) -> Integration:
        return self.client_kd_aware().get(destination_object_id=custom_object_id)

    def update_integration_additional_data(
        self, integration_id: UUID, additional_data: dict
    ):
        current_timestamp = timezone.now()

        with transaction.atomic():
            existing_record = (
                self.client_aware().filter(integration_id=integration_id).first()
            )
            if not existing_record:
                raise Exception(
                    f"Integration record not found for id: {integration_id}"
                )

            self.invalidate_record(integration_id)

            # Convert model to dict excluding unwanted fields
            record_dict = model_to_dict(
                existing_record,
                exclude=[
                    "id",
                    "knowledge_begin_date",
                    "knowledge_end_date",
                    "additional_data",
                ],
            )

            # Create new record with updated timestamps and additional data
            new_record = Integration(
                **record_dict,
                knowledge_begin_date=current_timestamp,
                knowledge_end_date=None,
                additional_data=additional_data,
            )
            new_record.save()
