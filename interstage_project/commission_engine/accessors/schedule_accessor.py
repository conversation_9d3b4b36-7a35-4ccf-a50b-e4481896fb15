import json
import logging
import re
from datetime import datetime
from typing import List
from zoneinfo import ZoneInfo

from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Q
from django_celery_beat.models import CrontabSchedule, PeriodicTask

from commission_engine.models.hard_delete_models import HardDeletePeriodicTask
from commission_engine.models.schedule_models import (
    ETLTask,
    MilestoneNotificationStatus,
    NotificationTask,
)
from commission_engine.utils.general_data import Freq

logger = logging.getLogger(__name__)


class NotificationTaskAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return NotificationTask.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_latest_deleted_aware()
            .filter(knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def client_latest_deleted_aware(self):
        qs = self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )
        return qs

    def get_payees_for_task(self, kd, task):
        qs = self.client_kd_deleted_aware(kd)
        if isinstance(task, list):
            qs = qs.filter(task__in=task)
        else:
            qs = qs.filter(task=task)
        return list(qs)

    def get_payees_for_task_schedule_id(self, kd, task, cron_id):
        qs = self.client_kd_deleted_aware(kd).filter(cron_id=cron_id)
        if isinstance(task, list):
            qs = qs.filter(task__in=task)
        else:
            qs = qs.filter(task=task)
        return list(qs)

    def get_tasks_by_cron_id(self, cron_id, as_dicts=True):
        qs = self.client_latest_deleted_aware().filter(cron_id=cron_id)
        return list(qs.values()) if as_dicts else list(qs)

    def get_payee_emails_by_task(self, task, cron_id):
        qs = self.client_latest_deleted_aware().filter(task=task, cron_id=cron_id)
        return qs.values_list("employee_email_id", flat=True)

    def invalidate_all_task(self, email_id, kd):
        self.client_latest_deleted_aware().filter(employee_email_id=email_id).update(
            knowledge_end_date=kd, is_deleted=True
        )

    def invalidate_all_task_for_employees(self, kd, employee_emails):
        self.client_latest_deleted_aware().filter(
            employee_email_id__in=employee_emails
        ).update(knowledge_end_date=kd, is_deleted=True)

    def invalidate_payee_task(self, email_id, task_name, kd):
        self.client_latest_deleted_aware().filter(
            employee_email_id=email_id, task=task_name
        ).update(knowledge_end_date=kd, is_deleted=True)

    def invalidate_emp_task(self, email_id, task_name, kd):
        self.client_latest_deleted_aware().filter(
            employee_email_id=email_id,
            task=task_name,
        ).update(knowledge_end_date=kd, is_deleted=True)

    def invalidate_all_emp_task_with_role(self, email_id, kd):
        self.client_latest_deleted_aware().filter(employee_email_id=email_id).update(
            knowledge_end_date=kd, is_deleted=True
        )

    def invalidate_specified_task_for_payees(self, email_ids, tasks, kd):
        return (
            self.client_latest_deleted_aware()
            .filter(employee_email_id__in=email_ids, task__in=tasks)
            .update(knowledge_end_date=kd, is_deleted=True)
        )

    def get_tasks_for_payee(self, email_id: str, as_dicts: bool = False):
        qs = self.client_latest_deleted_aware().filter(employee_email_id=email_id)
        return list(qs.values()) if as_dicts else list(qs)

    def get_tasks_for_payee_as_obj(self, email_id):
        qs = self.client_latest_deleted_aware().filter(employee_email_id=email_id)
        return list(qs)

    def get_task_by_name_for_employee(self, task_name, email):
        task = (
            self.client_latest_deleted_aware()
            .filter(employee_email_id=email, task=task_name)
            .first()
        )
        return task

    def get_task_by_name_for_role(self, task_name, email):
        task = (
            self.client_latest_deleted_aware()
            .filter(employee_email_id=email, task=task_name)
            .first()
        )
        return task

    def get_payee_frequency_by_task(self, task_name, email):
        task = self.client_latest_deleted_aware().filter(
            employee_email_id=email, task=task_name
        )
        return list(task.values("frequency"))

    def get_specified_tasks_for_payee(self, email_id, task):
        query_set = self.client_latest_deleted_aware().filter(
            employee_email_id=email_id
        )
        if isinstance(task, list):
            query_set = query_set.filter(task__in=task)
        else:
            query_set = query_set.filter(task=task)
        return list(query_set)

    def get_all_payees_email_by_tasks(
        self, task_name, logged_in_email, employee_email_id
    ):
        """
        filtering list of payee's for which the particular task is enables from the employee_email_id list excluding the logger email id
        """
        task = (
            self.client_latest_deleted_aware()
            .filter(employee_email_id__in=employee_email_id)
            .filter(task=task_name)
            .exclude(employee_email_id=logged_in_email)
        )
        if task.exists():
            email_list = [
                entry["employee_email_id"] for entry in task.values("employee_email_id")
            ]
            return email_list
        else:
            # Handle the case where the queryset is empty
            return []

    def get_filtered_payees_by_task(self, task_name, employee_email_id):
        """filtering list of payee's for which the particular task is enables from the employee_email_id list"""
        task = (
            self.client_latest_deleted_aware()
            .filter(employee_email_id__in=employee_email_id)
            .filter(task=task_name)
        )
        if task.exists():
            email_list = [
                entry["employee_email_id"] for entry in task.values("employee_email_id")
            ]
            return email_list
        else:
            # Handle the case where the queryset is empty
            return []

    def get_notifification_tasks_by_task_name(
        self, task_name: str, exclude_email: str | None = None
    ):
        """Get notification tasks given the task name. Optionally exclude a specific email"""
        qs = self.client_latest_deleted_aware().filter(task=task_name)
        if exclude_email:
            qs = qs.exclude(employee_email_id=exclude_email)
        return list(qs)

    @staticmethod
    def get_clients_given_cron_id(cron_id):
        """Get client IDs for which the notification cron job is scheduled"""
        qs = (
            NotificationTask.objects.filter(
                cron_id=cron_id, is_deleted=False, knowledge_end_date__isnull=True
            )
            .values_list("client_id", flat=True)
            .distinct()
        )
        return list(qs)

    def get_all_payees_by_tasks(self, task_name, logged_in_email):
        task = (
            self.client_latest_deleted_aware()
            .filter(task=task_name)
            .exclude(employee_email_id=logged_in_email)
        )
        return task


class ETLTaskAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return ETLTask.objects.filter(client_id=self.client_id)

    def invalidate_all_client_records(self, time):
        self.client_aware().filter(knowledge_end_date__isnull=True).update(
            knowledge_end_date=time, is_deleted=True
        )

    def delete_all_client_records(self):
        self.client_aware().delete()

    def client_kd_deleted_aware(self, knowledge_date):
        return (
            self.client_aware()
            .filter(is_deleted=False, knowledge_begin_date__lte=knowledge_date)
            .filter(
                Q(knowledge_end_date__gt=knowledge_date)
                | Q(knowledge_end_date__isnull=True)
            )
        )

    def client_latest_deleted_aware(self):
        qs = self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )
        return qs

    def get_entries_for_schedule_id(self, kd, pt_id):
        qs = self.client_kd_deleted_aware(kd).filter(periodic_task_id=pt_id)
        return list(qs)

    def get_entries_for_task_schedule_id(self, kd, task, pt_id):
        qs = self.client_kd_deleted_aware(kd).filter(periodic_task_id=pt_id)
        if isinstance(task, list):
            qs = qs.filter(task__in=task)
        else:
            qs = qs.filter(task=task)
        return list(qs)


class PeriodicTaskAccessor:
    def __init__(self):
        pass

    def get_or_create_task(
        self, name, task, args, cron_tab, queue, routing_key, kwargs=None
    ):
        if not kwargs:
            kwargs = {}
        return PeriodicTask.objects.get_or_create(
            name=name,
            args=json.dumps(args),
            task=task,
            crontab=cron_tab,
            kwargs=json.dumps(kwargs),
            crontab_id=cron_tab.id,
            queue=queue,
            routing_key=routing_key,
        )[0]

    def does_task_exist(self, task_key):
        return PeriodicTask.objects.filter(name=task_key).exists()

    def bulk_create_tasks(self, tasks: list) -> None:
        periodic_tasks = [PeriodicTask(**task) for task in tasks]
        PeriodicTask.objects.bulk_create(periodic_tasks)

    def update_task_details_by_name(
        self, name, status, date_changed, cron_tab, description
    ):
        task = PeriodicTask.objects.get(name=name)
        task.enabled = status
        task.date_changed = date_changed
        task.crontab_id = cron_tab.id
        task.description = description
        task.save()

    def get_all_upstream_sync_tasks(self, as_list=True):
        qs = PeriodicTask.objects.filter(name__startswith="UPSTREAM_SYNC")
        if qs.exists():
            return list(qs.values()) if as_list else qs
        return [] if as_list else qs

    def get_all_daily_upstream_sync_tasks(self, as_list=True):
        qs = self.get_all_upstream_sync_tasks(as_list=False).exclude(args__icontains="true")  # type: ignore
        if qs.exists():
            return list(qs.values()) if as_list else qs
        return [] if as_list else qs

    def get_additional_delete_upstream_sync_tasks_for_client(
        self, client_id: int, as_list=True
    ):
        qs = self.get_all_upstream_sync_tasks(as_list=False).filter(args__icontains=f"[{client_id}, true]")  # type: ignore
        if qs.exists():
            return list(qs.values()) if as_list else qs
        return [] if as_list else qs

    def get_task_by_name(self, name):
        task = PeriodicTask.objects.get(name=name)
        if not task:
            raise ObjectDoesNotExist(f"{name} not found in Periodic Task Table")
        else:
            return task

    def update_task_kwargs_by_name(self, name, kwargs):
        return PeriodicTask.objects.filter(name=name).update(kwargs=kwargs)

    def update_task_cron(self, name, cron_tab):
        return PeriodicTask.objects.filter(name=name).update(
            crontab=cron_tab, date_changed=datetime.now()
        )

    def delete_task_by_name(self, task):
        if isinstance(task, list):
            query_set = PeriodicTask.objects.filter(name__in=task)
        else:
            query_set = PeriodicTask.objects.filter(name=task)
        if query_set.exists():
            query_set.delete()

    def remove_task_by_name(self, name):
        return PeriodicTask.objects.filter(name=name).delete()

    def get_all_upstream_sync_tasks_for_client(self, client_id: int) -> List[dict]:
        """
        Retrieve all upstream sync tasks associated with the given client ID.
        Args:
            client_id (int): Unique identifier of the client whose sync tasks are requested.

        Returns:
            list[dict]: A list containing dictionaries representing each task, where keys may include 'task_id', 'status', etc.
        """
        # Create patterns to match exact client_id in args
        exact_match_pattern = f"[{client_id}]"
        additional_delete_pattern = f"[{client_id},"

        filtered_qs = self.get_all_upstream_sync_tasks(as_list=False).filter(
            Q(args__exact=exact_match_pattern)
            | Q(args__startswith=additional_delete_pattern, args__iregex=r"true\]$")
        )
        result = list(filtered_qs.values())
        return result

    def delete_tasks_by_description(self, description: str) -> None:
        PeriodicTask.objects.filter(description=description).delete()

    def is_active_cron_schedule_present_for_client(self, client_id):
        """
        Checks if there is any enabled cron entries present for the client
        Returns False on no entries, else True
        """
        client_id = str(client_id)

        periodic_tasks = PeriodicTask.objects.filter(
            name__regex=rf"UPSTREAM_SYNC##{client_id}(?:##\d+)?$", enabled=True
        )

        return True if periodic_tasks.exists() else False

    def get_scheduled_workflow_task_by_workflow_id(
        self, workflow_id: str, client_id: str
    ) -> PeriodicTask:
        name = f"SCHEDULED_WORKFLOW_{workflow_id}##{client_id}"
        return PeriodicTask.objects.filter(name=name).first()

    @staticmethod
    def get_active_cron_schedule_mapping() -> dict[int, dict[str, int]]:
        """
        Fetches a mapping of client_id to their cron schedule (hours and minutes).

        Returns:
            dict[int, dict[str, int]]: {client_id: {"hours": int, "minutes": int}}
        """
        periodic_tasks = PeriodicTask.objects.filter(
            name__regex=r"UPSTREAM_SYNC##\d+(?:##\d+)?$", enabled=True
        ).select_related("crontab")

        schedule_mapping = {}

        for task in periodic_tasks:
            client_id_match = task.name.split("##")[1]  # Extracts the first numeric ID
            if not client_id_match.isdigit():
                logger.warning(
                    f"Failed to extract client_id from task name: {task.name}"
                )
                continue

            client_id = int(client_id_match)

            # Convert crontab UTC time to IST time. +5:30 hours
            if task.crontab:
                hours, minutes = (
                    int(task.crontab.hour),
                    int(task.crontab.minute),
                )
                minutes += 30
                carry_hour = minutes // 60
                minutes = minutes % 60
                hours += 5 + carry_hour
                hours = hours % 24
                schedule_mapping[client_id] = {"hours": hours, "minutes": minutes}
            else:
                logger.warning(f"Task {task.name} has no crontab entry.")

        return schedule_mapping


class CrontabScheduleAccessor:
    def __init__(self):
        pass

    def convert_time_to_utc(self, time, timezone_string):
        # TODO: Move this function to utils file and write method to convert time to any timezone
        """
        Supported timezone formats eg: "(GMT+00:00) UTC" / "UTC"

        >>> from zoneinfo import ZoneInfo
        >>> from datetime import datetime

        >>> d = datetime(2020, 10, 31, 12, tzinfo=ZoneInfo('America/Los_Angeles'))
        >>> d.astimezone(ZoneInfo('Europe/Berlin'))  # 12:00 in Cali will be 20:00 in Berlin
        datetime.datetime(2020, 10, 31, 20, 0, tzinfo=zoneinfo.ZoneInfo(key='Europe/Berlin'))
        """
        pattern = r"(\(.*\)) (.*)"
        if re.match(pattern, timezone_string):
            timezone_string = re.search(pattern, timezone_string).group(2)

        schedule_time = datetime.now(tz=ZoneInfo(timezone_string)).replace(
            hour=time["hour"], minute=time["minute"], second=time["second"]
        )
        utc_dt = schedule_time.astimezone(ZoneInfo("UTC"))
        return {"minute": utc_dt.minute, "hour": utc_dt.hour}

    def get_or_create_cron_expression(
        self,
        minute="*",
        hour="9",
        day_of_week="*",
        day_of_month="1",
        month_of_year="*",
    ):
        """
        By default notification will be sent at 9.00 Am
        for monthly notification, by default it will be 1st day of the month
        for weekly notification, by default it will be 1st day of the week
        for bi-weekly notification, by default it will be 7th and 21st of the month
        for daily notification, by default it will be everyday of the week

        returns cron object if exist or create one
        """
        schedule, _ = CrontabSchedule.objects.get_or_create(
            minute=minute,
            hour=hour,
            day_of_week=day_of_week,
            day_of_month=day_of_month,
            month_of_year=month_of_year,
        )
        return schedule

    def create_cron_expression(self, minute: str, hour: str) -> CrontabSchedule:
        return CrontabSchedule.objects.create(
            minute=minute,
            hour=hour,
            day_of_week="*",
            day_of_month="*",
            month_of_year="*",
        )

    def query_by_cron_expressions(self, expressions: list) -> list[CrontabSchedule]:
        query_filter = Q()
        for expression in expressions:
            query_filter |= Q(minute=expression["minute"], hour=expression["hour"])

        return list(
            CrontabSchedule.objects.filter(
                query_filter
                & Q(day_of_week="*")
                & Q(day_of_month="*")
                & Q(month_of_year="*")
            )
        )

    def get_cron_schedule_by_id(self, cron_id):
        if isinstance(cron_id, list):
            qs = CrontabSchedule.objects.filter(id__in=cron_id)
        else:
            qs = CrontabSchedule.objects.filter(id=cron_id)

        if qs.exists():
            return list(qs.values())
        return []

    def get_or_create_schedule_for_frequency(self, frequency, timezone="UTC"):
        time = {"minute": 0, "hour": 9, "second": 0}
        converted_date_time = self.convert_time_to_utc(time, timezone)
        if frequency == Freq.WEEKLY:
            return self.get_or_create_cron_expression(
                minute=converted_date_time["minute"],
                hour=converted_date_time["hour"],
                day_of_week="1",
                day_of_month="*",
                month_of_year="*",
            )
        if frequency == Freq.MONTHLY:
            return self.get_or_create_cron_expression(
                minute=converted_date_time["minute"],
                hour=converted_date_time["hour"],
                day_of_week="*",
                day_of_month="1",
                month_of_year="*",
            )
        if frequency == Freq.DAILY:
            return self.get_or_create_cron_expression(
                minute=converted_date_time["minute"],
                hour=converted_date_time["hour"],
                day_of_week="*",
                day_of_month="*",
                month_of_year="*",
            )
        if frequency == Freq.BIWEEKLY:
            return self.get_or_create_cron_expression(
                minute=converted_date_time["minute"],
                hour=converted_date_time["hour"],
                day_of_week="1",
                day_of_month="1-7,15-21",
                month_of_year="*",
            )

    def get_or_create_custom_frequency(self, date_time, timezone="UTC"):
        converted_date_time = self.convert_time_to_utc(date_time, timezone)
        return self.get_or_create_cron_expression(
            minute=converted_date_time["minute"],
            hour=converted_date_time["hour"],
            day_of_month="8",
        )


class MilestoneNotificationStatusAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return MilestoneNotificationStatus.objects.filter(client_id=self.client_id)

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def is_quota_attainment_notified_for_milestone(
        self, email_id, task, additional_details, time_period_label, milestone
    ):
        return (
            self.client_kd_aware()
            .filter(
                task=task,
                additional_details__quota_category=additional_details["quota_category"],
                additional_details__quota_type=additional_details["quota_type"],
                employee_email_id=email_id,
                time_period_label=time_period_label,
                milestone=milestone,
            )
            .exists()
        )

    def is_commission_attainment_notified_for_milestone(
        self, email_id, task, time_period_label, milestone
    ):
        return (
            self.client_kd_aware()
            .filter(
                task=task,
                employee_email_id=email_id,
                time_period_label=time_period_label,
                milestone=milestone,
            )
            .exists()
        )

    def persist(self, data):
        data.save()

    def invalidate_quota_attainment_notification_details(
        self, email_id, task, additional_details, time_period_label, ked
    ):
        self.client_kd_aware().filter(
            task=task,
            additional_details__quota_category=additional_details["quota_category"],
            additional_details__quota_type=additional_details["quota_type"],
            employee_email_id=email_id,
            time_period_label=time_period_label,
        ).update(knowledge_end_date=ked, is_deleted=True)

    def invalidate_commission_attainment_notification_details(
        self, email_id, task, time_period_label, ked
    ):
        self.client_kd_aware().filter(
            task=task,
            employee_email_id=email_id,
            time_period_label=time_period_label,
        ).update(knowledge_end_date=ked, is_deleted=True)


class HardDeleteAccessor:
    def __init__(self, client_id):
        self.client_id = client_id

    def client_aware(self):
        return HardDeletePeriodicTask.objects.filter(client_id=self.client_id)

    def client_kd_aware(self):
        return self.client_aware().filter(
            is_deleted=False, knowledge_end_date__isnull=True
        )

    def invalidate_hard_delete_entry(self, audit):
        task = self.client_kd_aware().first()
        task.knowledge_end_date = datetime.now()
        task.additional_details = audit
        task.save()

    def get_or_create_hard_delete_task(self, cron_tab, audit, status=True):
        task = HardDeletePeriodicTask(
            client_id=self.client_id,
            crontab=cron_tab,
            knowledge_begin_date=datetime.now(),
            enabled=status,
            additional_details=audit,
        )
        task.save()

    def does_task_exist(self):
        return self.client_kd_aware().exists()

    def update_task_details(self, status, cron_tab, audit):
        self.invalidate_hard_delete_entry(audit)
        self.get_or_create_hard_delete_task(cron_tab, audit, status)

    def get_task_by_client_id(self):
        return self.client_kd_aware()

    def get_active_hard_delete_tasks(self):
        return self.client_kd_aware().filter(enabled=True)

    def get_additional_delete_upstream_sync_tasks_for_client(self):
        qs = self.client_kd_aware()
        if qs.exists():
            return list(qs.values())
        return []

    def remove_task_by_client_id(self, audit):
        self.invalidate_hard_delete_entry(audit)
