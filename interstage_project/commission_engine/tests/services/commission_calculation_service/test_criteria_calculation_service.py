import datetime
import functools
from decimal import Decimal
from unittest.mock import patch

import arrow
import decorator
import pytest
from django.utils import timezone
from django.utils.timezone import make_aware

from commission_engine.accessors.client_accessor import (
    get_evaluation_mode,
    set_client_feature,
)
from commission_engine.models import Databook, Datasheet, DatasheetVariable
from commission_engine.models.commission_models import QuotaErosion
from commission_engine.services.commission_calculation_service import (
    criteria_calculation_service,
)
from commission_engine.services.commission_calculation_service.criteria_calculation_service import (
    get_line_item_level_commission_for_non_tier_summation_criterias,
    get_line_item_level_commission_records_for_tier_summation_criterias,
)
from commission_engine.utils import (
    delete_commission_cache,
    delete_hierarchy_cache,
    first_day_of_month,
    last_day_of_month,
)
from spm.accessors.variable_accessor import VariableDataTypeAccessor
from spm.models import PlanCriteria
from spm.models.config_models.employee_models import Hierarchy
from spm.models.quota_models import EmployeeQuota
from spm.models.teams_models.team_models import Membership, TeamConfig
from spm.tests.models import create_employee_payroll_object


@pytest.fixture()
def resource():
    Datasheet.objects.create(
        databook_id="0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
        datasheet_id="d27620ec-eb1f-40f5-a699-6b50aa7a4688",
        name="Test Sheet",
        source_type="object",
        source_id=5,
        order=1,
        primary_key=["co_5_delete_newid"],
        transformation_spec=[],
        ordered_columns="{co_5_delete_newid,co_5_delete_amount,co_5_delete_closed_date,co_5_delete_newemail}",
        data_origin="custom_object",
        knowledge_begin_date=timezone.now(),
        client_id=1,
    )
    vda = VariableDataTypeAccessor()
    vd = vda.get_data_type("Date")
    DatasheetVariable.objects.create(
        client_id=1,
        knowledge_begin_date=timezone.now(),
        databook_id="0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
        datasheet_id="d27620ec-eb1f-40f5-a699-6b50aa7a4688",
        system_name="co_5_delete_closed_date",
        display_name="delete_closed_date",
        data_type=vd,
        tags=None,
        field_order=0,
        meta_data={},
    )
    Hierarchy.objects.create(
        client_id=1,
        knowledge_begin_date=first_day_of_month(timezone.now()),
        employee_email_id="<EMAIL>",
        reporting_manager_email_id="<EMAIL>",
        effective_start_date=first_day_of_month(timezone.now()),
        effective_end_date=None,
    )
    Databook.objects.create(
        client_id=1,
        knowledge_begin_date=timezone.now(),
        databook_id="0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
        is_draft=True,
        is_archived=False,
        created_at=timezone.now(),
        created_by="<EMAIL>",
    )
    create_employee_payroll_object(
        "<EMAIL>", "123", "IND", "INR", "Monthly"
    )
    create_employee_payroll_object("<EMAIL>", "123", "IND", "INR", "Monthly")

    TeamConfig.objects.create(
        client_id=1,
        knowledge_begin_date=timezone.now().replace(day=1),
        team_id="d27620ec-eb1f-40f5-a699-6b50aa7a4688",
        team_name="Delete Team",
        team_created_by="<EMAIL>",
        team_created_time=timezone.now().replace(day=1),
        team_updated_by="<EMAIL>",
        team_updated_time=timezone.now().replace(day=1),
        team_type="team",
        pod_owner_name=None,
    )
    TeamConfig.objects.create(
        client_id=1,
        knowledge_begin_date=timezone.now(),
        team_id="d27620ec-eb1f-40f5-a699-6b50aa7a4686",
        team_name="Delete Automationxyz",
        team_created_by="<EMAIL>",
        team_created_time=timezone.now(),
        team_updated_by="<EMAIL>",
        team_updated_time=timezone.now(),
        team_type="pod",
        pod_owner_name="<EMAIL>",
    )
    Membership.objects.create(
        client_id=1,
        knowledge_begin_date=timezone.now(),
        team_id="d27620ec-eb1f-40f5-a699-6b50aa7a4688",
        group_member_email_id="<EMAIL>",
        effective_start_date=timezone.now(),
        effective_end_date=None,
    )
    Membership.objects.create(
        client_id=1,
        knowledge_begin_date=timezone.now(),
        team_id="d27620ec-eb1f-40f5-a699-6b50aa7a4686",
        group_member_email_id="<EMAIL>",
        effective_start_date=first_day_of_month(timezone.now()),
        effective_end_date=None,
    )


# def run_tests_for_different_evaluation_modes(client_id):
#     """
#     Runs a test for different evaluation modes like serial, vectorize
#     """
#     def inner_decorator(test_function):
#         def wrapper(function, *args, **kwargs):
#             evaluation_modes = ["serial", "vectorize"]
#             original_value = get_evaluation_mode(client_id=client_id)
#             for evaluation_mode in evaluation_modes:
#                 set_client_feature(
#                     client_id=1, label="evaluation_mode", value=evaluation_mode
#                 )
#                 test_function(*args, **kwargs)

#             set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

#         return decorator.decorator(wrapper, test_function)

#     return inner_decorator


@pytest.mark.django_db
@pytest.mark.commission_engine
class TestCriteriaCalculationService:
    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    def test_exec_team_criteria(self, mock_get_data, resource):
        mock_get_data.return_value = {
            "merged_data": {
                "<EMAIL>": [
                    {
                        "co_5_delete_newid": "delidautomation",
                        "co_5_delete_amount": 500000,
                        "co_5_delete_newemail": "<EMAIL>",
                        "co_5_delete_oppstage": "ClosedWon",
                        "co_5_delete_closed_date": first_day_of_month(
                            datetime.datetime.now()
                        ),
                        "row_key": "delidautomation",
                    }
                ]
            },
            "payee_field": "co_5_delete_newemail",
            "context": {
                "<EMAIL>": {
                    "payee": [
                        {
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("500000.00"),
                            "payout_frequency": "Monthly",
                            "data_payout_frequency": "Monthly",
                            "variable_pay": Decimal("41666.66666666666666666666667"),
                        }
                    ]
                }
            },
        }
        delete_hierarchy_cache(1)
        delete_commission_cache(1)
        evaluation_modes = ["vectorize", "serial"]
        original_value = get_evaluation_mode(client_id=1)
        for evaluation_mode in evaluation_modes:
            set_client_feature(
                client_id=1, label="evaluation_mode", value=evaluation_mode
            )
            exec_team = criteria_calculation_service.exec_team_criteria(
                1,
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
                timezone.now(),
                "<EMAIL>",
                {
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "payee_field": "co_5_delete_newemail",
                    "date_field": "co_5_delete_closed_date",
                },
                True,
                None,
                {
                    "merged_data": [
                        {
                            "co_5_delete_newid": "delidautomation",
                            "co_5_delete_amount": 500000,
                            "co_5_delete_newemail": "<EMAIL>",
                            "co_5_delete_oppstage": "ClosedWon",
                            "co_5_delete_closed_date": first_day_of_month(
                                datetime.datetime.now()
                            ),
                            "row_key": "delidautomation",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("50000.00"),
                            "payout_frequency": "Monthly",
                            "variable_pay": Decimal("4166.666666666666666666666667"),
                        },
                    ],
                    "context": {
                        "payee": [
                            {
                                "email": "<EMAIL>",
                                "actual_variable_pay": Decimal("50000.00"),
                                "payout_frequency": "Monthly",
                                "data_payout_frequency": "Monthly",
                                "variable_pay": Decimal(
                                    "4166.666666666666666666666667"
                                ),
                            }
                        ]
                    },
                },
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                first_day_of_month(datetime.datetime.now()),
                {
                    "infix_exp": None,
                    "effective_date_map": None,
                    "tier_type": "TieredSum",
                    "part1_exp": [
                        {
                            "databook_id": "fa49b0b4-1793-4f10-b74f-c9daa8c08518",
                            "datasheet_id": "793126c1-5bee-432c-a8b4-eb2d7075f39d",
                            "name": "delete_amount",
                            "meta": {
                                "data_type_id": 1,
                                "system_name": "co_5_delete_amount",
                                "model_name": "Delete_Automation_sheet",
                                "category": None,
                            },
                            "tags": None,
                            "data_type": "Integer",
                            "type": "VARIABLE",
                        }
                    ],
                    "part2_exp": [
                        {
                            "lower_bound": "-INF",
                            "upper_bound": 50,
                            "expression_stack": [
                                {
                                    "databook_id": "fa49b0b4-1793-4f10-b74f-c9daa8c08518",
                                    "datasheet_id": "793126c1-5bee-432c-a8b4-eb2d7075f39d",
                                    "name": "delete_amount",
                                    "meta": {
                                        "data_type_id": 1,
                                        "system_name": "co_5_delete_amount",
                                        "model_name": "Delete_Automation_sheet",
                                        "category": None,
                                    },
                                    "tags": None,
                                    "data_type": "Integer",
                                    "type": "VARIABLE",
                                },
                                {
                                    "name": "+",
                                    "alt_name": "PLUS",
                                    "category": "ARITHMETIC",
                                    "operand_type_ids": [1, 9, 10, 11],
                                    "output_type_ids": [1],
                                    "needs_operand": True,
                                    "multi_valued": False,
                                    "__typename": "",
                                    "output_types": ["Integer"],
                                    "operand_types": [
                                        "Integer",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "type": "OPERATOR",
                                },
                                {
                                    "function_name": "CONSTANT",
                                    "name": 1,
                                    "args": ["Integer", 1],
                                    "data_type": "Integer",
                                    "type": "VARIABLE",
                                    "token_category": "DYNAMIC",
                                },
                            ],
                            "tier_key": "a164f4c5-e96b-4ae7-82d4-83cccbc1bf34",
                            "is_valid": True,
                            "is_override_slab": False,
                            "tier_name": "Tier 0",
                        },
                        {
                            "lower_bound": 50,
                            "upper_bound": "INF",
                            "expression_stack": [
                                {
                                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                                    "name": "delete_amount",
                                    "meta": {
                                        "data_type_id": 1,
                                        "system_name": "co_5_delete_amount",
                                        "model_name": "Delete_Automation_sheet",
                                        "category": None,
                                    },
                                    "tags": None,
                                    "data_type": "Integer",
                                    "type": "VARIABLE",
                                },
                                {
                                    "name": "/",
                                    "alt_name": "DIVIDE",
                                    "category": "ARITHMETIC",
                                    "operand_type_ids": [1, 9, 10, 11],
                                    "output_type_ids": [1],
                                    "needs_operand": True,
                                    "multi_valued": False,
                                    "__typename": "",
                                    "output_types": ["Integer"],
                                    "operand_types": [
                                        "Integer",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "type": "OPERATOR",
                                },
                                {
                                    "function_name": "CONSTANT",
                                    "name": 2,
                                    "args": ["Integer", 2],
                                    "data_type": "Integer",
                                    "type": "VARIABLE",
                                    "token_category": "DYNAMIC",
                                },
                            ],
                            "tier_key": "67a54603-3020-4d10-bd3c-4ce7d6ee879f",
                            "is_valid": True,
                            "is_override_slab": False,
                            "is_last_tier": True,
                            "tier_name": "Tier 1",
                        },
                    ],
                    "part1_type": None,
                    "criteria_type": "Tier",
                    "team_name": "Delete Team",
                    "category": None,
                    "team_type": "static",
                    "team_members": ["<EMAIL>"],
                    "team_owner_email_id": "<EMAIL>",
                },
            )
            assert exec_team["result"] == [
                {
                    "id": "delidautomation",
                    "commission": Decimal("500001"),
                    "commission_date": first_day_of_month(datetime.datetime.now()),
                    "tier_id": 0,
                    "tier_name": "Tier 0",
                    "original_tier_id": 0,
                    "original_tier_name": "Tier 0",
                    "tier_value": Decimal("50.00"),
                },
                {
                    "id": "delidautomation",
                    "commission": Decimal("250000"),
                    "commission_date": first_day_of_month(datetime.datetime.now()),
                    "tier_id": 1,
                    "tier_name": "Tier 1",
                    "original_tier_id": 1,
                    "original_tier_name": "Tier 1",
                    "tier_value": Decimal("499950.00"),
                },
            ]
        set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    def test_evaluate_team_line_item_level_false(self, mock_get_data, resource):
        mock_get_data.return_value = {
            "merged_data": {
                "<EMAIL>": [
                    {
                        "co_5_delete_newid": "delidautomation",
                        "co_5_delete_amount": 500000,
                        "co_5_delete_newemail": "<EMAIL>",
                        "co_5_delete_oppstage": "ClosedWon",
                        "co_5_delete_closed_date": first_day_of_month(
                            datetime.datetime.now()
                        ),
                        "row_key": "delidautomation",
                    }
                ]
            },
            "payee_field": "co_5_delete_newemail",
            "context": {
                "<EMAIL>": {
                    "payee": [
                        {
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("500000.00"),
                            "payout_frequency": "Monthly",
                            "data_payout_frequency": "Monthly",
                            "variable_pay": Decimal("41666.66666666666666666666667"),
                        }
                    ]
                }
            },
        }
        delete_hierarchy_cache(1)
        delete_commission_cache(1)
        evaluation_modes = ["vectorize", "serial"]
        original_value = get_evaluation_mode(client_id=1)
        for evaluation_mode in evaluation_modes:
            set_client_feature(
                client_id=1, label="evaluation_mode", value=evaluation_mode
            )
            evaluate_team = criteria_calculation_service.evaluate_team(
                1,
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
                timezone.now(),
                "<EMAIL>",
                {
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "payee_field": "co_5_delete_newemail",
                    "date_field": "co_5_delete_closed_date",
                },
                False,
                None,
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                first_day_of_month(datetime.datetime.now()),
                {
                    "infix_exp": [
                        {
                            "function_name": "SUM",
                            "name": "SUM(delete_amount)",
                            "args": [
                                {
                                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                                    "name": "delete_amount",
                                    "meta": {
                                        "data_type_id": 1,
                                        "system_name": "co_5_delete_amount",
                                        "model_name": "Delete_Automation_sheet",
                                        "category": None,
                                    },
                                    "tags": None,
                                    "data_type": "Integer",
                                    "type": "VARIABLE",
                                }
                            ],
                            "data_type": "Integer",
                            "type": "VARIABLE",
                            "token_category": "DYNAMIC",
                        }
                    ],
                    "tier_type": None,
                    "part1_exp": None,
                    "part2_exp": None,
                    "part1_type": None,
                    "criteria_type": "Simple",
                    "team_name": "Delete Team",
                    "category": None,
                },
            )
            assert evaluate_team["result"] == [
                {
                    "id": None,
                    "commission": Decimal("500000"),
                    "commission_date": last_day_of_month(datetime.datetime.now()),
                    "source_payee": "<EMAIL>",
                }
            ]
            assert not evaluate_team["is_line_item_level"]

        set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    @pytest.mark.parametrize(
        "params, result",
        [
            (
                {
                    "infix_exp": [
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Delete_Automation_sheet",
                                "system_name": "co_5_delete_amount",
                                "data_type_id": 1,
                            },
                            "name": "number",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                            "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                        }
                    ],
                    "tier_type": None,
                    "part1_exp": None,
                    "part2_exp": None,
                    "part1_type": None,
                    "criteria_type": "Simple",
                    "team_name": "Payee & Team",
                    "category": None,
                },
                {
                    "context": {},
                    "summary": {},
                    "result": [
                        {
                            "commission": 0,
                            "commission_date": last_day_of_month(
                                datetime.datetime.now()
                            ),
                        }
                    ],
                    "is_line_item_level": True,
                    "do_nothing_records": [],
                },
            )
        ],
    )
    def test_evaluate_team_invalid_payee(self, mock_get_data, resource, params, result):
        mock_get_data.return_value = {
            "merged_data": {
                "<EMAIL>": [
                    {
                        "co_5_delete_newid": "delidautomation",
                        "co_5_delete_amount": 500000,
                        "co_5_delete_newemail": "<EMAIL>",
                        "co_5_delete_oppstage": "ClosedWon",
                        "co_5_delete_closed_date": first_day_of_month(
                            datetime.datetime.now()
                        ),
                        "row_key": "delidautomation",
                    }
                ]
            },
            "payee_field": "co_5_delete_newemail",
            "context": {},
        }
        evaluation_modes = ["vectorize", "serial"]
        original_value = get_evaluation_mode(client_id=1)
        for evaluation_mode in evaluation_modes:
            set_client_feature(
                client_id=1, label="evaluation_mode", value=evaluation_mode
            )

            evaluate_team = criteria_calculation_service.evaluate_team(
                1,
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
                timezone.now(),
                "<EMAIL>",
                {
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "payee_field": "co_5_delete_newemail",
                    "date_field": "co_5_delete_closed_date",
                },
                True,
                None,
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                first_day_of_month(datetime.datetime.now()),
                params,
            )
            assert evaluate_team == result

        set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

        delete_hierarchy_cache(1)
        delete_commission_cache(1)

    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    @pytest.mark.parametrize(
        "email,commission,test_id",
        [
            (
                "<EMAIL>",
                [
                    {
                        "id": "delidautomation",
                        "commission": Decimal("1000000"),
                        "commission_date": first_day_of_month(datetime.datetime.now()),
                    }
                ],
                1,
            ),
            (
                "<EMAIL>",
                [
                    {
                        "commission": 0,
                        "commission_date": last_day_of_month(timezone.now()),
                    }
                ],
                2,
            ),
        ],
    )
    def test_evaluate_infix(self, mock_get_data, resource, email, commission, test_id):
        if test_id == 1:
            mock_get_data.return_value = {
                "merged_data": {
                    "<EMAIL>": [
                        {
                            "co_5_delete_newid": "delidautomation",
                            "co_5_delete_amount": 500000,
                            "co_5_delete_newemail": "<EMAIL>",
                            "co_5_delete_oppstage": "ClosedWon",
                            "co_5_delete_closed_date": first_day_of_month(
                                datetime.datetime.now()
                            ),
                            "row_key": "delidautomation",
                        }
                    ]
                },
                "payee_field": "co_5_delete_newemail",
                "context": {
                    "<EMAIL>": {
                        "payee": [
                            {
                                "email": "<EMAIL>",
                                "actual_variable_pay": Decimal("500000.00"),
                                "payout_frequency": "Monthly",
                                "data_payout_frequency": "Monthly",
                                "variable_pay": Decimal(
                                    "41666.66666666666666666666667"
                                ),
                            }
                        ]
                    }
                },
            }
        if test_id == 2:
            mock_get_data.return_value = {
                "merged_data": {},
                "payee_field": "co_5_delete_newemail",
                "context": {
                    "<EMAIL>": {
                        "payee": [
                            {
                                "email": "<EMAIL>",
                                "actual_variable_pay": Decimal("500000.00"),
                                "payout_frequency": "Monthly",
                                "data_payout_frequency": "Monthly",
                                "variable_pay": Decimal(
                                    "41666.66666666666666666666667"
                                ),
                            }
                        ]
                    }
                },
            }

        evaluation_modes = ["vectorize", "serial"]
        original_value = get_evaluation_mode(client_id=1)
        for evaluation_mode in evaluation_modes:
            set_client_feature(
                client_id=1, label="evaluation_mode", value=evaluation_mode
            )
            infix = criteria_calculation_service.evaluate_infix(
                1,
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
                timezone.now(),
                [
                    {
                        "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                        "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                        "name": "delete_amount",
                        "meta": {
                            "data_type_id": 1,
                            "system_name": "co_5_delete_amount",
                            "model_name": "Delete_Automation_sheet",
                            "category": None,
                        },
                        "tags": None,
                        "data_type": "Integer",
                        "type": "VARIABLE",
                    },
                    {
                        "name": "*",
                        "alt_name": "MULTIPLY",
                        "category": "ARITHMETIC",
                        "operand_type_ids": [1, 6, 9, 10, 11],
                        "output_type_ids": [1],
                        "needs_operand": True,
                        "multi_valued": False,
                        "output_types": ["Integer"],
                        "operand_types": [
                            "Integer",
                            "Percentage",
                            "DayDuration",
                            "MinuteDuration",
                            "SecondDuration",
                        ],
                        "type": "OPERATOR",
                    },
                    {
                        "function_name": "CONSTANT",
                        "name": 2,
                        "args": ["Integer", 2],
                        "data_type": "Integer",
                        "type": "VARIABLE",
                        "token_category": "DYNAMIC",
                    },
                ],
                email,
                {
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "payee_field": "co_5_delete_newemail",
                    "date_field": "co_5_delete_closed_date",
                },
                True,
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
            )
            assert infix["result"] == commission
            delete_commission_cache(1)
            delete_hierarchy_cache(1)

        set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    @pytest.mark.parametrize(
        "params, result, test_id",
        [
            (
                {
                    "infix_exp": [
                        {
                            "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                            "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                            "name": "delete_amount",
                            "meta": {
                                "data_type_id": 1,
                                "system_name": "co_5_delete_amount",
                                "model_name": "Delete_Automation_sheet",
                                "category": None,
                            },
                            "tags": None,
                            "data_type": "Integer",
                            "type": "VARIABLE",
                        },
                        {
                            "name": "+",
                            "alt_name": "PLUS",
                            "category": "ARITHMETIC",
                            "operand_type_ids": [1, 9, 10, 11],
                            "output_type_ids": [1],
                            "needs_operand": True,
                            "multi_valued": False,
                            "output_types": ["Integer"],
                            "operand_types": [
                                "Integer",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                            ],
                            "type": "OPERATOR",
                        },
                        {
                            "function_name": "CONSTANT",
                            "name": 1,
                            "args": ["Integer", 1],
                            "data_type": "Integer",
                            "type": "VARIABLE",
                            "token_category": "DYNAMIC",
                        },
                    ],
                    "tier_type": None,
                    "part1_exp": None,
                    "part2_exp": None,
                    "part1_type": None,
                    "criteria_type": "Simple",
                    "team_name": "Delete Team",
                    "category": None,
                },
                [
                    {
                        "id": "delidautomation",
                        "commission": Decimal("500001"),
                        "commission_date": first_day_of_month(datetime.datetime.now()),
                        "source_payee": "<EMAIL>",
                    }
                ],
                1,
            ),
            (
                {
                    "infix_exp": [
                        {
                            "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                            "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                            "name": "delete_amount",
                            "meta": {
                                "data_type_id": 1,
                                "system_name": "co_5_delete_amount",
                                "model_name": "Delete_Automation_sheet",
                                "category": None,
                            },
                            "tags": None,
                            "data_type": "Integer",
                            "type": "VARIABLE",
                        },
                        {
                            "name": "+",
                            "alt_name": "PLUS",
                            "category": "ARITHMETIC",
                            "operand_type_ids": [1, 9, 10, 11],
                            "output_type_ids": [1],
                            "needs_operand": True,
                            "multi_valued": False,
                            "__typename": "",
                            "output_types": ["Integer"],
                            "operand_types": [
                                "Integer",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                            ],
                            "type": "OPERATOR",
                        },
                        {
                            "function_name": "CONSTANT",
                            "name": 1,
                            "args": ["Integer", 1],
                            "data_type": "Integer",
                            "type": "VARIABLE",
                            "token_category": "DYNAMIC",
                        },
                    ],
                    "tier_type": None,
                    "part1_exp": None,
                    "part2_exp": None,
                    "part1_type": None,
                    "criteria_type": "Simple",
                    "team_name": "Pod",
                    "category": None,
                },
                [
                    {
                        "id": "delidautomation",
                        "commission": 500001,
                        "commission_date": first_day_of_month(datetime.datetime.now()),
                        "source_payee": "<EMAIL>",
                    }
                ],
                2,
            ),
            (
                {
                    "infix_exp": [
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Delete_Automation_sheet",
                                "system_name": "co_5_delete_amount",
                                "data_type_id": 1,
                            },
                            "name": "number",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                            "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                        }
                    ],
                    "tier_type": None,
                    "part1_exp": None,
                    "part2_exp": None,
                    "part1_type": None,
                    "criteria_type": "Simple",
                    "team_name": "Payee & Team",
                    "category": None,
                },
                [
                    {
                        "id": "delidautomation",
                        "commission": 500000,
                        "commission_date": first_day_of_month(datetime.datetime.now()),
                        "source_payee": "<EMAIL>",
                    },
                    {
                        "id": "delidautomation_1",
                        "commission": 500000,
                        "commission_date": first_day_of_month(datetime.datetime.now()),
                        "source_payee": "<EMAIL>",
                    },
                ],
                3,
            ),
        ],
    )
    def test_evaluate_team(self, mock_get_data, params, result, test_id, resource):
        delete_commission_cache(1)
        delete_hierarchy_cache(1)
        if test_id != 3:
            mock_get_data.return_value = mock_get_data.return_value = {
                "merged_data": {
                    "<EMAIL>": [
                        {
                            "co_5_delete_newid": "delidautomation",
                            "co_5_delete_amount": 500000,
                            "co_5_delete_newemail": "<EMAIL>",
                            "co_5_delete_oppstage": "ClosedWon",
                            "co_5_delete_closed_date": first_day_of_month(
                                datetime.datetime.now()
                            ),
                            "row_key": "delidautomation",
                        }
                    ]
                },
                "payee_field": "co_5_delete_newemail",
                "context": {
                    "<EMAIL>": {
                        "payee": [
                            {
                                "email": "<EMAIL>",
                                "actual_variable_pay": Decimal("500000.00"),
                                "payout_frequency": "Monthly",
                                "data_payout_frequency": "Monthly",
                                "variable_pay": Decimal(
                                    "41666.66666666666666666666667"
                                ),
                            }
                        ]
                    }
                },
            }
        else:
            mock_get_data.return_value = mock_get_data.return_value = {
                "merged_data": {
                    "<EMAIL>": [
                        {
                            "co_5_delete_newid": "delidautomation",
                            "co_5_delete_amount": 500000,
                            "co_5_delete_newemail": "<EMAIL>",
                            "co_5_delete_oppstage": "ClosedWon",
                            "co_5_delete_closed_date": first_day_of_month(
                                datetime.datetime.now()
                            ),
                            "row_key": "delidautomation",
                        }
                    ],
                    "<EMAIL>": [
                        {
                            "co_5_delete_newid": "delidautomation_1",
                            "co_5_delete_amount": 500000,
                            "co_5_delete_newemail": "<EMAIL>",
                            "co_5_delete_oppstage": "ClosedWon",
                            "co_5_delete_closed_date": first_day_of_month(
                                datetime.datetime.now()
                            ),
                            "row_key": "delidautomation_1",
                        }
                    ],
                },
                "payee_field": "co_5_delete_newemail",
                "context": {
                    "<EMAIL>": {
                        "payee": [
                            {
                                "email": "<EMAIL>",
                                "actual_variable_pay": Decimal("500000.00"),
                                "payout_frequency": "Monthly",
                                "data_payout_frequency": "Monthly",
                                "variable_pay": Decimal(
                                    "41666.66666666666666666666667"
                                ),
                            }
                        ]
                    },
                    "<EMAIL>": {
                        "payee": [
                            {
                                "email": "<EMAIL>",
                                "actual_variable_pay": Decimal("500000.00"),
                                "payout_frequency": "Monthly",
                                "variable_pay": Decimal(
                                    "41666.66666666666666666666667"
                                ),
                            }
                        ]
                    },
                },
            }
        time = datetime.datetime.now()
        evaluation_modes = ["vectorize", "serial"]
        original_value = get_evaluation_mode(client_id=1)
        for evaluation_mode in evaluation_modes:
            set_client_feature(
                client_id=1, label="evaluation_mode", value=evaluation_mode
            )

            evaluate_team = criteria_calculation_service.evaluate_team(
                1,
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
                time,
                "<EMAIL>",
                {
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "payee_field": "co_5_delete_newemail",
                    "date_field": "co_5_delete_closed_date",
                },
                True,
                None,
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                first_day_of_month(datetime.datetime.now()),
                params,
            )
            assert evaluate_team["result"] == result

        set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    @pytest.mark.parametrize("show_do_nothing", [True, False])
    def test_evaluate_infix_do_nothing(self, mock_get_data, show_do_nothing, resource):
        mock_get_data.return_value = {
            "merged_data": {
                "<EMAIL>": [
                    {
                        "co_5_delete_newid": "delidautomation",
                        "co_5_delete_amount": 500000,
                        "co_5_delete_newemail": "<EMAIL>",
                        "co_5_delete_oppstage": "ClosedWon",
                        "co_5_delete_closed_date": first_day_of_month(
                            datetime.datetime.now()
                        ),
                        "row_key": "delidautomation",
                    }
                ]
            },
            "payee_field": "co_5_delete_newemail",
            "context": {
                "<EMAIL>": {
                    "payee": [
                        {
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("500000.00"),
                            "payout_frequency": "Monthly",
                            "data_payout_frequency": "Monthly",
                            "variable_pay": Decimal("41666.66666666666666666666667"),
                        }
                    ]
                }
            },
        }
        if show_do_nothing:
            PlanCriteria.objects.create(
                plan_id="0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                criteria_id="0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                criteria_name="test",
                criteria_type="Conditional",
                criteria_description=None,
                criteria_display_order=None,
                criteria_data={},
                criteria_column=[],
                criteria_config={"show_do_nothing": True, "criteria_is_hidden": False},
                client_id=1,
                knowledge_begin_date=timezone.now(),
            )
        evaluation_modes = ["vectorize", "serial"]
        original_value = get_evaluation_mode(client_id=1)
        for evaluation_mode in evaluation_modes:
            set_client_feature(
                client_id=1, label="evaluation_mode", value=evaluation_mode
            )

            infix = criteria_calculation_service.evaluate_infix(
                1,
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
                timezone.now(),
                {
                    "function_name": "IF",
                    "name": "IF",
                    "data_type": "Integer",
                    "type": "VARIABLE",
                    "token_category": "DYNAMIC",
                    "args": [
                        {
                            ">": [
                                {
                                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                                    "name": "delete_amount",
                                    "meta": {
                                        "data_type_id": 1,
                                        "system_name": "co_5_delete_amount",
                                        "model_name": "Delete_Automation_sheet",
                                        "category": None,
                                    },
                                    "tags": None,
                                    "data_type": "Integer",
                                    "type": "VARIABLE",
                                },
                                {
                                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                                    "name": "delete_amount",
                                    "meta": {
                                        "data_type_id": 1,
                                        "system_name": "co_5_delete_amount",
                                        "model_name": "Delete_Automation_sheet",
                                        "category": None,
                                    },
                                    "tags": None,
                                    "data_type": "Integer",
                                    "type": "VARIABLE",
                                },
                            ]
                        },
                        {
                            "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                            "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                            "name": "delete_amount",
                            "meta": {
                                "data_type_id": 1,
                                "system_name": "co_5_delete_amount",
                                "model_name": "Delete_Automation_sheet",
                                "category": None,
                            },
                            "tags": None,
                            "data_type": "Integer",
                            "type": "VARIABLE",
                        },
                        None,
                    ],
                    "then_is_nested": False,
                    "else_is_nested": False,
                    "else_do_nothing": True,
                    "level": 0,
                },
                "<EMAIL>",
                {
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "payee_field": "co_5_delete_newemail",
                    "date_field": "co_5_delete_closed_date",
                },
                True,
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
            )
            if not show_do_nothing:
                assert infix["result"] == [
                    {
                        "commission": 0,
                        "commission_date": last_day_of_month(datetime.datetime.now()),
                    }
                ]
                assert infix["do_nothing_records"] == ["delidautomation"]
            else:
                assert infix["result"] == [
                    {
                        "id": "delidautomation",
                        "commission": 0,
                        "commission_date": first_day_of_month(datetime.datetime.now()),
                        "show_do_nothing": True,
                    }
                ]
        set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

        delete_commission_cache(1)
        delete_hierarchy_cache(1)

    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    def test_evaluate_infix_overall(self, mock_get_data, resource):
        mock_get_data.return_value = {
            "merged_data": {
                "<EMAIL>": [
                    {
                        "co_5_delete_newid": "delidautomation",
                        "co_5_delete_amount": 500000,
                        "co_5_delete_newemail": "<EMAIL>",
                        "co_5_delete_oppstage": "ClosedWon",
                        "co_5_delete_closed_date": first_day_of_month(
                            datetime.datetime.now()
                        ),
                        "row_key": "delidautomation",
                    }
                ]
            },
            "payee_field": "co_5_delete_newemail",
            "context": {
                "<EMAIL>": {
                    "payee": [
                        {
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("500000.00"),
                            "payout_frequency": "Monthly",
                            "data_payout_frequency": "Monthly",
                            "variable_pay": Decimal("41666.66666666666666666666667"),
                        }
                    ]
                }
            },
        }
        PlanCriteria.objects.create(
            plan_id="0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
            criteria_id="0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
            criteria_name="test",
            criteria_type="Conditional",
            criteria_description=None,
            criteria_display_order=None,
            criteria_data={},
            criteria_column=[],
            criteria_config={"show_do_nothing": True, "criteria_is_hidden": False},
            client_id=1,
            knowledge_begin_date=timezone.now(),
        )

        evaluation_modes = ["serial"]
        original_value = get_evaluation_mode(client_id=1)
        for evaluation_mode in evaluation_modes:
            set_client_feature(
                client_id=1, label="evaluation_mode", value=evaluation_mode
            )
            infix = criteria_calculation_service.evaluate_infix(
                1,
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
                timezone.now(),
                {
                    "function_name": "IF",
                    "name": "IF",
                    "data_type": "Integer",
                    "type": "VARIABLE",
                    "token_category": "DYNAMIC",
                    "args": [
                        {
                            ">": [
                                {
                                    "function_name": "SUM",
                                    "name": "SUM(number)",
                                    "args": [
                                        {
                                            "databook_id": "92f4f2be-4d1c-4bb8-be96-ed0b4214883a",
                                            "datasheet_id": "983ecd9c-063d-4eec-8c29-b22654569e7f",
                                            "name": "number",
                                            "meta": {
                                                "data_type_id": 1,
                                                "system_name": "co_3_number",
                                                "model_name": "test sheet",
                                                "category": None,
                                            },
                                            "tags": None,
                                            "data_type": "Integer",
                                            "type": "VARIABLE",
                                        }
                                    ],
                                    "data_type": "Integer",
                                    "type": "VARIABLE",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "function_name": "CONSTANT",
                                    "name": 3,
                                    "args": ["Integer", 3],
                                    "data_type": "Integer",
                                    "type": "VARIABLE",
                                    "token_category": "DYNAMIC",
                                },
                            ]
                        },
                        {
                            "function_name": "CONSTANT",
                            "name": 1,
                            "args": ["Integer", 1],
                            "data_type": "Integer",
                            "type": "VARIABLE",
                            "token_category": "DYNAMIC",
                        },
                        None,
                    ],
                    "then_is_nested": False,
                    "else_is_nested": False,
                    "else_do_nothing": True,
                    "level": 0,
                },
                "<EMAIL>",
                {
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "payee_field": "co_5_delete_newemail",
                    "date_field": "co_5_delete_closed_date",
                },
                False,
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
            )
            assert infix["result"] == [
                {
                    "id": None,
                    "commission": 0,
                    "commission_date": last_day_of_month(datetime.datetime.now()),
                    "show_do_nothing": True,
                }
            ]
            assert not infix["is_line_item_level"]

        set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

    def test_validate_infix_invalid(self):
        validate_infix = criteria_calculation_service.validate_infix(
            [
                {
                    "databook_id": "fa49b0b4-1793-4f10-b74f-c9daa8c08518",
                    "datasheet_id": "793126c1-5bee-432c-a8b4-eb2d7075f39d",
                    "name": "delete_created date",
                    "meta": {
                        "data_type_id": 2,
                        "system_name": "co_5_delete_created_date",
                        "model_name": "Delete_Automation_sheet",
                        "category": None,
                    },
                    "tags": None,
                    "data_type": "Date",
                    "type": "VARIABLE",
                },
                {
                    "name": ">",
                    "alt_name": "GREATERTHAN",
                    "category": "LOGICAL",
                    "operand_type_ids": [1, 2, 6, 9, 10, 11],
                    "output_type_ids": [3],
                    "needs_operand": True,
                    "multi_valued": False,
                    "__typename": "",
                    "output_types": ["Boolean"],
                    "operand_types": [
                        "Integer",
                        "Date",
                        "Percentage",
                        "DayDuration",
                        "MinuteDuration",
                        "SecondDuration",
                    ],
                    "type": "OPERATOR",
                },
                {
                    "function_name": "CONSTANT",
                    "name": 3,
                    "args": ["Integer", 1],
                    "data_type": "Integer",
                    "type": "VARIABLE",
                    "token_category": "DYNAMIC",
                },
            ],
            False,
        )
        assert validate_infix["status"] == "INVALID"
        delete_commission_cache(1)

    @pytest.mark.parametrize("validate_brackets", [True, False])
    def test_validate_infix(self, validate_brackets):
        delete_hierarchy_cache(1)
        delete_commission_cache(1)
        validate_infix = criteria_calculation_service.validate_infix(
            [
                {
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "datasheet_id": "73322be7-9815-4ef0-a891-8fcd092a80b0",
                    "name": "delete_amount",
                    "meta": {
                        "data_type_id": 1,
                        "system_name": "co_5_delete_amount",
                        "model_name": "Delete_Automation_sheet_Copy",
                        "category": None,
                    },
                    "tags": None,
                    "data_type": "Integer",
                    "type": "VARIABLE",
                },
                {
                    "name": "*",
                    "alt_name": "MULTIPLY",
                    "category": "ARITHMETIC",
                    "operand_type_ids": [1, 6, 9, 10, 11],
                    "output_type_ids": [1],
                    "needs_operand": True,
                    "multi_valued": False,
                    "__typename": "",
                    "output_types": ["Integer"],
                    "operand_types": [
                        "Integer",
                        "Percentage",
                        "DayDuration",
                        "MinuteDuration",
                        "SecondDuration",
                    ],
                    "type": "OPERATOR",
                },
                {
                    "type": "LBRACKET",
                    "category": "LBRACKET",
                    "name": "(",
                    "alt_name": "LeftBracket",
                },
                {
                    "function_name": "CONSTANT",
                    "name": 2,
                    "args": ["Integer", 2],
                    "data_type": "Integer",
                    "type": "VARIABLE",
                    "token_category": "DYNAMIC",
                },
                {
                    "type": "RBRACKET",
                    "category": "RBRACKET",
                    "name": ")",
                    "alt_name": "RightBracket",
                },
            ],
            validate_brackets,
        )
        assert validate_infix["status"] == "VALID"
        assert validate_infix["msg"] == "INTEGER"

    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    @pytest.mark.parametrize("user_email", ["<EMAIL>", "<EMAIL>"])
    def test_evaluate_tier(self, mock_get_data, resource, user_email):
        if user_email == "<EMAIL>":
            mock_get_data.return_value = {
                "merged_data": {
                    "<EMAIL>": [
                        {
                            "co_5_delete_newid": "delidautomation",
                            "co_5_delete_amount": 500000,
                            "co_5_delete_newemail": "<EMAIL>",
                            "co_5_delete_oppstage": "ClosedWon",
                            "co_5_delete_closed_date": first_day_of_month(
                                datetime.datetime.now()
                            ),
                            "row_key": "delidautomation",
                        }
                    ]
                },
                "payee_field": "co_5_delete_newemail",
                "context": {
                    "<EMAIL>": {
                        "payee": [
                            {
                                "email": "<EMAIL>",
                                "actual_variable_pay": Decimal("500000.00"),
                                "payout_frequency": "Monthly",
                                "data_payout_frequency": "Monthly",
                                "variable_pay": Decimal(
                                    "41666.66666666666666666666667"
                                ),
                            }
                        ]
                    }
                },
            }
        delete_hierarchy_cache(1)
        delete_commission_cache(1)
        evaluation_modes = ["vectorize", "serial"]
        original_value = get_evaluation_mode(client_id=1)
        for evaluation_mode in evaluation_modes:
            set_client_feature(
                client_id=1, label="evaluation_mode", value=evaluation_mode
            )
            evaluate_tier = criteria_calculation_service.evaluate_tier(
                1,
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
                timezone.now(),
                [
                    {
                        "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                        "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                        "name": "delete_amount",
                        "meta": {
                            "data_type_id": 1,
                            "system_name": "co_5_delete_amount",
                            "model_name": "Delete_Automation_sheet",
                            "category": None,
                        },
                        "tags": None,
                        "data_type": "Integer",
                        "type": "VARIABLE",
                    }
                ],
                user_email,
                {
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "payee_field": "co_5_delete_newemail",
                    "date_field": "co_5_delete_closed_date",
                },
                True,
                None,
                [
                    {
                        "lower_bound": "-INF",
                        "upper_bound": 25000,
                        "expression_stack": [
                            {
                                "function_name": "TieredValue",
                                "name": "TieredValue()",
                                "args": [],
                                "data_type": "Integer",
                                "type": "VARIABLE",
                                "token_category": "DYNAMIC",
                            }
                        ],
                        "tier_key": "a0405115-ae60-433a-a5d5-f8c2b4597a8f",
                        "is_valid": True,
                        "is_override_slab": False,
                        "tier_name": "Tier 0",
                    },
                    {
                        "lower_bound": 25000,
                        "upper_bound": "INF",
                        "expression_stack": [
                            {
                                "function_name": "TieredValue",
                                "name": "TieredValue()",
                                "args": [],
                                "data_type": "Integer",
                                "type": "VARIABLE",
                                "token_category": "DYNAMIC",
                            }
                        ],
                        "tier_key": "2c58ceed-332f-402b-8b50-cd877187f528",
                        "is_valid": True,
                        "is_override_slab": False,
                        "is_last_tier": True,
                        "tier_name": "Tier 1",
                    },
                ],
                "TieredSum",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
            )
            if user_email == "<EMAIL>":
                assert evaluate_tier["result"][0] == {
                    "id": "delidautomation",
                    "commission": Decimal("25000.00"),
                    "commission_date": first_day_of_month(datetime.datetime.now()),
                    "tier_id": 0,
                    "tier_name": "Tier 0",
                    "original_tier_id": 0,
                    "original_tier_name": "Tier 0",
                    "tier_value": Decimal("25000.00"),
                }
                assert evaluate_tier["result"][1] == {
                    "id": "delidautomation",
                    "commission": Decimal("475000.00"),
                    "commission_date": first_day_of_month(datetime.datetime.now()),
                    "tier_id": 1,
                    "tier_name": "Tier 1",
                    "original_tier_id": 1,
                    "original_tier_name": "Tier 1",
                    "tier_value": Decimal("475000.00"),
                }
            else:
                assert evaluate_tier["result"] == [
                    {
                        "commission": 0,
                        "commission_date": last_day_of_month(datetime.datetime.now()),
                        "tier_id": 0,
                        "tier_name": "Tier 0",
                    }
                ]
        set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    def test_evaluate_tier_override(self, mock_get_data, resource):
        mock_get_data.return_value = {
            "merged_data": {
                "<EMAIL>": [
                    {
                        "co_5_delete_newid": "delidautomation",
                        "co_5_delete_amount": 500000,
                        "co_5_delete_newemail": "<EMAIL>",
                        "co_5_delete_oppstage": "ClosedWon",
                        "co_5_delete_closed_date": first_day_of_month(
                            datetime.datetime.now()
                        ),
                        "row_key": "delidautomation",
                    }
                ]
            },
            "payee_field": "co_5_delete_newemail",
            "context": {
                "<EMAIL>": {
                    "payee": [
                        {
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("500000.00"),
                            "payout_frequency": "Monthly",
                            "data_payout_frequency": "Monthly",
                            "variable_pay": Decimal("41666.66666666666666666666667"),
                        }
                    ]
                }
            },
        }
        delete_hierarchy_cache(1)
        delete_commission_cache(1)
        evaluation_modes = ["vectorize", "serial"]
        original_value = get_evaluation_mode(client_id=1)
        for evaluation_mode in evaluation_modes:
            set_client_feature(
                client_id=1, label="evaluation_mode", value=evaluation_mode
            )
            evaluate_tier = criteria_calculation_service.evaluate_tier(
                1,
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
                timezone.now(),
                [
                    {
                        "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                        "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                        "name": "delete_amount",
                        "meta": {
                            "data_type_id": 1,
                            "system_name": "co_5_delete_amount",
                            "model_name": "Delete_Automation_sheet",
                            "category": None,
                        },
                        "tags": None,
                        "data_type": "Integer",
                        "type": "VARIABLE",
                    }
                ],
                "<EMAIL>",
                {
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "payee_field": "co_5_delete_newemail",
                    "date_field": "co_5_delete_closed_date",
                },
                True,
                True,
                [
                    {
                        "lower_bound": "-INF",
                        "upper_bound": 2,
                        "expression_stack": [
                            {
                                "function_name": "CONSTANT",
                                "name": 1,
                                "args": ["Integer", 1],
                                "data_type": "Integer",
                                "type": "VARIABLE",
                                "token_category": "DYNAMIC",
                            }
                        ],
                        "tier_key": "a263abb2-3cf0-4e92-bb68-30439ef15bbf",
                        "is_valid": True,
                        "is_override_slab": False,
                        "tier_name": "Tier 0",
                    },
                    {
                        "lower_bound": 2,
                        "upper_bound": "INF",
                        "expression_stack": [
                            {
                                "function_name": "CONSTANT",
                                "name": 2,
                                "args": ["Integer", 2],
                                "data_type": "Integer",
                                "type": "VARIABLE",
                                "token_category": "DYNAMIC",
                            }
                        ],
                        "tier_key": "7f5f14f6-e7ea-4f2a-b617-6a68d8294be5",
                        "is_valid": True,
                        "is_override_slab": True,
                        "is_last_tier": True,
                        "tier_name": "Tier 1",
                    },
                ],
                "TieredSum",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
            )
            assert evaluate_tier["result"][0]["commission"] == Decimal("2")
            assert evaluate_tier["result"][1]["commission"] == Decimal("2")
        set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    def test_evaluate_tier_not_is_line_item(self, mock_get_data, resource):
        mock_get_data.return_value = {
            "merged_data": {
                "<EMAIL>": [
                    {
                        "co_5_delete_newid": "delidautomation",
                        "co_5_delete_amount": 500000,
                        "co_5_delete_newemail": "<EMAIL>",
                        "co_5_delete_oppstage": "ClosedWon",
                        "co_5_delete_closed_date": first_day_of_month(
                            datetime.datetime.now()
                        ),
                        "row_key": "delidautomation",
                    }
                ]
            },
            "payee_field": "co_5_delete_newemail",
            "context": {
                "<EMAIL>": {
                    "payee": [
                        {
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("500000.00"),
                            "payout_frequency": "Monthly",
                            "data_payout_frequency": "Monthly",
                            "variable_pay": Decimal("41666.66666666666666666666667"),
                        }
                    ]
                }
            },
        }
        delete_hierarchy_cache(1)
        delete_commission_cache(1)
        evaluation_modes = ["vectorize", "serial"]
        original_value = get_evaluation_mode(client_id=1)
        for evaluation_mode in evaluation_modes:
            set_client_feature(
                client_id=1, label="evaluation_mode", value=evaluation_mode
            )
            evaluate_tier = criteria_calculation_service.evaluate_tier(
                1,
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
                timezone.now(),
                [
                    {
                        "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                        "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                        "name": "delete_amount",
                        "meta": {
                            "data_type_id": 1,
                            "system_name": "co_5_delete_amount",
                            "model_name": "Delete_Automation_sheet",
                            "category": None,
                        },
                        "tags": None,
                        "data_type": "Integer",
                        "type": "VARIABLE",
                    }
                ],
                "<EMAIL>",
                {
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "payee_field": "co_5_delete_newemail",
                    "date_field": "co_5_delete_closed_date",
                },
                False,
                True,
                [
                    {
                        "lower_bound": "-INF",
                        "upper_bound": 200000,
                        "expression_stack": [
                            {
                                "function_name": "CONSTANT",
                                "name": 1,
                                "args": ["Integer", 1],
                                "data_type": "Integer",
                                "type": "VARIABLE",
                                "token_category": "DYNAMIC",
                            }
                        ],
                        "tier_key": "45d2ae2c-7927-43e0-a56a-1c4a8e86a251",
                        "is_valid": True,
                        "is_override_slab": False,
                        "tier_name": "Tier 0",
                    },
                    {
                        "lower_bound": 2,
                        "upper_bound": "INF",
                        "expression_stack": [
                            {
                                "function_name": "CONSTANT",
                                "name": 2,
                                "args": ["Integer", 2],
                                "data_type": "Integer",
                                "type": "VARIABLE",
                                "token_category": "DYNAMIC",
                            }
                        ],
                        "tier_key": "baa235f0-97f3-4b2c-8ce4-d78bcb07ea65",
                        "is_valid": True,
                        "is_override_slab": False,
                        "is_last_tier": True,
                        "tier_name": "Tier 1",
                    },
                ],
                "TieredSum",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
            )
            assert evaluate_tier["result"] == [
                {
                    "id": None,
                    "tier_value": 500000,
                    "tier_id": 1,
                    "tier_name": "Tier 1",
                    "commission": Decimal("2"),
                    "commission_date": last_day_of_month(datetime.datetime.now()),
                }
            ]
        set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

    @patch(
        "commission_engine.services.commission_calculation_service.evaluate_vectorized_wrapper.get_ast_data_type"
    )
    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    def test_evaluate_criteria_tier(
        self, mock_get_data, mock_get_ast_data_type, resource
    ):
        mock_get_data.return_value = {
            "merged_data": {
                "<EMAIL>": [
                    {
                        "co_5_delete_newid": "delidautomation",
                        "co_5_delete_amount": 500000,
                        "co_5_delete_newemail": "<EMAIL>",
                        "co_5_delete_oppstage": "ClosedWon",
                        "co_5_delete_closed_date": first_day_of_month(
                            datetime.datetime.now()
                        ),
                        "row_key": "delidautomation",
                    }
                ]
            },
            "payee_field": "co_5_delete_newemail",
            "context": {
                "<EMAIL>": {
                    "payee": [
                        {
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("500000.00"),
                            "payout_frequency": "Monthly",
                            "data_payout_frequency": "Monthly",
                            "variable_pay": Decimal("41666.66666666666666666666667"),
                        }
                    ]
                }
            },
        }
        mock_get_ast_data_type.return_value = "integer"
        delete_hierarchy_cache(1)
        delete_commission_cache(1)
        evaluation_modes = ["vectorize", "serial"]
        original_value = get_evaluation_mode(client_id=1)
        for evaluation_mode in evaluation_modes:
            set_client_feature(
                client_id=1, label="evaluation_mode", value=evaluation_mode
            )
            evaluate_criteria = criteria_calculation_service.evaluate_criteria(
                1,
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
                "<EMAIL>",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                first_day_of_month(datetime.datetime.now()),
                timezone.now(),
                {
                    "type": "Tier",
                    "part1": [
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Delete_Automation_sheet",
                                "system_name": "co_5_delete_amount",
                                "data_type_id": 1,
                            },
                            "name": "delete_amount",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                            "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                        },
                        {
                            "name": "*",
                            "type": "OPERATOR",
                            "alt_name": "MULTIPLY",
                            "category": "ARITHMETIC",
                            "typename": "",
                            "multi_valued": False,
                            "output_types": ["Integer"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                            ],
                            "output_type_ids": [1],
                            "operand_type_ids": [1, 6, 9, 10, 11],
                        },
                        {
                            "args": ["Percentage", 10],
                            "name": "10%",
                            "type": "VARIABLE",
                            "data_type": "Percentage",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                    ],
                    "part2": [
                        {
                            "is_valid": True,
                            "tier_key": "4a07ef53-412e-4aed-96b0-51448ff57ad5",
                            "tier_name": "Tier 0",
                            "lower_bound": "-INF",
                            "upper_bound": 2000,
                            "expression_stack": [
                                {
                                    "args": ["Percentage", 10],
                                    "name": "10%",
                                    "type": "VARIABLE",
                                    "data_type": "Percentage",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                }
                            ],
                            "is_override_slab": False,
                        },
                        {
                            "is_valid": True,
                            "tier_key": "a463438e-8106-4bc0-b551-5a69e1377450",
                            "tier_name": "Tier 1",
                            "lower_bound": 2000,
                            "upper_bound": "INF",
                            "is_last_tier": True,
                            "expression_stack": [
                                {
                                    "args": ["Percentage", 8],
                                    "name": "8%",
                                    "type": "VARIABLE",
                                    "data_type": "Percentage",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                }
                            ],
                            "is_override_slab": False,
                        },
                    ],
                    "is_valid": True,
                    "tier_type": "TieredSum",
                    "date_field": "co_5_delete_closed_date",
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "is_override": False,
                    "payee_field": "co_5_delete_newemail",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "expression_type": "Simple",
                    "is_line_item_level": True,
                },
            )
            assert evaluate_criteria["result"] == [
                {
                    "id": "delidautomation",
                    "commission": Decimal("0.1"),
                    "commission_date": first_day_of_month(datetime.datetime.now()),
                    "tier_id": 0,
                    "tier_name": "Tier 0",
                    "original_tier_id": 0,
                    "original_tier_name": "Tier 0",
                    "tier_value": Decimal("2000.00"),
                },
                {
                    "id": "delidautomation",
                    "commission": Decimal("0.08"),
                    "commission_date": first_day_of_month(datetime.datetime.now()),
                    "tier_id": 1,
                    "tier_name": "Tier 1",
                    "original_tier_id": 1,
                    "original_tier_name": "Tier 1",
                    "tier_value": Decimal("48000.00"),
                },
            ]
        set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    @pytest.mark.parametrize(
        "criteria_type, criteria_data, result",
        [
            (
                "Simple",
                {
                    "ast": [
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Delete_Automation_sheet",
                                "system_name": "co_5_delete_amount",
                                "data_type_id": 1,
                            },
                            "name": "delete_amount",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                            "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                        },
                        {
                            "name": "*",
                            "type": "OPERATOR",
                            "alt_name": "MULTIPLY",
                            "category": "ARITHMETIC",
                            "__typename": "",
                            "multi_valued": False,
                            "output_types": ["Integer"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                            ],
                            "output_type_ids": [1],
                            "operand_type_ids": [1, 6, 9, 10, 11],
                        },
                        {
                            "args": ["Percentage", 10],
                            "name": "10%",
                            "type": "VARIABLE",
                            "data_type": "Percentage",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                    ],
                    "type": "Simple",
                    "is_valid": True,
                    "date_field": "co_5_delete_closed_date",
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "payee_field": "co_5_delete_newemail",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "is_line_item_level": True,
                },
                [
                    {
                        "id": "delidautomation",
                        "commission": Decimal("50000.0"),
                        "commission_date": first_day_of_month(datetime.datetime.now()),
                    }
                ],
            ),
            (
                "Team_Simple",
                {
                    "ast": [
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Delete_Automation_sheet",
                                "system_name": "co_5_delete_amount",
                                "data_type_id": 1,
                            },
                            "name": "delete_amount",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                            "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                        },
                        {
                            "name": "*",
                            "type": "OPERATOR",
                            "alt_name": "MULTIPLY",
                            "category": "ARITHMETIC",
                            "typename": "",
                            "multi_valued": False,
                            "output_types": ["Integer"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                            ],
                            "output_type_ids": [1],
                            "operand_type_ids": [1, 6, 9, 10, 11],
                        },
                        {
                            "args": ["Percentage", 10],
                            "name": "10%",
                            "type": "VARIABLE",
                            "data_type": "Percentage",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                    ],
                    "team": "Delete Team",
                    "type": "Simple",
                    "is_valid": True,
                    "date_field": "co_5_delete_closed_date",
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "payee_field": "co_5_delete_newemail",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "criteria_type": "Simple",
                    "is_line_item_level": True,
                },
                [
                    {
                        "id": "delidautomation",
                        "commission": Decimal("50000.0"),
                        "commission_date": first_day_of_month(datetime.datetime.now()),
                        "source_payee": "<EMAIL>",
                    }
                ],
            ),
        ],
    )
    def test_evaluate_criteria(
        self, mock_get_data, criteria_type, criteria_data, result, resource
    ):
        mock_get_data.return_value = {
            "merged_data": {
                "<EMAIL>": [
                    {
                        "co_5_delete_newid": "delidautomation",
                        "co_5_delete_amount": 500000,
                        "co_5_delete_newemail": "<EMAIL>",
                        "co_5_delete_oppstage": "ClosedWon",
                        "co_5_delete_closed_date": first_day_of_month(
                            datetime.datetime.now()
                        ),
                        "row_key": "delidautomation",
                    }
                ]
            },
            "payee_field": "co_5_delete_newemail",
            "context": {
                "<EMAIL>": {
                    "payee": [
                        {
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("500000.00"),
                            "payout_frequency": "Monthly",
                            "data_payout_frequency": "Monthly",
                            "variable_pay": Decimal("41666.66666666666666666666667"),
                        }
                    ]
                }
            },
        }
        delete_hierarchy_cache(1)
        delete_commission_cache(1)
        evaluation_modes = ["vectorize", "serial"]
        original_value = get_evaluation_mode(client_id=1)
        for evaluation_mode in evaluation_modes:
            set_client_feature(
                client_id=1, label="evaluation_mode", value=evaluation_mode
            )
            evaluate_criteria = criteria_calculation_service.evaluate_criteria(
                1,
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
                "<EMAIL>",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                timezone.now(),
                first_day_of_month(datetime.datetime.now()),
                criteria_data,
            )
            assert evaluate_criteria["result"] == result
            assert evaluate_criteria["summary"] == {}
            assert evaluate_criteria["is_line_item_level"]
        set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    @pytest.mark.parametrize(
        "line_item_level, payee_email",
        [
            (True, "<EMAIL>"),
            (False, "<EMAIL>"),
            (True, "<EMAIL>"),
        ],
    )
    def test_evaluate_quota(
        self, mock_get_data, line_item_level, payee_email, resource
    ):
        delete_hierarchy_cache(1)
        delete_commission_cache(1)
        if payee_email == "<EMAIL>":
            mock_get_data.return_value = {
                "merged_data": {
                    "<EMAIL>": [
                        {
                            "co_5_delete_newid": "delidautomation",
                            "co_5_delete_amount": 500000,
                            "co_5_delete_newemail": "<EMAIL>",
                            "co_5_delete_oppstage": "ClosedWon",
                            "co_5_delete_closed_date": first_day_of_month(
                                datetime.datetime.now()
                            ),
                            "row_key": "delidautomation",
                        }
                    ]
                },
                "payee_field": "co_5_delete_newemail",
                "context": {
                    "<EMAIL>": {
                        "payee": [
                            {
                                "email": "<EMAIL>",
                                "actual_variable_pay": Decimal("500000.00"),
                                "payout_frequency": "Monthly",
                                "data_payout_frequency": "Monthly",
                                "variable_pay": Decimal(
                                    "41666.66666666666666666666667"
                                ),
                            }
                        ]
                    }
                },
            }
        EmployeeQuota.objects.create(
            client_id=1,
            knowledge_begin_date=timezone.now(),
            employee_email_id="<EMAIL>",
            quota_category_name="Primary",
            quota_type="Monthly",
            quota_schedule_type="Monthly",
            is_team_quota=False,
            quota_year=arrow.now().format("YYYY"),
            effective_start_date=make_aware(
                datetime.datetime(int(arrow.now().format("YYYY")), 1, 1)
            ),
            schedule_quota=[
                {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
            ],
            payout_quota={
                "Apr": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                "Aug": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                "Dec": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                "Feb": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                "Jan": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                "Jul": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                "Jun": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                "Mar": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                "May": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                "Nov": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                "Oct": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                "Sep": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
            },
        )
        evaluation_modes = ["vectorize", "serial"]
        original_value = get_evaluation_mode(client_id=1)
        for evaluation_mode in evaluation_modes:
            set_client_feature(
                client_id=1, label="evaluation_mode", value=evaluation_mode
            )
            evaluate_quota = criteria_calculation_service.evaluate_quota(
                1,
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
                timezone.now(),
                [
                    {
                        "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                        "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                        "name": "delete_amount",
                        "meta": {
                            "data_type_id": 1,
                            "system_name": "co_5_delete_amount",
                            "model_name": "Delete_Automation_sheet",
                            "category": None,
                        },
                        "tags": None,
                        "data_type": "Integer",
                        "type": "VARIABLE",
                    }
                ],
                payee_email,
                {
                    "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                    "payee_field": "co_5_delete_newemail",
                    "date_field": "co_5_delete_closed_date",
                },
                line_item_level,
                None,
                [
                    {
                        "lower_bound": "-INF",
                        "upper_bound": 50,
                        "expression_stack": [
                            {
                                "function_name": "TieredPercentage",
                                "name": "TieredPercentage()",
                                "args": [],
                                "data_type": "Percentage",
                                "type": "VARIABLE",
                                "token_category": "DYNAMIC",
                            },
                            {
                                "name": "*",
                                "alt_name": "MULTIPLY",
                                "category": "ARITHMETIC",
                                "operand_type_ids": [1, 6, 9, 10, 11],
                                "output_type_ids": [1],
                                "needs_operand": True,
                                "multi_valued": False,
                                "__typename": "",
                                "output_types": ["Integer"],
                                "operand_types": [
                                    "Integer",
                                    "Percentage",
                                    "DayDuration",
                                    "MinuteDuration",
                                    "SecondDuration",
                                ],
                                "type": "OPERATOR",
                            },
                            {
                                "function_name": "CONSTANT",
                                "name": 5,
                                "args": ["Integer", 5],
                                "data_type": "Integer",
                                "type": "VARIABLE",
                                "token_category": "DYNAMIC",
                            },
                        ],
                        "tier_key": "c80ada66-500f-44c4-a8cd-15fd12a37144",
                        "is_valid": True,
                        "is_override_slab": False,
                        "tier_name": "Tier 0",
                    },
                    {
                        "lower_bound": 50,
                        "upper_bound": "INF",
                        "expression_stack": [
                            {
                                "function_name": "TieredValue",
                                "name": "TieredValue()",
                                "args": [],
                                "data_type": "Integer",
                                "type": "VARIABLE",
                                "token_category": "DYNAMIC",
                            },
                            {
                                "name": "/",
                                "alt_name": "DIVIDE",
                                "category": "ARITHMETIC",
                                "operand_type_ids": [1, 9, 10, 11],
                                "output_type_ids": [1],
                                "needs_operand": True,
                                "multi_valued": False,
                                "output_types": ["Integer"],
                                "operand_types": [
                                    "Integer",
                                    "DayDuration",
                                    "MinuteDuration",
                                    "SecondDuration",
                                ],
                                "type": "OPERATOR",
                            },
                            {
                                "function_name": "CONSTANT",
                                "name": 2,
                                "args": ["Integer", 2],
                                "data_type": "Integer",
                                "type": "VARIABLE",
                                "token_category": "DYNAMIC",
                            },
                        ],
                        "tier_key": "60a78f50-4264-4e40-a984-0e8c28e0e255",
                        "is_valid": True,
                        "is_override_slab": False,
                        "is_last_tier": True,
                        "tier_name": "Tier 1",
                    },
                ],
                "TieredSum",
                "QuotaAttainment",
                "Primary",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
            )
            if line_item_level and payee_email == "<EMAIL>":
                assert evaluate_quota["result"] == [
                    {
                        "id": "delidautomation",
                        "commission": Decimal("2.5"),
                        "commission_date": first_day_of_month(datetime.datetime.now()),
                        "tier_id": 0,
                        "tier_name": "Tier 0",
                        "original_tier_id": 0,
                        "original_tier_name": "Tier 0",
                        "quota_erosion": Decimal("5000.00"),
                    },
                    {
                        "id": "delidautomation",
                        "commission": Decimal("247500.0"),
                        "commission_date": first_day_of_month(datetime.datetime.now()),
                        "tier_id": 1,
                        "tier_name": "Tier 1",
                        "original_tier_id": 1,
                        "original_tier_name": "Tier 1",
                        "quota_erosion": Decimal("495000.0"),
                    },
                ]

                assert evaluate_quota["is_line_item_level"]
            elif not line_item_level:
                assert not evaluate_quota["is_line_item_level"]
                assert evaluate_quota["result"] == [
                    {
                        "id": None,
                        "quota_erosion": 500000,
                        "tier_id": 1,
                        "tier_name": "Tier 1",
                        "commission": Decimal("250000"),
                        "commission_date": last_day_of_month(timezone.now()),
                    }
                ]
            elif line_item_level and payee_email == "<EMAIL>":
                assert evaluate_quota["result"] == [
                    {
                        "commission": 0,
                        "commission_date": last_day_of_month(timezone.now()),
                        "quota_erosion": 0,
                        "tier_id": 0,
                        "tier_name": "Tier 0",
                    }
                ]
        set_client_feature(client_id=1, label="evaluation_mode", value=original_value)

    @patch(
        "commission_engine.services.commission_calculation_service.criteria_calculation_service.get_data"
    )
    def test_exec_team_criteria_quota(self, mock_get_data, resource):
        expected_output = [
            [
                {
                    "id": "delidautomation ignore",
                    "commission": Decimal("1"),
                    "commission_date": first_day_of_month(datetime.datetime.now()),
                    "tier_id": 0,
                    "tier_name": "Tier 0",
                    "original_tier_id": 0,
                    "original_tier_name": "Tier 0",
                    "quota_erosion": Decimal("70.00"),
                },
                {
                    "id": "delidautomation ignore",
                    "commission": Decimal("2"),
                    "commission_date": first_day_of_month(datetime.datetime.now()),
                    "tier_id": 1,
                    "tier_name": "Tier 1",
                    "original_tier_id": 1,
                    "original_tier_name": "Tier 1",
                    "quota_erosion": Decimal("299930.00"),
                },
            ],
            [
                {
                    "id": "delidautomation ignore",
                    "commission": 0,
                    "commission_date": first_day_of_month(datetime.datetime.now()),
                    "tier_id": 0,
                    "tier_name": "Tier 0",
                    "original_tier_id": 0,
                    "original_tier_name": "Tier 0",
                    "quota_erosion": Decimal("300000.00"),
                }
            ],
            [
                {
                    "id": "delidautomation ignore",
                    "commission": 0,
                    "commission_date": first_day_of_month(datetime.datetime.now()),
                    "tier_id": 0,
                    "tier_name": "Tier 0",
                    "original_tier_id": 0,
                    "original_tier_name": "Tier 0",
                    "quota_erosion": Decimal("300000.00"),
                }
            ],
            # Overall sum for Quota, value based allocation - with cum_qe = 0
            [
                {
                    "id": None,
                    "quota_erosion": Decimal("300000.0"),
                    "tier_id": 0,
                    "tier_name": "Tier 0",
                    "commission": 0,
                    "commission_date": last_day_of_month(datetime.datetime.now()),
                }
            ],
            # Overall sum for Quota, value based allocation - with cum_qe != 0
            [
                {
                    "id": None,
                    "quota_erosion": Decimal("300000.0"),
                    "tier_id": 0,
                    "tier_name": "Tier 0",
                    "commission": 0,
                    "commission_date": datetime.datetime(
                        2024, 5, 31, 23, 59, 59, 999999
                    ),
                }
            ],
        ]
        employee_email = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]
        possible_is_team_quota = ["True", "False", "True", "True", "True"]
        quotas = [
            [
                "Primary",
                "Monthly",
                [
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                ],
                {
                    "Apr": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Aug": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Dec": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Feb": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Jan": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Jul": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Jun": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Mar": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "May": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Nov": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Oct": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Sep": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                },
            ],
            [
                "Primary",
                "Monthly",
                [
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                ],
                {
                    "Apr": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Aug": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Dec": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Feb": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Jan": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Jul": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Jun": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Mar": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "May": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Nov": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Oct": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                    "Sep": {"ramp": 100, "quota": 10000, "ramped_quota": "10000.00"},
                },
            ],
            [
                "Secondary",
                "Quarterly",
                [
                    {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                ],
                {
                    "Apr": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Aug": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Dec": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Feb": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Jan": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Jul": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Jun": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Mar": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "May": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Nov": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Oct": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Sep": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                },
            ],
            [
                "Secondary",
                "Quarterly",
                [
                    {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                ],
                {
                    "Apr": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Aug": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Dec": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Feb": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Jan": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Jul": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Jun": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Mar": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "May": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Nov": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Oct": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Sep": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                },
            ],
            [
                "Secondary",
                "Quarterly",
                [
                    {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                ],
                {
                    "Apr": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Aug": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Dec": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Feb": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Jan": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Jul": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Jun": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Mar": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "May": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Nov": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Oct": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                    "Sep": {"ramp": 0, "quota": 0, "ramped_quota": "0.00"},
                },
            ],
        ]
        period_dates = [
            [
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
            ],
            [
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
            ],
            [
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
            ],
            [
                first_day_of_month(datetime.datetime.now()),
                last_day_of_month(datetime.datetime.now()),
            ],
            [
                first_day_of_month(datetime.datetime(2024, 5, 1)),
                last_day_of_month(datetime.datetime(2024, 5, 31)),
            ],
        ]
        mock_get_data.return_value = {
            "merged_data": {
                "<EMAIL>": [
                    {
                        "co_5_delete_newid": "delidautomation",
                        "co_5_delete_amount": 500000,
                        "co_5_delete_newemail": "<EMAIL>",
                        "co_5_delete_oppstage": "ClosedWon",
                        "co_5_delete_closed_date": first_day_of_month(
                            datetime.datetime.now()
                        ),
                        "row_key": "delidautomation",
                    }
                ]
            },
            "payee_field": "co_5_delete_newemail",
            "context": {
                "<EMAIL>": {
                    "payee": [
                        {
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("500000.00"),
                            "payout_frequency": "Monthly",
                            "data_payout_frequency": "Monthly",
                            "variable_pay": Decimal("41666.66666666666666666666667"),
                        }
                    ]
                }
            },
        }
        for index in range(len(possible_is_team_quota)):
            delete_hierarchy_cache(1)
            delete_commission_cache(1)
            EmployeeQuota.objects.create(
                client_id=1,
                knowledge_begin_date=timezone.now(),
                employee_email_id=employee_email[index],
                quota_category_name=quotas[index][0],
                quota_type=quotas[index][1],
                quota_schedule_type=quotas[index][1],
                is_team_quota=possible_is_team_quota[index],
                quota_year=arrow.now().format("YYYY"),
                effective_start_date=make_aware(
                    datetime.datetime(int(arrow.now().format("YYYY")), 1, 1)
                ),
                schedule_quota=quotas[index][2],
                payout_quota=quotas[index][3],
            )
            evaluation_modes = ["vectorize", "serial"]
            original_value = get_evaluation_mode(client_id=1)

            # Using overall sum case for quota criteria - only for cases 4 and 5
            is_line_item_level = False if (index == 3 or index == 4) else True

            if index == 4:
                QuotaErosion.objects.create(
                    client_id=1,
                    knowledge_begin_date=first_day_of_month(
                        datetime.datetime(2024, 4, 1)
                    ),
                    knowledge_end_date=None,
                    period_start_date=first_day_of_month(datetime.datetime(2024, 4, 1)),
                    period_end_date=last_day_of_month(datetime.datetime(2024, 4, 30)),
                    payee_email_id="<EMAIL>",
                    commission_plan_id="0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    criteria_id="0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    quota_category_name="Secondary",
                    qv=0,
                    cumulative_qe=0,
                    line_item_type="Sheet1",
                    is_team=True,
                    team_id="ashiq@everstage.com_leader_type",
                    tier_id=0,
                    quota_erosion=1000,
                    primary_kd=first_day_of_month(datetime.datetime(2024, 4, 1)),
                    secondary_kd=first_day_of_month(datetime.datetime(2024, 4, 1)),
                )

            for evaluation_mode in evaluation_modes:
                set_client_feature(
                    client_id=1, label="evaluation_mode", value=evaluation_mode
                )
                exec_team = criteria_calculation_service.exec_team_criteria(
                    1,
                    period_dates[index][0],
                    period_dates[index][1],
                    timezone.now(),
                    employee_email[index],
                    {
                        "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                        "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                        "payee_field": "co_5_delete_newemail",
                        "date_field": "co_5_delete_closed_date",
                    },
                    is_line_item_level,
                    None,
                    {
                        "merged_data": [
                            {
                                "co_5_delete_newid": "delidautomation ignore",
                                "co_5_delete_amount": 300000.0,
                                "co_5_delete_newemail": "<EMAIL>",
                                "co_5_delete_oppstage": "ClosedWon",
                                "co_5_delete_closed_date": first_day_of_month(
                                    datetime.datetime.now()
                                ),
                                "row_key": "delidautomation ignore",
                                "email": "<EMAIL>",
                                "actual_variable_pay": Decimal("50000.00"),
                                "payout_frequency": "Monthly",
                                "variable_pay": Decimal(
                                    "4166.666666666666666666666667"
                                ),
                            },
                        ],
                        "context": {
                            "payee": [
                                {
                                    "email": "<EMAIL>",
                                    "actual_variable_pay": Decimal("50000.00"),
                                    "payout_frequency": "Monthly",
                                    "data_payout_frequency": "Monthly",
                                    "variable_pay": Decimal(
                                        "4166.666666666666666666666667"
                                    ),
                                }
                            ]
                        },
                    },
                    "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                    first_day_of_month(datetime.datetime.now()),
                    {
                        "infix_exp": None,
                        "tier_type": "TieredSum",
                        "part1_exp": [
                            {
                                "databook_id": "0fecc4d7-70da-4a08-9c80-37a0fbf83a73",
                                "datasheet_id": "d27620ec-eb1f-40f5-a699-6b50aa7a4688",
                                "name": "delete_amount",
                                "meta": {
                                    "data_type_id": 1,
                                    "system_name": "co_5_delete_amount",
                                    "model_name": "Delete_Automation_sheet",
                                    "category": None,
                                },
                                "tags": None,
                                "data_type": "Integer",
                                "type": "VARIABLE",
                            }
                        ],
                        "part2_exp": [
                            {
                                "lower_bound": "-INF",
                                "upper_bound": 70,
                                "expression_stack": [
                                    {
                                        "function_name": "CONSTANT",
                                        "name": 1,
                                        "args": ["Integer", 1],
                                        "data_type": "Integer",
                                        "type": "VARIABLE",
                                        "token_category": "DYNAMIC",
                                    }
                                ],
                                "tier_key": "64324380-4522-4931-bed8-cb06dabf730b",
                                "is_valid": True,
                                "is_override_slab": False,
                                "tier_name": "Tier 0",
                            },
                            {
                                "lower_bound": 70,
                                "upper_bound": "INF",
                                "expression_stack": [
                                    {
                                        "function_name": "CONSTANT",
                                        "name": 2,
                                        "args": ["Integer", 2],
                                        "data_type": "Integer",
                                        "type": "VARIABLE",
                                        "token_category": "DYNAMIC",
                                    }
                                ],
                                "tier_key": "fbe8b306-4415-4eae-97ff-c686162679af",
                                "is_valid": True,
                                "is_override_slab": False,
                                "is_last_tier": True,
                                "tier_name": "Tier 1",
                            },
                        ],
                        "part1_type": "Value",
                        "criteria_type": "Quota",
                        "team_name": "Payee & Team",
                        "category": quotas[index][0],
                        "team_type": "leader_type",
                        "team_members": [],
                        "team_owner_email_id": "<EMAIL>",
                        "effective_date_map": None,
                    },
                )
                assert exec_team["result"] == expected_output[index]
            set_client_feature(
                client_id=1, label="evaluation_mode", value=original_value
            )

            delete_commission_cache(1)
            delete_hierarchy_cache(1)

    @pytest.mark.parametrize(
        "total_commission, row_keys, do_nothing_keys, commission_date, show_do_nothing, expected_result",
        [
            # Scenario 1: No row keys and do_nothing_keys and show_do_nothing is False
            (
                float("-inf"),
                [],
                [],
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                False,
                [
                    {
                        "id": None,
                        "commission": 0,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    }
                ],
            ),
            # Scenario 2: No row keys and do_nothing_keys and show_do_nothing is True
            (
                float("-inf"),
                [],
                [],
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                True,
                [
                    {
                        "id": None,
                        "commission": 0,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    }
                ],
            ),
            # Scenario 3: row keys and do_nothing_keys and show_do_nothing is False
            (
                float("-inf"),
                ["a", "b", "c"],
                ["a", "b", "c"],
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                False,
                [
                    {
                        "id": None,
                        "commission": 0,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    }
                ],
            ),
            # Scenario 4: row keys and do_nothing_keys and show_do_nothing is True
            (
                float("-inf"),
                ["a", "b"],
                ["a", "b"],
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                True,
                [
                    {
                        "id": "a",
                        "commission": 0,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                        "show_do_nothing": True,
                    },
                    {
                        "id": "b",
                        "commission": 0,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                        "show_do_nothing": True,
                    },
                ],
            ),
            # Scenario 5: no do_nothing_keys and show_do_nothing is True
            (
                Decimal(123.45),
                ["a", "b"],
                [],
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                True,
                [
                    {
                        "id": "a",
                        "commission": Decimal(123.45) / 2,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                    {
                        "id": "b",
                        "commission": Decimal(123.45) / 2,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                ],
            ),
            # Scenario 6: no do_nothing_keys and show_do_nothing is False
            (
                Decimal(123.45),
                ["a", "b"],
                [],
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                True,
                [
                    {
                        "id": "a",
                        "commission": Decimal(123.45) / 2,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                    {
                        "id": "b",
                        "commission": Decimal(123.45) / 2,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                ],
            ),
            # Scenario 7: zero total commission
            (
                Decimal(0),
                ["a", "b"],
                [],
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                True,
                [
                    {
                        "id": "a",
                        "commission": 0,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                    {
                        "id": "b",
                        "commission": 0,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                ],
            ),
        ],
    )
    def test_get_line_item_level_commission_for_non_tier_summation_criterias(
        self,
        total_commission,
        row_keys,
        do_nothing_keys,
        commission_date,
        show_do_nothing,
        expected_result,
    ):
        actual_result = get_line_item_level_commission_for_non_tier_summation_criterias(
            total_commission=total_commission,
            row_keys=row_keys,
            do_nothing_keys=do_nothing_keys,
            commission_date=commission_date,
            show_do_nothing=show_do_nothing,
        )

        assert actual_result == expected_result

    @pytest.mark.parametrize(
        "part1_result, do_nothing_keys, total_commission, tier_id, tier_name, commission_date, show_do_nothing, tier_key, expected_result",
        [
            # Scenario 1: Basic scenario with valid keys with no do_nothing_keys
            (
                {"key1": Decimal(10), "key2": Decimal(20)},
                [],
                Decimal(30),
                1,
                "Tier 1",
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                False,
                "quota_erosion",
                [
                    {
                        "id": "key1",
                        "commission": Decimal(15),
                        "tier_id": 1,
                        "tier_name": "Tier 1",
                        "quota_erosion": Decimal(10),
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                    {
                        "id": "key2",
                        "commission": Decimal(15),
                        "tier_id": 1,
                        "tier_name": "Tier 1",
                        "quota_erosion": Decimal(20),
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                ],
            ),
            # Scenario 2: do_nothing_keys present but show_do_nothing is False
            (
                {"key1": Decimal(10), "key2": Decimal(20)},
                ["key2"],
                Decimal(20),
                1,
                "Tier 1",
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                False,
                "quota_erosion",
                [
                    {
                        "id": "key1",
                        "commission": Decimal(20),
                        "tier_id": 1,
                        "tier_name": "Tier 1",
                        "quota_erosion": Decimal(10),
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                ],
            ),
            # Scenario 3: do_nothing_keys present and show_do_nothing is True
            (
                {"key1": Decimal(10), "key2": Decimal(20)},
                ["key2"],
                Decimal(20),
                1,
                "Tier 1",
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                True,
                "quota_erosion",
                [
                    {
                        "id": "key1",
                        "commission": 20,
                        "tier_id": 1,
                        "tier_name": "Tier 1",
                        "quota_erosion": 10,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                    {
                        "id": "key2",
                        "commission": 0,
                        "tier_id": "-",
                        "tier_name": "-",
                        "quota_erosion": 0,
                        "show_do_nothing": True,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                ],
            ),
            # Scenario 4: All keys are do_nothing_keys and show_do_nothing is True
            (
                {"key1": Decimal(10), "key2": Decimal(20)},
                ["key1", "key2"],
                0,
                1,
                "Tier 1",
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                True,
                "quota_erosion",
                [
                    {
                        "id": "key1",
                        "commission": 0,
                        "tier_id": "-",
                        "tier_name": "-",
                        "quota_erosion": 0,
                        "show_do_nothing": True,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                    {
                        "id": "key2",
                        "commission": 0,
                        "tier_id": "-",
                        "tier_name": "-",
                        "quota_erosion": 0,
                        "show_do_nothing": True,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                ],
            ),
            # Scenario 5: No valid keys and show_do_nothing is True
            (
                {},
                [],
                float("-inf"),
                0,
                "Tier 0",
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                True,
                "quota_erosion",
                [
                    {
                        "id": None,
                        "commission": 0,
                        "tier_id": 0,
                        "tier_name": "Tier 0",
                        "quota_erosion": 0,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                ],
            ),
            # Scenario 6: No valid keys and show_do_nothing is False
            (
                {},
                [],
                float("-inf"),
                0,
                "Tier 0",
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                False,
                "quota_erosion",
                [
                    {
                        "id": None,
                        "commission": 0,
                        "tier_id": 0,
                        "tier_name": "Tier 0",
                        "quota_erosion": 0,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                ],
            ),
            # Scenario 7: Different tier_key
            (
                {"key1": 10},
                [],
                10,
                "T1",
                "Tier 1",
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                False,
                "tier_value",
                [
                    {
                        "id": "key1",
                        "commission": 10,
                        "tier_id": "T1",
                        "tier_name": "Tier 1",
                        "tier_value": 10,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                ],
            ),
            # Scenario 8: Zero total_commission
            (
                {"key1": 10, "key2": 20},
                [],
                0,
                0,
                "Tier 0",
                last_day_of_month(datetime.datetime(2023, 11, 1)),
                False,
                "quota_erosion",
                [
                    {
                        "id": "key1",
                        "commission": 0,
                        "tier_id": 0,
                        "tier_name": "Tier 0",
                        "quota_erosion": 10,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                    {
                        "id": "key2",
                        "commission": 0,
                        "tier_id": 0,
                        "tier_name": "Tier 0",
                        "quota_erosion": 20,
                        "commission_date": last_day_of_month(
                            datetime.datetime(2023, 11, 1)
                        ),
                    },
                ],
            ),
        ],
    )
    def test_get_line_item_level_commission_records_for_tier_summation_criterias(
        self,
        part1_result,
        do_nothing_keys,
        total_commission,
        tier_id,
        tier_name,
        commission_date,
        show_do_nothing,
        tier_key,
        expected_result,
    ):
        actual_result = (
            get_line_item_level_commission_records_for_tier_summation_criterias(
                part1_result=part1_result,
                do_nothing_keys=do_nothing_keys,
                total_commission=total_commission,
                tier_id=tier_id,
                tier_name=tier_name,
                commission_date=commission_date,
                show_do_nothing=show_do_nothing,
                tier_key=tier_key,
            )
        )
        assert actual_result == expected_result
