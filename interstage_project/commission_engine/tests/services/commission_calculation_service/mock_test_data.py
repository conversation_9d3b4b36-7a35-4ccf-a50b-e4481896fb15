# pylint: disable-all
from decimal import Decimal

from django.utils import timezone

from commission_engine.utils import first_day_of_month


def get_ast_mock(criteria_id):
    criteria_ast = {
        "bd9f050f-05b4-4b73-852e-cc973f54f127": {
            "ast": {
                "args": [
                    [
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Deals Sheet v1",
                                "system_name": "co_1_deal_status",
                                "data_type_id": 4,
                            },
                            "name": "deal status",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "String",
                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                        },
                        {
                            "name": "==",
                            "type": "OPERATOR",
                            "alt_name": "EQUALS",
                            "category": "LOGICAL",
                            "typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Date",
                                "Boolean",
                                "String",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                                "Email",
                            ],
                            "output_type_ids": [3],
                            "operand_type_ids": [1, 2, 3, 4, 6, 9, 10, 11, 12],
                        },
                        {
                            "args": ["String", "Closed Won"],
                            "name": "Closed Won",
                            "type": "VARIABLE",
                            "data_type": "String",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                    ],
                    {
                        "args": [
                            [
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_x",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount X",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "<=",
                                    "type": "OPERATOR",
                                    "alt_name": "LESSERTHANEQUALTO",
                                    "category": "LOGICAL",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Date",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [1, 2, 6, 9, 10, 11],
                                },
                                {
                                    "name": "(",
                                    "type": "LBRACKET",
                                    "alt_name": "LeftBracket",
                                    "category": "LBRACKET",
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_y",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount Y",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "+",
                                    "type": "OPERATOR",
                                    "alt_name": "PLUS",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 9, 10, 11],
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_z",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount Z",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": ")",
                                    "type": "RBRACKET",
                                    "alt_name": "RightBracket",
                                    "category": "RBRACKET",
                                },
                            ],
                            [
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_y",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount Y",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "*",
                                    "type": "OPERATOR",
                                    "alt_name": "MULTIPLY",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 6, 9, 10, 11],
                                },
                                {
                                    "args": ["Percentage", 1],
                                    "name": "1%",
                                    "type": "VARIABLE",
                                    "data_type": "Percentage",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                            ],
                            None,
                        ],
                        "name": "IF",
                        "type": "VARIABLE",
                        "level": 1,
                        "data_type": "Integer",
                        "function_name": "IF",
                        "else_is_nested": False,
                        "then_is_nested": False,
                        "token_category": "DYNAMIC",
                        "else_do_nothing": True,
                    },
                    None,
                ],
                "name": "IF",
                "type": "VARIABLE",
                "level": 0,
                "data_type": "Integer",
                "function_name": "IF",
                "else_is_nested": False,
                "then_is_nested": True,
                "token_category": "DYNAMIC",
                "else_do_nothing": True,
            },
            "type": "Conditional",
            "is_valid": True,
            "date_field": "co_1_deal_close_date",
            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
            "payee_field": "co_1_deal_owner_mail",
            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
            "is_line_item_level": True,
        },
        "710bf17a-b5fc-49c9-9da8-0bacf435afc5": {
            "type": "Quota",
            "part1": {
                "args": [
                    [
                        {
                            "name": "(",
                            "type": "LBRACKET",
                            "alt_name": "LeftBracket",
                            "category": "LBRACKET",
                        },
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Deals Sheet v1",
                                "system_name": "co_1_deal_type",
                                "data_type_id": 4,
                            },
                            "name": "deal type",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "String",
                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                        },
                        {
                            "name": "IN",
                            "type": "OPERATOR",
                            "alt_name": "IN",
                            "category": "LOGICAL",
                            "typename": "",
                            "multi_valued": True,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "String",
                                "IntArray",
                                "StringArray",
                            ],
                            "output_type_ids": [3],
                            "operand_type_ids": [1, 4, 13, 14],
                        },
                        {
                            "args": ["StringArray", ["Business, Social"]],
                            "name": "Array - Business, Social",
                            "type": "VARIABLE",
                            "data_type": "StringArray",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                        {
                            "name": "AND",
                            "type": "OPERATOR",
                            "alt_name": "AND",
                            "category": "LOGICAL",
                            "typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": ["Boolean"],
                            "output_type_ids": [3],
                            "operand_type_ids": [3],
                        },
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Deals Sheet v1",
                                "system_name": "co_1_deal_status",
                                "data_type_id": 4,
                            },
                            "name": "deal status",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "String",
                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                        },
                        {
                            "name": "==",
                            "type": "OPERATOR",
                            "alt_name": "EQUALS",
                            "category": "LOGICAL",
                            "typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Date",
                                "Boolean",
                                "String",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                                "Email",
                            ],
                            "output_type_ids": [3],
                            "operand_type_ids": [1, 2, 3, 4, 6, 9, 10, 11, 12],
                        },
                        {
                            "args": ["String", "Closed Won"],
                            "name": "Closed Won",
                            "type": "VARIABLE",
                            "data_type": "String",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                        {
                            "name": ")",
                            "type": "RBRACKET",
                            "alt_name": "RightBracket",
                            "category": "RBRACKET",
                        },
                    ],
                    [
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Deals Sheet v1",
                                "system_name": "co_1_deal_amount_x",
                                "data_type_id": 1,
                            },
                            "name": "deal amount X",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                        }
                    ],
                    {
                        "args": [
                            [
                                {
                                    "name": "(",
                                    "type": "LBRACKET",
                                    "alt_name": "LeftBracket",
                                    "category": "LBRACKET",
                                },
                                {
                                    "args": [
                                        {
                                            "meta": {
                                                "category": None,
                                                "model_name": "Deals Sheet v1",
                                                "system_name": "co_1_deal_amount_x",
                                                "data_type_id": 1,
                                            },
                                            "name": "deal amount X",
                                            "tags": None,
                                            "type": "VARIABLE",
                                            "data_type": "Integer",
                                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                        },
                                        [
                                            {
                                                "meta": {
                                                    "category": None,
                                                    "model_name": "Deals Sheet v1",
                                                    "system_name": "co_1_deal_type",
                                                    "data_type_id": 4,
                                                },
                                                "name": "deal type",
                                                "tags": None,
                                                "type": "VARIABLE",
                                                "data_type": "String",
                                                "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                                "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                            },
                                            {
                                                "name": "NOTIN",
                                                "type": "OPERATOR",
                                                "alt_name": "NOTIN",
                                                "category": "LOGICAL",
                                                "typename": "",
                                                "multi_valued": True,
                                                "output_types": ["Boolean"],
                                                "needs_operand": True,
                                                "operand_types": [
                                                    "Integer",
                                                    "String",
                                                    "IntArray",
                                                    "StringArray",
                                                ],
                                                "output_type_ids": [3],
                                                "operand_type_ids": [1, 4, 13, 14],
                                            },
                                            {
                                                "args": [
                                                    "StringArray",
                                                    ["Business, Social"],
                                                ],
                                                "name": "Array - Business, Social",
                                                "type": "VARIABLE",
                                                "data_type": "StringArray",
                                                "function_name": "CONSTANT",
                                                "token_category": "DYNAMIC",
                                            },
                                            {
                                                "name": "AND",
                                                "type": "OPERATOR",
                                                "alt_name": "AND",
                                                "category": "LOGICAL",
                                                "typename": "",
                                                "multi_valued": False,
                                                "output_types": ["Boolean"],
                                                "needs_operand": True,
                                                "operand_types": ["Boolean"],
                                                "output_type_ids": [3],
                                                "operand_type_ids": [3],
                                            },
                                            {
                                                "meta": {
                                                    "category": None,
                                                    "model_name": "Deals Sheet v1",
                                                    "system_name": "co_1_deal_eligible",
                                                    "data_type_id": 3,
                                                },
                                                "name": "deal eligible",
                                                "tags": None,
                                                "type": "VARIABLE",
                                                "data_type": "Boolean",
                                                "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                                "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                            },
                                            {
                                                "name": "==",
                                                "type": "OPERATOR",
                                                "alt_name": "EQUALS",
                                                "category": "LOGICAL",
                                                "typename": "",
                                                "multi_valued": False,
                                                "output_types": ["Boolean"],
                                                "needs_operand": True,
                                                "operand_types": [
                                                    "Integer",
                                                    "Date",
                                                    "Boolean",
                                                    "String",
                                                    "Percentage",
                                                    "DayDuration",
                                                    "MinuteDuration",
                                                    "SecondDuration",
                                                    "Email",
                                                ],
                                                "output_type_ids": [3],
                                                "operand_type_ids": [
                                                    1,
                                                    2,
                                                    3,
                                                    4,
                                                    6,
                                                    9,
                                                    10,
                                                    11,
                                                    12,
                                                ],
                                            },
                                            {
                                                "args": ["Boolean", "True"],
                                                "name": "True",
                                                "type": "VARIABLE",
                                                "data_type": "Boolean",
                                                "function_name": "CONSTANT",
                                                "token_category": "DYNAMIC",
                                            },
                                        ],
                                    ],
                                    "name": "SUMIF(deal amount X,deal type NOTIN Array - Business, Social AND deal eligible == True)",
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "SUMIF",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": "/",
                                    "type": "OPERATOR",
                                    "alt_name": "DIVIDE",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 9, 10, 11],
                                },
                                {
                                    "args": [
                                        {
                                            "meta": {
                                                "model": "Quota",
                                                "cateogry": "CALCUALTED",
                                                "system_name": "PLP quota",
                                            },
                                            "name": "PLP quota",
                                            "data_type": "Integer",
                                        },
                                        0,
                                    ],
                                    "name": "Quota(PLP quota,0)",
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "Quota",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": ")",
                                    "type": "RBRACKET",
                                    "alt_name": "RightBracket",
                                    "category": "RBRACKET",
                                },
                                {
                                    "name": ">=",
                                    "type": "OPERATOR",
                                    "alt_name": "GREATERTHANEQUALTO",
                                    "category": "LOGICAL",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Date",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [1, 2, 6, 9, 10, 11],
                                },
                                {
                                    "args": ["Percentage", 25],
                                    "name": "25%",
                                    "type": "VARIABLE",
                                    "data_type": "Percentage",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                            ],
                            {
                                "args": [
                                    [
                                        {
                                            "args": [
                                                {
                                                    "meta": {
                                                        "category": None,
                                                        "model_name": "Deals Sheet v1",
                                                        "system_name": "co_1_deal_amount_y",
                                                        "data_type_id": 1,
                                                    },
                                                    "name": "deal amount Y",
                                                    "tags": None,
                                                    "type": "VARIABLE",
                                                    "data_type": "Integer",
                                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                                },
                                                [
                                                    {
                                                        "meta": {
                                                            "category": None,
                                                            "model_name": "Deals Sheet v1",
                                                            "system_name": "co_1_deal_amount_z",
                                                            "data_type_id": 1,
                                                        },
                                                        "name": "deal amount Z",
                                                        "tags": None,
                                                        "type": "VARIABLE",
                                                        "data_type": "Integer",
                                                        "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                                        "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                                    },
                                                    {
                                                        "name": ">=",
                                                        "type": "OPERATOR",
                                                        "alt_name": "GREATERTHANEQUALTO",
                                                        "category": "LOGICAL",
                                                        "typename": "",
                                                        "multi_valued": False,
                                                        "output_types": ["Boolean"],
                                                        "needs_operand": True,
                                                        "operand_types": [
                                                            "Integer",
                                                            "Date",
                                                            "Percentage",
                                                            "DayDuration",
                                                            "MinuteDuration",
                                                            "SecondDuration",
                                                        ],
                                                        "output_type_ids": [3],
                                                        "operand_type_ids": [
                                                            1,
                                                            2,
                                                            6,
                                                            9,
                                                            10,
                                                            11,
                                                        ],
                                                    },
                                                    {
                                                        "args": ["Integer", 13.487999],
                                                        "name": 13.487999,
                                                        "type": "VARIABLE",
                                                        "data_type": "Integer",
                                                        "function_name": "CONSTANT",
                                                        "token_category": "DYNAMIC",
                                                    },
                                                ],
                                            ],
                                            "name": "SUMIF(deal amount Y,deal amount Z >= 13.487999)",
                                            "type": "VARIABLE",
                                            "data_type": "Integer",
                                            "function_name": "SUMIF",
                                            "token_category": "DYNAMIC",
                                        },
                                        {
                                            "name": "/",
                                            "type": "OPERATOR",
                                            "alt_name": "DIVIDE",
                                            "category": "ARITHMETIC",
                                            "typename": "",
                                            "multi_valued": False,
                                            "output_types": ["Integer"],
                                            "needs_operand": True,
                                            "operand_types": [
                                                "Integer",
                                                "DayDuration",
                                                "MinuteDuration",
                                                "SecondDuration",
                                            ],
                                            "output_type_ids": [1],
                                            "operand_type_ids": [1, 9, 10, 11],
                                        },
                                        {
                                            "args": [
                                                {
                                                    "meta": {
                                                        "model": "Quota",
                                                        "cateogry": "CALCUALTED",
                                                        "system_name": "PLP quota",
                                                    },
                                                    "name": "PLP quota",
                                                    "data_type": "Integer",
                                                },
                                                0,
                                            ],
                                            "name": "Quota(PLP quota,0)",
                                            "type": "VARIABLE",
                                            "data_type": "Integer",
                                            "function_name": "Quota",
                                            "token_category": "DYNAMIC",
                                        },
                                        {
                                            "name": ">=",
                                            "type": "OPERATOR",
                                            "alt_name": "GREATERTHANEQUALTO",
                                            "category": "LOGICAL",
                                            "typename": "",
                                            "multi_valued": False,
                                            "output_types": ["Boolean"],
                                            "needs_operand": True,
                                            "operand_types": [
                                                "Integer",
                                                "Date",
                                                "Percentage",
                                                "DayDuration",
                                                "MinuteDuration",
                                                "SecondDuration",
                                            ],
                                            "output_type_ids": [3],
                                            "operand_type_ids": [1, 2, 6, 9, 10, 11],
                                        },
                                        {
                                            "args": ["Percentage", 50],
                                            "name": "50%",
                                            "type": "VARIABLE",
                                            "data_type": "Percentage",
                                            "function_name": "CONSTANT",
                                            "token_category": "DYNAMIC",
                                        },
                                    ],
                                    [
                                        {
                                            "meta": {
                                                "category": None,
                                                "model_name": "Deals Sheet v1",
                                                "system_name": "co_1_deal_amount_x",
                                                "data_type_id": 1,
                                            },
                                            "name": "deal amount X",
                                            "tags": None,
                                            "type": "VARIABLE",
                                            "data_type": "Integer",
                                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                        },
                                        {
                                            "name": "*",
                                            "type": "OPERATOR",
                                            "alt_name": "MULTIPLY",
                                            "category": "ARITHMETIC",
                                            "typename": "",
                                            "multi_valued": False,
                                            "output_types": ["Integer"],
                                            "needs_operand": True,
                                            "operand_types": [
                                                "Integer",
                                                "Percentage",
                                                "DayDuration",
                                                "MinuteDuration",
                                                "SecondDuration",
                                            ],
                                            "output_type_ids": [1],
                                            "operand_type_ids": [1, 6, 9, 10, 11],
                                        },
                                        {
                                            "args": ["Percentage", 20],
                                            "name": "20%",
                                            "type": "VARIABLE",
                                            "data_type": "Percentage",
                                            "function_name": "CONSTANT",
                                            "token_category": "DYNAMIC",
                                        },
                                    ],
                                    None,
                                ],
                                "name": "IF",
                                "type": "VARIABLE",
                                "level": 2,
                                "data_type": "Integer",
                                "function_name": "IF",
                                "else_is_nested": False,
                                "then_is_nested": False,
                                "token_category": "DYNAMIC",
                                "else_do_nothing": True,
                            },
                            [
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_y",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount Y",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "*",
                                    "type": "OPERATOR",
                                    "alt_name": "MULTIPLY",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 6, 9, 10, 11],
                                },
                                {
                                    "args": ["Percentage", 2],
                                    "name": "2%",
                                    "type": "VARIABLE",
                                    "data_type": "Percentage",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                            ],
                        ],
                        "name": "IF",
                        "type": "VARIABLE",
                        "level": 1,
                        "data_type": "Integer",
                        "function_name": "IF",
                        "else_is_nested": False,
                        "then_is_nested": True,
                        "token_category": "DYNAMIC",
                        "else_do_nothing": False,
                    },
                ],
                "name": "IF",
                "type": "VARIABLE",
                "level": 0,
                "data_type": "Integer",
                "function_name": "IF",
                "else_is_nested": True,
                "then_is_nested": False,
                "token_category": "DYNAMIC",
                "else_do_nothing": False,
            },
            "part2": [
                {
                    "is_valid": True,
                    "tier_key": "0bc7e8a1-f49d-4608-a4f2-acbcca2b50b6",
                    "tier_name": "Tier 0",
                    "lower_bound": "-INF",
                    "upper_bound": 500,
                    "expression_stack": [
                        {
                            "args": ["Integer", 0],
                            "name": 0,
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        }
                    ],
                    "is_override_slab": False,
                },
                {
                    "is_valid": True,
                    "tier_key": "3d553e1d-cad5-4e17-a986-fe48b56954e7",
                    "tier_name": "Tier 1",
                    "lower_bound": 500,
                    "upper_bound": "INF",
                    "is_last_tier": True,
                    "expression_stack": [
                        {
                            "args": ["Integer", 0],
                            "name": 0,
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        }
                    ],
                    "is_override_slab": False,
                },
            ],
            "is_quota": True,
            "is_valid": True,
            "tier_type": "TieredSum",
            "date_field": "co_1_deal_close_date",
            "quota_name": "PLP quota",
            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
            "is_override": False,
            "part_1_type": "QuotaAttainment",
            "payee_field": "co_1_deal_owner_mail",
            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
            "expression_type": "Conditional",
            "is_line_item_level": True,
        },
        "e3f70f59-035d-47ce-90c0-a5aa77520410": {
            "ast": {
                "args": [
                    [
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Deals Sheet v1",
                                "system_name": "co_1_deal_status",
                                "data_type_id": 4,
                            },
                            "name": "deal status",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "String",
                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                        },
                        {
                            "name": "==",
                            "type": "OPERATOR",
                            "alt_name": "EQUALS",
                            "category": "LOGICAL",
                            "__typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Date",
                                "Boolean",
                                "String",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                                "Email",
                            ],
                            "output_type_ids": [3],
                            "operand_type_ids": [1, 2, 3, 4, 6, 9, 10, 11, 12],
                        },
                        {
                            "args": ["String", "Closed Won"],
                            "name": "Closed Won",
                            "type": "VARIABLE",
                            "data_type": "String",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                    ],
                    {
                        "args": [
                            [
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_x",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount X",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": ">=",
                                    "type": "OPERATOR",
                                    "alt_name": "GREATERTHANEQUALTO",
                                    "category": "LOGICAL",
                                    "__typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Date",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [1, 2, 6, 9, 10, 11],
                                },
                                {
                                    "args": ["Integer", 1134.3889],
                                    "name": 1134.3889,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                            ],
                            [
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_y",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount Y",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "*",
                                    "type": "OPERATOR",
                                    "alt_name": "MULTIPLY",
                                    "category": "ARITHMETIC",
                                    "__typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 6, 9, 10, 11],
                                },
                                {
                                    "args": ["Percentage", 5],
                                    "name": "5%",
                                    "type": "VARIABLE",
                                    "data_type": "Percentage",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                            ],
                            None,
                        ],
                        "name": "IF",
                        "type": "VARIABLE",
                        "level": 1,
                        "data_type": "Integer",
                        "function_name": "IF",
                        "else_is_nested": False,
                        "then_is_nested": False,
                        "token_category": "DYNAMIC",
                        "else_do_nothing": True,
                    },
                    None,
                ],
                "name": "IF",
                "type": "VARIABLE",
                "level": 0,
                "data_type": "Integer",
                "function_name": "IF",
                "else_is_nested": False,
                "then_is_nested": True,
                "token_category": "DYNAMIC",
                "else_do_nothing": True,
            },
            "team": "Payee & Team",
            "type": "Conditional",
            "is_valid": True,
            "date_field": "co_1_deal_close_date",
            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
            "payee_field": "co_1_deal_owner_mail",
            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
            "criteria_type": "Conditional",
            "is_line_item_level": True,
        },
        "768e96cd-e1a2-4ded-9873-6a01276c4d56": {
            "ast": {
                "args": [
                    [
                        {
                            "name": "(",
                            "type": "LBRACKET",
                            "alt_name": "LeftBracket",
                            "category": "LBRACKET",
                        },
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Deals Sheet v1",
                                "system_name": "co_1_deal_status",
                                "data_type_id": 4,
                            },
                            "name": "deal status",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "String",
                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                        },
                        {
                            "name": "==",
                            "type": "OPERATOR",
                            "alt_name": "EQUALS",
                            "category": "LOGICAL",
                            "typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Date",
                                "Boolean",
                                "String",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                                "Email",
                            ],
                            "output_type_ids": [3],
                            "operand_type_ids": [1, 2, 3, 4, 6, 9, 10, 11, 12],
                        },
                        {
                            "args": ["String", "Closed Won"],
                            "name": "Closed Won",
                            "type": "VARIABLE",
                            "data_type": "String",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                        {
                            "name": "AND",
                            "type": "OPERATOR",
                            "alt_name": "AND",
                            "category": "LOGICAL",
                            "typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": ["Boolean"],
                            "output_type_ids": [3],
                            "operand_type_ids": [3],
                        },
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Deals Sheet v1",
                                "system_name": "co_1_deal_eligible",
                                "data_type_id": 3,
                            },
                            "name": "deal eligible",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "Boolean",
                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                        },
                        {
                            "name": "==",
                            "type": "OPERATOR",
                            "alt_name": "EQUALS",
                            "category": "LOGICAL",
                            "typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Date",
                                "Boolean",
                                "String",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                                "Email",
                            ],
                            "output_type_ids": [3],
                            "operand_type_ids": [1, 2, 3, 4, 6, 9, 10, 11, 12],
                        },
                        {
                            "args": ["Boolean", "True"],
                            "name": "True",
                            "type": "VARIABLE",
                            "data_type": "Boolean",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                        {
                            "name": ")",
                            "type": "RBRACKET",
                            "alt_name": "RightBracket",
                            "category": "RBRACKET",
                        },
                    ],
                    {
                        "args": [
                            [
                                {
                                    "name": "(",
                                    "type": "LBRACKET",
                                    "alt_name": "LeftBracket",
                                    "category": "LBRACKET",
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_type",
                                        "data_type_id": 4,
                                    },
                                    "name": "deal type",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "String",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "==",
                                    "type": "OPERATOR",
                                    "alt_name": "EQUALS",
                                    "category": "LOGICAL",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Date",
                                        "Boolean",
                                        "String",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                        "Email",
                                    ],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [1, 2, 3, 4, 6, 9, 10, 11, 12],
                                },
                                {
                                    "args": ["String", "New Business"],
                                    "name": "New Business",
                                    "type": "VARIABLE",
                                    "data_type": "String",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": "AND",
                                    "type": "OPERATOR",
                                    "alt_name": "AND",
                                    "category": "LOGICAL",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": ["Boolean"],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [3],
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_x",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount X",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": ">",
                                    "type": "OPERATOR",
                                    "alt_name": "GREATERTHAN",
                                    "category": "LOGICAL",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Date",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [1, 2, 6, 9, 10, 11],
                                },
                                {
                                    "args": ["Integer", 0.3],
                                    "name": 0.3,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": "AND",
                                    "type": "OPERATOR",
                                    "alt_name": "AND",
                                    "category": "LOGICAL",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": ["Boolean"],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [3],
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_y",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount Y",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "!=",
                                    "type": "OPERATOR",
                                    "alt_name": "NOTEQUALS",
                                    "category": "LOGICAL",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Date",
                                        "Boolean",
                                        "String",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                        "Email",
                                    ],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [1, 2, 3, 4, 6, 9, 10, 11, 12],
                                },
                                {
                                    "args": [
                                        {
                                            "meta": {
                                                "model": "AConfig",
                                                "cateogry": "CALCUALTED",
                                                "system_name": "FTE factor",
                                            },
                                            "name": "FTE factor",
                                            "data_type": "Integer",
                                        }
                                    ],
                                    "name": "Config(FTE factor)",
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "Config",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": ")",
                                    "type": "RBRACKET",
                                    "alt_name": "RightBracket",
                                    "category": "RBRACKET",
                                },
                            ],
                            [
                                {
                                    "name": "(",
                                    "type": "LBRACKET",
                                    "alt_name": "LeftBracket",
                                    "category": "LBRACKET",
                                },
                                {
                                    "name": "(",
                                    "type": "LBRACKET",
                                    "alt_name": "LeftBracket",
                                    "category": "LBRACKET",
                                },
                                {
                                    "name": "(",
                                    "type": "LBRACKET",
                                    "alt_name": "LeftBracket",
                                    "category": "LBRACKET",
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_x",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount X",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "+",
                                    "type": "OPERATOR",
                                    "alt_name": "PLUS",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 9, 10, 11],
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_y",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount Y",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": ")",
                                    "type": "RBRACKET",
                                    "alt_name": "RightBracket",
                                    "category": "RBRACKET",
                                },
                                {
                                    "name": "-",
                                    "type": "OPERATOR",
                                    "alt_name": "MINUS",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 9, 10, 11],
                                },
                                {
                                    "args": ["Integer", 100],
                                    "name": 100,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": ")",
                                    "type": "RBRACKET",
                                    "alt_name": "RightBracket",
                                    "category": "RBRACKET",
                                },
                                {
                                    "name": "*",
                                    "type": "OPERATOR",
                                    "alt_name": "MULTIPLY",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 6, 9, 10, 11],
                                },
                                {
                                    "args": ["Integer", 2],
                                    "name": 2,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": "+",
                                    "type": "OPERATOR",
                                    "alt_name": "PLUS",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 9, 10, 11],
                                },
                                {
                                    "args": ["Integer", 100],
                                    "name": 100,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": ")",
                                    "type": "RBRACKET",
                                    "alt_name": "RightBracket",
                                    "category": "RBRACKET",
                                },
                                {
                                    "name": "*",
                                    "type": "OPERATOR",
                                    "alt_name": "MULTIPLY",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 6, 9, 10, 11],
                                },
                                {
                                    "meta": {
                                        "category": "CONFIG",
                                        "model_name": "Payroll",
                                        "system_name": "variable_pay",
                                        "data_type_id": 1,
                                    },
                                    "name": "Variable Pay",
                                    "tags": [],
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": None,
                                    "datasheet_id": None,
                                },
                                {
                                    "name": "/",
                                    "type": "OPERATOR",
                                    "alt_name": "DIVIDE",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 9, 10, 11],
                                },
                                {
                                    "args": ["Integer", 100],
                                    "name": 100,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                            ],
                            {
                                "args": [
                                    [
                                        {
                                            "meta": {
                                                "category": None,
                                                "model_name": "Deals Sheet v1",
                                                "system_name": "co_1_deal_amount_x",
                                                "data_type_id": 1,
                                            },
                                            "name": "deal amount X",
                                            "tags": None,
                                            "type": "VARIABLE",
                                            "data_type": "Integer",
                                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                        },
                                        {
                                            "name": ">=",
                                            "type": "OPERATOR",
                                            "alt_name": "GREATERTHANEQUALTO",
                                            "category": "LOGICAL",
                                            "__typename": "",
                                            "multi_valued": False,
                                            "output_types": ["Boolean"],
                                            "needs_operand": True,
                                            "operand_types": [
                                                "Integer",
                                                "Date",
                                                "Percentage",
                                                "DayDuration",
                                                "MinuteDuration",
                                                "SecondDuration",
                                            ],
                                            "output_type_ids": [3],
                                            "operand_type_ids": [1, 2, 6, 9, 10, 11],
                                        },
                                        {
                                            "args": ["Integer", 0.3],
                                            "name": 0.3,
                                            "type": "VARIABLE",
                                            "data_type": "Integer",
                                            "function_name": "CONSTANT",
                                            "token_category": "DYNAMIC",
                                        },
                                    ],
                                    [
                                        {
                                            "args": ["Integer", 10],
                                            "name": 10,
                                            "type": "VARIABLE",
                                            "data_type": "Integer",
                                            "function_name": "CONSTANT",
                                            "token_category": "DYNAMIC",
                                        }
                                    ],
                                    [
                                        {
                                            "args": ["Integer", 25],
                                            "name": 25,
                                            "type": "VARIABLE",
                                            "data_type": "Integer",
                                            "function_name": "CONSTANT",
                                            "token_category": "DYNAMIC",
                                        }
                                    ],
                                ],
                                "name": "IF",
                                "type": "VARIABLE",
                                "level": 2,
                                "data_type": "Integer",
                                "function_name": "IF",
                                "else_is_nested": False,
                                "then_is_nested": False,
                                "token_category": "DYNAMIC",
                                "else_do_nothing": False,
                            },
                        ],
                        "name": "IF",
                        "type": "VARIABLE",
                        "level": 1,
                        "data_type": "Integer",
                        "function_name": "IF",
                        "else_is_nested": True,
                        "then_is_nested": False,
                        "token_category": "DYNAMIC",
                        "else_do_nothing": False,
                    },
                    [
                        {
                            "args": ["Integer", 5],
                            "name": 5,
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        }
                    ],
                ],
                "name": "IF",
                "type": "VARIABLE",
                "level": 0,
                "data_type": "Integer",
                "function_name": "IF",
                "else_is_nested": False,
                "then_is_nested": True,
                "token_category": "DYNAMIC",
                "else_do_nothing": False,
            },
            "type": "Conditional",
            "is_valid": True,
            "date_field": "co_1_deal_close_date",
            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
            "payee_field": "co_1_deal_owner_mail",
            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
            "is_line_item_level": True,
        },
        "d6975744-a33b-49af-bbfc-190c3cd7c8c9": {
            "type": "Tier",
            "part1": {
                "args": [
                    [
                        {
                            "name": "(",
                            "type": "LBRACKET",
                            "alt_name": "LeftBracket",
                            "category": "LBRACKET",
                        },
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Deals Sheet v1",
                                "system_name": "co_1_deal_status",
                                "data_type_id": 4,
                            },
                            "name": "deal status",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "String",
                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                        },
                        {
                            "name": "==",
                            "type": "OPERATOR",
                            "alt_name": "EQUALS",
                            "category": "LOGICAL",
                            "__typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Date",
                                "Boolean",
                                "String",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                                "Email",
                            ],
                            "output_type_ids": [3],
                            "operand_type_ids": [1, 2, 3, 4, 6, 9, 10, 11, 12],
                        },
                        {
                            "args": ["String", "Closed Won"],
                            "name": "Closed Won",
                            "type": "VARIABLE",
                            "data_type": "String",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                        {
                            "name": "AND",
                            "type": "OPERATOR",
                            "alt_name": "AND",
                            "category": "LOGICAL",
                            "__typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": ["Boolean"],
                            "output_type_ids": [3],
                            "operand_type_ids": [3],
                        },
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Deals Sheet v1",
                                "system_name": "co_1_deal_amount_x",
                                "data_type_id": 1,
                            },
                            "name": "deal amount X",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                        },
                        {
                            "name": ">=",
                            "type": "OPERATOR",
                            "alt_name": "GREATERTHANEQUALTO",
                            "category": "LOGICAL",
                            "__typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Date",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                            ],
                            "output_type_ids": [3],
                            "operand_type_ids": [1, 2, 6, 9, 10, 11],
                        },
                        {
                            "args": [
                                {
                                    "meta": {
                                        "model": "AConfig",
                                        "cateogry": "CALCUALTED",
                                        "system_name": "RTE factor",
                                    },
                                    "name": "RTE factor",
                                    "data_type": "Integer",
                                }
                            ],
                            "name": "Config(RTE factor)",
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "function_name": "Config",
                            "token_category": "DYNAMIC",
                        },
                        {
                            "name": ")",
                            "type": "RBRACKET",
                            "alt_name": "RightBracket",
                            "category": "RBRACKET",
                        },
                    ],
                    [
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Deals Sheet v1",
                                "system_name": "co_1_deal_amount_x",
                                "data_type_id": 1,
                            },
                            "name": "deal amount X",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                        }
                    ],
                    None,
                ],
                "name": "IF",
                "type": "VARIABLE",
                "level": 0,
                "data_type": "Integer",
                "function_name": "IF",
                "else_is_nested": False,
                "then_is_nested": False,
                "token_category": "DYNAMIC",
                "else_do_nothing": True,
            },
            "part2": [
                {
                    "is_valid": True,
                    "tier_key": "287f554f-de1b-4414-a393-08e4e050fd85",
                    "tier_name": "Tier 0",
                    "lower_bound": "-INF",
                    "upper_bound": 100,
                    "expression_stack": [
                        {
                            "args": [],
                            "name": "TieredValue()",
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "function_name": "TieredValue",
                            "token_category": "DYNAMIC",
                        },
                        {
                            "name": "*",
                            "type": "OPERATOR",
                            "alt_name": "MULTIPLY",
                            "category": "ARITHMETIC",
                            "__typename": "",
                            "multi_valued": False,
                            "output_types": ["Integer"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                            ],
                            "output_type_ids": [1],
                            "operand_type_ids": [1, 6, 9, 10, 11],
                        },
                        {
                            "args": ["Integer", 2],
                            "name": 2,
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                    ],
                    "is_override_slab": False,
                },
                {
                    "is_valid": True,
                    "tier_key": "45578c2b-b3a2-4d66-b86b-9f864b34bdf9",
                    "tier_name": "Tier 1",
                    "lower_bound": 100,
                    "upper_bound": "INF",
                    "is_last_tier": True,
                    "expression_stack": [
                        {
                            "args": [],
                            "name": "TieredValue()",
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "function_name": "TieredValue",
                            "token_category": "DYNAMIC",
                        },
                        {
                            "name": "*",
                            "type": "OPERATOR",
                            "alt_name": "MULTIPLY",
                            "category": "ARITHMETIC",
                            "__typename": "",
                            "multi_valued": False,
                            "output_types": ["Integer"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                            ],
                            "output_type_ids": [1],
                            "operand_type_ids": [1, 6, 9, 10, 11],
                        },
                        {
                            "args": ["Integer", 5],
                            "name": 5,
                            "type": "VARIABLE",
                            "data_type": "Integer",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                    ],
                    "is_override_slab": False,
                },
            ],
            "is_valid": True,
            "tier_type": "TieredSum",
            "date_field": "co_1_deal_close_date",
            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
            "is_override": False,
            "payee_field": "co_1_deal_owner_mail",
            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
            "expression_type": "Conditional",
            "is_line_item_level": True,
        },
        "5a18fb61-6835-4efc-8642-e93f8921cd59": {
            "ast": {
                "args": [
                    [
                        {
                            "name": "(",
                            "type": "LBRACKET",
                            "alt_name": "LeftBracket",
                            "category": "LBRACKET",
                        },
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Deals Sheet v1",
                                "system_name": "co_1_deal_status",
                                "data_type_id": 4,
                            },
                            "name": "deal status",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "String",
                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                        },
                        {
                            "name": "==",
                            "type": "OPERATOR",
                            "alt_name": "EQUALS",
                            "category": "LOGICAL",
                            "typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Date",
                                "Boolean",
                                "String",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                                "Email",
                            ],
                            "output_type_ids": [3],
                            "operand_type_ids": [1, 2, 3, 4, 6, 9, 10, 11, 12],
                        },
                        {
                            "args": ["String", "Closed Won"],
                            "name": "Closed Won",
                            "type": "VARIABLE",
                            "data_type": "String",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                        {
                            "name": "AND",
                            "type": "OPERATOR",
                            "alt_name": "AND",
                            "category": "LOGICAL",
                            "typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": ["Boolean"],
                            "output_type_ids": [3],
                            "operand_type_ids": [3],
                        },
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Deals Sheet v1",
                                "system_name": "co_1_deal_eligible",
                                "data_type_id": 3,
                            },
                            "name": "deal eligible",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "Boolean",
                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                        },
                        {
                            "name": "==",
                            "type": "OPERATOR",
                            "alt_name": "EQUALS",
                            "category": "LOGICAL",
                            "typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Date",
                                "Boolean",
                                "String",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                                "Email",
                            ],
                            "output_type_ids": [3],
                            "operand_type_ids": [1, 2, 3, 4, 6, 9, 10, 11, 12],
                        },
                        {
                            "args": ["Boolean", "True"],
                            "name": "True",
                            "type": "VARIABLE",
                            "data_type": "Boolean",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                        {
                            "name": ")",
                            "type": "RBRACKET",
                            "alt_name": "RightBracket",
                            "category": "RBRACKET",
                        },
                    ],
                    {
                        "args": [
                            [
                                {
                                    "name": "(",
                                    "type": "LBRACKET",
                                    "alt_name": "LeftBracket",
                                    "category": "LBRACKET",
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_type",
                                        "data_type_id": 4,
                                    },
                                    "name": "deal type",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "String",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "==",
                                    "type": "OPERATOR",
                                    "alt_name": "EQUALS",
                                    "category": "LOGICAL",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Date",
                                        "Boolean",
                                        "String",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                        "Email",
                                    ],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [1, 2, 3, 4, 6, 9, 10, 11, 12],
                                },
                                {
                                    "args": ["String", "New Business"],
                                    "name": "New Business",
                                    "type": "VARIABLE",
                                    "data_type": "String",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": "AND",
                                    "type": "OPERATOR",
                                    "alt_name": "AND",
                                    "category": "LOGICAL",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": ["Boolean"],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [3],
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_x",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount X",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": ">",
                                    "type": "OPERATOR",
                                    "alt_name": "GREATERTHAN",
                                    "category": "LOGICAL",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Date",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [1, 2, 6, 9, 10, 11],
                                },
                                {
                                    "args": ["Integer", 0.3],
                                    "name": 0.3,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": "AND",
                                    "type": "OPERATOR",
                                    "alt_name": "AND",
                                    "category": "LOGICAL",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": ["Boolean"],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [3],
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_y",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount Y",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "!=",
                                    "type": "OPERATOR",
                                    "alt_name": "NOTEQUALS",
                                    "category": "LOGICAL",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Date",
                                        "Boolean",
                                        "String",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                        "Email",
                                    ],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [1, 2, 3, 4, 6, 9, 10, 11, 12],
                                },
                                {
                                    "args": [
                                        {
                                            "meta": {
                                                "model": "AConfig",
                                                "cateogry": "CALCUALTED",
                                                "system_name": "FTE factor",
                                            },
                                            "name": "FTE factor",
                                            "data_type": "Integer",
                                        }
                                    ],
                                    "name": "Config(FTE factor)",
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "Config",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": ")",
                                    "type": "RBRACKET",
                                    "alt_name": "RightBracket",
                                    "category": "RBRACKET",
                                },
                            ],
                            [
                                {
                                    "name": "(",
                                    "type": "LBRACKET",
                                    "alt_name": "LeftBracket",
                                    "category": "LBRACKET",
                                },
                                {
                                    "name": "(",
                                    "type": "LBRACKET",
                                    "alt_name": "LeftBracket",
                                    "category": "LBRACKET",
                                },
                                {
                                    "name": "(",
                                    "type": "LBRACKET",
                                    "alt_name": "LeftBracket",
                                    "category": "LBRACKET",
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_x",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount X",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "+",
                                    "type": "OPERATOR",
                                    "alt_name": "PLUS",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 9, 10, 11],
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_y",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount Y",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": ")",
                                    "type": "RBRACKET",
                                    "alt_name": "RightBracket",
                                    "category": "RBRACKET",
                                },
                                {
                                    "name": "-",
                                    "type": "OPERATOR",
                                    "alt_name": "MINUS",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 9, 10, 11],
                                },
                                {
                                    "args": ["Integer", 100],
                                    "name": 100,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": ")",
                                    "type": "RBRACKET",
                                    "alt_name": "RightBracket",
                                    "category": "RBRACKET",
                                },
                                {
                                    "name": "*",
                                    "type": "OPERATOR",
                                    "alt_name": "MULTIPLY",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 6, 9, 10, 11],
                                },
                                {
                                    "args": ["Integer", 2],
                                    "name": 2,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": "+",
                                    "type": "OPERATOR",
                                    "alt_name": "PLUS",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 9, 10, 11],
                                },
                                {
                                    "args": ["Integer", 100],
                                    "name": 100,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                                {
                                    "name": ")",
                                    "type": "RBRACKET",
                                    "alt_name": "RightBracket",
                                    "category": "RBRACKET",
                                },
                                {
                                    "name": "*",
                                    "type": "OPERATOR",
                                    "alt_name": "MULTIPLY",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 6, 9, 10, 11],
                                },
                                {
                                    "meta": {
                                        "category": "CONFIG",
                                        "model_name": "Payroll",
                                        "system_name": "variable_pay",
                                        "data_type_id": 1,
                                    },
                                    "name": "Variable Pay",
                                    "tags": [],
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": None,
                                    "datasheet_id": None,
                                },
                                {
                                    "name": "/",
                                    "type": "OPERATOR",
                                    "alt_name": "DIVIDE",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 9, 10, 11],
                                },
                                {
                                    "args": ["Integer", 100],
                                    "name": 100,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                            ],
                            {
                                "args": [
                                    [
                                        {
                                            "meta": {
                                                "category": None,
                                                "model_name": "Deals Sheet v1",
                                                "system_name": "co_1_deal_amount_x",
                                                "data_type_id": 1,
                                            },
                                            "name": "deal amount X",
                                            "tags": None,
                                            "type": "VARIABLE",
                                            "data_type": "Integer",
                                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                        },
                                        {
                                            "name": ">=",
                                            "type": "OPERATOR",
                                            "alt_name": "GREATERTHANEQUALTO",
                                            "category": "LOGICAL",
                                            "__typename": "",
                                            "multi_valued": False,
                                            "output_types": ["Boolean"],
                                            "needs_operand": True,
                                            "operand_types": [
                                                "Integer",
                                                "Date",
                                                "Percentage",
                                                "DayDuration",
                                                "MinuteDuration",
                                                "SecondDuration",
                                            ],
                                            "output_type_ids": [3],
                                            "operand_type_ids": [1, 2, 6, 9, 10, 11],
                                        },
                                        {
                                            "args": ["Integer", 0.3],
                                            "name": 0.3,
                                            "type": "VARIABLE",
                                            "data_type": "Integer",
                                            "function_name": "CONSTANT",
                                            "token_category": "DYNAMIC",
                                        },
                                    ],
                                    [
                                        {
                                            "args": ["Integer", 10],
                                            "name": 10,
                                            "type": "VARIABLE",
                                            "data_type": "Integer",
                                            "function_name": "CONSTANT",
                                            "token_category": "DYNAMIC",
                                        }
                                    ],
                                    None,
                                ],
                                "name": "IF",
                                "type": "VARIABLE",
                                "level": 2,
                                "data_type": "Integer",
                                "function_name": "IF",
                                "else_is_nested": False,
                                "then_is_nested": False,
                                "token_category": "DYNAMIC",
                                "else_do_nothing": True,
                            },
                        ],
                        "name": "IF",
                        "type": "VARIABLE",
                        "level": 1,
                        "data_type": "Integer",
                        "function_name": "IF",
                        "else_is_nested": True,
                        "then_is_nested": False,
                        "token_category": "DYNAMIC",
                        "else_do_nothing": False,
                    },
                    None,
                ],
                "name": "IF",
                "type": "VARIABLE",
                "level": 0,
                "data_type": "Integer",
                "function_name": "IF",
                "else_is_nested": False,
                "then_is_nested": True,
                "token_category": "DYNAMIC",
                "else_do_nothing": True,
            },
            "type": "Conditional",
            "is_valid": True,
            "date_field": "co_1_deal_close_date",
            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
            "payee_field": "co_1_deal_owner_mail",
            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
            "is_line_item_level": True,
        },
        "cd941d76-2c20-41a0-a198-2c5845e80893": {
            "ast": {
                "args": [
                    [
                        {
                            "meta": {
                                "category": None,
                                "model_name": "Deals Sheet v1",
                                "system_name": "co_1_deal_status",
                                "data_type_id": 4,
                            },
                            "name": "deal status",
                            "tags": None,
                            "type": "VARIABLE",
                            "data_type": "String",
                            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                        },
                        {
                            "name": "==",
                            "type": "OPERATOR",
                            "alt_name": "EQUALS",
                            "category": "LOGICAL",
                            "typename": "",
                            "multi_valued": False,
                            "output_types": ["Boolean"],
                            "needs_operand": True,
                            "operand_types": [
                                "Integer",
                                "Date",
                                "Boolean",
                                "String",
                                "Percentage",
                                "DayDuration",
                                "MinuteDuration",
                                "SecondDuration",
                                "Email",
                            ],
                            "output_type_ids": [3],
                            "operand_type_ids": [1, 2, 3, 4, 6, 9, 10, 11, 12],
                        },
                        {
                            "args": ["String", "Closed Won"],
                            "name": "Closed Won",
                            "type": "VARIABLE",
                            "data_type": "String",
                            "function_name": "CONSTANT",
                            "token_category": "DYNAMIC",
                        },
                    ],
                    {
                        "args": [
                            [
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_x",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount X",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "<=",
                                    "type": "OPERATOR",
                                    "alt_name": "LESSERTHANEQUALTO",
                                    "category": "LOGICAL",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Boolean"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Date",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [3],
                                    "operand_type_ids": [1, 2, 6, 9, 10, 11],
                                },
                                {
                                    "name": "(",
                                    "type": "LBRACKET",
                                    "alt_name": "LeftBracket",
                                    "category": "LBRACKET",
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_y",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount Y",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "+",
                                    "type": "OPERATOR",
                                    "alt_name": "PLUS",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 9, 10, 11],
                                },
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_z",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount Z",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": ")",
                                    "type": "RBRACKET",
                                    "alt_name": "RightBracket",
                                    "category": "RBRACKET",
                                },
                            ],
                            [
                                {
                                    "meta": {
                                        "category": None,
                                        "model_name": "Deals Sheet v1",
                                        "system_name": "co_1_deal_amount_y",
                                        "data_type_id": 1,
                                    },
                                    "name": "deal amount Y",
                                    "tags": None,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
                                    "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
                                },
                                {
                                    "name": "*",
                                    "type": "OPERATOR",
                                    "alt_name": "MULTIPLY",
                                    "category": "ARITHMETIC",
                                    "typename": "",
                                    "multi_valued": False,
                                    "output_types": ["Integer"],
                                    "needs_operand": True,
                                    "operand_types": [
                                        "Integer",
                                        "Percentage",
                                        "DayDuration",
                                        "MinuteDuration",
                                        "SecondDuration",
                                    ],
                                    "output_type_ids": [1],
                                    "operand_type_ids": [1, 6, 9, 10, 11],
                                },
                                {
                                    "args": ["Percentage", 1],
                                    "name": "1%",
                                    "type": "VARIABLE",
                                    "data_type": "Percentage",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                },
                            ],
                            [
                                {
                                    "args": ["Integer", 10],
                                    "name": 10,
                                    "type": "VARIABLE",
                                    "data_type": "Integer",
                                    "function_name": "CONSTANT",
                                    "token_category": "DYNAMIC",
                                }
                            ],
                        ],
                        "name": "IF",
                        "type": "VARIABLE",
                        "level": 1,
                        "data_type": "Integer",
                        "function_name": "IF",
                        "else_is_nested": False,
                        "then_is_nested": False,
                        "token_category": "DYNAMIC",
                        "else_do_nothing": False,
                    },
                    None,
                ],
                "name": "IF",
                "type": "VARIABLE",
                "level": 0,
                "data_type": "Integer",
                "function_name": "IF",
                "else_is_nested": False,
                "then_is_nested": True,
                "token_category": "DYNAMIC",
                "else_do_nothing": True,
            },
            "type": "Conditional",
            "is_valid": True,
            "date_field": "co_1_deal_close_date",
            "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
            "payee_field": "co_1_deal_owner_mail",
            "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
            "is_line_item_level": True,
        },
    }

    return criteria_ast[criteria_id]


def get_criteria_conditions_mock():
    common_criteria_conditons = {
        "databook_id": "6bb67cc0-ea57-4bf1-8209-3c744595e647",
        "datasheet_id": "91b87a7d-6547-4b91-98d0-be7db87c4f86",
        "payee_field": "co_1_deal_owner_mail",
        "date_field": "co_1_deal_close_date",
    }
    return common_criteria_conditons


def get_single_row(
    payee_email,
    row_key,
    amount_x,
    amount_y,
    amount_z,
    deal_type="New Business",
    deal_eligible=True,
    deal_status="Closed Won",
):
    return (
        row_key,
        f"deal_{row_key}",
        payee_email,
        deal_status,
        first_day_of_month(timezone.now()),
        amount_x,
        amount_y,
        amount_z,
        deal_type,
        deal_eligible,
        row_key,
    )


def get_payee_merged_data(payee_email, deals, _type=1):
    dict_keys = [
        "co_1_deal_id",
        "co_1_deal_name",
        "co_1_deal_owner_mail",
        "co_1_deal_status",
        "co_1_deal_close_date",
        "co_1_deal_amount_x",
        "co_1_deal_amount_y",
        "co_1_deal_amount_z",
        "co_1_deal_type",
        "co_1_deal_eligible",
        "row_key",
    ]

    data_tuple_list = []
    if _type == 1:
        for deal in deals:
            amount_x, amount_y, amount_z, row_key = deal
            data_tuple_list.append(
                get_single_row(payee_email, row_key, amount_x, amount_y, amount_z)
            )
    elif _type == 2:
        for deal in deals:
            (
                amount_x,
                amount_y,
                amount_z,
                deal_type,
                deal_eligible,
                deal_status,
                row_key,
            ) = deal
            data_tuple_list.append(
                get_single_row(
                    payee_email,
                    row_key,
                    amount_x,
                    amount_y,
                    amount_z,
                    deal_type,
                    deal_eligible,
                    deal_status,
                )
            )

    data_dict_list = [dict(zip(dict_keys, tpl)) for tpl in data_tuple_list]
    return {payee_email: data_dict_list}


def get_payee_context(payee_email):
    return {
        payee_email: {
            "payee": [
                {
                    "email": payee_email,
                    "actual_variable_pay": Decimal("152000.00"),
                    "payout_frequency": "Monthly",
                    "data_payout_frequency": "Monthly",
                    "variable_pay": Decimal("12666.66666666666666666666667"),
                }
            ]
        }
    }


def get_team_params(criteria_data):
    team_params = {}
    team_params["infix_exp"] = criteria_data["ast"] if "ast" in criteria_data else None
    team_params["tier_type"] = (
        criteria_data["tier_type"] if "tier_type" in criteria_data else None
    )
    team_params["part1_exp"] = (
        criteria_data["part1"] if "part1" in criteria_data else None
    )
    team_params["part2_exp"] = (
        criteria_data["part2"] if "part2" in criteria_data else None
    )
    team_params["part1_type"] = (
        criteria_data["part_1_type"] if "part_1_type" in criteria_data else None
    )
    team_params["criteria_type"] = criteria_data["type"]
    team_params["team_name"] = criteria_data["team"]
    team_params["category"] = (
        criteria_data["quota_name"] if "quota_name" in criteria_data else None
    )
    return team_params
