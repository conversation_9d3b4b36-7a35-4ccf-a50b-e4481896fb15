from datetime import datetime
from decimal import Decimal
from unittest import mock

import pandas as pd
import pytest
from pytz import UTC

from commission_engine.tests.data_utils import JsonDatasheetData
from commission_engine.tests.services.commission_calculation_service.evaluate_utils import (
    get_evaluate_result,
)
from commission_engine.utils import last_day_of_month, make_aware_wrapper

CLIENT_ID = 6897
DATASHEET_NAME = "sales"
# plans
MONTHLY_MAIN_PLAN = "f9c8c0be-4d37-4881-b877-03c5bc11bb4f"
MONTHLY_MANAGERS_PLAN = "95dd9e4d-7bef-4165-8a8e-39c8133f4f1e"
QUARTERLY_MAIN_PLAN = "68019630-abb9-446c-8271-9accf8144524"

# criterias
# monthly main plan
SIMPLE_ROW_LEVEL_CRITERIA = "1a6bb2b7-6d49-430f-8b3f-76e5b5503574"
SIMPLE_SUMATION_CRITERIA = "a6ea135e-d000-4c5e-816b-426251ceded9"
CONDITIONAL_ROW_LEVEL_CRITERIA = "ed56c9a2-c51c-4d95-832b-a034a9a33b15"
CONDITONAL_SUMATION_CRITERIA = "b15e7091-2101-41c7-9800-3808fd0c839f"
TIER_ROW_LEVEL_CRITERIA = "2dd871c8-3a89-449c-8a6c-761180433ecc"
TIER_SUMATION_CRITERIA = "162d7aa5-91c2-4b94-bd81-16053dd854c8"
TIER_OVERRIDE_CRITERIA = "3c20afc8-9b1e-4321-9fac-f8609b6e014c"
TIER_ROLLING_COUNT_CRITERIA = "dbef58bd-af23-469a-94fc-d1a91a531724"
PRIMARY_QUOTA_CRITERIA = "a3a74398-988f-4c30-a0e8-d6014fecea59"
# monthly managers plan
TEAM_QUOTA_CRITERA = "9dbb597f-6c21-4ba2-bb72-bda726c8308a"
PAYEE_REPORTS_CRITERA = "1c172c40-b8ea-4074-9d78-167730ad2b08"
# Quarterly main plan
QUARTERLY_PRIMARY_QUOTA_CRITERIA = "a3a74398-988f-4c30-a0e8-d6014fecea59"

json_datasheet_data = JsonDatasheetData(
    CLIENT_ID, DATASHEET_NAME, ["co_1_sales_date", "co_1_close_date"]
)


@pytest.mark.django_db
@pytest.mark.commission_engine
class TestCriteriaEvaluation:
    """
    Test class for criteria evaluation
    """

    @pytest.mark.parametrize(
        "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id, expected_result",
        [
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                ["123a", "123b"],
                MONTHLY_MAIN_PLAN,
                SIMPLE_ROW_LEVEL_CRITERIA,
                {
                    "context": {
                        "123a": {
                            "co_1_deal_id": "123a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-02"),
                            "co_1_close_date": pd.Timestamp("2023-02-09"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 1000.123,
                            "co_1_percentage": 12.119,
                            "row_key": "123a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123a",
                        },
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-13"),
                            "co_1_close_date": pd.Timestamp("2023-02-23"),
                            "co_1_flag": False,
                            "co_1_deal_amount": 1500.45,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        },
                    },
                    "result": [
                        {
                            "id": "123a",
                            "commission": Decimal("613.703483825"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                        },
                        {
                            "id": "123b",
                            "commission": Decimal("969.7446667500001"),
                            "commission_date": pd.Timestamp("2023-02-13"),
                        },
                    ],
                    "summary": {},
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                None,
                "<EMAIL>",
                ["123a", "123b"],
                MONTHLY_MAIN_PLAN,
                SIMPLE_ROW_LEVEL_CRITERIA,
                {
                    "context": {
                        "123a": {
                            "co_1_deal_id": "123a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-02"),
                            "co_1_close_date": pd.Timestamp("2023-03-21"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 2500.0,
                            "co_1_percentage": 12.119,
                            "row_key": "123a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123a",
                        },
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-13"),
                            "co_1_close_date": pd.Timestamp("2023-02-23"),
                            "co_1_flag": False,
                            "co_1_deal_amount": 2011.23,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        },
                    },
                    "result": [
                        {
                            "id": "123a",
                            "commission": Decimal("1455.018031925"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                        },
                        {
                            "id": "123b",
                            "commission": Decimal("1401.6437257500002"),
                            "commission_date": pd.Timestamp("2023-02-13"),
                        },
                    ],
                    "summary": {},
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-03-01",
                "2023-03-31",
                None,
                "<EMAIL>",
                ["213a"],
                MONTHLY_MAIN_PLAN,
                SIMPLE_ROW_LEVEL_CRITERIA,
                {
                    "context": {
                        "213a": {
                            "co_1_deal_id": "213a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-03-02"),
                            "co_1_close_date": pd.Timestamp("2023-03-10"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 1900.333,
                            "co_1_percentage": 66.667,
                            "row_key": "213a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("11494.252874"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("11494.252874"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("957.85"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "213a",
                        }
                    },
                    "result": [
                        {
                            "id": "213a",
                            "commission": Decimal("1900.336250825"),
                            "commission_date": pd.Timestamp("2023-03-02"),
                        }
                    ],
                    "summary": {},
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("11494.252874"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("11494.252874"),
                        "data_variable_pay_by_month": Decimal("957.85"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("957.854406"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                [],
                MONTHLY_MAIN_PLAN,
                SIMPLE_ROW_LEVEL_CRITERIA,
                {
                    "context": {},
                    "result": [
                        {
                            "commission": 0,
                            "commission_date": make_aware_wrapper(
                                last_day_of_month(
                                    datetime.strptime("2023-02-28", "%Y-%m-%d")
                                )
                            ),
                        }
                    ],
                    "summary": {},
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {},
                    "do_nothing_records": [],
                },
            ),
        ],
    )
    @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    @mock.patch(
        "everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date"
    )
    def test_simple_row_level_criteria_evaluation(
        self,
        mock_fetch,
        mock_fetch_datasheet_data_as_of_date,
        period_start_date,
        period_end_date,
        secondary_kd,
        payee_email,
        row_keys,
        plan_id,
        criteria_id,
        expected_result,
    ):
        """
        Test simple row level criteria evaluation
        """
        data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
        mock_fetch.return_value = data_frame
        mock_fetch_datasheet_data_as_of_date.return_value = data_frame
        result = get_evaluate_result(
            CLIENT_ID,
            period_start_date,
            period_end_date,
            payee_email,
            plan_id,
            criteria_id,
            seconday_kd=secondary_kd,
        )
        if result != expected_result:
            print(
                f"result is != expected...result - {result}\n expected result-{expected_result}\n"
            )
        assert result == expected_result

    @pytest.mark.parametrize(
        "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id, expected_result",
        [
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                ["123a", "123b"],
                MONTHLY_MAIN_PLAN,
                SIMPLE_SUMATION_CRITERIA,
                {
                    "context": {
                        "123a": {
                            "co_1_deal_id": "123a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-02-02", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-02-09", "%Y-%m-%d"
                            ),
                            "co_1_flag": True,
                            "co_1_deal_amount": 1000.123,
                            "co_1_percentage": 12.119,
                            "row_key": "123a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123a",
                        },
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-02-13", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-02-23", "%Y-%m-%d"
                            ),
                            "co_1_flag": False,
                            "co_1_deal_amount": 1500.45,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        },
                    },
                    "result": [
                        {
                            "id": None,
                            "commission": Decimal("213134.08548979287"),
                            "commission_date": make_aware_wrapper(
                                last_day_of_month(
                                    datetime.strptime("2023-02-28", "%Y-%m-%d")
                                )
                            ),
                        }
                    ],
                    "summary": {},
                    "is_line_item_level": False,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                None,
                "<EMAIL>",
                ["123a", "123b"],
                MONTHLY_MAIN_PLAN,
                SIMPLE_SUMATION_CRITERIA,
                {
                    "context": {
                        "123a": {
                            "co_1_deal_id": "123a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-02-02", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-03-21", "%Y-%m-%d"
                            ),
                            "co_1_flag": True,
                            "co_1_deal_amount": 2500.0,
                            "co_1_percentage": 12.119,
                            "row_key": "123a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123a",
                        },
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-02-13", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-02-23", "%Y-%m-%d"
                            ),
                            "co_1_flag": False,
                            "co_1_deal_amount": 2011.23,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        },
                    },
                    "result": [
                        {
                            "id": None,
                            "commission": Decimal("384096.763172728"),
                            "commission_date": make_aware_wrapper(
                                last_day_of_month(
                                    datetime.strptime("2023-02-28", "%Y-%m-%d")
                                )
                            ),
                        }
                    ],
                    "summary": {},
                    "is_line_item_level": False,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                [],
                MONTHLY_MAIN_PLAN,
                SIMPLE_SUMATION_CRITERIA,
                {
                    "context": {},
                    "result": [
                        {
                            "commission": 0,
                            "commission_date": make_aware_wrapper(
                                last_day_of_month(
                                    datetime.strptime("2023-02-28", "%Y-%m-%d")
                                )
                            ),
                        }
                    ],
                    "summary": {},
                    "is_line_item_level": False,
                    "payee_detail_sf_reports": {},
                    "do_nothing_records": [],
                },
            ),
        ],
    )
    @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    @mock.patch(
        "everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date"
    )
    def test_simple_sumation_criteria_evaluation(
        self,
        mock_fetch,
        mock_fetch_datasheet_data_as_of_date,
        period_start_date,
        period_end_date,
        secondary_kd,
        payee_email,
        row_keys,
        plan_id,
        criteria_id,
        expected_result,
    ):
        """
        Test simple sumation criteria evaluation
        """
        data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
        mock_fetch.return_value = data_frame
        mock_fetch_datasheet_data_as_of_date.return_value = data_frame
        result = get_evaluate_result(
            CLIENT_ID,
            period_start_date,
            period_end_date,
            payee_email,
            plan_id,
            criteria_id,
            secondary_kd,
        )
        if result != expected_result:
            print(
                f"result is != expected...result - {result}\n expected result-{expected_result}\n"
            )
        assert result == expected_result

    @pytest.mark.parametrize(
        "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id, expected_result",
        [
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                ["123a", "123b"],
                MONTHLY_MAIN_PLAN,
                CONDITIONAL_ROW_LEVEL_CRITERIA,
                {
                    "context": {
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-02-13", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-02-23", "%Y-%m-%d"
                            ),
                            "co_1_flag": False,
                            "co_1_deal_amount": 1500.45,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        }
                    },
                    "result": [
                        {
                            "id": "123b",
                            "commission": Decimal("1140.3420"),
                            "commission_date": datetime.strptime(
                                "2023-02-13", "%Y-%m-%d"
                            ),
                        }
                    ],
                    "summary": {},
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                    },
                    "do_nothing_records": ["123a"],
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                None,
                "<EMAIL>",
                [],
                MONTHLY_MAIN_PLAN,
                CONDITIONAL_ROW_LEVEL_CRITERIA,
                {
                    "context": {},
                    "result": [
                        {
                            "commission": 0,
                            "commission_date": make_aware_wrapper(
                                last_day_of_month(
                                    datetime.strptime("2023-02-28", "%Y-%m-%d")
                                )
                            ),
                        }
                    ],
                    "summary": {},
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {},
                    "do_nothing_records": [],
                },
            ),
        ],
    )
    @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    @mock.patch(
        "everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date"
    )
    def test_conditional_row_level_criteria_evaluation(
        self,
        mock_fetch,
        mock_fetch_datasheet_data_as_of_date,
        period_start_date,
        period_end_date,
        secondary_kd,
        payee_email,
        row_keys,
        plan_id,
        criteria_id,
        expected_result,
    ):
        """
        Test conditional row level criteria evaluation
        """
        data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
        mock_fetch.return_value = data_frame
        mock_fetch_datasheet_data_as_of_date.return_value = data_frame
        result = get_evaluate_result(
            CLIENT_ID,
            period_start_date,
            period_end_date,
            payee_email,
            plan_id,
            criteria_id,
            secondary_kd,
        )
        assert result == expected_result

    # TO DO: Fix the test cases (criteria has line item enabled. it should be disabled for this test)
    # @pytest.mark.parametrize(
    #     "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id, expected_result",
    #     [
    #         (
    #             "2023-02-01",
    #             "2023-02-28",
    #             "2023-06-03",
    #             "<EMAIL>",
    #             ["123a", "123b"],
    #             MONTHLY_MAIN_PLAN,
    #             CONDITONAL_SUMATION_CRITERIA,
    #             {
    #                 "context": {
    #                     "123a": {
    #                         "co_1_deal_id": "123a",
    #                         "co_1_email": "<EMAIL>",
    #                         "co_1_sales_date": datetime.strptime(
    #                             "2023-02-02", "%Y-%m-%d"
    #                         ),
    #                         "co_1_close_date": datetime.strptime(
    #                             "2023-02-09", "%Y-%m-%d"
    #                         ),
    #                         "co_1_flag": True,
    #                         "co_1_deal_amount": 1000.123,
    #                         "co_1_percentage": 12.119,
    #                         "row_key": "123a",
    #                         "email": "<EMAIL>",
    #                         "actual_variable_pay": Decimal("10204.081633"),
    #                         "payout_frequency": "Monthly",
    #                         "key": "123a",
    #                     },
    #                     "123b": {
    #                         "co_1_deal_id": "123b",
    #                         "co_1_email": "<EMAIL>",
    #                         "co_1_sales_date": datetime.strptime(
    #                             "2023-02-13", "%Y-%m-%d"
    #                         ),
    #                         "co_1_close_date": datetime.strptime(
    #                             "2023-02-23", "%Y-%m-%d"
    #                         ),
    #                         "co_1_flag": False,
    #                         "co_1_deal_amount": 1500.45,
    #                         "co_1_percentage": 23.41,
    #                         "row_key": "123b",
    #                         "email": "<EMAIL>",
    #                         "actual_variable_pay": Decimal("10204.081633"),
    #                         "payout_frequency": "Monthly",
    #                         "key": "123b",
    #                     },
    #                 },
    #                 "result": [
    #                     {
    #                         "id": "123a",
    #                         "commission": 0,
    #                         "commission_date": datetime.strptime(
    #                             "2023-02-02", "%Y-%m-%d"
    #                         ),
    #                         "show_do_nothing": True,
    #                     },
    #                     {
    #                         "id": "123b",
    #                         "commission": 0,
    #                         "commission_date": datetime.strptime(
    #                             "2023-02-13", "%Y-%m-%d"
    #                         ),
    #                         "show_do_nothing": True,
    #                     },
    #                 ],
    #                 "summary": {},
    #                 "is_line_item_level": True,
    #                 "do_nothing_records": ["123a", "123b"],
    #             },
    #         ),
    #         (
    #             "2023-03-01",
    #             "2023-03-31",
    #             "2023-06-03",
    #             "<EMAIL>",
    #             ["23r5"],
    #             MONTHLY_MAIN_PLAN,
    #             CONDITONAL_SUMATION_CRITERIA,
    #             {
    #                 "context": {
    #                     "23r5": {
    #                         "co_1_deal_id": "23r5",
    #                         "co_1_email": "<EMAIL>",
    #                         "co_1_sales_date": datetime.strptime(
    #                             "2023-03-28", "%Y-%m-%d"
    #                         ),
    #                         "co_1_close_date": datetime.strptime(
    #                             "2023-03-29", "%Y-%m-%d"
    #                         ),
    #                         "co_1_flag": False,
    #                         "co_1_deal_amount": 4120.009,
    #                         "co_1_percentage": 12.46,
    #                         "row_key": "23r5",
    #                         "email": "<EMAIL>",
    #                         "actual_variable_pay": Decimal("22988.505747"),
    #                         "payout_frequency": "Monthly",
    #                         "key": "23r5",
    #                     }
    #                 },
    #                 "result": [
    #                     {
    #                         "id": "23r5",
    #                         "commission": Decimal("4120.009"),
    #                         "commission_date": datetime.strptime(
    #                             "2023-03-28", "%Y-%m-%d"
    #                         ),
    #                     }
    #                 ],
    #                 "summary": {},
    #                 "is_line_item_level": True,
    #                 "do_nothing_records": [],
    #             },
    #         ),
    #     ],
    # )
    # @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    # @mock.patch("everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date")
    # def test_conditional_sumation_criteria_evaluation(
    #     self,
    #     mock_fetch,
    #     mock_fetch_datasheet_data_as_of_date,
    #     period_start_date,
    #     period_end_date,
    #     secondary_kd,
    #     payee_email,
    #     row_keys,
    #     plan_id,
    #     criteria_id,
    #     expected_result,
    # ):
    #     """
    #     Test conditional sumation criteria evaluation
    #     """
    #     data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
    #     mock_fetch.return_value = data_frame
    #     result = get_evaluate_result(
    #         CLIENT_ID,
    #         period_start_date,
    #         period_end_date,
    #         payee_email,
    #         plan_id,
    #         criteria_id,
    #         secondary_kd,
    #     )

    #     expected_do_nothing_records = expected_result.pop("do_nothing_records")
    #     result_do_nothing_records = result.pop("do_nothing_records")

    #     assert set(expected_do_nothing_records) == set(result_do_nothing_records)
    #     assert result == expected_result

    @pytest.mark.parametrize(
        "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id, expected_result",
        [
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                ["123a", "123b"],
                MONTHLY_MAIN_PLAN,
                TIER_ROW_LEVEL_CRITERIA,
                {
                    "context": {
                        "123a": {
                            "co_1_deal_id": "123a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-02"),
                            "co_1_close_date": pd.Timestamp("2023-02-09"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 1000.123,
                            "co_1_percentage": 12.119,
                            "row_key": "123a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123a",
                        },
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-13"),
                            "co_1_close_date": pd.Timestamp("2023-02-23"),
                            "co_1_flag": False,
                            "co_1_deal_amount": 1500.45,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        },
                    },
                    "summary": {},
                    "result": [
                        {
                            "id": "123a",
                            "commission": Decimal("510.0"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                            "original_tier_id": 0,
                            "original_tier_name": "Tier 0",
                            "tier_value": Decimal("500"),
                        },
                        {
                            "id": "123a",
                            "commission": Decimal("1100.0"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "tier_value": Decimal("1000"),
                        },
                        {
                            "id": "123a",
                            "commission": Decimal("1125.6195"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                            "tier_id": 2,
                            "tier_name": "Tier 2",
                            "original_tier_id": 2,
                            "original_tier_name": "Tier 2",
                            "tier_value": Decimal("750.413"),
                        },
                        {
                            "id": "123b",
                            "commission": Decimal("4126.110"),
                            "commission_date": pd.Timestamp("2023-02-13"),
                            "tier_id": 2,
                            "tier_name": "Tier 2",
                            "original_tier_id": 2,
                            "original_tier_name": "Tier 2",
                            "tier_value": Decimal("2750.74"),
                        },
                    ],
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                None,
                "<EMAIL>",
                ["123a", "123b"],
                MONTHLY_MAIN_PLAN,
                TIER_ROW_LEVEL_CRITERIA,
                {
                    "context": {
                        "123a": {
                            "co_1_deal_id": "123a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-02"),
                            "co_1_close_date": pd.Timestamp("2023-03-21"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 2500.0,
                            "co_1_percentage": 12.119,
                            "row_key": "123a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123a",
                        },
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-13"),
                            "co_1_close_date": pd.Timestamp("2023-02-23"),
                            "co_1_flag": False,
                            "co_1_deal_amount": 2011.23,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        },
                    },
                    "summary": {},
                    "result": [
                        {
                            "id": "123a",
                            "commission": Decimal("510.0"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                            "original_tier_id": 0,
                            "original_tier_name": "Tier 0",
                            "tier_value": Decimal("500"),
                        },
                        {
                            "id": "123a",
                            "commission": Decimal("1100.0"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "tier_value": Decimal("1000"),
                        },
                        {
                            "id": "123a",
                            "commission": Decimal("4883.415000000002"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                            "tier_id": 2,
                            "tier_name": "Tier 2",
                            "original_tier_id": 2,
                            "original_tier_name": "Tier 2",
                            "tier_value": Decimal("3255.610000000001"),
                        },
                        {
                            "id": "123b",
                            "commission": Decimal("6400.26"),
                            "commission_date": pd.Timestamp("2023-02-13"),
                            "tier_id": 2,
                            "tier_name": "Tier 2",
                            "original_tier_id": 2,
                            "original_tier_name": "Tier 2",
                            "tier_value": Decimal("4266.84"),
                        },
                    ],
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-03-01",
                "2023-03-31",
                None,
                "<EMAIL>",
                ["213a"],
                MONTHLY_MAIN_PLAN,
                TIER_ROW_LEVEL_CRITERIA,
                {
                    "context": {
                        "213a": {
                            "co_1_deal_id": "213a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-03-02"),
                            "co_1_close_date": pd.Timestamp("2023-03-10"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 1900.333,
                            "co_1_percentage": 66.667,
                            "row_key": "213a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("11494.252874"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("11494.252874"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("957.85"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "213a",
                        }
                    },
                    "summary": {},
                    "result": [
                        {
                            "id": "213a",
                            "commission": Decimal("510.0"),
                            "commission_date": pd.Timestamp("2023-03-02"),
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                            "original_tier_id": 0,
                            "original_tier_name": "Tier 0",
                            "tier_value": Decimal("500"),
                        },
                        {
                            "id": "213a",
                            "commission": Decimal("1100.0"),
                            "commission_date": pd.Timestamp("2023-03-02"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "tier_value": Decimal("1000"),
                        },
                        {
                            "id": "213a",
                            "commission": Decimal("3450.9945"),
                            "commission_date": pd.Timestamp("2023-03-02"),
                            "tier_id": 2,
                            "tier_name": "Tier 2",
                            "original_tier_id": 2,
                            "original_tier_name": "Tier 2",
                            "tier_value": Decimal("2300.663"),
                        },
                    ],
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("11494.252874"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("11494.252874"),
                        "data_variable_pay_by_month": Decimal("957.85"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("957.854406"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                [],
                MONTHLY_MAIN_PLAN,
                TIER_ROW_LEVEL_CRITERIA,
                {
                    "context": {},
                    "result": [
                        {
                            "commission": 0,
                            "commission_date": make_aware_wrapper(
                                last_day_of_month(pd.Timestamp("2023-02-28"))
                            ),
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                        }
                    ],
                    "summary": {},
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {},
                    "do_nothing_records": [],
                },
            ),
        ],
    )
    @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    @mock.patch(
        "everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date"
    )
    def test_tier_row_level_criteria_evaluation(
        self,
        mock_fetch,
        mock_fetch_datasheet_data_as_of_date,
        period_start_date,
        period_end_date,
        secondary_kd,
        payee_email,
        row_keys,
        plan_id,
        criteria_id,
        expected_result,
    ):
        """
        Test tier row level criteria evaluation
        """
        data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
        mock_fetch.return_value = data_frame
        mock_fetch_datasheet_data_as_of_date.return_value = data_frame
        result = get_evaluate_result(
            CLIENT_ID,
            period_start_date,
            period_end_date,
            payee_email,
            plan_id,
            criteria_id,
            secondary_kd,
        )
        assert result == expected_result

    @pytest.mark.parametrize(
        "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id, expected_result",
        [
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                ["123a", "123b"],
                MONTHLY_MAIN_PLAN,
                TIER_SUMATION_CRITERIA,
                {
                    "context": {
                        "123a": {
                            "co_1_deal_id": "123a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-02-02", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-02-09", "%Y-%m-%d"
                            ),
                            "co_1_flag": True,
                            "co_1_deal_amount": 1000.123,
                            "co_1_percentage": 12.119,
                            "row_key": "123a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123a",
                        },
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-02-13", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-02-23", "%Y-%m-%d"
                            ),
                            "co_1_flag": False,
                            "co_1_deal_amount": 1500.45,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        },
                    },
                    "summary": {},
                    "result": [
                        {
                            "id": None,
                            "tier_value": Decimal("2250.54225"),
                            "tier_id": 2,
                            "tier_name": "Tier 2",
                            "commission": Decimal("2925.704925"),
                            "commission_date": make_aware_wrapper(
                                datetime(2023, 2, 28, 23, 59, 59, 999999)
                            ),
                        }
                    ],
                    "is_line_item_level": False,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                None,
                "<EMAIL>",
                ["123b"],
                MONTHLY_MAIN_PLAN,
                TIER_SUMATION_CRITERIA,
                {
                    "context": {
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-02-13", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-02-23", "%Y-%m-%d"
                            ),
                            "co_1_flag": False,
                            "co_1_deal_amount": 2011.23,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        }
                    },
                    "summary": {},
                    "result": [
                        {
                            "id": None,
                            "tier_value": Decimal("2011.23"),
                            "tier_id": 2,
                            "tier_name": "Tier 2",
                            "commission": Decimal("2614.599"),
                            "commission_date": make_aware_wrapper(
                                datetime(2023, 2, 28, 23, 59, 59, 999999)
                            ),
                        }
                    ],
                    "is_line_item_level": False,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                [],
                MONTHLY_MAIN_PLAN,
                TIER_SUMATION_CRITERIA,
                {
                    "context": {},
                    "result": [
                        {
                            "commission": 0,
                            "commission_date": make_aware_wrapper(
                                last_day_of_month(
                                    datetime.strptime("2023-02-28", "%Y-%m-%d")
                                )
                            ),
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                        }
                    ],
                    "summary": {},
                    "is_line_item_level": False,
                    "payee_detail_sf_reports": {},
                    "do_nothing_records": [],
                },
            ),
        ],
    )
    @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    @mock.patch(
        "everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date"
    )
    def test_tier_sumation_criteria_evaluation(
        self,
        mock_fetch,
        mock_fetch_datasheet_data_as_of_date,
        period_start_date,
        period_end_date,
        secondary_kd,
        payee_email,
        row_keys,
        plan_id,
        criteria_id,
        expected_result,
    ):
        """
        Test tier sumation criteria evaluation
        """
        data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
        mock_fetch.return_value = data_frame
        mock_fetch_datasheet_data_as_of_date.return_value = data_frame
        result = get_evaluate_result(
            CLIENT_ID,
            period_start_date,
            period_end_date,
            payee_email,
            plan_id,
            criteria_id,
            secondary_kd,
        )
        assert result == expected_result

    @pytest.mark.parametrize(
        "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id, expected_result",
        [
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                ["123a", "123b"],
                MONTHLY_MAIN_PLAN,
                TIER_OVERRIDE_CRITERIA,
                {
                    "context": {
                        "123a": {
                            "co_1_deal_id": "123a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-02"),
                            "co_1_close_date": pd.Timestamp("2023-02-09"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 1000.123,
                            "co_1_percentage": 12.119,
                            "row_key": "123a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123a",
                        },
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-13"),
                            "co_1_close_date": pd.Timestamp("2023-02-23"),
                            "co_1_flag": False,
                            "co_1_deal_amount": 1500.45,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        },
                    },
                    "summary": {},
                    "result": [
                        {
                            "id": "123a",
                            "commission": Decimal("550.0"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 0,
                            "original_tier_name": "Tier 0",
                            "tier_value": Decimal("500"),
                        },
                        {
                            "id": "123a",
                            "commission": Decimal("1100.0"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "tier_value": Decimal("1000"),
                        },
                        {
                            "id": "123a",
                            "commission": Decimal("1125.6195"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                            "tier_id": 2,
                            "tier_name": "Tier 2",
                            "original_tier_id": 2,
                            "original_tier_name": "Tier 2",
                            "tier_value": Decimal("750.413"),
                        },
                        {
                            "id": "123b",
                            "commission": Decimal("4126.110"),
                            "commission_date": pd.Timestamp("2023-02-13"),
                            "tier_id": 2,
                            "tier_name": "Tier 2",
                            "original_tier_id": 2,
                            "original_tier_name": "Tier 2",
                            "tier_value": Decimal("2750.74"),
                        },
                    ],
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                None,
                "<EMAIL>",
                ["123a", "123b"],
                MONTHLY_MAIN_PLAN,
                TIER_OVERRIDE_CRITERIA,
                {
                    "context": {
                        "123a": {
                            "co_1_deal_id": "123a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-02"),
                            "co_1_close_date": pd.Timestamp("2023-03-21"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 2500.0,
                            "co_1_percentage": 12.119,
                            "row_key": "123a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123a",
                        },
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-13"),
                            "co_1_close_date": pd.Timestamp("2023-02-23"),
                            "co_1_flag": False,
                            "co_1_deal_amount": 2011.23,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        },
                    },
                    "summary": {},
                    "result": [
                        {
                            "id": "123a",
                            "commission": Decimal("550.0"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 0,
                            "original_tier_name": "Tier 0",
                            "tier_value": Decimal("500"),
                        },
                        {
                            "id": "123a",
                            "commission": Decimal("1100.0"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "tier_value": Decimal("1000"),
                        },
                        {
                            "id": "123a",
                            "commission": Decimal("4883.415000000002"),
                            "commission_date": pd.Timestamp("2023-02-02"),
                            "tier_id": 2,
                            "tier_name": "Tier 2",
                            "original_tier_id": 2,
                            "original_tier_name": "Tier 2",
                            "tier_value": Decimal("3255.610000000001"),
                        },
                        {
                            "id": "123b",
                            "commission": Decimal("6400.26"),
                            "commission_date": pd.Timestamp("2023-02-13"),
                            "tier_id": 2,
                            "tier_name": "Tier 2",
                            "original_tier_id": 2,
                            "original_tier_name": "Tier 2",
                            "tier_value": Decimal("4266.84"),
                        },
                    ],
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-03-01",
                "2023-03-31",
                None,
                "<EMAIL>",
                ["213a"],
                MONTHLY_MAIN_PLAN,
                TIER_OVERRIDE_CRITERIA,
                {
                    "context": {
                        "213a": {
                            "co_1_deal_id": "213a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-03-02"),
                            "co_1_close_date": pd.Timestamp("2023-03-10"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 1900.333,
                            "co_1_percentage": 66.667,
                            "row_key": "213a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("11494.252874"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("11494.252874"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("957.85"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "213a",
                        }
                    },
                    "summary": {},
                    "result": [
                        {
                            "id": "213a",
                            "commission": Decimal("550.0"),
                            "commission_date": pd.Timestamp("2023-03-02"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 0,
                            "original_tier_name": "Tier 0",
                            "tier_value": Decimal("500"),
                        },
                        {
                            "id": "213a",
                            "commission": Decimal("1100.0"),
                            "commission_date": pd.Timestamp("2023-03-02"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "tier_value": Decimal("1000"),
                        },
                        {
                            "id": "213a",
                            "commission": Decimal("3450.9945"),
                            "commission_date": pd.Timestamp("2023-03-02"),
                            "tier_id": 2,
                            "tier_name": "Tier 2",
                            "original_tier_id": 2,
                            "original_tier_name": "Tier 2",
                            "tier_value": Decimal("2300.663"),
                        },
                    ],
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("11494.252874"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("11494.252874"),
                        "data_variable_pay_by_month": Decimal("957.85"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("957.854406"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                [],
                MONTHLY_MAIN_PLAN,
                TIER_OVERRIDE_CRITERIA,
                {
                    "context": {},
                    "result": [
                        {
                            "commission": 0,
                            "commission_date": make_aware_wrapper(
                                last_day_of_month(
                                    datetime.strptime("2023-02-28", "%Y-%m-%d")
                                )
                            ),
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                        }
                    ],
                    "summary": {},
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {},
                    "do_nothing_records": [],
                },
            ),
        ],
    )
    @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    @mock.patch(
        "everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date"
    )
    def test_tier_overrieden_criteria_evaluation(
        self,
        mock_fetch,
        mock_fetch_datasheet_data_as_of_date,
        period_start_date,
        period_end_date,
        secondary_kd,
        payee_email,
        row_keys,
        plan_id,
        criteria_id,
        expected_result,
    ):
        """
        Test tier overriden criteria evaluation
        """
        data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
        mock_fetch.return_value = data_frame
        mock_fetch_datasheet_data_as_of_date.return_value = data_frame
        result = get_evaluate_result(
            CLIENT_ID,
            period_start_date,
            period_end_date,
            payee_email,
            plan_id,
            criteria_id,
            secondary_kd,
        )
        assert result == expected_result

    @pytest.mark.parametrize(
        "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id, expected_result",
        [
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                ["123a", "123b"],
                MONTHLY_MAIN_PLAN,
                TIER_ROLLING_COUNT_CRITERIA,
                {
                    "context": {
                        "123a": {
                            "co_1_deal_id": "123a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-02-02", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-02-09", "%Y-%m-%d"
                            ),
                            "co_1_flag": True,
                            "co_1_deal_amount": 1000.123,
                            "co_1_percentage": 12.119,
                            "row_key": "123a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123a",
                        },
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-02-13", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-02-23", "%Y-%m-%d"
                            ),
                            "co_1_flag": False,
                            "co_1_deal_amount": 1500.45,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        },
                    },
                    "summary": {},
                    "result": [
                        {
                            "id": "123a",
                            "commission": Decimal("-9"),
                            "commission_date": datetime.strptime(
                                "2023-02-02", "%Y-%m-%d"
                            ),
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                            "original_tier_id": 0,
                            "original_tier_name": "Tier 0",
                            "tier_value": Decimal("1"),
                        }
                    ],
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                None,
                "<EMAIL>",
                ["123a", "123b"],
                MONTHLY_MAIN_PLAN,
                TIER_ROLLING_COUNT_CRITERIA,
                {
                    "context": {
                        "123a": {
                            "co_1_deal_id": "123a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-02-02", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-03-21", "%Y-%m-%d"
                            ),
                            "co_1_flag": True,
                            "co_1_deal_amount": 2500.0,
                            "co_1_percentage": 12.119,
                            "row_key": "123a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123a",
                        },
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-02-13", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-02-23", "%Y-%m-%d"
                            ),
                            "co_1_flag": False,
                            "co_1_deal_amount": 2011.23,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        },
                    },
                    "summary": {},
                    "result": [
                        {
                            "id": "123a",
                            "commission": Decimal("-9"),
                            "commission_date": datetime.strptime(
                                "2023-02-02", "%Y-%m-%d"
                            ),
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                            "original_tier_id": 0,
                            "original_tier_name": "Tier 0",
                            "tier_value": Decimal("1"),
                        }
                    ],
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-03-01",
                "2023-03-31",
                None,
                "<EMAIL>",
                ["213a"],
                MONTHLY_MAIN_PLAN,
                TIER_ROLLING_COUNT_CRITERIA,
                {
                    "context": {
                        "213a": {
                            "co_1_deal_id": "213a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-03-02", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-03-10", "%Y-%m-%d"
                            ),
                            "co_1_flag": True,
                            "co_1_deal_amount": 1900.333,
                            "co_1_percentage": 66.667,
                            "row_key": "213a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("11494.252874"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("11494.252874"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("957.85"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "213a",
                        }
                    },
                    "summary": {},
                    "result": [
                        {
                            "id": "213a",
                            "commission": Decimal("-9"),
                            "commission_date": datetime.strptime(
                                "2023-03-02", "%Y-%m-%d"
                            ),
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                            "original_tier_id": 0,
                            "original_tier_name": "Tier 0",
                            "tier_value": Decimal("1"),
                        }
                    ],
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("11494.252874"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("11494.252874"),
                        "data_variable_pay_by_month": Decimal("957.85"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("957.854406"),
                    },
                    "do_nothing_records": [],
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                [],
                MONTHLY_MAIN_PLAN,
                TIER_ROLLING_COUNT_CRITERIA,
                {
                    "context": {},
                    "result": [
                        {
                            "commission": 0,
                            "commission_date": make_aware_wrapper(
                                last_day_of_month(
                                    datetime.strptime("2023-02-28", "%Y-%m-%d")
                                )
                            ),
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                        }
                    ],
                    "summary": {},
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": {},
                    "do_nothing_records": [],
                },
            ),
        ],
    )
    @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    @mock.patch(
        "everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date"
    )
    def test_tier_rolling_count_criteria_evaluation(
        self,
        mock_fetch,
        mock_fetch_datasheet_data_as_of_date,
        period_start_date,
        period_end_date,
        secondary_kd,
        payee_email,
        row_keys,
        plan_id,
        criteria_id,
        expected_result,
    ):
        """
        Test tier rolling count criteria evaluation
        """
        data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
        mock_fetch.return_value = data_frame
        mock_fetch_datasheet_data_as_of_date.return_value = data_frame
        result = get_evaluate_result(
            CLIENT_ID,
            period_start_date,
            period_end_date,
            payee_email,
            plan_id,
            criteria_id,
            secondary_kd,
        )
        assert result == expected_result

    @pytest.mark.parametrize(
        "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id, expected_result",
        [
            (
                "2023-03-01",
                "2023-03-31",
                None,
                "<EMAIL>",
                ["211pp", "23r5"],
                MONTHLY_MANAGERS_PLAN,
                PAYEE_REPORTS_CRITERA,
                {
                    "context": {
                        "211pp": {
                            "co_1_deal_id": "211pp",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-03-22", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-03-22", "%Y-%m-%d"
                            ),
                            "co_1_flag": True,
                            "co_1_deal_amount": 3009.11,
                            "co_1_percentage": 38.96,
                            "row_key": "211pp",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10000.00"),
                            "data_payout_frequency": "Quarterly",
                            "data_variable_pay_base_currency": Decimal("10000.00"),
                            "data_variable_pay_as_per_period": Decimal("2500.00"),
                            "data_variable_pay_by_month": Decimal("833.33"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "USD",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "AUS",
                            "data_employment_country_name": "Australia",
                            "data_full_name": "Clark Kent",
                            "data_first_name": "Clark",
                            "data_last_name": "Kent",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": "<EMAIL>",
                            "data_manager_name": "Bruce Wayne",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "211pp",
                        },
                        "23r5": {
                            "co_1_deal_id": "23r5",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-03-28", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-03-29", "%Y-%m-%d"
                            ),
                            "co_1_flag": False,
                            "co_1_deal_amount": 4120.009,
                            "co_1_percentage": 12.46,
                            "row_key": "23r5",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("22988.505747"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("22988.505747"),
                            "data_variable_pay_as_per_period": Decimal("1666.67"),
                            "data_variable_pay_in_table": Decimal("20000.00"),
                            "data_variable_pay_by_month": Decimal("1915.71"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Barry Allen",
                            "data_first_name": "Barry",
                            "data_last_name": "Allen",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": "<EMAIL>",
                            "data_manager_name": "Clark Kent",
                            "data_payee_or_manager": "Payee",
                            "key": "23r5",
                        },
                    },
                    "summary": {},
                    "result": [
                        {
                            "id": "211pp",
                            "commission": Decimal("3009.11"),
                            "commission_date": datetime.strptime(
                                "2023-03-22", "%Y-%m-%d"
                            ),
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "id": "23r5",
                            "commission": Decimal("4120.009"),
                            "commission_date": datetime.strptime(
                                "2023-03-29", "%Y-%m-%d"
                            ),
                            "source_payee": "<EMAIL>",
                        },
                    ],
                    "is_line_item_level": True,
                    "do_nothing_records": [],
                    "team_owner_detail": {
                        "email": "<EMAIL>",
                        "actual_variable_pay": Decimal("11494.252874"),
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("957.854406"),
                        "data_variable_pay_base_currency": Decimal("11494.252874"),
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_by_month": Decimal("957.85"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "data_pay_currency": "EUR",
                        "data_joining_date": datetime(2023, 1, 1, 0, 0, tzinfo=UTC),
                        "data_exit_date": None,
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_full_name": "Bruce Wayne",
                        "data_first_name": "Bruce",
                        "data_last_name": "Wayne",
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_payee_or_manager": "Payee Manager",
                    },
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                [],
                MONTHLY_MANAGERS_PLAN,
                PAYEE_REPORTS_CRITERA,
                {
                    "context": {},
                    "summary": None,
                    "result": [
                        {
                            "commission": 0,
                            "commission_date": make_aware_wrapper(
                                datetime(2023, 2, 28, 23, 59, 59, 999999)
                            ),
                        }
                    ],
                    "is_line_item_level": True,
                    "do_nothing_records": [],
                },
            ),
        ],
    )
    @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    @mock.patch(
        "everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date"
    )
    def test_team_payees_reports_criteria_evaluation(
        self,
        mock_fetch_datasheet_data_as_of_date,
        mock_fetch,
        period_start_date,
        period_end_date,
        secondary_kd,
        payee_email,
        row_keys,
        plan_id,
        criteria_id,
        expected_result,
    ):
        """
        Test team payees reports critxeria evaluation
        """
        data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
        mock_fetch.return_value = data_frame
        mock_fetch_datasheet_data_as_of_date.return_value = data_frame
        result = get_evaluate_result(
            CLIENT_ID,
            period_start_date,
            period_end_date,
            payee_email,
            plan_id,
            criteria_id,
            secondary_kd,
        )
        expected_commission_result = expected_result.pop("result")
        actual_commission_result = result.pop("result")
        expected_commission_result = sorted(
            expected_commission_result, key=lambda x: x.get("id", 0)
        )
        actual_commission_result = sorted(
            actual_commission_result, key=lambda x: x.get("id", 0)
        )
        if result != expected_result:
            print(
                f"result is != expected...result - {result}\nexpected - {expected_result}"
            )
        assert result == expected_result

    @pytest.mark.parametrize(
        "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id, expected_result",
        [
            (
                "2023-02-01",
                "2023-04-30",
                None,
                "<EMAIL>",
                ["211pp", "332qw"],
                QUARTERLY_MAIN_PLAN,
                QUARTERLY_PRIMARY_QUOTA_CRITERIA,
                {
                    "context": {
                        "211pp": {
                            "co_1_deal_id": "211pp",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-03-22"),
                            "co_1_close_date": pd.Timestamp("2023-03-22"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 3009.11,
                            "co_1_percentage": 38.96,
                            "row_key": "211pp",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10000.00"),
                            "data_payout_frequency": "Quarterly",
                            "data_variable_pay_base_currency": Decimal("10000.00"),
                            "data_variable_pay_as_per_period": Decimal("2500.00"),
                            "data_variable_pay_by_month": Decimal("833.33"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "USD",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "AUS",
                            "data_employment_country_name": "Australia",
                            "data_full_name": "Clark Kent",
                            "data_first_name": "Clark",
                            "data_last_name": "Kent",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": "<EMAIL>",
                            "data_manager_name": "Bruce Wayne",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "211pp",
                        },
                        "332qw": {
                            "co_1_deal_id": "332qw",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-04-01"),
                            "co_1_close_date": pd.Timestamp("2023-04-01"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 514.43,
                            "co_1_percentage": 21.13,
                            "row_key": "332qw",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10000.00"),
                            "data_payout_frequency": "Quarterly",
                            "data_variable_pay_base_currency": Decimal("10000.00"),
                            "data_variable_pay_as_per_period": Decimal("2500.00"),
                            "data_variable_pay_by_month": Decimal("833.33"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "USD",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "AUS",
                            "data_employment_country_name": "Australia",
                            "data_full_name": "Clark Kent",
                            "data_first_name": "Clark",
                            "data_last_name": "Kent",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": "<EMAIL>",
                            "data_manager_name": "Bruce Wayne",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "332qw",
                        },
                    },
                    "summary": {"qv": Decimal("1000"), "cumulative_qe": 0},
                    "result": [
                        {
                            "id": None,
                            "quota_erosion": Decimal("3523.54"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "commission": Decimal("24830.6682632"),
                            "commission_date": make_aware_wrapper(
                                datetime(2023, 4, 30, 23, 59, 59, 999999)
                            ),
                        }
                    ],
                    "is_line_item_level": False,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("10000.00"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "AUS",
                        "data_employment_country_name": "Australia",
                        "data_exit_date": None,
                        "data_first_name": "Clark",
                        "data_full_name": "Clark Kent",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Kent",
                        "data_manager_email": "<EMAIL>",
                        "data_manager_name": "Bruce Wayne",
                        "data_pay_currency": "USD",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("2500.00"),
                        "data_variable_pay_base_currency": Decimal("10000.00"),
                        "data_variable_pay_by_month": Decimal("833.33"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Quarterly",
                        "variable_pay": Decimal("2500.00"),
                    },
                    "do_nothing_records": [],
                },
            ),
        ],
    )
    @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    @mock.patch(
        "everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date"
    )
    def test_quota_criteria_with_payout_frequency_equal_to_quota_frequency(
        self,
        mock_fetch,
        mock_fetch_datasheet_data_as_of_date,
        period_start_date,
        period_end_date,
        secondary_kd,
        payee_email,
        row_keys,
        plan_id,
        criteria_id,
        expected_result,
    ):
        """
        Test row level quota criteria evaluation with payout frequency equal to quota frequency
        """
        data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
        mock_fetch.return_value = data_frame
        mock_fetch_datasheet_data_as_of_date.return_value = data_frame
        result = get_evaluate_result(
            CLIENT_ID,
            period_start_date,
            period_end_date,
            payee_email,
            plan_id,
            criteria_id,
            secondary_kd,
        )
        assert result == expected_result

    # TODO: Fix the test case. plan and criteria id do not match
    # @pytest.mark.parametrize(
    #     "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id, expected_result",
    #     [
    #         (
    #             "2023-03-01",
    #             "2023-03-31",
    #             None,
    #             "<EMAIL>",
    #             ["213a", "123a"],
    #             MONTHLY_MAIN_PLAN,
    #             PRIMARY_QUOTA_CRITERIA,
    #             {
    #                 "context": {
    #                     "213a": {
    #                         "co_1_deal_id": "213a",
    #                         "co_1_email": "<EMAIL>",
    #                         "co_1_sales_date": datetime.strptime(
    #                             "2023-03-02", "%Y-%m-%d"
    #                         ),
    #                         "co_1_close_date": datetime.strptime(
    #                             "2023-03-10", "%Y-%m-%d"
    #                         ),
    #                         "co_1_flag": True,
    #                         "co_1_deal_amount": 1900.333,
    #                         "co_1_percentage": 66.667,
    #                         "row_key": "213a",
    #                         "email": "<EMAIL>",
    #                         "actual_variable_pay": Decimal("11494.252874"),
    #                         "payout_frequency": "Monthly",
    #                         "key": "213a",
    #                     },
    #                     "123a": {
    #                         "co_1_deal_id": "123a",
    #                         "co_1_email": "<EMAIL>",
    #                         "co_1_sales_date": datetime.strptime(
    #                             "2023-02-02", "%Y-%m-%d"
    #                         ),
    #                         "co_1_close_date": datetime.strptime(
    #                             "2023-03-21", "%Y-%m-%d"
    #                         ),
    #                         "co_1_flag": True,
    #                         "co_1_deal_amount": 2500.0,
    #                         "co_1_percentage": 12.119,
    #                         "row_key": "123a",
    #                         "email": "<EMAIL>",
    #                         "actual_variable_pay": Decimal("11494.252874"),
    #                         "payout_frequency": "Monthly",
    #                         "key": "123a",
    #                     },
    #                 },
    #                 "summary": {
    #                     "qv": Decimal("1100.00"),
    #                     "cumulative_qe": Decimal("2011.230000"),
    #                 },
    #                 "result": [
    #                     {
    #                         "id": None,
    #                         "quota_erosion": Decimal("4400.333"),
    #                         "tier_id": 1,
    #                         "tier_name": "Tier 1",
    #                         "commission": Decimal("51296.38590996181915723272224"),
    #                         "commission_date": make_aware_wrapper(
    #                             datetime(2023, 3, 31, 23, 59, 59, 999999)
    #                         ),
    #                     }
    #                 ],
    #                 "is_line_item_level": False,
    #                 "do_nothing_records": [],
    #             },
    #         ),
    #     ],
    # )
    # @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    # @mock.patch("everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date")
    # def test_quota_row_level_criteria_with_payout_frequency_lesser_than_quota_frequency(
    #     self,
    #     mock_fetch,
    #     mock_fetch_datasheet_data_as_of_date,
    #     period_start_date,
    #     period_end_date,
    #     secondary_kd,
    #     payee_email,
    #     row_keys,
    #     plan_id,
    #     criteria_id,
    #     expected_result,
    # ):
    #     """
    #     Test row level quota criteria evaluation with payout frequency lesser than quota frequency
    #     """
    #     data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
    #     mock_fetch.return_value = data_frame
    #     result = get_evaluate_result(
    #         CLIENT_ID,
    #         period_start_date,
    #         period_end_date,
    #         payee_email,
    #         plan_id,
    #         criteria_id,
    #         secondary_kd,
    #     )
    #     assert result == expected_result

    @pytest.mark.parametrize(
        "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id, expected_result",
        [
            (
                "2023-03-01",
                "2023-03-31",
                None,
                "<EMAIL>",
                ["213a", "123a"],
                MONTHLY_MAIN_PLAN,
                PRIMARY_QUOTA_CRITERIA,
                {
                    "context": {
                        "213a": {
                            "co_1_deal_id": "213a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-03-02"),
                            "co_1_close_date": pd.Timestamp("2023-03-10"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 1900.333,
                            "co_1_percentage": 66.667,
                            "row_key": "213a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("11494.252874"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("11494.252874"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("957.85"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "213a",
                        },
                        "123a": {
                            "co_1_deal_id": "123a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-02"),
                            "co_1_close_date": pd.Timestamp("2023-03-21"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 2500.0,
                            "co_1_percentage": 12.119,
                            "row_key": "123a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("11494.252874"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("11494.252874"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("957.85"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123a",
                        },
                    },
                    "summary": {
                        "qv": Decimal("1100.00"),
                        "cumulative_qe": Decimal("2011.230000"),
                    },
                    "result": [
                        {
                            "id": None,
                            "quota_erosion": Decimal("4400.333"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "commission": Decimal("51296.38590996183"),
                            "commission_date": make_aware_wrapper(
                                datetime(2023, 3, 31, 23, 59, 59, 999999)
                            ),
                        }
                    ],
                    "is_line_item_level": False,
                    "payee_detail_sf_reports": {
                        "actual_variable_pay": Decimal("11494.252874"),
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_exit_date": None,
                        "data_first_name": "Bruce",
                        "data_full_name": "Bruce Wayne",
                        "data_joining_date": pd.Timestamp(
                            "2023-01-01 00:00:00+0000", tz="UTC"
                        ),
                        "data_last_name": "Wayne",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_pay_currency": "EUR",
                        "data_payee_or_manager": "Payee Manager",
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_base_currency": Decimal("11494.252874"),
                        "data_variable_pay_by_month": Decimal("957.85"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "email": "<EMAIL>",
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("957.854406"),
                    },
                    "do_nothing_records": [],
                },
            ),
        ],
    )
    @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    @mock.patch(
        "everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date"
    )
    def test_quota_sumation_criteria_with_payout_frequency_lesser_than_quota_frequency(
        self,
        mock_fetch,
        mock_fetch_datasheet_data_as_of_date,
        period_start_date,
        period_end_date,
        secondary_kd,
        payee_email,
        row_keys,
        plan_id,
        criteria_id,
        expected_result,
    ):
        """
        Test sumation quota criteria evaluation with payout frequency lesser than quota frequency
        """
        data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
        mock_fetch.return_value = data_frame
        mock_fetch_datasheet_data_as_of_date.return_value = data_frame
        result = get_evaluate_result(
            CLIENT_ID,
            period_start_date,
            period_end_date,
            payee_email,
            plan_id,
            criteria_id,
            secondary_kd,
        )
        assert result == expected_result

    @pytest.mark.parametrize(
        "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id, expected_result",
        [
            (
                "2023-02-01",
                "2023-02-28",
                None,
                "<EMAIL>",
                ["123b"],
                MONTHLY_MANAGERS_PLAN,
                TEAM_QUOTA_CRITERA,
                {
                    "context": {
                        "123b": {
                            "co_1_deal_id": "123b",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": datetime.strptime(
                                "2023-02-13", "%Y-%m-%d"
                            ),
                            "co_1_close_date": datetime.strptime(
                                "2023-02-23", "%Y-%m-%d"
                            ),
                            "co_1_flag": False,
                            "co_1_deal_amount": 2011.23,
                            "co_1_percentage": 23.41,
                            "row_key": "123b",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10204.081633"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("10204.081633"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("850.34"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123b",
                        }
                    },
                    "summary": {"qv": Decimal("1000"), "cumulative_qe": 0},
                    "result": [
                        {
                            "id": "123b",
                            "commission": Decimal("850.00"),
                            "commission_date": datetime.strptime(
                                "2023-02-23", "%Y-%m-%d"
                            ),
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                            "original_tier_id": 0,
                            "original_tier_name": "Tier 0",
                            "quota_erosion": Decimal("1000"),
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "id": "123b",
                            "commission": Decimal("1162.9144999999999"),
                            "commission_date": datetime.strptime(
                                "2023-02-23", "%Y-%m-%d"
                            ),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "quota_erosion": Decimal("1011.23"),
                            "source_payee": "<EMAIL>",
                        },
                    ],
                    "is_line_item_level": True,
                    "do_nothing_records": [],
                    "team_owner_detail": {
                        "email": "<EMAIL>",
                        "actual_variable_pay": Decimal("10204.081633"),
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("850.340136"),
                        "data_variable_pay_base_currency": Decimal("10204.081633"),
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_by_month": Decimal("850.34"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "data_pay_currency": "EUR",
                        "data_joining_date": datetime(2023, 1, 1, 0, 0, tzinfo=UTC),
                        "data_exit_date": None,
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_full_name": "Bruce Wayne",
                        "data_first_name": "Bruce",
                        "data_last_name": "Wayne",
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_payee_or_manager": "Payee Manager",
                    },
                },
            ),
            (
                "2023-03-01",
                "2023-03-31",
                None,
                "<EMAIL>",
                ["213a", "123a", "211pp", "23r5"],
                MONTHLY_MANAGERS_PLAN,
                TEAM_QUOTA_CRITERA,
                {
                    "context": {
                        "213a": {
                            "co_1_deal_id": "213a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-03-02"),
                            "co_1_close_date": pd.Timestamp("2023-03-10"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 1900.333,
                            "co_1_percentage": 66.667,
                            "row_key": "213a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("11494.252874"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("11494.252874"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("957.85"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "213a",
                        },
                        "123a": {
                            "co_1_deal_id": "123a",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-02-02"),
                            "co_1_close_date": pd.Timestamp("2023-03-21"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 2500.0,
                            "co_1_percentage": 12.119,
                            "row_key": "123a",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("11494.252874"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("11494.252874"),
                            "data_variable_pay_as_per_period": Decimal("833.33"),
                            "data_variable_pay_by_month": Decimal("957.85"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Bruce Wayne",
                            "data_first_name": "Bruce",
                            "data_last_name": "Wayne",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": None,
                            "data_manager_name": "",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "123a",
                        },
                        "211pp": {
                            "co_1_deal_id": "211pp",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-03-22"),
                            "co_1_close_date": pd.Timestamp("2023-03-22"),
                            "co_1_flag": True,
                            "co_1_deal_amount": 3009.11,
                            "co_1_percentage": 38.96,
                            "row_key": "211pp",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("10000.00"),
                            "data_payout_frequency": "Quarterly",
                            "data_variable_pay_base_currency": Decimal("10000.00"),
                            "data_variable_pay_as_per_period": Decimal("2500.00"),
                            "data_variable_pay_by_month": Decimal("833.33"),
                            "data_variable_pay_in_table": Decimal("10000.00"),
                            "data_pay_currency": "USD",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "AUS",
                            "data_employment_country_name": "Australia",
                            "data_full_name": "Clark Kent",
                            "data_first_name": "Clark",
                            "data_last_name": "Kent",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": "<EMAIL>",
                            "data_manager_name": "Bruce Wayne",
                            "data_payee_or_manager": "Payee Manager",
                            "key": "211pp",
                        },
                        "23r5": {
                            "co_1_deal_id": "23r5",
                            "co_1_email": "<EMAIL>",
                            "co_1_sales_date": pd.Timestamp("2023-03-28"),
                            "co_1_close_date": pd.Timestamp("2023-03-29"),
                            "co_1_flag": False,
                            "co_1_deal_amount": 4120.009,
                            "co_1_percentage": 12.46,
                            "row_key": "23r5",
                            "email": "<EMAIL>",
                            "actual_variable_pay": Decimal("22988.505747"),
                            "data_payout_frequency": "Monthly",
                            "data_variable_pay_base_currency": Decimal("22988.505747"),
                            "data_variable_pay_as_per_period": Decimal("1666.67"),
                            "data_variable_pay_in_table": Decimal("20000.00"),
                            "data_variable_pay_by_month": Decimal("1915.71"),
                            "data_pay_currency": "EUR",
                            "data_joining_date": pd.Timestamp(
                                "2023-01-01 00:00:00+0000", tz="UTC"
                            ),
                            "data_exit_date": None,
                            "data_employment_country": "GBR",
                            "data_employment_country_name": "United Kingdom",
                            "data_full_name": "Barry Allen",
                            "data_first_name": "Barry",
                            "data_last_name": "Allen",
                            "data_designation": "",
                            "data_employee_id": "",
                            "data_manager_email": "<EMAIL>",
                            "data_manager_name": "Clark Kent",
                            "data_payee_or_manager": "Payee",
                            "key": "23r5",
                        },
                    },
                    "summary": {
                        "qv": Decimal("1100"),
                        "cumulative_qe": Decimal("2011.230000"),
                    },
                    "result": [
                        {
                            "id": "213a",
                            "commission": Decimal("2185.3829499999997"),
                            "commission_date": pd.Timestamp("2023-03-10"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "quota_erosion": Decimal("1900.333"),
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "id": "123a",
                            "commission": Decimal("2875.0"),
                            "commission_date": pd.Timestamp("2023-03-21"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "quota_erosion": Decimal("2500.0"),
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "id": "211pp",
                            "commission": Decimal("3460.4764999999998"),
                            "commission_date": pd.Timestamp("2023-03-22"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "quota_erosion": Decimal("3009.11"),
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "id": "23r5",
                            "commission": Decimal("4738.01035"),
                            "commission_date": pd.Timestamp("2023-03-29"),
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "quota_erosion": Decimal("4120.009"),
                            "source_payee": "<EMAIL>",
                        },
                    ],
                    "is_line_item_level": True,
                    "do_nothing_records": [],
                    "team_owner_detail": {
                        "email": "<EMAIL>",
                        "actual_variable_pay": Decimal("11494.252874"),
                        "data_payout_frequency": "Monthly",
                        "variable_pay": Decimal("957.854406"),
                        "data_variable_pay_base_currency": Decimal("11494.252874"),
                        "data_variable_pay_as_per_period": Decimal("833.33"),
                        "data_variable_pay_by_month": Decimal("957.85"),
                        "data_variable_pay_in_table": Decimal("10000.00"),
                        "data_pay_currency": "EUR",
                        "data_joining_date": datetime(2023, 1, 1, 0, 0, tzinfo=UTC),
                        "data_exit_date": None,
                        "data_employment_country": "GBR",
                        "data_employment_country_name": "United Kingdom",
                        "data_full_name": "Bruce Wayne",
                        "data_first_name": "Bruce",
                        "data_last_name": "Wayne",
                        "data_designation": "",
                        "data_employee_id": "",
                        "data_manager_email": None,
                        "data_manager_name": "",
                        "data_payee_or_manager": "Payee Manager",
                    },
                },
            ),
            (
                "2023-02-01",
                "2023-02-28",
                "2023-06-03",
                "<EMAIL>",
                [],
                MONTHLY_MANAGERS_PLAN,
                TEAM_QUOTA_CRITERA,
                {
                    "context": {},
                    "summary": {"qv": 0, "cumulative_qe": 0},
                    "result": [
                        {
                            "commission": 0,
                            "source_payee": "<EMAIL>",
                            "quota_erosion": 0,
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                        }
                    ],
                    "is_line_item_level": True,
                    "do_nothing_records": [],
                },
            ),
        ],
    )
    @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    @mock.patch(
        "everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date"
    )
    def test_manager_quota_criteria_evaluation(
        self,
        mock_fetch,
        mock_fetch_datasheet_data_as_of_date,
        period_start_date,
        period_end_date,
        secondary_kd,
        payee_email,
        row_keys,
        plan_id,
        criteria_id,
        expected_result,
    ):
        """
        Test manager quota criteria evaluation
        """
        data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
        mock_fetch.return_value = data_frame
        mock_fetch_datasheet_data_as_of_date.return_value = data_frame
        result = get_evaluate_result(
            CLIENT_ID,
            period_start_date,
            period_end_date,
            payee_email,
            plan_id,
            criteria_id,
            secondary_kd,
        )
        if result != expected_result:
            print(
                f"Result is != expected\nresult - {result}\nexpected = {expected_result}"
            )
        assert result == expected_result
