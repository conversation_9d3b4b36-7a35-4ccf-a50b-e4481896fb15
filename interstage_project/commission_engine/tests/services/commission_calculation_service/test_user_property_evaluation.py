from datetime import datetime
from decimal import Decimal
from unittest import mock

import pandas as pd
import pytest
import pytz

from commission_engine.accessors.client_accessor import get_evaluation_mode
from commission_engine.services.commission_calculation_service.evaluate_vectorized_wrapper import (
    EvaluationMode,
)
from commission_engine.tests.accessors.utils import set_client_feature
from commission_engine.tests.data_utils import JsonDatasheetData
from commission_engine.tests.services.commission_calculation_service.evaluate_utils import (
    get_evaluate_result,
)
from commission_engine.utils import end_of_day

CLIENT_ID = 6897
DATASHEET_NAME = "GUP-Test-Datasheet"

FEB_1_2024 = datetime.strptime("2024-02-01", "%Y-%m-%d").replace(tzinfo=pytz.UTC)
MAR_1_2024 = datetime.strptime("2024-03-01", "%Y-%m-%d").replace(tzinfo=pytz.UTC)
MAX_DATE = pd.Timestamp.max.replace(tzinfo=pytz.UTC)
MAR_15_2024 = datetime.strptime("2024-03-15", "%Y-%m-%d")
MAR_31_2024_EOD = end_of_day(datetime.strptime("2024-03-31", "%Y-%m-%d")).replace(
    tzinfo=pytz.UTC
)

json_datasheet_data = JsonDatasheetData(CLIENT_ID, DATASHEET_NAME, ["co_4_closedate"])

GUP_PLAN = "b02dd99e-5321-43c1-8010-67771c184ac5"

GUP_SIMPLE_TEAM_PAYEES_REPORTEES = "03f24621-ed54-48e4-a9e0-0ee7fe89c7ee"
GUP_CONDITIONAL_TEAM = "07dcb7f9-888c-42bd-a449-5b7c69a2291c"
GUP_SIMPLE_NON_LINE_ITEM = "0d6082b5-ac32-4ff9-8809-2b0285b27bf7"
GUP_CONDITIONAL_FALSE_NON_LINE_ITEM = "16bf8d07-beb8-48bb-aab9-096c01459013"
GUP_CONDITIONAL_TRUE = "4140d434-f081-4414-8f0b-9b05cce6c384"
GUP_TIER = "416f5f70-b538-45a9-b9c8-1b9e8537073d"
GUP_SIMPLE_NON_LINE_ITEM_TEAM = "58503503-2b8f-46f4-afa1-f1c9b7e94143"
GUP_SIMPLE_TEAM_FWTCIC = "651ace27-27fd-46df-905e-60f48edd483f"
GUP_SIMPLE_TEAM_PAYEE_AND_TEAM = "6fdb8952-bfc4-4418-8999-005d8e294025"
GUP_TIER_TEAM = "aa11c8cd-9955-4ef2-a7a5-59bffe35e1b0"
GUP_SIMPLE = "d48fd6b6-e1de-4aac-9c20-470253cee30a"
GUP_CONDITIONAL_FALSE = "d9405a7a-fae3-42fb-8076-d552b9806363"
GUP_CONDITIONAL_TRUE_NON_LINE_ITEM = "e396da15-e059-4d51-81ef-a85d83f6208f"

LINE_ITEM_CONTEXTS = {
    1: {
        "actual_variable_pay": 10000.00,
        "co_4_amount": 100,
        "co_4_closedate": MAR_15_2024,
        "co_4_email": "<EMAIL>",
        "co_4_id": 1,
        "email": "<EMAIL>",
        "key": 1,
        "data_payout_frequency": "Monthly",
        "row_key": 1,
        "data_designation": None,
        "data_employee_id": None,
        "data_employment_country": "IND",
        "data_exit_date": None,
        "data_first_name": "GUP",
        "data_full_name": "GUP Payee",
        "data_joining_date": MAR_1_2024,
        "data_last_name": "Payee",
        "data_manager_email": "<EMAIL>",
        "data_manager_name": "GUP Manager",
        "data_pay_currency": "INR",
        "data_payee_or_manager": "Payee",
        "data_variable_pay_as_per_period": Decimal("833.33"),
        "data_variable_pay_base_currency": 10000.00,
        "data_variable_pay_by_month": Decimal("833.33"),
        "data_variable_pay_in_table": 10000.00,
        "data_employment_country_name": "India",
    },
    2: {
        "actual_variable_pay": 10000.00,
        "co_4_amount": 100,
        "co_4_closedate": MAR_15_2024,
        "co_4_email": "<EMAIL>",
        "co_4_id": 2,
        "email": "<EMAIL>",
        "key": 2,
        "data_payout_frequency": "Monthly",
        "row_key": 2,
        "data_designation": None,
        "data_employee_id": None,
        "data_employment_country": "IND",
        "data_exit_date": None,
        "data_first_name": "GUP",
        "data_full_name": "GUP Payee",
        "data_joining_date": MAR_1_2024,
        "data_last_name": "Payee",
        "data_manager_email": "<EMAIL>",
        "data_manager_name": "GUP Manager",
        "data_pay_currency": "INR",
        "data_payee_or_manager": "Payee",
        "data_variable_pay_as_per_period": Decimal("833.33"),
        "data_variable_pay_base_currency": 10000.00,
        "data_variable_pay_by_month": Decimal("833.33"),
        "data_variable_pay_in_table": 10000.00,
        "data_employment_country_name": "India",
    },
    3: {
        "actual_variable_pay": 10000.00,
        "co_4_amount": 100,
        "co_4_closedate": MAR_15_2024,
        "co_4_email": "<EMAIL>",
        "co_4_id": 3,
        "email": "<EMAIL>",
        "key": 3,
        "data_payout_frequency": "Monthly",
        "row_key": 3,
        "data_designation": None,
        "data_employee_id": None,
        "data_employment_country": "IND",
        "data_exit_date": None,
        "data_first_name": "GUP",
        "data_full_name": "GUP Payee",
        "data_joining_date": MAR_1_2024,
        "data_last_name": "Payee",
        "data_manager_email": "<EMAIL>",
        "data_manager_name": "GUP Manager",
        "data_pay_currency": "INR",
        "data_payee_or_manager": "Payee",
        "data_variable_pay_as_per_period": Decimal("833.33"),
        "data_variable_pay_base_currency": 10000.00,
        "data_variable_pay_by_month": Decimal("833.33"),
        "data_variable_pay_in_table": 10000.00,
        "data_employment_country_name": "India",
    },
    4: {
        "actual_variable_pay": 10000.00,
        "co_4_amount": 100,
        "co_4_closedate": MAR_15_2024,
        "co_4_email": "<EMAIL>",
        "co_4_id": 4,
        "email": "<EMAIL>",
        "key": 4,
        "data_payout_frequency": "Monthly",
        "row_key": 4,
        "data_designation": None,
        "data_employee_id": None,
        "data_employment_country": "IND",
        "data_exit_date": None,
        "data_first_name": "GUP",
        "data_full_name": "GUP Payee",
        "data_joining_date": MAR_1_2024,
        "data_last_name": "Payee",
        "data_manager_email": "<EMAIL>",
        "data_manager_name": "GUP Manager",
        "data_pay_currency": "INR",
        "data_payee_or_manager": "Payee",
        "data_variable_pay_as_per_period": Decimal("833.33"),
        "data_variable_pay_base_currency": 10000.00,
        "data_variable_pay_by_month": Decimal("833.33"),
        "data_variable_pay_in_table": 10000.00,
        "data_employment_country_name": "India",
    },
    5: {
        "actual_variable_pay": 40000.00,
        "co_4_amount": 100,
        "co_4_closedate": MAR_15_2024,
        "co_4_email": "<EMAIL>",
        "co_4_id": 5,
        "email": "<EMAIL>",
        "key": 5,
        "data_payout_frequency": "Monthly",
        "row_key": 5,
        "data_designation": None,
        "data_employee_id": None,
        "data_employment_country": "IND",
        "data_exit_date": None,
        "data_first_name": "GUP",
        "data_full_name": "GUP Manager",
        "data_joining_date": FEB_1_2024,
        "data_last_name": "Manager",
        "data_manager_email": None,
        "data_manager_name": "",
        "data_pay_currency": "INR",
        "data_payee_or_manager": "Payee Manager",
        "data_variable_pay_as_per_period": Decimal("3333.33"),
        "data_variable_pay_base_currency": 40000.00,
        "data_variable_pay_by_month": Decimal("3333.33"),
        "data_variable_pay_in_table": 40000.00,
        "data_employment_country_name": "India",
    },
    6: {
        "actual_variable_pay": 40000.00,
        "co_4_amount": 100,
        "co_4_closedate": MAR_15_2024,
        "co_4_email": "<EMAIL>",
        "co_4_id": 6,
        "email": "<EMAIL>",
        "key": 6,
        "data_payout_frequency": "Monthly",
        "row_key": 6,
        "data_designation": None,
        "data_employee_id": None,
        "data_employment_country": "IND",
        "data_exit_date": None,
        "data_first_name": "GUP",
        "data_full_name": "GUP Manager",
        "data_joining_date": FEB_1_2024,
        "data_last_name": "Manager",
        "data_manager_email": None,
        "data_manager_name": "",
        "data_pay_currency": "INR",
        "data_payee_or_manager": "Payee Manager",
        "data_variable_pay_as_per_period": Decimal("3333.33"),
        "data_variable_pay_base_currency": 40000.00,
        "data_variable_pay_by_month": Decimal("3333.33"),
        "data_variable_pay_in_table": 40000.00,
        "data_employment_country_name": "India",
    },
}

PAYEE_DETAIL = {
    "actual_variable_pay": Decimal("10000.00"),
    "data_designation": None,
    "data_employee_id": None,
    "data_employment_country": "IND",
    "data_employment_country_name": "India",
    "data_exit_date": None,
    "data_first_name": "GUP",
    "data_full_name": "GUP Payee",
    "data_joining_date": MAR_1_2024,
    "data_last_name": "Payee",
    "data_manager_email": "<EMAIL>",
    "data_manager_name": "GUP Manager",
    "data_pay_currency": "INR",
    "data_payee_or_manager": "Payee",
    "data_variable_pay_as_per_period": Decimal("833.33"),
    "data_variable_pay_base_currency": Decimal("10000.00"),
    "data_variable_pay_by_month": Decimal("833.33"),
    "data_variable_pay_in_table": Decimal("10000.00"),
    "email": "<EMAIL>",
    "data_payout_frequency": "Monthly",
    "variable_pay": Decimal("833.3333333333333333333333333"),
}

TEAM_OWNER_DETAIL = {
    "actual_variable_pay": 40000.00,
    "data_designation": None,
    "email": "<EMAIL>",
    "data_employee_id": None,
    "data_employment_country": "IND",
    "data_exit_date": None,
    "data_first_name": "GUP",
    "data_full_name": "GUP Manager",
    "data_joining_date": FEB_1_2024,
    "data_last_name": "Manager",
    "data_manager_email": None,
    "data_manager_name": "",
    "data_pay_currency": "INR",
    "data_payee_or_manager": "Payee Manager",
    "data_payout_frequency": "Monthly",
    "variable_pay": Decimal("3333.333333333333333333333333"),
    "data_variable_pay_as_per_period": Decimal("3333.33"),
    "data_variable_pay_base_currency": 40000.00,
    "data_variable_pay_by_month": Decimal("3333.33"),
    "data_variable_pay_in_table": 40000.00,
    "data_employment_country_name": "India",
}


def sort_results_by_id(results):
    return sorted(results, key=lambda result: result["id"])


@pytest.mark.django_db
@pytest.mark.commission_engine
class TestGetUserPropertyEvaluate:
    @pytest.mark.parametrize(
        "period_start_date,period_end_date,secondary_kd,payee_email,row_keys,plan_id,criteria_id,mock_user_property_values,expected_result",
        [
            (
                "2024-03-01",
                "2024-03-31",
                "2024-03-01",
                "<EMAIL>",
                [1, 2, 3],
                GUP_PLAN,
                GUP_SIMPLE,
                [
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": MAR_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 20000.0,
                        "payee_variable_pay": 10000.0,
                    }
                ],
                {
                    "context": {
                        1: LINE_ITEM_CONTEXTS[1],
                        2: LINE_ITEM_CONTEXTS[2],
                        3: LINE_ITEM_CONTEXTS[3],
                    },
                    "do_nothing_records": [],
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": PAYEE_DETAIL,
                    "result": [
                        {"commission": 30000, "commission_date": MAR_15_2024, "id": 1},
                        {"commission": 30000, "commission_date": MAR_15_2024, "id": 2},
                        {"commission": 30000, "commission_date": MAR_15_2024, "id": 3},
                    ],
                    "summary": {},
                },
            ),
            (
                "2024-03-01",
                "2024-03-31",
                "2024-03-01",
                "<EMAIL>",
                [1, 2, 3],
                GUP_PLAN,
                GUP_CONDITIONAL_TRUE,
                [
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": MAR_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 20000.0,
                        "payee_variable_pay": 10000.0,
                    }
                ],
                {
                    "context": {
                        1: LINE_ITEM_CONTEXTS[1],
                        2: LINE_ITEM_CONTEXTS[2],
                        3: LINE_ITEM_CONTEXTS[3],
                    },
                    "do_nothing_records": [],
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": PAYEE_DETAIL,
                    "result": [
                        {"commission": 10000, "commission_date": MAR_15_2024, "id": 1},
                        {"commission": 10000, "commission_date": MAR_15_2024, "id": 2},
                        {"commission": 10000, "commission_date": MAR_15_2024, "id": 3},
                    ],
                    "summary": {},
                },
            ),
            (
                "2024-03-01",
                "2024-03-31",
                "2024-03-01",
                "<EMAIL>",
                [1, 2, 3],
                GUP_PLAN,
                GUP_CONDITIONAL_FALSE,
                [
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": MAR_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 20000.0,
                        "payee_variable_pay": 10000.0,
                    }
                ],
                {
                    "context": {
                        1: LINE_ITEM_CONTEXTS[1],
                        2: LINE_ITEM_CONTEXTS[2],
                        3: LINE_ITEM_CONTEXTS[3],
                    },
                    "do_nothing_records": [],
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": PAYEE_DETAIL,
                    "result": [
                        {"commission": 0, "commission_date": MAR_15_2024, "id": 1},
                        {"commission": 0, "commission_date": MAR_15_2024, "id": 2},
                        {"commission": 0, "commission_date": MAR_15_2024, "id": 3},
                    ],
                    "summary": {},
                },
            ),
            (
                "2024-03-01",
                "2024-03-31",
                "2024-03-01",
                "<EMAIL>",
                [1, 2, 3, 4],
                GUP_PLAN,
                GUP_TIER,
                [
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": MAR_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 20000.0,
                        "payee_variable_pay": 10000.0,
                    }
                ],
                {
                    "context": {
                        1: LINE_ITEM_CONTEXTS[1],
                        2: LINE_ITEM_CONTEXTS[2],
                        3: LINE_ITEM_CONTEXTS[3],
                        4: LINE_ITEM_CONTEXTS[4],
                    },
                    "do_nothing_records": [],
                    "is_line_item_level": True,
                    "payee_detail_sf_reports": PAYEE_DETAIL,
                    "result": [
                        {
                            "commission": 20000,
                            "commission_date": MAR_15_2024,
                            "id": 1,
                            "original_tier_id": 0,
                            "original_tier_name": "Tier 0",
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                            "tier_value": 10000,
                        },
                        {
                            "commission": 20000,
                            "commission_date": MAR_15_2024,
                            "id": 2,
                            "original_tier_id": 0,
                            "original_tier_name": "Tier 0",
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                            "tier_value": 10000,
                        },
                        {
                            "commission": 30000,
                            "commission_date": MAR_15_2024,
                            "id": 3,
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "tier_value": 10000,
                        },
                        {
                            "commission": 30000,
                            "commission_date": MAR_15_2024,
                            "id": 4,
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "tier_value": 10000,
                        },
                    ],
                    "summary": {},
                },
            ),
            (
                "2024-03-01",
                "2024-03-31",
                "2024-03-01",
                "<EMAIL>",
                [1, 2, 3],
                GUP_PLAN,
                GUP_SIMPLE_NON_LINE_ITEM,
                [
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": MAR_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 20000.0,
                        "payee_variable_pay": 10000.0,
                    }
                ],
                {
                    "context": {
                        1: LINE_ITEM_CONTEXTS[1],
                        2: LINE_ITEM_CONTEXTS[2],
                        3: LINE_ITEM_CONTEXTS[3],
                    },
                    "do_nothing_records": [],
                    "is_line_item_level": False,
                    "payee_detail_sf_reports": PAYEE_DETAIL,
                    "result": [
                        {
                            "commission": 30000,
                            "commission_date": MAR_31_2024_EOD,
                            "id": None,
                        },
                    ],
                    "summary": {},
                },
            ),
            (
                "2024-03-01",
                "2024-03-31",
                "2024-03-01",
                "<EMAIL>",
                [1, 2, 3],
                GUP_PLAN,
                GUP_CONDITIONAL_TRUE_NON_LINE_ITEM,
                [
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": MAR_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 20000.0,
                        "payee_variable_pay": 10000.0,
                    }
                ],
                {
                    "context": {
                        1: LINE_ITEM_CONTEXTS[1],
                        2: LINE_ITEM_CONTEXTS[2],
                        3: LINE_ITEM_CONTEXTS[3],
                    },
                    "do_nothing_records": [],
                    "is_line_item_level": False,
                    "payee_detail_sf_reports": PAYEE_DETAIL,
                    "result": [
                        {
                            "commission": 10000,
                            "commission_date": MAR_31_2024_EOD,
                            "id": None,
                        },
                    ],
                    "summary": {},
                },
            ),
            (
                "2024-03-01",
                "2024-03-31",
                "2024-03-01",
                "<EMAIL>",
                [1, 2, 3],
                GUP_PLAN,
                GUP_CONDITIONAL_FALSE_NON_LINE_ITEM,
                [
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": MAR_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 20000.0,
                        "payee_variable_pay": 10000.0,
                    }
                ],
                {
                    "context": {
                        1: LINE_ITEM_CONTEXTS[1],
                        2: LINE_ITEM_CONTEXTS[2],
                        3: LINE_ITEM_CONTEXTS[3],
                    },
                    "do_nothing_records": [],
                    "is_line_item_level": False,
                    "payee_detail_sf_reports": PAYEE_DETAIL,
                    "result": [
                        {
                            "commission": 0,
                            "commission_date": MAR_31_2024_EOD,
                            "id": None,
                        },
                    ],
                    "summary": {},
                },
            ),
            (
                "2024-03-01",
                "2024-03-31",
                "2024-03-01",
                "<EMAIL>",
                [1, 2, 5, 6],
                GUP_PLAN,
                GUP_SIMPLE_TEAM_PAYEE_AND_TEAM,
                [
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": MAR_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 20000.0,
                        "payee_variable_pay": 10000.0,
                    },
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": FEB_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 50000.0,
                        "payee_variable_pay": 40000.0,
                    },
                ],
                {
                    "context": {
                        1: LINE_ITEM_CONTEXTS[1],
                        2: LINE_ITEM_CONTEXTS[2],
                        5: LINE_ITEM_CONTEXTS[5],
                        6: LINE_ITEM_CONTEXTS[6],
                    },
                    "do_nothing_records": [],
                    "is_line_item_level": True,
                    "result": [
                        {
                            "commission": 30000,
                            "commission_date": MAR_15_2024,
                            "id": 1,
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "commission": 30000,
                            "commission_date": MAR_15_2024,
                            "id": 2,
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "commission": 90000,
                            "commission_date": MAR_15_2024,
                            "id": 5,
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "commission": 90000,
                            "commission_date": MAR_15_2024,
                            "id": 6,
                            "source_payee": "<EMAIL>",
                        },
                    ],
                    "summary": {},
                    "team_owner_detail": TEAM_OWNER_DETAIL,
                },
            ),
            (
                "2024-03-01",
                "2024-03-31",
                "2024-03-01",
                "<EMAIL>",
                [1, 2, 5, 6],
                GUP_PLAN,
                GUP_SIMPLE_TEAM_PAYEES_REPORTEES,
                [
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": MAR_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 20000.0,
                        "payee_variable_pay": 10000.0,
                    },
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": FEB_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 50000.0,
                        "payee_variable_pay": 40000.0,
                    },
                ],
                {
                    "context": {
                        1: LINE_ITEM_CONTEXTS[1],
                        2: LINE_ITEM_CONTEXTS[2],
                    },
                    "do_nothing_records": [],
                    "is_line_item_level": True,
                    "result": [
                        {
                            "commission": 30000,
                            "commission_date": MAR_15_2024,
                            "id": 1,
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "commission": 30000,
                            "commission_date": MAR_15_2024,
                            "id": 2,
                            "source_payee": "<EMAIL>",
                        },
                    ],
                    "summary": {},
                    "team_owner_detail": TEAM_OWNER_DETAIL,
                },
            ),
            (
                "2024-03-01",
                "2024-03-31",
                "2024-03-01",
                "<EMAIL>",
                [1, 2, 5, 6],
                GUP_PLAN,
                GUP_SIMPLE_TEAM_FWTCIC,
                [
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": MAR_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 20000.0,
                        "payee_variable_pay": 10000.0,
                    },
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": FEB_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 50000.0,
                        "payee_variable_pay": 40000.0,
                    },
                ],
                {
                    "context": {
                        1: LINE_ITEM_CONTEXTS[1],
                        2: LINE_ITEM_CONTEXTS[2],
                        5: LINE_ITEM_CONTEXTS[5],
                        6: LINE_ITEM_CONTEXTS[6],
                    },
                    "do_nothing_records": [],
                    "is_line_item_level": True,
                    "result": [
                        {
                            "commission": 90000,
                            "commission_date": MAR_15_2024,
                            "id": 1,
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "commission": 90000,
                            "commission_date": MAR_15_2024,
                            "id": 2,
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "commission": 90000,
                            "commission_date": MAR_15_2024,
                            "id": 5,
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "commission": 90000,
                            "commission_date": MAR_15_2024,
                            "id": 6,
                            "source_payee": "<EMAIL>",
                        },
                    ],
                    "summary": {},
                    "team_owner_detail": TEAM_OWNER_DETAIL,
                },
            ),
            (
                "2024-03-01",
                "2024-03-31",
                "2024-03-01",
                "<EMAIL>",
                [1, 2, 5, 6],
                GUP_PLAN,
                GUP_SIMPLE_NON_LINE_ITEM_TEAM,
                [
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": MAR_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 20000.0,
                        "payee_variable_pay": 10000.0,
                    },
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": FEB_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 50000.0,
                        "payee_variable_pay": 40000.0,
                    },
                ],
                {
                    "context": {
                        1: LINE_ITEM_CONTEXTS[1],
                        2: LINE_ITEM_CONTEXTS[2],
                        5: LINE_ITEM_CONTEXTS[5],
                        6: LINE_ITEM_CONTEXTS[6],
                    },
                    "do_nothing_records": [],
                    "is_line_item_level": False,
                    "result": [
                        {
                            "commission": 90000,
                            "commission_date": MAR_31_2024_EOD,
                            "id": None,
                            "source_payee": "<EMAIL>",
                        },
                    ],
                    "summary": {},
                    "team_owner_detail": TEAM_OWNER_DETAIL,
                },
            ),
            (
                "2024-03-01",
                "2024-03-31",
                "2024-03-01",
                "<EMAIL>",
                [1, 2, 5, 6],
                GUP_PLAN,
                GUP_CONDITIONAL_TEAM,
                [
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": MAR_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 20000.0,
                        "payee_variable_pay": 10000.0,
                    },
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": FEB_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 50000.0,
                        "payee_variable_pay": 40000.0,
                    },
                ],
                {
                    "context": {
                        1: LINE_ITEM_CONTEXTS[1],
                        2: LINE_ITEM_CONTEXTS[2],
                        5: LINE_ITEM_CONTEXTS[5],
                        6: LINE_ITEM_CONTEXTS[6],
                    },
                    "do_nothing_records": [],
                    "is_line_item_level": True,
                    "result": [
                        {
                            "commission": 0,
                            "commission_date": MAR_15_2024,
                            "id": 1,
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "commission": 0,
                            "commission_date": MAR_15_2024,
                            "id": 2,
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "commission": 40000,
                            "commission_date": MAR_15_2024,
                            "id": 5,
                            "source_payee": "<EMAIL>",
                        },
                        {
                            "commission": 40000,
                            "commission_date": MAR_15_2024,
                            "id": 6,
                            "source_payee": "<EMAIL>",
                        },
                    ],
                    "summary": {},
                    "team_owner_detail": TEAM_OWNER_DETAIL,
                },
            ),
            (
                "2024-03-01",
                "2024-03-31",
                "2024-03-01",
                "<EMAIL>",
                [1, 2, 5, 6],
                GUP_PLAN,
                GUP_TIER_TEAM,
                [
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": MAR_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 20000.0,
                        "payee_variable_pay": 10000.0,
                    },
                    {
                        "client_id": 6897,
                        "employee_email_id": "<EMAIL>",
                        "effective_start_date": FEB_1_2024,
                        "effective_end_date": MAX_DATE,
                        "fixed_pay": 50000.0,
                        "payee_variable_pay": 40000.0,
                    },
                ],
                {
                    "context": {
                        1: LINE_ITEM_CONTEXTS[1],
                        2: LINE_ITEM_CONTEXTS[2],
                        5: LINE_ITEM_CONTEXTS[5],
                        6: LINE_ITEM_CONTEXTS[6],
                    },
                    "do_nothing_records": [],
                    "is_line_item_level": True,
                    "result": [
                        {
                            "commission": 30000,
                            "commission_date": MAR_15_2024,
                            "source_payee": "<EMAIL>",
                            "id": 1,
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "tier_value": 10000,
                        },
                        {
                            "commission": 30000,
                            "commission_date": MAR_15_2024,
                            "source_payee": "<EMAIL>",
                            "id": 2,
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "tier_value": 10000,
                        },
                        {
                            "commission": 40000,
                            "commission_date": MAR_15_2024,
                            "source_payee": "<EMAIL>",
                            "id": 5,
                            "original_tier_id": 0,
                            "original_tier_name": "Tier 0",
                            "tier_id": 0,
                            "tier_name": "Tier 0",
                            "tier_value": 20000,
                        },
                        {
                            "commission": 60000,
                            "commission_date": MAR_15_2024,
                            "source_payee": "<EMAIL>",
                            "id": 5,
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "tier_value": 20000,
                        },
                        {
                            "commission": 120000,
                            "commission_date": MAR_15_2024,
                            "source_payee": "<EMAIL>",
                            "id": 6,
                            "original_tier_id": 1,
                            "original_tier_name": "Tier 1",
                            "tier_id": 1,
                            "tier_name": "Tier 1",
                            "tier_value": 40000,
                        },
                    ],
                    "summary": {},
                    "team_owner_detail": TEAM_OWNER_DETAIL,
                },
            ),
        ],
    )
    @pytest.mark.parametrize(
        "evaluation_mode", [EvaluationMode.SERIAL, EvaluationMode.VECTORIZED]
    )
    @mock.patch(
        "commission_engine.services.commission_calculation_service.user_properties.fetch_user_properties_dataframe"
    )
    @mock.patch("spm.services.stormbreaker.stormbreaker.StormBreakerDS.fetch")
    @mock.patch(
        "everstage_ddd.stormbreaker.base.StormBreakerDS.fetch_datasheet_data_as_of_date"
    )
    def test_get_user_property_evaluation(
        self,
        mock_fetch,
        mock_fetch_datasheet_data_as_of_date,
        mock_compute_user_properties_df,
        evaluation_mode,
        period_start_date,
        period_end_date,
        secondary_kd,
        payee_email,
        row_keys,
        plan_id,
        criteria_id,
        mock_user_property_values,
        expected_result,
    ):
        original_eval_mode = get_evaluation_mode(CLIENT_ID)
        set_client_feature(CLIENT_ID, "evaluation_mode", evaluation_mode.value)

        data_frame = json_datasheet_data.get_datasheet_df(row_keys, secondary_kd)
        mock_fetch.return_value = data_frame
        mock_fetch_datasheet_data_as_of_date.return_value = data_frame
        mock_compute_user_properties_df.return_value = pd.DataFrame.from_records(
            mock_user_property_values
        )

        result = get_evaluate_result(
            CLIENT_ID,
            period_start_date,
            period_end_date,
            payee_email,
            plan_id,
            criteria_id,
            secondary_kd,
        )
        result["result"] = sort_results_by_id(result["result"])
        assert result == expected_result

        set_client_feature(CLIENT_ID, "evaluation_mode", original_eval_mode)
