from unittest.mock import patch
from uuid import UUID

import pytest

from commission_engine.services.hris_integration_services.hris_integration_services import (
    get_config_columns,
    get_ever_fields_to_display_name_datatype_map,
    get_hris_data,
    get_usersheet_variables,
    validate_variables_used_in_hris,
)


@pytest.mark.django_db
@pytest.mark.commission_engine
class TestHRISIntegrationService:
    @pytest.mark.parametrize(
        "logged_in_user_email, expected_data",
        [
            (
                "<EMAIL>",
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "variables": [
                        {
                            "internal_field_name": "employee_email_id",
                            "display_name": "Employee Email Id",
                            "system_name": "ts_employee_email_id",
                            "datatype_id": 12,
                            "data_type": "Email",
                            "field_context": "employee",
                        },
                        {
                            "internal_field_name": "effective_start_date",
                            "display_name": "Effective Start Date",
                            "system_name": "ts_effective_start_date",
                            "datatype_id": 2,
                            "data_type": "Date",
                            "field_context": "employee",
                        },
                        {
                            "internal_field_name": "effective_end_date",
                            "display_name": "Effective End Date",
                            "system_name": "ts_effective_end_date",
                            "datatype_id": 2,
                            "data_type": "Date",
                            "field_context": "employee",
                        },
                        {
                            "internal_field_name": "first_name",
                            "display_name": "First Name",
                            "system_name": "ts_1_co_96_first_name",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee",
                        },
                        {
                            "internal_field_name": "last_name",
                            "display_name": "Last Name",
                            "system_name": "ts_1_co_96_last_name",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee",
                        },
                        {
                            "internal_field_name": "user_role",
                            "display_name": "User Role",
                            "system_name": "cf_userrole",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee",
                        },
                        {
                            "internal_field_name": "employee_id",
                            "display_name": "Employee Id",
                            "system_name": "ts_2_co_97_employee_id",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "joining_date",
                            "display_name": "Joining Date",
                            "system_name": "ts_2_co_97_joining_date",
                            "datatype_id": 2,
                            "data_type": "Date",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "designation",
                            "display_name": "Designation",
                            "system_name": "ts_2_co_97_designation",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "employment_country",
                            "display_name": "Employment Country",
                            "system_name": "ts_2_co_97_employment_country",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "fixed_pay",
                            "display_name": "Base Pay",
                            "system_name": "ts_2_co_97_base_pay",
                            "datatype_id": 1,
                            "data_type": "Integer",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "variable_pay",
                            "display_name": "Variable Pay",
                            "system_name": "ts_2_co_97_variable_pay",
                            "datatype_id": 1,
                            "data_type": "Integer",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "pay_currency",
                            "display_name": "Pay Currency",
                            "system_name": "ts_2_co_97_pay_currency",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "payout_frequency",
                            "display_name": "Payout Frequency",
                            "system_name": "ts_2_co_97_payout_frequency",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "payee_role",
                            "display_name": "Crystal Access",
                            "system_name": "ts_2_co_97_crystal_access",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "reporting_manager_email_id",
                            "display_name": "Manager Email",
                            "system_name": "ts_3_co_98_manager_email",
                            "datatype_id": 12,
                            "data_type": "Email",
                            "field_context": "hierarchy",
                        },
                        {
                            "internal_field_name": "cf_10005_eff_fx_rate",
                            "display_name": "Eff FX Rate",
                            "system_name": "ts_2_co_97_eff_fx_rate",
                            "datatype_id": 1,
                            "data_type": "Integer",
                            "field_context": "custom_fields",
                        },
                        {
                            "internal_field_name": "cf_10005_eff_cf_is_terminated",
                            "display_name": "EFF CF is terminated",
                            "system_name": "ts_2_co_97_effc_fisterminated",
                            "datatype_id": 3,
                            "data_type": "Boolean",
                            "field_context": "custom_fields",
                        },
                    ],
                },
            ),
            (
                "<EMAIL>",
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "variables": [
                        {
                            "internal_field_name": "employee_email_id",
                            "display_name": "Employee Email Id",
                            "system_name": "ts_employee_email_id",
                            "datatype_id": 12,
                            "data_type": "Email",
                            "field_context": "employee",
                        },
                        {
                            "internal_field_name": "effective_start_date",
                            "display_name": "Effective Start Date",
                            "system_name": "ts_effective_start_date",
                            "datatype_id": 2,
                            "data_type": "Date",
                            "field_context": "employee",
                        },
                        {
                            "internal_field_name": "effective_end_date",
                            "display_name": "Effective End Date",
                            "system_name": "ts_effective_end_date",
                            "datatype_id": 2,
                            "data_type": "Date",
                            "field_context": "employee",
                        },
                        {
                            "internal_field_name": "first_name",
                            "display_name": "First Name",
                            "system_name": "ts_1_co_96_first_name",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee",
                        },
                        {
                            "internal_field_name": "last_name",
                            "display_name": "Last Name",
                            "system_name": "ts_1_co_96_last_name",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee",
                        },
                        {
                            "internal_field_name": "user_role",
                            "display_name": "User Role",
                            "system_name": "cf_userrole",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee",
                        },
                        {
                            "internal_field_name": "employee_id",
                            "display_name": "Employee Id",
                            "system_name": "ts_2_co_97_employee_id",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "joining_date",
                            "display_name": "Joining Date",
                            "system_name": "ts_2_co_97_joining_date",
                            "datatype_id": 2,
                            "data_type": "Date",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "designation",
                            "display_name": "Designation",
                            "system_name": "ts_2_co_97_designation",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "employment_country",
                            "display_name": "Employment Country",
                            "system_name": "ts_2_co_97_employment_country",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "fixed_pay",
                            "display_name": "Base Pay",
                            "system_name": "ts_2_co_97_base_pay",
                            "datatype_id": 1,
                            "data_type": "Integer",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "variable_pay",
                            "display_name": "Variable Pay",
                            "system_name": "ts_2_co_97_variable_pay",
                            "datatype_id": 1,
                            "data_type": "Integer",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "pay_currency",
                            "display_name": "Pay Currency",
                            "system_name": "ts_2_co_97_pay_currency",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "payout_frequency",
                            "display_name": "Payout Frequency",
                            "system_name": "ts_2_co_97_payout_frequency",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "payee_role",
                            "display_name": "Crystal Access",
                            "system_name": "ts_2_co_97_crystal_access",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "employee_payroll",
                        },
                        {
                            "internal_field_name": "reporting_manager_email_id",
                            "display_name": "Manager Email",
                            "system_name": "ts_3_co_98_manager_email",
                            "datatype_id": 12,
                            "data_type": "Email",
                            "field_context": "hierarchy",
                        },
                        {
                            "internal_field_name": "cf_10005_customfield_1",
                            "display_name": "Non Eff CF Region",
                            "system_name": "ts_1_co_96_non_eff_cf_region",
                            "datatype_id": 4,
                            "data_type": "String",
                            "field_context": "custom_fields",
                        },
                        {
                            "internal_field_name": "cf_10005_customfield_2",
                            "display_name": "Non Eff CF PINCODE",
                            "system_name": "ts_1_co_96_non_eff_cfpincode",
                            "datatype_id": 1,
                            "data_type": "Integer",
                            "field_context": "custom_fields",
                        },
                        {
                            "internal_field_name": "cf_10005_eff_fx_rate",
                            "display_name": "Eff FX Rate",
                            "system_name": "ts_2_co_97_eff_fx_rate",
                            "datatype_id": 1,
                            "data_type": "Integer",
                            "field_context": "custom_fields",
                        },
                        {
                            "internal_field_name": "cf_10005_eff_cf_is_terminated",
                            "display_name": "EFF CF is terminated",
                            "system_name": "ts_2_co_97_effc_fisterminated",
                            "datatype_id": 3,
                            "data_type": "Boolean",
                            "field_context": "custom_fields",
                        },
                    ],
                },
            ),
        ],
    )
    @patch(
        "spm.services.user_group_service.UserGroupMemberService.add_to_user_to_user_group_map_cache"
    )
    def test_get_usersheet_variables(
        self, mock_cache, logged_in_user_email, expected_data
    ):
        mock_cache.return_value = None
        actual_data = get_usersheet_variables(
            client_id=10005, logged_in_user_email=logged_in_user_email
        )
        assert actual_data == expected_data

    @pytest.mark.parametrize(
        "selected_vars, datasheet_id, expected_data",
        [
            (
                [],
                "834dd433-b1af-494a-8a3d-14d077064ea2",
                {"is_valid": True, "errors": []},
            ),
            (
                [
                    "ts_employee_email_id",
                    "ts_effective_start_date",
                    "ts_effective_end_date",
                    "ts_1_co_96_first_name",
                    "ts_1_co_96_last_name",
                    "ts_2_co_97_designation",
                    "ts_2_co_97_employment_country",
                    "ts_2_co_97_base_pay",
                    "ts_2_co_97_variable_pay",
                    "ts_2_co_97_payout_frequency",
                    "ts_2_co_97_crystal_access",
                    "ts_2_co_97_pay_currency",
                    "ts_2_co_97_joining_date",
                    "ts_2_co_97_employee_id",
                    "ts_3_co_98_manager_email",
                    "cf_userrole",
                    "ts_1_co_96_non_eff_cf_region",
                    "ts_1_co_96_non_eff_cfpincode",
                    "ts_2_co_97_eff_fx_rate",
                    "ts_2_co_97_effc_fisterminated",
                ],
                "ada5d5d3-5fcc-43c5-a520-f3af5ee77aff",
                {"is_valid": True, "errors": []},
            ),
            (
                [
                    "ts_employee_email_id",
                    "ts_effective_start_date",
                    "ts_effective_end_date",
                    "ts_1_co_96_first_name",
                    "ts_1_co_96_last_name",
                    "ts_2_co_97_designation",
                    "ts_2_co_97_employment_country",
                    "ts_2_co_97_base_pay",
                    "ts_2_co_97_variable_pay",
                    "ts_2_co_97_payout_frequency",
                    "ts_2_co_97_crystal_access",
                    "ts_2_co_97_pay_currency",
                    "ts_2_co_97_joining_date",
                    "ts_2_co_97_employee_id",
                    "ts_3_co_98_manager_email",
                    "cf_userrole",
                    "ts_1_co_96_non_eff_cf_region",
                    "ts_1_co_96_non_eff_cfpincode",
                    "ts_2_co_97_eff_fx_rate",
                ],
                "ada5d5d3-5fcc-43c5-a520-f3af5ee77aff",
                {
                    "is_valid": False,
                    "errors": "Columns Payroll Object::EFF CF is terminated are used in HRIS integration, please select to continue.",
                },
            ),
        ],
    )
    def test_validate_variables_used_in_hris(
        self, selected_vars, datasheet_id, expected_data
    ):
        actual_data = validate_variables_used_in_hris(
            client_id=10005,
            selected_vars=selected_vars,
            datasheet_id=datasheet_id,
        )
        if datasheet_id == "ada5d5d3-5fcc-43c5-a520-f3af5ee77aff":
            assert actual_data == expected_data
        else:
            assert actual_data["is_valid"] == expected_data["is_valid"]

    def test_get_config_columns(self):
        actual_data = get_config_columns(client_id=10005)
        expected_data = {
            "config_columns": [
                {
                    "display_name": "Employee Email Id",
                    "user_field": "employee_email_id",
                    "field_context": "employee",
                    "is_required": True,
                    "datatype_id": 12,
                    "data_type": "Email",
                },
                {
                    "display_name": "Effective Start Date",
                    "user_field": "effective_start_date",
                    "field_context": "employee",
                    "is_required": True,
                    "datatype_id": 2,
                    "data_type": "Date",
                },
                {
                    "display_name": "Effective End Date",
                    "user_field": "effective_end_date",
                    "field_context": "employee",
                    "is_required": True,
                    "datatype_id": 2,
                    "data_type": "Date",
                },
                {
                    "display_name": "First Name",
                    "user_field": "first_name",
                    "field_context": "employee",
                    "is_required": True,
                    "datatype_id": 4,
                    "data_type": "String",
                },
                {
                    "display_name": "Last Name",
                    "user_field": "last_name",
                    "field_context": "employee",
                    "is_required": True,
                    "datatype_id": 4,
                    "data_type": "String",
                },
                {
                    "display_name": "User Role",
                    "user_field": "user_role",
                    "field_context": "employee",
                    "is_required": True,
                    "datatype_id": 4,
                    "data_type": "String",
                },
                {
                    "display_name": "Deactivation/Exit date",
                    "user_field": "deactivation_date",
                    "field_context": "employee",
                    "is_required": False,
                    "datatype_id": 2,
                    "data_type": "Date",
                },
                {
                    "display_name": "Employee Id",
                    "user_field": "employee_id",
                    "field_context": "employee_payroll",
                    "is_required": False,
                    "datatype_id": 4,
                    "data_type": "String",
                },
                {
                    "display_name": "Joining Date",
                    "user_field": "joining_date",
                    "field_context": "employee_payroll",
                    "is_required": True,
                    "datatype_id": 2,
                    "data_type": "Date",
                },
                {
                    "display_name": "Designation",
                    "user_field": "designation",
                    "field_context": "employee_payroll",
                    "is_required": False,
                    "datatype_id": 4,
                    "data_type": "String",
                },
                {
                    "display_name": "Employment Country",
                    "user_field": "employment_country",
                    "field_context": "employee_payroll",
                    "is_required": True,
                    "datatype_id": 4,
                    "data_type": "String",
                },
                {
                    "display_name": "Base Pay",
                    "user_field": "fixed_pay",
                    "field_context": "employee_payroll",
                    "is_required": False,
                    "datatype_id": 1,
                    "data_type": "Integer",
                },
                {
                    "display_name": "Variable Pay",
                    "user_field": "variable_pay",
                    "field_context": "employee_payroll",
                    "is_required": False,
                    "datatype_id": 1,
                    "data_type": "Integer",
                },
                {
                    "display_name": "Pay Currency",
                    "user_field": "pay_currency",
                    "field_context": "employee_payroll",
                    "is_required": True,
                    "datatype_id": 4,
                    "data_type": "String",
                },
                {
                    "display_name": "Payout Frequency",
                    "user_field": "payout_frequency",
                    "field_context": "employee_payroll",
                    "is_required": True,
                    "datatype_id": 4,
                    "data_type": "String",
                },
                {
                    "display_name": "Crystal Access",
                    "user_field": "payee_role",
                    "field_context": "employee_payroll",
                    "is_required": True,
                    "datatype_id": 4,
                    "data_type": "String",
                },
                {
                    "display_name": "Manager Email",
                    "user_field": "reporting_manager_email_id",
                    "field_context": "hierarchy",
                    "is_required": True,
                    "datatype_id": 12,
                    "data_type": "Email",
                },
                {
                    "display_name": "Non Eff CF Region",
                    "user_field": "cf_10005_customfield_1",
                    "field_context": "custom_fields",
                    "is_required": True,
                    "datatype_id": 4,
                    "data_type": "String",
                },
                {
                    "display_name": "Non Eff CF PINCODE",
                    "user_field": "cf_10005_customfield_2",
                    "field_context": "custom_fields",
                    "is_required": False,
                    "datatype_id": 1,
                    "data_type": "Integer",
                },
                {
                    "display_name": "Eff FX Rate",
                    "user_field": "cf_10005_eff_fx_rate",
                    "field_context": "custom_fields",
                    "is_required": False,
                    "datatype_id": 1,
                    "data_type": "Integer",
                },
                {
                    "display_name": "EFF CF is terminated",
                    "user_field": "cf_10005_eff_cf_is_terminated",
                    "field_context": "custom_fields",
                    "is_required": False,
                    "datatype_id": 3,
                    "data_type": "Boolean",
                },
            ],
            "section_data": [
                {
                    "section_name": "employee",
                    "display_name": "Employee Details",
                    "required": True,
                },
                {
                    "section_name": "employee_payroll",
                    "display_name": "Payroll Details",
                    "required": False,
                },
                {
                    "section_name": "hierarchy",
                    "display_name": "Reporting Hierarchy",
                    "required": False,
                },
                {
                    "section_name": "custom_fields",
                    "display_name": "Custom Fields",
                    "required": False,
                },
            ],
        }

        assert actual_data == expected_data

    def test_get_hris_data(self):
        actual_data = get_hris_data(client_id=10005)
        expected_data = {
            "active_hris_connections": [
                {
                    "connection_name": "Bamboo HR dev test",
                    "service_name": "sql",
                    "access_token_config_id": 3000,
                    "is_fivetran_connection": False,
                }
            ],
            "hris_config": [
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "employee_email_id",
                    "source_field": "ts_employee_email_id",
                    "field_context": "employee",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "effective_start_date",
                    "source_field": "ts_effective_start_date",
                    "field_context": "employee",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "effective_end_date",
                    "source_field": "ts_effective_end_date",
                    "field_context": "employee",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "first_name",
                    "source_field": "ts_1_co_96_first_name",
                    "field_context": "employee",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "last_name",
                    "source_field": "ts_1_co_96_last_name",
                    "field_context": "employee",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "user_role",
                    "source_field": "cf_userrole",
                    "field_context": "employee",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "employee_id",
                    "source_field": "ts_2_co_97_employee_id",
                    "field_context": "employee_payroll",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "joining_date",
                    "source_field": "ts_2_co_97_joining_date",
                    "field_context": "employee_payroll",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "designation",
                    "source_field": "ts_2_co_97_designation",
                    "field_context": "employee_payroll",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "employment_country",
                    "source_field": "ts_2_co_97_employment_country",
                    "field_context": "employee_payroll",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "fixed_pay",
                    "source_field": "ts_2_co_97_base_pay",
                    "field_context": "employee_payroll",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "variable_pay",
                    "source_field": "ts_2_co_97_variable_pay",
                    "field_context": "employee_payroll",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "pay_currency",
                    "source_field": "ts_2_co_97_pay_currency",
                    "field_context": "employee_payroll",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "payout_frequency",
                    "source_field": "ts_2_co_97_payout_frequency",
                    "field_context": "employee_payroll",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "payee_role",
                    "source_field": "ts_2_co_97_crystal_access",
                    "field_context": "employee_payroll",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "reporting_manager_email_id",
                    "source_field": "ts_3_co_98_manager_email",
                    "field_context": "hierarchy",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "cf_10005_customfield_1",
                    "source_field": "ts_1_co_96_non_eff_cf_region",
                    "field_context": "custom_fields",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "cf_10005_customfield_2",
                    "source_field": "ts_1_co_96_non_eff_cfpincode",
                    "field_context": "custom_fields",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "cf_10005_eff_fx_rate",
                    "source_field": "ts_2_co_97_eff_fx_rate",
                    "field_context": "custom_fields",
                },
                {
                    "datasheet_id": UUID("ada5d5d3-5fcc-43c5-a520-f3af5ee77aff"),
                    "databook_id": UUID("41774933-bd83-41b1-8c98-b1fd3aaaad87"),
                    "user_field": "cf_10005_eff_cf_is_terminated",
                    "source_field": "ts_2_co_97_effc_fisterminated",
                    "field_context": "custom_fields",
                },
            ],
            "data_review_type": "manual-review",
            "connected_objects": [],
            "is_sync_running": False,
        }

        assert actual_data == expected_data

    def test_get_ever_fields_to_display_name_datatype_map(self):
        actual_data = get_ever_fields_to_display_name_datatype_map(client_id=10005)
        expected_data = {
            "employee_email_id": {
                "display_name": "Employee Email Id",
                "datatype_id": 12,
                "data_type": "Email",
            },
            "effective_start_date": {
                "display_name": "Effective Start Date",
                "datatype_id": 2,
                "data_type": "Date",
            },
            "effective_end_date": {
                "display_name": "Effective End Date",
                "datatype_id": 2,
                "data_type": "Date",
            },
            "first_name": {
                "display_name": "First Name",
                "datatype_id": 4,
                "data_type": "String",
            },
            "last_name": {
                "display_name": "Last Name",
                "datatype_id": 4,
                "data_type": "String",
            },
            "user_role": {
                "display_name": "User Role",
                "datatype_id": 4,
                "data_type": "String",
            },
            "deactivation_date": {
                "display_name": "Deactivation/Exit date",
                "datatype_id": 2,
                "data_type": "Date",
            },
            "employee_id": {
                "display_name": "Employee Id",
                "datatype_id": 4,
                "data_type": "String",
            },
            "joining_date": {
                "display_name": "Joining Date",
                "datatype_id": 2,
                "data_type": "Date",
            },
            "designation": {
                "display_name": "Designation",
                "datatype_id": 4,
                "data_type": "String",
            },
            "employment_country": {
                "display_name": "Employment Country",
                "datatype_id": 4,
                "data_type": "String",
            },
            "fixed_pay": {
                "display_name": "Base Pay",
                "datatype_id": 1,
                "data_type": "Integer",
            },
            "variable_pay": {
                "display_name": "Variable Pay",
                "datatype_id": 1,
                "data_type": "Integer",
            },
            "pay_currency": {
                "display_name": "Pay Currency",
                "datatype_id": 4,
                "data_type": "String",
            },
            "payout_frequency": {
                "display_name": "Payout Frequency",
                "datatype_id": 4,
                "data_type": "String",
            },
            "payee_role": {
                "display_name": "Crystal Access",
                "datatype_id": 4,
                "data_type": "String",
            },
            "reporting_manager_email_id": {
                "display_name": "Manager Email",
                "datatype_id": 12,
                "data_type": "Email",
            },
            "cf_10005_customfield_1": {
                "display_name": "Non Eff CF Region",
                "datatype_id": 4,
                "data_type": "String",
            },
            "cf_10005_customfield_2": {
                "display_name": "Non Eff CF PINCODE",
                "datatype_id": 1,
                "data_type": "Integer",
            },
            "cf_10005_eff_fx_rate": {
                "display_name": "Eff FX Rate",
                "datatype_id": 1,
                "data_type": "Integer",
            },
            "cf_10005_eff_cf_is_terminated": {
                "display_name": "EFF CF is terminated",
                "datatype_id": 3,
                "data_type": "Boolean",
            },
        }
        assert actual_data == expected_data
