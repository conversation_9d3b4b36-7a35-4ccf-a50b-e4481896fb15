from typing import Dict, List
from unittest.mock import patch

import pytest

from commission_engine.accessors.client_accessor import (
    CRYSTAL_CUSTOM_PERIODS_DEFAULT,
    can_only_show_cron_marked_as_failed,
    create_client,
    enable_only_show_cron_marked_as_failed,
    get_client,
    get_run_settlement_report_value,
    get_tsar_enabled_clients,
    is_settlement_v3_enabled,
    is_split_summation_to_li_enabled,
    is_tsar_enabled_for_client,
    update_client,
)
from commission_engine.models import Client
from commission_engine.tests.models import create_client as create_test_client
from commission_engine.utils.report_enums import RunSettlementReport


@pytest.mark.django_db
@pytest.mark.commission_engine
class TestClientAccessor:
    @pytest.mark.parametrize(
        "client_id, client_features, expected_result",
        [
            # split_summation_to_li is enabled
            (9987810, {"split_summation_to_li": True}, True),
            # split_summation_to_li is disabled
            (9987810, {"split_summation_to_li": False}, False),
            # split_summation_to_li is not defined
            (9987810, {}, False),
        ],
    )
    def test_is_split_summation_to_li_enabled(
        self, client_id, client_features, expected_result
    ):
        Client.objects.create(client_id=client_id, client_features=client_features)
        assert is_split_summation_to_li_enabled(client_id) is expected_result

    @patch(
        "everstage_ddd.global_search.meta_data_extractor.ever_objects_meta_data.upsert_ever_obj_meta_data_in_vector_db"
    )
    def test_create_client(self, mock_upsert_ever_obj_meta_data_in_vector_db):
        mock_upsert_ever_obj_meta_data_in_vector_db.return_value = None
        params = {
            "client_id": 10000017,
            "logo_url": "https://www.adj.com/wp-content/uploads/2019/07/adj-logo-1.png",
            "name": "Adj Approvals - 10000017",
            "hubspot_company_id": "hub10000017",
            "domain": "adj-approvals-10000017.com",
            "file": "null",
            "statement_logo": "null",
            "connection_type": "email-password",
            "subscription_plan": "BASIC",
            "connection_name": "email-password",
            "base_currency": "USD",
            "fiscal_start_month": "January",
            "timezone": "UTC",
            "client_notification": "false",
            "payee_notification": "false",
            "show_commission_percent": "false",
            "show_commission_buddy": "true",
            "show_territory_plan": "false",
            "upstream_etl": "true",
            "show_data_sources_v2": "false",
            "commission_etl": "true",
            "hide_categories": "",
            "show_superset_dashboard": "false",
            "show_return_v1_button": "true",
            "status": "false",
            "show_payout_table_breakdown": "false",
            "delete_approvers": "false",
            "type": "false",
            "show_salesforce_integration": "false",
            "show_statements_v2": "true",
            "enable_ever_comparison": "true",
            "manager_rollup_ed": "false",
            "show_approval_feature": "true",
            "expose_comm_reports_in_plan": "false",
            "show_roles": "true",
            "show_datasheet_permission": "false",
            "show_custom_object_permission": "false",
            "is_new_frozen_payroll_etl": "true",
            "show_simulation_v2": "false",
            "enable_concurrent_sessions": "true",
            "datasheet_v2": "false",
            "show_datasheet_v2": "false",
            "datasheet_builder": "false",
            "allow_adjustments_to_frozen_commission": "false",
            "split_summation_to_li": "false",
            "show_metrics": "false",
            "avoid_iframe_in_contracts": "false",
            "use_multi_engine_stormbreaker": "true",
            "enable_hris_integration": "false",
            "object_knowledge_date_query_strategy": "postgres",
            "show_get_user_property_commission": "false",
            "help_doc_user_role": "",
            "show_chatgpt": "false",
            "isolated_snowflake_database": "false",
            "show_advanced_filter": "true",
            "chrome_extension_enabled": "false",
            "plan_summary_model": "gpt-4-1106-preview",
            "evaluation_mode": "serial",
            "show_statements_pdf": "true",
            "email_invite_template_id": "undefined",
            "documentation_url": "",
            "profile_picture_permission": "ALL",
            "warn_on_unlock": "NONE",
            "upstream_etl_version": "v1",
            "custom_calendar": "false",
            "commission_plan_version": "v2",
            "show_forecast": "false",
            "quota_effective_dated": "false",
            "allow_quota_settings_override": "false",
            "allow_annual_quota_effective_dated": "false",
            "expressionbox_version": "v2",
            "take_ds_snapshot": "false",
            "snapshot_data_for_statements": "false",
            "databook_expressionbox_version": "v2",
            "calc_fields_strategy": "udf",
            "calc_fields_lambda_chunk_size": 100000,
            "crm_hyperlinks": "false",
            "enable_custom_workflows": "false",
            "logo_s3_path": None,
            "statement_logo_s3_path": None,
            "updated_by": "<EMAIL>",
            "created_by": "<EMAIL>",
            "settlement_v2": "false",
            "edit_locked_quota": "false",
            "crystal_custom_calendar_future_periods": str(
                CRYSTAL_CUSTOM_PERIODS_DEFAULT
            ),
            "enable_support_user_access": "true",
            "is_secure_admin_ui_auth0_user_mgmt": "false",
            "enable_rounding_in_tier_functions": "false",
            "enable_everai": "false",
            "run_settlement_report": "always",
            "is_report_object_sharded": "true",
            "enable_custom_theme": "false",
            "enable_sidebar_v3": "false",
            "use_aggrid_for_pdf_export": "true",
            "insert_meta_data_to_vec_db": "false",
            "show_g2_review_form": "Off",
            "edit_locked_quota": "false",
            "is_auto_enrich_report": "false",
            "async_export_datasheet": "false",
            "allow_only_admins_to_modify_user_name": "false",
            "modules": "ICM",
            "run_sync_for_multiple_period": "false",
            "enable_tsar_webapp_custom_roles": "false",
            "upload_excel_files_in_custom_object": "false",
        }
        client_created = create_client(params)
        assert client_created.client_features.get("take_ds_snapshot") == True
        assert client_created.client_features.get("settlement_v2") == False
        assert (
            client_created.client_features.get("use_multi_engine_stormbreaker") == True
        )
        assert client_created.client_features.get("enable_support_user_access") == True
        assert (
            client_created.client_features.get("is_secure_admin_ui_auth0_user_mgmt")
            == False
        )
        assert client_created.crm_company_id == "hub10000017"

    @pytest.mark.django_db
    def test_update_client(self):
        params = {
            "client_id": 10000017,
            "logo_url": "https://www.adj.com/wp-content/uploads/2019/07/adj-logo-1.png",
            "name": "Adj Approvals - 10000017",
            "hubspot_company_id": "hub10000017",
            "domain": "adj-approvals-10000017.com",
            "file": "null",
            "statement_logo": "null",
            "connection_type": "email-password",
            "subscription_plan": "BASIC",
            "connection_name": "email-password",
            "base_currency": "USD",
            "fiscal_start_month": "January",
            "timezone": "UTC",
            "show_payout_table_breakdown": "false",
            "client_notification": "false",
            "payee_notification": "false",
            "show_commission_percent": "false",
            "show_commission_buddy": "true",
            "show_territory_plan": "false",
            "upstream_etl": "true",
            "show_data_sources_v2": "false",
            "commission_etl": "true",
            "hide_categories": "",
            "show_superset_dashboard": "false",
            "show_return_v1_button": "true",
            "status": "false",
            "delete_approvers": "false",
            "type": "false",
            "show_salesforce_integration": "false",
            "show_statements_v2": "true",
            "enable_ever_comparison": "true",
            "manager_rollup_ed": "false",
            "show_approval_feature": "true",
            "expose_comm_reports_in_plan": "false",
            "show_roles": "true",
            "show_datasheet_permission": "false",
            "show_custom_object_permission": "false",
            "is_new_frozen_payroll_etl": "true",
            "show_simulation_v2": "false",
            "enable_concurrent_sessions": "true",
            "datasheet_v2": "true",
            "show_datasheet_v2": "false",
            "datasheet_builder": "false",
            "allow_adjustments_to_frozen_commission": "false",
            "split_summation_to_li": "false",
            "show_metrics": "false",
            "avoid_iframe_in_contracts": "false",
            "use_multi_engine_stormbreaker": "true",
            "enable_hris_integration": "false",
            "object_knowledge_date_query_strategy": "postgres",
            "show_get_user_property_commission": "false",
            "help_doc_user_role": "",
            "show_chatgpt": "false",
            "isolated_snowflake_database": "false",
            "show_advanced_filter": "true",
            "chrome_extension_enabled": "false",
            "plan_summary_model": "gpt-4-1106-preview",
            "evaluation_mode": "serial",
            "show_statements_pdf": "true",
            "email_invite_template_id": "undefined",
            "documentation_url": "",
            "profile_picture_permission": "ALL",
            "warn_on_unlock": "NONE",
            "upstream_etl_version": "v1",
            "custom_calendar": "false",
            "commission_plan_version": "v2",
            "show_forecast": "false",
            "quota_effective_dated": "false",
            "allow_quota_settings_override": "false",
            "allow_annual_quota_effective_dated": "false",
            "expressionbox_version": "v2",
            "take_ds_snapshot": "false",
            "snapshot_data_for_statements": "false",
            "databook_expressionbox_version": "v2",
            "calc_fields_strategy": "udf",
            "calc_fields_lambda_chunk_size": 100000,
            "crm_hyperlinks": "false",
            "enable_custom_workflows": "false",
            "logo_s3_path": None,
            "statement_logo_s3_path": None,
            "updated_by": "<EMAIL>",
            "created_by": "<EMAIL>",
            "settlement_v2": "true",
            "crystal_custom_calendar_future_periods": str(
                CRYSTAL_CUSTOM_PERIODS_DEFAULT
            ),
            "enable_support_user_access": "true",
            "is_secure_admin_ui_auth0_user_mgmt": "false",
            "enable_rounding_in_tier_functions": "false",
            "enable_everai": "false",
            "run_settlement_report": "always",
            "use_aggrid_for_pdf_export": "true",
            "insert_meta_data_to_vec_db": "false",
            "show_g2_review_form": "Off",
            "enable_custom_theme": "false",
            "enable_sidebar_v3": "false",
            "edit_locked_quota": "false",
            "is_auto_enrich_report": "false",
            "async_export_datasheet": "false",
            "allow_only_admins_to_modify_user_name": "false",
            "modules": "ICM",
            "run_sync_for_multiple_period": "false",
            "enable_tsar_webapp_custom_roles": "false",
            "upload_excel_files_in_custom_object": "false",
        }
        params["client_id"] = 10033
        params["use_multi_engine_stormbreaker"] = "false"
        client_created_id = update_client(params)
        client_created = get_client(client_created_id)
        assert client_created.client_features.get("take_ds_snapshot") == True
        assert client_created.client_features.get("settlement_v2") == True
        assert (
            client_created.client_features.get("use_multi_engine_stormbreaker") == False
        )
        assert client_created.client_features.get("enable_support_user_access") == True
        assert (
            client_created.client_features.get("is_secure_admin_ui_auth0_user_mgmt")
            == False
        )
        assert client_created.crm_company_id == "hub10000017"

    def test_get_tsar_enabled_clients(self):
        # prepare data
        create_test_client(
            name="Blackrock",
            domain="blackrock.com",
            client_id=9871401,
            client_features={"enable_support_user_access": True},
            meta_info={
                "super_admin_user": {"email": "<EMAIL>"},
            },
        )
        create_test_client(
            name="Sonata",
            domain="sonatasoft.com",
            client_id=9871402,
            client_features={"enable_support_user_access": True},
            meta_info={
                "super_admin_user": {"email": "<EMAIL>"},
            },
        )
        create_test_client(
            name="Samsung",
            domain="samsung.com",
            client_id=9871403,
            client_features={"enable_support_user_access": True},
            meta_info={
                "super_admin_user": {"email": None},
            },
        )
        create_test_client(
            name="HDFC",
            domain="hdfc.com",
            client_id=9871404,
            client_features={"enable_support_user_access": False},
            meta_info={
                "super_admin_user": {"email": "<EMAIL>"},
            },
        )

        # run the test service
        clients: List[Dict] = get_tsar_enabled_clients()
        client_ids: List[int] = [client["client_id"] for client in clients]

        # assert
        assert 9871401 in client_ids
        assert 9871402 in client_ids
        assert 9871403 in client_ids
        assert 9871404 not in client_ids

    def test_is_support_membership_enabled_for_client(self):
        # prepare data
        create_test_client(
            name="Blackrock",
            domain="blackrock.com",
            client_id=9871401,
            client_features={"enable_support_user_access": True},
            meta_info={
                "super_admin_user": {"email": "<EMAIL>"},
            },
        )
        create_test_client(
            name="HDFC",
            domain="hdfc.com",
            client_id=9871404,
            client_features={"enable_support_user_access": False},
            meta_info={
                "super_admin_user": {"email": "<EMAIL>"},
            },
        )
        create_test_client(
            name="Samsung",
            domain="samsung.com",
            client_id=9871405,
            client_features={"enable_support_user_access": True},
            meta_info={
                "super_admin_user": None,
            },
        )

        # assert
        assert is_tsar_enabled_for_client(9871401) == True
        assert is_tsar_enabled_for_client(9871404) == False
        assert is_tsar_enabled_for_client(9871405) == True

    def test_get_run_settlement_report_value(self):
        # Test 1: Key not present in client_features
        assert get_run_settlement_report_value(1) == RunSettlementReport.ALWAYS

        # Test 2: Key present in client_features with value as "always"
        Client.objects.filter(client_id=1).update(
            client_features={"run_settlement_report": "always"}
        )
        assert get_run_settlement_report_value(1) == RunSettlementReport.ALWAYS

        # Test 3: Key present in client_features with value as "if-needed"
        Client.objects.filter(client_id=1).update(
            client_features={"run_settlement_report": "if_needed"}
        )
        assert get_run_settlement_report_value(1) == RunSettlementReport.IF_NEEDED

    def test_enable_only_show_cron_marked_as_failed(self):
        create_test_client(
            name="Blackrock",
            domain="blackrock.com",
            client_id=9871401,
            client_features={"only_show_cron_marked_as_failed": True},
        )
        assert enable_only_show_cron_marked_as_failed(9871401) == True

    def test_can_only_show_cron_marked_as_failed(self):
        create_test_client(
            name="Blackrock",
            domain="blackrock.com",
            client_id=9871401,
            client_features={"only_show_cron_marked_as_failed": True},
        )
        assert can_only_show_cron_marked_as_failed(9871401) == True

        create_test_client(
            name="HDFC",
            domain="hdfc.com",
            client_id=9871404,
            client_features={"only_show_cron_marked_as_failed": False},
        )
        assert can_only_show_cron_marked_as_failed(9871404) == False

    @pytest.mark.parametrize(
        "features, expected_result",
        [
            ({"settlement_v3_execution_mode": "ACTUAL_RUN"}, True),
            ({"settlement_v3_execution_mode": "DISABLED"}, False),
            ({"settlement_v3_execution_mode": "DRY_RUN"}, False),
            ({}, False),
        ],
    )
    def test_is_settlement_v3_enabled(self, monkeypatch, features, expected_result):
        monkeypatch.setattr(
            "commission_engine.accessors.client_accessor.get_client_features",
            lambda x: features,
        )
        assert is_settlement_v3_enabled(1) == expected_result
