import json

get_std_objects_response = b'{"userId":49345701,"hubId":23855630,"appId":1394775,"scopes":["oauth","integration-sync","tickets","crm.lists.read","crm.objects.contacts.read","crm.objects.marketing_events.read","crm.schemas.custom.read","crm.objects.custom.read","crm.schemas.contacts.read","crm.objects.feedback_submissions.read","crm.objects.deals.read","crm.schemas.deals.read","crm.objects.owners.read","crm.objects.quotes.read","crm.schemas.quotes.read","crm.objects.line_items.read","crm.schemas.line_items.read","crm.export","crm.objects.owners.read"]}'

get_std_objects_return_value = [
    {"label": "Contacts", "name": "contacts"},
    {"label": "Deals", "name": "deals"},
    {"label": "Feedback submissions", "name": "feedback_submissions"},
    {"label": "Tickets", "name": "tickets"},
    {"label": "Calls", "name": "calls"},
    {"label": "Communications", "name": "communications"},
    {"label": "Meetings", "name": "meetings"},
    {"label": "Notes", "name": "notes"},
    {"label": "Postal Mail", "name": "postal_mail"},
    {"label": "Tasks", "name": "tasks"},
    {"label": "Taxes", "name": "taxes"},
    {"label": "Owners", "name": "owners"},
    {"label": "Pipelines", "name": "pipelines"},
    {"label": "Pipeline Stages", "name": "pipeline_stages"},
]

get_custom_objects_response = b'{"results":[{"labels":{"singular":"Test Object","plural":"Test Objects"},"requiredProperties":["primary_field"],"searchableProperties":["secondary_field","primary_field"],"primaryDisplayProperty":"primary_field","secondaryDisplayProperties":["secondary_field"],"archived":false,"restorable":true,"metaType":"PORTAL_SPECIFIC","id":"11766605","fullyQualifiedName":"p23855630_test_objects","createdAt":"2023-01-27T10:57:38.676Z","updatedAt":"2023-01-27T10:57:38.676Z","objectTypeId":"2-11766605","properties":[{"name":"hs_all_accessible_team_ids","label":"All accessible team IDs","type":"enumeration","fieldType":"checkbox","description":"The team IDs, including up the team hierarchy, corresponding to all owner referencing properties for this object, both default and custom","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_all_assigned_business_unit_ids","label":"Business units","type":"enumeration","fieldType":"checkbox","description":"The business units this record is assigned to.","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hs_all_owner_ids","label":"All owner ids","type":"enumeration","fieldType":"select","description":"The value of all owner referencing properties for this object, both default and custom.","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_all_team_ids","label":"All team ids","type":"enumeration","fieldType":"select","description":"The team ids corresponding to all owner referencing properties for this object, both default and custom.","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_created_by_user_id","label":"Created by user ID","type":"number","fieldType":"number","description":"The user that created this object. This value is automatically set by HubSpot and may not be modified.","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_createdate","label":"Object create date/time","type":"datetime","fieldType":"date","description":"The date and time at which this object was created. This value is automatically set by HubSpot and may not be modified.","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_lastmodifieddate","label":"Object last modified date/time","type":"datetime","fieldType":"date","description":"Most recent timestamp of any property update for this object. This includes HubSpot internal properties, which can be visible or hidden. This property is updated automatically.","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_merged_object_ids","label":"Merged object IDs","type":"enumeration","fieldType":"checkbox","description":"The list of object IDs that have been merged into this object. This value is automatically set by HubSpot and may not be modified.","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_object_id","label":"Record ID","type":"number","fieldType":"number","description":"The unique ID for this record. This value is automatically set by HubSpot and may not be modified.","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-27T10:57:38.976Z","createdAt":"2023-01-27T10:57:38.976Z","name":"hs_pinned_engagement_id","label":"Pinned Engagement ID","type":"number","fieldType":"number","description":"The object ID of the current pinned engagement. This value is automatically set by HubSpot and may not be modified.","groupName":"test_objects_information","options":[],"createdUserId":"49345701","updatedUserId":"49345701","displayOrder":-1,"calculated":false,"externalOptions":false,"archived":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_read_only","label":"Read Only Object","type":"bool","fieldType":"booleancheckbox","description":"Is the object read only","groupName":"test_objects_information","options":[{"label":"True","value":"true","displayOrder":0,"hidden":false},{"label":"False","value":"false","displayOrder":1,"hidden":false}],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_unique_creation_key","label":"Unique creation key","type":"string","fieldType":"text","description":"Unique property used for idempotent creates","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":true,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_updated_by_user_id","label":"Updated by user ID","type":"number","fieldType":"number","description":"The user that last updated this object. This value is automatically set by HubSpot and may not be modified.","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_user_ids_of_all_notification_followers","label":"User IDs of all notification followers","type":"enumeration","fieldType":"checkbox","description":"The user IDs of all users that have clicked follow within the object to opt-in to getting follow notifications","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_user_ids_of_all_notification_unfollowers","label":"User IDs of all notification unfollowers","type":"enumeration","fieldType":"checkbox","description":"The user IDs of all object owners that have clicked unfollow within the object to opt-out of getting follow notifications","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_user_ids_of_all_owners","label":"User IDs of all owners","type":"enumeration","fieldType":"checkbox","description":"The user IDs of all owners of this object","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hubspot_owner_assigneddate","label":"Owner Assigned Date","type":"datetime","fieldType":"date","description":"The most recent date an owner was assigned to this object. This is set automatically by HubSpot and can be used for segmentation and reporting.","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hubspot_owner_id","label":"Owner","type":"enumeration","fieldType":"select","description":"The owner of the object.","groupName":"test_objects_information","options":[],"referencedObjectType":"OWNER","displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hubspot_team_id","label":"HubSpot Team","type":"enumeration","fieldType":"select","description":"The primary team of the owner.","groupName":"test_objects_information","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-27T10:57:38.849Z","createdAt":"2023-01-27T10:57:38.849Z","name":"primary_field","label":"Primary Field","type":"number","fieldType":"number","description":"","groupName":"test_objects_information","options":[],"createdUserId":"49345701","updatedUserId":"49345701","displayOrder":0,"calculated":false,"externalOptions":false,"archived":false,"hasUniqueValue":true,"hidden":false,"showCurrencySymbol":false,"modificationMetadata":{"archivable":true,"readOnlyDefinition":false,"readOnlyValue":false},"formField":true},{"updatedAt":"2023-01-27T10:57:38.849Z","createdAt":"2023-01-27T10:57:38.849Z","name":"secondary_field","label":"Secondary Field","type":"string","fieldType":"text","description":"","groupName":"test_objects_information","options":[],"createdUserId":"49345701","updatedUserId":"49345701","displayOrder":0,"calculated":false,"externalOptions":false,"archived":false,"hasUniqueValue":false,"hidden":false,"showCurrencySymbol":false,"modificationMetadata":{"archivable":true,"readOnlyDefinition":false,"readOnlyValue":false},"formField":false}],"associations":[{"fromObjectTypeId":"2-11766605","toObjectTypeId":"0-18","name":"test_object_to_communication","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"1","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-18","toObjectTypeId":"2-11766605","name":"test_object_to_communication","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"2","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"2-11766605","toObjectTypeId":"0-116","name":"test_object_to_postal_mail","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"7","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-116","toObjectTypeId":"2-11766605","name":"test_object_to_postal_mail","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"8","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"2-11766605","toObjectTypeId":"0-27","name":"test_object_to_task","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"9","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-27","toObjectTypeId":"2-11766605","name":"test_object_to_task","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"10","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"2-11766605","toObjectTypeId":"0-47","name":"test_object_to_meeting","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"13","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-47","toObjectTypeId":"2-11766605","name":"test_object_to_meeting","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"14","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"2-11766605","toObjectTypeId":"0-46","name":"test_object_to_note","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"3","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-46","toObjectTypeId":"2-11766605","name":"test_object_to_note","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"4","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"2-11766605","toObjectTypeId":"0-49","name":"test_object_to_email","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"5","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-49","toObjectTypeId":"2-11766605","name":"test_object_to_email","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"6","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"2-11766605","toObjectTypeId":"0-48","name":"test_object_to_call","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"11","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-48","toObjectTypeId":"2-11766605","name":"test_object_to_call","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"12","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"2-11766605","toObjectTypeId":"0-51","name":"test_object_to_conversation_session","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"15","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-51","toObjectTypeId":"2-11766605","name":"test_object_to_conversation_session","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"16","createdAt":null,"updatedAt":null}],"name":"test_objects"}]}'

get_custom_objects_return_value = [
    {"label": "Test Objects", "name": "p23855630_test_objects"},
]

get_all_fields_response = b'{"results":[{"name":"hs_all_assigned_business_unit_ids","label":"Business units","type":"enumeration","fieldType":"checkbox","description":"The business units this record is assigned to.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2023-01-18T17:42:59.950Z","createdAt":"2019-12-12T14:41:16.407Z","name":"hs_at_mentioned_owner_ids","label":"At-Mentioned Owner Ids","type":"enumeration","fieldType":"checkbox","description":"The owners that have been at-mentioned on the engagement","groupName":"engagement","options":[],"referencedObjectType":"OWNER","displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:08:34.062Z","createdAt":"2019-12-12T14:41:15.871Z","name":"hs_attachment_ids","label":"Attached file IDs","type":"enumeration","fieldType":"checkbox","description":"The IDs of the files that are attached to the engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:09:04.635Z","createdAt":"2019-12-12T14:41:15.922Z","name":"hs_body_preview","label":"Body preview","type":"string","fieldType":"text","description":"A truncated preview of the engagegement body","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"extract_most_recent_email_reply_text(string(hs_note_body))"},{"updatedAt":"2022-05-24T16:09:35.155Z","createdAt":"2019-12-12T14:41:16.223Z","name":"hs_body_preview_html","label":"HTML Body Preview","type":"string","fieldType":"text","description":"The HTML representation of the engagement body preview","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"extract_most_recent_email_reply_html(string(hs_note_body))"},{"updatedAt":"2022-05-24T16:10:05.679Z","createdAt":"2019-12-12T14:41:16.104Z","name":"hs_body_preview_is_truncated","label":"Body Preview Truncated","type":"bool","fieldType":"booleancheckbox","description":"Indicates if the engagement body was truncated for the preview","groupName":"engagement","options":[{"label":"True","value":"true","displayOrder":0,"hidden":false},{"label":"False","value":"false","displayOrder":1,"hidden":false}],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"has_email_reply(string(hs_note_body))"},{"updatedAt":"2022-05-24T16:10:36.222Z","createdAt":"2019-12-12T14:41:16.433Z","name":"hs_created_by","label":"Activity created by","type":"number","fieldType":"number","description":"The user who created the engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"hs_created_by_user_id"},{"name":"hs_created_by_user_id","label":"Created by user ID","type":"number","fieldType":"number","description":"The user that created this object. This value is automatically set by HubSpot and may not be modified.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:11:06.782Z","createdAt":"2019-12-12T14:41:16.523Z","name":"hs_createdate","label":"Create date","type":"datetime","fieldType":"date","description":"The date that an engagement was created","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:11:37.111Z","createdAt":"2019-12-12T14:41:16.079Z","name":"hs_engagement_source","label":"Engagement Source","type":"string","fieldType":"text","description":"The source that created this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:12:07.560Z","createdAt":"2019-12-12T14:41:15.973Z","name":"hs_engagement_source_id","label":"Engagement Source ID","type":"string","fieldType":"text","description":"The specific ID of the process that created this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:12:38.013Z","createdAt":"2019-12-12T14:41:16.363Z","name":"hs_follow_up_action","label":"Follow up action","type":"string","fieldType":"text","description":"What action should be performed during follow up","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:13:08.490Z","createdAt":"2019-12-12T14:41:16.548Z","name":"hs_gdpr_deleted","label":"GDPR deleted","type":"bool","fieldType":"booleancheckbox","description":"Indicates the body of this engagement has been cleared because of a GDPR delete request","groupName":"engagement","options":[{"label":"True","value":"true","displayOrder":0,"hidden":false},{"label":"False","value":"false","displayOrder":1,"hidden":false}],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:13:39.012Z","createdAt":"2019-12-12T14:41:16.019Z","name":"hs_lastmodifieddate","label":"Last modified date","type":"datetime","fieldType":"date","description":"The date any property on this engagement was modified","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_merged_object_ids","label":"Merged object IDs","type":"enumeration","fieldType":"checkbox","description":"The list of object IDs that have been merged into this object. This value is automatically set by HubSpot and may not be modified.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:14:09.342Z","createdAt":"2019-12-12T14:41:15.947Z","name":"hs_modified_by","label":"Updated by","type":"number","fieldType":"number","description":"The user who last updated the engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"hs_updated_by_user_id"},{"updatedAt":"2022-08-03T13:59:23.415Z","createdAt":"2019-12-12T14:41:16.136Z","name":"hs_note_body","label":"Note body","type":"string","fieldType":"html","description":"The body of the note","groupName":"note","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hs_object_id","label":"Record ID","type":"number","fieldType":"number","description":"The unique ID for this record. This value is automatically set by HubSpot and may not be modified.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:15:10.890Z","createdAt":"2019-12-12T14:41:16.338Z","name":"hs_product_name","label":"Product name","type":"string","fieldType":"text","description":"The name of the product associated with this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:15:41.693Z","createdAt":"2019-12-12T14:41:16.498Z","name":"hs_queue_membership_ids","label":"Queue Memberships","type":"enumeration","fieldType":"checkbox","description":"The list of queues that contain this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hs_read_only","label":"Read Only Object","type":"bool","fieldType":"booleancheckbox","description":"Is the object read only","groupName":"noteinformation","options":[{"label":"True","value":"true","displayOrder":0,"hidden":false},{"label":"False","value":"false","displayOrder":1,"hidden":false}],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:16:12.210Z","createdAt":"2019-12-12T14:41:16.265Z","name":"hs_timestamp","label":"Activity date","type":"datetime","fieldType":"date","description":"The date that an engagement occurred","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hs_unique_creation_key","label":"Unique creation key","type":"string","fieldType":"text","description":"Unique property used for idempotent creates","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":true,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:16:42.699Z","createdAt":"2019-12-12T14:41:15.847Z","name":"hs_unique_id","label":"Unique ID","type":"string","fieldType":"text","description":"The unique ID of the engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":true,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hs_updated_by_user_id","label":"Updated by user ID","type":"number","fieldType":"number","description":"The user that last updated this object. This value is automatically set by HubSpot and may not be modified.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_user_ids_of_all_notification_followers","label":"User IDs of all notification followers","type":"enumeration","fieldType":"checkbox","description":"The user IDs of all users that have clicked follow within the object to opt-in to getting follow notifications","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_user_ids_of_all_notification_unfollowers","label":"User IDs of all notification unfollowers","type":"enumeration","fieldType":"checkbox","description":"The user IDs of all object owners that have clicked unfollow within the object to opt-out of getting follow notifications","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_user_ids_of_all_owners","label":"User IDs of all owners","type":"enumeration","fieldType":"checkbox","description":"The user IDs of all owners of this object","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.457Z","name":"hubspot_owner_assigneddate","label":"Owner Assigned Date","type":"datetime","fieldType":"date","description":"The timestamp when an owner was assigned to this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.290Z","name":"hubspot_owner_id","label":"Activity assigned to","type":"enumeration","fieldType":"select","description":"The user that the activity is assigned to in HubSpot. This can be any HubSpot user or Salesforce integration user, and can be set manually or via Workflows.","groupName":"engagement","options":[],"referencedObjectType":"OWNER","displayOrder":6,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.315Z","name":"hubspot_team_id","label":"HubSpot Team","type":"enumeration","fieldType":"select","description":"The team of the owner of an engagement.","groupName":"engagement","options":[],"displayOrder":7,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.581Z","name":"hs_all_owner_ids","label":"All owner ids","type":"enumeration","fieldType":"select","description":"The value of all owner referencing properties for this object, both default and custom","groupName":"engagement","options":[],"displayOrder":8,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.182Z","name":"hs_all_team_ids","label":"All team ids","type":"enumeration","fieldType":"select","description":"The team ids corresponding to all owner referencing properties for this object, both default and custom","groupName":"engagement","options":[],"displayOrder":9,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.045Z","name":"hs_all_accessible_team_ids","label":"All accessible team ids","type":"enumeration","fieldType":"select","description":"The team ids, including up the team hierarchy, corresponding to all owner referencing properties for this object, both default and custom","groupName":"engagement","options":[],"displayOrder":10,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false}]}'

get_all_fields_return_value = [
    {
        "label": "Business units",
        "name": "hs_all_assigned_business_unit_ids",
        "type": "enumeration",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "At-Mentioned Owner Ids",
        "name": "hs_at_mentioned_owner_ids",
        "type": "enumeration",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Attached file IDs",
        "name": "hs_attachment_ids",
        "type": "enumeration",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Body preview",
        "name": "hs_body_preview",
        "type": "string",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "HTML Body Preview",
        "name": "hs_body_preview_html",
        "type": "string",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Body Preview Truncated",
        "name": "hs_body_preview_is_truncated",
        "type": "bool",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Activity created by",
        "name": "hs_created_by",
        "type": "number",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Created by user ID",
        "name": "hs_created_by_user_id",
        "type": "number",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Create date",
        "name": "hs_createdate",
        "type": "datetime",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Engagement Source",
        "name": "hs_engagement_source",
        "type": "string",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Engagement Source ID",
        "name": "hs_engagement_source_id",
        "type": "string",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Follow up action",
        "name": "hs_follow_up_action",
        "type": "string",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "GDPR deleted",
        "name": "hs_gdpr_deleted",
        "type": "bool",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Last modified date",
        "name": "hs_lastmodifieddate",
        "type": "datetime",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Merged object IDs",
        "name": "hs_merged_object_ids",
        "type": "enumeration",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Updated by",
        "name": "hs_modified_by",
        "type": "number",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Note body",
        "name": "hs_note_body",
        "type": "string",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Record ID",
        "name": "hs_object_id",
        "type": "number",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Product name",
        "name": "hs_product_name",
        "type": "string",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Queue Memberships",
        "name": "hs_queue_membership_ids",
        "type": "enumeration",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Read Only Object",
        "name": "hs_read_only",
        "type": "bool",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Activity date",
        "name": "hs_timestamp",
        "type": "datetime",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Unique creation key",
        "name": "hs_unique_creation_key",
        "type": "string",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Unique ID",
        "name": "hs_unique_id",
        "type": "string",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Updated by user ID",
        "name": "hs_updated_by_user_id",
        "type": "number",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "User IDs of all notification followers",
        "name": "hs_user_ids_of_all_notification_followers",
        "type": "enumeration",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "User IDs of all notification unfollowers",
        "name": "hs_user_ids_of_all_notification_unfollowers",
        "type": "enumeration",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "User IDs of all owners",
        "name": "hs_user_ids_of_all_owners",
        "type": "enumeration",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Owner Assigned Date",
        "name": "hubspot_owner_assigneddate",
        "type": "datetime",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "Activity assigned to",
        "name": "hubspot_owner_id",
        "type": "enumeration",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "HubSpot Team",
        "name": "hubspot_team_id",
        "type": "enumeration",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "All owner ids",
        "name": "hs_all_owner_ids",
        "type": "enumeration",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "All team ids",
        "name": "hs_all_team_ids",
        "type": "enumeration",
        "is_association": False,
        "is_function": False,
    },
    {
        "label": "All accessible team ids",
        "name": "hs_all_accessible_team_ids",
        "type": "enumeration",
        "is_association": False,
        "is_function": False,
    },
]

get_object_meta_info_response = b'{"labels":{"singular":"Note","plural":"Notes"},"requiredProperties":["hs_timestamp"],"searchableProperties":["hs_note_body"],"primaryDisplayProperty":null,"secondaryDisplayProperties":[],"archived":false,"restorable":false,"metaType":"HUBSPOT","id":"46","fullyQualifiedName":"NOTE","createdAt":"1970-01-01T00:00:00Z","updatedAt":"1970-01-01T00:00:00Z","objectTypeId":"0-46","properties":[{"name":"hs_all_assigned_business_unit_ids","label":"Business units","type":"enumeration","fieldType":"checkbox","description":"The business units this record is assigned to.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2023-01-18T17:42:59.950Z","createdAt":"2019-12-12T14:41:16.407Z","name":"hs_at_mentioned_owner_ids","label":"At-Mentioned Owner Ids","type":"enumeration","fieldType":"checkbox","description":"The owners that have been at-mentioned on the engagement","groupName":"engagement","options":[],"referencedObjectType":"OWNER","displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:08:34.062Z","createdAt":"2019-12-12T14:41:15.871Z","name":"hs_attachment_ids","label":"Attached file IDs","type":"enumeration","fieldType":"checkbox","description":"The IDs of the files that are attached to the engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:09:04.635Z","createdAt":"2019-12-12T14:41:15.922Z","name":"hs_body_preview","label":"Body preview","type":"string","fieldType":"text","description":"A truncated preview of the engagegement body","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"extract_most_recent_email_reply_text(string(hs_note_body))"},{"updatedAt":"2022-05-24T16:09:35.155Z","createdAt":"2019-12-12T14:41:16.223Z","name":"hs_body_preview_html","label":"HTML Body Preview","type":"string","fieldType":"text","description":"The HTML representation of the engagement body preview","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"extract_most_recent_email_reply_html(string(hs_note_body))"},{"updatedAt":"2022-05-24T16:10:05.679Z","createdAt":"2019-12-12T14:41:16.104Z","name":"hs_body_preview_is_truncated","label":"Body Preview Truncated","type":"bool","fieldType":"booleancheckbox","description":"Indicates if the engagement body was truncated for the preview","groupName":"engagement","options":[{"label":"True","value":"true","displayOrder":0,"hidden":false},{"label":"False","value":"false","displayOrder":1,"hidden":false}],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"has_email_reply(string(hs_note_body))"},{"updatedAt":"2022-05-24T16:10:36.222Z","createdAt":"2019-12-12T14:41:16.433Z","name":"hs_created_by","label":"Activity created by","type":"number","fieldType":"number","description":"The user who created the engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"hs_created_by_user_id"},{"name":"hs_created_by_user_id","label":"Created by user ID","type":"number","fieldType":"number","description":"The user that created this object. This value is automatically set by HubSpot and may not be modified.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:11:06.782Z","createdAt":"2019-12-12T14:41:16.523Z","name":"hs_createdate","label":"Create date","type":"datetime","fieldType":"date","description":"The date that an engagement was created","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:11:37.111Z","createdAt":"2019-12-12T14:41:16.079Z","name":"hs_engagement_source","label":"Engagement Source","type":"string","fieldType":"text","description":"The source that created this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:12:07.560Z","createdAt":"2019-12-12T14:41:15.973Z","name":"hs_engagement_source_id","label":"Engagement Source ID","type":"string","fieldType":"text","description":"The specific ID of the process that created this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:12:38.013Z","createdAt":"2019-12-12T14:41:16.363Z","name":"hs_follow_up_action","label":"Follow up action","type":"string","fieldType":"text","description":"What action should be performed during follow up","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:13:08.490Z","createdAt":"2019-12-12T14:41:16.548Z","name":"hs_gdpr_deleted","label":"GDPR deleted","type":"bool","fieldType":"booleancheckbox","description":"Indicates the body of this engagement has been cleared because of a GDPR delete request","groupName":"engagement","options":[{"label":"True","value":"true","displayOrder":0,"hidden":false},{"label":"False","value":"false","displayOrder":1,"hidden":false}],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:13:39.012Z","createdAt":"2019-12-12T14:41:16.019Z","name":"hs_lastmodifieddate","label":"Last modified date","type":"datetime","fieldType":"date","description":"The date any property on this engagement was modified","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_merged_object_ids","label":"Merged object IDs","type":"enumeration","fieldType":"checkbox","description":"The list of object IDs that have been merged into this object. This value is automatically set by HubSpot and may not be modified.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:14:09.342Z","createdAt":"2019-12-12T14:41:15.947Z","name":"hs_modified_by","label":"Updated by","type":"number","fieldType":"number","description":"The user who last updated the engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"hs_updated_by_user_id"},{"updatedAt":"2022-08-03T13:59:23.415Z","createdAt":"2019-12-12T14:41:16.136Z","name":"hs_note_body","label":"Note body","type":"string","fieldType":"html","description":"The body of the note","groupName":"note","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hs_object_id","label":"Record ID","type":"number","fieldType":"number","description":"The unique ID for this record. This value is automatically set by HubSpot and may not be modified.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:15:10.890Z","createdAt":"2019-12-12T14:41:16.338Z","name":"hs_product_name","label":"Product name","type":"string","fieldType":"text","description":"The name of the product associated with this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:15:41.693Z","createdAt":"2019-12-12T14:41:16.498Z","name":"hs_queue_membership_ids","label":"Queue Memberships","type":"enumeration","fieldType":"checkbox","description":"The list of queues that contain this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hs_read_only","label":"Read Only Object","type":"bool","fieldType":"booleancheckbox","description":"Is the object read only","groupName":"noteinformation","options":[{"label":"True","value":"true","displayOrder":0,"hidden":false},{"label":"False","value":"false","displayOrder":1,"hidden":false}],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:16:12.210Z","createdAt":"2019-12-12T14:41:16.265Z","name":"hs_timestamp","label":"Activity date","type":"datetime","fieldType":"date","description":"The date that an engagement occurred","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hs_unique_creation_key","label":"Unique creation key","type":"string","fieldType":"text","description":"Unique property used for idempotent creates","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":true,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:16:42.699Z","createdAt":"2019-12-12T14:41:15.847Z","name":"hs_unique_id","label":"Unique ID","type":"string","fieldType":"text","description":"The unique ID of the engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":true,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hs_updated_by_user_id","label":"Updated by user ID","type":"number","fieldType":"number","description":"The user that last updated this object. This value is automatically set by HubSpot and may not be modified.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_user_ids_of_all_notification_followers","label":"User IDs of all notification followers","type":"enumeration","fieldType":"checkbox","description":"The user IDs of all users that have clicked follow within the object to opt-in to getting follow notifications","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_user_ids_of_all_notification_unfollowers","label":"User IDs of all notification unfollowers","type":"enumeration","fieldType":"checkbox","description":"The user IDs of all object owners that have clicked unfollow within the object to opt-out of getting follow notifications","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_user_ids_of_all_owners","label":"User IDs of all owners","type":"enumeration","fieldType":"checkbox","description":"The user IDs of all owners of this object","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.457Z","name":"hubspot_owner_assigneddate","label":"Owner Assigned Date","type":"datetime","fieldType":"date","description":"The timestamp when an owner was assigned to this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.290Z","name":"hubspot_owner_id","label":"Activity assigned to","type":"enumeration","fieldType":"select","description":"The user that the activity is assigned to in HubSpot. This can be any HubSpot user or Salesforce integration user, and can be set manually or via Workflows.","groupName":"engagement","options":[],"referencedObjectType":"OWNER","displayOrder":6,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.315Z","name":"hubspot_team_id","label":"HubSpot Team","type":"enumeration","fieldType":"select","description":"The team of the owner of an engagement.","groupName":"engagement","options":[],"displayOrder":7,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.581Z","name":"hs_all_owner_ids","label":"All owner ids","type":"enumeration","fieldType":"select","description":"The value of all owner referencing properties for this object, both default and custom","groupName":"engagement","options":[],"displayOrder":8,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.182Z","name":"hs_all_team_ids","label":"All team ids","type":"enumeration","fieldType":"select","description":"The team ids corresponding to all owner referencing properties for this object, both default and custom","groupName":"engagement","options":[],"displayOrder":9,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.045Z","name":"hs_all_accessible_team_ids","label":"All accessible team ids","type":"enumeration","fieldType":"select","description":"The team ids, including up the team hierarchy, corresponding to all owner referencing properties for this object, both default and custom","groupName":"engagement","options":[],"displayOrder":10,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false}],"associations":[{"fromObjectTypeId":"0-46","toObjectTypeId":"0-5","name":"NOTE_TO_TICKET","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"228","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-5","toObjectTypeId":"0-46","name":"TICKET_TO_NOTE","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"227","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-46","toObjectTypeId":"2-11766605","name":"test_object_to_note","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"4","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"2-11766605","toObjectTypeId":"0-46","name":"test_object_to_note","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"3","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-46","toObjectTypeId":"0-3","name":"NOTE_TO_DEAL","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"214","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-3","toObjectTypeId":"0-46","name":"DEAL_TO_NOTE","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"213","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-46","toObjectTypeId":"0-1","name":"NOTE_TO_CONTACT","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"202","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-1","toObjectTypeId":"0-46","name":"CONTACT_TO_NOTE","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"201","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-46","toObjectTypeId":"0-2","name":"NOTE_TO_COMPANY","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"190","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-2","toObjectTypeId":"0-46","name":"COMPANY_TO_NOTE","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"189","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-46","toObjectTypeId":"0-54","name":"NOTE_TO_MARKETING_EVENT","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"260","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-54","toObjectTypeId":"0-46","name":"MARKETING_EVENT_TO_NOTE","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"259","createdAt":null,"updatedAt":null}],"name":"NOTE"}'

get_object_meta_info_return_value = {"label": "Notes", "name": "notes"}

get_singular_name_response = b'{"labels":{"singular":"Note","plural":"Notes"},"requiredProperties":["hs_timestamp"],"searchableProperties":["hs_note_body"],"primaryDisplayProperty":null,"secondaryDisplayProperties":[],"archived":false,"restorable":false,"metaType":"HUBSPOT","id":"46","fullyQualifiedName":"NOTE","createdAt":"1970-01-01T00:00:00Z","updatedAt":"1970-01-01T00:00:00Z","objectTypeId":"0-46","properties":[{"name":"hs_all_assigned_business_unit_ids","label":"Business units","type":"enumeration","fieldType":"checkbox","description":"The business units this record is assigned to.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2023-01-18T17:42:59.950Z","createdAt":"2019-12-12T14:41:16.407Z","name":"hs_at_mentioned_owner_ids","label":"At-Mentioned Owner Ids","type":"enumeration","fieldType":"checkbox","description":"The owners that have been at-mentioned on the engagement","groupName":"engagement","options":[],"referencedObjectType":"OWNER","displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:08:34.062Z","createdAt":"2019-12-12T14:41:15.871Z","name":"hs_attachment_ids","label":"Attached file IDs","type":"enumeration","fieldType":"checkbox","description":"The IDs of the files that are attached to the engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:09:04.635Z","createdAt":"2019-12-12T14:41:15.922Z","name":"hs_body_preview","label":"Body preview","type":"string","fieldType":"text","description":"A truncated preview of the engagegement body","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"extract_most_recent_email_reply_text(string(hs_note_body))"},{"updatedAt":"2022-05-24T16:09:35.155Z","createdAt":"2019-12-12T14:41:16.223Z","name":"hs_body_preview_html","label":"HTML Body Preview","type":"string","fieldType":"text","description":"The HTML representation of the engagement body preview","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"extract_most_recent_email_reply_html(string(hs_note_body))"},{"updatedAt":"2022-05-24T16:10:05.679Z","createdAt":"2019-12-12T14:41:16.104Z","name":"hs_body_preview_is_truncated","label":"Body Preview Truncated","type":"bool","fieldType":"booleancheckbox","description":"Indicates if the engagement body was truncated for the preview","groupName":"engagement","options":[{"label":"True","value":"true","displayOrder":0,"hidden":false},{"label":"False","value":"false","displayOrder":1,"hidden":false}],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"has_email_reply(string(hs_note_body))"},{"updatedAt":"2022-05-24T16:10:36.222Z","createdAt":"2019-12-12T14:41:16.433Z","name":"hs_created_by","label":"Activity created by","type":"number","fieldType":"number","description":"The user who created the engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"hs_created_by_user_id"},{"name":"hs_created_by_user_id","label":"Created by user ID","type":"number","fieldType":"number","description":"The user that created this object. This value is automatically set by HubSpot and may not be modified.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:11:06.782Z","createdAt":"2019-12-12T14:41:16.523Z","name":"hs_createdate","label":"Create date","type":"datetime","fieldType":"date","description":"The date that an engagement was created","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:11:37.111Z","createdAt":"2019-12-12T14:41:16.079Z","name":"hs_engagement_source","label":"Engagement Source","type":"string","fieldType":"text","description":"The source that created this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:12:07.560Z","createdAt":"2019-12-12T14:41:15.973Z","name":"hs_engagement_source_id","label":"Engagement Source ID","type":"string","fieldType":"text","description":"The specific ID of the process that created this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:12:38.013Z","createdAt":"2019-12-12T14:41:16.363Z","name":"hs_follow_up_action","label":"Follow up action","type":"string","fieldType":"text","description":"What action should be performed during follow up","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:13:08.490Z","createdAt":"2019-12-12T14:41:16.548Z","name":"hs_gdpr_deleted","label":"GDPR deleted","type":"bool","fieldType":"booleancheckbox","description":"Indicates the body of this engagement has been cleared because of a GDPR delete request","groupName":"engagement","options":[{"label":"True","value":"true","displayOrder":0,"hidden":false},{"label":"False","value":"false","displayOrder":1,"hidden":false}],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:13:39.012Z","createdAt":"2019-12-12T14:41:16.019Z","name":"hs_lastmodifieddate","label":"Last modified date","type":"datetime","fieldType":"date","description":"The date any property on this engagement was modified","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_merged_object_ids","label":"Merged object IDs","type":"enumeration","fieldType":"checkbox","description":"The list of object IDs that have been merged into this object. This value is automatically set by HubSpot and may not be modified.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:14:09.342Z","createdAt":"2019-12-12T14:41:15.947Z","name":"hs_modified_by","label":"Updated by","type":"number","fieldType":"number","description":"The user who last updated the engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false,"calculationFormula":"hs_updated_by_user_id"},{"updatedAt":"2022-08-03T13:59:23.415Z","createdAt":"2019-12-12T14:41:16.136Z","name":"hs_note_body","label":"Note body","type":"string","fieldType":"html","description":"The body of the note","groupName":"note","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hs_object_id","label":"Record ID","type":"number","fieldType":"number","description":"The unique ID for this record. This value is automatically set by HubSpot and may not be modified.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:15:10.890Z","createdAt":"2019-12-12T14:41:16.338Z","name":"hs_product_name","label":"Product name","type":"string","fieldType":"text","description":"The name of the product associated with this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2022-05-24T16:15:41.693Z","createdAt":"2019-12-12T14:41:16.498Z","name":"hs_queue_membership_ids","label":"Queue Memberships","type":"enumeration","fieldType":"checkbox","description":"The list of queues that contain this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hs_read_only","label":"Read Only Object","type":"bool","fieldType":"booleancheckbox","description":"Is the object read only","groupName":"noteinformation","options":[{"label":"True","value":"true","displayOrder":0,"hidden":false},{"label":"False","value":"false","displayOrder":1,"hidden":false}],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:16:12.210Z","createdAt":"2019-12-12T14:41:16.265Z","name":"hs_timestamp","label":"Activity date","type":"datetime","fieldType":"date","description":"The date that an engagement occurred","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hs_unique_creation_key","label":"Unique creation key","type":"string","fieldType":"text","description":"Unique property used for idempotent creates","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":true,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2022-05-24T16:16:42.699Z","createdAt":"2019-12-12T14:41:15.847Z","name":"hs_unique_id","label":"Unique ID","type":"string","fieldType":"text","description":"The unique ID of the engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":true,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"name":"hs_updated_by_user_id","label":"Updated by user ID","type":"number","fieldType":"number","description":"The user that last updated this object. This value is automatically set by HubSpot and may not be modified.","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_user_ids_of_all_notification_followers","label":"User IDs of all notification followers","type":"enumeration","fieldType":"checkbox","description":"The user IDs of all users that have clicked follow within the object to opt-in to getting follow notifications","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_user_ids_of_all_notification_unfollowers","label":"User IDs of all notification unfollowers","type":"enumeration","fieldType":"checkbox","description":"The user IDs of all object owners that have clicked unfollow within the object to opt-out of getting follow notifications","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"name":"hs_user_ids_of_all_owners","label":"User IDs of all owners","type":"enumeration","fieldType":"checkbox","description":"The user IDs of all owners of this object","groupName":"noteinformation","options":[],"displayOrder":-1,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.457Z","name":"hubspot_owner_assigneddate","label":"Owner Assigned Date","type":"datetime","fieldType":"date","description":"The timestamp when an owner was assigned to this engagement","groupName":"engagement","options":[],"displayOrder":-1,"calculated":false,"externalOptions":false,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.290Z","name":"hubspot_owner_id","label":"Activity assigned to","type":"enumeration","fieldType":"select","description":"The user that the activity is assigned to in HubSpot. This can be any HubSpot user or Salesforce integration user, and can be set manually or via Workflows.","groupName":"engagement","options":[],"referencedObjectType":"OWNER","displayOrder":6,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":false},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.315Z","name":"hubspot_team_id","label":"HubSpot Team","type":"enumeration","fieldType":"select","description":"The team of the owner of an engagement.","groupName":"engagement","options":[],"displayOrder":7,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":false,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.581Z","name":"hs_all_owner_ids","label":"All owner ids","type":"enumeration","fieldType":"select","description":"The value of all owner referencing properties for this object, both default and custom","groupName":"engagement","options":[],"displayOrder":8,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.182Z","name":"hs_all_team_ids","label":"All team ids","type":"enumeration","fieldType":"select","description":"The team ids corresponding to all owner referencing properties for this object, both default and custom","groupName":"engagement","options":[],"displayOrder":9,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false},{"updatedAt":"2023-01-10T23:43:41.247Z","createdAt":"2019-12-12T14:41:16.045Z","name":"hs_all_accessible_team_ids","label":"All accessible team ids","type":"enumeration","fieldType":"select","description":"The team ids, including up the team hierarchy, corresponding to all owner referencing properties for this object, both default and custom","groupName":"engagement","options":[],"displayOrder":10,"calculated":false,"externalOptions":true,"hasUniqueValue":false,"hidden":true,"hubspotDefined":true,"modificationMetadata":{"archivable":true,"readOnlyDefinition":true,"readOnlyValue":true},"formField":false}],"associations":[{"fromObjectTypeId":"0-46","toObjectTypeId":"0-5","name":"NOTE_TO_TICKET","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"228","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-5","toObjectTypeId":"0-46","name":"TICKET_TO_NOTE","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"227","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-46","toObjectTypeId":"2-11766605","name":"test_object_to_note","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"4","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"2-11766605","toObjectTypeId":"0-46","name":"test_object_to_note","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"3","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-46","toObjectTypeId":"0-3","name":"NOTE_TO_DEAL","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"214","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-3","toObjectTypeId":"0-46","name":"DEAL_TO_NOTE","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"213","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-46","toObjectTypeId":"0-1","name":"NOTE_TO_CONTACT","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"202","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-1","toObjectTypeId":"0-46","name":"CONTACT_TO_NOTE","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"201","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-46","toObjectTypeId":"0-2","name":"NOTE_TO_COMPANY","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"190","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-2","toObjectTypeId":"0-46","name":"COMPANY_TO_NOTE","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"189","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-46","toObjectTypeId":"0-54","name":"NOTE_TO_MARKETING_EVENT","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"260","createdAt":null,"updatedAt":null},{"fromObjectTypeId":"0-54","toObjectTypeId":"0-46","name":"MARKETING_EVENT_TO_NOTE","cardinality":"ONE_TO_MANY","inverseCardinality":"ONE_TO_MANY","id":"259","createdAt":null,"updatedAt":null}],"name":"NOTE"}'

get_singular_name_return_value = "Note"

get_validate_pipeline_response_200 = b'{"results":[{"label":"Sales Pipeline","displayOrder":0,"id":"default","stages":[{"label":"Appointment Scheduled","displayOrder":0,"metadata":{"isClosed":"false","probability":"0.2"},"id":"appointmentscheduled","createdAt":"1970-01-01T00:00:00Z","updatedAt":"1970-01-01T00:00:00Z","archived":false,"writePermissions":"CRM_PERMISSIONS_ENFORCEMENT"},{"label":"Qualified To Buy","displayOrder":1,"metadata":{"isClosed":"false","probability":"0.4"},"id":"qualifiedtobuy","createdAt":"1970-01-01T00:00:00Z","updatedAt":"1970-01-01T00:00:00Z","archived":false,"writePermissions":"CRM_PERMISSIONS_ENFORCEMENT"},{"label":"Presentation Scheduled","displayOrder":2,"metadata":{"isClosed":"false","probability":"0.6"},"id":"presentationscheduled","createdAt":"1970-01-01T00:00:00Z","updatedAt":"1970-01-01T00:00:00Z","archived":false,"writePermissions":"CRM_PERMISSIONS_ENFORCEMENT"},{"label":"Decision Maker Bought-In","displayOrder":3,"metadata":{"isClosed":"false","probability":"0.8"},"id":"decisionmakerboughtin","createdAt":"1970-01-01T00:00:00Z","updatedAt":"1970-01-01T00:00:00Z","archived":false,"writePermissions":"CRM_PERMISSIONS_ENFORCEMENT"},{"label":"Contract Sent","displayOrder":4,"metadata":{"isClosed":"false","probability":"0.9"},"id":"contractsent","createdAt":"1970-01-01T00:00:00Z","updatedAt":"1970-01-01T00:00:00Z","archived":false,"writePermissions":"CRM_PERMISSIONS_ENFORCEMENT"},{"label":"Closed Won","displayOrder":5,"metadata":{"isClosed":"true","probability":"1.0"},"id":"closedwon","createdAt":"1970-01-01T00:00:00Z","updatedAt":"1970-01-01T00:00:00Z","archived":false,"writePermissions":"CRM_PERMISSIONS_ENFORCEMENT"},{"label":"Closed Lost","displayOrder":6,"metadata":{"isClosed":"true","probability":"0.0"},"id":"closedlost","createdAt":"1970-01-01T00:00:00Z","updatedAt":"1970-01-01T00:00:00Z","archived":false,"writePermissions":"CRM_PERMISSIONS_ENFORCEMENT"}],"createdAt":"1970-01-01T00:00:00Z","updatedAt":"1970-01-01T00:00:00Z","archived":false}]}'

get_validate_pipeline_return_value_200 = {"message": "Success"}

get_validate_pipeline_response_400 = b'{"status":"error","message":"Unable to infer object type from: test","correlationId":"46119567-bf4f-4b21-a836-ad672a1fdfe2"}'

get_validate_pipeline_return_value_400 = {
    "message": json.loads(get_validate_pipeline_response_400.decode("utf-8"))["message"]
}

get_validate_pipeline_response_500 = b""

get_validate_pipeline_return_value_500 = {"message": "Unknown error"}

get_validate_association_response_200 = b'{"results":[{"id":"1","properties":{"createdate":"2023-01-24T12:08:05.764Z","email":"<EMAIL>","firstname":"Maria","hs_object_id":"1","lastmodifieddate":"2023-02-14T09:31:55.575Z","lastname":"Johnson (Sample Contact)"},"createdAt":"2023-01-24T12:08:05.764Z","updatedAt":"2023-02-14T09:31:55.575Z","archived":false,"associations":{"companies":{"results":[{"id":"11104709760","type":"contact_to_company"},{"id":"11104709760","type":"contact_to_company_unlabeled"}]}}},{"id":"51","properties":{"createdate":"2023-01-24T12:08:06.209Z","email":"<EMAIL>","firstname":"Brian","hs_object_id":"51","lastmodifieddate":"2023-02-14T09:27:30.387Z","lastname":"Halligan (Sample Contact)"},"createdAt":"2023-01-24T12:08:06.209Z","updatedAt":"2023-02-14T09:27:30.387Z","archived":false,"associations":{"companies":{"results":[{"id":"11104709760","type":"contact_to_company"},{"id":"11104709760","type":"contact_to_company_unlabeled"}]}}}]}'

get_validate_association_return_value_200 = {
    "message": "Success",
    "data": {
        "name": "company_object.associations.companies.results[0].id",
        "label": "Company ID",
        "type": "number",
        "object_name": "companies",
    },
}

get_validate_association_response_200_NA = b'{"results":[]}'

get_validate_association_return_value_200_NA = {"message": "No associations found"}

get_validate_association_response_400 = b'{"status":"error","message":"Unable to infer object type from: test","correlationId":"d1ed454a-7287-4719-bf95-074a954ed06a"}'

get_validate_association_return_value_400 = {
    "message": json.loads(get_validate_association_response_400.decode("utf-8"))[
        "message"
    ]
}

get_validate_association_response_500 = b""

get_validate_association_return_value_500 = {"message": "Unknown error"}
