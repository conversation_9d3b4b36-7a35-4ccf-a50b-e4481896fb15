from unittest.mock import MagicMock, patch

import pytest

from everstage_admin_backend.services.customer_id_management_service import (
    update_customer_id_mapping,
)


class TestUpdateCustomerIdMapping:
    """
    Test class for the update_customer_id_mapping function.
    """

    @pytest.mark.parametrize(
        "hubspot_company_id, client_id, customer_id",
        [
            ("hub123456", 1, "e000123"),
            ("hub789012", 2, "e000456"),
        ],
    )
    @patch(
        "everstage_admin_backend.services.customer_id_management_service.CustomerIdManagementAccessor"
    )
    @patch("everstage_admin_backend.services.customer_id_management_service.logger")
    def test_with_valid_hubspot_company_id(
        self, mock_logger, mock_accessor, hubspot_company_id, client_id, customer_id
    ):
        """
        Test the case where the HubSpot company ID is valid and does not exist in the database.
        """
        mock_accessor_instance = MagicMock()
        mock_accessor.return_value = mock_accessor_instance
        mock_accessor_instance.is_company_id_exists.return_value = False
        mock_accessor_instance.get_customer_id.return_value = customer_id

        update_customer_id_mapping(hubspot_company_id, client_id)

        mock_accessor.assert_called_once_with(hubspot_company_id)
        mock_accessor_instance.is_company_id_exists.assert_called_once()
        mock_accessor_instance.create_new_mapping.assert_called_once()
        mock_accessor_instance.get_customer_id.assert_called_once()
        mock_logger.info.assert_called_once_with(
            f"Customer ID for client {client_id} is {customer_id}"
        )

    @pytest.mark.parametrize(
        "hubspot_company_id, client_id, customer_id",
        [
            ("hub123456", 1, "e000123"),
            ("hub789012", 2, "e000456"),
        ],
    )
    @patch(
        "everstage_admin_backend.services.customer_id_management_service.CustomerIdManagementAccessor"
    )
    @patch("everstage_admin_backend.services.customer_id_management_service.logger")
    def test_with_existing_hubspot_company_id(
        self, mock_logger, mock_accessor, hubspot_company_id, client_id, customer_id
    ):
        """
        Test the case where the HubSpot company ID already exists in the database.
        """
        mock_accessor_instance = MagicMock()
        mock_accessor.return_value = mock_accessor_instance
        mock_accessor_instance.is_company_id_exists.return_value = True
        mock_accessor_instance.get_customer_id.return_value = customer_id

        update_customer_id_mapping(hubspot_company_id, client_id)

        mock_accessor.assert_called_once_with(hubspot_company_id)
        mock_accessor_instance.is_company_id_exists.assert_called_once()
        mock_accessor_instance.create_new_mapping.assert_not_called()
        mock_accessor_instance.get_customer_id.assert_called_once()
        mock_logger.info.assert_called_once_with(
            f"Customer ID for client {client_id} is {customer_id}"
        )

    @patch("everstage_admin_backend.services.customer_id_management_service.logger")
    def test_with_none_hubspot_company_id(self, mock_logger):
        """
        Test the case where the HubSpot company ID is None.
        """
        hubspot_company_id = None
        client_id = 3

        update_customer_id_mapping(hubspot_company_id, client_id)

        mock_logger.info.assert_called_once_with("Customer ID for client 3 is None")
