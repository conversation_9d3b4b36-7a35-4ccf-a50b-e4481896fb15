from everstage_admin_backend.models import CustomerIdHubspot


class CustomerIdManagementAccessor:
    def __init__(self, hubspot_company_id: str):
        """
        Initialize the CustomerIdManagementAccessor with a HubSpot company ID.

        :param hubspot_company_id: The HubSpot company ID to manage.
        """
        self.hubspot_company_id = hubspot_company_id

    def hubspot_company_id_aware(self):
        """
        This method is used to get the CustomerIdHubspot mapping
        filtered by the given Hubspot Company ID.
        :return: A queryset of CustomerIdHubspot objects.
        """
        return CustomerIdHubspot.objects.filter(
            hubspot_company_id=self.hubspot_company_id
        )

    def get_all_customer_ids(self) -> list[str]:
        """
        Get all customer IDs.

        :return: A list of customer IDs.
        """
        all_mappings = CustomerIdHubspot.objects.all()
        return [mapping.customer_id for mapping in all_mappings]

    def get_max_customer_id(self) -> int:
        """
        Get the maximum customer ID from the database.

        :return: The maximum customer ID as an integer.
        """
        customer_ids = self.get_all_customer_ids()
        numeric_ids = [
            int(customer_id[1:])
            for customer_id in customer_ids
            if customer_id[1:].isdigit()
        ]
        return max(numeric_ids) if numeric_ids else 0

    def is_company_id_exists(self) -> bool:
        """
        Check if the given HubSpot company ID exists in the database.

        :return: True if the company ID exists, False otherwise.
        """
        return self.hubspot_company_id_aware().exists()

    def create_new_mapping(self):
        """
        Create a new mapping for the given HubSpot company ID with a generated customer ID.
        The customer ID is generated sequentially in the format 'eXXXXXX'.
        (e.g., 'e000001', 'e000002', etc.)
        """
        numeric_id = self.get_max_customer_id() + 1
        customer_id = f"e{numeric_id:06d}"

        CustomerIdHubspot.objects.create(
            customer_id=customer_id, hubspot_company_id=self.hubspot_company_id
        )

    def get_customer_id(self) -> str | None:
        """
        Get the customer ID for the given HubSpot company ID.

        :return: The customer ID associated with the given HubSpot company ID.
        """
        try:
            customer_id = self.hubspot_company_id_aware().get().customer_id
        except CustomerIdHubspot.DoesNotExist:
            return None
        else:
            return customer_id
