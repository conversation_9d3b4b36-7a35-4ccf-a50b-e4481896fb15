import logging
import traceback
from ast import literal_eval

import commission_engine.accessors.client_accessor as ca
from commission_engine.services.payee_notification_service import (
    create_commission_notification_task_entry_for_client,
    disconnect_slack,
    get_cron_tab_schedule,
    remove_comm_notification_task_entry_for_client,
)
from commission_engine.services.periodic_sync_services.hard_delete_sync_status_service import (
    add_new_hard_delete_sync,
    delete_hard_delete_sync,
    get_hard_delete_sync_status_for_client,
    update_hard_delete_sync_status,
)
from commission_engine.utils.general_data import APPROVALS_AUTO_APPROVE_TASKS_TIME
from commission_engine.utils.general_data import (
    APPROVALS_PENDING_REQUESTS_NOTIFY_TIME as notification_task_time,
)
from commission_engine.utils.general_data import (
    Freq,
    Notification,
    NotificationMode,
    Task,
)
from everstage_ddd.tqm.services.territory_plan_services import setup_sigma_for_client
from interstage_project.utils import LogWithContext, log_me
from ms_teams_everstage.services.tenant_services import disconnect_msteams_for_client
from spm.accessors.email_template_accessor import EmailTemplateDetailsAccessor
from spm.accessors.notification_accessors import ClientNotificationAccessor
from spm.accessors.quota_acessors import EmployeeQuotaAccessor
from spm.services.approval_workflow_services.approval_cron_services import (
    create_approval_task_entry_for_client,
    remove_approval_task_entry_for_client,
)
from spm.services.user_group_service import update_user_group_sync_task_entry_for_client

logger = logging.getLogger(__name__)


def name_validation(name):
    is_name_exist = False
    res = ca.get_clients_by_name(name)
    if res:
        is_name_exist = True
    return is_name_exist


def disable_slack_integration(client_id, user_email):
    if ca.is_slack_connected(client_id):
        logger.info("Slack disconnected from admin-ui")
        disconnect_slack(client_id=client_id, email_id=user_email)


def disable_ms_teams_integration(client_id, user_email):
    if ca.is_msteams_connected(client_id):
        logger.info("MS Teams disconnected from admin-ui")
        disconnect_msteams_for_client(client_id=client_id, mail_id=user_email)


def update_approval_cron_task(client, params):
    logger = LogWithContext()  # pylint: disable=redefined-outer-name
    logger.info("BEGIN: Approval cron task update client {} {}.".format(client, params))
    client_id = client.client_id
    existing_time_zone = client.time_zone
    new_time_zone = params["timezone"]
    auto_approve_task_name = Task.APPROVALS_AUTO_APPROVE_TASK.value["name"]
    auto_approve_function = Task.APPROVALS_AUTO_APPROVE_TASK.value["function"]
    pending_requests_notify_task_name = (
        Task.APPROVALS_PENDING_REQUESTS_EMAIL_NOTIFY_TASK.value["name"]
    )
    pending_requests_notify_function = (
        Task.APPROVALS_PENDING_REQUESTS_EMAIL_NOTIFY_TASK.value["function"]
    )
    if not literal_eval(
        params["show_approval_feature"].capitalize()
    ):  # If approval feature turned off
        logger.info("Disable approval feature.")
        if client.client_features.get(
            "show_approval_feature"
        ):  # If approval feature on, previously then remove cron entry
            logger.info("Approval was enabled previously, removing them.")
            remove_approval_task_entry_for_client(
                client_id,
                existing_time_zone,
                auto_approve_task_name,
                APPROVALS_AUTO_APPROVE_TASKS_TIME,
            )
            remove_approval_task_entry_for_client(
                client_id,
                existing_time_zone,
                pending_requests_notify_task_name,
                notification_task_time,
            )
    else:
        logger.info("Enable approval feature related cron task.")
        if not client.client_features.get(
            "show_approval_feature"
        ):  # If approval feature was not on previously then create cron entry
            logger.info("Approval feature was not on previously.")
            create_approval_task_entry_for_client(
                client_id,
                new_time_zone,
                auto_approve_task_name,
                auto_approve_function,
                APPROVALS_AUTO_APPROVE_TASKS_TIME,
            )
        else:  # If approval feature on previously, and if there is a change in timezone then update cron entries else do nothing
            logger.info("Approval was disabled previously.")
            if existing_time_zone != new_time_zone:
                logger.info("Make time_zone change related changes.")
                remove_approval_task_entry_for_client(
                    client_id,
                    existing_time_zone,
                    auto_approve_task_name,
                    APPROVALS_AUTO_APPROVE_TASKS_TIME,
                )
                remove_approval_task_entry_for_client(
                    client_id,
                    existing_time_zone,
                    pending_requests_notify_task_name,
                    notification_task_time,
                )

                create_approval_task_entry_for_client(
                    client_id,
                    new_time_zone,
                    auto_approve_task_name,
                    auto_approve_function,
                    APPROVALS_AUTO_APPROVE_TASKS_TIME,
                )
                create_approval_task_entry_for_client(
                    client_id,
                    new_time_zone,
                    pending_requests_notify_task_name,
                    pending_requests_notify_function,
                    notification_task_time,
                )
    logger.info("END: Approval cron task update client.")


def update_admin_cron_tasks(client, params):
    logger = LogWithContext()  # pylint: disable=redefined-outer-name
    logger.info(
        f"BEGIN: Admin Notifications cron task update client {client} {params}."
    )

    client_id = client.client_id
    existing_time_zone = client.time_zone
    new_time_zone = params["timezone"]
    notification_modes = [mode.value["name"] for mode in NotificationMode]
    admin_cron_tasks = [
        Notification.MONTHLY_COMMISSION_NOTIFICATION.value,
        Notification.COMMISSION_REMINDER_NOTIFICATION.value,
        Notification.COMMISSION_REMINDER_NOTIFICATION_BEFORE_FD.value,
    ]

    # Only proceed if the time zone is actually changing
    if existing_time_zone != new_time_zone:
        logger.info(
            f"Time zone change detected. Updating tasks from {existing_time_zone} to {new_time_zone}."
        )

        for task in admin_cron_tasks:
            notification_name = task["name"]
            notification = Notification[notification_name].value
            frequency = task.get("frequency", notification["frequency"]).upper()
            freq = Freq[frequency]

            # Get existing and new cron tab schedules based on frequency
            existing_cron_tab_schedule = get_cron_tab_schedule(
                freq, client_id, notification["name"], existing_time_zone
            )
            new_cron_tab_schedule = get_cron_tab_schedule(
                freq, client_id, notification["name"], new_time_zone
            )
            notif_status = ClientNotificationAccessor(
                client_id
            ).get_notification_status(notification_name)
            for mode in notification_modes:
                task_name = notification[mode]["task"]
                function = Task[task_name].value["function"]
                enabled_value = notif_status[mode]
                # periodic task removal and creation
                logger.info(f"Updating task {task_name} for mode {mode}.")
                remove_comm_notification_task_entry_for_client(
                    client_id, task_name, existing_cron_tab_schedule
                )
                if enabled_value:
                    create_commission_notification_task_entry_for_client(
                        client_id, task_name, function, new_cron_tab_schedule
                    )

    logger.info(f"END: Admin Notifications cron task update for client {client_id}.")


def update_user_group_sync_task(client, params):
    existing_time_zone = client.time_zone
    new_time_zone = params["timezone"]
    if new_time_zone != existing_time_zone:
        update_user_group_sync_task_entry_for_client(client.client_id, new_time_zone)


def update_client_with_properties(params):
    try:
        errors = []
        status = "Success"
        _id = params["client_id"]
        client = ca.get_client(_id)
        is_name_exist = name_validation(params["name"])
        if is_name_exist and params["name"] != client.name:
            errors.append("Name Already exist")
            status = "Error"
        if not errors:
            client = ca.update_client(params)
            update_approval_cron_task(client, params)
            update_user_group_sync_task(client, params)
            update_admin_cron_tasks(client, params)

        return {"status": status, "errors": errors}
    except Exception as e:
        log_me("EXCEPTION IN CLIENT CREATION - {}".format(traceback.print_exc()))
        log_me("Exception: {}".format(e))


def update_client_email_invite_template(client_id, new_template_id):
    try:
        old_template_id = (
            EmailTemplateDetailsAccessor().get_invite_template_id_for_client(client_id)
        )

        if old_template_id == new_template_id:
            return

        EmailTemplateDetailsAccessor().remove_client_from_template(
            client_id, old_template_id
        )

        EmailTemplateDetailsAccessor().map_client_to_email_template(
            client_id, new_template_id
        )
    except Exception as e:
        log_me("EXCEPTION IN CLIENT CREATION - {}".format(traceback.print_exc()))
        log_me("Exception: {}".format(e))


def validate_fiscal_start_month(client_id: int, fiscal_month: str) -> bool:
    current_fiscal_month = ca.get_client_fiscal_start_month(client_id)
    new_fiscal_month = ca.fiscal_start_month_number_map[fiscal_month]
    is_quota_exists = EmployeeQuotaAccessor(
        client_id
    ).employee_quota_exists_for_client()
    if (
        is_quota_exists
        and current_fiscal_month
        and current_fiscal_month != new_fiscal_month
    ):
        return True
    return False


def update_hard_delete_cron(
    hard_delete_sync_frequency, client_id, day_of_week, day_of_month, audit
):
    if hard_delete_sync_frequency == "None":
        if get_hard_delete_sync_status_for_client(client_id=client_id).get(
            "additional_delete_syncs"
        ):
            delete_hard_delete_sync(client_id=client_id, audit=audit)

    else:
        if day_of_week != "*":
            day_of_week = str(int(day_of_week) - 1)
        cron_expression = {"day_of_week": day_of_week, "day_of_month": day_of_month}
        if get_hard_delete_sync_status_for_client(client_id=client_id).get(
            "additional_delete_syncs"
        ):
            update_hard_delete_sync_status(
                client_id=client_id,
                status="True",
                cron_expression=cron_expression,
                audit=audit,
            )
        else:
            add_new_hard_delete_sync(
                client_id=client_id, cron_expression=cron_expression, audit=audit
            )


def has_sub_plan_changed_for_client(client_id: int, new_subscription_plan: str) -> bool:
    existing_subscription_plan = ca.get_client_subscription_plan(client_id)
    return existing_subscription_plan != new_subscription_plan


def turn_off_global_run_if_no_sync_is_running(client_id: int) -> bool:
    """
    Disables global_run feature for a client if no sync is currently running.

    This function checks the ETLSyncStatus table to determine if any sync process
    is currently running for the specified client. If no sync is running, it updates
    the client_features to set global_run to false.

    Args:
        client_id (int): The ID of the client to check and update

    Returns:
        bool:
            - True: indiciates global_run was successfully disabled
            - False: global_run could not be updated (due to a running sync)

    The function ensures atomicity by only updating if no sync is running at the time of update.
    """
    from django.db import connection

    with connection.cursor() as cursor:
        cursor.execute(
            """
            UPDATE interstage_clients
            SET client_features = client_features || '{"global_run": false}'
            WHERE client_id = %s
            AND NOT EXISTS (
                SELECT 1 FROM etl_sync_status
                WHERE client_id = %s
                AND sync_end_time IS NULL
            );
        """,
            [client_id, client_id],
        )

        return cursor.rowcount == 1


def set_global_run(client_id: int) -> None:
    ca.set_client_feature(client_id, "global_run", True)


def update_tqm_access(params: dict) -> None:
    client_id = params["client_id"]
    client_name = params["name"]
    if literal_eval(params["show_territory_plan"].capitalize()):
        setup_sigma_for_client(client_id, client_name)
