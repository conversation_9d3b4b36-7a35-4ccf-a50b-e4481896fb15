import graphene
from graphene_django.types import DjangoObjectType

from spm.accessors.custom_categories_accessors import CustomCategoryAccessor
from spm.models.custom_categories_models import CustomCategory
from spm.services.localization_services import (
    get_localized_message_for_default_custom_category,
)


class CustomCategoriesType(DjangoObjectType):
    class Meta:
        model = CustomCategory
        fields = ["custom_category_id", "custom_category_name", "is_active"]


class CustomCategoriesQuery(object):
    all_custom_categories = graphene.List(
        CustomCategoriesType, module_name=graphene.String()
    )

    def resolve_all_custom_categories(self, info, **kwargs):
        client_id = info.context.client_id
        module_name = kwargs.get("module_name")
        custom_categories = CustomCategoryAccessor(
            client_id
        ).get_all_custom_categories_by_module(module_name=module_name)

        # for custom_category in custom_categories:
        #     if custom_category.is_default:
        #         custom_category.custom_category_name = (
        #             get_localized_message_for_default_custom_category(
        #                 custom_category.custom_category_name, client_id
        #             )
        #         )

        return custom_categories
