"""
Unit Tests for contracts docusign services
"""

import time
from unittest.mock import ANY, MagicMock

import pytest
from django.http import HttpRequest
from django.utils import timezone
from docusign_esign.apis import BulkEnvelopesApi, EnvelopesApi, Temp<PERSON><PERSON><PERSON>
from docusign_esign.models import (
    BulkSendBatchStatus,
    BulkSendingList,
    BulkSendResponse,
    CarbonCopy,
    CertifiedDelivery,
    CustomFields,
    Document,
    Envelope,
    EnvelopeDefinition,
    EnvelopesInformation,
    EnvelopeTemplate,
    Expirations,
    Notification,
    Recipients,
    Reminders,
    Signer,
    Tabs,
    TemplateRecipients,
    Text,
    TextCustomField,
    UserInfo,
)
from rest_framework.request import Request

from async_tasks.service import AsyncTaskService
from commission_engine.utils.general_data import STATUS_CODE
from interstage_project.utils import LogWithContext
from spm.accessors.docusign_template_accessor import DocusignTemplateDetailsAccessor
from spm.services.docusign_services import template_services
from spm.services.docusign_services.docusign_api_service import DocusignApiService
from spm.services.email_services.email_services import send_email
from spm.tests import models
from spm.tests.logger import LoggerUnit


def requests():
    """
    create a request object
    """
    http_request = HttpRequest()
    request = Request(request=http_request)
    setattr(request, "audit", LoggerUnit.logger())
    setattr(request, "logger", LogWithContext({}))
    return request


@pytest.mark.django_db
@pytest.mark.spm
class TestDocusignServices:
    def prep_data(self, create_template=False):
        """
        Initial preparation common to all tests
        """
        models.create_docusign_object()
        if create_template:
            models.create_docusign_template_details(
                template_id="cb326512-4488-40eb-aecf-544fbe85cc23"
            )

    def test_get_template_details(self):
        self.prep_data()
        request = requests()
        client_id = 1
        login_email_id = "<EMAIL>"
        template_id = "cb326512-4488-40eb-aecf-544fbe85cc23"
        logger = request.logger

        DocusignApiService.get_api_client = MagicMock()
        TemplatesApi.list_recipients_with_http_info = MagicMock(
            return_value=[
                TemplateRecipients(
                    signers=[
                        Signer(
                            recipient_id="1",
                            role_name="Broker",
                            routing_order="1",
                            recipient_type="signer",
                            tabs=Tabs(
                                text_tabs=[
                                    Text(
                                        value="",
                                        required="false",
                                        tab_label="Score",
                                        document_id="1",
                                        recipient_id="1",
                                        tab_id="ee3af588-702d-4d0b-b1a1-9212a8d6fe37",
                                        tab_type="text",
                                    ),
                                ]
                            ),
                        ),
                        Signer(
                            recipient_id="100000",
                            role_name="Salesman",
                            routing_order="2",
                            recipient_type="signer",
                            tabs=Tabs(
                                text_tabs=[
                                    Text(
                                        value="",
                                        required="true",
                                        tab_label="Score",
                                        document_id="1",
                                        recipient_id="100000",
                                        tab_id="8d55036f-9eeb-4a69-90a5-49dade2d6271",
                                        tab_type="text",
                                    ),
                                ]
                            ),
                        ),
                    ],
                    carbon_copies=[
                        CarbonCopy(
                            recipient_id="4067eb1c-01e9-4276-a208-a8e4da65c93c",
                            role_name="Manager",
                            routing_order="2",
                            recipient_type="carboncopy",
                        )
                    ],
                    certified_deliveries=[
                        CertifiedDelivery(
                            recipient_id="100001",
                            role_name="CEO",
                            routing_order="3",
                            recipient_type="certifieddelivery",
                        )
                    ],
                    recipient_count="4",
                ),
                None,
                "DUMMY DOCUSIGN API LIMIT",
            ]
        )
        template_tab_details = template_services.get_template_details(
            client_id, login_email_id, template_id, logger
        )
        assert (
            template_tab_details
            and len(template_tab_details["Broker"]) == 5
            and len(template_tab_details["Salesman"]) == 5
            and len(template_tab_details["Manager"]) == 4
            and len(template_tab_details["CEO"]) == 4
        )

    def test_get_docusign_template_details(self):
        template_id = "cb326512-4488-40eb-aecf-544fbe85cc23"
        account_id = "cb326512-4488-40eb-aecf-544fbe85cc23"

        DocusignApiService.get_api_client = MagicMock()

        TemplatesApi.get_with_http_info = MagicMock(
            return_value=[
                EnvelopeTemplate(
                    name="Cars CP Contract",
                    created="2022-11-22T06:54:24.8370000Z",
                    last_modified="2023-01-03T07:38:24.1500000Z",
                    recipients=Recipients(
                        signers=[
                            Signer(
                                recipient_id="1",
                                role_name="Broker",
                                routing_order="1",
                            ),
                            Signer(
                                recipient_id="100000",
                                role_name="Salesman",
                                routing_order="2",
                            ),
                        ],
                        carbon_copies=[
                            CarbonCopy(
                                recipient_id="4067eb1c-01e9-4276-a208-a8e4da65c93c",
                                role_name="Manager",
                                routing_order="2",
                            )
                        ],
                        certified_deliveries=[
                            CertifiedDelivery(
                                recipient_id="100001",
                                role_name="CEO",
                                routing_order="3",
                            )
                        ],
                        recipient_count="4",
                    ),
                    documents=[
                        Document(
                            name="Lorem_ipsum.pdf",
                            document_base64="dummy_base64_content",
                        )
                    ],
                ),
                None,
                "DUMMY DOCUSIGN API LIMIT",
            ]
        )

        TemplatesApi.get_notification_settings_with_http_info = MagicMock(
            return_value=[
                Notification(
                    reminders=Reminders(
                        reminder_enabled="false",
                        reminder_delay="120",
                        reminder_frequency="0",
                    ),
                    expirations=Expirations(
                        expire_enabled="false",
                        expire_after="120",
                        expire_warn="0",
                    ),
                )
            ]
        )

        template_details = template_services._get_docusign_template_details(
            account_id, template_id, TemplatesApi()
        )
        assert isinstance(template_details, dict)

    def test_initiate_bulk_send_envelopes_task(self):
        self.prep_data()
        request = requests()
        client_id = 1
        audit = request.audit
        login_email_id = "<EMAIL>"
        template_id = "cb326512-4488-40eb-aecf-544fbe85cc23"
        template_name = "Cars CP Contracts"
        raw_envelope_data = []

        AsyncTaskService.run_task = MagicMock(return_value=1)
        task_id = template_services.initiate_bulk_send_envelopes_task(
            client_id,
            audit,
            login_email_id,
            template_id,
            template_name,
            raw_envelope_data,
        )
        assert task_id == 1

    def test_bulk_send_envelopes(self):
        self.prep_data(create_template=True)
        request = requests()
        client_id = 1
        audit = request.audit
        login_email_id = "<EMAIL>"
        template_id = "cb326512-4488-40eb-aecf-544fbe85cc23"
        template_name = "Cars CP Contracts"
        raw_envelope_data = [
            [
                {
                    "tabId": "manually-created-id-envelope-id",
                    "tabType": "manualprimarykey",
                    "name": "EnvelopeId",
                    "tabLabel": "Envelope ID",
                    "colHeaderName": "Envelope ID",
                    "optional": "false",
                    "required": "true",
                    "tabValue": 1,
                },
                {
                    "tabId": "manually-created-id-1-name",
                    "tabType": "manualfullname",
                    "name": "FullName",
                    "tabLabel": "Name",
                    "colHeaderName": "Broker::Name",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "1",
                    "roleName": "Broker",
                    "tabValue": "Rupert Dockreay",
                },
                {
                    "tabId": "manually-created-id-1-email",
                    "tabType": "manualemailaddress",
                    "name": "EmailAddress",
                    "tabLabel": "Email",
                    "colHeaderName": "Broker::Email",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "1",
                    "roleName": "Broker",
                    "tabValue": "<EMAIL>",
                },
                {
                    "tabId": "manually-created-id-1-email-subject",
                    "tabType": "manualemailsubject",
                    "name": "EmailSubject",
                    "tabLabel": "Email Subject",
                    "colHeaderName": "Broker::Email Subject",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "1",
                    "roleName": "Broker",
                    "tabValue": "Phasellus id sapien in sapien iaculis congue.",
                },
                {
                    "tabId": "manually-created-id-1-email-body",
                    "tabType": "manualemailbody",
                    "name": "EmailBody",
                    "tabLabel": "Email Body",
                    "colHeaderName": "Broker::Email Body",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "1",
                    "roleName": "Broker",
                    "tabValue": "Donec diam neque, vestibulum eget, vulputate ut, ultrices vel, augue. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Donec pharetra, magna vestibulum aliquet ultrices, erat tortor sollicitudin mi, sit amet lobortis sapien sapien non mi. Integer ac neque.",
                },
                {
                    "tabId": "ee3af588-702d-4d0b-b1a1-9212a8d6fe37",
                    "tabType": "text",
                    "name": None,
                    "tabLabel": "Score",
                    "colHeaderName": "Broker::Score",
                    "optional": None,
                    "required": "false",
                    "documentId": "1",
                    "recipientId": "1",
                    "roleName": "Broker",
                    "tabValue": "",
                },
                {
                    "tabId": "manually-created-id-100000-name",
                    "tabType": "manualfullname",
                    "name": "FullName",
                    "tabLabel": "Name",
                    "colHeaderName": "Salesman::Name",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100000",
                    "roleName": "Salesman",
                    "tabValue": "Uriel Spratling",
                },
                {
                    "tabId": "manually-created-id-100000-email",
                    "tabType": "manualemailaddress",
                    "name": "EmailAddress",
                    "tabLabel": "Email",
                    "colHeaderName": "Salesman::Email",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100000",
                    "roleName": "Salesman",
                    "tabValue": "<EMAIL>",
                },
                {
                    "tabId": "manually-created-id-100000-email-subject",
                    "tabType": "manualemailsubject",
                    "name": "EmailSubject",
                    "tabLabel": "Email Subject",
                    "colHeaderName": "Salesman::Email Subject",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100000",
                    "roleName": "Salesman",
                    "tabValue": "Proin leo odio, porttitor id, consequat in, consequat ut, nulla.",
                },
                {
                    "tabId": "manually-created-id-100000-email-body",
                    "tabType": "manualemailbody",
                    "name": "EmailBody",
                    "tabLabel": "Email Body",
                    "colHeaderName": "Salesman::Email Body",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100000",
                    "roleName": "Salesman",
                    "tabValue": "Proin leo odio, porttitor id, consequat in, consequat ut, nulla. Sed accumsan felis. Ut at dolor quis odio consequat varius.",
                },
                {
                    "tabId": "8d55036f-9eeb-4a69-90a5-49dade2d6271",
                    "tabType": "text",
                    "name": None,
                    "tabLabel": "Score",
                    "colHeaderName": "Salesman::Score",
                    "optional": None,
                    "required": "true",
                    "documentId": "1",
                    "recipientId": "100000",
                    "roleName": "Salesman",
                    "tabValue": "23",
                },
                {
                    "tabId": "manually-created-id-4067eb1c-01e9-4276-a208-a8e4da65c93c-name",
                    "tabType": "manualfullname",
                    "name": "FullName",
                    "tabLabel": "Name",
                    "colHeaderName": "Manager::Name",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "4067eb1c-01e9-4276-a208-a8e4da65c93c",
                    "roleName": "Manager",
                    "tabValue": "Quill Norcock",
                },
                {
                    "tabId": "manually-created-id-4067eb1c-01e9-4276-a208-a8e4da65c93c-email",
                    "tabType": "manualemailaddress",
                    "name": "EmailAddress",
                    "tabLabel": "Email",
                    "colHeaderName": "Manager::Email",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "4067eb1c-01e9-4276-a208-a8e4da65c93c",
                    "roleName": "Manager",
                    "tabValue": "<EMAIL>",
                },
                {
                    "tabId": "manually-created-id-4067eb1c-01e9-4276-a208-a8e4da65c93c-email-subject",
                    "tabType": "manualemailsubject",
                    "name": "EmailSubject",
                    "tabLabel": "Email Subject",
                    "colHeaderName": "Manager::Email Subject",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "4067eb1c-01e9-4276-a208-a8e4da65c93c",
                    "roleName": "Manager",
                    "tabValue": "Fusce consequat.",
                },
                {
                    "tabId": "manually-created-id-4067eb1c-01e9-4276-a208-a8e4da65c93c-email-body",
                    "tabType": "manualemailbody",
                    "name": "EmailBody",
                    "tabLabel": "Email Body",
                    "colHeaderName": "Manager::Email Body",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "4067eb1c-01e9-4276-a208-a8e4da65c93c",
                    "roleName": "Manager",
                    "tabValue": "In hac habitasse platea dictumst. Morbi vestibulum, velit id pretium iaculis, diam erat fermentum justo, nec condimentum neque sapien placerat ante. Nulla justo.",
                },
                {
                    "tabId": "manually-created-id-100001-name",
                    "tabType": "manualfullname",
                    "name": "FullName",
                    "tabLabel": "Name",
                    "colHeaderName": "CEO::Name",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100001",
                    "roleName": "CEO",
                    "tabValue": "Leelah Purchase",
                },
                {
                    "tabId": "manually-created-id-100001-email",
                    "tabType": "manualemailaddress",
                    "name": "EmailAddress",
                    "tabLabel": "Email",
                    "colHeaderName": "CEO::Email",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100001",
                    "roleName": "CEO",
                    "tabValue": "<EMAIL>",
                },
                {
                    "tabId": "manually-created-id-100001-email-subject",
                    "tabType": "manualemailsubject",
                    "name": "EmailSubject",
                    "tabLabel": "Email Subject",
                    "colHeaderName": "CEO::Email Subject",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100001",
                    "roleName": "CEO",
                    "tabValue": "In quis justo.",
                },
                {
                    "tabId": "manually-created-id-100001-email-body",
                    "tabType": "manualemailbody",
                    "name": "EmailBody",
                    "tabLabel": "Email Body",
                    "colHeaderName": "CEO::Email Body",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100001",
                    "roleName": "CEO",
                    "tabValue": "Aenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.",
                },
            ]
        ]
        logger = request.logger

        DocusignApiService.get_api_client = MagicMock()

        # for tab_details
        TemplatesApi.list_recipients_with_http_info = MagicMock(
            return_value=[
                TemplateRecipients(
                    signers=[
                        Signer(
                            recipient_id="1",
                            role_name="Broker",
                            routing_order="1",
                            recipient_type="signer",
                            tabs=Tabs(
                                text_tabs=[
                                    Text(
                                        value="",
                                        required="false",
                                        tab_label="Score",
                                        document_id="1",
                                        recipient_id="1",
                                        tab_id="ee3af588-702d-4d0b-b1a1-9212a8d6fe37",
                                        tab_type="text",
                                    ),
                                ]
                            ),
                        ),
                        Signer(
                            recipient_id="100000",
                            role_name="Salesman",
                            routing_order="2",
                            recipient_type="signer",
                            tabs=Tabs(
                                text_tabs=[
                                    Text(
                                        value="",
                                        required="true",
                                        tab_label="Score",
                                        document_id="1",
                                        recipient_id="100000",
                                        tab_id="8d55036f-9eeb-4a69-90a5-49dade2d6271",
                                        tab_type="text",
                                    ),
                                ]
                            ),
                        ),
                    ],
                    carbon_copies=[
                        CarbonCopy(
                            recipient_id="4067eb1c-01e9-4276-a208-a8e4da65c93c",
                            role_name="Manager",
                            routing_order="2",
                            recipient_type="carboncopy",
                        )
                    ],
                    certified_deliveries=[
                        CertifiedDelivery(
                            recipient_id="100001",
                            role_name="CEO",
                            routing_order="3",
                            recipient_type="certifieddelivery",
                        )
                    ],
                    recipient_count="4",
                ),
                None,
                "DUMMY DOCUSIGN API LIMIT",
            ]
        )

        # for template_details
        TemplatesApi.get_with_http_info = MagicMock(
            return_value=[
                EnvelopeTemplate(
                    name="Cars CP Contract",
                    created="2022-11-22T06:54:24.8370000Z",
                    last_modified="2023-01-03T07:38:24.1500000Z",
                    recipients=Recipients(
                        signers=[
                            Signer(
                                recipient_id="1",
                                role_name="Broker",
                                routing_order="1",
                            ),
                            Signer(
                                recipient_id="100000",
                                role_name="Salesman",
                                routing_order="2",
                            ),
                        ],
                        carbon_copies=[
                            CarbonCopy(
                                recipient_id="4067eb1c-01e9-4276-a208-a8e4da65c93c",
                                role_name="Manager",
                                routing_order="2",
                            )
                        ],
                        certified_deliveries=[
                            CertifiedDelivery(
                                recipient_id="100001",
                                role_name="CEO",
                                routing_order="3",
                            )
                        ],
                        recipient_count="4",
                    ),
                    documents=[
                        Document(
                            name="Lorem_ipsum.pdf",
                            document_base64="dummy_base64_content",
                        )
                    ],
                ),
                None,
                "DUMMY DOCUSIGN API LIMIT",
            ]
        )
        TemplatesApi.get_notification_settings_with_http_info = MagicMock(
            return_value=[
                Notification(
                    reminders=Reminders(
                        reminder_enabled="false",
                        reminder_delay="120",
                        reminder_frequency="0",
                    ),
                    expirations=Expirations(
                        expire_enabled="false",
                        expire_after="120",
                        expire_warn="0",
                    ),
                )
            ]
        )

        BulkEnvelopesApi.create_bulk_send_list = MagicMock(
            return_value=BulkSendingList(list_id="dummy-list-id")
        )
        EnvelopesApi.create_envelope = MagicMock(
            return_value=EnvelopeDefinition(envelope_id="dummy-envelope-id")
        )
        BulkEnvelopesApi.create_bulk_send_request = MagicMock(
            return_value=BulkSendResponse(batch_id="dummy-batch-id")
        )
        BulkEnvelopesApi.get_bulk_send_batch_status = MagicMock(
            return_value=BulkSendBatchStatus(
                sent="0",
                failed="0",
                queued="1",
                batch_id="baa04bc9-39b2-40f5-aa69-0b8727625332",
                batch_name="Everstage - Cars CP Contract",
                batch_size="1",
                bulk_errors=[],
                submitted_date="2023-01-02T14:54:30.0730000Z",
            )
        )
        time.sleep = MagicMock()
        EnvelopesApi.list_status_changes_with_http_info = MagicMock(
            return_value=[
                EnvelopesInformation(
                    envelopes=[
                        Envelope(
                            envelope_id="e73bd154-ce2a-4d6e-b52f-2a5d01839ca8",
                            sender=UserInfo(user_name="Ankur Gupta"),
                            status="sent",
                            email_subject="Phasellus id sapien in sapien iaculis congue.",
                            created_date_time="2023-01-03T08:11:02.1530000Z",
                            initial_sent_date_time="2023-01-03T08:11:03.7770000Z",
                            sent_date_time="2023-01-03T08:11:03.7770000Z",
                            completed_date_time=None,
                            voided_date_time=None,
                            voided_reason=None,
                            declined_date_time=None,
                            recipients=Recipients(
                                signers=[
                                    Signer(
                                        recipient_id="1",
                                        name="Rupert Dockreay",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="1",
                                        status="sent",
                                        delivered_date_time="2023-01-03T08:11:03.7170000Z",
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    ),
                                    Signer(
                                        recipient_id="3",
                                        name="Uriel Spratling",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    ),
                                ],
                                carbon_copies=[
                                    CarbonCopy(
                                        recipient_id="2",
                                        name="Quill Norcock",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                certified_deliveries=[
                                    CertifiedDelivery(
                                        recipient_id="4",
                                        name="Leelah Purchase",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="3",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                recipient_count="4",
                            ),
                            custom_fields=CustomFields(
                                text_custom_fields=[
                                    TextCustomField(
                                        name="everstage_envelope_id", value="1"
                                    ),
                                ]
                            ),
                        ),
                    ],
                )
            ]
        )

        send_email.apply_async = MagicMock()

        resp = template_services.bulk_send_envelopes(
            client_id,
            audit,
            login_email_id,
            template_id,
            template_name,
            raw_envelope_data,
            logger,
        )
        assert resp["status"] == "SUCCESS"

    def test_export_template_tabs_csv(self):
        self.prep_data()
        client_id = 1
        login_email_id = "<EMAIL>"
        template_id = "cb326512-4488-40eb-aecf-544fbe85cc23"
        template_name = "Cars CP Contracts"

        TemplatesApi.list_recipients_with_http_info = MagicMock(
            return_value=[
                TemplateRecipients(
                    signers=[
                        Signer(
                            recipient_id="1",
                            role_name="Broker",
                            routing_order="1",
                            recipient_type="signer",
                            tabs=Tabs(
                                text_tabs=[
                                    Text(
                                        value="",
                                        required="false",
                                        tab_label="Score",
                                        document_id="1",
                                        recipient_id="1",
                                        tab_id="ee3af588-702d-4d0b-b1a1-9212a8d6fe37",
                                        tab_type="text",
                                    ),
                                ]
                            ),
                        ),
                        Signer(
                            recipient_id="100000",
                            role_name="Salesman",
                            routing_order="2",
                            recipient_type="signer",
                            tabs=Tabs(
                                text_tabs=[
                                    Text(
                                        value="",
                                        required="true",
                                        tab_label="Score",
                                        document_id="1",
                                        recipient_id="100000",
                                        tab_id="8d55036f-9eeb-4a69-90a5-49dade2d6271",
                                        tab_type="text",
                                    ),
                                ]
                            ),
                        ),
                    ],
                    carbon_copies=[
                        CarbonCopy(
                            recipient_id="4067eb1c-01e9-4276-a208-a8e4da65c93c",
                            role_name="Manager",
                            routing_order="2",
                            recipient_type="carboncopy",
                        )
                    ],
                    certified_deliveries=[
                        CertifiedDelivery(
                            recipient_id="100001",
                            role_name="CEO",
                            routing_order="3",
                            recipient_type="certifieddelivery",
                        )
                    ],
                    recipient_count="4",
                ),
                None,
                "DUMMY DOCUSIGN API LIMIT",
            ]
        )

        file_resp = template_services.export_template_tabs_csv(
            client_id, template_id, template_name, login_email_id
        )
        assert file_resp.filename == f"{template_name} - Template.csv"

    def test_export_envelopes_status_as_csv(self):
        self.prep_data()
        request = requests()
        client_id = 1
        login_email_id = "<EMAIL>"
        template_id = "cb326512-4488-40eb-aecf-544fbe85cc23"
        template_name = "Cars CP Contracts"
        logger = request.logger

        EnvelopesApi.list_status_changes_with_http_info = MagicMock(
            return_value=[
                EnvelopesInformation(
                    envelopes=[
                        Envelope(
                            envelope_id="e73bd154-ce2a-4d6e-b52f-2a5d01839ca8",
                            sender=UserInfo(user_name="Ankur Gupta"),
                            status="declined",
                            email_subject="Phasellus id sapien in sapien iaculis congue.",
                            created_date_time="2023-01-03T08:11:02.1530000Z",
                            initial_sent_date_time="2023-01-03T08:11:03.7770000Z",
                            sent_date_time="2023-01-03T08:11:03.7770000Z",
                            completed_date_time=None,
                            voided_date_time=None,
                            voided_reason=None,
                            declined_date_time="2024-01-03T09:11:03.7770000Z",
                            recipients=Recipients(
                                signers=[
                                    Signer(
                                        recipient_id="1",
                                        name="Rupert Dockreay",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="1",
                                        status="declined",
                                        delivered_date_time="2023-01-03T08:11:03.7170000Z",
                                        signed_date_time=None,
                                        declined_date_time="2024-01-03T09:11:03.7770000Z",
                                        declined_reason="Testing Decline",
                                    ),
                                    Signer(
                                        recipient_id="3",
                                        name="Uriel Spratling",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    ),
                                ],
                                carbon_copies=[
                                    CarbonCopy(
                                        recipient_id="2",
                                        name="Quill Norcock",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                certified_deliveries=[
                                    CertifiedDelivery(
                                        recipient_id="4",
                                        name="Leelah Purchase",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="3",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                recipient_count="4",
                            ),
                            custom_fields=CustomFields(
                                text_custom_fields=[
                                    TextCustomField(
                                        name="everstage_envelope_id", value="1"
                                    ),
                                ]
                            ),
                        ),
                    ],
                )
            ]
        )
        file_resp = template_services.export_envelopes_status_as_csv(
            client_id,
            template_id,
            template_name,
            login_email_id,
            logger,
        )
        assert file_resp.filename == f"{template_name} - status.csv"

    def test_get_contract_status_csv(self):
        self.prep_data()
        request = requests()
        client_id = 1
        email_id = "<EMAIL>"
        logger = request.logger
        fiscal_year = 2023
        is_archived = False

        from types import SimpleNamespace

        DocusignTemplateDetailsAccessor.get_filtered_templates = MagicMock(
            return_value=[
                SimpleNamespace(
                    **{
                        "template_id": "cb326512-4488-40eb-aecf-544fbe85cc23",
                        "plan_id": None,
                        "fiscal_year": 2023,
                        "is_archived": False,
                        "primary_recipient_role": "",
                    }
                )
            ]
        )

        TemplatesApi.list_templates_with_http_info = MagicMock(
            return_value=[
                SimpleNamespace(
                    **{
                        "envelope_templates": [
                            SimpleNamespace(
                                **{
                                    "template_id": "cb326512-4488-40eb-aecf-544fbe85cc23",
                                    "name": "Cars CP Contracts",
                                    "created": "2022-11-22T06:54:24.8370000Z",
                                    "fiscal_year": 2023,
                                    "is_archived": False,
                                    "primary_recipient_role": "",
                                }
                            )
                        ]
                    }
                ),
                0,
                "docusign http info",
            ]
        )

        EnvelopesApi.list_status_changes_with_http_info = MagicMock(
            return_value=[
                EnvelopesInformation(
                    envelopes=[
                        Envelope(
                            envelope_id="e73bd154-ce2a-4d6e-b52f-2a5d01839ca8",
                            sender=UserInfo(user_name="Ankur Gupta"),
                            status="declined",
                            email_subject="Phasellus id sapien in sapien iaculis congue.",
                            created_date_time="2023-01-03T08:11:02.1530000Z",
                            initial_sent_date_time="2023-01-03T08:11:03.7770000Z",
                            sent_date_time="2023-01-03T08:11:03.7770000Z",
                            completed_date_time=None,
                            voided_date_time=None,
                            voided_reason=None,
                            declined_date_time="2024-01-03T09:11:03.7770000Z",
                            recipients=Recipients(
                                signers=[
                                    Signer(
                                        recipient_id="1",
                                        name="Rupert Dockreay",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="1",
                                        status="declined",
                                        delivered_date_time="2023-01-03T08:11:03.7170000Z",
                                        signed_date_time=None,
                                        declined_date_time="2024-01-03T09:11:03.7770000Z",
                                        declined_reason="Testing Decline",
                                    ),
                                    Signer(
                                        recipient_id="3",
                                        name="Uriel Spratling",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    ),
                                ],
                                carbon_copies=[
                                    CarbonCopy(
                                        recipient_id="2",
                                        name="Quill Norcock",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                certified_deliveries=[
                                    CertifiedDelivery(
                                        recipient_id="4",
                                        name="Leelah Purchase",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="3",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                recipient_count="4",
                            ),
                            custom_fields=CustomFields(
                                text_custom_fields=[
                                    TextCustomField(
                                        name="everstage_envelope_id", value="1"
                                    ),
                                ]
                            ),
                        ),
                    ],
                )
            ]
        )

        result = template_services.get_contract_status_csv(
            client_id, email_id, fiscal_year, is_archived, logger
        )
        assert result["status"]["value"] == STATUS_CODE.SUCCESS

    def test_get_contract_status_csv_fail_case_1(self):
        """Does not match fiscal year"""
        self.prep_data()
        request = requests()
        client_id = 1
        email_id = "<EMAIL>"
        logger = request.logger
        fiscal_year = 2024
        is_archived = False

        from types import SimpleNamespace

        DocusignTemplateDetailsAccessor.get_filtered_templates = MagicMock(
            return_value=[
                SimpleNamespace(
                    **{
                        "template_id": "cb326512-4488-40eb-aecf-544fbe85cc23",
                        "plan_id": None,
                        "fiscal_year": 2023,
                        "is_archived": False,
                        "primary_recipient_role": "",
                    }
                )
            ]
        )

        TemplatesApi.list_templates_with_http_info = MagicMock(
            return_value=[
                SimpleNamespace(
                    **{
                        "envelope_templates": [
                            SimpleNamespace(
                                **{
                                    "template_id": "cb326512-4488-40eb-aecf-544fbe85cc23",
                                    "name": "Cars CP Contracts",
                                    "created": "2022-11-22T06:54:24.8370000Z",
                                    "fiscal_year": 2023,
                                    "is_archived": False,
                                    "primary_recipient_role": "",
                                }
                            )
                        ]
                    }
                ),
                0,
                "docusign http info",
            ]
        )

        EnvelopesApi.list_status_changes_with_http_info = MagicMock(
            return_value=[
                EnvelopesInformation(
                    envelopes=[
                        Envelope(
                            envelope_id="e73bd154-ce2a-4d6e-b52f-2a5d01839ca8",
                            sender=UserInfo(user_name="Ankur Gupta"),
                            status="declined",
                            email_subject="Phasellus id sapien in sapien iaculis congue.",
                            created_date_time="2023-01-03T08:11:02.1530000Z",
                            initial_sent_date_time="2023-01-03T08:11:03.7770000Z",
                            sent_date_time="2023-01-03T08:11:03.7770000Z",
                            completed_date_time=None,
                            voided_date_time=None,
                            voided_reason=None,
                            declined_date_time="2024-01-03T09:11:03.7770000Z",
                            recipients=Recipients(
                                signers=[
                                    Signer(
                                        recipient_id="1",
                                        name="Rupert Dockreay",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="1",
                                        status="declined",
                                        delivered_date_time="2023-01-03T08:11:03.7170000Z",
                                        signed_date_time=None,
                                        declined_date_time="2024-01-03T09:11:03.7770000Z",
                                        declined_reason="Testing Decline",
                                    ),
                                    Signer(
                                        recipient_id="3",
                                        name="Uriel Spratling",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    ),
                                ],
                                carbon_copies=[
                                    CarbonCopy(
                                        recipient_id="2",
                                        name="Quill Norcock",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                certified_deliveries=[
                                    CertifiedDelivery(
                                        recipient_id="4",
                                        name="Leelah Purchase",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="3",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                recipient_count="4",
                            ),
                            custom_fields=CustomFields(
                                text_custom_fields=[
                                    TextCustomField(
                                        name="everstage_envelope_id", value="1"
                                    ),
                                ]
                            ),
                        ),
                    ],
                )
            ]
        )

        result = template_services.get_contract_status_csv(
            client_id, email_id, fiscal_year, is_archived, logger
        )
        assert result["status"]["value"] == STATUS_CODE.SUCCESS

    def test_get_contract_status_csv_fail_case_2(self):
        """Does not have any templates"""
        self.prep_data()
        request = requests()
        client_id = 1
        email_id = "<EMAIL>"
        logger = request.logger
        fiscal_year = 2023
        is_archived = False

        from types import SimpleNamespace

        DocusignTemplateDetailsAccessor.get_filtered_templates = MagicMock(
            return_value=[]
        )

        TemplatesApi.list_templates_with_http_info = MagicMock(
            return_value=[
                SimpleNamespace(
                    **{
                        "envelope_templates": [
                            SimpleNamespace(
                                **{
                                    "template_id": "cb326512-4488-40eb-aecf-544fbe85cc23",
                                    "name": "Cars CP Contracts",
                                    "created": "2022-11-22T06:54:24.8370000Z",
                                    "fiscal_year": 2023,
                                    "is_archived": False,
                                    "primary_recipient_role": "",
                                }
                            )
                        ]
                    }
                ),
                0,
                "docusign http info",
            ]
        )

        EnvelopesApi.list_status_changes_with_http_info = MagicMock(
            return_value=[
                EnvelopesInformation(
                    envelopes=[
                        Envelope(
                            envelope_id="e73bd154-ce2a-4d6e-b52f-2a5d01839ca8",
                            sender=UserInfo(user_name="Ankur Gupta"),
                            status="declined",
                            email_subject="Phasellus id sapien in sapien iaculis congue.",
                            created_date_time="2023-01-03T08:11:02.1530000Z",
                            initial_sent_date_time="2023-01-03T08:11:03.7770000Z",
                            sent_date_time="2023-01-03T08:11:03.7770000Z",
                            completed_date_time=None,
                            voided_date_time=None,
                            voided_reason=None,
                            declined_date_time="2024-01-03T09:11:03.7770000Z",
                            recipients=Recipients(
                                signers=[
                                    Signer(
                                        recipient_id="1",
                                        name="Rupert Dockreay",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="1",
                                        status="declined",
                                        delivered_date_time="2023-01-03T08:11:03.7170000Z",
                                        signed_date_time=None,
                                        declined_date_time="2024-01-03T09:11:03.7770000Z",
                                        declined_reason="Testing Decline",
                                    ),
                                    Signer(
                                        recipient_id="3",
                                        name="Uriel Spratling",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    ),
                                ],
                                carbon_copies=[
                                    CarbonCopy(
                                        recipient_id="2",
                                        name="Quill Norcock",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                certified_deliveries=[
                                    CertifiedDelivery(
                                        recipient_id="4",
                                        name="Leelah Purchase",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="3",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                recipient_count="4",
                            ),
                            custom_fields=CustomFields(
                                text_custom_fields=[
                                    TextCustomField(
                                        name="everstage_envelope_id", value="1"
                                    ),
                                ]
                            ),
                        ),
                    ],
                )
            ]
        )

        result = template_services.get_contract_status_csv(
            client_id, email_id, fiscal_year, is_archived, logger
        )
        assert result["status"]["value"] == STATUS_CODE.FAILED

    def test_get_contract_status_csv_fail_case_3(self):
        """Does not have any templates"""
        self.prep_data()
        request = requests()
        client_id = 1
        email_id = "<EMAIL>"
        logger = request.logger
        fiscal_year = 2023
        is_archived = False

        from types import SimpleNamespace

        DocusignTemplateDetailsAccessor.get_filtered_templates = MagicMock(
            return_value=[]
        )

        TemplatesApi.list_templates_with_http_info = MagicMock(
            return_value=[
                SimpleNamespace(
                    **{
                        "envelope_templates": [
                            SimpleNamespace(
                                **{
                                    "template_id": "cb326512-4488-40eb-aecf-544fbe85cc23",
                                    "name": "Cars CP Contracts",
                                    "created": "2022-11-22T06:54:24.8370000Z",
                                    "fiscal_year": 2023,
                                    "is_archived": False,
                                    "primary_recipient_role": "",
                                }
                            )
                        ]
                    }
                ),
                0,
                "docusign http info",
            ]
        )

        EnvelopesApi.list_status_changes_with_http_info = MagicMock(
            return_value=[
                EnvelopesInformation(
                    envelopes=[
                        Envelope(
                            envelope_id="e73bd154-ce2a-4d6e-b52f-2a5d01839ca8",
                            sender=UserInfo(user_name="Ankur Gupta"),
                            status="declined",
                            email_subject="Phasellus id sapien in sapien iaculis congue.",
                            created_date_time="2023-01-03T08:11:02.1530000Z",
                            initial_sent_date_time="2023-01-03T08:11:03.7770000Z",
                            sent_date_time="2023-01-03T08:11:03.7770000Z",
                            completed_date_time=None,
                            voided_date_time=None,
                            voided_reason=None,
                            declined_date_time="2024-01-03T09:11:03.7770000Z",
                            recipients=Recipients(
                                signers=[
                                    Signer(
                                        recipient_id="1",
                                        name="Rupert Dockreay",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="1",
                                        status="declined",
                                        delivered_date_time="2023-01-03T08:11:03.7170000Z",
                                        signed_date_time=None,
                                        declined_date_time="2024-01-03T09:11:03.7770000Z",
                                        declined_reason="Testing Decline",
                                    ),
                                    Signer(
                                        recipient_id="3",
                                        name="Uriel Spratling",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    ),
                                ],
                                carbon_copies=[
                                    CarbonCopy(
                                        recipient_id="2",
                                        name="Quill Norcock",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                certified_deliveries=[
                                    CertifiedDelivery(
                                        recipient_id="4",
                                        name="Leelah Purchase",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="3",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                recipient_count="4",
                            ),
                            custom_fields=CustomFields(
                                text_custom_fields=[
                                    TextCustomField(
                                        name="everstage_envelope_id", value="1"
                                    ),
                                ]
                            ),
                        ),
                    ],
                )
            ]
        )

        result = template_services.get_contract_status_csv(
            client_id, email_id, fiscal_year, is_archived, logger
        )
        assert result["status"]["value"] == STATUS_CODE.FAILED

    def test_export_contract_status(self):
        self.prep_data()
        request = requests()
        client_id = 1
        email_id = "<EMAIL>"
        logger = request.logger
        fiscal_year = 2023
        is_archived = False

        from types import SimpleNamespace

        DocusignTemplateDetailsAccessor.get_filtered_templates = MagicMock(
            return_value=[
                SimpleNamespace(
                    **{
                        "template_id": "cb326512-4488-40eb-aecf-544fbe85cc23",
                        "plan_id": None,
                        "fiscal_year": 2023,
                        "is_archived": False,
                        "primary_recipient_role": "",
                    }
                )
            ]
        )

        TemplatesApi.list_templates_with_http_info = MagicMock(
            return_value=[
                SimpleNamespace(
                    **{
                        "envelope_templates": [
                            SimpleNamespace(
                                **{
                                    "template_id": "cb326512-4488-40eb-aecf-544fbe85cc23",
                                    "name": "Cars CP Contracts",
                                    "created": "2022-11-22T06:54:24.8370000Z",
                                    "fiscal_year": 2023,
                                    "is_archived": False,
                                    "primary_recipient_role": "",
                                }
                            )
                        ]
                    }
                ),
                0,
                "docusign http info",
            ]
        )

        EnvelopesApi.list_status_changes_with_http_info = MagicMock(
            return_value=[
                EnvelopesInformation(
                    envelopes=[
                        Envelope(
                            envelope_id="e73bd154-ce2a-4d6e-b52f-2a5d01839ca8",
                            sender=UserInfo(user_name="Ankur Gupta"),
                            status="declined",
                            email_subject="Phasellus id sapien in sapien iaculis congue.",
                            created_date_time="2023-01-03T08:11:02.1530000Z",
                            initial_sent_date_time="2023-01-03T08:11:03.7770000Z",
                            sent_date_time="2023-01-03T08:11:03.7770000Z",
                            completed_date_time=None,
                            voided_date_time=None,
                            voided_reason=None,
                            declined_date_time="2024-01-03T09:11:03.7770000Z",
                            recipients=Recipients(
                                signers=[
                                    Signer(
                                        recipient_id="1",
                                        name="Rupert Dockreay",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="1",
                                        status="declined",
                                        delivered_date_time="2023-01-03T08:11:03.7170000Z",
                                        signed_date_time=None,
                                        declined_date_time="2024-01-03T09:11:03.7770000Z",
                                        declined_reason="Testing Decline",
                                    ),
                                    Signer(
                                        recipient_id="3",
                                        name="Uriel Spratling",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    ),
                                ],
                                carbon_copies=[
                                    CarbonCopy(
                                        recipient_id="2",
                                        name="Quill Norcock",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="2",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                certified_deliveries=[
                                    CertifiedDelivery(
                                        recipient_id="4",
                                        name="Leelah Purchase",
                                        email="<EMAIL>",
                                        recipient_type="signer",
                                        routing_order="3",
                                        status="created",
                                        delivered_date_time=None,
                                        signed_date_time=None,
                                        declined_date_time=None,
                                        declined_reason=None,
                                    )
                                ],
                                recipient_count="4",
                            ),
                            custom_fields=CustomFields(
                                text_custom_fields=[
                                    TextCustomField(
                                        name="everstage_envelope_id", value="1"
                                    ),
                                ]
                            ),
                        ),
                    ],
                )
            ]
        )

        send_email.apply_async = MagicMock()

        to_email = "<EMAIL>"
        result = template_services.export_contract_status(
            client_id, email_id, to_email, fiscal_year, is_archived, logger
        )

        assert result == STATUS_CODE.SUCCESS

    def test_get_account_permission_success(self):
        # Arrange
        client_id = 1
        email_id = "<EMAIL>"
        mock_oauth = type(
            "OAuth",
            (),
            {
                "account_id": "acc-123",
                "refresh_token": "refresh-token",
                "base_uri": "https://demo.docusign.net",
            },
        )()
        # Patch DocusignAccessor
        from spm.services.docusign_services import template_services

        template_services.DocusignAccessor = MagicMock(
            return_value=MagicMock(get_oauth_details=MagicMock(return_value=mock_oauth))
        )
        # Patch DocusignApiService
        mock_docusign_service = MagicMock()
        mock_docusign_service.get_access_token_from_refresh_token.return_value = {
            "access_token": "token",
            "expires_in": 3600,
        }
        template_services.DocusignApiService = MagicMock(
            return_value=mock_docusign_service
        )
        # Patch cache
        template_services.cache = MagicMock()
        template_services.cache.get.return_value = None
        # Patch requests.get
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "accountNotification": {"userOverrideEnabled": True},
            "setRecipEmailLang": True,
            "bulkSend": True,
        }
        template_services.requests.get = MagicMock(return_value=mock_response)

        # Act
        result = template_services.get_account_permission(client_id, email_id)

        # Assert
        assert result == {
            "userOverrideEnabled": True,
            "setRecipEmailLang": True,
            "enableBulkRecipients": True,
        }

    def test_get_account_permission_oauth_none(self):
        client_id = 1
        email_id = "<EMAIL>"
        from spm.services.docusign_services import template_services

        template_services.DocusignAccessor = MagicMock(
            return_value=MagicMock(get_oauth_details=MagicMock(return_value=None))
        )
        with pytest.raises(Exception, match="oauth data not present for the user"):
            template_services.get_account_permission(client_id, email_id)

    def test_get_user_permission_success(self):
        client_id = 1
        email_id = "<EMAIL>"
        mock_oauth = type(
            "OAuth",
            (),
            {
                "account_id": "acc-123",
                "refresh_token": "refresh-token",
                "base_uri": "https://demo.docusign.net",
                "email_id": email_id,
                "user_id": "user-123",
            },
        )()
        # Patch DocusignAccessor
        from spm.services.docusign_services import template_services

        template_services.DocusignAccessor = MagicMock(
            return_value=MagicMock(get_oauth_details=MagicMock(return_value=mock_oauth))
        )
        # Patch DocusignApiService
        mock_docusign_service = MagicMock()
        mock_docusign_service.get_api_client.return_value = MagicMock()
        mock_docusign_service.get_access_token_from_refresh_token.return_value = {
            "access_token": "token",
            "expires_in": 3600,
        }
        template_services.DocusignApiService = MagicMock(
            return_value=mock_docusign_service
        )
        # Patch cache
        template_services.cache = MagicMock()
        template_services.cache.get.return_value = None
        # Patch requests.get
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "userSettings": {
                "canManageTemplates": True,
                "disableDocumentUpload": False,
                "canSendEnvelope": True,
                "allowRecipientLanguageSelection": True,
                "bulkSend": True,
            }
        }
        template_services.requests.get = MagicMock(return_value=mock_response)

        # Act
        result = template_services.get_user_permission(client_id, email_id)

        # Assert
        assert result == {
            "canManageTemplates": True,
            "disableDocumentUpload": False,
            "canSendEnvelope": True,
            "allowRecipientLanguageSelection": True,
            "bulkSend": True,
        }

    def test_get_user_permission_oauth_none(self):
        client_id = 1
        email_id = "<EMAIL>"
        from spm.services.docusign_services import template_services

        template_services.DocusignAccessor = MagicMock(
            return_value=MagicMock(get_oauth_details=MagicMock(return_value=None))
        )
        with pytest.raises(Exception, match="oauth data not present for the user"):
            template_services.get_user_permission(client_id, email_id)

    def test_get_user_permission_fetches_user_id_when_missing(self):
        client_id = 1
        email_id = "<EMAIL>"
        # oauth_data without user_id
        mock_oauth = type(
            "OAuth",
            (),
            {
                "account_id": "acc-123",
                "refresh_token": "refresh-token",
                "base_uri": "https://demo.docusign.net",
                "email_id": email_id,
                # no user_id
            },
        )()
        from spm.services.docusign_services import template_services

        # Patch DocusignAccessor
        mock_accessor = MagicMock()
        mock_accessor.get_oauth_details.return_value = mock_oauth
        mock_accessor.update_user_id = MagicMock()
        template_services.DocusignAccessor = MagicMock(return_value=mock_accessor)
        # Patch DocusignApiService
        mock_docusign_service = MagicMock()
        mock_api_client = MagicMock()
        mock_docusign_service.get_api_client.return_value = mock_api_client
        mock_docusign_service.get_access_token_from_refresh_token.return_value = {
            "access_token": "token",
            "expires_in": 3600,
        }
        template_services.DocusignApiService = MagicMock(
            return_value=mock_docusign_service
        )
        # Patch UsersApi.list to return a user_id
        mock_users_api = MagicMock()
        mock_users_api.list.return_value = type(
            "UsersResponse",
            (),
            {"users": [type("User", (), {"user_id": "user-999"})()]},
        )()
        template_services.UsersApi = MagicMock(return_value=mock_users_api)
        # Patch cache
        template_services.cache = MagicMock()
        template_services.cache.get.return_value = None
        # Patch requests.get
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "userSettings": {
                "canManageTemplates": True,
                "disableDocumentUpload": False,
                "canSendEnvelope": True,
                "allowRecipientLanguageSelection": True,
                "bulkSend": True,
            }
        }
        template_services.requests.get = MagicMock(return_value=mock_response)

        # Act
        result = template_services.get_user_permission(client_id, email_id)

        # Assert
        assert result == {
            "canManageTemplates": True,
            "disableDocumentUpload": False,
            "canSendEnvelope": True,
            "allowRecipientLanguageSelection": True,
            "bulkSend": True,
        }
        # Ensure update_user_id was called with the new user_id
        mock_accessor.update_user_id.assert_called_once_with("user-999", email_id, ANY)
