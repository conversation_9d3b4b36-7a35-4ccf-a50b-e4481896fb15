import logging
import uuid
from collections import defaultdict
from datetime import datetime

from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response

from commission_engine.accessors.databook_accessor import (
    DatasheetAccessor,
    DatasheetPermissionsAccessor,
    DatasheetPermissionsTargetAccessor,
)
from commission_engine.serializers.databook_serializers import (
    DataSheetPermissionBulkSerializer,
    DataSheetPermissionTargetBulkSerializer,
)
from commission_engine.utils.general_data import (
    SegmentEvents,
    SegmentProperties,
    user_properties_list,
)
from everstage_ddd.workflow_builder.service.workflow_service import (
    delete_datasheet_permissions_in_cache,
)
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.custom_field_accessor import CustomFieldsAccessor
from spm.accessors.user_group_accessor import UserGroupAccessor
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.custom_object_services.co_permission_services import (
    does_user_has_permission_to_custom_objects,
    get_objects_excluded_for_user,
)
from spm.services.datasheet_variable_services import get_datasheet_variables
from spm.services.rbac_services import does_user_have_databook_manage_permission
from spm.services.user_group_service import UserGroupMemberService

logger = logging.getLogger(__name__)


def get_custom_objects_involved_in_ds_creation(
    client_id, original_datasheet_id, knowledge_date=None
):
    if not isinstance(original_datasheet_id, uuid.UUID):
        original_datasheet_id = uuid.UUID(original_datasheet_id)

    ds_source_and_transformation_spec = DatasheetAccessor(
        client_id
    ).get_datasheet_source_and_transformation_spec(knowledge_date=knowledge_date)
    datasheet_id_to_custom_object_ids_map = defaultdict(set)
    for each_datasheet in ds_source_and_transformation_spec:
        datasheet_id_temp = each_datasheet["datasheet_id"]
        source_id = each_datasheet["source_id"]
        source_type = each_datasheet["source_type"]
        transformation_spec = each_datasheet["transformation_spec"] or []
        value_set = datasheet_id_to_custom_object_ids_map.get(datasheet_id_temp, set())
        if source_type.lower() == "datasheet":
            source_id = uuid.UUID(source_id)
            value_set.update(datasheet_id_to_custom_object_ids_map[source_id])
        elif source_type.lower() == "object":
            value_set.add(source_id)
        elif source_type.lower() == "report":
            value_set.add(source_id)
        for each_transformation in transformation_spec:
            if each_transformation.get("type", False):
                if each_transformation["type"] in ("JOIN", "UNION"):
                    value_set.update(
                        datasheet_id_to_custom_object_ids_map[
                            uuid.UUID(each_transformation["with"])
                        ]
                    )
        datasheet_id_to_custom_object_ids_map[datasheet_id_temp] = value_set
    return datasheet_id_to_custom_object_ids_map.get(original_datasheet_id, set())


def get_all_permissions(client_id, databook_id, datasheet_id):
    all_permission_entries = DatasheetPermissionsAccessor(
        client_id=client_id
    ).get_all_permissions_of_datasheet(
        databook_id=databook_id, datasheet_id=datasheet_id
    )

    datasheet_perm_target = DatasheetPermissionsTargetAccessor(client_id=client_id)
    employee = EmployeeAccessor(client_id=client_id)
    user_group = UserGroupAccessor(client_id=client_id)

    set_of_permissions_and_users = {}

    for i in range(len(all_permission_entries)):
        if (
            all_permission_entries[i]["permission_set_id"]
            not in set_of_permissions_and_users
        ):
            set_of_permissions_and_users[
                all_permission_entries[i]["permission_set_id"]
            ] = {
                "permission_set_name": all_permission_entries[i]["permission_set_name"],
                "users": list(
                    employee.get_employees_name_as_dict_with_full_name(
                        datasheet_perm_target.get_users(
                            all_permission_entries[i]["permission_set_id"]
                        )
                    )
                ),
                "user_groups": list(
                    user_group.get_user_group_name_as_dict(
                        datasheet_perm_target.get_user_groups(
                            all_permission_entries[i]["permission_set_id"]
                        )
                    )
                ),
                "filter_list": all_permission_entries[i]["filter_list"],
                "columns_to_be_hidden": all_permission_entries[i][
                    "columns_to_be_hidden"
                ],
            }

    final_list_of_permissions = {
        "databook_id": databook_id,
        "datasheet_id": datasheet_id,
        "permission_set": [],
    }

    for key, val in set_of_permissions_and_users.items():
        permission_set_entry = {}
        permission_set_entry["permission_set_id"] = key
        permission_set_entry["users"] = val["users"]
        permission_set_entry["user_groups"] = val["user_groups"]
        permission_set_entry["filter_list"] = val["filter_list"]
        permission_set_entry["permission_set_name"] = val["permission_set_name"]
        permission_set_entry["columns_to_be_hidden"] = val["columns_to_be_hidden"]
        final_list_of_permissions["permission_set"].append(permission_set_entry)

    return final_list_of_permissions


def create_or_update_permissions(client_id, params, user_name):
    datasheet_perm = DatasheetPermissionsAccessor(client_id=client_id)
    datasheet_target_perm = DatasheetPermissionsTargetAccessor(client_id=client_id)
    datasheet = DatasheetAccessor(client_id=client_id)
    databook_id = params["databook_id"]
    datasheet_id = params["datasheet_id"]
    variables = get_datasheet_variables(
        client_id, databook_id, datasheet_id, ["system_name", "variable_id"]
    )

    if not datasheet.does_datasheet_exist(datasheet_id):
        return Response(
            {"status": "Error", "error_message": "Datasheet does not exist"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

    time = timezone.now()
    all_permissions = datasheet_perm.get_only_permissions_of_datasheet(
        databook_id, datasheet_id
    )

    permissions_set_list = params["permission_set"]
    permissions_data_list = []

    for i in range(len(permissions_set_list)):
        # Validate filter_list
        filter_list = permissions_set_list[i]["filter_list"]
        used_columns = []

        for filter_item in filter_list:
            used_columns.append(filter_item["col_name"])
            if (
                filter_item["data_type"] == "Integer"
                and filter_item["operator"] == "IN"
            ):
                invalid_values = [
                    value["filter_value"]
                    for value in filter_item["value"]
                    if not value.get("user_system_field", False)
                    and not value["filter_value"].isdigit()
                ]
                if invalid_values:
                    return Response(
                        {
                            "status": "Error",
                            "error_message": f"Invalid filter value(s) {', '.join(invalid_values)} for Integer column '{filter_item['col_display_name']}'",
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

        variable_id_map = params.get("variable_id_map")
        all_used_column = set(
            used_columns + permissions_set_list[i]["columns_to_be_hidden"]
        )

        # construct variable_id_map if not provided
        if not variable_id_map:
            variable_id_map = {}
            for variable in variables:
                if variable["system_name"] in all_used_column:
                    variable_id_map[variable["system_name"]] = str(
                        variable["variable_id"]
                    )

        permissions_set_entry = {}
        permissions_set_entry["client_id"] = client_id
        permissions_set_entry["client"] = client_id
        permissions_set_entry["databook_id"] = params["databook_id"]
        permissions_set_entry["datasheet_id"] = params["datasheet_id"]
        permissions_set_entry["filter_list"] = filter_list
        permissions_set_entry["permission_set_id"] = permissions_set_list[i][
            "permission_set_id"
        ]
        permissions_set_entry["knowledge_begin_date"] = time
        permissions_set_entry["columns_to_be_hidden"] = permissions_set_list[i][
            "columns_to_be_hidden"
        ]
        permissions_set_entry["permission_set_name"] = permissions_set_list[i][
            "permission_set_name"
        ]
        permissions_set_entry["additional_details"] = {
            "variable_id_map": variable_id_map
        }
        permissions_data_list.append(permissions_set_entry)

    permissions_target_data_list = []
    analytics_data_list = []
    for i in range(len(permissions_set_list)):
        if permissions_set_list[i]["users"]:
            for j in range(len(permissions_set_list[i]["users"])):
                permissions_set_entry = {}
                permissions_set_entry["client_id"] = client_id
                permissions_set_entry["client"] = client_id
                permissions_set_entry["permission_set_id"] = permissions_set_list[i][
                    "permission_set_id"
                ]
                permissions_set_entry["target_type"] = "user"
                permissions_set_entry["target"] = permissions_set_list[i]["users"][j]
                permissions_set_entry["knowledge_begin_date"] = time
                permissions_target_data_list.append(permissions_set_entry)
            analytics_data = {
                "user_id": user_name,
                "event_name": SegmentEvents.CREATE_NEW_PERMISSSION_SET.value,
                "event_properties": {
                    SegmentProperties.PAYEE_NAME.value: permissions_set_list[i][
                        "users"
                    ],
                    SegmentProperties.COUNT_OF_ROW_PERMISSIONS.value: len(
                        permissions_set_list[i]["filter_list"]
                    ),
                    SegmentProperties.HIDE_COLUMNS.value: permissions_set_list[i][
                        "columns_to_be_hidden"
                    ],
                },
            }
            analytics_data_list.append(analytics_data)

        if permissions_set_list[i]["user_groups"]:
            for j in range(len(permissions_set_list[i]["user_groups"])):
                permissions_set_entry = {}
                permissions_set_entry["client_id"] = client_id
                permissions_set_entry["client"] = client_id
                permissions_set_entry["permission_set_id"] = permissions_set_list[i][
                    "permission_set_id"
                ]
                permissions_set_entry["target_type"] = "user_group"
                permissions_set_entry["target"] = permissions_set_list[i][
                    "user_groups"
                ][j]
                permissions_set_entry["knowledge_begin_date"] = time
                permissions_target_data_list.append(permissions_set_entry)

    permissions_data = DataSheetPermissionBulkSerializer(
        data=permissions_data_list, many=True
    )

    permissions_target_data = DataSheetPermissionTargetBulkSerializer(
        data=permissions_target_data_list, many=True
    )

    # Invalidate the existing permissions
    datasheet_perm.invalidate_permissions(databook_id, datasheet_id, time)
    datasheet_target_perm.invalidate_permissions(all_permissions)

    try:
        if permissions_data.is_valid():
            datasheet_perm.persist_permissions(permissions_data)
        else:
            logger.info(permissions_data.errors)
            return Response(
                {
                    "status": "Error",
                    "error_message": "Could not save datasheet permissions",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        if permissions_target_data.is_valid():
            datasheet_target_perm.persist_permissions(permissions_target_data)
        else:
            logger.info(permissions_target_data.errors)
            return Response(
                {
                    "status": "Error",
                    "error_message": "Could not save datasheet permissions with targets",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        # Delete the cache for the datasheet as this is used in workflow and should be updated for every user
        delete_datasheet_permissions_in_cache(client_id, databook_id, datasheet_id)

    except Exception as e:
        logger.info("Exception in create or update datasheet permissions: {}".format(e))
        return Response(
            {
                "status": "Exception",
                "error_message": "Could not save datasheet permissions",
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
    analytics = CoreAnalytics(analyser_type="segment")
    for analytics_data in analytics_data_list:
        analytics.send_analytics(analytics_data)
    logger.info("Object created / updated successfully")
    return Response({"status": "Success"}, status=status.HTTP_200_OK)


def does_user_has_permission_to_custom_objects_in_datasheet(
    client_id, databook_id, datasheet_id, user_email_id, knowledge_date=None
):
    if databook_id and isinstance(databook_id, str):
        databook_id = uuid.UUID(databook_id)

    if isinstance(datasheet_id, str):
        datasheet_id = uuid.UUID(datasheet_id)

    custom_objects = get_custom_objects_involved_in_ds_creation(
        client_id=client_id,
        original_datasheet_id=datasheet_id,
        knowledge_date=knowledge_date,
    )
    if not custom_objects:
        return True

    return does_user_has_permission_to_custom_objects(
        client_id,
        user_email_id,
        custom_objects,
    )


def get_permissions_for_users_and_user_group(
    client_id, databook_id, datasheet_id, user_and_user_group_names_list
):
    datasheet_perm = DatasheetPermissionsAccessor(client_id=client_id)
    datasheet_target_perm = DatasheetPermissionsTargetAccessor(client_id=client_id)
    all_permissions = datasheet_perm.get_only_permissions_of_datasheet(
        databook_id, datasheet_id
    )
    selected_permissions = (
        datasheet_target_perm.get_all_permissions_for_users_and_user_groups(
            all_permissions, user_and_user_group_names_list
        )
    )
    return datasheet_perm.get_filter_list_of_permissions(
        databook_id, datasheet_id, selected_permissions
    )


def get_all_hidden_columns_for_user(
    client_id, datasheet_ids_list, logged_in_user_email_id
):
    datasheet_permission = DatasheetPermissionsAccessor(client_id)
    datasheet_permission_target = DatasheetPermissionsTargetAccessor(client_id)
    user_groups_of_logged_in_user = UserGroupMemberService(
        client_id
    ).get_user_groups_of_user(logged_in_user_email_id)
    user_and_user_group_names_list = user_groups_of_logged_in_user + [
        logged_in_user_email_id
    ]
    hide_columns_map = {}

    for datasheet_id in datasheet_ids_list:
        all_permissions_for_datasheet = (
            datasheet_permission.get_all_permissions_of_datasheet_given_ds_id(
                datasheet_id
            )
        )
        selected_permissions = (
            datasheet_permission_target.get_all_permissions_for_users_and_user_groups(
                all_permissions_for_datasheet, user_and_user_group_names_list
            )
        )
        hide_columns_map[datasheet_id] = (
            datasheet_permission.get_hidden_columns_of_selected_permissions_of_datasheet(
                datasheet_id, selected_permissions
            )
        )

    return hide_columns_map


def does_datasheet_permission_exist_for_the_user(
    client_id, databook_id, datasheet_id, user_id
):
    user_groups = UserGroupMemberService(client_id).get_user_groups_of_user(user_id)
    datasheet_perm = DatasheetPermissionsAccessor(client_id=client_id)
    datasheet_target_perm = DatasheetPermissionsTargetAccessor(client_id=client_id)
    permission_ids = datasheet_perm.get_only_permissions_of_datasheet(
        databook_id, datasheet_id
    )
    if not permission_ids:
        return False
    targets = [user_id] + user_groups if user_groups else [user_id]
    return datasheet_target_perm.does_permission_exist_for_user(permission_ids, targets)


def get_all_hidden_columns_in_a_datasheet_for_user(
    client_id, datasheet_id, logged_in_user_email_id
):
    datasheet_permission = DatasheetPermissionsAccessor(client_id)
    datasheet_permission_target = DatasheetPermissionsTargetAccessor(client_id)
    user_groups_of_logged_in_user = UserGroupMemberService(
        client_id
    ).get_user_groups_of_user(logged_in_user_email_id)
    user_and_user_group_names_list = user_groups_of_logged_in_user + [
        logged_in_user_email_id
    ]

    all_permissions_for_datasheet = (
        datasheet_permission.get_all_permissions_of_datasheet_given_ds_id(datasheet_id)
    )

    selected_permissions = (
        datasheet_permission_target.get_all_permissions_for_users_and_user_groups(
            all_permissions_for_datasheet, user_and_user_group_names_list
        )
    )

    columns_to_be_hidden = (
        datasheet_permission.get_hidden_columns_of_selected_permissions_of_datasheet(
            datasheet_id, selected_permissions
        )
    )

    return columns_to_be_hidden


def does_user_have_access_to_datasheet_and_its_custom_objects(
    client_id, databook_id, datasheet_id, email
):
    permissions = {}
    permissions["custom_object_permission"] = (
        does_user_has_permission_to_custom_objects_in_datasheet(
            client_id, databook_id, datasheet_id, email, knowledge_date=None
        )
    )
    if not permissions["custom_object_permission"]:
        permissions["datasheet_restricted_access"] = True

    else:
        if does_user_have_databook_manage_permission(client_id, email):
            permissions["datasheet_restricted_access"] = False
        else:
            permissions["datasheet_restricted_access"] = (
                does_datasheet_permission_exist_for_the_user(
                    client_id, databook_id, datasheet_id, email
                )
            )

    return permissions


def validate_datasheet_permissions_given_user_group_id(client_id, user_group_id):
    datasheet_permission_target = DatasheetPermissionsTargetAccessor(client_id)
    return datasheet_permission_target.does_permissions_with_given_user_group_id_exist(
        user_group_id
    )


def get_datasheets_without_permissions(client_id, databook_id, user_email_id):
    objects_without_permission = get_objects_excluded_for_user(client_id, user_email_id)
    if not objects_without_permission:
        return []
    datasheets = DatasheetAccessor(client_id).get_ordered_datasheets_of_databook(
        databook_id
    )
    ds_without_permission = set()
    ds_names = []
    for datasheet in datasheets:
        if datasheet["source_type"] == "object":
            try:
                object_id = int(datasheet["source_id"])
                if object_id in objects_without_permission:
                    ds_without_permission.add(str(datasheet["datasheet_id"]))
                    ds_names.append(datasheet["name"])
                    continue
            except ValueError:
                pass
        elif (
            datasheet["source_type"] == "datasheet"
            and datasheet["source_id"] in ds_without_permission
        ):
            ds_without_permission.add(str(datasheet["datasheet_id"]))
            ds_names.append(datasheet["name"])
            continue
        for transformation in datasheet["transformation_spec"]:
            if (
                transformation
                and "type" in transformation
                and transformation["type"].lower() in ["join", "union"]
            ):
                if transformation["with"] in ds_without_permission:
                    ds_without_permission.add(str(datasheet["datasheet_id"]))
                    ds_names.append(datasheet["name"])
                    break
    return ds_names


def get_user_properties_data_type(client_id):
    custom_fields = CustomFieldsAccessor(client_id).fetch_all_custom_fields()
    modified_custom_fields = [
        {**custom_field, "show_for_single_valued": True}
        for custom_field in custom_fields
    ]
    return user_properties_list + modified_custom_fields


def get_visible_columns_for_logged_in_user(
    client_id, login_user_id, plan_id, criteria_id, columns
):
    """
    get_visible_columns_for_user function returns the columns for which the logged_in_user
    has permission to view, given plan and criteria id

    Args:
        client_id (int): client id
        login_user_id (email): login user email
        plan_id (int): plan id
        criteria_id (int): criteria id
        columns (list): list of columns available for the given criteria

    Returns:
        columns (list): list of visible columns for the given criteria
    """

    from spm.accessors.commission_plan_accessor import PlanCriteriaAccessor
    from spm.services.commission_plan_services import (
        get_datasheet_id_from_plan_and_criteria_id,
    )

    manage_permissions_enabled = does_user_have_databook_manage_permission(
        client_id, login_user_id
    )

    datasheet_id = get_datasheet_id_from_plan_and_criteria_id(
        client_id, plan_id, criteria_id
    )
    visible_column_list = columns
    if manage_permissions_enabled is False:
        visible_column_list = []
        hidden_columns_for_given_source_id = get_all_hidden_columns_for_user(
            client_id, [datasheet_id], login_user_id
        )[datasheet_id]
        for column in columns:
            if column not in hidden_columns_for_given_source_id:
                visible_column_list.append(column)

    valid_criteria_columns = PlanCriteriaAccessor(client_id).get_criteria(
        criteria_id=criteria_id,
        fields=["criteria_column"],
    )["criteria_column"]

    valid_columns_to_display = [
        col for col in visible_column_list if col in valid_criteria_columns
    ]

    return valid_columns_to_display


def invalidate_datasheet_permissions(
    client_id: int, databook_id: str, datasheet_id: str, knowledge_date: datetime
) -> None:
    """
    Invalidates all permissions and permission targets for a datasheet.
    This includes:
    1. Invalidating the permission sets
    2. Invalidating the permission targets
    3. Deleting permissions from cache
    4. Clearing workflow-related caches

    Args:
        client_id: The client ID
        databook_id: The databook ID
        datasheet_id: The datasheet ID
        knowledge_date: The knowledge date to set for invalidation
    """
    # Get accessors
    datasheet_perm = DatasheetPermissionsAccessor(client_id)
    datasheet_target_perm = DatasheetPermissionsTargetAccessor(client_id)

    # Get all permission sets for this datasheet
    permission_sets = datasheet_perm.get_only_permissions_of_datasheet(
        databook_id, datasheet_id
    )

    # Invalidate permissions
    datasheet_perm.invalidate_permissions(databook_id, datasheet_id, knowledge_date)

    # Invalidate permission targets
    if permission_sets:
        datasheet_target_perm.invalidate_permissions(permission_sets, knowledge_date)

    # Delete permissions from cache
    delete_datasheet_permissions_in_cache(
        client_id, str(databook_id), str(datasheet_id)
    )
