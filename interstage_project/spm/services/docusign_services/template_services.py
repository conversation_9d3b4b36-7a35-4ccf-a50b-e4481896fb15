import base64
import json
import logging
import time
import traceback

import pandas as pd
import requests
from dateutil import parser
from django.core.cache import cache
from django.http import FileResponse
from django.utils import timezone
from docusign_esign import ApiException
from docusign_esign.apis import BulkEnvelopes<PERSON><PERSON>, <PERSON>velopes<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from docusign_esign.models import (
    BulkSendingCopy,
    BulkSendingCopyCustomField,
    BulkSendingCopyRecipient,
    BulkSendingCopyTab,
    BulkSendingList,
    BulkSendRequest,
    CarbonCopy,
    CertifiedDelivery,
    CustomFields,
    Document,
    EnvelopeDefinition,
    EnvelopeTemplate,
    RecipientEmailNotification,
    Recipients,
    Signer,
    TextCustomField,
)
from rest_framework import status
from rest_framework.response import Response
from sendgrid.helpers.mail import (
    Attachment,
    Disposition,
    Email,
    FileContent,
    FileName,
    FileType,
    Mail,
)

from async_tasks.config import AsyncTaskConfig
from async_tasks.service import AsyncTaskService
from commission_engine.accessors.client_accessor import get_client_subscription_plan
from commission_engine.utils import SegmentEvents, SegmentProperties
from commission_engine.utils.general_data import STATUS_CODE
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import (
    LogWithContext,
    get_queue_name_respect_to_task_group,
)
from spm.accessors.commission_plan_accessor import CommissionPlanAccessor
from spm.accessors.config_accessors.employee_accessor import EmployeeAccessor
from spm.accessors.docusign_accessors import DocusignAccessor
from spm.accessors.docusign_template_accessor import DocusignTemplateDetailsAccessor
from spm.constants.audit_trail import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.serializers.docusign_serializers import DocusignTemplateDetailsSerializer
from spm.services import audit_services
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.docusign_services.docusign_api_service import DocusignApiService
from spm.services.email_services.email_services import send_email


def format_docusign_time_to_utc(format_time):
    return (
        parser.parse(format_time).strftime("%d-%b-%Y, %H:%M %Z")
        if format_time
        else None
    )


def get_template_details(client_id, email_id, template_id, logger=None):
    """
    return : a dict

            {
                role_name1: [list of all tabs for that role],
                role_name2: [{tab 1 details}, {tab 2 details}, {}, {},...],
                ...
            }
    """
    logger = (
        logger
        if logger
        else LogWithContext({"template_id": template_id, "email_id": email_id})
    )
    logger.info(
        "BEGIN: Get template details for template with Id - {}".format(template_id)
    )
    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    if oauth_data:
        account_id = oauth_data.account_id
        api_client = DocusignApiService(
            client_id, email_id, account_id
        ).get_api_client()
        try:
            t_data_with_http_info = TemplatesApi(
                api_client
            ).list_recipients_with_http_info(
                account_id,
                template_id,
                include_anchor_tab_locations=True,
                include_extended=True,
                include_tabs=True,
            )
            t_data = t_data_with_http_info[0]
            logging.info(
                "DOCUSIGN API LIMIT: list_recipients %s", t_data_with_http_info[2]
            )
            if t_data:
                template_details = _get_template_tab_details(t_data)
                logger.info(
                    "END: Get template details for template with Id - {}".format(
                        template_id
                    )
                )
                return template_details
            else:
                logger.info("Template details are not present")
        except Exception as e:
            logger.error("Get template details Failed - {}".format(e))
            return Response({"status": "FAILURE"}, status=status.HTTP_400_BAD_REQUEST)
    else:
        raise Exception("Oauth details not present for the user")


def _get_template_tab_details(t_data):
    template_tab_details = {}
    if not t_data:
        return template_tab_details

    for recipient in t_data.signers:
        template_tab_details[recipient.role_name] = []
        template_tab_details[recipient.role_name].append(
            {
                "tabId": "manually-created-id-" + recipient.recipient_id + "-name",
                "tabType": "manualfullname",
                "name": "FullName",
                "tabLabel": "Name",
                "colHeaderName": recipient.role_name + "::" + "Name",
                "optional": "false",
                "required": "true",
                "recipientId": recipient.recipient_id,
                "roleName": recipient.role_name,
            }
        )

        template_tab_details[recipient.role_name].append(
            {
                "tabId": "manually-created-id-" + recipient.recipient_id + "-email",
                "tabType": "manualemailaddress",
                "name": "EmailAddress",
                "tabLabel": "Email",
                "colHeaderName": recipient.role_name + "::" + "Email",
                "optional": "false",
                "required": "true",
                "recipientId": recipient.recipient_id,
                "roleName": recipient.role_name,
            }
        )

        template_tab_details[recipient.role_name].append(
            {
                "tabId": "manually-created-id-"
                + recipient.recipient_id
                + "-email-subject",
                "tabType": "manualemailsubject",
                "name": "EmailSubject",
                "tabLabel": "Email Subject",
                "colHeaderName": recipient.role_name + "::" + "Email Subject",
                "optional": "false",
                "required": "true",
                "recipientId": recipient.recipient_id,
                "roleName": recipient.role_name,
            }
        )

        template_tab_details[recipient.role_name].append(
            {
                "tabId": "manually-created-id-"
                + recipient.recipient_id
                + "-email-body",
                "tabType": "manualemailbody",
                "name": "EmailBody",
                "tabLabel": "Email Body",
                "colHeaderName": recipient.role_name + "::" + "Email Body",
                "optional": "false",
                "required": "true",
                "recipientId": recipient.recipient_id,
                "roleName": recipient.role_name,
            }
        )

        all_tabs = recipient.tabs
        if all_tabs:
            for _key, tabs_arr in all_tabs.__dict__.items():
                if tabs_arr and isinstance(tabs_arr, list):
                    for tab in tabs_arr:
                        if (
                            tab.tab_type
                            == "text"
                            # or tab.tab_type == "fullname"
                            # or tab.tab_type == "emailaddress"
                            # or tab.tab_type == "number"
                        ):
                            template_tab_details[recipient.role_name].append(
                                {
                                    "tabId": tab.tab_id,
                                    "tabType": tab.tab_type,
                                    "name": (
                                        tab.name
                                        if hasattr(tab, "name")
                                        else tab.tab_label
                                    ),
                                    "tabLabel": tab.tab_label,
                                    "colHeaderName": recipient.role_name
                                    + "::"
                                    + tab.tab_label,
                                    "optional": (
                                        tab.optional
                                        if hasattr(tab, "optional")
                                        else None
                                    ),
                                    "required": (
                                        tab.required
                                        if hasattr(tab, "required")
                                        else None
                                    ),
                                    "documentId": tab.document_id,
                                    "recipientId": tab.recipient_id,
                                    "roleName": recipient.role_name,
                                }
                            )

    for recipient in t_data.carbon_copies:
        template_tab_details[recipient.role_name] = []
        template_tab_details[recipient.role_name].append(
            {
                "tabId": "manually-created-id-" + recipient.recipient_id + "-name",
                "tabType": "manualfullname",
                "name": "FullName",
                "tabLabel": "Name",
                "colHeaderName": recipient.role_name + "::" + "Name",
                "optional": "false",
                "required": "true",
                "recipientId": recipient.recipient_id,
                "roleName": recipient.role_name,
            }
        )

        template_tab_details[recipient.role_name].append(
            {
                "tabId": "manually-created-id-" + recipient.recipient_id + "-email",
                "tabType": "manualemailaddress",
                "name": "EmailAddress",
                "tabLabel": "Email",
                "colHeaderName": recipient.role_name + "::" + "Email",
                "optional": "false",
                "required": "true",
                "recipientId": recipient.recipient_id,
                "roleName": recipient.role_name,
            }
        )

        template_tab_details[recipient.role_name].append(
            {
                "tabId": "manually-created-id-"
                + recipient.recipient_id
                + "-email-subject",
                "tabType": "manualemailsubject",
                "name": "EmailSubject",
                "tabLabel": "Email Subject",
                "colHeaderName": recipient.role_name + "::" + "Email Subject",
                "optional": "false",
                "required": "true",
                "recipientId": recipient.recipient_id,
                "roleName": recipient.role_name,
            }
        )

        template_tab_details[recipient.role_name].append(
            {
                "tabId": "manually-created-id-"
                + recipient.recipient_id
                + "-email-body",
                "tabType": "manualemailbody",
                "name": "EmailBody",
                "tabLabel": "Email Body",
                "colHeaderName": recipient.role_name + "::" + "Email Body",
                "optional": "false",
                "required": "true",
                "recipientId": recipient.recipient_id,
                "roleName": recipient.role_name,
            }
        )

    for recipient in t_data.certified_deliveries:
        template_tab_details[recipient.role_name] = []
        template_tab_details[recipient.role_name].append(
            {
                "tabId": "manually-created-id-" + recipient.recipient_id + "-name",
                "tabType": "manualfullname",
                "name": "FullName",
                "tabLabel": "Name",
                "colHeaderName": recipient.role_name + "::" + "Name",
                "optional": "false",
                "required": "true",
                "recipientId": recipient.recipient_id,
                "roleName": recipient.role_name,
            }
        )

        template_tab_details[recipient.role_name].append(
            {
                "tabId": "manually-created-id-" + recipient.recipient_id + "-email",
                "tabType": "manualemailaddress",
                "name": "EmailAddress",
                "tabLabel": "Email",
                "colHeaderName": recipient.role_name + "::" + "Email",
                "optional": "false",
                "required": "true",
                "recipientId": recipient.recipient_id,
                "roleName": recipient.role_name,
            }
        )

        template_tab_details[recipient.role_name].append(
            {
                "tabId": "manually-created-id-"
                + recipient.recipient_id
                + "-email-subject",
                "tabType": "manualemailsubject",
                "name": "EmailSubject",
                "tabLabel": "Email Subject",
                "colHeaderName": recipient.role_name + "::" + "Email Subject",
                "optional": "false",
                "required": "true",
                "recipientId": recipient.recipient_id,
                "roleName": recipient.role_name,
            }
        )

        template_tab_details[recipient.role_name].append(
            {
                "tabId": "manually-created-id-"
                + recipient.recipient_id
                + "-email-body",
                "tabType": "manualemailbody",
                "name": "EmailBody",
                "tabLabel": "Email Body",
                "colHeaderName": recipient.role_name + "::" + "Email Body",
                "optional": "false",
                "required": "true",
                "recipientId": recipient.recipient_id,
                "roleName": recipient.role_name,
            }
        )

    return template_tab_details


def create_edit_view(client_id, email_id, template_id):
    """
        creates a URL for the template edit view of the given template_id

    :param client_id: es client_id
    :param email_id: user email
    :param template_id: template to edit
    :return: Template edit page URL
    """
    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    if oauth_data is None:
        raise Exception("oauth data not present for the user")

    account_id = oauth_data.account_id
    docusign_service = DocusignApiService(client_id, email_id, account_id)
    cache_key = get_docusign_token_cache_key(email_id, account_id)

    if cache.get(cache_key) is None:
        response = docusign_service.get_access_token_from_refresh_token(
            oauth_data.refresh_token
        )
        access_token = response.get("access_token")
        timeout = response.get("expires_in", 3600)
        cache.set(cache_key, access_token, timeout - 120)
    else:
        access_token = cache.get(cache_key)
    url = f"{oauth_data.base_uri}/restapi/v2.1/accounts/{account_id}/templates/{template_id}/views/edit"
    headers = {
        "Authorization": "Bearer {0}".format(access_token),
        "Content-Type": "application/json",
        "Content-Length": "0",
    }
    response = requests.post(url, json={}, headers=headers)
    if response:
        response = response.json()
        url = response.get("url", "")
        url = url.replace("templateDetails", "prepareTemplateFields")
        return url
    else:
        logging.error(response.json())
        raise Exception("Error in getting template edit view url")


def get_account_permission(client_id, email_id):
    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    if oauth_data is None:
        raise Exception("oauth data not present for the user")

    account_id = oauth_data.account_id
    docusign_service = DocusignApiService(client_id, email_id, account_id)
    cache_key = get_docusign_token_cache_key(email_id, account_id)

    if cache.get(cache_key) is None:
        response = docusign_service.get_access_token_from_refresh_token(
            oauth_data.refresh_token
        )
        access_token = response.get("access_token")
        timeout = response.get("expires_in", 3600)
        cache.set(cache_key, access_token, timeout - 120)
    else:
        access_token = cache.get(cache_key)
    url = f"{oauth_data.base_uri}/restapi/v2.1/accounts/{account_id}/settings"
    headers = {
        "Authorization": "Bearer {0}".format(access_token),
        "Content-Type": "application/json",
        "Content-Length": "0",
    }
    response = requests.get(url, headers=headers)
    if response:
        response = response.json()
        # Safely extract the nested value
        user_override_enabled = response.get("accountNotification", {}).get(
            "userOverrideEnabled", False
        )
        set_recip_email_lang = response.get("setRecipEmailLang", False)
        enable_bulk_recipients = response.get("bulkSend", False)
        return {
            "userOverrideEnabled": user_override_enabled,
            "setRecipEmailLang": set_recip_email_lang,
            "enableBulkRecipients": enable_bulk_recipients,
        }
    else:
        logging.error(response.json())
        raise Exception("Error in getting account level permission")


def get_user_permission(client_id, email_id):
    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    if oauth_data is None:
        raise Exception("oauth data not present for the user")
    account_id = oauth_data.account_id
    user_id = getattr(oauth_data, "user_id", None)
    email = oauth_data.email_id
    # If user_id is not present, fetch it using UsersApi
    if not user_id:
        docusign_service = DocusignApiService(client_id, email_id, account_id)
        api_client = docusign_service.get_api_client()
        users_api = UsersApi(api_client)
        users_response = users_api.list(account_id=account_id, email=email)
        user_id = users_response.users[0].user_id
        DocusignAccessor(client_id).update_user_id(user_id, email_id, timezone.now())
    cache_key = get_docusign_token_cache_key(email_id, account_id)

    if cache.get(cache_key) is None:
        docusign_service = DocusignApiService(client_id, email_id, account_id)
        response = docusign_service.get_access_token_from_refresh_token(
            oauth_data.refresh_token
        )
        access_token = response.get("access_token")
        timeout = response.get("expires_in", 3600)
        cache.set(cache_key, access_token, timeout - 120)
    else:
        access_token = cache.get(cache_key)
    url = f"{oauth_data.base_uri}/restapi/v2.1/accounts/{account_id}/users/{user_id}"
    headers = {
        "Authorization": "Bearer {0}".format(access_token),
        "Content-Type": "application/json",
        "Content-Length": "0",
    }

    response = requests.get(url, headers=headers)
    if response:
        response = response.json()
        can_manage_templates = response.get("userSettings", {}).get(
            "canManageTemplates", False
        )
        disable_document_upload = response.get("userSettings", {}).get(
            "disableDocumentUpload", False
        )
        can_send_envelope = response.get("userSettings", {}).get(
            "canSendEnvelope", False
        )
        allow_recipient_language_selection = response.get("userSettings", {}).get(
            "allowRecipientLanguageSelection", False
        )
        bulk_send = response.get("userSettings", {}).get("bulkSend", False)
        return {
            "canManageTemplates": can_manage_templates,
            "disableDocumentUpload": disable_document_upload,
            "canSendEnvelope": can_send_envelope,
            "allowRecipientLanguageSelection": allow_recipient_language_selection,
            "bulkSend": bulk_send,
        }
    else:
        logging.error(f"Error in getting user permission: {response.json()}")
        raise Exception("Error in getting user permission")


def create_template(
    client_id,
    plan_id,
    email_id,
    template_name,
    template_files,
    roles,
    template_notification_settings,
    primary_recipient_role,
    fiscal_year,
):
    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    account_id = oauth_data.account_id
    docusign_service = DocusignApiService(client_id, email_id, account_id)

    api_client = docusign_service.get_api_client()
    templates_api = TemplatesApi(api_client)

    documents = []
    index = 1
    for template_file_key in template_files.keys():
        document_id = index
        base64_file_content = base64.b64encode(
            template_files[template_file_key].read()
        ).decode("ascii")
        document = Document(
            document_base64=base64_file_content,
            name=template_files[template_file_key].name,
            document_id=document_id,
        )
        index = index + 1
        documents.append(document)

    signers = []
    certified_deliveries = []
    carbon_copies = []
    for index, role in enumerate(roles):
        recipient_id = index + 1
        signing_preference = str(role["signingPreference"])
        if signing_preference == "Needs To Sign":
            signer = Signer(
                role_name=role["role"],
                recipient_id=recipient_id,
                routing_order=role["routingOrder"],
            )
            signers.append(signer)
        elif signing_preference == "Receives A Copy":
            cc = CarbonCopy(
                role_name=role["role"],
                recipient_id=recipient_id,
                routing_order=role["routingOrder"],
            )
            carbon_copies.append(cc)
        elif signing_preference == "Needs To View":
            certified_delivery = CertifiedDelivery(
                role_name=role["role"],
                recipient_id=recipient_id,
                routing_order=role["routingOrder"],
            )
            certified_deliveries.append(certified_delivery)
    recipients = Recipients()
    if signers:
        recipients.__setattr__("signers", signers)
    if carbon_copies:
        recipients.__setattr__("carbon_copies", carbon_copies)
    if certified_deliveries:
        recipients.__setattr__("certified_deliveries", certified_deliveries)

    template_req_object = EnvelopeTemplate(
        documents=documents,
        recipients=recipients,
        name=template_name,
        recipients_lock=False,
        brand_lock=False,
        message_lock=False,
    )

    template_with_http_info = templates_api.create_template_with_http_info(
        account_id=account_id, envelope_template=template_req_object
    )
    res = template_with_http_info[0]
    logging.info("DOCUSIGN API LIMIT: create_template %s", template_with_http_info[2])
    template_id = res.template_id
    docusign_template_details = {
        "client": client_id,
        "template_id": template_id,
        "account_id": account_id,
        "plan_id": plan_id,
        "knowledge_begin_date": timezone.now(),
        "primary_recipient_role": primary_recipient_role,
        "fiscal_year": fiscal_year,
    }

    update_template_notification_settings = (
        templates_api.update_notification_settings_with_http_info(
            account_id,
            template_id,
            template_notification_request=template_notification_settings,
        )
    )
    logging.info(
        "DOCUSIGN API LIMIT: update_template_notification_settings %s",
        update_template_notification_settings[2],
    )
    ###################### audit log #####################
    event_type_code = EVENT["CREATE_CONTRACT"]["code"]
    event_key = template_id
    summary = "Created Contract successfully"
    audit_data = docusign_template_details
    updated_by = email_id
    updated_at = timezone.now()
    ######################################################

    docusign_template_details_ser = DocusignTemplateDetailsSerializer(
        data=docusign_template_details
    )
    if docusign_template_details_ser.is_valid():
        DocusignTemplateDetailsAccessor(client_id).persist_docusign_template_fields(
            docusign_template_details_ser
        )
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )
    else:
        logging.error(docusign_template_details_ser.errors)
        raise Exception("Error while persisting docusign template details")

    return {
        "status": "SUCCESS",
        "template_id": template_id,
        "template_name": template_name,
    }


def get_file_string(byte):
    return base64.b64encode(byte).decode("ascii")


def get_template_lock_details(client_id, email_id, template_id):
    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    account_id = oauth_data.account_id
    docusign_service = DocusignApiService(client_id, email_id, account_id)

    api_client = docusign_service.get_api_client()
    templates_api = TemplatesApi(api_client)
    return _get_docusign_template_lock_details(account_id, template_id, templates_api)


def update_template(
    client_id,
    template_id,
    plan_id,
    email_id,
    template_name,
    template_files,
    roles,
    template_notification_settings,
    primary_recipient_role,
    fiscal_year,
):
    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    account_id = oauth_data.account_id
    docusign_service = DocusignApiService(client_id, email_id, account_id)

    api_client = docusign_service.get_api_client()
    templates_api = TemplatesApi(api_client)

    # updated_roles = [role["role"] for role in roles]
    updated_roles_ids = [role["id"] for role in roles]

    existing_recipients_with_http_info = templates_api.list_recipients_with_http_info(
        account_id=account_id, template_id=template_id
    )

    logging.info(
        "DOCUSIGN API LIMIT: list_recipients %s", existing_recipients_with_http_info[2]
    )
    # docusign api to get template documents
    template_documents_with_http_info = templates_api.list_documents_with_http_info(
        account_id=account_id, template_id=template_id
    )
    logging.info(
        "DOCUSIGN API LIMIT: list_documents %s", template_documents_with_http_info[2]
    )

    existing_recipients = existing_recipients_with_http_info[0]
    existing_documents = template_documents_with_http_info[0]

    if existing_recipients.signers is not None:
        for signer in existing_recipients.signers:
            if signer.recipient_id not in updated_roles_ids:
                result_with_http_info = templates_api.delete_recipient_with_http_info(
                    account_id=account_id,
                    template_id=template_id,
                    recipient_id=signer.recipient_id,
                )

                logging.info(
                    "DOCUSIGN API LIMIT: delete_recipient %s", result_with_http_info[2]
                )

    if existing_recipients.carbon_copies is not None:
        for cc in existing_recipients.carbon_copies:
            if cc.recipient_id not in updated_roles_ids:
                result_with_http_info = templates_api.delete_recipient_with_http_info(
                    account_id=account_id,
                    template_id=template_id,
                    recipient_id=cc.recipient_id,
                )
                logging.info(
                    "DOCUSIGN API LIMIT: delete_recipient %s", result_with_http_info[2]
                )

    if existing_recipients.certified_deliveries is not None:
        for certified_delivery in existing_recipients.certified_deliveries:
            if certified_delivery.recipient_id not in updated_roles_ids:
                result_with_http_info = templates_api.delete_recipient_with_http_info(
                    account_id=account_id,
                    template_id=template_id,
                    recipient_id=certified_delivery.recipient_id,
                )
                logging.info(
                    "DOCUSIGN API LIMIT: delete_recipient %s", result_with_http_info[2]
                )

    if existing_documents.template_documents is not None:
        existing_documents_map = {}
        document_ids_to_delete = []
        # creating map of document name to document_ids
        for document in existing_documents.template_documents:
            if document.name not in existing_documents_map:
                existing_documents_map[document.name] = []
            existing_documents_map[document.name].append(document.document_id)

        # creating list of document_ids to delete by comparing existing documents to updated documents
        all_template_name = [file.name for _, file in template_files.items()]
        for existing_file_key in existing_documents_map.keys():
            if existing_file_key not in all_template_name:
                document_ids_to_delete.extend(existing_documents_map[existing_file_key])
        if document_ids_to_delete:
            result_with_http_info = templates_api.delete_documents_with_http_info(
                account_id=account_id,
                template_id=template_id,
                envelope_definition=EnvelopeDefinition(
                    documents=[
                        Document(document_id=document_id)
                        for document_id in document_ids_to_delete
                    ]
                ),
            )
            logging.info(
                "DOCUSIGN API LIMIT: delete_documents %s", result_with_http_info[2]
            )

    documents = []
    index = 1

    for template_file_key in template_files.keys():
        document_id = index
        base64_file_content = base64.b64encode(
            template_files[template_file_key].read()
        ).decode("ascii")
        document = Document(
            document_base64=base64_file_content,
            name=template_files[template_file_key].name,
            document_id=document_id,
        )
        index = index + 1
        documents.append(document)

    signers = []
    certified_deliveries = []
    carbon_copies = []
    for index, role in enumerate(roles):
        signing_preference = str(role["signingPreference"])
        if signing_preference == "Needs To Sign":
            signer = Signer(
                role_name=role["role"],
                recipient_id=role["id"],
                routing_order=role["routingOrder"],
            )
            signers.append(signer)
        elif signing_preference == "Receives A Copy":
            cc = CarbonCopy(
                role_name=role["role"],
                recipient_id=role["id"],
                routing_order=role["routingOrder"],
            )
            carbon_copies.append(cc)
        elif signing_preference == "Needs To View":
            certified_delivery = CertifiedDelivery(
                role_name=role["role"],
                recipient_id=role["id"],
                routing_order=role["routingOrder"],
            )
            certified_deliveries.append(certified_delivery)

    recipients = Recipients()
    if signers:
        recipients.__setattr__("signers", signers)
    if carbon_copies:
        recipients.__setattr__("carbon_copies", carbon_copies)
    if certified_deliveries:
        recipients.__setattr__("certified_deliveries", certified_deliveries)

    template_req_object = EnvelopeTemplate(
        documents=documents,
        recipients=recipients,
        name=template_name,
        recipients_lock=False,
        brand_lock=False,
        message_lock=False,
    )

    update_doc_result_with_http_info = templates_api.update_documents_with_http_info(
        account_id,
        template_id,
        envelope_definition=EnvelopeDefinition(documents=documents),
    )

    logging.info(
        "DOCUSIGN API LIMIT: update_documents %s", update_doc_result_with_http_info[2]
    )
    update_template_with_http_info = templates_api.update_with_http_info(
        account_id=account_id,
        template_id=template_id,
        envelope_template=template_req_object,
    )

    logging.info(
        "DOCUSIGN API LIMIT: update_template %s", update_template_with_http_info[2]
    )

    update_template_notification_settings = (
        templates_api.update_notification_settings_with_http_info(
            account_id,
            template_id,
            template_notification_request=template_notification_settings,
        )
    )
    logging.info(
        "DOCUSIGN API LIMIT: update_template_notification_settings %s",
        update_template_notification_settings[2],
    )

    DocusignTemplateDetailsAccessor(client_id).update_template(
        template_id,
        {
            "plan_id": plan_id,
            "fiscal_year": fiscal_year,
            "primary_recipient_role": primary_recipient_role,
        },
    )

    return {
        "status": "SUCCESS",
        "template_id": template_id,
        "template_name": template_name,
    }


def get_all_template_details(client_id, email_id, logger=None):
    docusign_details = DocusignAccessor(client_id).get_docusign_details(email_id)
    templates = DocusignTemplateDetailsAccessor(client_id).get_all_templates(
        account_id=docusign_details.account_id
    )
    if len(templates) == 0:
        return []

    template_ids = []
    template_model_details = {}
    plan_ids = []
    for template in templates:
        template_ids.append(template.template_id)
        template_model_details[template.template_id] = template
        plan_ids.append(template.plan_id)

    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    account_id = oauth_data.account_id
    docusign_service = DocusignApiService(client_id, email_id, account_id)
    api_client = docusign_service.get_api_client()
    templates_api = TemplatesApi(api_client)
    docusign_templates_info = templates_api.list_templates_with_http_info(
        account_id, include="recipients", order_by="modified", order="desc"
    )
    docusign_templates = docusign_templates_info[0].envelope_templates
    logging.info("DOCUSIGN API LIMIT: list_templates %s", docusign_templates_info[2])
    all_contracts = []
    logger = (
        logger
        if logger
        else LogWithContext(
            {
                "template_ids": template_ids,
                "email_id": email_id,
            }
        )
    )
    plan_names = CommissionPlanAccessor(client_id).get_plans_by_ids(plan_ids)
    all_plan_names = {str(x.plan_id): x.plan_name for x in plan_names}
    for template in docusign_templates:
        if template.template_id in template_ids:
            logger.info(
                "BEGIN: Get template details for template with Id - {}".format(
                    template.template_id
                )
            )
            all_contracts.append(
                {
                    "template_id": template.template_id,
                    "template_name": template.name,
                    "template_roles": [],
                    "fiscal_year": template_model_details[
                        template.template_id
                    ].fiscal_year,
                    "primary_recipient_role": template_model_details[
                        template.template_id
                    ].primary_recipient_role,
                    "is_signing_order_enabled": False,
                    "created_at": template.created,
                    "updated_at": template.last_modified,
                    "envelopes_for_template": [],
                    "completed": 0,
                    "awaiting": 0,
                    "template_files": [],
                    "tab_details": [],
                    "is_locked": False,
                    "is_archived": template_model_details[
                        template.template_id
                    ].is_archived,
                    "plan_name": all_plan_names.get(
                        template_model_details[template.template_id].plan_id, None
                    ),
                    "plan_id": template_model_details[template.template_id].plan_id,
                    "include": "[template_details]",
                    "template_notification_settings": {
                        "reminders": {
                            "reminder_enabled": "false",
                            "reminder_delay": "0",
                            "reminder_frequency": "0",
                        },
                        "expirations": {
                            "expire_enabled": "true",
                            "expire_after": "120",
                            "expire_warn": "0",
                        },
                    },
                }
            )
    return all_contracts


def get_template_details_by_id(
    client_id,
    email_id,
    template_id,
    template=None,
    logger=None,
    include=None,
):
    logger = (
        logger
        if logger
        else LogWithContext({"template_id": template_id, "email_id": email_id})
    )
    if include is None:
        include = "[template_details,envelopes_details]"

    logger.info(
        "BEGIN: Get template details for template with Id - {}".format(template_id)
    )
    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    account_id = oauth_data.account_id
    docusign_service = DocusignApiService(client_id, email_id, account_id)

    api_client = docusign_service.get_api_client()
    envelopes_api = EnvelopesApi(api_client)
    templates_api = TemplatesApi(api_client)

    include_apis = include.strip("][").split(",")
    template_details = {}
    envelope_details = {}
    lock_details = {}
    tab_details = {}
    template_files = None

    logging.info("Docusign APIs needed: %s", include_apis)

    if "template_details" in include_apis and "documents" in include_apis:
        logger.info("Getting docusign template details and documents")
        template_documents_with_http_info = templates_api.get_with_http_info(
            account_id,
            template_id,
            include="documents,tabs",
        )
        template = template_documents_with_http_info[0]
        logging.info(
            "DOCUSIGN API LIMIT: get_with_http_info %s",
            template_documents_with_http_info[2],
        )
        template_details = _get_docusign_template_details(
            account_id, template_id, templates_api, template
        )
        template_files = template_details["documents"]

    elif "template_details" in include_apis:
        logger.info("Getting docusign template details")
        template_details = _get_docusign_template_details(
            account_id, template_id, templates_api, template
        )

    elif "documents" in include_apis:
        logger.info("Getting docusign template document details")
        template_documents_with_http_info = templates_api.get_with_http_info(
            account_id,
            template_id,
            include="documents,tabs",
        )
        documents = template_documents_with_http_info[0].documents
        logging.info(
            "DOCUSIGN API LIMIT: get_with_http_info %s",
            template_documents_with_http_info[2],
        )
        template_files = _get_docusign_template_documents(documents)

    if "tab_details" in include_apis:
        logger.info("Getting docusign template tab details")
        if "template_details" in include_apis:
            tab_details = _get_template_tab_details(template_details["recipients"])
        else:
            t_data_with_http_info = TemplatesApi(
                api_client
            ).list_recipients_with_http_info(
                account_id,
                template_id,
                include_anchor_tab_locations=True,
                include_extended=True,
                include_tabs=True,
            )
            t_data = t_data_with_http_info[0]
            logging.info(
                "DOCUSIGN API LIMIT: list_recipients %s", t_data_with_http_info[2]
            )
            tab_details = _get_template_tab_details(t_data)

    if "lock_information" in include_apis:
        logger.info("Getting docusign template lock details")
        lock_details = _get_docusign_template_lock_details(
            account_id, template_id, templates_api
        )

    if "envelopes_details" in include_apis:
        logger.info("Getting docusign template envelope details")
        envelope_details = _get_docusign_envelope_details(
            account_id, template_id, envelopes_api
        )

        if template_details:
            for envelope_for_template in envelope_details["envelopes_for_template"]:
                if not envelope_for_template["email_subject"]:
                    envelope_for_template["email_subject"] = (
                        f"Please DocuSign: {template_details['name']}"
                    )

    docusign_template_details = DocusignTemplateDetailsAccessor(
        client_id
    ).get_template_details_by_id(template_id)

    plan_details = None
    if docusign_template_details.plan_id is not None:
        plan_id = docusign_template_details.plan_id
        plan_details = CommissionPlanAccessor(
            client_id
        ).get_latest_commission_plan_by_id(plan_id)

    contract_details = {
        "template_id": template_id,
        "template_name": template_details.get("name", None),
        "template_roles": template_details.get("roles", []),
        "fiscal_year": docusign_template_details.fiscal_year,
        "primary_recipient_role": docusign_template_details.primary_recipient_role,
        "is_signing_order_enabled": template_details.get(
            "is_signing_order_enabled", False
        ),
        "created_at": template_details.get("created", None),
        "updated_at": template_details.get("last_modified", None),
        "envelopes_for_template": envelope_details.get("envelopes_for_template", []),
        "completed": envelope_details.get("total_completed", 0),
        "awaiting": envelope_details.get("total_awaiting", 0),
        "template_files": template_files,
        "tab_details": tab_details,
        "is_locked": lock_details.get("is_locked", False),
        "is_archived": docusign_template_details.is_archived,
        "plan_name": plan_details.plan_name if plan_details else None,
        "plan_id": str(plan_details.plan_id) if plan_details else None,
        "include": include_apis,
        "template_notification_settings": template_details.get(
            "template_notification_settings"
        ),
    }

    analytics_data = {
        "user_id": email_id,
        "event_name": SegmentEvents.VIEW_CONTRACT.value,
        "event_properties": {
            SegmentProperties.TEMPLATE_NAME.value: contract_details.get(
                "template_name"
            ),
            SegmentProperties.LINKED_PLAN.value: contract_details.get("plan_name", "-"),
            SegmentProperties.ENV_SENT.value: len(
                contract_details.get("envelopes_for_template", [])
            ),
        },
    }
    analytics = CoreAnalytics(analyser_type="segment")
    analytics.send_analytics(analytics_data)

    return contract_details


def get_docusign_envelope_details_for_templates(client_id, email_id, template_ids):
    docusign_details = DocusignAccessor(client_id).get_docusign_details(email_id)

    docusign_service = DocusignApiService(
        client_id, email_id, docusign_details.account_id
    )
    api_client = docusign_service.get_api_client()
    envelopes_api = EnvelopesApi(api_client)

    template_envelope_details = []
    for template_id in template_ids:
        if template_id:
            try:
                envelope_details = {}
                details = _get_docusign_envelope_details(
                    docusign_details.account_id, template_id, envelopes_api
                )
                envelope_details["envelopes_for_template"] = details.get(
                    "envelopes_for_template", []
                )
                envelope_details["completed"] = details.get("total_completed", 0)
                envelope_details["awaiting"] = details.get("total_awaiting", 0)
                envelope_details["template_id"] = template_id
                template_envelope_details.append(envelope_details)
            except:
                pass

    return template_envelope_details


def _get_docusign_envelope_details(account_id, template_id, envelopes_api):
    total_completed = 0
    total_awaiting = 0
    envelopes_for_template = []
    all_envelopes_info = envelopes_api.list_status_changes_with_http_info(
        account_id,
        from_date="2020/01/01",
        search_text=template_id,
        include="recipients",
        order_by="sent",
        order="desc",
    )
    all_envelopes = all_envelopes_info[0].envelopes
    logging.info(
        "DOCUSIGN API LIMIT: envelope_list_status_changes %s", all_envelopes_info[2]
    )
    if all_envelopes is None:
        all_envelopes = []
    for envelope in all_envelopes:
        recipients = []
        completed = 0
        awaiting = 0
        envelope_for_template = {
            "envelope_id": envelope.envelope_id,
            "email_subject": envelope.email_subject,
            "sender": envelope.sender.user_name,
            "last_modified_date_time": envelope.status_changed_date_time,
            "sent_date_time": envelope.sent_date_time,
            "status": envelope.status,
            "recipients": recipients,
        }

        recipients_for_envelope = envelope.recipients
        if recipients_for_envelope:
            if recipients_for_envelope.signers:
                for signer in recipients_for_envelope.signers:
                    recipients.append(
                        {
                            "name": signer.name,
                            "status": signer.status,
                            "email": signer.email,
                            "signing_preference": "NEEDS_TO_SIGN",
                            "sent_at": envelope.sent_date_time,
                        }
                    )
                    if envelope.status != "voided" and envelope.status != "declined":
                        if signer.status == "completed":
                            completed += 1
                        elif signer.status in ["sent", "delivered", "created"]:
                            awaiting += 1

            if recipients_for_envelope.carbon_copies:
                for cc in recipients_for_envelope.carbon_copies:
                    recipients.append(
                        {
                            "name": cc.name,
                            "status": cc.status,
                            "email": cc.email,
                            "signing_preference": "RECEIVES_A_COPY",
                            "sent_at": envelope.sent_date_time,
                        }
                    )
                    if envelope.status != "voided" and envelope.status != "declined":
                        if cc.status == "completed":
                            completed += 1
                        elif cc.status in ["sent", "delivered", "created"]:
                            awaiting += 1

            if recipients_for_envelope.certified_deliveries:
                for certified_delivery in recipients_for_envelope.certified_deliveries:
                    recipients.append(
                        {
                            "name": certified_delivery.name,
                            "status": certified_delivery.status,
                            "email": certified_delivery.email,
                            "signing_preference": "NEEDS_TO_VIEW",
                            "sent_at": envelope.sent_date_time,
                        }
                    )
                    if envelope.status != "voided" and envelope.status != "declined":
                        if certified_delivery.status == "completed":
                            completed += 1
                        elif certified_delivery.status in [
                            "sent",
                            "delivered",
                            "created",
                        ]:
                            awaiting += 1
            envelope_for_template["completed"] = completed
            envelope_for_template["awaiting"] = awaiting
            envelopes_for_template.append(envelope_for_template)
            total_completed += completed
            total_awaiting += awaiting

    envelope_details = {
        "envelopes_for_template": envelopes_for_template,
        "total_awaiting": total_awaiting,
        "total_completed": total_completed,
    }
    return envelope_details


def _get_docusign_template_lock_details(account_id, template_id, templates_api):
    try:
        lock_info = templates_api.get_lock_with_http_info(account_id, template_id)
        logging.info("DOCUSIGN API LIMIT: get_lock %s ", lock_info[2])
        return {"is_locked": True}
    except Exception as e:
        logging.info("DOCUSIGN API LIMIT: get_lock %s", e)
        return {"is_locked": False}


def _get_docusign_template_details(
    account_id,
    template_id,
    templates_api,
    template=None,
):
    template_details = {}
    template_roles = []
    template_files = []

    if template is None:
        template_info = templates_api.get_with_http_info(
            account_id,
            template_id,
            include="documents,tabs",
        )
        template = template_info[0]
        logging.info("DOCUSIGN API LIMIT: get_template %s", template_info[2])
    recipients_for_templates = template.recipients
    if recipients_for_templates is not None:
        if recipients_for_templates.signers is not None:
            for signer in recipients_for_templates.signers:
                template_role = {
                    "role": signer.role_name,
                    "signingPreference": "Needs To Sign",
                    "routingOrder": signer.routing_order,
                    "id": signer.recipient_id,
                }
                template_roles.append(template_role)

        if recipients_for_templates.carbon_copies is not None:
            for cc in recipients_for_templates.carbon_copies:
                template_role = {
                    "role": cc.role_name,
                    "signingPreference": "Receives A Copy",
                    "routingOrder": cc.routing_order,
                    "id": cc.recipient_id,
                }
                template_roles.append(template_role)

        if recipients_for_templates.certified_deliveries is not None:
            for certified_delivery in recipients_for_templates.certified_deliveries:
                template_role = {
                    "role": certified_delivery.role_name,
                    "signingPreference": "Needs To View",
                    "routingOrder": certified_delivery.routing_order,
                    "id": certified_delivery.recipient_id,
                }
                template_roles.append(template_role)
        documents = template.documents
        if documents is not None:
            for document in documents:
                template_files.append(
                    {
                        "name": document.name,
                        "contents": document.document_base64,
                    }
                )
    template_details["documents"] = template_files
    template_details["roles"] = template_roles
    template_details["name"] = template.name
    template_details["id"] = template_id
    template_details["created"] = template.created
    template_details["last_modified"] = template.last_modified
    template_details["recipients"] = recipients_for_templates

    # determining if signing order is enabled or not
    template_details["is_signing_order_enabled"] = False
    for role in template_roles:
        if role["routingOrder"] != "1":
            template_details["is_signing_order_enabled"] = True
            break

    # sort template roles as per signing/ routing order
    if template_details["is_signing_order_enabled"]:
        template_details["roles"] = sorted(
            template_roles, key=lambda role: (int(role["routingOrder"]), role["role"])
        )

    # get template notification settings
    template_notification_settings = (
        templates_api.get_notification_settings_with_http_info(account_id, template_id)
    )
    if template_notification_settings and template_notification_settings[0]:
        template_notification_settings = template_notification_settings[0]
        template_details["template_notification_settings"] = {
            "reminders": {
                "reminder_enabled": (
                    template_notification_settings.reminders.reminder_enabled
                    if template_notification_settings.reminders
                    else "false"
                ),
                "reminder_delay": (
                    template_notification_settings.reminders.reminder_delay
                    if template_notification_settings.reminders
                    else "0"
                ),
                "reminder_frequency": (
                    template_notification_settings.reminders.reminder_frequency
                    if template_notification_settings.reminders
                    else "0"
                ),
            },
            "expirations": {
                "expire_enabled": (
                    template_notification_settings.expirations.expire_enabled
                    if template_notification_settings.expirations
                    else "true"
                ),
                "expire_after": (
                    template_notification_settings.expirations.expire_after
                    if template_notification_settings.expirations
                    else "120"
                ),
                "expire_warn": (
                    template_notification_settings.expirations.expire_warn
                    if template_notification_settings.expirations
                    else "0"
                ),
            },
        }
    else:
        template_details["template_notification_settings"] = {
            "reminders": {
                "reminder_enabled": "false",
                "reminder_delay": "0",
                "reminder_frequency": "0",
            },
            "expirations": {
                "expire_enabled": "true",
                "expire_after": "120",
                "expire_warn": "0",
            },
        }

    return template_details


def _get_docusign_template_documents(documents):
    template_files = []
    if documents is not None:
        for document in documents:
            template_files.append(
                {
                    "name": document.name,
                    "contents": document.document_base64,
                }
            )
    return template_files


def archive_contract(request):
    client_id = request.client_id
    template_id = request.data["template_id"]
    logger = request.logger
    try:
        archived_template = DocusignTemplateDetailsAccessor(client_id).archive_template(
            template_id
        )
        logger.info("Archived template - {}".format(template_id))
        return Response(
            archived_template,
            status=status.HTTP_201_CREATED,
        )

    except Exception as e:
        logger.error("EMPLOYEE BULK INSERT EXP - {}".format(e))
        return Response({"status": "FAILURE"}, status=status.HTTP_400_BAD_REQUEST)


def activate_contract(request):
    client_id = request.client_id
    template_id = request.data["template_id"]
    logger = request.logger
    try:
        archived_template = DocusignTemplateDetailsAccessor(
            client_id
        ).activate_template(template_id)
        logger.info("Archived template - {}".format(template_id))
        return Response(
            archived_template,
            status=status.HTTP_201_CREATED,
        )

    except Exception as e:
        logger.error("EMPLOYEE BULK INSERT EXP - {}".format(e))
        return Response({"status": "FAILURE"}, status=status.HTTP_400_BAD_REQUEST)


def encode_data_for_attachment(data):
    data_bytes = data.encode("utf-8")
    encoded_file = base64.b64encode(data_bytes).decode()
    return encoded_file


def _prep_data_for_bulk_send_envelopes(data):
    try:
        copies = []
        for row in data:
            copy = {}
            for obj in row:
                if obj["tabType"] == "manualprimarykey":
                    copy["everstage_envelope_id"] = obj["tabValue"]
                    continue

                if obj["roleName"] not in copy:
                    copy[obj["roleName"]] = {
                        "recipient_id": obj["recipientId"],
                        "role_name": obj["roleName"],
                        "tabs": {},
                    }
                recipient = copy[obj["roleName"]]

                if obj["tabType"] == "manualfullname":
                    recipient["full_name"] = obj["tabValue"]
                elif obj["tabType"] == "manualemailaddress":
                    recipient["email_address"] = obj["tabValue"]
                elif obj["tabType"] == "manualemailsubject":
                    recipient["email_subject"] = obj["tabValue"]
                elif obj["tabType"] == "manualemailbody":
                    recipient["email_body"] = obj["tabValue"]
                else:
                    recipient["tabs"][obj["tabId"]] = obj
            copies.append(copy)
        return copies
    except Exception as exc:
        raise Exception(
            "Format of envelopes data received from frontend is INVALID!"
        ) from exc


def _create_bulk_sending_list(tab_details, envelope_data, template_details):
    first_recipient_id = template_details["roles"][0][
        "id"
    ]  # template_details["roles"] are sorted by routingOrder

    bulk_copies = []
    for rowObj in envelope_data:
        recipients = []
        first_recipient_email_subject = None
        everstage_envelope_id = "####"
        for key, value in rowObj.items():
            if key == "everstage_envelope_id":
                everstage_envelope_id = value
                continue

            role_name = key
            recipient_details = value
            if recipient_details["recipient_id"] == first_recipient_id:
                first_recipient_email_subject = recipient_details["email_subject"]
            all_tabs = tab_details[role_name]
            tabs = []
            tab_label_set = set()
            for tab in all_tabs:
                if tab["tabType"] == "text" and tab["tabLabel"] not in tab_label_set:
                    tab_label_set.add(tab["tabLabel"])
                    tabs.append(
                        BulkSendingCopyTab(
                            initial_value=recipient_details["tabs"]
                            .get(tab["tabId"], {})
                            .get("tabValue", "-")
                            or "-",
                            tab_label=tab["tabLabel"],
                        )
                    )
            email_notification = RecipientEmailNotification(
                email_body=recipient_details["email_body"],
                email_subject=recipient_details["email_subject"],
                supported_language="en",
            )

            recipient = BulkSendingCopyRecipient(
                role_name=role_name,
                name=recipient_details["full_name"],
                email=recipient_details["email_address"],
                tabs=tabs,
                email_notification=email_notification,
            )
            recipients.append(recipient)
        bulk_copy = BulkSendingCopy(
            recipients=recipients,
            custom_fields=[
                BulkSendingCopyCustomField(
                    name="everstage_envelope_id", value=everstage_envelope_id
                )
            ],
            email_subject=first_recipient_email_subject,
        )
        bulk_copies.append(bulk_copy)

    bulk_sending_list_name = f"Everstage - {template_details['name']}"
    bulk_sending_list_name = (
        bulk_sending_list_name[:97] + "..."
        if len(bulk_sending_list_name) > 100
        else bulk_sending_list_name
    )  # max length limit of bulk_sending_list_name is 100 characters
    bulk_sending_list = BulkSendingList(
        name=bulk_sending_list_name,
        bulk_copies=bulk_copies,
    )
    return bulk_sending_list


def _transform_raw_envelope_data_to_csv(raw_envelope_data):
    df_data = {}
    # atleast 1 row is always there in raw_envelope_data
    first_row = raw_envelope_data[0]
    for tab in first_row:
        df_data[tab["tabId"]] = [tab["colHeaderName"]]

    for row in raw_envelope_data:
        for tab in row:
            df_data[tab["tabId"]].append(tab["tabValue"])

    df = pd.DataFrame(df_data)
    return df.to_csv(index=False, header=False)


def initiate_bulk_send_envelopes_task(
    client_id,
    audit,
    login_email_id,
    template_id,
    template_name,
    raw_envelope_data,
):
    """
    'raw_envelope_data' will have a structure like :-
        [
            [
                {
                    "tabId": "manually-created-id-envelope-id",
                    "tabType": "manualprimarykey",
                    "name": "EnvelopeId",
                    "tabLabel": "Envelope ID",
                    "colHeaderName": "Envelope ID",
                    "optional": "false",
                    "required": "true",
                    "tabValue": 1,
                },
                {
                    "tabId": "manually-created-id-1-name",
                    "tabType": "manualfullname",
                    "name": "FullName",
                    "tabLabel": "Name",
                    "colHeaderName": "Broker::Name",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "1",
                    "roleName": "Broker",
                    "tabValue": "Rupert Dockreay",
                },
                {
                    "tabId": "manually-created-id-1-email",
                    "tabType": "manualemailaddress",
                    "name": "EmailAddress",
                    "tabLabel": "Email",
                    "colHeaderName": "Broker::Email",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "1",
                    "roleName": "Broker",
                    "tabValue": "<EMAIL>",
                },
                {
                    "tabId": "manually-created-id-1-email-subject",
                    "tabType": "manualemailsubject",
                    "name": "EmailSubject",
                    "tabLabel": "Email Subject",
                    "colHeaderName": "Broker::Email Subject",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "1",
                    "roleName": "Broker",
                    "tabValue": "Phasellus id sapien in sapien iaculis congue.",
                },
                {
                    "tabId": "manually-created-id-1-email-body",
                    "tabType": "manualemailbody",
                    "name": "EmailBody",
                    "tabLabel": "Email Body",
                    "colHeaderName": "Broker::Email Body",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "1",
                    "roleName": "Broker",
                    "tabValue": "Donec diam neque, vestibulum eget, vulputate ut, ultrices vel, augue. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Donec pharetra, magna vestibulum aliquet ultrices, erat tortor sollicitudin mi, sit amet lobortis sapien sapien non mi. Integer ac neque.",
                },
                {
                    "tabId": "ee3af588-702d-4d0b-b1a1-9212a8d6fe37",
                    "tabType": "text",
                    "name": None,
                    "tabLabel": "Score",
                    "colHeaderName": "Broker::Score",
                    "optional": None,
                    "required": "false",
                    "documentId": "1",
                    "recipientId": "1",
                    "roleName": "Broker",
                    "tabValue": "",
                },
                {
                    "tabId": "manually-created-id-100000-name",
                    "tabType": "manualfullname",
                    "name": "FullName",
                    "tabLabel": "Name",
                    "colHeaderName": "Salesman::Name",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100000",
                    "roleName": "Salesman",
                    "tabValue": "Uriel Spratling",
                },
                {
                    "tabId": "manually-created-id-100000-email",
                    "tabType": "manualemailaddress",
                    "name": "EmailAddress",
                    "tabLabel": "Email",
                    "colHeaderName": "Salesman::Email",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100000",
                    "roleName": "Salesman",
                    "tabValue": "<EMAIL>",
                },
                {
                    "tabId": "manually-created-id-100000-email-subject",
                    "tabType": "manualemailsubject",
                    "name": "EmailSubject",
                    "tabLabel": "Email Subject",
                    "colHeaderName": "Salesman::Email Subject",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100000",
                    "roleName": "Salesman",
                    "tabValue": "Proin leo odio, porttitor id, consequat in, consequat ut, nulla.",
                },
                {
                    "tabId": "manually-created-id-100000-email-body",
                    "tabType": "manualemailbody",
                    "name": "EmailBody",
                    "tabLabel": "Email Body",
                    "colHeaderName": "Salesman::Email Body",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100000",
                    "roleName": "Salesman",
                    "tabValue": "Proin leo odio, porttitor id, consequat in, consequat ut, nulla. Sed accumsan felis. Ut at dolor quis odio consequat varius.",
                },
                {
                    "tabId": "8d55036f-9eeb-4a69-90a5-49dade2d6271",
                    "tabType": "text",
                    "name": None,
                    "tabLabel": "Score",
                    "colHeaderName": "Salesman::Score",
                    "optional": None,
                    "required": "true",
                    "documentId": "1",
                    "recipientId": "100000",
                    "roleName": "Salesman",
                    "tabValue": "187",
                },
                {
                    "tabId": "manually-created-id-100001-name",
                    "tabType": "manualfullname",
                    "name": "FullName",
                    "tabLabel": "Name",
                    "colHeaderName": "CEO::Name",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100001",
                    "roleName": "CEO",
                    "tabValue": "Leelah Purchase",
                },
                {
                    "tabId": "manually-created-id-100001-email",
                    "tabType": "manualemailaddress",
                    "name": "EmailAddress",
                    "tabLabel": "Email",
                    "colHeaderName": "CEO::Email",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100001",
                    "roleName": "CEO",
                    "tabValue": "<EMAIL>",
                },
                {
                    "tabId": "manually-created-id-100001-email-subject",
                    "tabType": "manualemailsubject",
                    "name": "EmailSubject",
                    "tabLabel": "Email Subject",
                    "colHeaderName": "CEO::Email Subject",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100001",
                    "roleName": "CEO",
                    "tabValue": "In quis justo.",
                },
                {
                    "tabId": "manually-created-id-100001-email-body",
                    "tabType": "manualemailbody",
                    "name": "EmailBody",
                    "tabLabel": "Email Body",
                    "colHeaderName": "CEO::Email Body",
                    "optional": "false",
                    "required": "true",
                    "recipientId": "100001",
                    "roleName": "CEO",
                    "tabValue": "Aenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.",
                },
            ],
            [
                ...
            ],
            .
            .
            .
            n rows,
        ]
    """
    task_id = AsyncTaskService(
        client_id=client_id,
        created_by=login_email_id,
        task=AsyncTaskConfig.BULK_SEND_ENVELOPES,
    ).run_task(
        params={
            "client_id": client_id,
            "audit": audit,
            "login_email_id": login_email_id,
            "template_id": template_id,
            "template_name": template_name,
            "raw_envelope_data": raw_envelope_data,
        },
        force_run=True,
    )
    return task_id


def bulk_send_envelopes(
    client_id,
    audit,
    login_email_id,
    template_id,
    template_name,
    raw_envelope_data,
    logger,
):
    # pylint: disable=pointless-string-statement, lost-exception, unused-argument
    response = {}

    oauth_data = DocusignAccessor(client_id).get_oauth_details(login_email_id)
    try:
        if oauth_data:
            logger.update_context(
                {"template_id": template_id, "docusign_user_id": oauth_data.email_id}
            )
            account_id = oauth_data.account_id
            logger.info(
                "BEGIN - Bulk Send Envelopes process for account- {}".format(account_id)
            )
            docusign_template_details = DocusignTemplateDetailsAccessor(
                client_id
            ).get_template_details_by_id(template_id)
            envelope_data = _prep_data_for_bulk_send_envelopes(raw_envelope_data)
            logger.info("Initial data preparation complete for Bulk Send Envelopes")
            """
                Now, 'envelope_data' will have structure like :-
                [
                    {
                        "everstage_envelope_id": "1",
                        "Broker": {
                            "recipient_id": "1",
                            "role_name": "Broker",
                            "tabs": {
                                "ee3af588-702d-4d0b-b1a1-9212a8d6fe37": {
                                    "tabId": "ee3af588-702d-4d0b-b1a1-9212a8d6fe37",
                                    "tabType": "text",
                                    "name": None,
                                    "tabLabel": "Score",
                                    "colHeaderName": "Broker::Score",
                                    "optional": None,
                                    "required": "false",
                                    "documentId": "1",
                                    "recipientId": "1",
                                    "roleName": "Broker",
                                    "tabValue": "",
                                }
                            },
                            "full_name": "Rupert Dockreay",
                            "email_address": "<EMAIL>",
                            "email_subject": "Phasellus id sapien in sapien iaculis congue.",
                            "email_body": "Donec diam neque, vestibulum eget, vulputate ut, ultrices vel, augue. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Donec pharetra, magna vestibulum aliquet ultrices, erat tortor sollicitudin mi, sit amet lobortis sapien sapien non mi. Integer ac neque.",
                        },
                        "Salesman": {
                            "recipient_id": "100000",
                            "role_name": "Salesman",
                            "tabs": {
                                "8d55036f-9eeb-4a69-90a5-49dade2d6271": {
                                    "tabId": "8d55036f-9eeb-4a69-90a5-49dade2d6271",
                                    "tabType": "text",
                                    "name": None,
                                    "tabLabel": "Score",
                                    "colHeaderName": "Salesman::Score",
                                    "optional": None,
                                    "required": "true",
                                    "documentId": "1",
                                    "recipientId": "100000",
                                    "roleName": "Salesman",
                                    "tabValue": "187",
                                }
                            },
                            "full_name": "Uriel Spratling",
                            "email_address": "<EMAIL>",
                            "email_subject": "Proin leo odio, porttitor id, consequat in, consequat ut, nulla.",
                            "email_body": "Proin leo odio, porttitor id, consequat in, consequat ut, nulla. Sed accumsan felis. Ut at dolor quis odio consequat varius.",
                        },
                        "CEO": {
                            "recipient_id": "100001",
                            "role_name": "CEO",
                            "tabs": {},
                            "full_name": "Leelah Purchase",
                            "email_address": "<EMAIL>",
                            "email_subject": "In quis justo.",
                            "email_body": "Aenean lectus. Pellentesque eget nunc. Donec quis orci eget orci vehicula condimentum.",
                        },
                    }
                    {
                        ...
                    },
                    ...upto n rows
                ]
            """
            api_client = DocusignApiService(
                client_id, login_email_id, account_id
            ).get_api_client()
            templates_api = TemplatesApi(api_client)

            # fetch tab details
            t_data_with_http_info = templates_api.list_recipients_with_http_info(
                account_id,
                template_id,
                include_anchor_tab_locations=True,
                include_extended=True,
                include_tabs=True,
            )
            tab_details = _get_template_tab_details(t_data_with_http_info[0])

            # get docusign template details
            template_details = _get_docusign_template_details(
                account_id, template_id, templates_api
            )
            template_name = template_details["name"]

            # create bulk copies and bulk sending list
            bulk_envelopes_api = BulkEnvelopesApi(api_client)
            bulk_sending_list = _create_bulk_sending_list(
                tab_details, envelope_data, template_details
            )

            bulk_list = bulk_envelopes_api.create_bulk_send_list(
                account_id=account_id, bulk_sending_list=bulk_sending_list
            )
            bulk_list_id = bulk_list.list_id
            logger.info("Bulk Copies and Bulk Sending List created")
            # creating custom fields
            template_id_custom_field = TextCustomField(
                name="template_id",
                value=template_id,
                show="false",
                required="true",
            )
            bulk_list_id_custom_field = TextCustomField(
                name="mailing_list_id",
                value=bulk_list_id,
                show="false",
                required="true",
            )
            everstage_envelope_id_custom_field = TextCustomField(
                name="everstage_envelope_id",
                show="false",
                required="true",
            )
            primary_recipient_role = TextCustomField(
                name="primary_recipient_role",
                value=docusign_template_details.primary_recipient_role,
                show="false",
                required="true",
            )
            fiscal_year = TextCustomField(
                name="fiscal_year",
                value=docusign_template_details.fiscal_year,
                show="false",
                required="true",
            )
            custom_fields = CustomFields(
                text_custom_fields=[
                    template_id_custom_field,
                    bulk_list_id_custom_field,
                    everstage_envelope_id_custom_field,
                    primary_recipient_role,
                    fiscal_year,
                ]
            )
            logger.info("Custom Fields for Bulk List created")

            # create an envelope
            envelopes_api = EnvelopesApi(api_client)
            envelope_definition = EnvelopeDefinition(
                template_id=template_id,
                custom_fields=custom_fields,
                status="created",
                envelope_id_stamping="true",
                recipients_lock=False,
                brand_lock=False,
                message_lock=False,
            )
            envelope = envelopes_api.create_envelope(
                account_id=account_id, envelope_definition=envelope_definition
            )
            envelope_id = envelope.envelope_id
            logger.info("Envelope Defined (Draft Envelope created)")

            # initiate bulk send
            logger.info("Initiating Bulk Send request")
            bulk_send_request = BulkSendRequest(envelope_or_template_id=envelope_id)
            batch = bulk_envelopes_api.create_bulk_send_request(
                account_id=account_id,
                bulk_send_list_id=bulk_list_id,
                bulk_send_request=bulk_send_request,
            )
            batch_id = batch.batch_id

            # Confirm successful batch send
            bulk_send_batch_status = bulk_envelopes_api.get_bulk_send_batch_status(
                account_id=account_id, bulk_send_batch_id=batch_id
            )

            # now wait for docusign to process all the queued envelopes and send emails
            # setting max_waiting_time as (no. of envelopes * first_order_recipients_count * 0.5) + 60 extra seconds
            first_order_recipients_count = 1
            for i in range(1, len(template_details["roles"])):
                # template_details["roles"] is always sorted by routingOrder
                # first order recipients can have routing order other than 1
                if int(template_details["roles"][i]["routingOrder"]) != int(
                    template_details["roles"][0]["routingOrder"]
                ):
                    break
                first_order_recipients_count += 1

            max_waiting_time = (
                int(bulk_send_batch_status.batch_size)
                * first_order_recipients_count  # count of recipients who will be emailed in first order
                * 0.5  # assuming 500ms for each email in worst case
                + 60  # to support small batches
            )
            logger.info(
                f"Batch size: {bulk_send_batch_status.batch_size}, First order recipients count: {first_order_recipients_count}, Hence max_waiting_time: {max_waiting_time} sec."
            )
            time_waited = 0
            while (
                int(bulk_send_batch_status.queued) != 0
                and time_waited < max_waiting_time
            ):
                # sleep time can range from [min 10 sec. to max 90 sec.]
                sleep_time = min(max(int(bulk_send_batch_status.queued), 10), 90)
                logger.info(
                    f"Envelopes in queue: {bulk_send_batch_status.queued}, hence sleeping for {sleep_time} sec."
                )

                time.sleep(sleep_time)
                time_waited += sleep_time
                bulk_send_batch_status = bulk_envelopes_api.get_bulk_send_batch_status(
                    account_id=account_id, bulk_send_batch_id=batch_id
                )

            if int(bulk_send_batch_status.queued) != 0:
                logger.info(
                    f"Max waiting time elapsed! Envelopes still in queue: {bulk_send_batch_status.queued}. Not waiting anymore, proceeding ahead..."
                )

            result = bulk_send_batch_status.to_dict()
            result["submitted_date"] = format_docusign_time_to_utc(
                result["submitted_date"]
            )

            logger.update_context(
                {
                    "bulk_send_batch_status": result,
                    "max_waiting_time_permitted": max_waiting_time,
                    "actual_time_waited": time_waited,
                }
            )
            logger.info("SUCCESSFUL - Bulk Send Request (Bulk Envelopes are sent)")

            # success
            response = {
                "status": "SUCCESS",
                "data": {
                    "bulk_send_batch_status": result,
                    "max_waiting_time_permitted": max_waiting_time,
                    "actual_time_waited": time_waited,
                },
            }
        else:
            logger.error("Oauth details not present for the user")
            response = {
                "status": "FAILURE",
                "data": {
                    "error": "Oauth details not present for the user",
                },
            }
    except ApiException as err:
        error_body_json = err and hasattr(err, "body") and err.body
        try:
            error_body = json.loads(error_body_json)
        except json.decoder.JSONDecodeError:
            error_body = {}
        error_code = (
            error_body and "errorCode" in error_body and error_body["errorCode"]
        )
        error_message = error_body and "message" in error_body and error_body["message"]
        logger.error(
            "Bulk Send Envelopes Failed With Api Exception - {}".format(error_body)
        )
        logger.error("Bulk Send Envelopes Failed error code - {}".format(error_code))
        logger.error(
            "Bulk Send Envelopes Failed error message - {}".format(error_message)
        )
        response = {
            "status": "FAILURE",
            "data": {
                "error_code": error_code,
                "error_body": error_body,
                "message": error_message,
                "error": f"{error_code} - {error_message}",
            },
        }
    except Exception as err:
        logger.error("Bulk Send Envelopes Failed with Exception - {}".format(err))
        response = {
            "status": "FAILURE",
            "data": {
                "error": str(err),
            },
        }
    finally:
        # process for sending result email to user here
        try:
            message = Mail(
                from_email=Email(email="<EMAIL>", name="Everstage"),
                to_emails=login_email_id,
            )

            login_user = EmployeeAccessor(client_id).get_employee(login_email_id)
            full_name = (
                f"{login_user.first_name} {login_user.last_name}"
                if login_user
                else "Unknown"
            )

            dynamic_template_data = {
                "name": full_name,
                "contract_name": template_name,
            }

            envelope_data_csv = _transform_raw_envelope_data_to_csv(raw_envelope_data)
            message.add_attachment(
                Attachment(
                    FileContent(encode_data_for_attachment(envelope_data_csv)),
                    FileName(f"{template_name} - envelopes data.csv"),
                    FileType("text/csv"),
                    Disposition("attachment"),
                )
            )

            if response["status"] == "SUCCESS":
                dynamic_template_data.update(response["data"]["bulk_send_batch_status"])
                message.template_id = "d-fc400bd861074b3b95e415ad612834d6"
                message.dynamic_template_data = dynamic_template_data
                envelopes_status = list_docusign_envelopes_status(
                    email_id=login_email_id,
                    account_id=account_id,
                    envelopes_api=envelopes_api,
                    template_id=template_id,
                    custom_field=f"mailing_list_id={bulk_list_id}",
                    logger=logger,
                )

                df = pd.DataFrame(envelopes_status)
                envelope_status_csv = df.to_csv(index=False, header=False)
                message.add_attachment(
                    Attachment(
                        FileContent(encode_data_for_attachment(envelope_status_csv)),
                        FileName(f"{template_name} - envelopes status.csv"),
                        FileType("text/csv"),
                        Disposition("attachment"),
                    )
                )
                email_type = "Bulk Send Envelopes Succcess"

                # set docusign user id in cc; if login user id and docusign user id are not same
                if login_email_id != str(oauth_data.email_id):
                    message.add_cc(str(oauth_data.email_id))

            else:
                dynamic_template_data.update(
                    {"error_message": response["data"]["error"]}
                )
                message.template_id = "d-119ae95328f445688c210e133f751839"
                message.dynamic_template_data = dynamic_template_data
                email_type = "Bulk Send Envelopes Failure"
            subscription_plan = get_client_subscription_plan(client_id)
            send_email_queue_name = get_queue_name_respect_to_task_group(
                client_id, subscription_plan, TaskGroupEnum.EMAILNOTIFICATION.value
            )
            send_email.si(message=message, type=email_type).set(
                queue=send_email_queue_name
            ).apply_async()
            response["data"]["result_email_to_sender"] = {
                "status": "SUCCESS",
                "data": {
                    "email_details": message.get(),
                },
            }

            logger.info(
                "END - Result Email is sent. Bulk Send Envelopes process completed."
            )
        except Exception as exc:
            response["data"]["result_email_to_sender"] = {
                "status": "FAILURE",
                "data": {
                    "error": str(exc),
                },
            }
            logger.error(f"Result email failed in process with error - {str(exc)}")

        return response


def get_envelope_data(client_id, email_id, envelope_id, logger=None):
    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    logger.update_context({"envelope_id": envelope_id, "docusign_user_id": email_id})
    if oauth_data:
        account_id = oauth_data.account_id
        logger.info("BEGIN - Get recipient data for envelope- {}".format(envelope_id))
        api_client = DocusignApiService(
            client_id, email_id, account_id
        ).get_api_client()
        envelope_api = EnvelopesApi(api_client)
        try:
            envelope_info = envelope_api.list_recipients_with_http_info(
                account_id=account_id,
                envelope_id=envelope_id,
                include_tabs=True,
                include_metadata=True,
            )
            logging.info("DOCUSIGN API LIMIT: list_recipients %s", envelope_info[2])
            envelope_data = envelope_info[0]
            envelope_details = {}
            if envelope_data:
                if envelope_data.signers:
                    for s in envelope_data.signers:
                        columns_map = {}
                        signer_data = {
                            "email": s.email,
                            "name": s.name,
                            "recipient_id": s.recipient_id,
                            "recipient_type": s.recipient_type,
                            "role_name": s.role_name,
                            "status": s.status,
                        }
                        signer_tabs = s.tabs
                        tabs = []
                        if signer_tabs:
                            for _, tabs_arr in signer_tabs.__dict__.items():
                                if tabs_arr and isinstance(tabs_arr, list):
                                    for tab in tabs_arr:
                                        if tab.tab_type in [
                                            "text",
                                            "fullname",
                                            "emailaddress",
                                            "number",
                                        ]:
                                            tabs.append(
                                                {
                                                    "tab_name": tab.name,
                                                    "tab_label": tab.tab_label,
                                                    "tab_type": tab.tab_type,
                                                    "value": tab.value,
                                                }
                                            )
                                            if tab.name is not None:
                                                columns_map[tab.name] = tab.value
                        signer_data["tabs"] = tabs
                        signer_data["columns_map"] = columns_map
                        envelope_details[s.role_name] = signer_data
                else:
                    logger.info("No signers for envelope- {}".format(envelope_id))
                cc_viewer = []
                if envelope_data.carbon_copies:
                    cc_viewer = envelope_data.carbon_copies
                if envelope_data.certified_deliveries:
                    cc_viewer = cc_viewer + envelope_data.certified_deliveries
                for s in cc_viewer:
                    signer_data = {
                        "email": s.email,
                        "name": s.name,
                        "recipient_id": s.recipient_id,
                        "recipient_type": s.recipient_type,
                        "role_name": s.role_name,
                        "status": s.status,
                    }
                    envelope_details[s.role_name] = signer_data
            logger.info("END - Get recipient data for envelope- {}".format(envelope_id))
            return envelope_details
        except Exception as e:
            logger.error("Get Envelope data Failed - {}".format(e))
            return Response({"status": "FAILURE"}, status=status.HTTP_400_BAD_REQUEST)
    else:
        raise Exception("Oauth details not present for the user")


def get_template_tabs_for_datasheet_mapping(
    client_id, email_id, template_id, template_tab_details=None, logger=None
):
    logger = (
        logger
        if logger
        else LogWithContext({"template_id": template_id, "email_id": email_id})
    )
    logger.info("BEGIN: Get template tabs for datasheet mapping")
    template_tabs_list = []
    template_tabs_list.append(
        {
            "tabId": "manually-created-id-" + "envelope-id",
            "tabType": "manualprimarykey",
            "name": "EnvelopeId",
            "tabLabel": "Envelope ID",
            "colHeaderName": "Envelope ID",
            "optional": "false",
            "required": "true",
        }
    )

    if template_tab_details is None:
        template_tab_details = get_template_details(
            client_id, email_id, template_id, logger
        )
    # pylint: disable=unused-variable
    for (
        role_name,
        tabs_list,
    ) in template_tab_details.items():
        template_tabs_list += tabs_list

    logger.info("END: Get template tabs for datasheet mapping")
    return template_tabs_list


def export_template_tabs_csv(client_id, template_id, template_name, email_id):
    logger = LogWithContext({"template_id": template_id, "email_id": email_id})
    logger.info("BEGIN: export template tabs as csv file")
    template_tabs_list = get_template_tabs_for_datasheet_mapping(
        client_id, email_id, template_id
    )

    # all fields(column names) must be unique
    df_data = {tab["colHeaderName"]: [] for tab in template_tabs_list}
    df = pd.DataFrame(df_data)
    data_as_csv = df.to_csv(index=False)
    logger.info("END: export template tabs as csv file")
    return FileResponse(
        data_as_csv, as_attachment=True, filename=f"{template_name} - Template.csv"
    )


def download_envelope_document(request):
    client_id = request.client_id
    email_id = request.data["email_id"]
    envelope_id = request.data["envelope_id"]
    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    logger = request.logger
    logger.update_context({"envelope_id": envelope_id, "email_id": email_id})
    if oauth_data:
        account_id = oauth_data.account_id
        logger.info("Get download data for envelope- {}".format(envelope_id))
        api_client = DocusignApiService(
            client_id, email_id, account_id
        ).get_api_client()
        envelope_api = EnvelopesApi(api_client)
        try:
            temp_file_path_info = envelope_api.get_document_with_http_info(
                account_id=account_id,
                document_id="archive",
                envelope_id=envelope_id,
            )
            logger.info("File path for envelope retrieved successfully")
            logging.info(
                "DOCUSIGN API LIMIT: download_get_document %s", temp_file_path_info[2]
            )
            temp_file_path = temp_file_path_info[0]
            response = FileResponse(
                open(temp_file_path, "rb"),
                as_attachment=True,
                filename="EnvelopeData.zip",
            )
            return response
        except Exception as e:
            logger.error("Download envelope Failed - {}".format(e))
            return Response({"status": "FAILURE"}, status=status.HTTP_400_BAD_REQUEST)
    else:
        raise Exception("Oauth details not present for the user")


def resend_envelope(request):
    client_id = request.client_id
    email_id = request.data["email_id"]
    envelope_id = request.data["envelope_id"]
    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    logger = request.logger
    logger.update_context({"envelope_id": envelope_id, "email_id": email_id})
    if oauth_data:
        account_id = oauth_data.account_id
        logger.info("Resend envelope for id - {}".format(envelope_id))
        api_client = DocusignApiService(
            client_id, email_id, account_id
        ).get_api_client()
        env = EnvelopeDefinition(status="sent")
        try:
            results_info = EnvelopesApi(api_client).update_with_http_info(
                account_id,
                envelope_id,
                envelope=env,
                resend_envelope=True,
            )
            logging.info("DOCUSIGN API LIMIT: resend_envelope %s", results_info[2])
            logger.info("Resend envelope success")
            return Response(
                {"status": "SUCCESS"},
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            logger.error("Resend envelope Failed - {}".format(e))
            return Response({"status": "FAILURE"}, status=status.HTTP_400_BAD_REQUEST)
    else:
        raise Exception("Oauth details not present for the user")


def void_envelope(request):
    client_id = request.client_id
    email_id = request.data["email_id"]
    envelope_id = request.data["envelope_id"]
    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    logger = request.logger
    logger.update_context({"envelope_id": envelope_id, "email_id": email_id})
    if oauth_data:
        account_id = oauth_data.account_id
        logger.info("Void envelope for id - {}".format(envelope_id))
        api_client = DocusignApiService(
            client_id, email_id, account_id
        ).get_api_client()
        login_user = EmployeeAccessor(client_id).get_employee(email_id)
        full_name = (
            f"{login_user.first_name} {login_user.last_name}"
            if login_user
            else "someone"
        )
        env = EnvelopeDefinition(
            status="voided", voided_reason=f"Voided by {full_name} from Everstage"
        )
        try:
            results_info = EnvelopesApi(api_client).update_with_http_info(
                account_id, envelope_id, envelope=env
            )
            logging.info("DOCUSIGN API LIMIT: void_envelope %s", results_info[2])
            logger.info("Void envelope success")
            return Response(
                {"status": "SUCCESS"},
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            logger.error("Void envelope Failed - {}".format(e))
            return Response({"status": "FAILURE"}, status=status.HTTP_400_BAD_REQUEST)
    else:
        raise Exception("Oauth details not present for the user")


def list_docusign_envelopes_status(
    email_id,
    account_id,
    envelopes_api,
    template_id,
    custom_field=None,
    envelope_ids=None,
    include=None,
    from_date="2020/01/01",
    to_date="",
    start_position=0,
    count="",
    order="desc",
    order_by="sent",
    include_purge_information="true",
    logger=None,
):
    """
    Returns requested envelopes's status as a 2D-list
    """
    if envelope_ids is None:
        envelope_ids = []
    if include is None:
        include = []
    logger = (
        logger
        if logger
        else LogWithContext({"template_id": template_id, "email_id": email_id})
    )
    logger.info(f"BEGIN: list envelopes status for template_id: {template_id}")
    all_envelopes_info = envelopes_api.list_status_changes_with_http_info(
        account_id,
        custom_field=custom_field or f"template_id={template_id}",
        envelope_ids=",".join(envelope_ids),
        include=",".join(
            ["recipients", "folders", "custom_fields", "extensions"] + include
        ),
        from_date=from_date,
        to_date=to_date,
        start_position=start_position,
        count=count,
        order=order,
        order_by=order_by,
        include_purge_information=include_purge_information,
    )
    all_envelopes = all_envelopes_info[0].envelopes or []

    status_data = []
    status_data.append(
        [
            "Everstage Envelope ID",
            "Docusign Envelope ID",
            "Recipient Name",
            "Recipient Email",
            "Recipient Routing Order",
            "Recipient Type",
            "Envelope Status",
            "Recipient Status",
            "Envelope First Sent Date",
            "Envelope Created Date",
            "Envelope Last Sent Date",
            "Completed Date",
            "Voided Date",
            "Voided Reason",
            "Recipient Viewed Date",
            "Recipient Signed Date",
            "Recipient Declined Date",
            "Recipient Declined Reason",
            "Envelope RecipientId",
            "Envelope Subject",
            "Sender",
        ]
    )

    for envelope in all_envelopes:
        everstage_envelope_id_custom_field = list(
            filter(
                lambda custom_field: custom_field.name == "everstage_envelope_id",
                envelope.custom_fields.text_custom_fields,
            )
        )
        everstage_envelope_id = "####"
        if len(everstage_envelope_id_custom_field) > 0:
            everstage_envelope_id = everstage_envelope_id_custom_field[0].value

        recipients = (
            envelope.recipients.signers
            + envelope.recipients.carbon_copies
            + envelope.recipients.certified_deliveries
        )

        declined_reason = None
        if envelope.status == "declined":
            for recipient in recipients:
                declined_reason = recipient.declined_reason or declined_reason

        for recipient in recipients:
            if recipient.recipient_type == "signer":
                recipient_type = "Needs To Sign"
            elif recipient.recipient_type == "carboncopy":
                recipient_type = "Receives a Copy"
            elif recipient.recipient_type == "certifieddelivery":
                recipient_type = "Needs To View"
            else:
                recipient_type = recipient.recipient_type.capitalize()

            row = [
                everstage_envelope_id,  # Everstage Envelope ID
                envelope.envelope_id,  # Docusign Envelope ID
                recipient.name,  # Recipient Name
                recipient.email,  # Recipient Email
                recipient.routing_order,  # Recipient Routing Order
                recipient_type,  # Recipient Type
                envelope.status.capitalize(),  # Envelope Status
                recipient.status.capitalize(),  # Recipient Status
                format_docusign_time_to_utc(
                    envelope.initial_sent_date_time
                ),  # Envelope First Sent Date
                format_docusign_time_to_utc(
                    envelope.created_date_time
                ),  # Envelope Created Date
                format_docusign_time_to_utc(
                    envelope.sent_date_time
                ),  # Envelope Last Sent Date
                format_docusign_time_to_utc(
                    envelope.completed_date_time
                ),  # Completed Date
                format_docusign_time_to_utc(envelope.voided_date_time),  # Voided Date
                envelope.voided_reason,  # Voided Reason
                format_docusign_time_to_utc(
                    recipient.delivered_date_time
                ),  # Recipient Viewed Date
                format_docusign_time_to_utc(
                    recipient.signed_date_time
                ),  # Recipient Signed Date
                format_docusign_time_to_utc(
                    recipient.declined_date_time
                ),  # Recipient Declined Date
                recipient.declined_reason,  # Recipient Declined Reason
                recipient.recipient_id,  # Envelope RecipientId
                envelope.email_subject,  # Envelope Subject
                envelope.sender.user_name,  # Sender
            ]
            status_data.append(row)

    logger.info(f"END: list envelopes status for template_id: {template_id}")
    return status_data


def export_envelopes_status_as_csv(
    client_id, template_id, template_name, email_id, logger
):
    oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
    logger.info(
        f"BEGIN: export envelopes status as csv for template: {email_id, oauth_data}"
    )

    if oauth_data:
        account_id = oauth_data.account_id
        docusign_service = DocusignApiService(client_id, email_id, account_id)
        api_client = docusign_service.get_api_client()
        envelopes_api = EnvelopesApi(api_client)

        envelopes_status = list_docusign_envelopes_status(
            email_id=email_id,
            account_id=account_id,
            envelopes_api=envelopes_api,
            template_id=template_id,
            logger=logger,
        )
        df = pd.DataFrame(envelopes_status)
        data_as_csv = df.to_csv(index=False, header=False)
        logger.info(
            f"END: export envelopes status as csv for template: {template_id} - {template_name}"
        )
        return FileResponse(
            data_as_csv, as_attachment=True, filename=f"{template_name} - status.csv"
        )
    else:
        raise Exception("Oauth details not present for the user")


def export_contract_status(
    client_id, email_id, to_email_id, fiscal_year, is_archived, logger
) -> STATUS_CODE:
    """
    return : STATUS_CODE.SUCCESS | STATUS_CODE.FAILED
    """
    # fetching data from docusign via get_contract_status_csv function.
    result = get_contract_status_csv(
        client_id, email_id, fiscal_year, is_archived, logger
    )

    if (
        result["status"]["value"] == STATUS_CODE.FAILED
        and result["data"]["error"] is True
    ):
        logger.error(result["data"]["value"])
        return STATUS_CODE.FAILED

    data_as_csv = result["data"]["value"]
    # emailing the csv to to_email_id via sendgrid.
    try:
        message = Mail(
            from_email=Email(email="<EMAIL>", name="Everstage"),
            to_emails=to_email_id,
            subject="Contract Status Export",
        )
        attachment_file = Attachment(
            FileContent(encode_data_for_attachment(data_as_csv)),
            FileName("contracts-status.csv"),
            FileType("text/csv"),
            Disposition("attachment"),
        )
        message.attachment = [attachment_file]
        subscription_plan = get_client_subscription_plan(client_id)
        send_email_queue_name = get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.EMAILNOTIFICATION.value
        )
        dynamic_template_data = {
            "name": "everstage_user",  # some value is required for "name" key in send_email function
            "Date_of_download": timezone.now().strftime("%d-%m-%Y"),
            "Fiscal_year": fiscal_year,
        }
        message.template_id = (
            "d-96256d1b03f845288f6c90a78245eeda"  # success email template
        )
        message.dynamic_template_data = dynamic_template_data
        subscription_plan = get_client_subscription_plan(client_id)
        send_email_queue_name = get_queue_name_respect_to_task_group(
            client_id, subscription_plan, TaskGroupEnum.EMAILNOTIFICATION.value
        )
        send_email.si(message=message, type="Contracts Status Email Export").set(
            queue=send_email_queue_name
        ).apply_async(compression="lzma", serializer="pickle")

        return STATUS_CODE.SUCCESS

    except Exception:
        logger.error("Cannot send email with contracts status csv file")
        traceback.print_exc()
        return STATUS_CODE.FAILED


def initiate_export_contracts_csv(
    client_id,
    login_email_id,
    to_email_id,
    fiscal_year,
    is_archived,
    audit,
):
    """
    Initiates exporting contracts as csv file and send as email
    """
    login_user = EmployeeAccessor(client_id).get_employee(login_email_id)
    task_id = AsyncTaskService(
        client_id=client_id,
        created_by=login_email_id,
        task=AsyncTaskConfig.CONTRACTS_STATUS_EXPORT,
    ).run_task(
        params={
            "client_id": client_id,
            "audit": audit,
            "login_email_id": login_email_id,
            "to_email_id": to_email_id,
            "fiscal_year": fiscal_year,
            "is_archived": is_archived,
            "user_name": {
                "first_name": login_user.first_name,
                "last_name": login_user.last_name,
            },
            "custom_object_name": "Not Applicable",
        },
        force_run=True,
    )

    return task_id


def get_contract_status_csv(client_id, email_id, fiscal_year, is_archived, logger):
    """
    return : {
        status : {
            value : STATUS_CODE.SUCCESS | STATUS_CODE.FAILED
        }
        data : {
            error : True | False,
            value : "error string" | CSV
        }
    }
    """
    try:
        oauth_data = DocusignAccessor(client_id).get_oauth_details(email_id)
        logger.info(f"BEGIN: export all contracts status as csv: {email_id}")
        headers = [
            "Contract Name",
            "Contract Id",
            "Everstage Envelope ID",
            "Docusign EnvelopeId",
            "Fiscal Year",
            "Primary Recipient Role",
            "Primary Recipient Name",
            "Primary Recipient Email",
            "Contract Status",
            "Contract Envelope First Created Date",
            "Contract Envelope Completed Date",
            "Voided Date",
            "Sender",
            "Commission Plan Name",
            "Number Of Recipients",
        ]
        if oauth_data:
            account_id = oauth_data.account_id  # get account id from oauth details
            docusign_service = DocusignApiService(client_id, email_id, account_id)
            api_client = docusign_service.get_api_client()  # get api client
            docusign_details = DocusignAccessor(client_id).get_docusign_details(
                email_id
            )  # get docusign details
            templates = DocusignTemplateDetailsAccessor(
                client_id
            ).get_filtered_templates(  # get all templates
                account_id=docusign_details.account_id,
                fiscal_year=fiscal_year,
                is_archived=is_archived,
            )
            if len(templates) == 0:
                return {
                    "status": {
                        "value": STATUS_CODE.FAILED,
                    },
                    "data": {"error": True, "value": "No templates found for the user"},
                }
            # get template ids and template model details from DB
            template_ids = []
            template_model_details = {}
            plan_ids = []
            for template in templates:
                template_ids.append(template.template_id)
                template_model_details[template.template_id] = template
                plan_ids.append(template.plan_id)
            # API from Docusign
            templates_api = TemplatesApi(api_client)
            envelopes_api = EnvelopesApi(api_client)
            docusign_templates_info = templates_api.list_templates_with_http_info(
                account_id, include="recipients", order_by="modified", order="desc"
            )
            docusign_templates = docusign_templates_info[0].envelope_templates
            logging.info(
                "DOCUSIGN API LIMIT: list_templates %s", docusign_templates_info[2]
            )
            # all_contracts is the return value (csv like format)
            all_contracts = []
            all_contracts.append(headers)
            plan_names = CommissionPlanAccessor(client_id).get_plans_by_ids(plan_ids)
            all_plan_names = {str(x.plan_id): x.plan_name for x in plan_names}

            # iterate over all templates
            for template in docusign_templates:
                envelope_data = []
                # check if the template is present in the DB
                if template.template_id in template_ids:
                    logger.info(
                        "BEGIN: Get template details for template with Id - {}".format(
                            template.template_id
                        )
                    )
                    envelope_ids = []
                    all_envelopes_info = (
                        envelopes_api.list_status_changes_with_http_info(
                            account_id,
                            custom_field=f"template_id={template.template_id}",
                            envelope_ids=",".join(envelope_ids),
                            include=",".join(
                                ["recipients", "folders", "custom_fields", "extensions"]
                            ),
                            from_date="2020/01/01",
                            to_date="",
                            start_position=0,
                            count="",
                            order="desc",
                            order_by="sent",
                            include_purge_information="true",
                        )
                    )
                    all_envelopes = all_envelopes_info[0].envelopes or []
                    for envelope in all_envelopes:
                        # get everstage_envelope_id from custom fields
                        everstage_envelope_id_custom_field = list(
                            filter(
                                lambda custom_field: custom_field.name
                                == "everstage_envelope_id",
                                envelope.custom_fields.text_custom_fields,
                            )
                        )
                        # if everstage_envelope_id is not present, set it to ####
                        everstage_envelope_id = "####"
                        if len(everstage_envelope_id_custom_field) > 0:
                            everstage_envelope_id = everstage_envelope_id_custom_field[
                                0
                            ].value
                        # get all recepients of the envelope
                        recipients = (
                            envelope.recipients.signers
                            + envelope.recipients.carbon_copies
                            + envelope.recipients.certified_deliveries
                        )
                        # get primary recipient name and email
                        primary_recipient_role = template_model_details[
                            template.template_id
                        ].primary_recipient_role
                        primary_recipient_name = ""
                        primary_recipient_email = ""
                        for recipient in recipients:
                            if recipient.role_name == primary_recipient_role:
                                # if the current recipient role is the primary recipient role
                                primary_recipient_name = recipient.name
                                primary_recipient_email = recipient.email
                                break
                        # convert format to 01-jun-2023, 10:00 TZ
                        envelope_data.append(
                            {
                                "everstage_envelope_id": everstage_envelope_id,  # Everstage Envelope ID
                                "docusign_envelope_id": envelope.envelope_id,  # Docusign Envelope ID
                                "primary_recipient_name": primary_recipient_name,
                                "primary_recipient_email": primary_recipient_email,
                                "sender": envelope.sender.user_name,  # Sender
                                "recipient_count": len(recipients),
                                "envelope_status": envelope.status.capitalize(),  # Envelope Status
                                "completed_date": format_docusign_time_to_utc(
                                    envelope.completed_date_time
                                ),  # Completed Date
                                "voided_date": format_docusign_time_to_utc(
                                    envelope.voided_date_time
                                ),  # Voided Date
                            }
                        )

                    # add all the envelope data to all_contracts
                    for envelope_data_item in envelope_data:
                        all_contracts.append(
                            [
                                template.name,  # Contract Name
                                template.template_id,  # Contract Id
                                envelope_data_item[
                                    "everstage_envelope_id"
                                ],  # Everstage Envelope ID
                                envelope_data_item[
                                    "docusign_envelope_id"
                                ],  # Docusign Envelope ID
                                template_model_details[
                                    template.template_id
                                ].fiscal_year,  # Fiscal Year
                                template_model_details[  # Primary Recipient Role
                                    template.template_id
                                ].primary_recipient_role,
                                envelope_data_item[
                                    "primary_recipient_name"
                                ],  # Primary Recipient Name
                                envelope_data_item[
                                    "primary_recipient_email"
                                ],  # Primary Recipient Email
                                envelope_data_item[
                                    "envelope_status"
                                ],  # Contract Status
                                format_docusign_time_to_utc(template.created),
                                envelope_data_item[
                                    "completed_date"
                                ],  # Contract Envelope completed date
                                envelope_data_item["voided_date"],  # Voided date
                                envelope_data_item["sender"],  # Sender
                                all_plan_names.get(  # Commission plan name
                                    template_model_details[
                                        template.template_id
                                    ].plan_id,
                                    None,
                                ),
                                envelope_data_item[
                                    "recipient_count"
                                ],  # Number of recipients
                            ]
                        )
                    logger.info(
                        "END: Get template details for template with Id - {}".format(
                            template.template_id
                        )
                    )

            data_frame = pd.DataFrame(all_contracts)
            data_as_csv = data_frame.to_csv(index=False, header=False)
            return {
                "status": {"value": STATUS_CODE.SUCCESS},
                "data": {"error": False, "value": data_as_csv},
            }
        else:
            return {
                "status": {"value": STATUS_CODE.FAILED},
                "data": {
                    "error": True,
                    "value": "Oauth details not present for the user",
                },
            }

    except Exception as exc:
        logger.error("Cannot export contracts status as csv file")
        traceback.print_exc()
        return {
            "status": {"value": STATUS_CODE.FAILED},
            "data": {"error": True, "value": str(exc)},
        }


def get_docusign_token_cache_key(email_id, account_id):
    return f"docusign_token##{email_id}##{account_id}"
