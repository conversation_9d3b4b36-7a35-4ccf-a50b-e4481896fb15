# pylint: disable=redefined-outer-name
import logging
import traceback
import uuid
from datetime import datetime
from typing import Optional

import pydash
import pytz
from deepdiff.diff import DeepDiff
from django.core.exceptions import ObjectDoesNotExist
from django.db import OperationalError, transaction
from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response
from sqlparse.exceptions import SQLParseError

import commission_engine.utils.report_utils as report_utils
import interstage_project.utils as iputils
from commission_engine.accessors.client_accessor import (
    get_client_databook_expression_designer_version,
    get_client_is_datasheet_sharded,
    get_client_subscription_plan,
    should_insert_meta_data_to_vec_db,
)
from commission_engine.accessors.custom_object_accessor import (
    CustomObjectAccessor,
    CustomObjectVariableAccessor,
)
from commission_engine.accessors.databook_accessor import (
    DatabookAccessor,
    DatasheetAccessor,
    DatasheetFilterAccessor,
    DatasheetPermissionsAccessor,
    DatasheetVariableAccessor,
)
from commission_engine.accessors.ever_object_accessor import (
    EverObjectAccessor,
    EverObjectVariableAccessor,
)
from commission_engine.accessors.hris_integration_accessor import HrisConfigAccessor
from commission_engine.accessors.skd_pkd_map_accessor import DbkdPkdMapAccessor
from commission_engine.custom_exceptions.ast_exceptions import VariableNotFoundException
from commission_engine.database.snowflake_query_utils import (
    create_datasheet_data_table,
    delete_transformation_result_tables,
    invalidate_data_datasheet,
)
from commission_engine.models.databook_models import Datasheet
from commission_engine.serializers.databook_serializers import (
    DatabookSerializer,
    DatasheetFilterSerializer,
    DatasheetSerializer,
    DatasheetVariableSerializer,
)
from commission_engine.services.client_feature_service import has_feature
from commission_engine.services.commission_calculation_service.reference_data_type import (
    get_data_types,
    get_datatype_id_name_map,
)
from commission_engine.services.databook_etl_sync_status_service import (
    current_all_locked_datasheets,
)
from commission_engine.services.datasheet_data_services import datasheet_validation
from commission_engine.services.datasheet_data_services.variable_extractor import (
    VariableExtractor,
)
from commission_engine.services.expression_designer import (
    AstMetaContext,
    ContextMetaDataModel,
    DatasheetContext,
    DatasheetCriteriaTypes,
    FunctionContext,
    TokenTypesV2,
    ValidationContextModel,
    add_meta_to_advanced_filter_infix,
    add_meta_to_calculated_fields,
    convert_window_func_cf_variables,
    get_expression_format,
)
from commission_engine.services.hris_integration_services.hris_integration_services import (
    validate_variables_used_in_hris,
)
from commission_engine.types import HierarchyMetaData, HierarchyUtils
from commission_engine.utils import databook_utils
from commission_engine.utils.criteria_calculator_utils import create_ast
from commission_engine.utils.general_data import (
    COMMISSION_TYPE,
    DATASHEET_FILTER_TYPE,
    DATASHEET_INVALID_REASON,
    DATASHEET_SOURCE,
    DATASHEET_TRANSFORMATIONS,
    SYNC_OBJECT,
    DatabookDeleteError,
    DatasheetDeleteError,
    SegmentEvents,
    SegmentProperties,
)
from commission_engine.utils.transformation_models import (
    GetUserPropertiesTransformationMeta,
)
from everstage_ddd.datasheet import ETLSpecTransformer
from everstage_ddd.global_search.meta_data_extractor.datasheet_meta_data import (
    delete_ds_meta_data_from_vector_db,
    upsert_ds_meta_data_in_vector_db,
)
from everstage_ddd.workflow_builder.service.workflow_service import (
    get_errors_for_missing_datasheet_columns,
    is_databook_datasheet_part_of_any_workflow,
)
from interstage_project.celery import TaskGroupEnum
from interstage_project.utils import (
    LogWithContext,
    get_queue_name_respect_to_task_group,
)
from spm.accessors.custom_field_accessor import CustomFieldsAccessor
from spm.constants.audit_trail import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.services import audit_services
from spm.services.analytics_services.analytics_service import CoreAnalytics
from spm.services.custom_object_services.co_permission_services import (
    get_objects_excluded_for_user,
)
from spm.services.datasheet_permission_services import (
    does_user_has_permission_to_custom_objects_in_datasheet,
    get_datasheets_without_permissions,
    invalidate_datasheet_permissions,
)
from spm.services.datasheet_services import remove_prefixes_lhs_rhs
from spm.services.datasheet_variable_services import (
    extract_window_calculated_field_source_variables,
    get_datasheet_display_names_using_client_id,
    insert_ast_in_meta_data,
)
from spm.services.datasheet_variables_graph import DatasheetVariablesGraph
from spm.utils import (
    EvaluationContext,
    GetUserPropertiesTransformationUtils,
    compare_if_integer_or_percentage,
    get_window_based_calculated_fields,
)
from superset.services.data_source_services import (
    check_if_db_used_in_analytics_and_update,
    check_if_ds_used_in_analytics_and_update,
)

logger = logging.getLogger(__name__)


def create_databook(request):
    time = timezone.now()
    client_id = request.client_id
    request.data["client"] = client_id
    request.data["knowledge_begin_date"] = time
    request.data["databook_id"] = (
        request.data["databook_id"] if "databook_id" in request.data else uuid.uuid4()
    )
    request.data["additional_details"] = request.audit
    request.data["created_at"] = time
    request.data["created_by"] = request.user.username

    ###################### audit log #####################
    event_type_code = EVENT["CREATE_DATABOOK"]["code"]
    event_key = request.data["databook_id"]
    summary = request.data["name"]
    audit_data = request.data
    updated_by = request.audit["updated_by"]
    updated_at = time
    ######################################################
    logger = request.logger
    logger.update_context({"databook_id": request.data["databook_id"]})
    book_ser = DatabookSerializer(data=request.data)
    try:
        if book_ser.is_valid():
            all_databooks = list(DatabookAccessor(client_id).client_latest_kd_aware())
            db_names = [db.name.lower() for db in all_databooks]
            if book_ser.validated_data["name"].lower() in db_names:
                logger.info(
                    "Databook with name {} Already exist ".format(
                        book_ser.validated_data["name"].lower()
                    )
                )
                return Response(
                    {"status": "DATABOOK_NAME_EXISTS"},
                    status=status.HTTP_201_CREATED,
                )
            DatabookAccessor(client_id).invalidate_databook(
                request.data["databook_id"], time
            )
            DatabookAccessor(client_id).persist_databook(book_ser)
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )
            logger.info(
                "New Databook with name {} created successfully".format(
                    book_ser.validated_data["name"].lower()
                )
            )

            analytics_data = {
                "user_id": request.audit["updated_by"],
                "event_name": SegmentEvents.CREATE_DATABOOK.value,
                "event_properties": {
                    SegmentProperties.DATABOOK_NAME.value: request.data["name"],
                    SegmentProperties.DATABOOK_ID.value: str(
                        request.data["databook_id"]
                    ),
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)
            return Response(
                {
                    "status": "SUCCESS",
                    "databookId": request.data["databook_id"],
                },
                status=status.HTTP_201_CREATED,
            )
        else:
            error_dict = {
                "ser_errors": book_ser.errors,
                "trace_back": traceback.print_exc(),
            }
            logger.error(
                "Error in creating databook with Id {}".format(
                    request.data["databook_id"]
                ),
                error_dict,
            )
            return Response(book_ser.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error(
            "Excep in creating databook with Id {}".format(request.data["databook_id"]),
            error_dict,
        )
        raise SQLParseError() from e


def rename_databook(request):
    time = timezone.now()
    client_id = request.client_id
    databook_id = request.data.get("databook_id")
    name = request.data.get("name")
    databook = DatabookAccessor(client_id).get_databook_by_id(databook_id)
    prev_name = databook.name
    logger = request.logger
    logger.update_context({"databook_id": databook_id, "databook_name": name})
    if databook:
        databook = clone_object(databook, time, request.audit)
        databook.name = name

        ###################### audit log #####################
        event_type_code = EVENT["RENAME_DATABOOK"]["code"]
        event_key = databook_id
        summary = name
        audit_data = databook.__dict__
        updated_by = request.audit["updated_by"]
        updated_at = time
        ######################################################

        try:
            all_databooks = list(DatabookAccessor(client_id).client_latest_kd_aware())
            db_names = [db.name.lower() for db in all_databooks]
            if name.lower() in db_names:
                logger.info("Databook with name {} already exists".format(name))
                return Response(
                    {"status": "DATABOOK_NAME_EXISTS"},
                    status=status.HTTP_201_CREATED,
                )
            DatabookAccessor(client_id).invalidate_databook(databook_id, time)
            DatabookAccessor(client_id).persist_databook(databook)
            try:
                check_if_db_used_in_analytics_and_update(client_id, databook_id, name)
            except Exception as e:
                logger.info(
                    f"Error in updating databook name in analytics table - {e}",
                    {"trace_back": traceback.print_exc()},
                )
                return Response(
                    {
                        "status": "ANALYTICS_UPDATE_FAILED",
                        "databookId": request.data["databook_id"],
                    },
                    status=status.HTTP_200_OK,
                )  # TODO : modify status

            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )
            logger.info(
                "Databook with name {} and databook_id {} created successfully".format(
                    name, databook_id
                )
            )

            analytics_data = {
                "user_id": request.audit["updated_by"],
                "event_name": SegmentEvents.RENAME_DATABOOK.value,
                "event_properties": {
                    SegmentProperties.DATABOOK_NAME.value: prev_name,
                    SegmentProperties.DATABOOK_ID.value: databook_id,
                    SegmentProperties.NEW_DATABOOK_NAME.value: name,
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)
            return Response(
                {
                    "status": "SUCCESS",
                    "databookId": request.data["databook_id"],
                },
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            error_dict = {"trace_back": traceback.print_exc()}
            logger.error(
                "Excep in renaming databook with name {}  and databook_id {}".format(
                    name, databook_id
                ),
                error_dict,
            )
            raise SQLParseError() from e
    else:
        raise ObjectDoesNotExist


def check_if_variable_is_window_field(variable: dict) -> bool:
    """
    returns true if a variable is a window based calculated field
    """
    for function in get_window_based_calculated_fields():
        if function in variable["meta_data"]:
            return True

    return False


def _split_calc_and_normal_fields(variables):
    """
    Given a list of variables returns two list
    one calc field list and the other normal list.
    """
    calc_fields = list(
        filter(
            lambda rec: rec["field_order"] > 0,
            variables,
        )
    )
    non_calc_fields = list(filter(lambda rec: rec["field_order"] == 0, variables))
    return calc_fields, non_calc_fields


def _check_if_variable_config_changed(variable_set_a, variable_set_b):
    """
    Check if variables config has changed
    """
    if len(variable_set_a) != len(variable_set_b):
        return True

    variable_set_a = sorted(variable_set_a, key=lambda rec: rec["system_name"])
    variable_set_b = sorted(variable_set_b, key=lambda rec: rec["system_name"])
    for table_var, request_var in zip(variable_set_a, variable_set_b):
        # check if system name and data_type are the same
        if (
            table_var["system_name"] != request_var["system_name"]
            or table_var["data_type_id"] != request_var["data_type"]
            or table_var["field_order"] != request_var["field_order"]
            or len(DeepDiff(table_var["meta_data"], request_var["meta_data"])) > 0
        ):
            return True

        # For datasheet v2 revamp , is_selected also needs to be evaluated,
        if (
            "is_selected" in table_var
            and "is_selected" in request_var
            and table_var["is_selected"] != request_var["is_selected"]
        ):
            return True

    return False


def check_if_variables_changed(existing_vars, request_vars) -> tuple[bool, bool]:
    """
    checks if there is a difference between variables in table
    and request datasheet variables
    returns a tuple of boolean (calc_fields_changed, non_calc_fields_changed)
    """

    existing_calc_fields, existing_non_calc_fields = _split_calc_and_normal_fields(
        existing_vars
    )
    request_calc_fields, request_non_calc_fields = _split_calc_and_normal_fields(
        request_vars
    )

    return _check_if_variable_config_changed(
        existing_calc_fields, request_calc_fields
    ), _check_if_variable_config_changed(
        existing_non_calc_fields, request_non_calc_fields
    )


def _check_if_transformations_changed(
    existing_transformation_and_source, request_transformations_and_source
):
    """
    checks if there is a difference between transformations in table
    and request transformations specs
    """
    # sometimes source custom object can be changed to a different object
    # with the same schema where transformation spec doesn't change
    if existing_transformation_and_source.get(
        "source_type"
    ) != request_transformations_and_source.get(
        "source_type"
    ) or existing_transformation_and_source.get(
        "source_id"
    ) != request_transformations_and_source.get(
        "source_id"
    ):
        return True

    existing_transformations = existing_transformation_and_source.get(
        "transformation_spec"
    )
    request_transformations = request_transformations_and_source.get(
        "transformation_spec"
    )

    if len(existing_transformations) != len(request_transformations):
        return True

    # if any of transformation's spec changes
    for ex_transformation, req_transformation in zip(
        existing_transformations, request_transformations
    ):
        if len(DeepDiff(ex_transformation, req_transformation)) > 0:
            return True

    return False


def _has_config_changed(existing_vars, request_vars) -> tuple[bool, bool, bool]:
    """
    Checks for changes in datasheet variables
    and sets values of fields accordingly
    """
    calc_field_changed, non_calc_field_changed = check_if_variables_changed(
        existing_vars=existing_vars,
        request_vars=request_vars,
    )

    is_pk_modified = calc_field_changed or non_calc_field_changed
    is_config_changed = non_calc_field_changed
    is_calc_field_changed = calc_field_changed

    logger.info(
        f"For the given request is is_pk_modified: {is_pk_modified}, is_config_changed: {is_config_changed} and is_calc_field_changed: {is_calc_field_changed}"
    )
    return is_pk_modified, is_config_changed, is_calc_field_changed


@transaction.atomic
def create_datasheet(request):
    time = timezone.now()
    client_id = request.client_id
    databook_id = request.data["databook_id"]
    request.data["client"] = client_id
    request.data["knowledge_begin_date"] = time
    add_det = request.audit
    request.data["additional_details"] = add_det
    # if no datasheet_id passed then it is a create event
    is_edit = request.data.get("datasheet_id")
    request.data["datasheet_id"] = (
        request.data["datasheet_id"]
        if "datasheet_id" in request.data and request.data["datasheet_id"]
        else uuid.uuid4()
    )
    logger = request.logger

    expression_box_version = request.data.get("expression_box_version", "v1")
    if (
        get_client_databook_expression_designer_version(client_id)
        != expression_box_version
    ):
        return Response(
            "Formula doesn't match the enabled version, refresh and try again",
            status=status.HTTP_400_BAD_REQUEST,
        )

    if expression_box_version == "v2":
        # When the `expressionbox_version` client feature flag is set to 'v2', the infix expression of
        # `ADVANCED_FILTER` transformation will be in 'v2' format. To make it usable for
        # calculations (create_ast) we are adding `AST_META` token to the infix expression.
        request.data["transformation_spec"] = add_meta_to_advanced_filter_infix(
            client_id=client_id,
            transformation_spec=request.data["transformation_spec"],
            context=AstMetaContext(
                databook_id=str(databook_id),
                datasheet_id=str(request.data["datasheet_id"]),
            ),
        )

    co_permission_for_datasheet = (
        does_user_has_permission_to_custom_objects_in_datasheet(
            client_id,
            databook_id,
            request.data["datasheet_id"],
            request.user,
            knowledge_date=None,
        )
    )

    if not co_permission_for_datasheet:
        logger.error(
            "You can't perform this action since you don't have access to the underlying data"
        )
        return Response(
            "You can't perform this action since you don't have access to the underlying data",
            status=status.HTTP_400_BAD_REQUEST,
        )

    variables = request.data["variables"]

    if expression_box_version == "v2":
        # When the `databook_expressionbox_version` client feature flag is set to 'v2', it indicates that the `meta_data`
        # of variables containing calculated fields such as Rank, Rolling, or Hierarchy will be in 'v2' format.
        # Consequently, it is necessary to convert these expressions to the 'v1' format to maintain compatibility
        # with downstream processes or functions that expect the 'v1' format.
        convert_window_func_cf_variables(
            client_id,
            variables,
            databook_id=databook_id,
            datasheet_id=request.data["datasheet_id"],
        )

    ordered_columns = []

    ###################### audit log #####################
    event_type_code = (
        EVENT["EDIT_DATASHEET"]["code"]
        if is_edit
        else EVENT["CREATE_DATASHEET"]["code"]
    )
    event_key = request.data.get("datasheet_id")
    summary = request.data.get("name")
    audit_data = request.data
    updated_by = request.audit["updated_by"]
    updated_at = time
    ######################################################

    ds_variables = []
    new_vars = []
    # get the existing datasheet variables
    existing_vars: list[dict] = DatasheetVariableAccessor(
        client_id
    ).get_variables_for_db_ds(databook_id, request.data["datasheet_id"], as_dicts=True)
    system_name_map = {}
    if existing_vars:
        system_name_map = {
            ds_var.get("system_name"): ds_var for ds_var in existing_vars
        }

    # create and insert ast in meta_data
    insert_ast_in_meta_data(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=str(request.data["datasheet_id"]),
        variables=variables,
        expression_box_version=expression_box_version,
    )

    datasheet_variables_graph = DatasheetVariablesGraph(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=str(request.data["datasheet_id"]),
        variables=variables,
        expression_box_version=expression_box_version,
    )
    (
        field_order_records,
        evaluation_context_dict,
    ) = datasheet_variables_graph.compute_variables_field_order_and_evaluation_context()

    for details in variables:
        variable_meta_data = details.get("meta_data", None)
        if variable_meta_data and details["system_name"] in evaluation_context_dict:
            variable_meta_data["evaluation_context"] = evaluation_context_dict[
                details["system_name"]
            ]

        is_primary = details["system_name"] in request.data.get("primary_key", [])

        ds_var = {
            "knowledge_begin_date": time,
            "client": client_id,
            "additional_details": add_det,
            "databook_id": databook_id,
            "datasheet_id": request.data["datasheet_id"],
            "system_name": details["system_name"],
            "display_name": details["display_name"],
            "is_primary": is_primary,
            "variable_id": system_name_map.get(details["system_name"], {}).get(
                "variable_id"
            )
            or uuid.uuid4(),
            "data_type": details["data_type_id"],
            "tags": (
                system_name_map[details["system_name"]]["tags"]
                if system_name_map.get(details["system_name"], {}).get("tags")
                else None
            ),
            "meta_data": variable_meta_data,
            "field_order": field_order_records[details["system_name"]],
            "description": details.get("description"),
        }
        ordered_columns.append(details["system_name"])

        if is_hierarchy_calculated_field(
            details
        ):  # on creating hierarchy field , populate the source cf data.
            ds_var["source_cf_meta_data"] = {
                "datasheet_id": request.data["datasheet_id"],
                "hierarchy_for_data_type_id": details["meta_data"]["hierarchy"][
                    "hierarchy_for_data_type_id"
                ],
            }
        else:
            # to populate source data for derived cf variables ,during initial phase only hierarchy cf contains this data
            ds_var["source_cf_meta_data"] = details.get("source_cf_meta_data")

        ds_variables.append(ds_var)
        new_var = ds_var | {"is_selected": True}
        new_vars.append(new_var)

    request.data["ordered_columns"] = ordered_columns
    sheet_ser = DatasheetSerializer(data=request.data)
    ds_var_ser = DatasheetVariableSerializer(data=ds_variables, many=True)
    logger.update_context(
        {
            "databook_id": databook_id,
            "datasheet_id": request.data["datasheet_id"],
            "ordered_columns": request.data["ordered_columns"],
        }
    )

    segment_event = SegmentEvents.CREATE_DATASHEET.value
    if is_edit:
        try:
            segment_event = SegmentEvents.EDIT_DATASHEET.value
            ds_obj = DatasheetAccessor(client_id).get_datasheet_select_for_update(
                databook_id, request.data["datasheet_id"]
            )

            # check if primary key has changed, and populate 'is_pk_modified' field.
            if ds_obj.primary_key != request.data["primary_key"]:
                request.data["is_pk_modified"] = True
                request.data["is_config_changed"] = True
            else:
                request.data["is_pk_modified"] = ds_obj.is_pk_modified
                request.data["is_config_changed"] = ds_obj.is_config_changed

            # set the is_calc_field_changed to its existing value
            request.data["is_calc_field_changed"] = ds_obj.is_calc_field_changed

        except OperationalError:
            logger.error(
                "Another update is in progress. Error in creating Datasheet with datasheet_id {} and databook_id {}".format(
                    request.data["datasheet_id"], databook_id
                ),
            )
            return Response("locked", status=status.HTTP_400_BAD_REQUEST)

    is_pk_modified, is_config_changed, is_calc_field_changed = _has_config_changed(
        existing_vars=existing_vars, request_vars=ds_variables
    )

    if is_pk_modified:
        request.data["is_pk_modified"] = True
    if is_config_changed:
        request.data["is_config_changed"] = True
    if is_calc_field_changed:
        request.data["is_calc_field_changed"] = True
    if is_edit:
        datasheet_id = request.data.get("datasheet_id")
        datasheet_obj = DatasheetAccessor(client_id).get_datasheet_by_id(datasheet_id)

        if datasheet_obj and datasheet_obj.is_force_skip is True:
            request.data["is_force_skip"] = True

    if is_edit and not request.data.get("is_config_changed"):
        existing_transformations_and_source = DatasheetAccessor(
            client_id=client_id
        ).get_datasheet_source_and_transformation_spec(
            datasheet_id=request.data["datasheet_id"]
        )[
            0
        ]
        request_transformations_and_source = {
            "source_type": request.data["source_type"],
            "source_id": request.data["source_id"],
            "transformation_spec": request.data["transformation_spec"],
        }
        if _check_if_transformations_changed(
            existing_transformations_and_source, request_transformations_and_source
        ):
            request.data["is_config_changed"] = True

    # if any of the datasheet config hasn't changed then set the generate datasheet flag to true
    if is_edit and not (
        request.data["is_config_changed"]
        or request.data["is_pk_modified"]
        or request.data["is_calc_field_changed"]
    ):
        request.data["is_datasheet_generated"] = (
            DatasheetAccessor(client_id)
            .get_datasheet_by_id(request.data["datasheet_id"])
            .is_datasheet_generated
        )
    try:
        datasheet_id = str(request.data["datasheet_id"])

        if sheet_ser.is_valid() and ds_var_ser.is_valid():
            # persist into datasheet table

            ds_id = request.data.get("datasheet_id")
            ds_name = request.data.get("name")
            is_renamed = False
            if ds_id and ds_name:
                is_renamed = is_datasheet_renamed(
                    client_id, databook_id, ds_id, ds_name
                )

            DatasheetAccessor(client_id).invalidate_datasheet(
                databook_id, request.data["datasheet_id"], time
            )
            DatasheetAccessor(client_id).persist_datasheet(sheet_ser)

            if is_renamed:
                try:
                    check_if_ds_used_in_analytics_and_update(
                        client_id,
                        databook_id,
                        request.data["datasheet_id"],
                        request.data.get("name"),
                    )
                except Exception as ex:
                    logger.exception(
                        "Error in updating analytics for datasheet with datasheet_id {} and databook_id {} - {}".format(
                            request.data["datasheet_id"], databook_id, ex
                        ),
                    )
                    raise SQLParseError() from ex

            # create a new table for the new datasheet if sharding is enabled for the client
            is_datasheet_data_sharded = get_client_is_datasheet_sharded(
                client_id=client_id
            )
            if not is_edit and is_datasheet_data_sharded:
                logger.info(
                    f"Creating a new table for the datasheet {request.data['datasheet_id']}"
                )
                create_datasheet_data_table(
                    client_id=client_id, datasheet_id=request.data["datasheet_id"]
                )

            # persist into datasheet variable table
            DatasheetVariableAccessor(client_id).invalidate_datasheet_variables(
                databook_id, request.data["datasheet_id"], time
            )
            DatasheetVariableAccessor(client_id).persist_variables(ds_var_ser)

            # updating the kbd of data book
            if is_edit:
                record_databook_changes_on_edit(
                    client_id, databook_id, time, add_det, logger
                )
            else:
                record_databook_changes_on_create(
                    client_id,
                    databook_id,
                    str(request.data["datasheet_id"]),
                    time,
                    add_det,
                    logger,
                )

            from spm.services.datasheet_graph.datasheet_graph import DataSheetGraph
            from spm.services.datasheet_services import bulk_datasheet_order_update

            # datasheet_order previously used to be computed on the frontend and be available
            # in request.data.  To keep backward compatibility, we will do the same here

            datasheet_graph = DataSheetGraph(
                client_id=client_id,
                include_stale_information_query=False,
            )

            datasheet_order_records = datasheet_graph.compute_datasheet_order(
                datasheet_id=str(datasheet_id)
            )

            # Updating the datasheet_order in bulk the database
            bulk_datasheet_order_update(
                client_id=client_id, datasheet_order_records=datasheet_order_records
            )

            from everstage_ddd.datasheet.helpers.datasheet_version_writer import (
                DatasheetVersionWriter,
            )

            DatasheetVersionWriter(
                client_id=client_id,
                datasheet_id=datasheet_id,
                version="v2",
                audit_detail=request.audit,
            ).transform_and_save()

            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )
            logger.info(
                "Datasheet with datasheet_id {} and databook_id {} created successfully".format(
                    request.data["datasheet_id"], databook_id
                )
            )
            analytics_data = {
                "user_id": request.audit["updated_by"],
                "event_name": segment_event,
                "event_properties": {
                    SegmentProperties.DATABOOK_NAME.value: str(
                        request.data.get("databook_name", "Untitled")
                    ),
                    SegmentProperties.DATABOOK_ID.value: str(
                        request.data["databook_id"]
                    ),
                    SegmentProperties.DATASHEET_NAME.value: str(request.data["name"]),
                    SegmentProperties.DATASHEET_DATA_SOURCE.value: str(
                        request.data["source_type"]
                    ),
                    SegmentProperties.DATASHEET_SOURCE_NAME.value: str(
                        request.data["source_name"]
                    ),
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)

            join_type = []
            agg_type = []
            for transform_data in request.data["transformation_spec"]:
                if transform_data["type"] == "JOIN":
                    join_type.append(transform_data["join_type"])
                if transform_data["type"] == "GROUP_BY":
                    for agg_data in transform_data["aggregations"]:
                        agg_type.append(agg_data["function"])
            variables_count = len(request.data["variables"])
            transformations_count = len(request.data["transformation_spec"])
            formula_fields_count = 0
            for variable in request.data["variables"]:
                if ("field_order" in variable and variable["field_order"] > 0) or (
                    "meta_data" in variable
                    and "infix" in variable["meta_data"]
                    and variable["meta_data"]["infix"] != None
                ):
                    formula_fields_count += 1
            if should_insert_meta_data_to_vec_db(client_id):
                upsert_ds_meta_data_in_vector_db(
                    client_id,
                    databook_id,
                    request.data["datasheet_id"],
                    add_stats=False,
                )
            analytics_data = {
                "user_id": request.user.username,
                "event_name": SegmentEvents.GET_JOIN_TYPE.value,
                "event_properties": {
                    SegmentProperties.DATABOOK_NAME.value: summary,
                    SegmentProperties.JOIN_TYPE.value: join_type,
                },
            }
            analytics.send_analytics(analytics_data)
            analytics_data = {
                "user_id": request.user.username,
                "event_name": SegmentEvents.GET_AGGREGATION_TYPE.value,
                "event_properties": {
                    SegmentProperties.DATABOOK_NAME.value: summary,
                    SegmentProperties.AGGREGATION_TYPE.value: agg_type,
                },
            }
            analytics.send_analytics(analytics_data)
            analytics_data = {
                "user_id": request.user.username,
                "event_name": SegmentEvents.COUNT_OF_TRANSFORMATIONS.value,
                "event_properties": {
                    SegmentProperties.DATABOOK_NAME.value: summary,
                    SegmentProperties.COUNT_OF_TRANSFORMATIONS.value: transformations_count,
                },
            }
            analytics.send_analytics(analytics_data)
            analytics_data = {
                "user_id": request.user.username,
                "event_name": SegmentEvents.COUNT_OF_FIELDS.value,
                "event_properties": {
                    SegmentProperties.DATABOOK_NAME.value: summary,
                    SegmentProperties.COUNT_OF_FIELDS.value: variables_count,
                },
            }
            analytics.send_analytics(analytics_data)
            analytics_data = {
                "user_id": request.user.username,
                "event_name": SegmentEvents.COUNT_OF_FORMULA_FIELDS.value,
                "event_properties": {
                    SegmentProperties.DATABOOK_NAME.value: summary,
                    SegmentProperties.COUNT_OF_FIELDS.value: formula_fields_count,
                },
            }
            analytics.send_analytics(analytics_data)
            return Response({"status": "SUCCESS"}, status=status.HTTP_201_CREATED)
        else:
            error_dict = {
                "ser_errors": sheet_ser.errors,
                "ds_var_ser_errors": ds_var_ser.errors,
                "trace_back": traceback.print_exc(),
            }
            logger.error(
                "Error in creating Datasheet with datasheet_id {} and databook_id {}".format(
                    request.data["datasheet_id"], databook_id
                ),
                error_dict,
            )
            return Response(
                sheet_ser.errors if sheet_ser.errors else ds_var_ser.errors,
                status=status.HTTP_400_BAD_REQUEST,
            )
    except Exception as e:
        error_dict = {
            "trace_back": traceback.print_exc(),
        }
        logger.error(
            "Excep in creating Datasheet with datasheet_id {} and databook_id {}".format(
                request.data["datasheet_id"], databook_id
            ),
            error_dict,
        )
        raise SQLParseError() from e


def is_datasheet_renamed(client_id, databook_id, ds_id, ds_name):
    """
    This function checks if the datasheet is renamed or not, used to identify a datasheet rename call from create_datasheet method
    """
    cur_datasheet = DatasheetAccessor(client_id).get_data_by_datasheet_id(
        databook_id, ds_id
    )
    is_renamed = False
    if cur_datasheet:
        cur_name = cur_datasheet[0].name
        if cur_name != ds_name:
            is_renamed = True
    return is_renamed


def clone_databook(client_id, databook_id, request):
    """
    Function to clone a given databook_id
    """
    time = timezone.now()
    clone_databook_id = uuid.uuid4()
    # hashmap to store the mapping between curr datasheet id and the corresponding clone datasheet_id
    datasheet_id_map = {}
    # cloning databook object
    databook = DatabookAccessor(client_id).get_databook_by_id(databook_id)
    if databook:
        databook = clone_object(databook, time, request.audit)
        databook.is_draft = True
        databook.created_at = time
        databook.created_by = request.user.username
        db_names = DatabookAccessor(client_id).get_databook_names()
        # list of existing databook names
        names = [db_name.get("name") for db_name in db_names]
        databook.name = name_builder(databook.name, names)
        summary = databook.name
        # cloning datasheets for the databook
        datasheets = DatasheetAccessor(client_id).get_datasheets_for_databooks(
            databook_id
        )
        for datasheet in datasheets:
            datasheet = clone_object(datasheet, time, request.audit)
            clone_datasheet_id = uuid.uuid4()
            datasheet.databook_id = clone_databook_id
            datasheet.is_pk_modified = False
            datasheet.is_datasheet_generated = False
            datasheet.is_config_changed = True
            datasheet_id_map[datasheet.datasheet_id] = clone_datasheet_id
            # If datasheet is sourced from same databook , replace the source databook with new databook
            if str(databook_id) == str(datasheet.source_databook_id):
                datasheet.source_databook_id = clone_databook_id

            # create a new shard for the cloned sheet
            print(f"New sheet is being created here {clone_datasheet_id}")
            # create a new table for the new datasheet if sharding is enabled for the client
            is_datasheet_data_sharded = get_client_is_datasheet_sharded(
                client_id=client_id
            )
            if is_datasheet_data_sharded:
                create_datasheet_data_table(
                    client_id=client_id, datasheet_id=clone_datasheet_id
                )

        # updating the datasheet_id, source_id, transformation spec "with" field
        # datasheet_id_map: key - original_datasheet_id, value - clone_datasheet_id

        for datasheet in datasheets:
            clone_datasheet_id = datasheet_id_map[datasheet.datasheet_id]
            clone_datasheet_variables(
                client_id,
                databook.databook_id,
                clone_databook_id,
                datasheet.datasheet_id,
                clone_datasheet_id,
                request.audit,
                original_to_cloned_datasheet_id_map=datasheet_id_map,
            )
            datasheet.datasheet_id = clone_datasheet_id
            from everstage_etl.databook_etl.datasheet_etl_utils import (
                TransformationType,
            )

            if datasheet.source_type == "datasheet":
                # source_id cannot be null at anypoint for any datasheet

                # If source_id is not found in the datasheet_id_map means
                # the source_id is from another databook
                # in this case we wont clone that source datasheet
                # so there is no change in source_id UUID
                if uuid.UUID(datasheet.source_id) in datasheet_id_map:
                    datasheet.source_id = str(
                        datasheet_id_map[uuid.UUID(datasheet.source_id)]
                    )

            transformations = datasheet.transformation_spec
            for transformation in transformations:
                if (
                    transformation.get("type")
                    == TransformationType.TEMPORAL_SPLICE.name
                ):
                    for source in transformation["meta"]:
                        if source["source_type"] == "datasheet":
                            old_source_id = uuid.UUID(source["source_id"])
                            source["source_id"] = str(
                                datasheet_id_map.get(old_source_id, source["source_id"])
                            )
                else:
                    with_datasheet_id = transformation.get("with")
                    if with_datasheet_id:
                        apply_with_ds = uuid.UUID(with_datasheet_id)
                        transformation["with"] = str(
                            datasheet_id_map.get(apply_with_ds, with_datasheet_id)
                        )
                    # Replace new databook id if sheet transformation used on same databook
                    if str(transformation.get("with_databook_id")) == str(databook_id):
                        transformation["with_databook_id"] = str(clone_databook_id)

            datasheet.transformation_spec = transformations

        # Populating the datasheet_order column for the cloned databook
        # Take the order from original databook if present else populate in the order of datasheet creation
        datasheet_order = databook.datasheet_order or [
            str(datasheet.datasheet_id) for datasheet in datasheets
        ]
        clone_datasheet_order = [
            datasheet_id_map[uuid.UUID(datasheet_id)]
            for datasheet_id in datasheet_order
        ]
        databook.datasheet_order = clone_datasheet_order

        ###################### audit log #####################
        event_type_code = EVENT["CLONE_DATABOOK"]["code"]
        event_key = clone_databook_id
        summary = databook.name
        audit_data = databook.__dict__
        updated_by = request.audit["updated_by"]
        updated_at = time
        ######################################################
        logger = request.logger
        logger.update_context({"databook_id": clone_databook_id})
        try:
            DatasheetAccessor(client_id).datasheet_bulk_create(datasheets)
            databook.databook_id = clone_databook_id
            DatabookAccessor(client_id).persist_databook(databook)

            from everstage_ddd.datasheet.helpers.datasheet_version_writer import (
                DatasheetVersionWriter,
            )

            for datasheet in list(
                sorted(datasheets, key=lambda datasheet: datasheet.order)
            ):
                DatasheetVersionWriter(
                    client_id, datasheet.datasheet_id, "v2"
                ).transform_and_save()

            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )

            ###################### audit log #####################
            for datasheet in datasheets:
                event_type_code = EVENT["CREATE_DATASHEET"]["code"]
                event_key = datasheet.datasheet_id
                summary = datasheet.name
                audit_data = datasheet.__dict__
                updated_by = request.audit["updated_by"]
                updated_at = time

                audit_services.log(
                    client_id,
                    event_type_code,
                    event_key,
                    summary,
                    updated_by,
                    updated_at,
                    audit_data,
                )
            ######################################################
            logger.info(
                "Cloned Databook successfully with databook_id {}".format(
                    clone_databook_id
                )
            )

            analytics_data = {
                "user_id": request.audit["updated_by"],
                "event_name": SegmentEvents.CLONE_DATABOOK.value,
                "event_properties": {
                    SegmentProperties.DATABOOK_NAME.value: DatabookAccessor(client_id)
                    .get_databook_by_id(databook_id)
                    .name,
                    SegmentProperties.DATABOOK_ID.value: databook_id,
                    SegmentProperties.CLONED_DATABOOK_NAME.value: DatabookAccessor(
                        client_id
                    )
                    .get_databook_by_id(clone_databook_id)
                    .name,
                    SegmentProperties.CLONED_DATABOOK_ID.value: str(clone_databook_id),
                },
            }
            analytics = CoreAnalytics(analyser_type="segment")
            analytics.send_analytics(analytics_data)
            return {
                "status": "SUCCESS",
                "clone_databook_id": clone_databook_id,
                "clone_datasheet_ids_map": datasheet_id_map,
            }
        except Exception as e:
            error_dict = {"trace_back": traceback.print_exc()}
            logger.error(
                "Excep in CloningDatabook with databook_id {}".format(
                    clone_databook_id
                ),
                error_dict,
            )
            raise SQLParseError() from e
    else:
        raise ObjectDoesNotExist


def clone_datasheet(
    client_id,
    databook_id,
    databook_name,
    datasheet_id,
    user_email,
    request_audit,
    logger,
    to_version="v2",
    order_reverse=False,
):
    """
    Function to clone a given datasheet_id
    """
    time = timezone.now()
    co_permission_for_datasheet = (
        does_user_has_permission_to_custom_objects_in_datasheet(
            client_id, databook_id, datasheet_id, user_email, knowledge_date=None
        )
    )

    if not co_permission_for_datasheet:
        logger.error(
            "You can't perform this action since you don't have access to the underlying data"
        )
        return Response(
            "You can't perform this action since you don't have access to the underlying data",
            status=status.HTTP_400_BAD_REQUEST,
        )

    datasheet = DatasheetAccessor(client_id).get_data_by_datasheet_id(
        databook_id, datasheet_id
    )[0]
    if datasheet:
        clone_datasheet_id = uuid.uuid4()
        datasheet = clone_object(datasheet, time, request_audit)
        # when cloning only datasheet the clone databook_id remains the same as original databook_id
        clone_datasheet_variables(
            client_id,
            databook_id,
            databook_id,
            datasheet.datasheet_id,
            clone_datasheet_id,
            request_audit,
        )
        datasheet.datasheet_id = clone_datasheet_id
        ds_names = DatasheetAccessor(client_id).get_datasheet_names()
        names = [ds_name.get("name") for ds_name in ds_names]
        datasheet.name = name_builder(datasheet.name, names)
        datasheet.is_datasheet_generated = False
        datasheet.is_pk_modified = False
        datasheet.is_config_changed = True

        # create a new datasheet_data shard for the newly created sheet
        logger.info(f"New sheet is being created here {clone_datasheet_id}")
        # create a new table for the new datasheet if sharding is enabled for the client
        is_datasheet_data_sharded = get_client_is_datasheet_sharded(client_id=client_id)
        if is_datasheet_data_sharded:
            logger.info(f"Creating a new table for the datasheet {clone_datasheet_id}")
            create_datasheet_data_table(
                client_id=client_id, datasheet_id=clone_datasheet_id
            )

        ###################### audit log #####################
        event_type_code = EVENT["CLONE_DATASHEET"]["code"]
        event_key = clone_datasheet_id
        summary = datasheet.name
        audit_data = datasheet.__dict__
        updated_by = request_audit.get("updated_by")
        updated_at = time
        ######################################################

        DatasheetAccessor(client_id).persist_datasheet(datasheet)
        from everstage_ddd.datasheet.helpers.datasheet_version_writer import (
            DatasheetVersionWriter,
        )

        DatasheetVersionWriter(
            client_id, clone_datasheet_id, to_version
        ).transform_and_save()

        # updating the kbd of data book
        record_databook_changes_on_create(
            client_id,
            databook_id,
            str(clone_datasheet_id),
            time,
            request_audit,
            logger,
            reverse_order=order_reverse,
        )
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )
        (
            source_type,
            source_name,
            datasheet_name,
        ) = datasheet_source_details(client_id, databook_id, datasheet_id)

        analytics_data = {
            "user_id": request_audit["updated_by"],
            "event_name": SegmentEvents.CLONE_DATASHEET.value,
            "event_properties": {
                SegmentProperties.DATABOOK_NAME.value: databook_name,
                SegmentProperties.DATABOOK_ID.value: str(databook_id),
                SegmentProperties.DATASHEET_NAME.value: datasheet_name,
                SegmentProperties.DATASHEET_DATA_SOURCE.value: source_type,
                SegmentProperties.DATASHEET_SOURCE_NAME.value: source_name,
                SegmentProperties.NEW_DATASHEET_NAME.value: datasheet.name,
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return {
            "status": "SUCCESS",
            "clone_datasheet_id": clone_datasheet_id,
        }
    else:
        raise ObjectDoesNotExist


def clone_datasheet_variables(
    client_id: int,
    databook_id: uuid.UUID,
    clone_databook_id: uuid.UUID,
    datasheet_id: uuid.UUID,
    clone_datasheet_id: uuid.UUID,
    request_audit: dict,
    original_to_cloned_datasheet_id_map: Optional[dict[uuid.UUID, uuid.UUID]] = None,
) -> None:
    """
    Creates another copy of all the datasheet variables in {datasheet_id} and assigns it to {clone_datasheet_id}
    """
    iputils.log_me(f"Cloning datasheet datasheet_id = {datasheet_id}")
    time = timezone.now()
    variables = DatasheetVariableAccessor(client_id).get_variables_for_db_ds(
        databook_id, datasheet_id
    )

    for ds_variable in variables:
        # the hierarchy reference sheet id will be pointing to a datasheet in a different databook
        # so we need to update the hierarchy reference sheet id to point to the cloned datasheet
        if (
            ds_variable["field_order"] > 0
            and is_hierarchy_calculated_field(ds_variable)
            and original_to_cloned_datasheet_id_map is not None
        ):
            hierarchy_ref_sheet_id = get_hierarchy_reference_sheet_id(
                ds_variable, as_uuid=True
            )
            reference_book = HierarchyMetaData(
                **ds_variable["meta_data"]["hierarchy"]
            ).reference_book

            # if the hierarchy reference sheet id is not the default value
            # then we need to update it, point it to the cloned datasheet_id
            if (
                hierarchy_ref_sheet_id
                != HierarchyUtils.USE_CURRENT_SHEET_DEFAULT_VALUE.value
            ):
                modify_hierarchy_meta_data_for_key(
                    ds_variable,
                    HierarchyUtils.REFERENCE_SHEET_KEY.value,
                    str(
                        original_to_cloned_datasheet_id_map.get(
                            hierarchy_ref_sheet_id, hierarchy_ref_sheet_id
                        )
                    ),
                )

            if str(databook_id) == reference_book:
                modify_hierarchy_meta_data_for_key(
                    ds_variable,
                    HierarchyUtils.REFERENCE_BOOK.value,
                    str(clone_databook_id),
                )

            if pydash.get(ds_variable, "source_cf_meta_data.datasheet_id"):
                source_datasheet_id = ds_variable["source_cf_meta_data"]["datasheet_id"]
                ds_variable["source_cf_meta_data"]["datasheet_id"] = str(
                    original_to_cloned_datasheet_id_map.get(
                        uuid.UUID(source_datasheet_id), source_datasheet_id
                    )
                )

        if ds_variable["field_order"] > 0 and not is_window_calculated_field(
            ds_variable
        ):
            # if the variable is a calculated field and is in v2 format, we need to update AST_META token
            # to point to the cloned databook and datasheet
            infix_expression = ds_variable["meta_data"]["infix"]

            if get_expression_format(infix_expression) == "v2":
                for exp in infix_expression:
                    if exp["token_type"] == TokenTypesV2.AST_META.name:
                        exp["token"]["databook_id"] = str(clone_databook_id)
                        exp["token"]["datasheet_id"] = str(clone_datasheet_id)

                ds_variable["meta_data"]["infix"] = infix_expression

        ds_variable["pk"] = None
        ds_variable["knowledge_begin_date"] = time
        ds_variable["additional_details"] = request_audit
        ds_variable["databook_id"] = clone_databook_id
        ds_variable["datasheet_id"] = clone_datasheet_id
        ds_variable["variable_id"] = uuid.uuid4()
        ds_variable["source_variable_id"] = None
        ds_variable["source_id"] = None
        ds_variable["source_type"] = None

    DatasheetVariableAccessor(client_id).create_objects(variables)


def archive_databook(client_id, databook_id, request_audit):
    knowledge_date: datetime = timezone.now()
    iputils.log_me(f"Getting the databook for the id {databook_id}")
    databook = DatabookAccessor(client_id).get_databook_by_id(databook_id)
    if databook:
        from spm.services.datasheet_services import (
            is_possible_to_archive_given_databook,
        )

        is_possible, datasheet_nodes = is_possible_to_archive_given_databook(
            client_id=client_id, databook_id=str(databook_id)
        )
        if is_possible is False:
            datasheet_names: str = ", ".join(
                str(datasheet_node.name) for datasheet_node in datasheet_nodes
            )
            datasheet_details: list[dict] = [
                {
                    "datasheet_id": datasheet_node.node_id,
                    "databook_id": datasheet_node.databook_id,
                    "datasheet_name": str(datasheet_node.name),
                }
                for datasheet_node in datasheet_nodes
            ]
            logger.info(
                f"For the given client_id {client_id} and databook_id {databook_id} it is not possible to archive the databook because the following datasheets are used in other databooks {datasheet_names}"
            )

            # A message to be displayed to the user
            error_message: str = (
                f"These datasheets in this databook are used in other databooks: {datasheet_names}"
            )
            return {
                "status": "error",
                "reason": error_message,
                "datasheet_details": datasheet_details,
            }

        datasheet_objects: list[Datasheet] = DatasheetAccessor(
            client_id
        ).get_datasheets_for_databooks([databook_id])
        datasheet_ids: list[uuid.UUID] = [
            datasheet_object.datasheet_id for datasheet_object in datasheet_objects
        ]
        # Importing here locally to avoid circular imports
        from spm.services.commission_plan_services import (
            is_datasheets_used_in_any_commission_plan,
            is_datasheets_used_in_any_settlement_rule,
        )

        is_datasheets_used_in_commission_plan: bool = (
            is_datasheets_used_in_any_commission_plan(
                client_id=client_id, datasheet_ids=datasheet_ids
            )
        )

        is_datasheet_used_in_forecast_plan: bool = (
            is_datasheets_used_in_any_commission_plan(
                client_id=client_id,
                datasheet_ids=datasheet_ids,
                commission_type=COMMISSION_TYPE.FORECAST,
            )
        )

        is_datasheets_used_in_settlement_rule: bool = (
            is_datasheets_used_in_any_settlement_rule(
                client_id=client_id, datasheet_ids=datasheet_ids
            )
        )

        if (
            is_datasheets_used_in_commission_plan
            or is_datasheets_used_in_settlement_rule
        ):
            error_message: str = (
                "Cannot archive databook as it is used in commission plan or settlement rule"
            )
            return {
                "status": "error",
                "reason": error_message,
            }

        is_datasheets_used_in_hris: bool = HrisConfigAccessor(
            client_id=client_id
        ).is_datasheets_used_in_hris(datasheet_ids=datasheet_ids)

        if is_datasheets_used_in_hris:
            error_message: str = "Cannot archive databook as it is used in HRIS"
            return {
                "status": "error",
                "reason": error_message,
            }

        if is_datasheet_used_in_forecast_plan:
            error_message: str = (
                "Cannot archive databook as it is used in forecast plan"
            )
            return {
                "status": "error",
                "reason": error_message,
            }

        DatabookAccessor(client_id).invalidate_databook(databook_id, knowledge_date)
        databook = clone_object(databook, knowledge_date, request_audit)
        databook.is_archived = True
        DatabookAccessor(client_id=client_id).persist_databook(databook)

        analytics_data = {
            "user_id": request_audit["updated_by"],
            "event_name": SegmentEvents.ARCHIVE_DATABOOK.value,
            "event_properties": {
                SegmentProperties.DATABOOK_NAME.value: databook.name,
                SegmentProperties.DATABOOK_ID.value: databook_id,
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return {
            "status": "success",
            "reason": f"Databook {databook.name} is archived successfully",
        }
    else:
        raise ObjectDoesNotExist


def activate_databook(client_id: int, databook_id: str, request_audit):
    """
    This function is used to activate the given databook_id

    If not possible it will return the reason for the failure to activate
    """
    from spm.services.datasheet_services import is_possible_to_active_given_databook

    is_possible, datasheet_nodes = is_possible_to_active_given_databook(
        client_id=client_id, databook_id=str(databook_id)
    )
    if is_possible is False:
        datasheet_names: str = ", ".join(
            str(datasheet_node.name) for datasheet_node in datasheet_nodes
        )
        datasheet_details: list[dict] = [
            {
                "datasheet_id": datasheet_node.node_id,
                "databook_id": datasheet_node.databook_id,
                "datasheet_name": str(datasheet_node.name),
            }
            for datasheet_node in datasheet_nodes
        ]
        logger.info(
            f"For the given client_id {client_id} and databook_id {databook_id} it is not possible to activate the databook because the following datasheets are used in other databooks {datasheet_names}"
        )

        # A message to be displayed to the user
        error_message: str = (
            f"These datasheets in this databook are used in other databooks: {datasheet_names}"
        )
        return {
            "status": "error",
            "reason": error_message,
            "datasheet_details": datasheet_details,
        }

    knowledge_date = timezone.now()
    logger.info(f"Getting the databook for the id {databook_id}")
    databook = DatabookAccessor(client_id=client_id).get_databook_by_id(
        databook_id=databook_id
    )

    if databook:
        DatabookAccessor(client_id=client_id).invalidate_databook(
            databook_id, knowledge_date
        )
        databook = clone_object(databook, knowledge_date, request_audit)
        databook.is_archived = False
        DatabookAccessor(client_id).persist_databook(databook)

        analytics_data = {
            "user_id": request_audit["updated_by"],
            "event_name": SegmentEvents.UNARCHIVE_DATABOOK.value,
            "event_properties": {
                SegmentProperties.DATABOOK_NAME.value: databook.name,
                SegmentProperties.DATABOOK_ID.value: databook_id,
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return {
            "status": "success",
            "reason": f"Databook {databook.name} is activated successfully",
        }
    else:
        raise ObjectDoesNotExist


def delete_databook(client_id: int, request):
    knowledge_date = timezone.now()
    databook_id = request.data.get("databook_id")

    ###################### audit log #####################
    event_type_code = EVENT["DELETE_DATABOOK"]["code"]
    event_key = databook_id
    summary = DatabookAccessor(client_id).get_databook_by_id(databook_id).name
    audit_data = {}
    updated_by = request.audit["updated_by"]
    updated_at = knowledge_date
    ######################################################

    # Check is this databook is used anyother other databook (active or archived)
    # If yes then we dont allow the user to delete this databook
    from spm.services.datasheet_services import can_delete_databook

    is_possible, reason_datasheet_nodes = can_delete_databook(
        client_id=client_id, databook_id=str(databook_id)
    )
    if is_possible is False:
        databook_ids: list = [
            datasheet.databook_id for datasheet in reason_datasheet_nodes
        ]
        databook_names: dict = databook_names_by_ids(
            client_id=client_id, databook_ids=databook_ids
        )
        reason: str = (
            f"Databook has dependencies with this following databooks: {list(databook_names.values())}"
        )
        return {
            "status": "FAILED",
            "reason": reason,
        }

    # Check if any datasheet or databook is in locked state
    # If yes, then we dont allow to delete that databook/datasheet

    locked_datasheets = current_all_locked_datasheets(
        client_id=client_id,
        databook_ids=databook_id,
        task=[
            SYNC_OBJECT.DATASHEET_SYNC.value,
            SYNC_OBJECT.DATABOOK_SYNC.value,
        ],
    )
    if locked_datasheets:
        reason = _construct_reason_for_generation_in_progress_delete_error(
            client_id=client_id, datasheet_ids=list(locked_datasheets)
        )
        # Checking if the generation is in progress
        # If yes, then return the reason for the failure
        return {
            "status": "FAILED",
            "reason": reason,
        }

    logger = request.logger
    logger.update_context({"databook_id": databook_id})

    datasheet_objects: list[Datasheet] = DatasheetAccessor(
        client_id
    ).get_datasheets_for_databooks([databook_id])
    datasheet_ids: list[uuid.UUID] = [
        datasheet_object.datasheet_id for datasheet_object in datasheet_objects
    ]
    # Importing here locally to avoid circular imports
    from crystal.services.crystal_services import is_databook_used_in_crystal_view
    from spm.services.commission_plan_services import (
        is_datasheets_used_in_any_commission_plan,
        is_datasheets_used_in_any_settlement_rule,
    )

    is_datasheets_used_in_commission_plan: bool = (
        is_datasheets_used_in_any_commission_plan(
            client_id=client_id, datasheet_ids=datasheet_ids
        )
    )

    is_datasheets_used_in_forecast_plan: bool = (
        is_datasheets_used_in_any_commission_plan(
            client_id=client_id,
            datasheet_ids=datasheet_ids,
            commission_type=COMMISSION_TYPE.FORECAST,
        )
    )

    is_datasheets_used_in_settlement_rule: bool = (
        is_datasheets_used_in_any_settlement_rule(
            client_id=client_id, datasheet_ids=datasheet_ids
        )
    )

    if is_datasheets_used_in_commission_plan or is_datasheets_used_in_settlement_rule:
        reason = (
            "Cannot delete databook as it is used in commission plan or settlement rule"
        )
        return {
            "status": "FAILED",
            "reason": reason,
        }

    databook_datasheet_part_of_workflow: bool = (
        is_databook_datasheet_part_of_any_workflow(
            client_id=client_id, databook_id=str(databook_id)
        )
    )

    if databook_datasheet_part_of_workflow:
        return {
            "status": "FAILED",
            "reason": DatabookDeleteError.USED_IN_WORKFLOW.value,
        }

    # Check if databook is used in any crystal view
    if is_databook_used_in_crystal_view(client_id=client_id, databook_id=databook_id):
        return {
            "status": "FAILED",
            "reason": DatabookDeleteError.USED_IN_CRYSTAL_VIEW.value,
        }

    is_db_used_in_hris = HrisConfigAccessor(client_id).is_databook_used_in_hris(
        databook_id=databook_id
    )
    if is_db_used_in_hris:
        return Response(
            {"status": "FAILED", "message": "Databook is used in HRIS"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    sheets_without_permission = get_datasheets_without_permissions(
        client_id, databook_id, updated_by
    )
    if sheets_without_permission:
        if len(sheets_without_permission) == 1:
            sheets = sheets_without_permission[0]
        elif len(sheets_without_permission) == 2:
            sheets = (
                f"{sheets_without_permission[0]} and {sheets_without_permission[1]}"
            )
        else:
            sheets = f"{sheets_without_permission[0]}, {sheets_without_permission[1]} and more"
        return Response(
            {
                "status": "FAILED",
                "message": f"You can't delete this databook since you don't have permission to the following sheets: {sheets}.",
                "sheets_without_permission": sheets_without_permission,
            },
            status=status.HTTP_400_BAD_REQUEST,
        )

    if is_datasheets_used_in_forecast_plan:
        return Response(
            {
                "status": "FAILED",
                "reason": "Cannot delete databook as it is used in forecast plan",
            },
        )
    try:
        DatasheetVariableAccessor(client_id).invalidate_all_datasheets_variables(
            databook_id, knowledge_date
        )
        DatasheetAccessor(client_id).invalidate_all_datasheets(
            databook_id, knowledge_date
        )
        DatabookAccessor(client_id).invalidate_databook(databook_id, knowledge_date)
        if should_insert_meta_data_to_vec_db(client_id):
            delete_ds_meta_data_from_vector_db(client_id, datasheet_ids)
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )
        logger.info(
            "Deleted databook with databook_id {} successfully".format(databook_id)
        )

        analytics_data = {
            "user_id": request.audit["updated_by"],
            "event_name": SegmentEvents.DELETE_DATABOOK.value,
            "event_properties": {
                SegmentProperties.DATABOOK_NAME.value: summary,
                SegmentProperties.DATABOOK_ID.value: databook_id,
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return Response(
            {"status": "SUCCESS", "message": "Databook deleted"},
            status=status.HTTP_201_CREATED,
        )
    except SQLParseError as e:
        error_dict = {"trace_back": traceback.print_exc()}
        logger.error(
            "Excep in Deleting Databook with databook_id {}".format(databook_id),
            error_dict,
        )
        raise SQLParseError() from e


def delete_datasheet(
    client_id: int, databook_id, databook_name, datasheet_id, user_email, request_audit
):
    from crystal.accessors.crystal_admin_accessor import CrystalAdminAccessor

    # Check if any datasheet or databook is in locked state
    # If yes, then we dont allow to delete that databook/datasheet
    locked_datasheets = current_all_locked_datasheets(
        client_id=client_id,
        databook_ids=databook_id,
        task=[
            SYNC_OBJECT.DATASHEET_SYNC.value,
            SYNC_OBJECT.DATABOOK_SYNC.value,
        ],
    )
    if locked_datasheets:
        reason = _construct_reason_for_generation_in_progress_delete_error(
            client_id=client_id, datasheet_ids=list(locked_datasheets)
        )
        # Checking if the generation is in progress
        # If yes, then return the reason for the failure
        return {
            "status": "FAILED",
            "reason": reason,
        }

    knowledge_date = timezone.now()
    logger = LogWithContext(
        {
            "client_id": client_id,
            "databook_id": databook_id,
            "datasheet_id": datasheet_id,
        }
    )

    co_permission_for_datasheet = (
        does_user_has_permission_to_custom_objects_in_datasheet(
            client_id, databook_id, datasheet_id, user_email, knowledge_date=None
        )
    )

    if not co_permission_for_datasheet:
        logger.error(
            "You can't perform this action since you don't have access to the underlying data"
        )
        return Response(
            "You can't perform this action since you don't have access to the underlying data",
            status=status.HTTP_400_BAD_REQUEST,
        )
    dependency_details = get_datasheet_dependencies_details(
        client_id=client_id, datasheet_id=str(datasheet_id)
    )
    if dependency_details["has_dependent"]:
        return {
            "status": "FAILED",
            "reason": dependency_details["dependent_details"],
        }

    ds_list = DatasheetAccessor(client_id).get_datasheets_for_databooks([databook_id])
    ds_id_map = {}
    for ds in ds_list:
        ds_id_map[str(ds.datasheet_id)] = ds

    datasheet = ds_id_map[str(datasheet_id)]
    # Importing here locally to avoid circular imports
    from spm.services.commission_plan_services import (
        is_datasheets_used_in_any_commission_plan,
        is_datasheets_used_in_any_settlement_rule,
    )

    is_datasheets_used_in_commission_plan: bool = (
        is_datasheets_used_in_any_commission_plan(
            client_id=client_id, datasheet_ids=[datasheet_id]
        )
    )

    is_datasheets_used_in_settlement_rule: bool = (
        is_datasheets_used_in_any_settlement_rule(
            client_id=client_id, datasheet_ids=[datasheet_id]
        )
    )
    is_datasheets_used_in_any_forecast_plan: bool = (
        is_datasheets_used_in_any_commission_plan(
            client_id=client_id,
            datasheet_ids=[datasheet_id],
            commission_type=COMMISSION_TYPE.FORECAST,
        )
    )

    if is_datasheets_used_in_commission_plan or is_datasheets_used_in_settlement_rule:
        reason = "Cannot delete datasheet as it is used in commission plan or settlement rule"
        return {
            "status": "FAILED",
            "reason": reason,
        }

    if CrystalAdminAccessor(client_id).is_datasheet_used_in_crystal_view(datasheet_id):
        return Response(
            {"status": "DATASHEET IS USED IN CRYSTAL VIEW"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    databook_datasheet_part_of_workflow: bool = (
        is_databook_datasheet_part_of_any_workflow(
            client_id=client_id,
            databook_id=str(databook_id),
            datasheet_id=str(datasheet_id),
        )
    )

    if databook_datasheet_part_of_workflow:
        return {
            "status": "FAILED",
            "reason": DatasheetDeleteError.USED_IN_WORKFLOW.value,
        }

    if HrisConfigAccessor(client_id).is_datasheet_used_in_hris(datasheet_id):
        return Response(
            {"status": "DATASHEET IS USED IN HRIS INTEGRATION"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    if is_datasheets_used_in_any_forecast_plan:
        return Response(
            {"status": "Cannot delete datasheet as it is used in forecast plan"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    try:
        ###################### audit log #####################
        event_type_code = EVENT["DELETE_DATASHEET"]["code"]
        event_key = datasheet_id
        summary = datasheet.name if datasheet else None
        audit_data = {}
        updated_by = request_audit.get("updated_by")
        updated_at = knowledge_date
        ######################################################

        (
            source_type,
            source_name,
            datasheet_name,
        ) = datasheet_source_details(client_id, databook_id, datasheet_id)

        DatasheetVariableAccessor(client_id).invalidate_datasheet_variables(
            databook_id, datasheet_id, knowledge_date
        )

        # Delete datasheet permissions and targets
        invalidate_datasheet_permissions(
            client_id, databook_id, datasheet_id, knowledge_date
        )

        client_subscription_plan = get_client_subscription_plan(client_id=client_id)
        invalidation_task_queue = get_queue_name_respect_to_task_group(
            client_id=client_id,
            subscription_plan=client_subscription_plan,
            task_group=TaskGroupEnum.MISC.value,
        )
        # soft delete all records in datasheet_data when sheet is deleted asynchronously
        invalidate_data_datasheet.si(
            client_id=client_id,
            databook_id=databook_id,
            datasheet_id=datasheet_id,
            knowledge_date=knowledge_date,
        ).set(queue=invalidation_task_queue).apply_async()

        # drop all intermediate result tables
        delete_transformation_result_tables(
            client_id=client_id,
            datasheet_id=datasheet_id,
            knowledge_date=knowledge_date,
        )

        DatasheetAccessor(client_id).invalidate_datasheet(
            databook_id, datasheet_id, knowledge_date
        )

        # updating the kbd of data book
        record_databook_changes_on_delete(
            client_id,
            databook_id,
            str(datasheet_id),
            knowledge_date,
            request_audit,
            logger,
        )
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )
        if should_insert_meta_data_to_vec_db(client_id):
            delete_ds_meta_data_from_vector_db(client_id, datasheet_id)

        analytics_data = {
            "user_id": request_audit["updated_by"],
            "event_name": SegmentEvents.DELETE_DATASHEET.value,
            "event_properties": {
                SegmentProperties.DATABOOK_NAME.value: databook_name,
                SegmentProperties.DATABOOK_ID.value: str(databook_id),
                SegmentProperties.DATASHEET_NAME.value: str(datasheet_name),
                SegmentProperties.DATASHEET_DATA_SOURCE.value: str(source_type),
                SegmentProperties.DATASHEET_SOURCE_NAME.value: str(source_name),
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return Response({"status": "SUCCESS"}, status=status.HTTP_201_CREATED)
    except Exception as e:
        raise SQLParseError() from e


def datasheet_source_details(client_id, databook_id, datasheet_id):
    datasheet = DatasheetAccessor(client_id).get_data_by_datasheet_id(
        databook_id, datasheet_id
    )[0]
    source_type = datasheet.source_type
    source_name = None

    if source_type == DATASHEET_SOURCE.DATASHEET.value:
        # The source datasheet need not to be present
        # in the same databook as the current datasheet is present
        source_name: str | None = (
            DatasheetAccessor(client_id)
            .get_data_by_datasheet_id_without_db(datasheet.source_id)[0]
            .name
        )
    elif source_type == DATASHEET_SOURCE.CUSTOM_OBJECT.value:
        source_name = (
            CustomObjectAccessor(client_id)
            .get_object_by_id(datasheet.source_id)
            .first()
            .name
        )
    elif source_type == DATASHEET_SOURCE.REPORT.value:
        for report_object_id in report_utils.get_report_object_ids():
            if report_object_id == datasheet.source_id:
                source_name = EverObjectAccessor().get_ever_objs(report_object_id).name

    return (source_type, source_name, datasheet.name)


def name_builder(curr_name, names):
    """
    Args:
        curr_name: name of the current doc which you want to clone
        names: list of names of all the existing docs

    Returns: a new unique copy_name made from the curr_name

    """
    new_name = str(curr_name) + str("_Copy")
    while is_name_exists(names, new_name):
        last_char = new_name[-1]
        if last_char.isnumeric():
            new_name = str(new_name) + str(int(last_char) + 1)
        else:
            new_name = str(new_name) + str(1)
    return new_name


def is_name_exists(names, new_name):
    for name in names:
        if new_name.lower() == name.lower():
            return True
    return False


def record_databook_changes_on_edit(
    client_id, databook_id, modified_at, request_audit, logger
):
    databook = DatabookAccessor(client_id).get_databook_by_id(databook_id)
    if databook:
        DatabookAccessor(client_id).invalidate_databook(databook_id, modified_at)
        databook = clone_object(databook, modified_at, request_audit)
        DatabookAccessor(client_id).persist_databook(databook)
        logger.info("Edited datasheet in databook({})'s".format(databook_id))
        return Response({"status": "SUCCESS"}, status=status.HTTP_201_CREATED)
    else:
        raise ObjectDoesNotExist


def record_databook_changes_on_create(
    client_id,
    databook_id,
    datasheet_id,
    modified_at,
    request_audit,
    logger,
    reverse_order=False,
):
    databook = DatabookAccessor(client_id).get_databook_by_id(databook_id)
    if databook:
        DatabookAccessor(client_id).invalidate_databook(databook_id, modified_at)
        databook = clone_object(databook, modified_at, request_audit)
        if databook.datasheet_order:
            if reverse_order:
                databook.datasheet_order = [datasheet_id] + databook.datasheet_order
            else:
                databook.datasheet_order = databook.datasheet_order + [datasheet_id]
        else:
            # for existing databooks with datasheet_order as null
            ds_ids = DatasheetAccessor(client_id).get_datasheet_ids_by_databook_id(
                databook_id
            )

            databook.datasheet_order = [str(x.datasheet_id) for x in ds_ids]
        DatabookAccessor(client_id).persist_databook(databook)

        logger.info(
            "Added/Cloned new datasheet({}) in databook({}) : {}".format(
                datasheet_id, databook_id, databook
            )
        )

        return Response({"status": "SUCCESS"}, status=status.HTTP_201_CREATED)
    else:
        raise ObjectDoesNotExist


def record_databook_changes_on_delete(
    client_id, databook_id, datasheet_id, modified_at, request_audit, logger
):
    databook = DatabookAccessor(client_id).get_databook_by_id(databook_id)
    if databook:
        DatabookAccessor(client_id).invalidate_databook(databook_id, modified_at)
        databook = clone_object(databook, modified_at, request_audit)
        if databook.datasheet_order and datasheet_id in databook.datasheet_order:
            databook.datasheet_order.remove(datasheet_id)
            logger.info(
                "Deleting datasheet ID({}) from databook({})'s datasheet_order column.".format(
                    datasheet_id, databook_id
                )
            )

        DatabookAccessor(client_id).persist_databook(databook)

        return Response({"status": "SUCCESS"}, status=status.HTTP_201_CREATED)
    else:
        raise ObjectDoesNotExist


def clone_object(current_object, curr_time, request_audit):
    current_object.pk = None
    current_object._state.adding = True  # pylint: disable=protected-access
    current_object.knowledge_begin_date = curr_time
    current_object.additional_details = request_audit
    return current_object


def create_filter(request):
    time = timezone.now()
    client_id = request.client_id
    config_type = request.data["config_type"]
    request.data["client"] = client_id
    request.data["knowledge_begin_date"] = time
    request.data["filter_id"] = uuid.uuid4()
    request.data["last_updated_by"] = str(request.user)
    request.data["filter_type"] = "PUBLIC"
    filter_name = request.data["filter_name"]
    databook_id = request.data["databook_id"]
    datasheet_id = request.data["datasheet_id"]
    request.data["additional_details"] = request.audit
    datasheet_filter_ser = DatasheetFilterSerializer(data=request.data)
    logger = request.logger
    logger.update_context(
        {"filter_id": request.data["filter_id"], "filter_name": filter_name}
    )
    ###################### audit log #####################
    event_type_code = EVENT[f"CREATE_DATASHEET-{config_type}"]["code"]
    event_key = request.data["filter_id"]
    summary = "Created {}: ".format(config_type.lower()) + filter_name
    audit_data = {}
    updated_by = request.audit["updated_by"]
    updated_at = time
    ######################################################
    try:
        if datasheet_filter_ser.is_valid():
            if DatasheetFilterAccessor(client_id).is_filter_name_exist(
                databook_id, datasheet_id, filter_name, config_type
            ):
                logger.info(
                    "{} with name {} already exists.".format(
                        config_type.capitalize(), filter_name
                    )
                )
                return Response(
                    {
                        "status": f"{config_type}_NAME_ALREADY_EXIST",
                        "filter_id": request.data["filter_id"],
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            DatasheetFilterAccessor(client_id).save_filter(datasheet_filter_ser)

            logger.info(
                "Saved {} successfully with name {}".format(
                    config_type.lower(), filter_name
                )
            )
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )

            filter_id = request.data["filter_id"]

            from everstage_ddd.datasheet.utils.datasheet_utils import (
                convert_v1_pivot_to_v2,
                convert_v1_ui_filter_spec_to_v2,
            )

            # create views in datasheet v2
            if config_type == DATASHEET_FILTER_TYPE.FILTER.value:
                convert_v1_ui_filter_spec_to_v2(client_id, datasheet_id, filter_id)
            elif config_type == DATASHEET_FILTER_TYPE.PIVOT.value:
                convert_v1_pivot_to_v2(client_id, datasheet_id, filter_id)

            return Response(
                {
                    "status": "SUCCESS",
                    "filter_id": request.data["filter_id"],
                },
                status=status.HTTP_201_CREATED,
            )
        else:
            error_dict = {
                "ser_errors": datasheet_filter_ser.errors,
                "trace_back": traceback.print_exc(),
            }
            logger.error(
                "Error creating {} with name {}".format(
                    config_type.lower(), filter_name
                ),
                error_dict,
            )
            return Response(
                datasheet_filter_ser.errors, status=status.HTTP_400_BAD_REQUEST
            )
    except SQLParseError as e:
        error_dict = {
            "trace_back": traceback.print_exc(),
        }
        logger.error(
            "Exception in creating {} with name {}".format(
                config_type.lower(), filter_name
            ),
            error_dict,
        )
        raise SQLParseError() from e


def update_filter(request):
    time = timezone.now()
    client_id = request.client_id
    config_type = request.data["config_type"]
    request.data["client"] = client_id
    request.data["knowledge_begin_date"] = time
    filter_id = request.data["filter_id"]
    datasheet_id = request.data["datasheet_id"]
    request.data["last_updated_by"] = str(request.user)
    request.data["filter_type"] = "PUBLIC"
    filter_name = request.data["filter_name"]
    request.data["additional_details"] = request.audit
    datasheet_filter_ser = DatasheetFilterSerializer(data=request.data)
    logger = request.logger
    logger.update_context(
        {
            "filter_id": request.data["filter_id"],
            "filter_name": filter_name,
        }
    )
    ###################### audit log #####################
    event_type_code = EVENT[f"UPDATE_DATASHEET-{config_type}"]["code"]
    event_key = request.data["filter_id"]
    summary = "Updated {}: ".format(config_type.lower()) + filter_name
    audit_data = request.data
    updated_by = request.audit["updated_by"]
    updated_at = time
    ######################################################
    try:
        if datasheet_filter_ser.is_valid():
            DatasheetFilterAccessor(client_id).invalidate_filter(filter_id, time)
            DatasheetFilterAccessor(client_id).save_filter(datasheet_filter_ser)
            logger.info(
                "Updated {} successfully with name {}".format(
                    config_type.lower(), filter_name
                )
            )
            audit_services.log(
                client_id,
                event_type_code,
                event_key,
                summary,
                updated_by,
                updated_at,
                audit_data,
            )
            from everstage_ddd.datasheet.utils.datasheet_utils import (
                convert_v1_pivot_to_v2,
                convert_v1_ui_filter_spec_to_v2,
            )

            # create views in datasheet v2
            if config_type == DATASHEET_FILTER_TYPE.FILTER.value:
                convert_v1_ui_filter_spec_to_v2(client_id, datasheet_id, filter_id)
            elif config_type == DATASHEET_FILTER_TYPE.PIVOT.value:
                convert_v1_pivot_to_v2(client_id, datasheet_id, filter_id)

            return Response(
                {
                    "status": "SUCCESS",
                    "filter_id": request.data["filter_id"],
                },
                status=status.HTTP_201_CREATED,
            )
        else:
            error_dict = {
                "ser_errors": datasheet_filter_ser.errors,
                "trace_back": traceback.print_exc(),
            }
            logger.error(
                "Error updating {} with name {}".format(
                    config_type.lower(), filter_name
                ),
                error_dict,
            )
            return Response(
                datasheet_filter_ser.errors, status=status.HTTP_400_BAD_REQUEST
            )
    except SQLParseError as e:
        error_dict = {
            "trace_back": traceback.print_exc(),
        }
        logger.error(
            "Excep in updating {} with name {}".format(
                config_type.lower(), filter_name
            ),
            error_dict,
        )
        raise SQLParseError() from e


def delete_filter(request):
    time = timezone.now()
    client_id = request.client_id
    config_type = request.data["config_type"]
    filter_id_to_invalidate = request.data["filter_id"]
    filter_name = request.data["filter_name"]
    logger = request.logger
    datasheet_id = request.data["datasheet_id"]
    logger.update_context({"filter_id_to_invalidate": filter_id_to_invalidate})
    ###################### audit log #####################
    event_type_code = EVENT[f"DELETE_DATASHEET-{config_type}"]["code"]
    event_key = request.data["filter_id"]
    summary = "Invalidated {}: ".format(config_type.lower()) + filter_name
    audit_data = {}
    updated_by = request.audit["updated_by"]
    updated_at = time
    ######################################################
    try:
        if not DatasheetFilterAccessor(client_id).is_filter_id_exist(
            filter_id_to_invalidate
        ):
            logger.info(
                "{} with Id {} doesn't exist to delete".format(
                    config_type.capitalize(), filter_id_to_invalidate
                )
            )
            return Response(
                {
                    "status": "FILTER_ID_DOESNT_EXIST",
                    "filter_id": request.data["filter_id"],
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        DatasheetFilterAccessor(client_id).invalidate_filter(
            filter_id_to_invalidate, time
        )
        logger.info(
            "Invalidated {} with Id {} successfully".format(
                config_type.lower(), filter_id_to_invalidate
            )
        )
        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )

        from everstage_ddd.datasheet.utils.datasheet_utils import (
            handle_datasheet_view_delete_from_v1,
        )

        handle_datasheet_view_delete_from_v1(
            client_id=client_id,
            datasheet_id=datasheet_id,
            view_id=filter_id_to_invalidate,
            current_user=updated_by,
        )
        return Response(
            {
                "status": "SUCCESS",
                "filter_id": filter_id_to_invalidate,
            },
            status=status.HTTP_201_CREATED,
        )
    except Exception as e:
        error_dict = {
            "trace_back": traceback.print_exc(),
        }
        logger.error(
            "Exception in deleting {} with ID {}".format(
                config_type.lower(), filter_id_to_invalidate
            ),
            error_dict,
        )
        raise SQLParseError() from e


def get_datasheet_variables(client_id, datasheet_id, logger):
    """
    Description: Function which takes datasheet_id as input param
    and returns list of present datasheet variables.
    Input : datasheet_id -> UUID string
    Output: list of variables system name.
    """
    logger.info("Fetching datasheet variables for datasheet_id {}".format(datasheet_id))
    datasheet_variables = DatasheetVariableAccessor(
        client_id
    ).get_datasheet_vars_system_name(datasheet_id)
    return datasheet_variables


def get_calculated_fields(vars_from_table):
    calc_vars = []
    for var in vars_from_table:
        if var.field_order > 0:
            calc_vars.append(var)
    return calc_vars


def is_window_calculated_field(variable):
    """
    returns true if the given calculated field is a window based field
    """
    return variable.get("field_order", 0) > 0 and (
        "meta_data" in variable
        and (
            "rank" in variable["meta_data"]
            or "rolling" in variable["meta_data"]
            or "hierarchy" in variable["meta_data"]
        )
        and (
            variable["meta_data"].get("rank") is not None
            or variable["meta_data"].get("rolling") is not None
            or is_hierarchy_calculated_field(variable)
        )
    )


def is_hierarchy_calculated_field(variable):
    has_hierarchy = pydash.get(variable, "meta_data.hierarchy") is not None
    return has_hierarchy


def get_hierarchy_reference_sheet_id(variable: dict, as_uuid: bool = False):
    """
    Returns the reference sheet id for the hierarchy calculated field

    Args:
        variable: variable dict
        as_uuid: if True, returns the reference sheet id as uuid

    Returns:
        reference sheet id: str or uuid

    Examples:
        >>> get_hierarchy_reference_sheet_id(variable)
        '5891ae79-c416-4a25-bb43-1b7f060e4a68'
        >>> get_hierarchy_reference_sheet_id(variable, as_uuid=True)
        UUID('5891ae79-c416-4a25-bb43-1b7f060e4a68')
    """
    hierarchy_meta_data = HierarchyMetaData(**variable["meta_data"]["hierarchy"])
    ref_id = hierarchy_meta_data.reference_sheet

    # if reference sheet is current sheet,
    # then do not honor the as_uuid flag and return the ref_id as it is
    if ref_id == HierarchyUtils.USE_CURRENT_SHEET_DEFAULT_VALUE.value:
        return ref_id

    if as_uuid:
        return uuid.UUID(ref_id)

    return ref_id


def modify_hierarchy_meta_data_for_key(variable: dict, key: str, value: str):
    """
    Sets the reference sheet id for the hierarchy calculated field.
    *Note*: This mutates the variable passed in

    Args:
        variable: hierarchy variable dict
        key: key in variable meta data to be modified
        value: value to be updated

    Examples:
        >>> set_hierarchy_reference_sheet_id(variable, "reference_sheet", '5891ae79-c416-4a25-bb43-1b7f060e4a68')
        >>> variable
        {
            "meta_data": {
                "hierarchy": {
                    "reference_sheet": "5891ae79-c416-4a25-bb43-1b7f060e4a68"
                }
                "args": {
                    "hierarchy": { "reference_sheet": "5891ae79-c416-4a25-bb43-1b7f060e4a68" }
                }
            }
        }
    """
    variable["meta_data"]["hierarchy"][key] = value

    variable["meta_data"]["args"]["hierarchy"][key] = value


def validate_calculated_fields(
    client_id,
    variables,
    unselected_variables,
    source_variables,
    databook_id=None,
    datasheet_id=None,
    logger=None,
    expression_box_version="v1",
):
    logger = LogWithContext({}) if logger is None else logger
    res = {"errors": []}

    variables_names_map = {variable["system_name"]: variable for variable in variables}
    unselected_variables_names_map = {
        variable["system_name"]: variable for variable in unselected_variables
    }
    source_variables_names_map = {
        variable["system_name"]: variable for variable in source_variables
    }

    data_types = get_datatype_id_name_map()

    missing_dependencies_map = {}

    def is_calculated_field(variable):
        return (
            variable.get("field_order", 0) > 0
            or (
                "meta_data" in variable
                and "infix" in variable["meta_data"]
                and variable["meta_data"]["infix"] is not None
            )
        ) or (is_window_calculated_field(variable))

    def is_window_calculated_field(variable):
        return "meta_data" in variable and (
            variable["meta_data"].get("rank") is not None
            or variable["meta_data"].get("rolling") is not None
            or is_hierarchy_calculated_field(variable)
        )

    def init_dependency_stack(dependency_stack):
        dependency_stack = list() if dependency_stack is None else dependency_stack
        return dependency_stack

    def check_dependencies_in_expression(dependency_stack=None, variable_name=None):
        dependency_stack = init_dependency_stack(dependency_stack)
        ast = variable["meta_data"]["ast"]
        dependencies = VariableExtractor().get_variables_used(ast)
        missing_dependencies = list(set(dependencies) - set(variables_names_map.keys()))
        if missing_dependencies:
            missing_dependencies_map[variable_name] = missing_dependencies
            return

    def check_dependencies_in_window_calc_fields_expression(req_vars, variable_name):
        missing_dependencies = list(set(req_vars) - set(variables_names_map.keys()))
        if missing_dependencies:
            missing_dependencies_map[variable_name] = missing_dependencies
            return

    def check_variable_dependencies(variable, dependency_stack=None):
        variable_name = variable["system_name"]
        dependency_stack = init_dependency_stack(dependency_stack)
        dependency_stack.append(variable_name)
        if variable_name in missing_dependencies_map:
            return
        if is_calculated_field(variable):
            if is_window_calculated_field(variable):
                req_vars = extract_window_calculated_field_source_variables(variable)
                check_dependencies_in_window_calc_fields_expression(
                    req_vars, variable_name
                )
            else:
                check_dependencies_in_expression(
                    dependency_stack=dependency_stack,
                    variable_name=variable_name,
                )

    def check_expression_data_type(variable):
        import commission_engine.services.commission_calculation_service.criteria_calculation_service as ccs

        if not is_calculated_field(variable) or is_window_calculated_field(variable):
            return

        validation_context = ValidationContextModel(
            databook_id=databook_id if databook_id else "",
            datasheet_id=datasheet_id,
            context=FunctionContext.DATASHEET,
            context_meta=ContextMetaDataModel(
                datasheet=DatasheetContext.CALCULATED_FIELDS
            ),
            skip_copy_paste_validation=True,
            additional_variables=variables + unselected_variables,
        )

        try:
            validation_res = ccs.validate_infix_wrapper(
                client_id=client_id,
                infix_exp=variable["meta_data"]["infix"],
                context=validation_context,
                skip_brackets=True,
            )
        except VariableNotFoundException as exc:
            exc.message = f"Calculated field '{variable['display_name']}' is invalid: {exc.message}"
            raise exc

        if validation_res["status"] == "INVALID":
            res["errors"].append(
                "Calculated field '{}' is invalid: {}".format(
                    variable["display_name"],
                    validation_res["msg"],
                )
            )
        elif validation_res["data_type_id"] != variable[
            "data_type_id"
        ] and not compare_if_integer_or_percentage(
            validation_res["data_type_id"], variable["data_type_id"]
        ):
            res["errors"].append(
                "Calculated field '{}' is invalid: This is a {} expression. Please provide a {} expression".format(
                    variable["display_name"],
                    data_types[validation_res["data_type_id"]],
                    data_types[variable["data_type_id"]],
                )
            )

    for variable in variables:
        # 1. Validate the expression data type
        check_expression_data_type(variable)

        # 2. Add `ast` to variable["meta_data"]
        if is_calculated_field(variable) and not is_window_calculated_field(variable):
            # adding "ast" to meta_data to store it in the datasheet_variable table
            # ast is currently used in commission trace

            if expression_box_version == "v2":
                # For v2, infix_expression must be populated with AST_META token before creating AST

                # Reason adding meta AST_META token after `check_expression_data_type()` is intentional.
                # For check_expression_data_type validation, AST_META will automatically be added.
                infix_expression_with_ast_meta = add_meta_to_calculated_fields(
                    client_id,
                    variable["meta_data"],
                    context=AstMetaContext(
                        expression_type=DatasheetCriteriaTypes(
                            variable["meta_data"]["criteria_type"]
                        ),
                        databook_id=databook_id,
                        datasheet_id=datasheet_id,
                        additional_variables=variables + unselected_variables,
                    ),
                )

                ast = create_ast(infix_expression_with_ast_meta)["ast"]
                variable["meta_data"]["ast"] = ast
            else:
                # For v1, infix_expression is already sufficient to create AST
                infix_expression = variable["meta_data"]["infix"]
                ast = create_ast(infix_expression)["ast"]
                variable["meta_data"]["ast"] = ast

        # 3. Check if the variable has dependencies
        check_variable_dependencies(variable)

    cyclic_dependencies_res = DatasheetVariablesGraph(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        variables=variables,
        expression_box_version=expression_box_version,
    ).get_cyclic_dependencies()
    if cyclic_dependencies_res:
        from spm.custom_exceptions.custom_exceptions import (
            CycleFoundOnDatasheetVariableGraph,
        )

        error_message = CycleFoundOnDatasheetVariableGraph(
            display_name1=cyclic_dependencies_res[0],
            display_name2=cyclic_dependencies_res[1],
        ).message
        res["errors"].append(error_message)
        # Dont raise exception here, because we need to show all the errors to the user

    if missing_dependencies_map:
        error_msgs = []
        variables_from_transformations = []
        for (
            calc_var_name,
            missing_dependencies,
        ) in missing_dependencies_map.items():
            missing_dependencies_names = []
            for dependency_name in missing_dependencies:
                # Unselected_variables_names_map contains variables information which are not selected in the datasheet
                if dependency_name in unselected_variables_names_map:
                    missing_dependencies_names.append(
                        unselected_variables_names_map.get(dependency_name).get(
                            "display_name"
                        )
                    )

                # Source_variables_names_map contains variables information which are from the source sheet
                elif dependency_name in source_variables_names_map:
                    missing_dependencies_names.append(
                        source_variables_names_map.get(dependency_name).get(
                            "display_name"
                        )
                    )

                # Check if the missing variables are present in
                # the unselected_variables_names_map and source_variables_map
                # if Not then append all the missing system_names in this else block

                # This all missing varibles must be created from transformation results variables
                else:
                    dependency_name = remove_prefixes_lhs_rhs(word=dependency_name)
                    variables_from_transformations.append(dependency_name)

            # For the given system_names fetching the display_names
            # here why we are getting display names using client_id filter only
            # and not datasheet_id filter ?

            # User first create one transformations (let say join transformations)
            # And then he create a calc field using that transformation join result
            # And finally before validating the datasheet he delete/remove the first
            # join transformation mean.. the transformation result which is used to create the
            # calc field also invalid. This all happens before validating and save a datasheet
            # If here we use datasheet_id filter we only get variables used on that datasheet

            # But the missing variables can be from any datasheet
            # For correctly get the display name we use client_id filter only here

            records = get_datasheet_display_names_using_client_id(
                client_id=client_id, system_names=variables_from_transformations
            )
            display_names = [record["display_name"] for record in records]
            if len(display_names) > 0:
                missing_dependencies_names.extend(display_names)

            # The missing variables can be from sources of this current datasheet
            # let say the current datasheet have 10 source all 10 source have this same missing
            # variables then we will get 10 times same missing variables but we need to display only once in UI
            # to remove the duplicates here we are using set
            missing_dependencies_names = list(set(missing_dependencies_names))
            calc_var_display_name = variables_names_map.get(calc_var_name).get(
                "display_name"
            )
            if len(missing_dependencies) != len(missing_dependencies_names):
                logger.error(
                    "Calculated Field '{}' has dependency with unknown variables!".format(
                        calc_var_display_name
                    ),
                    extra_context={
                        "calculated_field": variables_names_map.get(calc_var_name),
                        "missing_dependencies": missing_dependencies,
                        "variables": variables,
                        "unselected_variables": unselected_variables,
                        "source_variables": source_variables,
                    },
                )
                error_msgs.append(
                    "There seems to be an error with the configuration of the calculated field '{b}'. Please unselect '{b}' to proceed.".format(
                        b=calc_var_display_name
                    )
                )
            else:
                error_msgs.append(
                    "Variables {a} are used in calculated field '{b}' in the current sheet. Please unselect '{b}' to proceed".format(
                        a=missing_dependencies_names, b=calc_var_display_name
                    )
                )
        res["errors"].extend(error_msgs)

    return res


def check_if_source_has_permission(
    source_type, source_id, client_id, user_email, databook_id, transformation_spec_list
):
    has_permission = True
    if (
        source_type == DATASHEET_SOURCE.OBJECT.value
        or source_type == DATASHEET_SOURCE.REPORT.value
    ):
        objects_without_permission = get_objects_excluded_for_user(
            client_id, user_email, source_type
        )
        if source_id in objects_without_permission:
            has_permission = False
            return has_permission

    elif source_type == DATASHEET_SOURCE.DATASHEET.value:
        co_permission_for_datasheet = (
            does_user_has_permission_to_custom_objects_in_datasheet(
                client_id,
                databook_id,
                source_id,
                user_email,
                knowledge_date=None,
            )
        )

        if not co_permission_for_datasheet:
            has_permission = False
            return has_permission

    if transformation_spec_list:
        for i in transformation_spec_list:
            if i["type"] in ["JOIN", "UNION"]:
                co_permission_for_datasheet = (
                    does_user_has_permission_to_custom_objects_in_datasheet(
                        client_id,
                        databook_id,
                        i["with"],
                        user_email,
                        knowledge_date=None,
                    )
                )

                if not co_permission_for_datasheet:
                    has_permission = False
                    return has_permission

    return has_permission


def is_datasheet_valid(
    client_id,
    databook_id,
    datasheet_id,
    selected_vars,
    transformation_spec_list,
    selected_vars_data,
    unselected_vars_data,
    source_type,
    source_id,
    data_origin,
    user_email,
    logger,
    expression_box_version="v1",
):
    """
    Description: Function which takes databook_id, datasheet_id, selected_vars
                 as input param and returns True if datasheet is syntactically valid else False.
    Input : datasheet_id -> UUID string
    Output: True/False
    """
    from spm.custom_exceptions.datasheet_exceptions import (
        DisplayNameLengthExceededError,
        InvalidSourceTypeException,
    )

    # When the `expressionbox_version` client feature flag is set to 'v2', the infix expression of
    # `ADVANCED_FILTER` transformation will be in 'v2' format. To make it usable for
    # calculations (create_ast) we are adding `AST_META` token to the infix expression.
    if expression_box_version == "v2":
        transformation_spec_list = add_meta_to_advanced_filter_infix(
            client_id=client_id,
            transformation_spec=transformation_spec_list,
            context=AstMetaContext(
                databook_id=str(databook_id),
                datasheet_id=str(datasheet_id),
            ),
        )

    for var in selected_vars_data:
        if var["display_name"] and len(var["display_name"]) > 254:  # noqa: PLR2004
            raise DisplayNameLengthExceededError(display_name=var["display_name"])

    databook_object = DatabookAccessor(client_id=client_id).get_databook_by_id(
        databook_id=databook_id
    )
    if pydash.get(databook_object, "is_archived", False) is False:
        # Check if any of the datasheet's source is in archived state
        # If yes then we dont allow the user to create such datasheets
        # Because the daily etl wont run for archived databook
        crosslinked_archived_databook_ids: list = (
            _get_datasheet_dependencies_in_archived_state(
                client_id=client_id,
                transformation_spec_list=transformation_spec_list,
                selected_variables_data=selected_vars_data,
                source_type=source_type,
                source_id=source_id,
            )
        )

        databook_names: dict = databook_names_by_ids(
            client_id=client_id, databook_ids=crosslinked_archived_databook_ids
        )
        if len(crosslinked_archived_databook_ids) > 0:
            result: dict = {
                "is_valid": False,
                "errors": [
                    DATASHEET_INVALID_REASON.DATASHEET_SOURCE_ARCHIVED.value.format(
                        databook_names=set(databook_names.values())
                    )
                ],
            }
            return result

    # Check is there any exist cycle in the datasheets
    datasheets_in_cycle = _will_produce_cycle(
        client_id=client_id,
        datasheet_id=datasheet_id,
        transformation_spec_list=transformation_spec_list,
        selected_variables_data=selected_vars_data,
    )
    if len(datasheets_in_cycle) > 0:
        datasheets_in_cycle = ", ".join(datasheets_in_cycle)
        result = {
            "is_valid": False,
            "errors": [
                DATASHEET_INVALID_REASON.CYCLE_FORMATION_IN_DATASHEET.value.format(
                    datasheet_names=datasheets_in_cycle
                )
            ],
        }

        return result

    logger.info("Checking datasheet validity for datasheet_id {}".format(datasheet_id))

    if not check_if_source_has_permission(
        source_type,
        source_id,
        client_id,
        user_email,
        databook_id,
        transformation_spec_list,
    ):
        logger.error(
            "You can't perform this action since you don't have access to the underlying data"
        )
        final_output = {
            "is_valid": False,
            "errors": [
                "You can't perform this action since you don't have access to the underlying data"
            ],
        }
        return final_output

    ds_acc = DatasheetAccessor(client_id=client_id)

    # Check its source databook is in arcived state
    # If yes dont allow to create a datasheet from it because the daily etl wont run for archived databook
    # If we allow this leads to data inconsistency

    ds_list: list = ds_acc.get_all_datasheets()

    priority_map = databook_utils.get_ordered_datasheets(ds_list)
    run_time_ds_vars = {}
    final_output = {"is_valid": True, "errors": []}

    # Datasheet used in a criteria should not be modified to have commission object origin
    if datasheet_id and data_origin in [
        report_utils.DataOrigin.COMMISSION_OBJECT.value,
        report_utils.DataOrigin.FORECAST_OBJECT.value,
        report_utils.DataOrigin.INTER_FORECAST_OBJECT.value,
    ]:
        # Importing here to avoid circular import
        from spm.services.commission_plan_services import (
            is_datasheets_used_in_any_commission_plan,
            is_datasheets_used_in_any_settlement_rule,
        )

        is_datasheets_used_in_commission_plan: bool = (
            is_datasheets_used_in_any_commission_plan(
                client_id=client_id,
                datasheet_ids=[datasheet_id],
                commission_type=COMMISSION_TYPE.COMMISSION,
            )
        )

        is_datasheets_used_in_settlement_rule: bool = (
            is_datasheets_used_in_any_settlement_rule(
                client_id=client_id, datasheet_ids=[datasheet_id]
            )
        )
        if (
            is_datasheets_used_in_commission_plan
            or is_datasheets_used_in_settlement_rule
        ):
            final_output["is_valid"] = False
            report_name = (
                "Commission report"
                if data_origin == report_utils.DataOrigin.COMMISSION_OBJECT.value
                else "Forecast report"
            )
            final_output["errors"].append(
                f"As datasheet included in Commission Plan, {report_name} can't be used."
            )
            return final_output
    if datasheet_id and data_origin == report_utils.DataOrigin.FORECAST_OBJECT.value:
        from spm.services.commission_plan_services import (
            is_datasheets_used_in_any_commission_plan,
        )

        is_datasheets_used_in_any_forecast_plan: bool = (
            is_datasheets_used_in_any_commission_plan(
                client_id=client_id,
                datasheet_ids=[datasheet_id],
                commission_type=COMMISSION_TYPE.FORECAST,
            )
        )
        if is_datasheets_used_in_any_forecast_plan:
            final_output["is_valid"] = False
            final_output["errors"].append(
                "As datasheet included in Forecast Plan, Forecast report can't be used."
            )
            return final_output

    current_sheet_source_vars = []

    if source_type == DATASHEET_SOURCE.OBJECT.value:
        current_sheet_source_vars = CustomObjectVariableAccessor(
            client_id
        ).get_variables_by_object_id(source_id, as_dicts=True)
    elif source_type == DATASHEET_SOURCE.REPORT.value:
        for report_object_id in report_utils.get_report_object_ids():
            if report_object_id == source_id:
                current_sheet_source_vars = [
                    var
                    for var in report_utils.get_report_variables(
                        report_object_id=source_id, client_id=client_id
                    )
                ]
                break
    elif source_type == DATASHEET_SOURCE.DATASHEET.value:
        current_sheet_source_vars = DatasheetVariableAccessor(
            client_id
        ).get_objects_by_datasheet_id(source_id, as_dicts=True)

    else:
        raise InvalidSourceTypeException(source_type=source_type)

    calc_vars_res = {}
    try:
        calc_vars_res = validate_calculated_fields(
            client_id=client_id,
            variables=selected_vars_data,
            unselected_variables=unselected_vars_data,
            source_variables=current_sheet_source_vars,
            databook_id=databook_id,
            datasheet_id=datasheet_id,
            expression_box_version=expression_box_version,
        )
    except VariableNotFoundException as exc:
        calc_vars_res["errors"] = [exc.message]

    if calc_vars_res["errors"]:
        final_output["is_valid"] = False
        final_output["errors"].extend(calc_vars_res["errors"])

    hris_errors = validate_variables_used_in_hris(
        client_id=client_id, selected_vars=selected_vars, datasheet_id=datasheet_id
    )

    if not hris_errors["is_valid"]:
        final_output["is_valid"] = False
        final_output["errors"].append(hris_errors["errors"])

    for priority_id in sorted(priority_map):
        datasheets = priority_map[priority_id]
        for datasheet in datasheets:
            vars_from_table = DatasheetVariableAccessor(
                client_id
            ).get_objects_by_datasheet_id(datasheet.datasheet_id)
            calc_vars = get_calculated_fields(vars_from_table)
            calc_vars_names = [var.system_name for var in calc_vars]
            if datasheet.source_type == DATASHEET_SOURCE.OBJECT.value:
                source_vars = CustomObjectVariableAccessor(
                    client_id
                ).get_system_names_by_object_id(datasheet.source_id)
            elif datasheet.source_type == DATASHEET_SOURCE.REPORT.value:
                for report_object_id in report_utils.get_report_object_ids():
                    if report_object_id == datasheet.source_id:
                        source_vars = [
                            var.get("system_name")
                            for var in report_utils.get_report_variables(
                                report_object_id=datasheet.source_id,
                                client_id=client_id,
                            )
                        ]
            else:
                source_vars = run_time_ds_vars[str(datasheet.source_id)]
            source_vars.extend(calc_vars_names)
            if str(datasheet.datasheet_id) == str(datasheet_id):
                transformations = None
                source_vars = selected_vars  # check this line
            else:
                transformations = datasheet.transformation_spec
            vars_from_transformation = validate_and_transform_vars_from_spec(
                client_id,
                source_vars,
                transformations,
                datasheet,
                run_time_ds_vars,
                calc_vars_names,
            )
            if vars_from_transformation["errors"]:
                final_output["is_valid"] = False
                final_output["errors"].extend(vars_from_transformation["errors"])
            # if str(datasheet.datasheet_id) == datasheet_id:
            #     calc_vars_res = check_calc_fields_dependencies(
            #         datasheet,
            #         selected_vars_data,
            #         vars_from_table
            #     )
            #     if calc_vars_res["errors"]:
            #         final_output["is_valid"] = False
            #         final_output["errors"].extend(
            #             calc_vars_res["errors"]
            #         )

            hierarchy_calc_fields_validations = validate_hierarchy_calc_fields_vars(
                client_id, calc_vars, selected_vars, datasheet_id, datasheet
            )
            if hierarchy_calc_fields_validations["errors"]:
                final_output["is_valid"] = False
                final_output["errors"].extend(
                    hierarchy_calc_fields_validations["errors"]
                )

            if str(datasheet.datasheet_id) == datasheet_id:
                run_time_ds_vars[str(datasheet.datasheet_id)] = selected_vars
            else:
                run_time_ds_vars[str(datasheet.datasheet_id)] = (
                    vars_from_transformation["variables"]
                )  # datasheet_id or source_id?
            if str(datasheet.datasheet_id) == str(datasheet_id):
                vars_from_table = source_vars
            else:
                vars_from_table = [var.system_name for var in vars_from_table]
            deleted_vars = set(vars_from_table) - set(
                vars_from_transformation["variables"]
            )
            if deleted_vars:
                final_output["is_valid"] = False
                system_name_and_display_name = []
                if datasheet.source_type == "object":
                    system_name_and_display_name = DatasheetVariableAccessor(
                        client_id
                    ).get_display_names_from_system_names(
                        datasheet_id=datasheet.datasheet_id,
                        system_names=tuple(deleted_vars),
                    )
                elif datasheet.source_type == "datasheet":
                    system_name_and_display_name = DatasheetVariableAccessor(
                        client_id
                    ).get_display_names_from_system_names(
                        datasheet_id=datasheet.source_id,
                        system_names=tuple(deleted_vars),
                    )
                elif datasheet.source_type == "report":
                    system_name_and_display_name = report_utils.get_report_variables(
                        report_object_id=datasheet.source_id, client_id=client_id
                    )
                system_name_to_display_name_map = {}
                for ele in system_name_and_display_name:
                    system_name_to_display_name_map[ele["system_name"]] = ele[
                        "display_name"
                    ]
                display_names = []
                for var in deleted_vars:
                    display_names.append(system_name_to_display_name_map[var])
                final_output["errors"].append(
                    "Variables {} are present in datasheet '{}', Please remove it to save here".format(
                        display_names, datasheet.name
                    )
                )
    if datasheet_id:
        deleted_variables_errors = (
            datasheet_validation.validate_deleted_datasheet_variables(
                client_id, datasheet_id, selected_vars
            )
        )

        workflow_variables_errors = get_errors_for_missing_datasheet_columns(
            client_id, str(datasheet_id), selected_vars
        )
        if workflow_variables_errors:
            deleted_variables_errors["is_valid"] = False
            deleted_variables_errors["errors"].extend(workflow_variables_errors)

        if not deleted_variables_errors["is_valid"]:
            final_output["is_valid"] = False
            final_output["errors"].extend(deleted_variables_errors["errors"])

    datasheet_permissions = DatasheetPermissionsAccessor(client_id)
    all_filter_list_for_datasheet = datasheet_permissions.get_filter_list(
        databook_id, datasheet_id
    )
    columns_to_which_filter_applied = set()

    for filter_list in all_filter_list_for_datasheet:
        columns_to_which_filter_applied.add(filter_list["col_name"])

    hidden_columns_for_datasheet = (
        datasheet_permissions.get_all_hidden_columns_of_datasheet(datasheet_id)
    )

    unselected_filter_list_cols = columns_to_which_filter_applied.difference(
        set(selected_vars)
    )
    unselected_hidden_cols = set(hidden_columns_for_datasheet).difference(
        set(selected_vars)
    )

    unselected_cols_with_permission = unselected_filter_list_cols.union(
        unselected_hidden_cols
    )
    system_name_and_display_name = DatasheetVariableAccessor(
        client_id
    ).get_display_names_from_system_names(
        datasheet_id=datasheet_id, system_names=list(unselected_cols_with_permission)
    )
    display_name_mapping = {}
    for var in system_name_and_display_name:
        if var["system_name"] not in display_name_mapping:
            display_name_mapping[var["system_name"]] = var["display_name"]
    if unselected_cols_with_permission:
        final_output["is_valid"] = False
        final_output["errors"].append(
            "Please remove the following field(s) from datasheet permissions before validating: {}".format(
                [
                    display_name_mapping[x] if x in display_name_mapping else x
                    for x in unselected_cols_with_permission
                ]
            )
        )

    final_output["errors"] = list(set(final_output["errors"]))
    # To ensure that we didn't show the same error message multiple times,
    #
    # when does this happen?
    # Let's say we have datasheet d1, from which we have three datasheets: d2, d3, and d4.
    # By joining all three datasheets (d2, d3, d4), we derive a datasheet, d5. If we
    # remove variables from datasheet d5, it is possible to show the error message
    # multiple times because all these datasheets come from one source datasheet, d1.
    return final_output


def has_config_changed(client_id, databook_id, datasheet_id, selected_vars) -> bool:
    """
    This function is used to check if the datasheet config has changed. (Datasheet V1)
    """
    is_part_of_workflow = is_databook_datasheet_part_of_any_workflow(
        client_id=client_id,
        databook_id=str(databook_id),
        datasheet_id=str(datasheet_id),
    )

    if not is_part_of_workflow:
        return False

    variables_copy = []

    for variable in selected_vars:
        if "field_order" not in variable:
            variable["field_order"] = 0
        if "meta_data" not in variable:
            variable["meta_data"] = None
        variable["data_type"] = variable["data_type_id"]
        variables_copy.append(variable)

    existing_vars = DatasheetVariableAccessor(client_id).get_variables_for_db_ds(
        databook_id, datasheet_id, as_dicts=True
    )
    is_pk_modified, is_config_changed, is_calc_field_changed = _has_config_changed(
        existing_vars, variables_copy
    )
    return is_pk_modified or is_config_changed or is_calc_field_changed


def filter_validate_and_output(source_vars, spec):
    result_dict = {"output_columns": source_vars, "missing_columns": []}
    if spec["col_name"] not in set(source_vars):
        result_dict["missing_columns"].append(spec["col_name"])
    return result_dict


def sort_validate_and_output(source_vars, spec):
    result_dict = {"output_columns": source_vars, "missing_columns": []}
    missing_vars = set(spec["on"]) - set(source_vars)
    if missing_vars:
        result_dict["missing_columns"].extend(list(missing_vars))
    return result_dict


def union_validate_and_output(source_vars, spec, run_time_ds_vars):
    # seperate columns of lhs and rhs
    # compare lhs column with source vars
    # compare rhs column with runtimevarsmap['with']
    result_dict = {"output_columns": [], "missing_columns": []}
    on_cols = spec["on"]
    lhs_vars, rhs_vars = set(), set()
    for each_on in on_cols:
        lhs_vars.add(each_on["lhs"])
        rhs_vars.add(each_on["rhs"])
        result_dict["output_columns"].append(each_on["col_name"])
    lhs_missing_vars = set(lhs_vars) - set(source_vars)
    rhs_missing_vars = set(rhs_vars) - set(run_time_ds_vars[str(spec["with"])])
    result_dict["missing_columns"].extend(list(lhs_missing_vars))
    result_dict["missing_columns"].extend(list(rhs_missing_vars))
    return result_dict


def join_validate_and_output(source_vars, spec, run_time_ds_vars):
    result_dict = {"output_columns": [], "missing_columns": []}
    on_cols = spec["on"]
    lhs_vars, rhs_vars = set(on_cols["lhs"]), set(on_cols["rhs"])
    entire_cols = spec["columns"]
    for col in entire_cols:
        if col.startswith("lhs_"):
            lhs_vars.add(col)
        elif col.startswith("rhs_"):
            rhs_vars.add(col)
    lhs_append_source_vars, rhs_appended_source_vars = set(), set()
    for each_var in source_vars:
        lhs_append_source_vars.add("lhs_" + each_var)
    for each_var in run_time_ds_vars[str(spec["with"])]:
        rhs_appended_source_vars.add("rhs_" + each_var)
    lhs_missing_vars = lhs_vars - set(lhs_append_source_vars)
    rhs_missing_vars = rhs_vars - set(rhs_appended_source_vars)
    lhs_removed_missing_vars, rhs_removed_missing_vars = set(), set()
    for each_var in lhs_missing_vars:
        lhs_removed_missing_vars.add(each_var[4:])
    for each_var in rhs_missing_vars:
        rhs_removed_missing_vars.add(each_var[4:])
    if lhs_missing_vars:
        result_dict["missing_columns"].extend(list(lhs_removed_missing_vars))
    if rhs_missing_vars:
        result_dict["missing_columns"].extend(list(rhs_removed_missing_vars))
    result_dict["output_columns"].extend(spec["columns"])
    return result_dict


def groupby_validate_and_output(source_vars, spec):
    result_dict = {"output_columns": [], "missing_columns": []}
    cols_to_check = set(spec["by"])
    aggregations = spec["aggregations"]
    for each_agg in aggregations:
        result_dict["output_columns"].append(each_agg["col_name"])
        cols_to_check.add(each_agg["of"])
    missing_vars = set(cols_to_check) - set(source_vars)
    if missing_vars:
        result_dict["missing_columns"].extend(list(missing_vars))
    result_dict["output_columns"].extend(spec["by"])
    return result_dict


def advanced_filter_validate_and_output(source_vars, spec):
    result_dict = {"output_columns": source_vars, "missing_columns": []}

    filter_variables = _get_variables_from_advanced_filter(spec)
    result_dict["missing_columns"] = list(set(filter_variables) - set(source_vars))
    return result_dict


def flatten_validate_and_output(source_vars, spec):
    """
    This function validates the flatten spec and returns the output columns and missing columns
    """
    flattend_col_name = spec["flattened_col_name"]
    source_vars.append(flattend_col_name)
    result_dict = {"output_columns": source_vars, "missing_columns": []}
    if spec["col_name"] not in set(source_vars):
        result_dict["missing_columns"].append(spec["col_name"])
    return result_dict


def validate_get_user_properties_spec(source_vars, spec):
    """
    This function validates the get_user_properties spec
    and returns the output columns and missing columns
    """
    result_dict = {"output_columns": source_vars, "missing_columns": []}
    transformation_meta = GetUserPropertiesTransformationMeta(**spec)
    source_vars_set = set(source_vars)

    email_column = transformation_meta.email_column
    as_of_date_column = transformation_meta.as_of_date_column
    user_properties = transformation_meta.user_properties

    # check if email_column and as_of_date_column are present in source_vars
    # because these two columns are required for the transformation
    if email_column not in source_vars_set:
        result_dict["missing_columns"].append(email_column)
    if (
        as_of_date_column != GetUserPropertiesTransformationUtils.CURRENT_DATE_KEY.value
        and as_of_date_column not in source_vars_set
    ):
        result_dict["missing_columns"].append(as_of_date_column)

    for user_property in user_properties:
        result_dict["output_columns"].append(user_property.output_variable_system_name)

    return result_dict


def _get_variables_from_advanced_filter(spec):
    ast = create_ast(spec["infix"])
    return VariableExtractor().get_variables_used(ast["ast"])


def temporal_splice_validate_and_output(spec, run_time_ds_vars, source_vars):
    result_dict = {"output_columns": [], "missing_columns": []}

    std_vars = [
        "ts_employee_email_id",
        "ts_effective_start_date",
        "ts_effective_end_date",
    ]
    all_vars = []
    ds_vars = []
    rt_ds_vars = []
    for index, datasource in enumerate(spec["meta"]):
        all_vars.extend([var["system_name"] for var in datasource["input_columns"]])
        if datasource["source_type"] == DATASHEET_SOURCE.DATASHEET.value:
            ds_vars.extend([var["system_name"] for var in datasource["input_columns"]])
            if index == 0:
                rt_ds_vars.extend(
                    ["ts_{}_{}".format(index + 1, var) for var in source_vars]
                )
            else:
                rt_ds_vars.extend(
                    [
                        "ts_{}_{}".format(index + 1, var)
                        for var in run_time_ds_vars[str(datasource["source_id"])]
                    ]
                )

    result_dict["output_columns"] = list(set(all_vars)) + std_vars
    result_dict["missing_columns"] = list(set(ds_vars) - set(rt_ds_vars))

    return result_dict


def _get_variables_used_in_temporal_splice(spec):
    all_vars = [
        var["system_name"]
        for datasource in spec["meta"]
        for var in datasource["input_columns"]
    ]
    return list(set(all_vars))


def _get_variables_used_in_get_user_properties(spec):
    transformation_meta = GetUserPropertiesTransformationMeta(**spec)
    as_of_date_column = transformation_meta.as_of_date_column

    variable_list = [transformation_meta.email_column]

    if as_of_date_column != GetUserPropertiesTransformationUtils.CURRENT_DATE_KEY.value:
        variable_list.append(as_of_date_column)

    return variable_list


def get_variables_used_in_transformation_spec_list(client_id, transformation_spec_list):
    """
    Description: Function which takes client id and transformation spec as input param
                 and returns list of variables used in spec
    Input : client_id -> client id
            transformation_spec_list -> List of transformation spec [{},{}]
            (version can be v1 or v2 depends on the current version of the datasheet)
    Output: list of system names of variables used in spec
    """
    if not transformation_spec_list:
        return []
    output_list = []

    if has_feature(client_id, "show_data_sources_v2"):
        spec_transform = ETLSpecTransformer(transformation_spec_list)
        spec_transform.transform()
        transformation_spec_list = spec_transform.transformations

    for spec in transformation_spec_list:
        if spec["type"] == DATASHEET_TRANSFORMATIONS.FILTER.name:
            output_list.append(spec["col_name"])
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.SORT.name:
            output_list.extend(spec["on"])
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.UNION.name:
            for var in spec["on"]:
                output_list.append(var["lhs"])
                output_list.append(var["rhs"])
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.JOIN.name:
            output_list.extend(spec["on"]["lhs"])
            output_list.extend(spec["on"]["rhs"])
            output_list.extend(spec["columns"])
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.GROUP_BY.name:
            output_list.extend(spec["by"])
            for each_agg in spec["aggregations"]:
                output_list.append(each_agg["of"])
        elif spec["type"] in [
            DATASHEET_TRANSFORMATIONS.ADVANCED_FILTER.name,
            DATASHEET_TRANSFORMATIONS.ADVANCED_FILTER_V2.name,
        ]:
            output_list.extend(_get_variables_from_advanced_filter(spec))
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.TEMPORAL_SPLICE.name:
            output_list.extend(_get_variables_used_in_temporal_splice(spec))
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.FLATTEN.name:
            output_list.append(spec["col_name"])
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.GET_USER_PROPERTIES.name:
            output_list.extend(_get_variables_used_in_get_user_properties(spec))
        else:
            raise Exception("Invalid Filter Type")

    return list(set(output_list))


def validate_and_transform_vars_from_spec(
    client_id,
    source_vars,
    transformation_spec_list,
    datasheet_object,
    run_time_ds_vars,
    calc_vars,
):
    """
    Description: Function which takes source_vars and spec as input param
                 and returns vars after applying transformation spec, if its valid
                 else returns error message.
    Input : source_vars -> list of source variables
            transformation_spec_list -> List of transformation spec [{},{}]
    Output: {'errors': [], 'variables':[]}
            Here both list is mutually exclusive, if errors is not empty, then there will be no variables
    """
    if not transformation_spec_list:
        return {"errors": [], "variables": source_vars}
    output_dict = {"errors": [], "variables": []}
    for spec in transformation_spec_list:
        if spec["type"] == DATASHEET_TRANSFORMATIONS.FILTER.name:
            result = filter_validate_and_output(source_vars, spec)
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.JOIN.name:
            result = join_validate_and_output(source_vars, spec, run_time_ds_vars)
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.SORT.name:
            result = sort_validate_and_output(source_vars, spec)
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.UNION.name:
            result = union_validate_and_output(source_vars, spec, run_time_ds_vars)
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.GROUP_BY.name:
            result = groupby_validate_and_output(source_vars, spec)
        elif spec["type"] in [
            DATASHEET_TRANSFORMATIONS.ADVANCED_FILTER.name,
            DATASHEET_TRANSFORMATIONS.ADVANCED_FILTER_V2.name,
        ]:
            result = advanced_filter_validate_and_output(source_vars, spec)
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.TEMPORAL_SPLICE.name:
            result = temporal_splice_validate_and_output(
                spec, run_time_ds_vars, source_vars
            )
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.FLATTEN.name:
            result = flatten_validate_and_output(source_vars, spec)
        elif spec["type"] == DATASHEET_TRANSFORMATIONS.GET_USER_PROPERTIES.name:
            result = validate_get_user_properties_spec(source_vars, spec)
        else:
            raise Exception("Invalid Filter Type")

        result["output_columns"].extend(calc_vars)
        source_vars = result["output_columns"]
        if result["missing_columns"]:
            if datasheet_object.source_type == "datasheet":
                system_name_and_display_name = DatasheetVariableAccessor(
                    client_id
                ).get_display_names_from_system_names(
                    datasheet_id=datasheet_object.source_id,
                    system_names=tuple(result["missing_columns"]),
                )
            elif datasheet_object.source_type == DATASHEET_SOURCE.REPORT.value:
                report_vars = report_utils.get_report_variables(
                    report_object_id=datasheet_object.source_id, client_id=client_id
                )
                system_name_and_display_name = []
                for var in report_vars:
                    if var["system_name"] in result["missing_columns"]:
                        system_name_and_display_name.append(
                            {
                                "system_name": var["system_name"],
                                "display_name": var["display_name"],
                            }
                        )
                system_name_and_display_name = tuple(system_name_and_display_name)
            else:
                system_name_and_display_name = CustomObjectVariableAccessor(
                    client_id
                ).get_display_names_from_system_names(
                    datasheet_object.source_id,
                    tuple(result["missing_columns"]),
                )

            # If the transformation is a union or join, Then we also need to consider the rhs datasheet_id
            if spec["type"] in (
                DATASHEET_TRANSFORMATIONS.JOIN.name,
                DATASHEET_TRANSFORMATIONS.UNION.name,
            ):
                system_name_and_display_name_from_lhs = system_name_and_display_name
                datasheet_id = spec["with"]
                missing_system_names = tuple(result["missing_columns"])

                databook_id = str(spec["with_databook_id"])

                # Getting the display names for the missing system names from the rhs datasheet
                system_name_and_display_name_from_rhs = get_datasheet_display_names(
                    client_id=client_id,
                    databook_id=databook_id,
                    datasheet_id=datasheet_id,
                    system_names=missing_system_names,
                )
                # Combinining the display names from lhs and rhs sources
                system_name_and_display_name = (
                    *system_name_and_display_name_from_lhs,
                    *system_name_and_display_name_from_rhs,
                )

            if spec["type"] == DATASHEET_TRANSFORMATIONS.TEMPORAL_SPLICE.name:
                source_system_and_display_name = system_name_and_display_name
                variables_used_in_temporal_splice = []
                for row in spec["meta"]:
                    if row["source_type"] == DATASHEET_SOURCE.DATASHEET.value:
                        missing_system_names = tuple(result["missing_columns"])
                        system_name_and_display_name_list = get_datasheet_display_names(
                            client_id=client_id,
                            databook_id=row["with_databook_id"],
                            datasheet_id=row["source_id"],
                            system_names=missing_system_names,
                        )
                        variables_used_in_temporal_splice.extend(
                            system_name_and_display_name_list
                        )
                system_name_and_display_name = (
                    *source_system_and_display_name,
                    *variables_used_in_temporal_splice,
                )

            system_name_to_display_name_map = {}
            for ele in system_name_and_display_name:
                system_name_to_display_name_map[ele["system_name"]] = ele[
                    "display_name"
                ]

            for missing_col in result["missing_columns"]:
                output_dict["errors"].append(
                    "Variables {} used in {} Operation of {} datasheet".format(
                        system_name_to_display_name_map[missing_col],
                        spec["type"],
                        datasheet_object.name,
                    )
                )
    output_dict["variables"] = source_vars
    return output_dict


def validate_hierarchy_calc_fields_vars(
    client_id, calc_vars_arr, selected_vars, src_datasheet_id, current_datasheet
):
    """
    This function validates if any of the variables in the current datasheet
    which is used in the hierarchy calculated fields is selected or not.

    eg:
        If there are 2 datasheets with below columns,
            - A (x_col, y_col, z_col)
            - B (c_col)

        And if there is a hierarchy calculated field in B datasheet which uses x_col, y_col, z_col from A datasheet,
        then this function will check if x_col, y_col, z_col are selected in A datasheet or not.

    Args:
        calc_vars_arr (list): list of calculated fields variables
        selected_vars (list): list of selected variables
        src_datasheet_id (str): source datasheet id
        current_datasheet (Datasheet): current datasheet object
    """
    result = {"errors": []}
    for calc_vars in calc_vars_arr:
        if (
            not calc_vars
            or not calc_vars.meta_data
            or "hierarchy" not in calc_vars.meta_data
        ):
            continue

        meta_data = calc_vars.meta_data
        hierarchy_meta_data = HierarchyMetaData(**meta_data["hierarchy"])
        ref_id = hierarchy_meta_data.reference_sheet

        if src_datasheet_id != ref_id:
            continue

        parent_col = hierarchy_meta_data.parent_column
        child_col = hierarchy_meta_data.child_column
        start_time_col = hierarchy_meta_data.start_time_column
        end_time_col = hierarchy_meta_data.end_time_column

        hierarchy_vars = [parent_col, child_col]

        if start_time_col:
            hierarchy_vars.append(start_time_col)
        if end_time_col:
            hierarchy_vars.append(end_time_col)

        missed_vars = list(set(hierarchy_vars) - set(selected_vars))
        system_name_and_display_name = DatasheetVariableAccessor(
            client_id
        ).get_display_names_from_system_names(
            datasheet_id=src_datasheet_id,
            system_names=tuple(missed_vars),
        )

        system_name_to_display_name_map = {}
        for ele in system_name_and_display_name:
            system_name_to_display_name_map[ele["system_name"]] = ele["display_name"]

        missed_vars_display_names = []
        for missed_var in missed_vars:
            missed_vars_display_names.append(
                system_name_to_display_name_map[missed_var]
            )

        missed_vars_as_str = "'" + "', '".join(missed_vars_display_names) + "'"

        if missed_vars:
            if len(missed_vars) == 1:
                error_msg = f"Variable {missed_vars_as_str} is used in calculated field {calc_vars.display_name} in the {current_datasheet.name} sheet. Please select {missed_vars_as_str} to proceed"
            else:
                error_msg = f"Variables {missed_vars_as_str} are used in calculated field {calc_vars.display_name} in the {current_datasheet.name} sheet. Please select {missed_vars_as_str} to proceed"
            result["errors"].append(error_msg)

    return result


def get_datasheet_dependencies_details(client_id: int, datasheet_id: str) -> dict:
    """
    This function returns the dependent details of the datasheet.
    """
    from spm.services.datasheet_graph.datasheet_graph import DataSheetGraph

    datasheet_graph = DataSheetGraph(
        client_id=client_id,
        include_stale_information_query=False,
    )

    descendants_datasheet: set = datasheet_graph.descendants(datasheet_id=datasheet_id)
    descendants_datasheet_names = [
        descendant.name for descendant in descendants_datasheet
    ]

    databook_names: dict = databook_names_by_ids(
        client_id=client_id,
        databook_ids=[descendant.databook_id for descendant in descendants_datasheet],
    )
    dependent_datasheet_details = [
        {
            "datasheet_name": descendant.name,
            "datasheet_id": descendant.node_id,
            "databook_id": descendant.databook_id,
            "databook_name": databook_names.get(uuid.UUID(descendant.databook_id)),
        }
        for descendant in descendants_datasheet
    ]

    if descendants_datasheet_names:
        dependent_details = "This sheet is currently used in " + ", ".join(
            descendants_datasheet_names
        )
        return {
            "has_dependent": True,
            "dependent_details": dependent_details,
            "dependent_datasheet_details": dependent_datasheet_details,
        }
    return {
        "has_dependent": False,
        "dependent_details": "",
        "dependent_datasheet_details": [],
    }


def save_datasheet_order(client_id, databook_id, ds_order, logger):
    try:
        logger.info(
            "Saving new datasheet order for databook_id: {}".format(databook_id)
        )
        datasheets = DatasheetAccessor(client_id).get_datasheet_ids_by_databook_id(
            databook_id
        )
        datasheet_ids = {str(x.datasheet_id) for x in datasheets}
        if set(ds_order) != datasheet_ids:
            logger.info(
                "Datasheet order is not matching with number of datasheets in databook. Datasheet order: {}, Datasheets in databook: {}".format(
                    ds_order, datasheet_ids
                )
            )
            return Response(
                {
                    "message": "Datasheet order is not matching with number of datasheets in databook"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        DatabookAccessor(client_id).save_datasheet_order(
            databook_id=databook_id, ds_order=ds_order, modified_date=timezone.now()
        )
        return Response({"message": "Updated"}, status=status.HTTP_200_OK)
    except Exception as e:
        logger.info("Exception in save datasheet order: {}".format(e))
        return Response(
            {"message": "Exception"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def get_databook_by_ids(client_id, databook_ids, as_dicts=False):
    return DatabookAccessor(client_id).get_databook_by_db_ids(
        databook_ids=databook_ids, as_dicts=as_dicts
    )


def get_datasheet_display_names(
    client_id,
    databook_id,
    datasheet_id,
    system_names,
    crystal_calc_fields_override_logic_v2=False,
):
    if DatasheetAccessor(client_id).is_datasheet_exist(databook_id, datasheet_id):
        if crystal_calc_fields_override_logic_v2:
            return DatasheetVariableAccessor(
                client_id
            ).get_display_names_from_system_names_with_meta_data(
                datasheet_id=datasheet_id, system_names=system_names
            )
        else:
            return DatasheetVariableAccessor(
                client_id
            ).get_display_names_from_system_names(
                datasheet_id=datasheet_id, system_names=system_names
            )
    return []


def get_datasheet_graph_data(client_id: int, databook_id: str = None) -> dict:
    """
    returns a response with the following format:
    {
        "nodes": [{ id: '08c649bd-36e4-4cc8-bae6-6aff477f00ae', name: 'ds 6' },{ id: '2f4ce533-5b4b-4d3d-a6b8-f229dceac979', name: 'ds4' }],
        "edges": [{
            from: '08c649bd-36e4-4cc8-bae6-6aff477f00ae',
            to: '10623ae2-e1f3-4ab5-820b-bcd879b56d3c',
          },
          {
            from: '2f4ce533-5b4b-4d3d-a6b8-f229dceac979',
            to: '08c649bd-36e4-4cc8-bae6-6aff477f00ae',
          }]
    }
    """
    from spm.services.datasheet_graph.datasheet_graph import DataSheetGraph

    ds_graph = DataSheetGraph(
        client_id=client_id,
        include_stale_information_query=True,
    )

    # Getting databook_id to databook_names map
    databook_ids = ds_graph.unqiue_databook_ids()
    databook_names_dict = databook_names_by_ids(
        client_id=client_id, databook_ids=list(databook_ids)
    )
    serialized_data = ds_graph.serialize_data()
    ds_info = ds_graph.ds_info

    nodes = []
    edges = []
    source_data_origin = {
        "object": "Custom Object",
        "report": "Report Object",
    }
    root_color = "#2362f9"
    leaf_color = "#7FDAA7"
    parent_color = "#56A9FF"
    stale_color = "#FBBF24"

    node_width = 110
    node_height = 110
    border_color = "#111827"

    allowed_nodes = set()
    # What does 'allowed_nodes' mean?
    # In the UI, we only want to display datasheet nodes
    # from that data book. Additionally, if a datasheet references another data
    # book, we want to show the one-level parent datasheet node as well.
    # This subset of datasheet nodes is referred to as 'allowed_nodes'."
    for edge in serialized_data["links"]:
        if databook_id == ds_info[edge["target"]["node_id"]].databook_id:
            # If the datasheet belongs to the given databook_id
            # then both source and target nodes are allowed
            allowed_nodes.add(edge["source"]["node_id"])
            allowed_nodes.add(edge["target"]["node_id"])
            edge_dict = {
                "from": edge["source"]["node_id"],
                "to": edge["target"]["node_id"],
                "order": edge["target"]["order"],
            }
            edges.append(edge_dict)

    edges.sort(key=lambda x: (x["order"] is None, x["order"]))

    for node in serialized_data["nodes"]:
        node_id = node["node_id"]
        # If the node is not in allowed_nodes, then skip it
        if node_id not in allowed_nodes:
            continue
        name = node.get("name", node_id)
        default_color = "grey"
        color = default_color
        object_type = node.get("object_type")
        data_origin = node.get("data_origin")
        is_leaf = node.get("is_leaf")
        is_root = node.get("is_root")
        is_stale = ds_graph.is_datasheet_data_stale(node_id) if not is_root else False
        # TODO:
        # if the parent node is stale means all the child nodes must be stale
        # instead of calling is_datasheet_data_stale for each node if the node is
        # stale mark that node and all of its child node as stale directly
        # dont make is_datasheet_data_stale call for child nodes

        if not is_root:
            is_stale = (
                is_stale["is_report_data_stale"] or is_stale["is_datasheet_stale"]
            )

        source_sheet_name = ""
        if "source_id" in node:
            source_sheet = ds_info.get(node["source_id"])
            if source_sheet:
                source_sheet_name = source_sheet.name

        transformation_meta = {}
        hierarchy_reference_datasheet_names = []
        databook_name = ""

        div_wrapped_name = (
            f'<div style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; ">{name}</div>',
        )

        if not is_root:
            transformation_meta = get_transformation_meta_data(
                node["transformations"]["trans_spec"], ds_info
            )
            # Fetching all reference datasheet_ids.
            hierarchy_reference_datasheet_ids = (
                ds_graph.hierarchy_reference_datasheet_ids(datasheet_id=node_id)
            )
            # Corresponding datasheet names for the reference datasheet_ids.
            hierarchy_reference_datasheet_names = [
                ds_info[datasheet_id].name
                for datasheet_id in hierarchy_reference_datasheet_ids
            ]
            databook_id = uuid.UUID(ds_info[node_id].databook_id)
            databook_name = databook_names_dict.get(databook_id)

        if object_type:
            data_origin = source_data_origin.get(object_type, data_origin)

        if is_root:
            color = root_color
        elif is_leaf:
            color = leaf_color
        else:
            color = parent_color

        if is_stale:
            color = stale_color

        node_dict = {
            "id": node_id,
            "name": div_wrapped_name,
            "color": color,
            "borderWidth": 1,
            "borderRadius": 8,
            "borderColor": border_color,
            "order": node.get("order", 0),
            "dataOrigin": data_origin,
            "fontSize": 12,
            "transformationMeta": transformation_meta,
            "hierarchyMeta": hierarchy_reference_datasheet_names,
            "source": source_sheet_name,
            "databookName": databook_name,
        }

        nodes.append(node_dict)

    nodes.sort(key=lambda x: (x["order"] is None, x["order"]))

    max_height, max_width = ds_graph.calculate_width_and_height_for_graph(
        node_ids=allowed_nodes
    )

    total_width = node_width * max_width
    total_height = node_height * max_height

    res = {
        "nodes": nodes,
        "edges": edges,
        "totalWidth": total_width,
        "totalHeight": total_height,
    }

    return res


def get_transformation_meta_data(transformation_spec, ds_info):
    transformation_meta = {}
    if transformation_spec:
        joined_with = []
        unioned_with = []
        for trans_dict in transformation_spec:
            if trans_dict["type"] in ("JOIN", "UNION"):
                with_id = trans_dict["with"]
                ds = ds_info[with_id]
                if trans_dict["type"] == "JOIN":
                    joined_with.append(ds.name)
                else:
                    unioned_with.append(ds.name)
        transformation_meta["join"] = joined_with
        transformation_meta["union"] = unioned_with

    return transformation_meta


def get_count_window_calc_fields(datasheet_variables: tuple) -> int:
    """
    Get the number of window based calculated fields (rank function)
    for a datasheet.
    """
    count_window_fields = 0
    for variable in datasheet_variables:
        if variable["field_order"] == 0:  # type: ignore
            continue

        if variable["meta_data"] is not None:  # type: ignore
            for function in get_window_based_calculated_fields():
                if function in variable["meta_data"]:  # type: ignore
                    count_window_fields += 1

    return count_window_fields


def is_window_calc_fields_deleted(
    client_id: int,
    databook_id: str,
    datasheet_id: str,
    last_generated_time: datetime,
    knowledge_date: datetime,
) -> bool:
    """
    check if there exists a window based field that was inserted before last_generated_time
    but invalidate in the time range [last_generated_time, knowledge_date]
    """

    ds_vars_invalidated_in_range = DatasheetVariableAccessor(
        client_id=client_id
    ).get_variables_for_ds_id_invalidated_in_range(
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        last_generated_time=last_generated_time,
        knowledge_date=knowledge_date,
    )

    for variable in ds_vars_invalidated_in_range:
        if variable.field_order == 0:  # type: ignore
            continue

        if variable.meta_data is not None:  # type: ignore
            for function in get_window_based_calculated_fields():
                if function in variable.meta_data:  # type: ignore
                    return True

    return False


def remove_archived_databooks(client_id: int, databook_ids) -> list[uuid.UUID]:
    """
    Returns a list of databook ids after removing the archived books from the list
    """
    databook_ids: list[uuid.UUID] = DatabookAccessor(
        client_id=client_id
    ).remove_archived_databooks(databook_ids=databook_ids)
    return databook_ids


def get_archived_books_from_list(client_id: int, databook_ids) -> list[uuid.UUID]:
    """
    Returns a list of databook ids after removing the active books from the list
    """
    databook_ids: list[uuid.UUID] = DatabookAccessor(
        client_id=client_id
    ).get_archived_books_from_list(databook_ids=databook_ids)

    return databook_ids


def get_all_databook_ids(client_id: int, skip_archived_books: bool) -> list[uuid.UUID]:
    """
    Returns a list of databook ids for the client.

    If skip_archived_books is True, then only non-archived databooks are returned.
    If skip_archived_books is False, then all databooks are returned.
    """
    databook_ids = DatabookAccessor(client_id=client_id).get_all_databook_ids(
        skip_archived_books=skip_archived_books
    )
    return databook_ids


def get_datasheet_last_generation_time(
    client_id: int, databook_id: str, datasheet_id: str, knowledge_date: datetime = None
) -> datetime:
    """
    Gets the last time at which the datasheet was generated.
    If no records are found in dpkd_map, then return the earliest possible date.
    """

    datasheet_record = DbkdPkdMapAccessor(
        client_id=client_id
    ).get_datasheet_latest_primary_kd(
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        knowledge_date=knowledge_date,
    )
    if datasheet_record and len(datasheet_record) > 0:
        return datasheet_record[0]["primary_kd"]

    # return the minimum datetime if no record in dpkp map is found in the local timezone
    min_datetime = datetime.min
    utc_timezone = pytz.UTC
    # pylint: disable-msg=no-value-for-parameter
    return utc_timezone.localize(min_datetime)
    # pylint: enable-msg=no-value-for-parameter


def get_databook_id_by_datasheet_id(client_id: int, datasheet_id) -> uuid.UUID:
    """
    Returns the databook id for the given datasheet id
    """
    databook_id = DatasheetAccessor(
        client_id=client_id
    ).get_databook_id_using_datasheet_id(datasheet_id=datasheet_id)
    return databook_id


def get_datasheet_calc_fields(client_id, datasheet_id, knowledge_date, as_dicts=False):
    datasheet_calc_fields: tuple = DatasheetVariableAccessor(
        client_id
    ).get_calc_fields_by_datasheet_id_kd_aware(
        datasheet_id, knowledge_date, order_by="field_order", as_dicts=as_dicts
    )
    return datasheet_calc_fields


def group_calc_fields_by_evaluation_context_ordered(
    calculated_fields: list[dict],
) -> tuple[list[dict], list[dict]]:
    """
    Given an ordered list of calculated fields, group them by their evaluation context
    Here evaluation context can be either ltd/delta.
    Returns an ordered list of delta calc fields and grouped list of row + window based field.
    Post splitting by eval context, it groups row and window based fields order wise
    (refer to `group_calc_fields_by_field_order` function below )
    """

    delta_context_fields = list()
    ltd_context_fields = list()

    for calc_field in calculated_fields:
        # evaluation context of the calc field
        evaluation_context = calc_field.get("meta_data", {}).get(
            "evaluation_context", EvaluationContext.LTD.value
        )
        if evaluation_context == EvaluationContext.DELTA.value:
            delta_context_fields.append(calc_field)
        elif evaluation_context == EvaluationContext.LTD.value:
            ltd_context_fields.append(calc_field)

    # process the ltd context fields
    grouped_ltd_context_fields = []
    if len(ltd_context_fields) > 0:
        grouped_ltd_context_fields = group_calc_fields_by_field_order(
            calculated_fields=ltd_context_fields
        )

    return delta_context_fields, grouped_ltd_context_fields


def group_calc_fields_by_field_order(calculated_fields: list[dict]) -> list[dict]:
    """
    Given a list of calculated fields, groups them by field order
    and splits them into row based and window based fields within the group.

    Algorithm working :-
        Input is a list of calculated fields ordered in ascending order of field_order
        (refer to examples here - interstage_project/everstage_etl/tests/test_etl.py#test_test_calculated_fields_dependency)

        Consider the following list of calculated fields [ (row_fields_count = 4, window_fields_count = 0), (2,2), (1, 0), (4,1)]
        (each element represent count of row and window fields in that field_order)
        When number of window_fields in a field order is 0, we can compute row fields of next field order along with current field_order's row fields
        Based on the above algoritm, this is the output for the above input :-

        [ (row_fields_count = 6, window_fields_count = 0), (0, 2), (5, 0), (0, 1)]
    """

    calculated_field_ordered_dict = dict()

    for calc_field in calculated_fields:
        field_order = calc_field["field_order"]

        if field_order not in calculated_field_ordered_dict:
            calculated_field_ordered_dict[field_order] = {
                "row_fields": [],
                "window_fields": [],
            }

        if is_window_calculated_field(calc_field):
            calculated_field_ordered_dict[field_order]["window_fields"].append(
                calc_field
            )
        else:
            calculated_field_ordered_dict[field_order]["row_fields"].append(calc_field)

    calculated_field_grouped_list = [
        (field_order, grouped_fields)
        for field_order, grouped_fields in calculated_field_ordered_dict.items()
    ]
    # sort the above list by field order
    calculated_field_ordered_list = sorted(
        calculated_field_grouped_list, key=lambda x: x[0]
    )

    # group the calculated fields with window calculated fields as the delimiter
    calculated_fields_ordered = [{"row_fields": [], "window_fields": []}]
    previous_order_has_window_fields = False

    for grouped_calc_fields in calculated_field_ordered_list:
        field_order = grouped_calc_fields[0]
        current_order_fields = grouped_calc_fields[1]
        current_order_row_fields = current_order_fields["row_fields"]
        current_order_window_fields = current_order_fields["window_fields"]

        if not previous_order_has_window_fields:
            calculated_fields_ordered[-1]["row_fields"].extend(current_order_row_fields)
            if len(current_order_window_fields) > 0:
                calculated_fields_ordered.append(
                    {"row_fields": [], "window_fields": current_order_window_fields}
                )
        else:
            calculated_fields_ordered.append(calculated_field_ordered_dict[field_order])

        previous_order_has_window_fields = len(current_order_window_fields) > 0

    return calculated_fields_ordered


def split_calculated_fields_bw_row_window(
    ordered_calculated_fields: list[dict],
) -> tuple[list[dict], list[dict]]:
    """
    Given a list of ordered calculated fields, where each element, contains window and row fields of the field order,
    returns a tuple of 2 lists, where the first list contains calc fields in field order where no field order contains window fields
    and the second lists contains rest of the variables post the first window field
    """

    row_based_fields = list()
    window_row_based_fields = list()
    window_fields_encountered = False

    for field_order_variables in ordered_calculated_fields:
        # if window field hasn't been encountered yet and there are window fields in current field order
        if (
            not window_fields_encountered
            and len(field_order_variables["window_fields"]) > 0
        ):
            window_fields_encountered = True

        if not window_fields_encountered:
            row_based_fields.append(field_order_variables)
        else:
            window_row_based_fields.append(field_order_variables)

    return row_based_fields, window_row_based_fields


def _construct_reason_for_generation_in_progress_delete_error(client_id, datasheet_ids):
    """
    Construct the reason for generation in progress delete error
    """
    import commission_engine.services.datasheet_data_services.datasheet_retrieval_service as drs

    reason = None

    datasheets = drs.get_datasheets_by_id(
        client_id=client_id, datasheet_ids=datasheet_ids[:3], columns=["name"]
    )
    datasheet_names = [datasheet["name"] for datasheet in datasheets]
    datasheet_names = ", ".join(datasheet_names)  # Join the first 3 datasheet names

    reason = DatasheetDeleteError.GENERATION_IN_PROGRESS.value.format(
        total_count=len(datasheet_ids), datasheet_names=datasheet_names
    )

    return reason


def _will_produce_cycle(
    client_id: int,
    datasheet_id: str,
    transformation_spec_list: list,
    selected_variables_data: list,
) -> list:
    """
    Checks if adding the datasheet_id (to whom the transformation_spec_list belongs) to the
    databook graph causes a cycle in the graph.
    """
    import commission_engine.services.datasheet_data_services.datasheet_retrieval_service as drs

    # Importing here locally to avoid circular import
    from spm.services.datasheet_graph.datasheet_graph import DataSheetGraph

    parent_datasheets = []
    for transformation_spec in transformation_spec_list:
        if transformation_spec["type"] in (
            DATASHEET_TRANSFORMATIONS.JOIN.name,
            DATASHEET_TRANSFORMATIONS.UNION.name,
        ):
            # From the transformation spec, get the parent datasheet_id
            parent_datasheet_id = transformation_spec["with"]
            parent_datasheets.append(parent_datasheet_id)

    # check whether hierarchy calculated field
    for variables_data in selected_variables_data:
        if is_hierarchy_calculated_field(variables_data):
            meta_data = variables_data.get("meta_data", {})
            hierarchy_meta_data = HierarchyMetaData(**meta_data["hierarchy"])
            reference_sheet = hierarchy_meta_data.reference_sheet
            if reference_sheet != HierarchyUtils.USE_CURRENT_SHEET_DEFAULT_VALUE.value:
                parent_datasheets.append(reference_sheet)

    # if there are no parent datasheets, then there is no cycle
    if len(parent_datasheets) == 0:
        return []

    datasheet_graph = DataSheetGraph(
        client_id=client_id,
        include_stale_information_query=False,
    )

    # we bulid it datasheet_graph with only active datasheets.
    # but when user creates a new datasheet (let say datasheet_1), add transformations
    # and validating the datasheet. In this case, datasheet_1 is not yet active(After datasheet is saved then only it is consider as active datasheet).

    # So, we need to add datasheet_1 to datasheet_graph first and then check for cycle.
    if datasheet_graph.is_node_exists(node_id=datasheet_id) is False:
        # Here we are adding datasheet_name because we need it in error message.
        datasheet_name = drs.get_datasheets_by_id(
            client_id=client_id, datasheet_ids=datasheet_id, columns=["name"]
        )
        datasheet_name = datasheet_name[0]["name"]
        # Building datasheet_details here, not inside the graph file,
        # because don't want to make many function imports from other files inside the graph.
        datasheet_details = {
            "node_id": datasheet_id,
            "name": datasheet_name,
        }
        datasheet_graph.create_minimal_node(
            node_id=datasheet_id,
            node_type=DATASHEET_SOURCE.DATASHEET,
            node_details=datasheet_details,
        )

    # We are establishing the path between the
    # parent_datasheets and the datasheet_id

    # parent_datasheets means the datasheets from the transformation spec
    # and the datasheets from the hierarchy calculated field

    # destination_ids means the current datasheet_id which we are validating.(A request come from frontend)
    is_cycle_formed = datasheet_graph.establish_paths(
        source_ids=parent_datasheets, destination_ids=[datasheet_id]
    )
    # If cycle is formed return the reason for cycle datasheet names
    if not is_cycle_formed:
        parent_datasheets = [
            datasheet_graph.get_node(node_id).name for node_id in parent_datasheets
        ]
        return parent_datasheets
    return []


def is_databook_exist(client_id, databook_id) -> bool:
    """
    If the given databook_id is active exists for the client means
    This function will return True else False
    """

    try:
        # Trying to convert the databook_id(str) to uuid
        # If it is not possible means it is not a valid uuid and return False

        # Example: if user pass something like "1234" means it will return False
        databook_id = uuid.UUID(databook_id)
    except ValueError:
        return False

    return DatabookAccessor(client_id=client_id).is_databook_exist(
        databook_id=databook_id
    )


def _get_datasheet_dependencies_in_archived_state(
    client_id, transformation_spec_list, selected_variables_data, source_type, source_id
) -> list:
    """
    Check if the datasheet source is in archived state
    """
    parent_datasheets = []
    for transformation_spec in transformation_spec_list:
        if transformation_spec["type"] in (
            DATASHEET_TRANSFORMATIONS.JOIN.name,
            DATASHEET_TRANSFORMATIONS.UNION.name,
        ):
            # From the transformation spec, get the parent datasheet_id
            parent_datasheet_id = transformation_spec["with"]
            parent_datasheets.append(parent_datasheet_id)

    # check whether hierarchy calculated field
    for variables_data in selected_variables_data:
        if is_hierarchy_calculated_field(variables_data):
            meta_data = variables_data.get("meta_data", {})
            hierarchy_meta_data = HierarchyMetaData(**meta_data["hierarchy"])
            reference_sheet = hierarchy_meta_data.reference_sheet
            if reference_sheet != HierarchyUtils.USE_CURRENT_SHEET_DEFAULT_VALUE.value:
                parent_datasheets.append(reference_sheet)

    if source_type == DATASHEET_SOURCE.DATASHEET:
        parent_datasheets.append(source_id)

    # Get all databook_ids for the given datasheets
    databook_ids_dict: dict = DatasheetAccessor(
        client_id=client_id
    ).get_datasheet_id_to_databook_id_dict(datasheet_ids=parent_datasheets)
    source_databook_is_in_archived_state: list = get_archived_books_from_list(
        client_id=client_id, databook_ids=list(databook_ids_dict.values())
    )
    return source_databook_is_in_archived_state


def databook_names_by_ids(client_id: int, databook_ids: list | None = None) -> dict:
    """
    Returns a list of databook names for the given databook ids
    """
    databook_names: dict = DatabookAccessor(client_id=client_id).databook_names_by_ids(
        databook_ids=databook_ids
    )
    return databook_names


def fetch_all_user_properties(client_id: int, ignore_properties=True) -> list:
    """
    Returns a list of all user properties

    Combines the custom fields and user report variables
    """
    object_id = "user"
    custom_fields = CustomFieldsAccessor(client_id=client_id).fetch_all_custom_fields()
    data_type_to_id_map = get_datatype_id_name_map()
    data_type_id_to_name_map = get_data_types()

    for custom_field in custom_fields:
        data_type_id = custom_field["data_type_id"]

        # If the data type is a StringArray(14), then change it to String(4)
        # custom_fields with field_type as Dropdown has the data_type_id as 14
        if data_type_to_id_map[data_type_id] == "STRINGARRAY":
            custom_field["data_type_id"] = data_type_id_to_name_map["STRING"]

    ignored_properties = (
        GetUserPropertiesTransformationUtils.TO_BE_IGNORED.value
        if ignore_properties
        else []
    )

    # Filter out the user report variables which are to be ignored
    user_report_variables = [
        user_report_variable
        for user_report_variable in EverObjectVariableAccessor().get_ever_object_variable(
            object_id
        )
        if user_report_variable["system_name"] not in ignored_properties
    ]

    all_user_properties = custom_fields + user_report_variables

    return all_user_properties


def does_datasheet_with_settlement_source_exists(client_id: int) -> bool:
    """
    Returns True if a datasheet with settlement source exists, else False
    """
    return DatasheetAccessor(client_id).does_datasheet_with_settlement_source_exists()


def get_databook_name(client_id: int, databook_id: str) -> str:
    """
    Returns the databook name for the given databook id
    """
    return DatabookAccessor(client_id).get_databook_by_id(databook_id).name


def get_datasheet_name(client_id: int, datasheet_id: str) -> str:
    """
    Returns the datasheet name for the given datasheet id
    """
    return DatasheetAccessor(client_id).get_datasheet_by_id(datasheet_id).name
