import ast
import copy
import logging
from collections import OrderedDict
from io import String<PERSON>
from itertools import islice
from typing import List
from uuid import UUID

import dateutil.parser
import pandas as pd
from dateutil.parser import parse
from django.core.cache import caches
from django.db import connection
from django.utils import timezone
from django.utils.timezone import make_aware
from pydash import nest

import commission_engine.accessors.settlement_accessor as settlement_accessor
import commission_engine.services.commission_calculation_service.criteria_calculation_service as cs
import interstage_project.utils as iputils
from commission_engine.accessors.client_accessor import (
    get_client,
    get_client_features,
    get_remove_zero_value_deferred,
    get_snapshot_data_for_statements,
    is_read_settlement_from_snowflake,
    should_use_multi_engine_stormbreaker,
)
from commission_engine.accessors.commission_accessor import (
    CommissionAccessor,
    QuotaErosionAccessor,
)
from commission_engine.accessors.commission_sec_kd_accessor import (
    CommissionSecondaryKdAccessor,
)
from commission_engine.accessors.databook_accessor import DatasheetVariableAccessor
from commission_engine.accessors.lock_accessor import CommissionLockAccessor
from commission_engine.database.snowflake_connection import get_pooled_connection
from commission_engine.services.client_details_service import get_base_currency
from commission_engine.services.commission_calculation_service.data_generator import (
    remove_nan_nat,
)
from commission_engine.services.commission_calculation_service.team_calculator import (
    get_team_context,
    get_team_members,
)
from commission_engine.services.hyperlink_service import HyperlinkService
from commission_engine.snowflake_accessors.settlement_accessor import (
    SettlementSnowflakeAccessor,
)
from commission_engine.utils import FXRate, date_utils
from commission_engine.utils.cache_utils import get_commission_cache_key
from commission_engine.utils.date_utils import (
    end_of_day,
    find_period,
    get_fiscal_year,
    get_period_freq,
    get_period_label,
    start_of_day,
)
from commission_engine.utils.general_data import (
    SNOWFLAKE_IN_CLAUSE_MAX_LIMIT,
    CommissionViewType,
    Freq,
    SnapshostTable,
    static_frequencies,
)
from commission_engine.utils.report_utils import get_report_object_data_table_name
from commission_engine.utils.s3_utils import S3Uploader
from everstage_ddd.stormbreaker import StormBreakerDS as StormBreakerDSMultiEngine
from everstage_ddd.stormbreaker import StormBreakerDSFactory, StormBreakerDSInitType
from interstage_project.utils import log_me
from spm.accessors.commission_plan_accessor import (
    CommissionPlanAccessor,
    PlanCriteriaAccessor,
)
from spm.accessors.config_accessors.countries_accessor import CountriesAccessor
from spm.accessors.config_accessors.employee_accessor import (
    EmployeePayrollAccessor,
    HierarchyAccessor,
)
from spm.accessors.quota_acessors import EmployeeQuotaAccessor, QuotaAccessor
from spm.services.datasheet_permission_services import get_all_hidden_columns_for_user
from spm.services.rbac_services import does_user_have_databook_manage_permission
from spm.services.stormbreaker.stormbreaker import (
    StormBreakerDS as StormBreakerDSVariant,
)
from spm.sort_utils import SortInfoType, SortUtility

cache = caches["default"]
c_logger = logging.getLogger(__name__)


def __extend_data(data, row_key_name="row_key"):
    return pd.DataFrame(
        [
            {
                row_key_name: data_item["row_key"],
                **data_item["data"],
            }
            for data_item in data
        ]
    )


def get_latest_or_kd_if_paid(client_id, psd, ped, payee_email):
    locked_kd = CommissionLockAccessor(
        client_id
    ).get_locked_kd_for_locked_comm_in_period(psd, ped, payee_email)
    return locked_kd


def group_settlements_by_commission_line_item(
    settlements,
    fx_rate_convertor=None,
    payee_currency=None,
    client_id=None,
    payee_email=None,
    is_snapshot_call=False,
):
    """returns settlement amount grouped by commission line item_id
    Note: commission_row_key in settlement contains the line_item_id in commission record
    """
    settlements_by_commission_row_key = {}
    psd_ped_tuple_list = set()
    ped_payee_list_dict = {}
    payee_ped_lock_map = {}
    sec_kd_map = {}

    comm_period_start_date_column = "comm_period_start_date"
    comm_period_end_date_column = "comm_period_end_date"
    commission_row_key_column = "commission_row_key"
    amount_column = "amount"
    # For queries from snapshot table in snowflake, is_snapshot_call will be true & snapshot_sys_ identifier
    # is prefixed to get the column names
    # For eg, commission_row_key is fetched as snapshot_sys_commission_row_key
    if is_snapshot_call:
        snapshot_column_prefix = SnapshostTable.COLUMN_IDENTIFIER_PREFIX.value
        comm_period_start_date_column = (
            f"{snapshot_column_prefix}comm_period_start_date"
        )
        comm_period_end_date_column = f"{snapshot_column_prefix}comm_period_end_date"
        commission_row_key_column = f"{snapshot_column_prefix}commission_row_key"
        amount_column = f"{snapshot_column_prefix}amount"
    #  creating ped payee_id map to get fx_rate without looing query
    if fx_rate_convertor and payee_currency:
        for settlement in settlements:
            psd_ped_tuple_list.add(
                (
                    settlement.get(comm_period_start_date_column),
                    settlement.get(comm_period_end_date_column),
                )
            )
            ped = settlement.get(comm_period_end_date_column)
            if ped not in ped_payee_list_dict:
                ped_payee_list_dict[ped] = set()
            ped_payee_list_dict[ped].add(payee_email)

        for key, values in ped_payee_list_dict.items():
            ped_payee_list_dict[key] = list(values)
        psd_ped_tuple_list = list(set(psd_ped_tuple_list))

        all_locks = CommissionLockAccessor(
            client_id
        ).get_all_locked_kd_for_locked_comm_in_period(psd_ped_tuple_list, payee_email)

        for lock in all_locks:
            payee_id = lock["payee_email_id"]
            ped = lock["period_end_date"]
            if payee_id not in payee_ped_lock_map:
                payee_ped_lock_map[payee_id] = {}
            payee_ped_lock_map[payee_id][ped] = lock["locked_knowledge_date"]

        sec_kds_for_period = CommissionSecondaryKdAccessor(
            client_id=client_id
        ).get_payee_sec_kd_payee_in_peds(ped_payee_list_map=ped_payee_list_dict)
        for comm in sec_kds_for_period:
            payee_id = comm["payee_email_id"]
            if comm["period_end_date"] not in sec_kd_map:
                sec_kd_map[comm["period_end_date"]] = {}
            sec_kd_map[comm["period_end_date"]][payee_id] = comm["sec_kd"]

    for settlement in settlements:
        fx_rate = None

        if fx_rate_convertor and payee_currency:
            secondary_kd = sec_kd_map.get(
                settlement.get(comm_period_end_date_column), {}
            ).get(payee_email, None)

            fx_rate = fx_rate_convertor.get_fx_rate_kd(
                settlement.get(comm_period_end_date_column),
                payee_currency,
                secondary_kd,
            )

        commission_row_key = settlement.get(commission_row_key_column)
        commission_row_key = commission_row_key if commission_row_key else "None"

        if (
            commission_row_key
            and commission_row_key not in settlements_by_commission_row_key
        ):
            settlements_by_commission_row_key[commission_row_key] = 0

        settlement_amount = settlement.get(amount_column)
        # converting to payee currency
        if fx_rate_convertor is not None:
            settlement_amount = float(
                fx_rate_convertor.change_to_payee_currency(settlement_amount, fx_rate)
            )

        settlements_by_commission_row_key[commission_row_key] += settlement_amount

    return settlements_by_commission_row_key


def group_commissions_by_line_item(
    commissions, fx_rate_convertor=None, is_snapshot_call=False
):
    """returns commission amount grouped by line_item_id"""
    commissions_by_line_item = {}
    line_item_id_column = "line_item_id"
    amount_column = "amount"
    # For queries from snapshot table in snowflake, is_snapshot_call is True & snapshot_sys
    # will be prefixed to get the column names
    # For eg, line_item_id is fetched as snapshot_sys_line_item_id
    if is_snapshot_call:
        snapshot_column_prefix = SnapshostTable.COLUMN_IDENTIFIER_PREFIX.value
        line_item_id_column = f"{snapshot_column_prefix}line_item_id"
        amount_column = f"{snapshot_column_prefix}amount"
    for commission in commissions:
        line_item_id = commission.get(line_item_id_column)
        if line_item_id:
            if line_item_id not in commissions_by_line_item:
                commissions_by_line_item[line_item_id] = 0

            commission_amount = commission.get(amount_column)
            # converting to payee currency
            if fx_rate_convertor is not None:
                commission_amount = float(
                    fx_rate_convertor.change_to_payee_currency(commission_amount)
                )

            commissions_by_line_item[line_item_id] += commission_amount

    return commissions_by_line_item


def group_commissions_by_line_item_tier_id(commissions, fx_rate_convertor=None):
    """returns commission amount grouped by line_item_id"""
    commissions_by_li_tier = {}
    for commission in commissions:
        line_item_id = commission.get("line_item_id")
        tier_id = commission.get("tier_id") if "tier_id" in commission else None
        if (line_item_id, tier_id) not in commissions_by_li_tier:
            commissions_by_li_tier[(line_item_id, tier_id)] = 0

        commission_amount = commission.get("amount")
        # converting to payee currency
        if fx_rate_convertor is not None:
            commission_amount = float(
                fx_rate_convertor.change_to_payee_currency(commission_amount)
            )
        commissions_by_li_tier[(line_item_id, tier_id)] += commission_amount
    return commissions_by_li_tier


def group_commissions_by_line_item_for_payees(
    commissions, payee_currency_fx_rate_map
) -> dict:
    """
    Group commissions by payee, line item, plan and criteria.

    Returns:

        payee_line_item_data_map: {
            "payee_email_id_1": {
                ("line_item_id_1", "plan_id_1", "criteria_id_1"): {
                    "commission_amount": 100,
                    "is_do_nothing": False,
                    "is_summation_criteria": True,
                    "period_start_date": datetime.date(2020, 1, 1),
                    "period_end_date": datetime.date(2020, 1, 31),
                    "quota_erosion": 10,
                    "tier_name": "Tier 0"
                },
                ("line_item_id_2", "plan_id_2", "criteria_id_2"): {
                    "commission_amount": 200,
                    "is_do_nothing": False,
                    "is_summation_criteria": False,
                    "period_start_date": datetime.date(2020, 1, 1),
                    "period_end_date": datetime.date(2020, 1, 31),
                },
            },
            "payee_email_id_2": {
                ("line_item_id_3", "plan_id_3", "criteria_id_3"): {
                    "commission_amount": 300,
                    "is_do_nothing": False,
                    "is_summation_criteria": True,
                    "period_start_date": datetime.date(2020, 1, 1),
                    "period_end_date": datetime.date(2020, 1, 31),
                    "quota_erosion": 20,
                    "tier_name": "Tier 0",
                    "tier_id_split": {
                        "0": { # this is the tier id value
                            "0":{ # this is the actual tier id value
                                "commission": 100,
                                "quota_erosion": 10,
                                "tier_name": "Tier 0"
                            }
                        },
                        "1": { # this is the tier id value
                            "1":{ # this is the actual tier id value
                                "commission": 200,
                                "quota_erosion": 10,
                                "tier_name": "Tier 1"
                            },
                            "0":{ # this is the actual tier id value
                                "commission": 100,
                                "quota_erosion": 10,
                                "tier_name": "Tier 1"
                            }
                        }
                    }
                },
                ("line_item_id_4", "plan_id_4", "criteria_id_4"): {
                    "commission_amount": 400,
                    "is_do_nothing": False,
                    "is_summation_criteria": False,
                    "period_start_date": datetime.date(2020, 1, 1),
                    "period_end_date": datetime.date(2020, 1, 31),
                    "quota_erosion": 10,
                    "tier_name": "Tier 0"
                },
            },
        }
    """
    payee_line_item_data_map = {}
    for commission in commissions:
        payee = commission.get("payee_email_id")
        line_item_id = commission.get("line_item_id")
        context_ids = commission.get("context_ids")
        commission_amount = commission.get("amount")
        plan_id = commission.get("commission_plan_id")
        criteria_id = commission.get("criteria_id")

        if payee not in payee_line_item_data_map:
            payee_line_item_data_map[payee] = {}

        # converting to payee currency
        if payee in payee_currency_fx_rate_map:
            fx_rate_convertor = payee_currency_fx_rate_map[payee].get(
                "fx_rate_convertor"
            )
            if fx_rate_convertor is not None:
                commission_amount = float(
                    fx_rate_convertor.change_to_payee_currency(commission_amount)
                )

        if isinstance(context_ids, str):
            context_ids = ast.literal_eval(context_ids)

        tier_id_key = str(commission.get("tier_id"))
        original_tier_id = str(commission.get("original_tier_id"))
        commission_amount_value = float(commission_amount) if commission_amount else 0

        if context_ids:
            # For summation criteria
            commission_line_item_ids = []
            commission_line_item_ids.extend(context_ids)
            for comm_line_item_id in commission_line_item_ids:
                if comm_line_item_id is not None:
                    line_item_key = (comm_line_item_id, plan_id, criteria_id)

                    if line_item_key not in payee_line_item_data_map[payee]:
                        line_item_data = {
                            **{
                                key: value
                                for key, value in commission.items()
                                if key not in ["quota_erosion", "tier_id"]
                            },
                            "is_summation_criteria": True,
                            "commission_amount": float(commission_amount),
                        }

                        if "quota_erosion" in commission:
                            if commission["quota_erosion"] is not None:
                                line_item_data["quota_erosion"] = commission[
                                    "quota_erosion"
                                ]

                        if "tier_id" in commission:
                            if (
                                commission["tier_id"] is not None
                                and commission["tier_id"] != "-"
                            ):
                                line_item_data["tier_name"] = (
                                    f"Tier {str(commission['tier_id'])}"
                                )
                                # Tier id split will be added as a dictionary with key as tier_id and value as
                                # commsison amount, quota erosion and tier name for that tier id. This will be used to store the
                                # to additional_data in payout_snapshot_data_<client_id> table
                                if commission.get("quota_erosion"):
                                    line_item_data["tier_id_split"] = {
                                        tier_id_key: {
                                            original_tier_id: {
                                                "commission": commission_amount_value,
                                                "quota_erosion": commission.get(
                                                    "quota_erosion"
                                                ),
                                                "tier_name": f"Tier {tier_id_key}",
                                            }
                                        }
                                    }
                                else:
                                    line_item_data["tier_id_split"] = {
                                        tier_id_key: {
                                            original_tier_id: {
                                                "commission": commission_amount_value,
                                                "tier_name": f"Tier {tier_id_key}",
                                            }
                                        }
                                    }

                        payee_line_item_data_map[payee][line_item_key] = line_item_data
                    else:
                        payee_line_item_data_map[payee][line_item_key][
                            "commission_amount"
                        ] += (float(commission_amount) if commission_amount else 0)
                        # If tier_id is present, then add the commission amount/ quota erosion to the respective tier_id

                        if (
                            "tier_id_split"
                            in payee_line_item_data_map[payee][line_item_key]
                            and tier_id_key
                            in payee_line_item_data_map[payee][line_item_key][
                                "tier_id_split"
                            ].keys()
                        ):
                            if (
                                original_tier_id
                                in payee_line_item_data_map[payee][line_item_key][
                                    "tier_id_split"
                                ][tier_id_key]
                            ):
                                if (
                                    "commission"
                                    in payee_line_item_data_map[payee][line_item_key][
                                        "tier_id_split"
                                    ][tier_id_key][original_tier_id]
                                ):
                                    # If commission is already present, then add the new commission amount to the existing commission amount
                                    payee_line_item_data_map[payee][line_item_key][
                                        "tier_id_split"
                                    ][tier_id_key][original_tier_id][
                                        "commission"
                                    ] += commission_amount_value
                                else:
                                    # If commission is not present, then assign the commission amount to the respective tier_id
                                    payee_line_item_data_map[payee][line_item_key][
                                        "tier_id_split"
                                    ][tier_id_key][original_tier_id][
                                        "commission"
                                    ] = commission_amount_value
                                if "quota_erosion" in payee_line_item_data_map[payee][
                                    line_item_key
                                ]["tier_id_split"][tier_id_key][
                                    original_tier_id
                                ] and commission.get(
                                    "quota_erosion"
                                ):
                                    # If quota erosion is already present, then add the new quota erosion to the existing quota erosion
                                    payee_line_item_data_map[payee][line_item_key][
                                        "tier_id_split"
                                    ][tier_id_key][original_tier_id][
                                        "quota_erosion"
                                    ] += commission.get(
                                        "quota_erosion"
                                    )
                                elif commission.get("quota_erosion"):
                                    # If quota erosion is not present, then assign the quota erosion value to the respective tier_id
                                    payee_line_item_data_map[payee][line_item_key][
                                        "tier_id_split"
                                    ][tier_id_key][original_tier_id][
                                        "quota_erosion"
                                    ] = commission.get(
                                        "quota_erosion"
                                    )
                            else:
                                if commission.get("quota_erosion"):
                                    payee_line_item_data_map[payee][line_item_key][
                                        "tier_id_split"
                                    ][tier_id_key] = {
                                        original_tier_id: {
                                            "commission": commission_amount_value,
                                            "quota_erosion": commission.get(
                                                "quota_erosion"
                                            ),
                                            "tier_name": f"Tier {tier_id_key}",
                                        }
                                    }
                                else:
                                    payee_line_item_data_map[payee][line_item_key][
                                        "tier_id_split"
                                    ][tier_id_key] = {
                                        original_tier_id: {
                                            "commission": commission_amount_value,
                                            "tier_name": f"Tier {tier_id_key}",
                                        }
                                    }
                        if "quota_erosion" in commission:
                            if commission["quota_erosion"] is not None:
                                payee_line_item_data_map[payee][line_item_key][
                                    "quota_erosion"
                                ] += commission.get("quota_erosion")

        else:
            # For line item level criteria
            if line_item_id is not None:
                line_item_key = (line_item_id, plan_id, criteria_id)
                if line_item_id is not None:
                    if line_item_key not in payee_line_item_data_map[payee]:
                        line_item_data = {
                            **{
                                line_item_key: line_item_value
                                for line_item_key, line_item_value in commission.items()
                                if line_item_key not in ["quota_erosion", "tier_id"]
                            },
                            "is_summation_criteria": False,
                            "commission_amount": float(commission_amount),
                        }

                        if "quota_erosion" in commission:
                            if commission["quota_erosion"] is not None:
                                line_item_data["quota_erosion"] = commission[
                                    "quota_erosion"
                                ]

                        if "tier_id" in commission:
                            if (
                                commission["tier_id"] is not None
                                and commission["tier_id"] != "-"
                            ):
                                line_item_data["tier_name"] = (
                                    f"Tier {str(commission['tier_id'])}"
                                )
                                if commission.get("quota_erosion"):
                                    # Tier id split will be added as a dictionary with key as tier_id and value
                                    # as commsison amount, quota erosion and tier name for that tier id. This will be used to store the
                                    # to additional_data in payout_snapshot_data_<client_id> table
                                    line_item_data["tier_id_split"] = {
                                        tier_id_key: {
                                            original_tier_id: {
                                                "commission": commission_amount_value,
                                                "quota_erosion": commission.get(
                                                    "quota_erosion"
                                                ),
                                                "tier_name": f"Tier {tier_id_key}",
                                            }
                                        }
                                    }
                                else:
                                    line_item_data["tier_id_split"] = {
                                        tier_id_key: {
                                            original_tier_id: {
                                                "commission": commission_amount_value,
                                                "tier_name": f"Tier {tier_id_key}",
                                            }
                                        }
                                    }
                        payee_line_item_data_map[payee][line_item_key] = line_item_data
                    else:
                        payee_line_item_data_map[payee][line_item_key][
                            "commission_amount"
                        ] += commission_amount_value

                        if "quota_erosion" in commission:
                            if commission["quota_erosion"] is not None:
                                payee_line_item_data_map[payee][line_item_key][
                                    "quota_erosion"
                                ] += commission.get("quota_erosion")

                            if "tier_id" in commission:
                                if (
                                    commission["tier_id"] is not None
                                    and commission["tier_id"] != "-"
                                ):
                                    tier_name = payee_line_item_data_map[payee][
                                        line_item_key
                                    ].get("tier_name", None)
                                    if tier_name is not None:
                                        payee_line_item_data_map[payee][line_item_key][
                                            "tier_name"
                                        ] = f"{tier_name}, {str(commission['tier_id'])}"
                                        # If tier_id is present, then add the commission amount/ quota erosion to the respective tier_id
                                        if (
                                            tier_id_key
                                            in payee_line_item_data_map[payee][
                                                line_item_key
                                            ]["tier_id_split"].keys()
                                        ):
                                            if (
                                                original_tier_id
                                                in payee_line_item_data_map[payee][
                                                    line_item_key
                                                ]["tier_id_split"][tier_id_key]
                                            ):
                                                if (
                                                    "quota_erosion"
                                                    in payee_line_item_data_map[payee][
                                                        line_item_key
                                                    ]["tier_id_split"][tier_id_key][
                                                        original_tier_id
                                                    ]
                                                    and commission.get("quota_erosion")
                                                ):
                                                    # If quota erosion is already present, then add the new quota erosion to the existing quota erosion
                                                    payee_line_item_data_map[payee][
                                                        line_item_key
                                                    ]["tier_id_split"][tier_id_key][
                                                        original_tier_id
                                                    ][
                                                        "quota_erosion"
                                                    ] += commission.get(
                                                        "quota_erosion"
                                                    )
                                                elif commission.get("quota_erosion"):
                                                    # If quota erosion is not present, then assign the quota erosion value to the respective tier_id
                                                    payee_line_item_data_map[payee][
                                                        line_item_key
                                                    ]["tier_id_split"][tier_id_key][
                                                        original_tier_id
                                                    ][
                                                        "quota_erosion"
                                                    ] = commission.get(
                                                        "quota_erosion"
                                                    )
                                                if (
                                                    "commission"
                                                    in payee_line_item_data_map[payee][
                                                        line_item_key
                                                    ]["tier_id_split"][tier_id_key][
                                                        original_tier_id
                                                    ]
                                                ):
                                                    # If commission is already present, then add the new commission amount to the existing commission amount
                                                    payee_line_item_data_map[payee][
                                                        line_item_key
                                                    ]["tier_id_split"][tier_id_key][
                                                        original_tier_id
                                                    ][
                                                        "commission"
                                                    ] += commission_amount_value
                                                else:
                                                    # If commission is not present, then assign the commission amount to the respective tier_id
                                                    payee_line_item_data_map[payee][
                                                        line_item_key
                                                    ]["tier_id_split"][tier_id_key][
                                                        original_tier_id
                                                    ][
                                                        "commission"
                                                    ] = commission_amount_value
                                            else:
                                                if commission.get("quota_erosion"):
                                                    payee_line_item_data_map[payee][
                                                        line_item_key
                                                    ]["tier_id_split"][tier_id_key][
                                                        original_tier_id
                                                    ] = {
                                                        "commission": commission_amount_value,
                                                        "quota_erosion": commission.get(
                                                            "quota_erosion"
                                                        ),
                                                        "tier_name": f"Tier {tier_id_key}",
                                                    }
                                                else:
                                                    payee_line_item_data_map[payee][
                                                        line_item_key
                                                    ]["tier_id_split"][tier_id_key][
                                                        original_tier_id
                                                    ] = {
                                                        "commission": commission_amount_value,
                                                        "tier_name": f"Tier {tier_id_key}",
                                                    }
                                        elif commission.get("quota_erosion"):
                                            # If the tier_id is not present in tier_id_split,
                                            # then add the tier_id with commission amount and quota erosion & tier name
                                            payee_line_item_data_map[payee][
                                                line_item_key
                                            ]["tier_id_split"].update(
                                                {
                                                    tier_id_key: {
                                                        original_tier_id: {
                                                            "commission": commission_amount_value,
                                                            "quota_erosion": commission.get(
                                                                "quota_erosion"
                                                            ),
                                                            "tier_name": f"Tier {tier_id_key}",
                                                        }
                                                    }
                                                }
                                            )
                                        else:
                                            # If the tier_id is not present in tier_id_split,
                                            # then add the tier_id with commission amount and tier name
                                            payee_line_item_data_map[payee][
                                                line_item_key
                                            ]["tier_id_split"].update(
                                                {
                                                    tier_id_key: {
                                                        original_tier_id: {
                                                            "commission": commission_amount_value,
                                                            "tier_name": f"Tier {tier_id_key}",
                                                        }
                                                    }
                                                }
                                            )

    return payee_line_item_data_map


def get_commission_records_unique_line_item_ids_total_count(
    client_id,
    payee_email,
    psd,
    ped,
    locked_kd,
    logger,
    additional_filters=None,
):
    # if locked - get locked commission data
    if locked_kd:
        logger.info(
            f"Payee commission for given period is locked. Fetching locked-kd - {locked_kd} commission records unique line_item_ids total count"
        )
        count = CommissionAccessor(
            client_id
        ).get_payee_locked_kd_commission_records_unique_line_item_ids(
            payee_email=payee_email,
            locked_kd=locked_kd,
            psd=psd,
            ped=ped,
            additional_filters=additional_filters,
            get_total_count=True,
        )
    else:
        logger.info(
            "Payee commission for given period is not locked. Fetching active commission records unique line_item_ids total count"
        )
        count = CommissionAccessor(
            client_id
        ).get_commission_records_unique_line_item_ids(
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            additional_filters=additional_filters,
            get_total_count=True,
        )
    logger.info(f"Total records count - {count}")
    return count


def get_valid_criteria_columns(
    client_id,
    columns,
    datasheet_id,
    knowledge_date,
):
    # Geting valid datasheet columns
    valid_columns = DatasheetVariableAccessor(client_id).get_datasheet_vars_system_name(
        datasheet_id, knowledge_date
    )
    valid_columns = set(valid_columns)

    # Adding default simulate columns
    valid_columns.update(
        {
            "commission",
            # Note: Quota erosion and tier name are saved as camel case in criteria columns
            "quotaErosion",
            "tierName",
            "tierValue",
            "tier_value",
            "original_tier_name",
            "originalTierName",
        }
    )

    # Filtering out invalid columns from criteria columns
    valid_criteria_columns = [col for col in columns if col in valid_columns]
    return valid_criteria_columns


def get_commission_records_unique_line_item_ids(
    client_id,
    payee_email,
    psd,
    ped,
    locked_kd,
    logger,
    additional_filters=None,
    offset=None,
    limit=None,
):
    # if locked - get locked commission data
    if locked_kd:
        logger.info(
            f"Payee commission for given period is locked. Fetching locked-kd - {locked_kd} commission line_item_ids"
        )
        commission_line_item_ids = CommissionAccessor(
            client_id
        ).get_payee_locked_kd_commission_records_unique_line_item_ids(
            payee_email=payee_email,
            locked_kd=locked_kd,
            psd=psd,
            ped=ped,
            additional_filters=additional_filters,
            offset=offset,
            limit=limit,
        )
    else:
        logger.info(
            "Payee commission for given period is not locked. Fetching active commission line_item_ids"
        )
        commission_line_item_ids = CommissionAccessor(
            client_id
        ).get_commission_records_unique_line_item_ids(
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            additional_filters=additional_filters,
            offset=offset,
            limit=limit,
        )

    return commission_line_item_ids


def update_hyperlink_columns_in_criteria_columns(
    client_id: int, criteria_id: UUID, plan_id: UUID, criteria_columns: List[str]
) -> List[str]:
    """
    Updates the list of criteria columns with hyperlink-required columns based on the given criteria columns.

    Args:
        client_id (int): The ID of the client.
        criteria_id (UUID): The ID of the criteria.
        plan_id (UUID): The ID of the plan.
        criteria_columns (List[str]): The list of criteria columns.

    Returns:
        List[str]: The updated list of criteria columns.

    """
    is_crm_hyperlinks_enabled = get_client_features(client_id=client_id).get(
        "crm_hyperlinks", False
    )
    hyperlink_required_columns = []

    if is_crm_hyperlinks_enabled:
        hyperlink_map = HyperlinkService(
            client_id=client_id
        ).get_hyperlink_map_for_statements(criteria_id=criteria_id, plan_id=plan_id)

        for item in hyperlink_map:
            hyperlink_required_columns += hyperlink_map[item]["url_identifier_fields"]

    return list(OrderedDict.fromkeys(criteria_columns + hyperlink_required_columns))


def get_current_period_commission_for_criteria(
    client_id,
    psd,
    ped,
    payee_email,
    plan_id,
    criteria_id,
    fx_rate_convertor,
    logger,
    login_user_id,
    offset=None,
    limit=None,
    return_records_count_only=False,
    is_zero_comm_line_item_included=False,
    orderby_fields: list[SortInfoType] | None = None,
):
    """
    current payout commission at line_item level for a criteria along with the datasheet data used in criteria
    """
    if isinstance(psd, str):
        psd = make_aware(date_utils.start_of_day(parse(psd, dayfirst=True)))
    if isinstance(ped, str):
        ped = make_aware(date_utils.end_of_day(parse(ped, dayfirst=True)))
    current_period_commission_by_line_item = []
    criteria = PlanCriteriaAccessor(client_id).get_criteria(
        criteria_id=criteria_id,
        fields=["criteria_data", "criteria_column"],
    )
    criteria_data = criteria["criteria_data"]
    criteria_columns = update_hyperlink_columns_in_criteria_columns(
        client_id, criteria_id, plan_id, criteria["criteria_column"]
    )
    is_line_item_level = criteria_data.get("is_line_item_level")
    read_settlement_from_snowflake = is_read_settlement_from_snowflake(client_id)
    # get commission lock
    comm_locked_kd = CommissionLockAccessor(
        client_id
    ).get_locked_kd_for_locked_comm_in_period(psd=psd, ped=ped, payee_email=payee_email)
    logger.info("Locked Kd for payee {0}: {1}".format(payee_email, comm_locked_kd))
    from commission_engine.services.settlement_data_service import (
        get_settlement_lock_kd,
    )

    # getting settlement lock
    settlement_locked_kd = get_settlement_lock_kd(
        client_id=client_id, psd=psd, ped=ped, payee_email=payee_email
    )

    if is_line_item_level:
        # first fetch unique commission line item ids with limit & offset
        logger.info("Fetching commission records line_item_ids")
        commission_line_item_ids = get_commission_records_unique_line_item_ids(
            client_id=client_id,
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            locked_kd=comm_locked_kd,
            logger=logger,
            additional_filters={
                "commission_plan_id": plan_id,
                "criteria_id": criteria_id,
            },
        )
        settlement_acc = settlement_accessor.SettlementAccessor(client_id)
        extra_filters = {
            "plan_id": plan_id,
            "criteria_id": criteria_id,
            "commission_row_key__in": commission_line_item_ids,
        }
        if read_settlement_from_snowflake:
            settlement_acc = SettlementSnowflakeAccessor(client_id)
            extra_filters = {
                "plan_id": plan_id,
                "criteria_id": criteria_id,
                "commission_row_key": commission_line_item_ids,
            }

        # return total count if requested
        if return_records_count_only:
            logger.info("Fetching commission records count.")
            return settlement_acc.get_settlements_curr_period_count(
                psd=psd,
                ped=ped,
                payee_email=payee_email,
                locked_kd=settlement_locked_kd,
                additional_filters=extra_filters,
            )

        # now fetch commission records for those unique line_item_id
        logger.info("Fetching commission_data_secondary_kd.")
        commission_data_secondary_kd = get_commission_records_secondary_kd(
            client_id=client_id,
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            locked_kd=comm_locked_kd,
            commission_line_item_ids=commission_line_item_ids,
            logger=logger,
            additional_filters={
                "commission_plan_id": plan_id,
                "criteria_id": criteria_id,
            },
        )
        logger.info("Fetched commission_data_secondary_kd.")

        knowledge_date = commission_data_secondary_kd

        # get settlements data
        logger.info("Fetching settlements for current period.")
        settlement_data = settlement_acc.get_settlements_curr_period(
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            locked_kd=settlement_locked_kd,
            additional_filters=extra_filters,
            fields=[
                "amount",
                "commission_row_key",
                "comm_period_start_date",
                "comm_period_end_date",
            ],
        )
        logger.info(
            f"Fetched settlements for current period. {len(settlement_data)} records"
        )

        settlement_by_commission_line_item_id = (
            group_settlements_by_commission_line_item(
                settlement_data, fx_rate_convertor
            )
        )

        for line_item, settlement in settlement_by_commission_line_item_id.items():
            current_period_commission_by_line_item.append(
                {"line_item_id": line_item, "commission": settlement}
            )

        # paginating only when orderby fields are empty. Otherwise pagination will
        # happen after sorting.
        if offset is not None and limit is not None and not orderby_fields:
            logger.info(
                f"Paginating the settlement records of limit {limit} and offset {offset}."
            )
            current_period_commission_by_line_item = (
                current_period_commission_by_line_item[
                    offset * limit : (offset * limit) + limit
                ]
            )

    else:
        comm_locked_kd = comm_locked_kd or timezone.now()
        logger.info("Fetching commission records for plan and summation criteria.")
        commission_recs = CommissionAccessor(
            client_id
        ).get_commission_for_plan_and_summation_criteria(
            payee_email,
            psd,
            ped,
            plan_id,
            criteria_id,
            comm_locked_kd,
            fields=["context_ids", "secondary_kd", "line_item_id"],
        )
        logger.info("Fetched commission record for plan and summation criteria.")
        knowledge_date = None
        commission_line_item_ids = []
        if commission_recs:
            knowledge_date = commission_recs[0]["secondary_kd"]
        # when split_summation_to_li flag is enabled, commission records will be created for each line item, line_item_id will be present in commission record and context_ids will be empty
        # when split_summation_to_li flag is disabled, single commission record will be created, line_item_id will be empty and context_ids will be present
        for record in commission_recs:
            if record["context_ids"]:
                commission_line_item_ids.extend(record["context_ids"])
            elif record["line_item_id"]:
                commission_line_item_ids.append(record["line_item_id"])

        logger.info("Fetching settlements for current period.")
        settlement_acc = settlement_accessor.SettlementAccessor(client_id)
        if read_settlement_from_snowflake:
            settlement_acc = SettlementSnowflakeAccessor(client_id)
        settlement_data = settlement_acc.get_settlements_curr_period(
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            locked_kd=settlement_locked_kd,
            additional_filters={
                "plan_id": plan_id,
                "criteria_id": criteria_id,
            },
            fields=[
                "amount",
            ],
        )
        logger.info(
            f"Fetched settlements for current period. {len(settlement_data)} records"
        )

        # return total count if requested
        if return_records_count_only:
            return len(commission_line_item_ids)

        # paginating only when orderby fields are empty. Otherwise pagination will
        # happen after sorting.
        if offset is not None and limit is not None and not orderby_fields:
            commission_line_item_ids = commission_line_item_ids[
                offset * limit : (offset * limit) + limit
            ]
        total_settlement = sum([sett["amount"] for sett in settlement_data])
        for line_item in commission_line_item_ids:
            current_period_commission_by_line_item.append(
                {"line_item_id": line_item, "commission": total_settlement}
            )

    databook_id = criteria_data.get("databook_id")
    datasheet_id = criteria_data.get("datasheet_id")
    ds_variables = DatasheetVariableAccessor(client_id).get_datasheet_vars_system_name(
        datasheet_id, knowledge_date
    )
    ds_fields_to_fetch = list(set(criteria_columns).intersection(set(ds_variables)))
    ds_fields_to_fetch.append("row_key")
    datasheet_df = fetch_datasheet_data_from_snowflake(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        knowledge_date=knowledge_date,
        row_keys=commission_line_item_ids,
        fields=ds_fields_to_fetch,
        login_user_id=login_user_id,
        logger=logger,
        as_dataframe=True,
    )

    logger.info("Joining Commission Dataframe with Datasheet Dataframe.")
    commission_df = pd.DataFrame(current_period_commission_by_line_item)

    valid_criteria_columns = zero_line_items_table(
        client_id, criteria_columns, datasheet_id, knowledge_date
    )
    if datasheet_df.empty or commission_df.empty:
        # For statement export as PDF(is_zero_comm_line_item_included is True), if no line item ids are present,
        # then return empty list for records with columns names
        if is_zero_comm_line_item_included:
            return {
                "records": [],
                "columns": valid_criteria_columns,
                "is_line_item_level": is_line_item_level,
            }
        return {"records": [], "columns": [], "is_line_item_level": is_line_item_level}

    collated_df = pd.merge(
        commission_df,
        datasheet_df,
        left_on="line_item_id",
        right_on="row_key",
        how="inner",
    )
    logger.info("Removing nan nat in collated fataframe (Commission & Datasheet)")
    collated_df = remove_nan_nat(collated_df)

    if orderby_fields:
        collated_df = (
            SortUtility(collated_df, orderby_fields)
            .sort_records()
            .apply_limit_offset(
                limit,
                offset * limit if limit is not None and offset is not None else None,
            )
            .as_dataframe()
        )
        logger.info(
            f"Sorted the dataframe and applied limit and offset. Sort by columns: {orderby_fields}"
        )

    result = {
        "records": collated_df.to_dict("records"),
        "columns": valid_criteria_columns,
        "is_line_item_level": is_line_item_level,
    }
    return result


def zero_line_items_table(client_id, criteria_columns, datasheet_id, knowledge_date):
    """
    This function is used to return the valid criteria columns for statement export as PDF even if the line item ids are not present
    """
    # removing tier column as tier level info won't be there
    if "tierName" in criteria_columns:
        criteria_columns.remove("tierName")
    # Filtering out invalid datasheet columns
    valid_criteria_columns = criteria_columns
    if datasheet_id:
        valid_criteria_columns = get_valid_criteria_columns(
            client_id, criteria_columns, datasheet_id, knowledge_date
        )
    return valid_criteria_columns


def get_deferred_commission_for_criteria(
    client_id,
    psd,
    ped,
    payee_email,
    plan_id,
    criteria_id,
    fx_rate_convertor,
    logger,
    login_user_id,
    offset=None,
    limit=None,
    return_records_count_only=False,
    is_zero_comm_line_item_included=False,
    orderby_fields: list[SortInfoType] | None = None,
):
    """
    deferred commission at line_item level for a criteria along with the datasheet data used in criteria
    """
    if isinstance(psd, str):
        psd = make_aware(date_utils.start_of_day(parse(psd, dayfirst=True)))
    if isinstance(ped, str):
        ped = make_aware(date_utils.end_of_day(parse(ped, dayfirst=True)))
    deferred_commission_by_line_item = []
    criteria = PlanCriteriaAccessor(client_id).get_criteria(
        criteria_id=criteria_id,
        fields=["criteria_data", "criteria_column", "criteria_config"],
    )
    criteria_data = criteria["criteria_data"]
    criteria_columns = update_hyperlink_columns_in_criteria_columns(
        client_id, criteria_id, plan_id, criteria["criteria_column"]
    )
    criteria_config = criteria["criteria_config"]
    trace_enabled = criteria_config.get("trace_enabled", False)
    is_line_item_level = criteria_data.get("is_line_item_level")
    read_settlement_from_snowflake = is_read_settlement_from_snowflake(client_id)

    # get commission lock
    comm_locked_kd = CommissionLockAccessor(
        client_id
    ).get_locked_kd_for_locked_comm_in_period(psd=psd, ped=ped, payee_email=payee_email)
    logger.info("Locked Kd for payee {0}: {1}".format(payee_email, comm_locked_kd))

    if is_line_item_level:
        # return total count if requested
        if return_records_count_only:
            logger.info("Fetching commission records count.")
            return get_commission_records_unique_line_item_ids_total_count(
                client_id=client_id,
                psd=psd,
                ped=ped,
                payee_email=payee_email,
                locked_kd=comm_locked_kd,
                logger=logger,
                additional_filters={
                    "commission_plan_id": plan_id,
                    "criteria_id": criteria_id,
                },
            )

        # When sorting is applied, the limit_value and offset_value will be None making the
        # get commission records to return all values.
        limit_value, offset_value = None, None
        if limit is not None and offset is not None and not orderby_fields:
            limit_value, offset_value = limit, offset

        # first fetch unique commission line item ids with limit & offset
        logger.info("Fetching commission records line_item_ids")
        commission_line_item_ids = get_commission_records_unique_line_item_ids(
            client_id=client_id,
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            locked_kd=comm_locked_kd,
            logger=logger,
            additional_filters={
                "commission_plan_id": plan_id,
                "criteria_id": criteria_id,
            },
            offset=offset_value,
            limit=limit_value,
        )

        # now fetch commission records for those unique line_item_id
        logger.info("Fetching commission records.")
        commission_data = get_commission_records(
            client_id=client_id,
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            locked_kd=comm_locked_kd,
            logger=logger,
            commission_line_item_ids=commission_line_item_ids,
            additional_filters={
                "commission_plan_id": plan_id,
                "criteria_id": criteria_id,
            },
            fields=["line_item_id", "amount", "secondary_kd"],
        )
        logger.info(f"Fetched commission records. {len(commission_data)} records")

        knowledge_date = next(iter(commission_data or []), {}).get(
            "secondary_kd"
        )  # first record in commission

        # getting settlement lock
        from commission_engine.services.settlement_data_service import (
            get_settlement_lock_kd,
        )

        settlement_locked_kd = get_settlement_lock_kd(
            client_id=client_id, psd=psd, ped=ped, payee_email=payee_email
        )

        # get settlements data
        logger.info("Fetching settlements for current period.")
        settlement_acc = settlement_accessor.SettlementAccessor(client_id)
        extra_filters = {
            "plan_id": plan_id,
            "criteria_id": criteria_id,
            "commission_row_key__in": commission_line_item_ids,
        }
        if read_settlement_from_snowflake:
            settlement_acc = SettlementSnowflakeAccessor(client_id)
            extra_filters = {
                "plan_id": plan_id,
                "criteria_id": criteria_id,
                "commission_row_key": commission_line_item_ids,
            }

        settlement_data = settlement_acc.get_settlements_curr_period(
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            locked_kd=settlement_locked_kd,
            additional_filters=extra_filters,
            fields=[
                "amount",
                "commission_row_key",
                "comm_period_start_date",
                "comm_period_end_date",
            ],
        )
        logger.info(
            f"Fetched settlements for current period. {len(settlement_data)} records"
        )

        settlement_by_commission_line_item_id = (
            group_settlements_by_commission_line_item(
                settlement_data, fx_rate_convertor
            )
        )
        commission_by_line_item = group_commissions_by_line_item(
            commission_data, fx_rate_convertor
        )
        settlement_by_commission_line_item_id_keys = (
            settlement_by_commission_line_item_id.keys()
        )

        for line_item, commission in commission_by_line_item.items():
            if str(line_item) not in settlement_by_commission_line_item_id_keys:
                deferred_commission_by_line_item.append(
                    {"line_item_id": line_item, "commission": commission * -1}
                )
        for line_item, settlement in settlement_by_commission_line_item_id.items():
            commission = commission_by_line_item.get(line_item, 0)
            commission_deferred = settlement - commission
            deferred_commission_by_line_item.append(
                {"line_item_id": line_item, "commission": commission_deferred}
            )
    else:
        comm_locked_kd = comm_locked_kd or timezone.now()
        logger.info("Fetching commission records for plan and summation criteria.")
        commission_recs = CommissionAccessor(
            client_id
        ).get_commission_for_plan_and_summation_criteria(
            payee_email,
            psd,
            ped,
            plan_id,
            criteria_id,
            comm_locked_kd,
            fields=["context_ids", "secondary_kd", "line_item_id"],
        )

        knowledge_date = None
        commission_line_item_ids = []
        if commission_recs:
            knowledge_date = commission_recs[0]["secondary_kd"]
        # when split_summation_to_li flag is enabled, commission records will be created for each line item, line_item_id will be present in commission record and context_ids will be empty
        # when split_summation_to_li flag is disabled, single commission record will be created, line_item_id will be empty and context_ids will be present
        for record in commission_recs:
            if record["context_ids"]:
                commission_line_item_ids.extend(record["context_ids"])
            elif record["line_item_id"]:
                commission_line_item_ids.append(record["line_item_id"])

        logger.info("Fetched commission record for plan and summation criteria.")

        # return total count if requested
        if return_records_count_only:
            return len(commission_line_item_ids)

        # paginating only when orderby fields are empty. Otherwise pagination will
        # happen after sorting.
        if offset is not None and limit is not None and not orderby_fields:
            commission_line_item_ids = commission_line_item_ids[
                offset * limit : (offset * limit) + limit
            ]

        for line_item in commission_line_item_ids:
            deferred_commission_by_line_item.append(
                {"line_item_id": line_item, "commission": 0}
            )

    databook_id = criteria_data.get("databook_id")
    datasheet_id = criteria_data.get("datasheet_id")
    ds_variables = DatasheetVariableAccessor(client_id).get_datasheet_vars_system_name(
        datasheet_id, knowledge_date
    )
    ds_fields_to_fetch = list(set(criteria_columns).intersection(set(ds_variables)))
    ds_fields_to_fetch.append("row_key")
    datasheet_df = fetch_datasheet_data_from_snowflake(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        knowledge_date=knowledge_date,
        row_keys=commission_line_item_ids,
        fields=ds_fields_to_fetch,
        login_user_id=login_user_id,
        logger=logger,
        as_dataframe=True,
    )

    logger.info("Joining Commission Dataframe with Datasheet Dataframe.")
    commission_df = pd.DataFrame(deferred_commission_by_line_item)
    valid_criteria_columns = zero_line_items_table(
        client_id, criteria_columns, datasheet_id, knowledge_date
    )

    if datasheet_df.empty or commission_df.empty:
        # For statement export as PDF(is_zero_comm_line_item_included is True), if no line item ids are present,
        # then return empty list for records with columns names
        if is_zero_comm_line_item_included:
            return {
                "records": [],
                "columns": valid_criteria_columns,
                "is_line_item_level": is_line_item_level,
                "trace_enabled": trace_enabled,
            }
        return {
            "records": [],
            "columns": [],
            "is_line_item_level": is_line_item_level,
            "trace_enabled": trace_enabled,
        }

    collated_df = pd.merge(
        commission_df,
        datasheet_df,
        left_on="line_item_id",
        right_on="row_key",
        how="inner",
    )
    logger.info("Removing nan nat in collated fataframe (Commission & Datasheet)")
    collated_df = remove_nan_nat(collated_df)

    if orderby_fields:
        collated_df = (
            SortUtility(collated_df, orderby_fields)
            .sort_records()
            .apply_limit_offset(
                limit,
                offset * limit if limit is not None and offset is not None else None,
            )
            .as_dataframe()
        )
        logger.info(
            f"Sorted the dataframe and applied limit and offset. Sort by columns: {orderby_fields}"
        )

    return {
        "records": collated_df.to_dict("records"),
        "columns": valid_criteria_columns,
        "is_line_item_level": is_line_item_level,
        "trace_enabled": trace_enabled,
    }


def get_prev_deferred_commission_for_criteria(
    client_id,
    comm_ped,
    psd,
    ped,
    payee_email,
    plan_id,
    criteria_id,
    fx_rate_convertor,
    login_user_id,
    logger,
    offset=None,
    limit=None,
    return_records_count_only=False,
    base_currency=False,
    is_zero_comm_line_item_included=False,
    orderby_fields: list[SortInfoType] | None = None,
):
    """
    prev deferred commission settled at [psd,ped] at line_item level for a criteria along with
    the datasheet data used in criteria
    """

    if isinstance(psd, str):
        psd = make_aware(date_utils.start_of_day(parse(psd, dayfirst=True)))
    if isinstance(ped, str):
        ped = make_aware(date_utils.end_of_day(parse(ped, dayfirst=True)))
    criteria = PlanCriteriaAccessor(client_id).get_criteria(
        criteria_id=criteria_id,
        fields=["criteria_data", "criteria_column", "criteria_config"],
    )
    criteria_data = criteria["criteria_data"]
    criteria_columns = update_hyperlink_columns_in_criteria_columns(
        client_id, criteria_id, plan_id, criteria["criteria_column"]
    )
    criteria_config = criteria["criteria_config"]
    trace_enabled = criteria_config.get("trace_enabled", False)
    is_line_item_level = criteria_data.get("is_line_item_level")
    from commission_engine.services.settlement_data_service import (
        get_settlement_lock_kd,
    )

    # getting settlement lock
    settlement_locked_kd = get_settlement_lock_kd(
        client_id=client_id, psd=psd, ped=ped, payee_email=payee_email
    )

    settlement_acc = settlement_accessor.SettlementAccessor(client_id)
    if is_read_settlement_from_snowflake(client_id):
        settlement_acc = SettlementSnowflakeAccessor(client_id)
    # return total count if requested
    if return_records_count_only:
        return (
            settlement_acc.get_settlements_prev_period_unique_commission_row_keys_count(
                comm_ped=comm_ped,
                psd=psd,
                ped=ped,
                payee_email=payee_email,
                locked_kd=settlement_locked_kd,
                additional_filters={"plan_id": plan_id, "criteria_id": criteria_id},
            )
        )

    # get unique commission row keys with limit & offset
    commission_row_keys = (
        settlement_acc.get_settlements_prev_period_unique_commission_row_keys(
            comm_ped=comm_ped,
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            locked_kd=settlement_locked_kd,
            additional_filters={"plan_id": plan_id, "criteria_id": criteria_id},
        )
    )

    # get settlements data for previous period
    logger.info("Fetching settlements for previous period.")
    settlement_acc = settlement_accessor.SettlementAccessor(client_id)
    extra_filters = {
        "plan_id": plan_id,
        "criteria_id": criteria_id,
        "commission_row_key__in": commission_row_keys,
    }
    if is_read_settlement_from_snowflake(client_id):
        settlement_acc = SettlementSnowflakeAccessor(client_id)
        extra_filters = {
            "plan_id": plan_id,
            "criteria_id": criteria_id,
            "commission_row_key": commission_row_keys,
        }
    settlement_data = settlement_acc.get_settlements_prev_period(
        psd=psd,
        ped=ped,
        payee_email=payee_email,
        locked_kd=settlement_locked_kd,
        additional_filters=extra_filters,
        fields=[
            "knowledge_begin_date",
            "amount",
            "commission_row_key",
            "comm_period_start_date",
            "comm_period_end_date",
        ],
    )
    logger.info(
        f"Fetched settlements for previous period. {len(settlement_data)} records"
    )

    if len(settlement_data) == 0:
        return {
            "records": [],
            "columns": [],
            "is_line_item_level": is_line_item_level,
            "trace_enabled": trace_enabled,
        }

    payee_currency = (
        EmployeePayrollAccessor(client_id)
        .get_latest_employee_payroll(payee_email, fields="pay_currency")
        .get("pay_currency")
    )

    settlement_by_line_item = group_settlements_by_commission_line_item(
        settlement_data, fx_rate_convertor, payee_currency, client_id, payee_email
    )

    # When sorting is applied, the limit_value and offset_value will be None making the
    # get commission records to return all values.
    if limit is not None and offset is not None and not orderby_fields:
        logger.info(
            f"Paginating the settlement records of limit {limit} and offset {offset}."
        )
        settlement_by_line_item = dict(
            islice(
                settlement_by_line_item.items(), offset * limit, offset * limit + limit
            )
        )

    commission_row_keys = settlement_by_line_item.keys()

    databook_id = criteria_data.get("databook_id")
    datasheet_id = criteria_data.get("datasheet_id")
    comm_sec_kd = CommissionSecondaryKdAccessor(
        client_id=client_id
    ).get_sec_kd_for_period_payees(
        period_end_date=comm_ped, payee_email_ids=[payee_email]
    )
    latest_settlement_data = max(
        settlement_data, key=lambda rec: rec.get("knowledge_begin_date")
    )
    # On fixing a sev-2 issue where the datasheet_data records were deleted after locking commissions
    # Changed the know_date to commission_sec_kd and found unit_tests failing when there are no comm_sec_kd
    # therefore added an else condition
    knowledge_date = (
        comm_sec_kd[0].get("sec_kd")
        if comm_sec_kd
        else latest_settlement_data.get("knowledge_begin_date")
    )
    ds_variables = DatasheetVariableAccessor(client_id).get_datasheet_vars_system_name(
        datasheet_id, knowledge_date
    )
    ds_fields_to_fetch = list(set(criteria_columns).intersection(set(ds_variables)))
    ds_fields_to_fetch.append("row_key")
    datasheet_df = fetch_datasheet_data_from_snowflake(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        knowledge_date=knowledge_date,
        row_keys=list(commission_row_keys),
        fields=ds_fields_to_fetch,
        login_user_id=login_user_id,
        logger=logger,
        as_dataframe=True,
    )
    if base_currency:
        for settlement in settlement_by_line_item:
            settlement_by_line_item[settlement] = float(
                fx_rate_convertor.change_to_base_currency(
                    settlement_by_line_item[settlement], None
                )
            )

    settlement_df = pd.DataFrame(
        settlement_by_line_item.items(), columns=["line_item_id", "commission"]
    )
    valid_criteria_columns = zero_line_items_table(
        client_id, criteria_columns, datasheet_id, knowledge_date
    )

    if datasheet_df.empty or settlement_df.empty:
        # For statement export as PDF(is_zero_comm_line_item_included is True), if no line item ids are present,
        # then return empty list for records with columns names
        if is_zero_comm_line_item_included:
            return {
                "records": [],
                "columns": valid_criteria_columns,
                "is_line_item_level": is_line_item_level,
                "trace_enabled": trace_enabled,
            }
        return {
            "records": [],
            "columns": [],
            "is_line_item_level": is_line_item_level,
            "trace_enabled": trace_enabled,
        }

    logger.info("Joining Settlement Dataframe with Datasheet Dataframe.")
    collated_df = pd.merge(
        settlement_df,
        datasheet_df,
        left_on="line_item_id",
        right_on="row_key",
        how="inner",
    )
    logger.info("Removing nan nat in collated fataframe (Settlement & Datasheet).")
    collated_df = remove_nan_nat(collated_df)

    if orderby_fields:
        collated_df = (
            SortUtility(collated_df, orderby_fields)
            .sort_records()
            .apply_limit_offset(
                limit,
                offset * limit if limit is not None and offset is not None else None,
            )
            .as_dataframe()
        )
        logger.info(
            f"Sorted the dataframe and applied limit and offset. Sort by columns: {orderby_fields}"
        )

    return {
        "records": collated_df.to_dict("records"),
        "columns": valid_criteria_columns,
        "is_line_item_level": is_line_item_level,
        "trace_enabled": trace_enabled,
    }


# quota_team_type can have values "all" / None
def get_commission_data(
    client_id,
    psd,
    ped,
    payee_email,
    plan_criteria_constraint=None,
    login_user_id=None,
    apply_datasheet_permissions=False,
):
    """
    plan_criteria_constraint can be used to fetch data for certain plan and criteria only
    plan_criteria_constraint: {plan_id: <>, criteria_id: <>}
    """
    manage_permissions_enabled = False
    if login_user_id:
        manage_permissions_enabled = does_user_have_databook_manage_permission(
            client_id, login_user_id
        )

    psd = make_aware(start_of_day(parse(psd, dayfirst=True)))
    ped = make_aware(end_of_day(parse(ped, dayfirst=True)))

    log_context = {
        "client_id": client_id,
        "period_start_date": psd,
        "period_end_date": ped,
        "payee_email": payee_email,
    }
    logger = iputils.LogWithContext(log_context)
    cache_key = ""
    if manage_permissions_enabled:
        apply_datasheet_permissions = False
    if plan_criteria_constraint is None and not apply_datasheet_permissions:
        # generate cache key and check if commission data is cached
        cache_key = get_commission_cache_key(client_id, psd, ped, payee_email)
        comm_data = cache.get(cache_key)
        # if commission data found in cache return value
        if comm_data:
            logger.info(
                f"Cache Hit: get_commission_data function for payee - {payee_email}",
                {"cache_event": "CACHE_HIT"},
            )
            return comm_data

    paid_kd = get_latest_or_kd_if_paid(client_id, psd, ped, payee_email)
    # Get Paid KD
    is_paid = False
    # Get frozen KD from paid KD or latest Frozen KD
    # TODO: should change is_paid logic.
    if paid_kd:
        is_paid = True
        locked_kd = CommissionLockAccessor(
            client_id
        ).get_kd_locked_kd_for_locked_comm_in_period(
            psd=psd, ped=ped, payee_email=payee_email, kd=paid_kd
        )
    else:
        locked_kd = CommissionLockAccessor(
            client_id
        ).get_locked_kd_for_locked_comm_in_period(
            psd=psd, ped=ped, payee_email=payee_email
        )
    # if locked - get locked commission data
    logger.info("Locked Kd for payee {0}: {1}".format(payee_email, locked_kd))
    is_locked = False
    if locked_kd:
        is_locked = True
        all_commission_plan_data = CommissionAccessor(
            client_id
        ).get_payee_locked_kd_commission_data(
            payee_email=payee_email,
            locked_kd=locked_kd,
            psd=psd,
            ped=ped,
            fields=[],
        )
        # is_team is passed as None to retrieve all quotas. showing &Team quota for manager is done in frontend
        quota_data = QuotaErosionAccessor(client_id).get_payee_snapshot_quota_data(
            payee_email=payee_email,
            locked_kd=locked_kd,
            psd=psd,
            ped=ped,
            is_team=None,
            fields=[],
        )
    else:
        all_commission_plan_data = CommissionAccessor(client_id).get_commission_data(
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            fields=[],
        )
        quota_data = QuotaErosionAccessor(client_id).get_quota_data(
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            is_team=None,
            fields=[],
        )
    logger.info(
        "Got Commissions, Quotas for payee {0} in period {1}-{2}: {3}, {4}".format(
            payee_email,
            psd,
            ped,
            len(all_commission_plan_data),
            len(quota_data),
        )
    )
    quota_by_plan = nest(quota_data, "commission_plan_id")
    quota_by_plan_merged = {}
    for plan_id, plan_data in quota_by_plan.items():
        plan_id = str(plan_id)
        grouped_records_by_line_item_id = {}
        for record in plan_data:
            criteria_id = (
                str(record["criteria_id"]) if record["criteria_id"] else "None"
            )
            line_item_id = (
                str(record["line_item_id"]) if record["line_item_id"] else "None"
            )
            tier_id = str(record["tier_id"]) if record["tier_id"] else "None"
            key = criteria_id + "__##__" + line_item_id + "__##__" + tier_id
            #
            if key in grouped_records_by_line_item_id:
                grouped_records_by_line_item_id[key]["quota_erosion"] += record[
                    "quota_erosion"
                ]
            else:
                grouped_records_by_line_item_id[key] = record
        quota_by_plan_merged[str(plan_id)] = list(
            grouped_records_by_line_item_id.values()
        )
    quota_by_plan = quota_by_plan_merged
    logger.info("Grouped Quota by plan, criteria, line item and tier")
    secondary_kd = (
        all_commission_plan_data[0]["secondary_kd"]
        if all_commission_plan_data
        else None
    )
    primary_kd = (
        all_commission_plan_data[0]["primary_kd"] if all_commission_plan_data else None
    )
    # group by plan id
    grouped_by_plan = nest(
        all_commission_plan_data, "commission_plan_id", "criteria_id"
    )
    # group same tier values into one
    logger.info("Secondary Kd: {0}, Primary Kd: {1}".format(secondary_kd, primary_kd))
    grouped_by_plan_and_commission = {}
    for plan_id, plan_data in grouped_by_plan.items():
        plan_id = str(plan_id)
        grouped_by_plan_and_commission[plan_id] = {}
        for criteria_id, criteria_data in plan_data.items():
            criteria_id = str(criteria_id)
            line_item_tier_map = {}
            for commission in criteria_data:
                criteria_id = (
                    str(commission["criteria_id"])
                    if commission["criteria_id"]
                    else "None"
                )
                line_item_id = (
                    str(commission["line_item_id"])
                    if commission["line_item_id"]
                    else "None"
                )
                tier_id = (
                    str(commission["tier_id"]) if commission["tier_id"] else "None"
                )
                key = criteria_id + "__##__" + line_item_id + "__##__" + tier_id
                if key in line_item_tier_map:
                    line_item_tier_map[key]["amount"] += commission["amount"]
                else:
                    line_item_tier_map[key] = commission
            grouped_by_plan_and_commission[str(plan_id)][str(criteria_id)] = list(
                line_item_tier_map.values()
            )
    grouped_by_plan = grouped_by_plan_and_commission
    logger.info("Grouped Commission by plan, criteria, line item and tier")
    plan_ids = grouped_by_plan.keys()
    plan_dfns = nest(
        CommissionPlanAccessor(client_id).get_commission_plan_by_plan_id(
            plan_ids, as_dicts=True
        ),
        "plan_id",
    )
    criteria_dfns = nest(
        PlanCriteriaAccessor(client_id).get_criterias_for_plans(plan_ids),
        "criteria_id",
    )
    logger.info("Got plan and criteria details for plans - {}".format(plan_ids))
    modified_criteria_dfns, modified_plan_dfns = {}, {}
    for key, value in criteria_dfns.items():
        modified_criteria_dfns[str(key)] = value
    for key, value in plan_dfns.items():
        modified_plan_dfns[str(key)] = value
    criteria_dfns, plan_dfns = modified_criteria_dfns, modified_plan_dfns
    plan_result = {}
    for plan_id, plan_data in grouped_by_plan.items():
        if plan_criteria_constraint:
            if str(plan_criteria_constraint["plan_id"]) != str(plan_id):
                continue
        plan_name = plan_dfns[plan_id][0]["plan_name"]
        plan_type = plan_dfns[plan_id][0]["plan_type"]
        plan_display_order = plan_dfns[plan_id][0]["plan_display_order"]
        plan_id_str = str(plan_id)
        plan_result[plan_id_str] = {
            "plan_id": plan_id,
            "plan_name": plan_name,
            "plan_type": plan_type,
            "criteria_datas": {},
            "plan_display_order": plan_display_order,
        }
        plan_result[plan_id_str]["quota_data"] = (
            quota_by_plan[plan_id] if plan_id in quota_by_plan else None
        )
        for criteria_id, criteria_comm_data in plan_data.items():
            if plan_criteria_constraint:
                if str(plan_criteria_constraint["criteria_id"]) != str(criteria_id):
                    continue

            criteria_dfn = criteria_dfns[criteria_id][0]
            criteria_name = criteria_dfn["criteria_name"]
            criteria_type = criteria_dfn["criteria_type"]
            team_name = (
                criteria_dfn["criteria_data"]["team"]
                if "team" in criteria_dfn["criteria_data"]
                else None
            )
            log_me(f"Working on criteria {criteria_name}")

            # get criteria conditions
            criteria_conditions = {
                "databook_id": (
                    criteria_dfn["criteria_data"]["databook_id"]
                    if "databook_id" in criteria_dfn["criteria_data"]
                    else None
                ),
                "datasheet_id": (
                    criteria_dfn["criteria_data"]["datasheet_id"]
                    if "datasheet_id" in criteria_dfn["criteria_data"]
                    else None
                ),
                "payee_field": (
                    criteria_dfn["criteria_data"]["payee_field"]
                    if "payee_field" in criteria_dfn["criteria_data"]
                    else None
                ),
                "date_field": (
                    criteria_dfn["criteria_data"]["date_field"]
                    if "date_field" in criteria_dfn["criteria_data"]
                    else None
                ),
            }
            criteria_dfn["criteria_data"]["criteria_conditions"] = criteria_conditions
            # get data from get_data using sec kd
            stiched_data = get_primary_secondary_data_for_payee(
                client_id,
                psd,
                ped,
                secondary_kd,
                criteria_conditions,
                payee_email,
                criteria_type,
                team_name,
                login_user_id=login_user_id,
                apply_datasheet_permissions=apply_datasheet_permissions,
            )
            logger.info(
                "Fetched {0} records for Criteria Id {1}".format(
                    len(stiched_data), criteria_id
                )
            )
            logger.info(
                "Fetched {0} records for Criteria Id {1}".format(
                    len(stiched_data), criteria_id
                )
            )
            if stiched_data:
                for line_item_id in stiched_data:
                    for line_item in stiched_data[line_item_id]:
                        line_item["item_key"] = line_item_id
            # // Preparing result
            criteria_id_str = str(criteria_id)
            is_line_item_level = criteria_dfn["criteria_data"]["is_line_item_level"]
            if not is_line_item_level:  # Get Context ids from commission
                lk = locked_kd if locked_kd else None
                commission_recs = CommissionAccessor(
                    client_id
                ).get_commission_for_plan_and_summation_criteria(
                    payee_email,
                    psd,
                    ped,
                    plan_id,
                    criteria_id,
                    lk,
                    ["context_ids", "line_item_id"],
                )
                commission_line_item_ids = []
                # when split_summation_to_li flag is enabled, commission records will be created for each line item, line_item_id will be present in commission record and context_ids will be empty
                # when split_summation_to_li flag is disabled, single commission record will be created, line_item_id will be empty and context_ids will be present
                for record in commission_recs:
                    if record["context_ids"]:
                        commission_line_item_ids.extend(record["context_ids"])
                    elif record["line_item_id"]:
                        commission_line_item_ids.append(record["line_item_id"])

                for ele in criteria_comm_data:
                    ele["context_ids"] = commission_line_item_ids

            if (
                apply_datasheet_permissions is True
                and manage_permissions_enabled is False
            ):
                hidden_columns_for_given_source_id = get_all_hidden_columns_for_user(
                    client_id, [criteria_conditions["datasheet_id"]], login_user_id
                )[criteria_conditions["datasheet_id"]]

                criteria_dfn["criteria_column"] = list(
                    set(criteria_dfn["criteria_column"]).difference(
                        set(hidden_columns_for_given_source_id)
                    )
                )

            plan_result[plan_id_str]["criteria_datas"][criteria_id_str] = {
                "criteria_id": criteria_id,
                "criteria_name": criteria_name,
                "criteria_type": criteria_dfn["criteria_type"],
                "team_criteria_type": criteria_dfn["criteria_data"]["type"],
                "criteria_column": criteria_dfn["criteria_column"],
                "criteria_config": criteria_dfn["criteria_config"],
                "criteria_display_order": criteria_dfn["criteria_display_order"],
                "is_line_item_level": criteria_dfn["criteria_data"][
                    "is_line_item_level"
                ],
                "criteria_conditions": criteria_dfn["criteria_data"][
                    "criteria_conditions"
                ],
            }
            plan_result[plan_id_str]["criteria_datas"][criteria_id_str][
                "stiched_data"
            ] = stiched_data
            plan_result[plan_id_str]["criteria_datas"][criteria_id_str][
                "commission_data"
            ] = []
            for criteria_comm in criteria_comm_data:
                if "tier_id" in criteria_comm and criteria_comm["tier_id"] is not None:
                    try:
                        tier_id = int(criteria_comm["tier_id"])
                        tier = criteria_dfn["criteria_data"]["part2"][tier_id]
                        tier_name = (
                            tier["tier_name"]
                            if "tier_name" in tier
                            and tier["tier_name"] is not None
                            and len(tier["tier_name"]) > 0
                            else str(tier_id)
                        )
                    except Exception:
                        tier_name = criteria_comm["tier_id"]
                    criteria_comm["tier_name"] = tier_name
                # del criteria_comm["_state"]
                plan_result[plan_id_str]["criteria_datas"][criteria_id_str][
                    "commission_data"
                ].append(criteria_comm)
    log_me(f"Criteria Result {plan_result.keys()}")
    # Get quota schedule
    client = get_client(client_id)
    fiscal_year = get_fiscal_year(client.fiscal_start_month, ped)
    quotas = EmployeeQuotaAccessor(
        client_id
    ).get_all_latest_employee_quota_using_effective_date(
        payee_email, fiscal_year, False, ped
    )
    quota_schedule = {
        quota.quota_category_name: quota.quota_schedule_type for quota in quotas
    }
    # check if payee has reportees
    reportees = HierarchyAccessor(client_id).get_reportees(ped, payee_email)
    has_reportees = False
    if reportees and len(reportees) > 0:
        has_reportees = True
    logger.info("End of get_commission_data")
    res = {
        "plan_data": plan_result,
        "is_paid": is_paid,
        "is_locked": is_locked,
        "has_reportees": has_reportees,
        "primary_kd": primary_kd,
        "quota_schedule": quota_schedule,
    }
    # cache fetched commission data for payee
    logger.info(
        f"Cache Missed: get_commission_data function. Fetched commission data for {payee_email}",
        {"cache_event": "CACHE_MISS"},
    )
    if plan_criteria_constraint is None and not apply_datasheet_permissions:
        cache.set(cache_key, res, 3600)  # timeout for 60 minutes
    return res
    # calc cum quota erosion and quota attainment (current qe + cum qe)
    ######
    # get comm adjustments -> separate query
    # get draw adjustments  -> separate query
    # Variable pay -> separate query
    # Reporting manager -> separate query


def get_commission_data_snowflake(client_id, psd, ped, payee_email):
    """
    Fetch commission data from commission report object
    """
    psd = make_aware(start_of_day(parse(psd, dayfirst=True)))
    ped = make_aware(end_of_day(parse(ped, dayfirst=True)))
    cache_key = ""
    # generate cache key and check if commission data is cached
    cache_key = "what_if" + get_commission_cache_key(client_id, psd, ped, payee_email)
    comm_data = cache.get(cache_key)
    # if commission data found in cache return value
    if comm_data:
        info = (
            "Cache Hit: get_commission_data_snowflake function for payee " + payee_email
        )
        c_logger.info(info, {"cache_event": "CACHE_HIT"})
        return comm_data

    period_dict = {"period_start_date": psd, "period_end_date": ped}

    payroll = EmployeePayrollAccessor(client_id).get_payroll_details_based_on_sec_kd(
        ped, [payee_email]
    )
    if payroll and payroll[0].payout_frequency.lower() in static_frequencies:
        period_label = _get_report_period_label(client_id, period_dict)
    else:
        period_label = psd.strftime("%b %d, %Y") + " - " + ped.strftime("%b %d, %Y")

    table_name = get_report_object_data_table_name(client_id, "commission")
    commission_data_query_string = f"""
        select
            data:period_label::STRING as period_label,
            to_timestamp(data:period_start_date) as period_start_date,
            to_timestamp(data:period_end_date) as period_end_date,
            data:payee_name::STRING as payee_name,
            data:payee_email_id::STRING as payee_email_id,
            data:payout_freq::STRING as payout_freq,
            data:plan_name::STRING as plan_name,
            data:plan_type::STRING as plan_type,
            data:record_type::STRING as record_type,
            data:criteria_name::STRING as criteria_name,
            data:line_item_id::STRING as line_item_id,
            data:tier_id::STRING as tier_id,
            data:tier_name::STRING as tier_name,
            data:quota_erosion::DOUBLE as quota_erosion,
            data:amount::DOUBLE as amount,
            data:payee_currency::STRING as payee_currency,
            data:conversion_rate::DOUBLE as conversion_rate,
            data:amount_payee_currency::DOUBLE as amount_payee_currency,
            data:databook_name::STRING as databook_name,
            data:datasheet_name::STRING as datasheet_name,
            data:is_locked::BOOLEAN as is_locked,
            to_timestamp(data:locked_kd) as locked_kd,
            to_timestamp(data:knowledge_begin_date) as knowledge_begin_date,
            data:commission_plan_id::STRING as commission_plan_id,
            data:criteria_id::STRING as criteria_id,
            data:adjustment_id::STRING as adjustment_id
        from
            {table_name}
        where
            client_id = {client_id}
            and knowledge_end_date is null
            and is_deleted = false
            and object_id = 'commission'
            and object_type = 'commission_object'
            and data:payee_email_id = '{payee_email}'
            and data:record_type = 'Commission'
            and data:period_label = '{period_label}'
    """
    ctx = get_pooled_connection(client_id=client_id)

    com_cur = ctx.cursor().execute(commission_data_query_string)
    all_commission_plan_data_df = com_cur.fetch_pandas_all()
    all_commission_plan_data_df.columns = [
        col_name.lower() for col_name in all_commission_plan_data_df.columns
    ]
    all_commission_plan_data = all_commission_plan_data_df.to_dict("records")

    c_logger.info(
        "GET_COMMISSION_DATA_SNOWFLAKE : Cache Missed - Got Commissions for payee %s in period %s-%s: %s",
        payee_email,
        psd,
        ped,
        len(all_commission_plan_data),
    )

    # cache fetched commission data for payee
    cache.set(cache_key, all_commission_plan_data, 60 * 60)  # timeout for 60 minutes
    return all_commission_plan_data


def _get_report_period_label(client_id, record):
    c_logger.info("Fetch report period label for - %s", record)
    psd = record["period_start_date"]
    ped = record["period_end_date"]

    payout_freq = get_period_freq(start_date=psd, end_date=ped)
    if payout_freq == "invalid":
        return "Invalid"
    client = get_client(client_id)
    payout_period = find_period(int(psd.month), client.fiscal_start_month, payout_freq)
    c_logger.info("Payout period is - %s", payout_period)
    period_index = payout_period.get("index")
    period_label = get_period_label(
        period_index, payout_freq, client.fiscal_start_month
    )
    c_logger.info("Payout label is - %s", period_label)
    fiscal_year = get_fiscal_year(client.fiscal_start_month, psd)
    if payout_freq == Freq.MONTHLY.value:
        fiscal_year = psd.year
    report_period_label = f"{period_label} {fiscal_year}"
    c_logger.info("The report period label is - %s", report_period_label)
    return report_period_label


def get_primary_secondary_data_for_payee(
    client_id,
    psd,
    ped,
    secondary_kd,
    criteria_conditions,
    payee_email,
    criteria_type,
    team_name,
    login_user_id=None,
    apply_datasheet_permissions=True,
):
    payee_email_tuple = (payee_email,)
    if criteria_type in ("Team", "CustomTeam"):
        team_members = get_team_members(client_id, psd, ped, payee_email, team_name)
        client_features = get_client_features(client_id=client_id)
        is_manager_rollup_ed = client_features.get("manager_rollup_ed")
        if is_manager_rollup_ed:
            team_members_start = get_team_members(
                client_id, psd, psd, payee_email, team_name
            )
            team_members = list(set(team_members).union(team_members_start))
        for payee in team_members:
            payee_email_tuple += (payee,)

    stiched_data_for_all_payees = cs.get_data(
        client_id,
        psd,
        ped,
        sec_knowledge_date=secondary_kd,
        criteria_conditions=criteria_conditions,
        payee_email_tuple=payee_email_tuple,
        login_user_id=login_user_id,
        apply_datasheet_permission=apply_datasheet_permissions,
    )
    key = "row_key"
    stitched_data = {}
    if criteria_type in ("Team", "CustomTeam"):
        team_members = get_team_members(client_id, psd, ped, payee_email, team_name)
        log_me(f"team members: {team_members}")
        team_data = get_team_context(team_members, stiched_data_for_all_payees)
        stitched_data = nest(team_data["merged_data"], key)
    else:
        if payee_email in stiched_data_for_all_payees["merged_data"]:
            stitched_data = nest(
                stiched_data_for_all_payees["merged_data"][payee_email], key
            )
    stitched_data = stitched_data if stitched_data else {}
    # if limit_length and len(stitched_data) > 500:
    #     keys = list(stitched_data.keys())[:500]
    #     new_dict = {}
    #     for k in keys:
    #         new_dict[k] = stitched_data[k]
    #     stitched_data = new_dict
    return stitched_data


def generate_criteria_details(client_id, psd, ped, payee_email, plan_id, criteria_id):
    plan_criteria_constraint = {"plan_id": plan_id, "criteria_id": criteria_id}
    data: dict = get_commission_data(
        client_id, psd, ped, payee_email, plan_criteria_constraint
    )

    criteria_data = data["plan_data"][plan_id]["criteria_datas"][criteria_id]
    criteria_type = criteria_data["criteria_type"]
    commission_data = criteria_data["commission_data"]
    stitched_data = criteria_data["stiched_data"]
    records, columns = [], []
    cols_system_name = criteria_data["criteria_column"]
    datasheet_id = criteria_data["criteria_conditions"]["datasheet_id"]

    if criteria_type in ["Quota", "CustomQuota", "CustomTeam"]:
        quota_data = data["plan_data"][plan_id]["quota_data"]
        criteria_data["crit_quota_data"] = (
            list(filter(lambda x: x and x["criteria_id"] == criteria_id, quota_data))
            if quota_data
            else None
        )

    # display column names
    sys_and_display_name = DatasheetVariableAccessor(
        client_id
    ).get_display_names_from_system_names(
        datasheet_id=datasheet_id, system_names=cols_system_name
    )
    cols_display_name_map = {}
    for element in sys_and_display_name:
        cols_display_name_map[element["system_name"]] = element["display_name"]
    for system_name in cols_system_name:
        if system_name in cols_display_name_map:
            columns.append(cols_display_name_map[system_name])
        else:
            columns.append(system_name)

    if criteria_data["is_line_item_level"]:
        for comm_data in commission_data:
            line_item_id = comm_data["line_item_id"]
            ds = {
                "commission": comm_data["amount"],
                "line_item_id": line_item_id,
            }

            if criteria_type in ["Tier", "CustomTier"]:
                ds["tier_id"] = comm_data["tier_id"]
                ds["tier_name"] = (
                    comm_data["tier_name"] if "tier_name" in comm_data else ""
                )

            if criteria_type in ["Quota", "CustomQuota", "CustomTeam"]:
                ds["tier_id"] = comm_data["tier_id"]
                ds["tier_name"] = (
                    comm_data["tier_name"] if "tier_name" in comm_data else ""
                )
                ds["quota_erosion"] = ""
                if criteria_data["crit_quota_data"]:
                    for rec in criteria_data["crit_quota_data"]:
                        if (
                            rec["line_item_id"] == comm_data["line_item_id"]
                            and rec["tier_id"] == comm_data["tier_id"]
                        ):
                            ds["quota_erosion"] = rec["quota_erosion"]

            if line_item_id in stitched_data and stitched_data[line_item_id]:
                line_item_data = stitched_data[line_item_id][0]
                ds = {**ds, **line_item_data}

            records.append(ds)
    else:
        # summation
        comm_data = commission_data[0]
        for line_item_id in comm_data["context_ids"]:
            ds = {
                "line_item_id": line_item_id,
                "commission": comm_data["amount"],
            }

            if criteria_type in ["Tier", "CustomTier"]:
                ds["tier_id"] = comm_data["tier_id"]
                ds["tier_name"] = (
                    comm_data["tier_name"] if "tier_name" in comm_data else ""
                )

            if criteria_type in ["Quota", "CustomQuota", "CustomTeam"]:
                ds["tier_id"] = comm_data["tier_id"]
                ds["tier_name"] = (
                    comm_data["tier_name"] if "tier_name" in comm_data else ""
                )
                ds["quota_erosion"] = ""
                if criteria_data["crit_quota_data"]:
                    for rec in criteria_data["crit_quota_data"]:
                        if (
                            rec["line_item_id"] == line_item_id
                            and rec["tier_id"] == comm_data["tier_id"]
                        ):
                            ds["quota_erosion"] = rec["quota_erosion"]

            if line_item_id in stitched_data and stitched_data[line_item_id]:
                line_item_data = stitched_data[line_item_id][0]
                ds = {**ds, **line_item_data}

            records.append(ds)

    return records, columns, cols_display_name_map


def generate_and_upload_criteria_details_csv(
    client_id, psd, ped, payee_email, plan_id, criteria_id, logger=None
):
    try:
        if logger:
            logger.info("CSV generation started")

        data, columns, cols_display_name_map = generate_criteria_details(
            client_id, psd, ped, payee_email, plan_id, criteria_id
        )
        data = pd.DataFrame(data)
        data.rename(columns=cols_display_name_map, inplace=True)
        csv_buffer = StringIO()
        data.to_csv(csv_buffer, columns=columns, float_format="%g", index=False)
        file = csv_buffer.getvalue()
        file_path = f"{client_id}/{psd}#:#{ped}#:#{payee_email}#:#{client_id}.csv"
        if logger:
            logger.info("Criteria details CSV generated")

        s3_uploader = S3Uploader()
        is_upload_complete = s3_uploader.upload_file(file, file_path)

        if is_upload_complete:
            file_url = s3_uploader.generate_presigned_url(file_path)
            if logger:
                logger.info("CSV file uploaded - file url: {}".format(file_url))
            return {"file_url": file_url}
        else:
            if logger:
                logger.error("CSV file upload failed")
            raise Exception("File upload failed")

    except Exception as e:
        logger.error("CSV data generation failed with exception: {}".format(str(e)))
        raise e


def get_commission_records(
    client_id,
    payee_email,
    psd,
    ped,
    locked_kd,
    logger,
    commission_line_item_ids=None,
    additional_filters=None,
    offset=None,
    limit=None,
    fields=None,
    order_by=None,
    order_desc=False,
):
    # if locked - get locked commission data
    if locked_kd:
        logger.info(
            f"Payee commission for given period is locked. Fetching locked-kd - {locked_kd} commission data"
        )
        all_commission_plan_data = CommissionAccessor(
            client_id
        ).get_payee_locked_kd_commission_data(
            payee_email=payee_email,
            locked_kd=locked_kd,
            psd=psd,
            ped=ped,
            line_item_id=commission_line_item_ids,
            additional_filters=additional_filters,
            offset=offset,
            limit=limit,
            fields=fields,
            order_by=order_by,
            order_desc=order_desc,
        )
    else:
        logger.info(
            "Payee commission for given period is not locked. Fetching active commission data"
        )
        all_commission_plan_data = CommissionAccessor(client_id).get_commission_data(
            psd=psd,
            ped=ped,
            line_item_id=commission_line_item_ids,
            payee_email=payee_email,
            additional_filters=additional_filters,
            offset=offset,
            limit=limit,
            fields=fields,
            order_by=order_by,
            order_desc=order_desc,
        )

    return all_commission_plan_data


def get_commission_records_secondary_kd(
    client_id,
    payee_email,
    psd,
    ped,
    locked_kd,
    logger,
    commission_line_item_ids=None,
    additional_filters=None,
):
    # if locked - get locked commission data secondary_kd
    if locked_kd:
        logger.info(
            f"Payee commission for given period is locked. Fetching locked-kd - {locked_kd} commission secondary_kd"
        )
        return CommissionAccessor(
            client_id
        ).get_payee_locked_kd_commission_secondary_kd(
            payee_email=payee_email,
            locked_kd=locked_kd,
            psd=psd,
            ped=ped,
            line_item_id=commission_line_item_ids,
            additional_filters=additional_filters,
        )
    else:
        logger.info(
            "Payee commission for given period is not locked. Fetching active commission secondary_kd"
        )
        return CommissionAccessor(client_id).get_commission_secondary_kd(
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            line_item_id=commission_line_item_ids,
            additional_filters=additional_filters,
        )


def get_quota_criteria_data(
    client_id,
    payee_email,
    psd,
    ped,
    plan_id,
    criteria_id,
    locked_kd,
    line_item_ids,
    logger,
    fields=None,
):
    additional_filters = {
        "commission_plan_id": plan_id,
        "criteria_id": criteria_id,
        **(
            {"line_item_id__in": line_item_ids}
            if isinstance(line_item_ids, list)
            else {}
        ),
    }
    if locked_kd:
        logger.info(f"Fetching quota criteria data for locked-kd: {locked_kd}.")
        quota_criteria_data = QuotaErosionAccessor(
            client_id
        ).get_payee_snapshot_quota_data(
            payee_email=payee_email,
            locked_kd=locked_kd,
            psd=psd,
            ped=ped,
            is_team=None,
            fields=fields,
            additional_filters=additional_filters,
        )
        logger.info(
            f"Fetched quota criteria data for locked-kd: {locked_kd}. {len(quota_criteria_data)} records"
        )
    else:
        logger.info("Fetching quota data.")
        quota_criteria_data = QuotaErosionAccessor(client_id).get_quota_data(
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            is_team=None,
            fields=fields,
            additional_filters=additional_filters,
        )
        logger.info(f"Fetched quota criteria data. {len(quota_criteria_data)} records")
    return quota_criteria_data


def fetch_datasheet_data_from_snowflake(
    client_id,
    databook_id,
    datasheet_id,
    knowledge_date,
    row_keys,
    fields,
    login_user_id,
    logger,
    limit=None,
    offset=None,
    as_dataframe=False,
    for_payout_snapshot=False,
):
    _should_use_multi_engine_stormbreaker = should_use_multi_engine_stormbreaker(
        client_id
    )
    if _should_use_multi_engine_stormbreaker:
        params = StormBreakerDSInitType(
            compute_strategy="duckdb_fallback_variant_snowflake",
            client_id=client_id,
            databook_id=UUID(str(databook_id)),
            datasheet_id=UUID(str(datasheet_id)),
            logged_in_user_email=login_user_id,
            knowledge_date=knowledge_date,
        )
        ds_storm_breaker = StormBreakerDSFactory.init(params)
    else:
        ds_storm_breaker = StormBreakerDSVariant(
            client_id,
            UUID(str(databook_id)),
            UUID(str(datasheet_id)),
            logged_in_user_email=login_user_id,
        )

    if for_payout_snapshot:
        ds_storm_breaker.set_fetch_null_as_null()
        # resetting apply datasheet permissions, as we are fetching data for payout snapshot
        if login_user_id is None:
            ds_storm_breaker.reset_apply_datasheet_permissions()

    if isinstance(fields, list) and len(fields) > 0:
        ds_storm_breaker.select(fields)
    ds_storm_breaker.set_knowledge_date(knowledge_date)
    if len(row_keys) < SNOWFLAKE_IN_CLAUSE_MAX_LIMIT:
        ds_storm_breaker.filter("row_key", "in", row_keys)
        logger.info(
            "BEGIN: Stormbreaker fetch datasheet data for given row keys for criteria."
        )
        try:
            if _should_use_multi_engine_stormbreaker and isinstance(
                ds_storm_breaker, StormBreakerDSMultiEngine
            ):
                data = ds_storm_breaker.fetch_datasheet_data_as_of_date(
                    knowledge_date=knowledge_date,
                    limit=limit,
                    offset=offset,
                    as_dataframe=as_dataframe,
                )
            elif not _should_use_multi_engine_stormbreaker and isinstance(
                ds_storm_breaker, StormBreakerDSVariant
            ):
                data = ds_storm_breaker.fetch(limit=limit, offset=offset, as_dataframe=as_dataframe)  # type: ignore
            else:
                raise TypeError("Invalid type of StormbreakerDS")
        except PermissionError as ex:
            logger.error(
                f"Permission error while fetching datasheet data for given row keys for criteria. {str(ex)}"
            )
            data = pd.DataFrame()
        except Exception as ex:
            logger.error(
                f"Error while fetching datasheet data for given row keys for criteria. {str(ex)}"
            )
            raise ex
        logger.info(
            f"END: Stormbreaker fetch datasheet data for given row keys for criteria. {len(data)} records"
        )
    else:
        list_of_row_keys = [
            row_keys[i : i + SNOWFLAKE_IN_CLAUSE_MAX_LIMIT]
            for i in range(0, len(row_keys), SNOWFLAKE_IN_CLAUSE_MAX_LIMIT)
        ]
        total_batches = len(list_of_row_keys)
        logger.info(f"Fetching data from Stormbreaker in {total_batches} batches")
        res_data = []
        for curr_batch in range(total_batches):
            ds_storm_breaker.filter("row_key", "in", list_of_row_keys[curr_batch])
            logger.info(
                f"{curr_batch+1}/{total_batches} : BEGIN: Stormbreaker fetch datasheet data for given row keys for criteria."
            )
            try:
                if _should_use_multi_engine_stormbreaker and isinstance(
                    ds_storm_breaker, StormBreakerDSMultiEngine
                ):
                    data = ds_storm_breaker.fetch_datasheet_data_as_of_date(
                        knowledge_date=knowledge_date,
                        limit=limit,
                        offset=offset,
                        as_dataframe=as_dataframe,
                    )
                elif not _should_use_multi_engine_stormbreaker and isinstance(
                    ds_storm_breaker, StormBreakerDSVariant
                ):
                    data = ds_storm_breaker.fetch(
                        limit=limit, offset=offset, as_dataframe=as_dataframe
                    )
                else:
                    raise TypeError("Invalid type of StormbreakerDS")

                if as_dataframe:
                    res_data.append(data)
                else:
                    res_data.extend(data)
                ds_storm_breaker.reset_last_filter()
            except PermissionError as ex:
                logger.error(
                    f"{curr_batch+1}/{total_batches} : Permission error while fetching datasheet data for given row keys for criteria. {str(ex)}"
                )
                data = pd.DataFrame()
            except Exception as ex:
                logger.error(
                    f"{curr_batch+1}/{total_batches} : Error while fetching datasheet data for given row keys for criteria. {str(ex)}"
                )
                raise ex
            logger.info(
                f"{curr_batch+1}/{total_batches} : END: Stormbreaker fetch datasheet data for given row keys for criteria. {len(data)} records"
            )
        data = (
            pd.concat(res_data, ignore_index=True)
            if as_dataframe and res_data
            else res_data
        )
    return data


def get_sorted_earned_commissions(
    records: list[dict],
    orderby_fields: list[SortInfoType],
    limit: int | None = None,
    offset: int | None = None,
):
    """
    Sort earned commissions given the sort fields and apply limit and offset.
    It specially handles the quota erosion column and remove the temporary quota erosion
    value column after sorting.
    """
    earned_commissions_df = (
        SortUtility(records, orderby_fields)
        .sort_records(
            column_replacements=[{"column": "quota_erosion", "callback": None}]
        )
        .apply_limit_offset(
            limit,
            offset * limit if limit is not None and offset is not None else None,
        )
        .as_dataframe()
    )
    earned_commissions_df.drop(
        columns=["quota_erosion__temp"], inplace=True, errors="ignore"
    )
    earned_commissions_df = remove_nan_nat(earned_commissions_df)
    return earned_commissions_df.to_dict("records")


def earned_commission_records_for_line_item_level_criteria(
    client_id,
    psd,
    ped,
    payee_email,
    plan_id,
    criteria_id,
    criteria_data,
    criteria_columns,
    fx_rate_convertor,
    comm_locked_kd,
    tier_id_to_name_map,
    offset,
    limit,
    return_records_count_only,
    login_user_id,
    logger,
    orderby_fields: list[SortInfoType] | None = None,
):
    res = {
        "earned_commission": [],
    }
    earned_commissions = []

    # When sorting is applied, the limit_value and offset_value will be None making the
    # get commission records to return all values.
    limit_value, offset_value = None, None
    if limit is not None and offset is not None and not orderby_fields:
        limit_value, offset_value = limit, offset

    commission_grouped_by_line_item = {}
    quota_erosion_grouped_by_line_item = {}
    quota_category_name = None
    logger.info("Fetching commission records.")
    commission_data = get_commission_records(
        client_id=client_id,
        psd=psd,
        ped=ped,
        payee_email=payee_email,
        locked_kd=comm_locked_kd,
        logger=logger,
        additional_filters={
            "commission_plan_id": plan_id,
            "criteria_id": criteria_id,
        },
        offset=offset_value,
        limit=limit_value,
        order_by="temporal_id",
        order_desc=True,
        fields=["line_item_id", "tier_id", "amount", "secondary_kd"],
    )
    logger.info(f"Fetched commission records. {len(commission_data)} records")
    commission_by_line_item_tier_id = group_commissions_by_line_item_tier_id(
        commission_data, fx_rate_convertor
    )
    commission_line_item_ids = [
        ele[0] for ele in commission_by_line_item_tier_id.keys()
    ]

    commission_grouped_by_line_item = commission_by_line_item_tier_id

    # return total count if requested
    commission_line_item_ids_count = len(commission_line_item_ids)
    if return_records_count_only:
        res["count"] = commission_line_item_ids_count
        return res, None

    # return if commission_line_item_ids is empty
    if commission_line_item_ids_count == 0:
        return res, None

    knowledge_date = next(iter(commission_data or []), {}).get(
        "secondary_kd"
    )  # first record in commission

    # get quota criteria data
    quota_criteria_data = get_quota_criteria_data(
        client_id=client_id,
        payee_email=payee_email,
        psd=psd,
        ped=ped,
        plan_id=plan_id,
        criteria_id=criteria_id,
        locked_kd=comm_locked_kd,
        line_item_ids=commission_line_item_ids,
        fields=["line_item_id", "tier_id", "quota_erosion", "quota_category_name"],
        logger=logger,
    )

    logger.info("Grouping quota erosion data at line item level.")
    for record in quota_criteria_data:
        line_item_id = record.get("line_item_id")
        tier_id = record.get("tier_id") if "tier_id" in record else None
        quota_erosion = record.get("quota_erosion", 0)
        quota_category_name = record.get("quota_category_name")
        if (line_item_id, tier_id) not in quota_erosion_grouped_by_line_item:
            quota_erosion_grouped_by_line_item[(line_item_id, tier_id)] = 0
        quota_erosion_grouped_by_line_item[(line_item_id, tier_id)] += quota_erosion
    logger.info("Grouped quota erosion data at line item level.")

    # fetch datasheet data from snowflake; only for fields which are to be used at FE
    databook_id = criteria_data.get("databook_id")
    datasheet_id = criteria_data.get("datasheet_id")
    ds_variables = DatasheetVariableAccessor(client_id).get_datasheet_vars_system_name(
        datasheet_id, knowledge_date
    )
    ds_fields_to_fetch = list(set(criteria_columns).intersection(set(ds_variables)))
    ds_fields_to_fetch.append("row_key")

    adjusted_data = fetch_datasheet_data_from_snowflake(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        knowledge_date=knowledge_date,
        row_keys=commission_line_item_ids,
        fields=ds_fields_to_fetch,
        login_user_id=login_user_id,
        logger=logger,
    )

    adjusted_data_map = {record["row_key"]: record for record in adjusted_data}
    if adjusted_data_map:
        quota_currency = (
            QuotaAccessor(client_id).get_quota_category_currency_type(
                quota_category_name
            )
            if quota_category_name
            else None
        )
        quota_country_symbol = (
            CountriesAccessor(client_id).get_currency_symbol(
                quota_currency.quota_currency_type
            )
            if quota_currency
            else None
        )
        for key, value in commission_grouped_by_line_item.items():
            if key[0] in adjusted_data_map:
                deep_copied_record = copy.deepcopy(
                    adjusted_data_map[key[0]]
                )  # Changing record without deep copy will append only last record here
                deep_copied_record["commission"] = float(value)
                if key[1] in tier_id_to_name_map:
                    deep_copied_record["tierName"] = tier_id_to_name_map[key[1]]
                if key in quota_erosion_grouped_by_line_item:
                    curr_symbol = (
                        quota_country_symbol.currency_symbol
                        if quota_country_symbol
                        else None
                    )
                    quota_erosion_value = float(quota_erosion_grouped_by_line_item[key])
                    quota_erosion_str = (
                        f"{curr_symbol} {quota_erosion_value}"
                        if curr_symbol is not None
                        else str(quota_erosion_value)
                    )
                    deep_copied_record["quota_erosion"] = quota_erosion_str
                    # Temporarily adding quota erosion value to enable proper sorting
                    # over quota erosion, this field will be removed after sorting.
                    if orderby_fields:
                        deep_copied_record["quota_erosion__temp"] = quota_erosion_value

                earned_commissions.append(deep_copied_record)

    if earned_commissions and orderby_fields:
        earned_commissions = get_sorted_earned_commissions(
            earned_commissions, orderby_fields, limit, offset
        )
        logger.info(
            f"Sorted the dataframe and applied limit and offset. Sort by columns: {orderby_fields}"
        )

    res["earned_commission"] = earned_commissions
    return res, knowledge_date


def earned_commission_records_for_summation_level_criteria(
    client_id,
    psd,
    ped,
    payee_email,
    plan_id,
    criteria_id,
    criteria_data,
    criteria_columns,
    fx_rate_convertor,
    comm_locked_kd,
    tier_id_to_name_map,
    offset,
    limit,
    return_records_count_only,
    login_user_id,
    logger,
    orderby_fields: list[SortInfoType] | None = None,
):
    res = {
        "earned_commission": [],
    }
    earned_commissions = []
    commission_grouped_by_line_item = {}
    quota_erosion_grouped_by_line_item = {}
    quota_category_name = None
    comm_locked_kd = comm_locked_kd or timezone.now()
    commission_recs = CommissionAccessor(
        client_id
    ).get_commission_for_plan_and_summation_criteria(
        payee_email,
        psd,
        ped,
        plan_id,
        criteria_id,
        comm_locked_kd,
        fields=["context_ids", "amount", "secondary_kd", "tier_id", "line_item_id"],
    )
    commission_line_item_ids = []
    amount = 0
    knowledge_date = None
    tier_id = None
    # when split_summation_to_li flag is enabled, commission records will be created for each line item, line_item_id will be present in commission record and context_ids will be empty
    # when split_summation_to_li flag is disabled, single commission record will be created, line_item_id will be empty and context_ids will be present
    if commission_recs:
        for record in commission_recs:
            if record["context_ids"]:
                commission_line_item_ids.extend(record["context_ids"])
            elif record["line_item_id"]:
                commission_line_item_ids.append(record["line_item_id"])
            amount += record["amount"]
        knowledge_date = commission_recs[0]["secondary_kd"]
        tier_id = commission_recs[0]["tier_id"]

    if fx_rate_convertor is not None:
        amount = float(fx_rate_convertor.change_to_payee_currency(amount))

    # return total count if requested
    commission_line_item_ids_count = len(commission_line_item_ids)
    if return_records_count_only:
        res["count"] = commission_line_item_ids_count
        return res, knowledge_date

    # return if commission_line_item_ids is empty
    if commission_line_item_ids_count == 0:
        return res, knowledge_date

    # paginating only when orderby fields are empty. Otherwise pagination will
    # happen after sorting.
    if offset is not None and limit is not None and not orderby_fields:
        commission_line_item_ids = commission_line_item_ids[
            offset * limit : (offset * limit) + limit
        ]

    logger.info("Grouping commission and quota erosion at line item level.")
    for line_item in commission_line_item_ids:
        commission_grouped_by_line_item[(line_item, tier_id)] = amount

    # get quota criteria data
    quota_criteria_data = get_quota_criteria_data(
        client_id=client_id,
        payee_email=payee_email,
        psd=psd,
        ped=ped,
        plan_id=plan_id,
        criteria_id=criteria_id,
        locked_kd=comm_locked_kd,
        line_item_ids=None,
        fields=["quota_erosion", "tier_id", "quota_category_name"],
        logger=logger,
    )

    if quota_criteria_data:
        quota_erosion = 0
        for record in quota_criteria_data:
            quota_erosion += record.get("quota_erosion", 0)
        quota_category_name = quota_criteria_data[0].get("quota_category_name")
        quota_tier_id = quota_criteria_data[0].get("tier_id")
        for line_item_id in commission_line_item_ids:
            if (line_item_id, quota_tier_id) not in quota_erosion_grouped_by_line_item:
                quota_erosion_grouped_by_line_item[(line_item_id, quota_tier_id)] = 0
            quota_erosion_grouped_by_line_item[(line_item_id, quota_tier_id)] = (
                quota_erosion
            )
    logger.info("Grouped commission and quota erosion at line item level.")

    # fetch datasheet data from snowflake; only for fields which are to be used at FE
    databook_id = criteria_data.get("databook_id")
    datasheet_id = criteria_data.get("datasheet_id")
    ds_variables = DatasheetVariableAccessor(client_id).get_datasheet_vars_system_name(
        datasheet_id, knowledge_date
    )
    ds_fields_to_fetch = list(set(criteria_columns).intersection(set(ds_variables)))
    ds_fields_to_fetch.append("row_key")

    adjusted_data = fetch_datasheet_data_from_snowflake(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        knowledge_date=knowledge_date,
        row_keys=commission_line_item_ids,
        fields=ds_fields_to_fetch,
        login_user_id=login_user_id,
        logger=logger,
    )

    adjusted_data_map = {record["row_key"]: record for record in adjusted_data}
    if adjusted_data_map:
        quota_currency = (
            QuotaAccessor(client_id).get_quota_category_currency_type(
                quota_category_name
            )
            if quota_category_name
            else None
        )
        quota_country_symbol = (
            CountriesAccessor(client_id).get_currency_symbol(
                quota_currency.quota_currency_type
            )
            if quota_currency
            else None
        )
        for key, value in commission_grouped_by_line_item.items():
            if key[0] in adjusted_data_map:
                deep_copied_record = copy.deepcopy(
                    adjusted_data_map[key[0]]
                )  # Changing record without deep copy will append only last record here
                deep_copied_record["commission"] = float(value)
                if key[1] in tier_id_to_name_map:
                    deep_copied_record["tierName"] = tier_id_to_name_map[key[1]]
                if key in quota_erosion_grouped_by_line_item:
                    curr_symbol = (
                        quota_country_symbol.currency_symbol
                        if quota_country_symbol
                        else None
                    )
                    quota_erosion_value = float(quota_erosion_grouped_by_line_item[key])
                    quota_erosion_str = (
                        f"{curr_symbol} {quota_erosion_value}"
                        if curr_symbol is not None
                        else str(quota_erosion_value)
                    )
                    deep_copied_record["quota_erosion"] = quota_erosion_str
                    # Temporarily adding quota erosion value to enable proper sorting
                    # over quota erosion, this field will be removed after sorting.
                    if orderby_fields:
                        deep_copied_record["quota_erosion__temp"] = quota_erosion_value

                earned_commissions.append(deep_copied_record)

    if earned_commissions and orderby_fields:
        earned_commissions = get_sorted_earned_commissions(
            earned_commissions, orderby_fields, limit, offset
        )
        logger.info(
            f"Sorted the dataframe and applied limit and offset. Sort by columns: {orderby_fields}"
        )

    res["earned_commission"] = earned_commissions

    return res, knowledge_date


def get_earned_commission_for_criteria(
    client_id,
    psd,
    ped,
    payee_email,
    plan_id,
    criteria_id,
    fx_rate_convertor,
    login_user_id,
    logger,
    offset=None,
    limit=None,
    return_records_count_only=False,
    orderby_fields: list[SortInfoType] | None = None,
):
    """returns the commission and quota earned in a period"""

    # get criteria details
    criteria = PlanCriteriaAccessor(client_id).get_criteria(
        criteria_id=criteria_id,
        fields=["criteria_data", "criteria_column", "criteria_config"],
    )
    criteria_columns = update_hyperlink_columns_in_criteria_columns(
        client_id, criteria_id, plan_id, criteria["criteria_column"]
    )
    criteria_data = criteria["criteria_data"]
    criteria_config = criteria["criteria_config"]
    trace_enabled = criteria_config.get("trace_enabled", False)
    tier_id_to_name_map = {}
    if criteria_data["type"] == "Tier" or criteria_data["type"] == "Quota":
        for idx in range(len(criteria_data["part2"])):
            if (
                "tier_name" in criteria_data["part2"][idx]
                and criteria_data["part2"][idx]["tier_name"]
            ):
                tier_id_to_name_map[str(idx)] = criteria_data["part2"][idx]["tier_name"]
            else:
                tier_id_to_name_map[str(idx)] = "Tier " + str(idx)

    logger.info("Fetching commission records with freeze aware.")

    # get commission lock
    comm_locked_kd = CommissionLockAccessor(
        client_id
    ).get_locked_kd_for_locked_comm_in_period(psd=psd, ped=ped, payee_email=payee_email)
    logger.info("Locked Kd for payee {0}: {1}".format(payee_email, comm_locked_kd))
    if criteria_data.get("is_line_item_level"):
        res, knowledge_date = earned_commission_records_for_line_item_level_criteria(
            client_id=client_id,
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            plan_id=plan_id,
            criteria_id=criteria_id,
            criteria_data=criteria_data,
            criteria_columns=criteria_columns,
            fx_rate_convertor=fx_rate_convertor,
            comm_locked_kd=comm_locked_kd,
            tier_id_to_name_map=tier_id_to_name_map,
            offset=offset,
            limit=limit,
            return_records_count_only=return_records_count_only,
            login_user_id=login_user_id,
            logger=logger,
            orderby_fields=orderby_fields,
        )
    else:
        res, knowledge_date = earned_commission_records_for_summation_level_criteria(
            client_id=client_id,
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            plan_id=plan_id,
            criteria_id=criteria_id,
            criteria_data=criteria_data,
            criteria_columns=criteria_columns,
            fx_rate_convertor=fx_rate_convertor,
            comm_locked_kd=comm_locked_kd,
            tier_id_to_name_map=tier_id_to_name_map,
            offset=offset,
            limit=limit,
            return_records_count_only=return_records_count_only,
            login_user_id=login_user_id,
            logger=logger,
            orderby_fields=orderby_fields,
        )

    if return_records_count_only:
        return res["count"]

    logger.info("Created earned commissions records.")

    datasheet_id = criteria_data.get("datasheet_id")
    valid_criteria_columns = criteria_columns
    if datasheet_id and knowledge_date:
        valid_criteria_columns = get_valid_criteria_columns(
            client_id, criteria_columns, datasheet_id, knowledge_date
        )

    # removing tier column as tier level info won't be there
    # if "tierName" in criteria.criteria_column:
    #     columns.remove("tierName")
    return {
        "records": res["earned_commission"],
        "columns": valid_criteria_columns,
        "is_line_item_level": criteria_data.get("is_line_item_level"),
        "trace_enabled": trace_enabled,
    }


def get_payout_details_for_criteria(
    client_id,
    psd,
    ped,
    payee_email,
    plan_id,
    criteria_id,
    requested_data,
    login_user_id,
    currency=None,
    offset=None,
    limit=None,
    comm_ped=None,
    is_zero_comm_line_item_included=False,
    orderby_fields: list[SortInfoType] | None = None,
):
    """
    This function returns the line item level details based on the commission type.
    """
    from spm.services.statements_data_services import (
        get_current_pd_commission_data_from_snowflake,
        get_deferred_comm_data_from_snowflake,
        get_earned_commission_data_from_snowflake,
        get_new_deferred_comm_data_from_snowflake,
        get_prev_deferred_comm_data_from_snowflake,
    )

    period_start_date = psd
    period_end_date = ped
    if isinstance(psd, str):
        period_start_date = make_aware(start_of_day(parse(psd, dayfirst=True)))
    if isinstance(ped, str):
        period_end_date = make_aware(end_of_day(parse(ped, dayfirst=True)))
    if isinstance(comm_ped, str):
        comm_ped = make_aware(end_of_day(parse(comm_ped, dayfirst=True)))

    log_context = {
        "client_id": client_id,
        "period_start_date": period_start_date,
        "period_end_date": period_end_date,
        "payee_email": payee_email,
        "plan_id": plan_id,
        "criteria_id": criteria_id,
    }
    logger = iputils.LogWithContext(log_context)

    logger.info(
        f"Getting payout details for criteria of requested data - {requested_data}"
    )
    client = get_client(client_id)
    base_currency = client.base_currency
    payee_details = get_payee_payrolls_secondary_kd_aware(
        client_id, period_end_date, [payee_email], True
    )

    payee_currency = (
        currency if currency else payee_details["payroll_data"][0]["pay_currency"]
    )
    if requested_data.lower() == CommissionViewType.PREV_DEFERRED_COMMISSIONS.value:
        payee_currency = payee_details["payroll_data"][0]["pay_currency"]

    fx_rate_convertor = None
    if base_currency != payee_currency:
        fx_rate_convertor = FXRate(
            client_id,
            period_end_date,
            payee_currency,
            payee_details["sec_kd_data"][payee_email],
        )

    read_data_from_snowflake = get_snapshot_data_for_statements(client_id)
    # Backend Only Feature - Avoid opportunities cleared in the current period from deferred section (with $0 value)
    remove_zero_value_deferred_commissions = get_remove_zero_value_deferred(client_id)

    if requested_data.lower() == CommissionViewType.EARNED_COMMISSIONS.value:
        logger.info("Fetching earned commission for criteria.")
        if read_data_from_snowflake:
            earned_commissions = get_earned_commission_data_from_snowflake(
                client_id=client_id,
                psd=period_start_date,
                ped=period_end_date,
                payee_email=payee_email,
                plan_id=plan_id,
                criteria_id=criteria_id,
                fx_rate_convertor=fx_rate_convertor,
                offset=offset,
                limit=limit,
                logger=logger,
                orderby_fields=orderby_fields,
                login_user_id=login_user_id,
            )
        else:
            earned_commissions = get_earned_commission_for_criteria(
                client_id=client_id,
                psd=period_start_date,
                ped=period_end_date,
                payee_email=payee_email,
                plan_id=plan_id,
                criteria_id=criteria_id,
                fx_rate_convertor=fx_rate_convertor,
                offset=offset,
                limit=limit,
                login_user_id=login_user_id,
                logger=logger,
                orderby_fields=orderby_fields,
            )
            logger.info(
                f"Fetched earned commission for criteria. {len(earned_commissions)} records"
            )
        return earned_commissions

    if requested_data.lower() == CommissionViewType.DEFERRED_COMMISSIONS.value:
        logger.info("Fetching deferred commission for criteria.")
        if remove_zero_value_deferred_commissions:
            deferred_commissions_func = (
                get_new_deferred_comm_data_from_snowflake
                if read_data_from_snowflake
                else get_new_deferred_commission_for_criteria
            )
        else:
            deferred_commissions_func = (
                get_deferred_comm_data_from_snowflake
                if read_data_from_snowflake
                else get_deferred_commission_for_criteria
            )
        deferred_commissions = deferred_commissions_func(
            client_id=client_id,
            ped=ped,
            payee_email=payee_email,
            plan_id=plan_id,
            criteria_id=criteria_id,
            fx_rate_convertor=fx_rate_convertor,
            offset=offset,
            limit=limit,
            is_zero_comm_line_item_included=is_zero_comm_line_item_included,
            orderby_fields=orderby_fields,
            logger=logger,
            login_user_id=login_user_id,
            **({"psd": psd} if not read_data_from_snowflake else {}),
        )
        logger.info(
            f"Fetched deferred commission for criteria. {len(deferred_commissions)} records"
        )
        return deferred_commissions

    if requested_data.lower() == CommissionViewType.CURRENT_PERIOD_PAYOUT.value:
        logger.info("Fetching settlement commission for criteria.")
        if read_data_from_snowflake:
            settlement_comissions = get_current_pd_commission_data_from_snowflake(
                client_id=client_id,
                ped=ped,
                payee_email=payee_email,
                plan_id=plan_id,
                criteria_id=criteria_id,
                fx_rate_convertor=fx_rate_convertor,
                offset=offset,
                limit=limit,
                is_zero_comm_line_item_included=is_zero_comm_line_item_included,
                logger=logger,
                orderby_fields=orderby_fields,
                login_user_id=login_user_id,
            )
        else:
            settlement_comissions = get_current_period_commission_for_criteria(
                client_id=client_id,
                psd=psd,
                ped=ped,
                payee_email=payee_email,
                plan_id=plan_id,
                criteria_id=criteria_id,
                fx_rate_convertor=fx_rate_convertor,
                login_user_id=login_user_id,
                offset=offset,
                limit=limit,
                is_zero_comm_line_item_included=is_zero_comm_line_item_included,
                logger=logger,
                orderby_fields=orderby_fields,
            )
        logger.info(
            f"Fetched settlement comissions for criteria. {len(settlement_comissions)} records"
        )
        return settlement_comissions

    if requested_data.lower() == CommissionViewType.PREV_DEFERRED_COMMISSIONS.value:
        logger.info("Fetching previous deferred commission for criteria.")
        if read_data_from_snowflake:
            prev_deferred_commission = get_prev_deferred_comm_data_from_snowflake(
                client_id=client_id,
                comm_ped=comm_ped,
                psd=psd,
                ped=ped,
                payee_email=payee_email,
                plan_id=plan_id,
                criteria_id=criteria_id,
                fx_rate_convertor=fx_rate_convertor,
                offset=offset,
                limit=limit,
                is_zero_comm_line_item_included=is_zero_comm_line_item_included,
                logger=logger,
                base_currency=True if currency == base_currency else False,
                orderby_fields=orderby_fields,
                login_user_id=login_user_id,
            )
        else:
            prev_deferred_commission = get_prev_deferred_commission_for_criteria(
                client_id=client_id,
                comm_ped=comm_ped,
                psd=psd,
                ped=ped,
                payee_email=payee_email,
                plan_id=plan_id,
                criteria_id=criteria_id,
                fx_rate_convertor=fx_rate_convertor,
                login_user_id=login_user_id,
                offset=offset,
                limit=limit,
                is_zero_comm_line_item_included=is_zero_comm_line_item_included,
                logger=logger,
                base_currency=True if currency == base_currency else False,
                orderby_fields=orderby_fields,
            )
        logger.info(
            f"Fetched previous deferred commission for criteria. {len(prev_deferred_commission)} records"
        )
        return prev_deferred_commission


def get_payout_records_count_for_criteria(
    client_id,
    psd,
    ped,
    payee_email,
    plan_id,
    criteria_id,
    requested_data,
    login_user_id,
    comm_ped=None,
):
    """
    This function returns the payout records count based on the commission type.
    """
    period_start_date = psd
    period_end_date = ped
    if isinstance(psd, str):
        period_start_date = make_aware(start_of_day(parse(psd, dayfirst=True)))
    if isinstance(ped, str):
        period_end_date = make_aware(end_of_day(parse(ped, dayfirst=True)))
    if isinstance(comm_ped, str):
        comm_ped = make_aware(end_of_day(parse(comm_ped, dayfirst=True)))

    log_context = {
        "client_id": client_id,
        "period_start_date": period_start_date,
        "period_end_date": period_end_date,
        "payee_email": payee_email,
        "plan_id": plan_id,
        "criteria_id": criteria_id,
    }

    logger = iputils.LogWithContext(log_context)

    logger.info(
        f"Getting payout records count for criteria of requested data - {requested_data}"
    )
    fx_rate_convertor = None

    if requested_data.lower() == CommissionViewType.EARNED_COMMISSIONS.value:
        logger.info("Fetching earned commission records count for criteria.")
        earned_commissions_count = get_earned_commission_for_criteria(
            client_id=client_id,
            psd=period_start_date,
            ped=period_end_date,
            payee_email=payee_email,
            plan_id=plan_id,
            criteria_id=criteria_id,
            fx_rate_convertor=fx_rate_convertor,
            login_user_id=login_user_id,
            logger=logger,
            return_records_count_only=True,
        )
        logger.info(
            f"Fetched earned commission records count for criteria. {earned_commissions_count} records"
        )
        return earned_commissions_count

    if requested_data.lower() == CommissionViewType.DEFERRED_COMMISSIONS.value:
        logger.info("Fetching deferred commission records count for criteria.")
        deferred_commissions_count = get_deferred_commission_for_criteria(
            client_id=client_id,
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            plan_id=plan_id,
            criteria_id=criteria_id,
            fx_rate_convertor=fx_rate_convertor,
            login_user_id=login_user_id,
            logger=logger,
            return_records_count_only=True,
        )
        logger.info(
            f"Fetched deferred commission records count for criteria. {deferred_commissions_count} records"
        )
        return deferred_commissions_count

    if requested_data.lower() == CommissionViewType.PREV_DEFERRED_COMMISSIONS.value:
        logger.info("Fetching previous deferred commission records count for criteria.")
        prev_deferred_commission_count = get_prev_deferred_commission_for_criteria(
            client_id=client_id,
            comm_ped=comm_ped,
            psd=psd,
            ped=ped,
            payee_email=payee_email,
            plan_id=plan_id,
            criteria_id=criteria_id,
            fx_rate_convertor=fx_rate_convertor,
            login_user_id=login_user_id,
            logger=logger,
            return_records_count_only=True,
        )
        logger.info(
            f"Fetched previous deferred commission records count for criteria. {prev_deferred_commission_count} records"
        )
        return prev_deferred_commission_count

    if requested_data.lower() == CommissionViewType.CURRENT_PERIOD_PAYOUT.value:
        logger.info(
            "Fetching current period payout commission records count for criteria."
        )
        current_period_commission_for_criteria = (
            get_current_period_commission_for_criteria(
                client_id=client_id,
                psd=psd,
                ped=ped,
                payee_email=payee_email,
                plan_id=plan_id,
                criteria_id=criteria_id,
                fx_rate_convertor=fx_rate_convertor,
                login_user_id=login_user_id,
                logger=logger,
                return_records_count_only=True,
            )
        )
        logger.info(
            f"Fetched current period payout commission records count for criteria. {current_period_commission_for_criteria} records"
        )
        return current_period_commission_for_criteria


def get_commission_records_payees(client_id, ped, payee_psd_map):
    locked_kd_map = {}
    if payee_psd_map:
        locked_kd_map = CommissionLockAccessor(
            client_id
        ).get_locked_kd_for_locked_comm_in_period_payees(
            ped=ped, payee_psd_map=payee_psd_map
        )
    for payee in payee_psd_map:
        if payee not in locked_kd_map:
            locked_kd_map[payee] = None

    result = []
    if len(payee_psd_map) == 1:
        payee = list(payee_psd_map.keys())[0]
        result = CommissionAccessor(client_id).get_payee_commission_data(
            payee_psd_map[payee],
            ped,
            payee,
            (
                locked_kd_map[payee]["locked_knowledge_date"]
                if locked_kd_map[payee]
                else None
            ),
        )
    elif payee_psd_map:
        result = CommissionAccessor(client_id).get_payees_commission_data(
            ped, locked_kd_map, payee_psd_map
        )

    return {"curr_period": result, "locked_kd_map": locked_kd_map}


def secondary_kd_map_for_payees(client_id, ped, payee_emails):
    sec_kd_entries = CommissionSecondaryKdAccessor(
        client_id
    ).get_sec_kd_for_period_payees(ped, payee_emails)
    sec_kd_map = {}
    for entry in sec_kd_entries:
        sec_kd_map[entry["payee_email_id"]] = entry["sec_kd"]
    for payee in set(payee_emails) - set(sec_kd_map.keys()):
        sec_kd_map[payee] = None
    return sec_kd_map


def get_payee_payrolls_secondary_kd_aware(
    client_id, ped, payee_emails, sec_kd_details=False, as_dicts=True
):
    logger = iputils.LogWithContext(
        {
            "client_id": client_id,
            "period_end_date": ped,
            "payee_emails": payee_emails,
        }
    )
    sec_kd_map = secondary_kd_map_for_payees(client_id, ped, payee_emails)
    logger.info("Got secondary kd map")

    payroll_data = EmployeePayrollAccessor(
        client_id
    ).get_payroll_details_based_on_sec_kd(ped, payee_emails)
    logger.info("Got employee payroll")

    payroll_details = []
    payroll_payees = set()
    for record in payroll_data:
        payroll_payees.add(record.employee_email_id)
        payroll_details.append(record.__dict__)
    missed_out_payees = list(set(payee_emails) - payroll_payees)
    if missed_out_payees:
        logger.info("Missed out payees - %s" % missed_out_payees)
        latest_entries = EmployeePayrollAccessor(
            client_id
        ).get_latest_employee_payroll_list(missed_out_payees, as_dict=False)
        payroll_data.extend(latest_entries)
        for record in latest_entries:
            payroll_details.append(record.__dict__)
    if not as_dicts:
        payroll_details = payroll_data
    if sec_kd_details:
        return {"payroll_data": payroll_details, "sec_kd_data": sec_kd_map}
    return payroll_details


def get_payee_periods_secondary_kd_map(client_id, peds, payee_emails, logger):
    sec_kd_entries = CommissionSecondaryKdAccessor(
        client_id
    ).get_sec_kd_for_periods_payees(peds, payee_emails)
    logger.info("Got secondary kd map for periods -%s" % peds)

    sec_kd_map = {}
    for record in sec_kd_entries:
        ped = record["period_end_date"].strftime("%Y-%m-%d")
        payee_email = record["payee_email_id"]
        sec_kd = record["sec_kd"]
        if ped not in sec_kd_map:
            sec_kd_map[ped] = {}
        sec_kd_map[ped][payee_email] = sec_kd
    return sec_kd_map


def get_payee_currency_map(client_id, payee_emails, ped, as_dicts=True):
    logger = iputils.LogWithContext(
        {
            "client_id": client_id,
            "period_end_date": ped,
            "payee_emails": payee_emails,
        }
    )
    logger.info("BEGIN: Fetching payee currency map")
    esd = make_aware(end_of_day(dateutil.parser.parse(ped, dayfirst=True)))
    payroll_data = EmployeePayrollAccessor(
        client_id
    ).get_payroll_details_based_on_sec_kd(esd, payee_emails)
    logger.info("Got employee payroll")

    payroll_details = []
    payroll_payees = set()
    for record in payroll_data:
        payroll_payees.add(record.employee_email_id)
        payroll_details.append(record.__dict__)
    missed_out_payees = list(set(payee_emails) - payroll_payees)
    if missed_out_payees:
        logger.info("Missed out payees - %s" % missed_out_payees)
        latest_entries = EmployeePayrollAccessor(
            client_id
        ).get_latest_employee_payroll_list(missed_out_payees, as_dict=False)
        payroll_data.extend(latest_entries)
        for record in latest_entries:
            payroll_details.append(record.__dict__)
    if not as_dicts:
        payroll_details = payroll_data
    base_currency = get_base_currency(client_id)

    result = {}
    for data in payroll_details:
        result[data["employee_email_id"]] = {
            "payee_currency": data.get("pay_currency"),
            "base_currency": base_currency,
        }
    logger.info("END: Fetching payee currency map")

    return result


def get_commission_settlement_data(params):
    """
    Get commission settlement data for both locked and non-locked rows.

    Parameters:
    - params (dict): A dictionary containing the following keys:
        - client_id (int): The ID of the client.
        - psd (datetime): The period start date.
        - ped (datetime): The period end date.
        - payee_email (str): The email of the payee.
        - plan_id (str): The ID of the commission plan.
        - criteria_id (str): The ID of the criteria.
        - locked_kd (optional): The locked knowledge date, if applicable.
        - settlement_locked_kd (optional): The settlement locked knowledge date, if applicable.
        - limit (optional): The maximum number of records to return.
        - offset (optional): The number of records to skip before starting to return records.
    """

    commission_where_clauses = [
        'c."client_id" = %(client_id)s',
        'NOT c."is_deleted"',
        'c."payee_email_id" = %(payee_email)s',
        'c."period_end_date" = %(ped)s',
        'c."period_start_date" = %(psd)s',
        'c."commission_plan_id" = %(plan_id)s',
        'c."criteria_id" = %(criteria_id)s',
    ]

    settlement_where_clauses = [
        's."client_id" = %(client_id)s',
        's."payee_email_id" = %(payee_email)s',
        's."plan_id" = %(plan_id)s',
        's."criteria_id" = %(criteria_id)s',
        's."comm_period_start_date" = %(psd)s',
        's."comm_period_end_date" = %(ped)s',
        's."period_start_date" = %(psd)s',
        's."period_end_date" = %(ped)s',
        's."settlement_flag" = TRUE',
        'NOT s."is_deleted"',
    ]

    commission_kd_clause = (
        """
        c."knowledge_begin_date" <= %(locked_kd)s AND (
            c."knowledge_end_date" > %(locked_kd)s OR c."knowledge_end_date" IS NULL
        )
        """
        if params.get("locked_kd")
        else 'c."knowledge_end_date" IS NULL'
    )
    commission_where_clauses.append(commission_kd_clause)

    settlement_kd_clause = (
        """
        s."knowledge_begin_date" <= %(settlement_locked_kd)s AND (
            s."knowledge_end_date" >= %(settlement_locked_kd)s OR s."knowledge_end_date" IS NULL
        )
        """
        if params.get("settlement_locked_kd")
        else 's."knowledge_end_date" IS NULL'
    )
    settlement_where_clauses.append(settlement_kd_clause)
    commission_where = " AND ".join(commission_where_clauses)
    settlement_where = " AND ".join(settlement_where_clauses)
    limit_clause = ""
    if params.get("limit") is not None and params.get("offset") is not None:
        limit_clause = "LIMIT %(limit)s OFFSET %(offset)s"

    query = f"""
            WITH aggregated_commission AS (
            SELECT
                c.line_item_id,
                MAX(c.secondary_kd) AS secondary_kd,
                MAX(c.temporal_id) AS temporal_id,
                SUM(c.amount) AS commission_amount
            FROM commission c
            WHERE {commission_where}
            GROUP BY c.line_item_id
        ),
        aggregated_settlement AS (
            SELECT
                s.commission_row_key,
                SUM(s.amount) AS settlement_amount
            FROM settlement s
            WHERE {settlement_where}
            GROUP BY s.commission_row_key
        )
        SELECT
            ac.line_item_id,
            ac.temporal_id,
            ac.secondary_kd,
            ac.commission_amount,
            COALESCE(aset.settlement_amount, 0) AS settlement_amount,
            COALESCE(aset.settlement_amount, 0) - ac.commission_amount AS amount
        FROM aggregated_commission ac
        LEFT JOIN aggregated_settlement aset
            ON ac.line_item_id = aset.commission_row_key
        WHERE
            aset.settlement_amount IS NULL
            OR (COALESCE(aset.settlement_amount, 0) - ac.commission_amount) <> 0
        ORDER BY temporal_id DESC
        {limit_clause}

        """
    with connection.cursor() as cursor:
        cursor.execute(query, params)
        rows = cursor.fetchall()
        if not cursor.description:
            return []
        columns = [col[0] for col in cursor.description]
        return [dict(zip(columns, row)) for row in rows]


def get_new_deferred_commission_for_criteria(
    client_id,
    psd,
    ped,
    payee_email,
    plan_id,
    criteria_id,
    fx_rate_convertor,
    logger,
    login_user_id,
    offset=None,
    limit=None,
    return_records_count_only=False,
    is_zero_comm_line_item_included=False,
    orderby_fields: list[SortInfoType] | None = None,
):
    """
    deferred commission at line_item level for a criteria along with the datasheet data used in criteria
    """
    if isinstance(psd, str):
        psd = make_aware(date_utils.start_of_day(parse(psd, dayfirst=True)))
    if isinstance(ped, str):
        ped = make_aware(date_utils.end_of_day(parse(ped, dayfirst=True)))
    deferred_commission_by_line_item = []
    criteria = PlanCriteriaAccessor(client_id).get_criteria(
        criteria_id=criteria_id,
        fields=["criteria_data", "criteria_column", "criteria_config"],
    )
    criteria_data = criteria["criteria_data"]
    criteria_columns = update_hyperlink_columns_in_criteria_columns(
        client_id, criteria_id, plan_id, criteria["criteria_column"]
    )
    criteria_config = criteria["criteria_config"]
    trace_enabled = criteria_config.get("trace_enabled", False)
    is_line_item_level = criteria_data.get("is_line_item_level")

    # get commission lock
    comm_locked_kd = CommissionLockAccessor(
        client_id
    ).get_locked_kd_for_locked_comm_in_period(psd=psd, ped=ped, payee_email=payee_email)
    logger.info("Locked Kd for payee {0}: {1}".format(payee_email, comm_locked_kd))

    if is_line_item_level:
        # return total count if requested
        if return_records_count_only:
            logger.info("Fetching commission records count.")
            return get_commission_records_unique_line_item_ids_total_count(
                client_id=client_id,
                psd=psd,
                ped=ped,
                payee_email=payee_email,
                locked_kd=comm_locked_kd,
                logger=logger,
                additional_filters={
                    "commission_plan_id": plan_id,
                    "criteria_id": criteria_id,
                },
            )

        # When sorting is applied, the limit_value and offset_value will be None making the
        # get commission records to return all values.
        limit_value, offset_value = None, None
        if limit is not None and offset is not None and not orderby_fields:
            limit_value, offset_value = limit, offset

        # getting settlement lock
        from commission_engine.services.settlement_data_service import (
            get_settlement_lock_kd,
        )

        settlement_locked_kd = get_settlement_lock_kd(
            client_id=client_id, psd=psd, ped=ped, payee_email=payee_email
        )

        logger.info("Fetching commission and settlement records")
        params = {
            "client_id": client_id,
            "psd": psd,
            "ped": ped,
            "payee_email": payee_email,
            "plan_id": plan_id,
            "criteria_id": criteria_id,
            "locked_kd": comm_locked_kd,
            "settlement_locked_kd": settlement_locked_kd,
            "limit": limit_value,
            "offset": offset_value,
        }
        commission_data = get_commission_settlement_data(params)
        commission_line_item_ids = [
            record["line_item_id"] for record in commission_data
        ]
        knowledge_date = next(iter(commission_data or []), {}).get(
            "secondary_kd"
        )  # first record in commission

        for commission in commission_data:
            line_item_id = commission.get("line_item_id")
            commission_amount = commission.get("amount")
            # converting to payee currency
            if fx_rate_convertor is not None:
                commission_amount = float(
                    fx_rate_convertor.change_to_payee_currency(commission_amount)
                )
            deferred_commission_by_line_item.append(
                {"line_item_id": line_item_id, "commission": commission_amount}
            )
    else:
        comm_locked_kd = comm_locked_kd or timezone.now()
        logger.info("Fetching commission records for plan and summation criteria.")
        commission_recs = CommissionAccessor(
            client_id
        ).get_commission_for_plan_and_summation_criteria(
            payee_email,
            psd,
            ped,
            plan_id,
            criteria_id,
            comm_locked_kd,
            fields=["context_ids", "secondary_kd", "line_item_id"],
        )

        knowledge_date = None
        commission_line_item_ids = []
        if commission_recs:
            knowledge_date = commission_recs[0]["secondary_kd"]
        # when split_summation_to_li flag is enabled, commission records will be created for each line item, line_item_id will be present in commission record and context_ids will be empty
        # when split_summation_to_li flag is disabled, single commission record will be created, line_item_id will be empty and context_ids will be present
        for record in commission_recs:
            if record["context_ids"]:
                commission_line_item_ids.extend(record["context_ids"])
            elif record["line_item_id"]:
                commission_line_item_ids.append(record["line_item_id"])

        logger.info("Fetched commission record for plan and summation criteria.")

        # return total count if requested
        if return_records_count_only:
            return len(commission_line_item_ids)

        # paginating only when orderby fields are empty. Otherwise pagination will
        # happen after sorting.
        if offset is not None and limit is not None and not orderby_fields:
            commission_line_item_ids = commission_line_item_ids[
                offset * limit : (offset * limit) + limit
            ]

        for line_item in commission_line_item_ids:
            deferred_commission_by_line_item.append(
                {"line_item_id": line_item, "commission": 0}
            )

    databook_id = criteria_data.get("databook_id")
    datasheet_id = criteria_data.get("datasheet_id")
    ds_variables = DatasheetVariableAccessor(client_id).get_datasheet_vars_system_name(
        datasheet_id, knowledge_date
    )
    ds_fields_to_fetch = list(set(criteria_columns).intersection(set(ds_variables)))
    ds_fields_to_fetch.append("row_key")
    datasheet_df = fetch_datasheet_data_from_snowflake(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        knowledge_date=knowledge_date,
        row_keys=commission_line_item_ids,
        fields=ds_fields_to_fetch,
        login_user_id=login_user_id,
        logger=logger,
        as_dataframe=True,
    )

    logger.info("Joining Commission Dataframe with Datasheet Dataframe.")
    commission_df = pd.DataFrame(deferred_commission_by_line_item)
    valid_criteria_columns = zero_line_items_table(
        client_id, criteria_columns, datasheet_id, knowledge_date
    )

    if datasheet_df.empty or commission_df.empty:
        # For statement export as PDF(is_zero_comm_line_item_included is True), if no line item ids are present,
        # then return empty list for records with columns names
        if is_zero_comm_line_item_included:
            return {
                "records": [],
                "columns": valid_criteria_columns,
                "is_line_item_level": is_line_item_level,
                "trace_enabled": trace_enabled,
            }
        return {
            "records": [],
            "columns": [],
            "is_line_item_level": is_line_item_level,
            "trace_enabled": trace_enabled,
        }

    collated_df = pd.merge(
        commission_df,
        datasheet_df,
        left_on="line_item_id",
        right_on="row_key",
        how="inner",
    )
    logger.info("Removing nan nat in collated fataframe (Commission & Datasheet)")
    collated_df = remove_nan_nat(collated_df)

    if orderby_fields:
        collated_df = (
            SortUtility(collated_df, orderby_fields)
            .sort_records()
            .apply_limit_offset(
                limit,
                offset * limit if limit is not None and offset is not None else None,
            )
            .as_dataframe()
        )
        logger.info(
            f"Sorted the dataframe and applied limit and offset. Sort by columns: {orderby_fields}"
        )

    return {
        "records": collated_df.to_dict("records"),
        "columns": valid_criteria_columns,
        "is_line_item_level": is_line_item_level,
        "trace_enabled": trace_enabled,
    }
