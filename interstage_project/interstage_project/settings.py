"""
Django settings for interstage_project project.
Generated by 'django-admin startproject' using Django 3.0.6.
For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/
For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/

Configurations:
    - Django core
    - Rest framework
    - Auth
    - Security
    - Database
    - Cache
    - Password validations
    - Globalization
    - Static files
    - Logging
    - GraphQL
    - S3
    - Migrations
"""

import json
import mimetypes
import os
from pathlib import Path

from graphql.validation import rules

from everstage_infra.aws_infra.constants.environments import PRODUCTION_ENVIRONMENT
from everstage_infra.aws_infra.ecs import is_prod_or_demo_env
from interstage_project.utils import get_s3_bucket

mimetypes.add_type("text/css", ".css", True)


#####################################################################################
###################### D J A N G O   C O R E   C O N F I G ##########################
#####################################################################################

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get("SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = int(os.environ.get("DEBUG", default=0))

ALLOWED_HOSTS = os.environ.get("DJANGO_ALLOWED_HOSTS", "").split(" ")

ROOT_URLCONF = "interstage_project.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "interstage_project.wsgi.application"

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "djangorestframework_camel_case",
    "django_extensions",
    "commission_engine.apps.CommissionEngineConfig",
    "superset.apps.SupersetConfig",
    "spm.apps.SpmConfig",
    "kpi.apps.KpiConfig",
    "graphene_django",
    "storages",
    "everstage_admin_backend.apps.EverstageAdminBackendConfig",
    "django_celery_beat",
    "slack_everstage.apps.SlackEverstageConfig",
    "ms_teams_everstage.apps.MsTeamsEverstageConfig",
    "crystal.apps.CrystalConfig",
    "async_tasks.apps.AsyncTasksConfig",
    "everstage_infra.apps.EverstageInfra",
    "everstage_etl.apps.EverstageEtlConfig",
    "everstage_ddd.apps.EverstageDDDConfig",
    "common.apps.CommonConfig",
    "corsheaders",
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "interstage_project.middleware.HealthCheckMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.auth.middleware.RemoteUserMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "crum.CurrentRequestUserMiddleware",
    "interstage_project.middleware.ApplicationMiddleware",
    "interstage_project.middleware.CleanupLogContextMiddleware",
]

if not is_prod_or_demo_env():
    MIDDLEWARE.append("interstage_project.middleware.DevShieldMiddleware")

#####################################################################################
############# R E S T   F R A M E W O R K   C O N F I G U R A T I O N ###############
#####################################################################################

REST_FRAMEWORK = {
    "JSON_UNDERSCOREIZE": {  # Ref : https://github.com/vbabiy/djangorestframework-camel-case?tab=readme-ov-file#underscoreize-options
        "no_underscore_before_number": True,
    },
    "DEFAULT_RENDERER_CLASSES": (
        "djangorestframework_camel_case.render.CamelCaseJSONRenderer",
        "djangorestframework_camel_case.render.CamelCaseBrowsableAPIRenderer",
        # Any other renders
    ),
    "DEFAULT_PARSER_CLASSES": (
        # If you use MultiPartFormParser or FormParser, we also have a camel case version
        "djangorestframework_camel_case.parser.CamelCaseFormParser",
        "djangorestframework_camel_case.parser.CamelCaseMultiPartParser",
        "djangorestframework_camel_case.parser.CamelCaseJSONParser",
        # Any other parsers
    ),
    "DEFAULT_PERMISSION_CLASSES": ("rest_framework.permissions.IsAuthenticated",),
}


#####################################################################################
###################### A U T H   C O N F I G U R A T I O N ##########################
#####################################################################################

JWT_AUTH = {
    "JWT_VERIFY_EXPIRATION": True,
    "JWT_PAYLOAD_GET_USERNAME_HANDLER": "interstage_project.jwt_handler.jwt_get_username_from_payload_handler",
    "JWT_DECODE_HANDLER": "interstage_project.jwt_handler.jwt_decode_token",
    "JWT_ALGORITHM": "RS256",
    "JWT_AUDIENCE": os.environ.get("AUTH_AUDIENCE"),
    "JWT_ISSUER": os.environ.get("AUTH_ISSUER"),
    "JWT_AUTH_HEADER_PREFIX": "Bearer",
}

MGMT_API = {
    "AUTH_MGMT_ISSUER": os.environ.get("AUTH_MGMT_ISSUER"),
    "AUDIENCE": os.environ.get("AUTH_MGMT_AUDIENCE"),
    "CLIENT_ID": os.environ.get("AUTH_MGMT_CLIENT_ID"),
    "CLIENT_SECRET": os.environ.get("AUTH_MGMT_CLIENT_SECRET"),
}

#####################################################################################
############### G R A P H Q L   C O N F I G U R A T I O N  ##########################
#####################################################################################

GRAPHQL_JWT = JWT_AUTH

# GraphQL configuration
GRAPHENE = {
    "SCHEMA": "spm.graphql.schema",
    "MIDDLEWARE": [
        "interstage_project.middleware.DisableIntrospectionMiddleware",
    ],
}


def get_suggested_field_names(schema, graphql_type, field_name):
    return []


def get_suggested_type_names(schema, output_type, field_name):
    return []


# override the error messages if not local or dev environment
if os.environ.get("ENV") not in ["LOCALDEV", "DEV", "CI_SELENIUM"]:
    rules.fields_on_correct_type.get_suggested_field_names = get_suggested_field_names
    rules.fields_on_correct_type.get_suggested_type_names = get_suggested_type_names


#####################################################################################
#################### S E C U R I T Y   C O N F I G U R A T I O N ####################
#####################################################################################


CORS_ORIGIN_REGEX_WHITELIST = [
    r"https://mail.google.com",
    r"https://mail.google.com*",
]

if os.environ.get("ENV") in ["LOCALDEV"]:
    CORS_ORIGIN_REGEX_WHITELIST += [
        r"http://localhost:*",
        r"https://localhost:*",
    ]

# Security header config for staging
SECURE_BROWSER_XSS_FILTER = True
SECURE_HSTS_PRELOAD = True
SECURE_HSTS_SECONDS = 3600
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SECURE = True

MIDDLEWARE += [
    "csp.middleware.CSPMiddleware",  # Can we append this in main middlewares list ?
]

CSP_DEFAULT_SRC = ("'self'",)
CSP_FONT_SRC = (
    "'self'",
    "rsms.me",
    "data:",
    "fonts.gstatic.com",
)
CSP_STYLE_SRC = (
    "'self'",
    "'unsafe-inline'",
    "*.cloudfront.net",
    "rsms.me",
    "fonts.googleapis.com",
)
CSP_CONNECT_SRC = ("'self'", "*.everstage.com", "*.auth0.com", "wss://*.supabase.co")
CSP_SCRIPT_SRC = ("'self'",)
CSP_IMG_SRC = (
    "'self'",
    "*.cloudfront.net",
    "*.gravatar.com",
    "*.amazonaws.com",
    "*.googleusercontent.com",
    "data:",
)
CSP_FRAME_SRC = (
    "'self'",
    "*.everstage.com",
    "*.auth0.com",
    "app.sigmacomputing.com",
)
CSP_OBJECT_SRC = ("'none'",)
CSP_FRAME_ANCESTORS = (
    "'self'",
    "https://*.force.com",
    "https://*.salesforce.com",
    "https://*.visualforce.com",
)

#####################################################################################
##################### D A T A B A S E   C O N F I G U R A T I O N ###################
#####################################################################################

# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ.get("DB_NAME"),
        "USER": os.environ.get("DB_USER"),
        "PASSWORD": os.environ.get("DB_PASSWORD"),
        "HOST": os.environ.get("DB_HOST"),
        "PORT": os.environ.get("DB_PORT"),
    },
    "admin": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ.get("DB_NAME"),
        "USER": os.environ.get("ADMIN_DB_USER"),
        "PASSWORD": os.environ.get("ADMIN_DB_PASSWORD"),
        "HOST": os.environ.get("DB_HOST"),
        "PORT": os.environ.get("DB_PORT"),
    },
}

#####################################################################################
###################### C A C H E   C O N F I G U R A T I O N ########################
#####################################################################################

# https://docs.djangoproject.com/en/3.2/ref/settings/#caches
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": [
            os.environ.get("ELASTIC_CACHE_SERVER"),
            os.environ.get("ELASTIC_CACHE_RO_SERVER"),
        ],
        "TIMEOUT": 36000,
        "OPTIONS": {
            "MASTER_CACHE": os.environ.get("ELASTIC_CACHE_SERVER"),
            "COMPRESSOR": "django_redis.compressors.zstd.ZStdCompressor",
        },
    },
    "localMem": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
        "TIMEOUT": 36000,
    },
}

# USE_LOCAL_CACHE can be set to true whenever we run a script or tests
if os.getenv("USE_LOCAL_CACHE") == "true":
    CACHES["default"] = {"BACKEND": "interstage_project.cache.CustomLocalMemCache"}


#####################################################################################
###################### P A S S W O R D   V A L I D A T I O N S ######################
#####################################################################################

# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

#####################################################################################
###################### G L O B A L I Z A T I O N   C O N F I G ######################
#####################################################################################

# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_L10N = True

USE_TZ = True

#####################################################################################
###################### S T A T I C   F I L E S   C O N F I G ########################
#####################################################################################
# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = "/static/"
# Update STATIC_ROOT to a top-level directory
STATIC_ROOT = "static"

#####################################################################################
###################### L O G G I N G   C O N F I G U R A T I O N ####################
#####################################################################################
# Ensure logging directory/file are presen
logdir = Path().resolve() / "logs"
os.makedirs(str(logdir), exist_ok=True)
# log_loc = "logs/development.log"

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "simple": {
            "level": "INFO",
            "datefmt": "%Y-%m-%d %H:%M:%S",
            "format": "[%(asctime)s %(filename)s %(lineno)d %(levelname)s %(processName)s]: %(message)s",
        },
        "json": {
            "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
            "format": "%(message)s",
        },
        "json_extended": {
            "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
            "format": "%(asctime)s %(correlation_id)s %(e2e_sync_run_id)s %(request_id)s %(client_id)s %(log_context)s %(name)s %(levelname)s %(message)s %(filename)s %(lineno)d %(module)s %(process)d %(thread)d %(processName)s",
        },
        "json_exception": {
            "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
            "format": "[%(asctime)s] %(request_id)s %(name)s %(levelname)s %(message)s %(filename)s %(exception)s %(exception_tag)s %(lineno)d %(module)s %(process)d %(thread)d %(processName)s %(exc_info)s",
        },
    },
    "filters": {
        "add_exception_tag": {
            "()": "ever_logging.AddExceptionTagFilter",
        },
        "exclude_error_level": {
            "()": "ever_logging.ExcludeLogLevelFilter",
            "level_name": "ERROR",
        },
        "ever_web_context": {
            "()": "ever_logging.EverWebContextFilter",
        },
        "ever_log_context": {
            "()": "ever_logging.CorrelationIdFilter",
        },
    },
    "handlers": {
        "file": {
            "level": "INFO",
            "class": "logging.FileHandler",
            "filename": f"{logdir}/development.log",
            "formatter": "simple",
        },
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "json",
        },
        "console_json_formatter": {
            "class": "logging.StreamHandler",
            "formatter": "json_extended",
            "filters": [
                "ever_web_context",
                "ever_log_context",
                "add_exception_tag",
                "exclude_error_level",
            ],
        },
        "console_simple_formatter": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "simple",
            "filters": [
                "ever_web_context",
                "ever_log_context",
                "add_exception_tag",
                "exclude_error_level",
            ],
        },
        "console_exception_json_formatter": {
            "level": "ERROR",
            "class": "logging.StreamHandler",
            "formatter": "json_exception",
        },
        "console_exception_simple_formatter": {
            "level": "ERROR",
            "class": "logging.StreamHandler",
            "formatter": "simple",
        },
    },
    "loggers": {
        # Will be added by the _set_package_loggers function below
    },
}


def _set_package_loggers():
    everstage_logger = {
        "handlers": ["console_json_formatter", "console_exception_json_formatter"],
        "level": os.getenv("EVERSTAGE_LOG_LEVEL", "INFO"),
        "propagate": False,
    }
    deprecated_logger = {
        "handlers": ["console"],
        "level": os.getenv("EVERSTAGE_LOG_LEVEL", "INFO"),
        "propagate": False,
    }
    # Handlers to use during local development - makes reading easier
    local_handlers = [
        "console_simple_formatter",
        "console_exception_simple_formatter",
    ]
    # For non-prod environments, use a simple text logger as json is hard to read
    # In prod environments, we have datadog to parse the json logs
    if os.environ.get("ENV") not in PRODUCTION_ENVIRONMENT:
        everstage_logger["handlers"] = local_handlers
        deprecated_logger["handlers"] = local_handlers

    # Set the modified logger for all apps (in alphabetical order)
    django_apps = [
        "async_tasks",
        "commission_engine",
        "common",
        "crystal",
        "everstage_admin_backend",
        "everstage_ddd",
        "everstage_etl",
        "everstage_infra",
        "kpi",
        "ms_teams_everstage",
        "slack_everstage",
        "spm",
        "superset",
    ]
    for app in django_apps:
        LOGGING["loggers"][app] = everstage_logger

    LOGGING["loggers"]["django"] = deprecated_logger


_set_package_loggers()

# TODO: Create more loggers like commission_engine and spm

#####################################################################################
######################## S 3   C O N F I G U R A T I O N  ###########################
#####################################################################################
# file upload
# DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"  # Check for deprecation behaviour - https://docs.djangoproject.com/en/5.0/releases/4.2/#custom-file-storages

STORAGES = {
    "default": {"BACKEND": "storages.backends.s3boto3.S3Boto3Storage"}
}  # We can remove DEFAULT_FILE_STORAGE going forward

AWS_S3_SECURE_URLS = False  # use http instead of https
AWS_QUERYSTRING_AUTH = (
    False  # don't add complex authentication-related query parameters for requests
)

AWS_STORAGE_BUCKET_NAME = get_s3_bucket(os.environ.get("ENV"))
# AWS_S3_CUSTOM_DOMAIN = 'https://d177w6gi8jmdvz.cloudfront.net'
AWS_S3_CUSTOM_DOMAIN = os.environ.get("S3_CDN")

# request payload max size (512MB = 536870912)
DATA_UPLOAD_MAX_MEMORY_SIZE = 536870912


#####################################################################################
################# M I G R A T I O N S   C O N F I G U R A T I O N ###################
#####################################################################################


def _get_migration_modules():
    # Uncomment this only when you want to generate migration files against a non-local database
    # Replace app_env value with whichever environement you want to generate the migration files against
    # See complete instructions here - https://interstage.atlassian.net/wiki/spaces/TECH/pages/356122692/CI+CD#Handling-Migration-Related-Failures%3A
    # Should be one of the following - demo, dev, prod, sandbox, staging, localdev
    # app_env = "localdev"
    # migration_modules_str = {
    #     "commission_engine": f"{app_env}.commission_engine.migrations",
    #     "spm": f"{app_env}.spm.migrations",
    #     "crystal": f"{app_env}.crystal.migrations",
    #     "async_tasks": f"{app_env}.async_tasks.migrations",
    #     "slack_everstage": f"{app_env}.slack_everstage.migrations",
    #     "ms_teams_everstage": f"{app_env}.ms_teams_everstage.migrations",
    #     "superset": f"{app_env}.superset.migrations",
    #     "kpi": f"{app_env}.kpi.migrations",
    #     "everstage_ddd": f"{app_env}.everstage_ddd.migrations",
    #     "common": f"{app_env}.common.migrations",
    #     "everstage_admin_backend": f"{app_env}.everstage_admin_backend.migrations",
    #     "everstage_etl": f"{app_env}.everstage_etl.migrations",
    #     "everstage_infra": f"{app_env}.everstage_infra.migrations",
    # }
    # value = json.dumps(migration_modules_str)
    value = os.environ.get(
        "MIGRATION_MODULES_ENV", "{}"
    )  # Comment this line and uncomment above code when you want to run migrations using localdev migrations
    return json.loads(value)


MIGRATION_MODULES = _get_migration_modules()
