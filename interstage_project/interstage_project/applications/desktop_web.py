import os
from typing import Dict, Optional, Tuple
from uuid import UUID

from django.conf import settings
from django.http import HttpRequest
from django.urls import include, path
from django.views.decorators.csrf import csrf_exempt

# TODO: Remove this import from datasheet_api when circular imports are resolved
from everstage_ddd.datasheet.databook_api import api as databook_api
from everstage_ddd.datasheet.datasheet_api import api as datasheet_api
from gql.views import CustomGraphQLView
from interstage_project.auth_utils import (
    get_token_auth_header,
    is_verified_staff_member,
)
from interstage_project.jwt_handler import (
    get_auth0_public_key_for_auth_api,
    jwt_decode_token,
    jwt_get_access_token_uid,
    jwt_get_emails_from_payload_handler,
)
from interstage_project.views import ExitUserCheck
from spm.services.session_management_services import (
    TokenSessionMappingServices,
    validate_token_status,
)

from ..ninja_api import ninja_api
from .generic import BaseApplication, ClientFinderV2Mixin, SupportUserAuthMixin
from .generic.utils import (
    get_django_user_object,
    is_request_path_matching_with_urlpatterns,
)


class DesktopWeb(BaseApplication, ClientFinderV2Mixin, SupportUserAuthMixin):
    """
    Represents the desktop web application.

    This class extends the BaseApplication class and includes mixins for client finding.

    Attributes:
        urlpatterns (list): A list of URL patterns for the application.

    Methods:
        identify_request(request): Identifies if the request matches the application's URL patterns.
        authenticate(): Authenticates the user by decoding and validating the JWT token.
        get_client_claims(): Retrieves the client claims for the user.

    """

    # TODO: There should be a common identifier for apis; and a common identifier specific to app.
    # For e.g. - /api/desktop-web/async-tasks/, /api/desktop-web/commission-engine/, etc.
    urlpatterns = [
        path("ninja/", ninja_api.urls),  # type: ignore
        path("async-tasks/", include("async_tasks.urls")),
        path("commission_engine/", include("commission_engine.urls")),
        path("common/", include("common.urls")),
        path("crystal/", include("crystal.urls")),
        path("kpi/", include("kpi.urls")),
        path("thunderforge/", include("everstage_ddd.thunderforge.urls")),
        path("global_search/", include("everstage_ddd.global_search.urls")),
        path("llm_agent/", include("everstage_ddd.llm_agent.agent_workbench.urls")),
        path("llm_invoke/", include("everstage_ddd.llm_invocation.urls")),
        path("datasheet_builder/", include("everstage_ddd.datasheet_builder.urls")),
        path("msteams/", include("ms_teams_everstage.urls")),
        path("slack/", include("slack_everstage.urls")),
        path("spm/", include("spm.urls")),
        path("superset/", include("superset.urls")),
        path("workflow_builder/", include("everstage_ddd.workflow_builder.urls")),
        path(
            "user-exited-check", ExitUserCheck.as_view(), name="user_exited_check_view"
        ),
        path(
            "graphql",
            csrf_exempt(CustomGraphQLView.as_view(graphiql=True)),
            name="custom_graphql_view",
        ),
        path("datasheets/", include("everstage_ddd.datasheet.urls")),
        path(
            "observable_report_objects/",
            include("everstage_ddd.observable_report_objects.urls"),
        ),
        path("tqm/", include("everstage_ddd.tqm.urls")),
        path("databooks/", databook_api.urls),
        path(
            "self-service-integration/",
            include("everstage_ddd.self_service_integration.urls"),
        ),
        path(
            "notification_audit/",
            include("everstage_ddd.notifications.urls"),
        ),
        path("datasheet_ninja/", datasheet_api.urls),
        path(
            "send_email_to_everstage/",
            include("everstage_ddd.send_email_to_everstage.urls"),
        ),
    ]

    @classmethod
    def identify_request(cls, request: HttpRequest) -> bool:
        """
        Identifies if the request matches the application's URL patterns.

        Args:
            request: The HTTP request object.

        Returns:
            bool: True if the request matches the URL patterns, False otherwise.

        """
        return is_request_path_matching_with_urlpatterns(request.path, cls.urlpatterns)

    def authenticate(self) -> Tuple[str, str, Dict, str]:
        """
        Authenticates the user by decoding and validating the JWT token.

        Returns:
            Tuple: A Tuple containing the user's email, the raw token and the decoded_token.

        """
        # extract token
        token: str = get_token_auth_header(self.request)

        # arrange secrets
        jwt_auth: Dict = settings.JWT_AUTH
        issuer: str = jwt_auth["JWT_ISSUER"]
        audience: str = jwt_auth["JWT_AUDIENCE"]
        public_key: str = get_auth0_public_key_for_auth_api(token)

        # decode & validate token
        decoded_token: Dict = jwt_decode_token(token, public_key, audience, issuer)
        token_id: UUID = jwt_get_access_token_uid(decoded_token=decoded_token)

        # validate token status; if token is blacklisted or not registered, raise exception
        if os.environ.get("ENV") not in ["LOCALDEV", "CI_SELENIUM"]:
            validate_token_status(self.request.path, token_id)

        # get session_id using token_id | session<>token mapping is created in trigger_mfa api (on first trigger in session); so it would be available after that only
        session_id: Optional[str] = TokenSessionMappingServices(
            token_id
        ).get_session_id()

        # get login_email and email from decoded_token
        login_email, email = jwt_get_emails_from_payload_handler(decoded_token)
        master_email = login_email

        ################ S U P P O R T   U S E R   A U T H ##################
        # check if user is a verified staff member; it could be potentially a support user
        if is_verified_staff_member(login_email, decoded_token.get("con_provider")):
            master_email, email = self.authenticate_support_user(
                session_id, login_email, email
            )
        #######################################################################

        # set additional attributes to request
        self.set_attributes_to_request(
            {
                "master_user": get_django_user_object(master_email),
                "session_id": session_id,
                "token_id": token_id,
            },
            add_to_logger_context=True,
        )

        return email, token, decoded_token, login_email

    def get_client_claims(self) -> Tuple[Optional[int], Optional[str]]:
        """
        Retrieves the client claims for the request.
        Mixin used: ClientFinderV2Mixin

        Returns:
            Tuple: A Tuple containing the client ID and client name, or None if not found.

        """
        client_id, domain = self.find_client()
        return client_id, domain
