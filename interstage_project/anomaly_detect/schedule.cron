*/15 * * * * scripts.global_customer_data_health_check.etl_anomaly.detect_etl_anomalies 2
*/15 * * * * scripts.global_customer_data_health_check.commission_anomalies.empty_quota_checker.detect_empty_quotas_wrapper
*/15 * * * * scripts.global_customer_data_health_check.commission_anomalies.quota_payout_freq_mismatch_checker.detect_quota_payout_freq_mismatch_wrapper
*/15 * * * * scripts.global_customer_data_health_check.commission_anomalies.fx_rate_mismatch.detect_fx_rate_mismatch_wrapper
30 6 * * * scripts.global_customer_data_health_check.commission_anomalies.com_set_duplicates.detect_comm_sett_duplicates_wrapper
30 8 * * * scripts.global_customer_data_health_check.commission_anomalies.postgres_snowflake_mismatch.detect_postgres_snowflake_mismatch_wrapper
30 7 * * * scripts.global_customer_data_health_check.commission_anomalies.postgres_snowlflake_line_item_checker.detect_postgres_to_snowflake_line_item_level_mismatch
30 7 * * * scripts.global_customer_data_health_check.commission_anomalies.base_table_to_snapshot_mismatch_checker.detect_base_table_to_snapshot_mismatch
30 7 * * * scripts.global_customer_data_health_check.commission_anomalies.snapshot_to_report_mismatch_checker.detect_snapshot_to_report_mismatch
30 5 * * * scripts.global_customer_data_health_check.commission_anomalies.payout_value_mismatch.compare_ps_and_statement_wrapper
30 5 * * * scripts.global_customer_data_health_check.commission_anomalies.report_non_effective_dated_data.check_non_effective_dated_data_wrapper
*/15 * * * * scripts.global_customer_data_health_check.admin_champ.hierarchy_health.run_hierarchy_health_check_wrapper
0 13 * * * scripts.global_customer_data_health_check.admin_champ.missing_ds_variables_in_canvas.check_missing_ds_variables_in_canvas_wrapper
0 13 * * * scripts.global_customer_data_health_check.repconnect.missing_ds_variables_in_crystal.check_missing_ds_variables_in_crystal_wrapper
*/15 * * * * scripts.global_customer_data_health_check.upstream_anomalies.extracted_load_checker.check_extract_load_count_wrapper
*/15 * * * * scripts.global_customer_data_health_check.upstream_anomalies.non_integrated_object_checker.check_unuploaded_sync_data_wrapper
*/15 * * * * everstage_etl.validation.tasks.upstream_validation_wrapper_task
0 */12 * * * scripts.global_customer_data_health_check.upstream_anomalies.retrospective_updates.detect_retrospective_updates_wrapper
0 */12 * * * scripts.global_customer_data_health_check.upstream_anomalies.source_file_columns_validator.detect_source_file_columns_changes_wrapper
0 0 * * * scripts.global_customer_data_health_check.upstream_anomalies.future_dated_changes_field.detect_future_dated_records_wrapper
0 0 * * * scripts.global_customer_data_health_check.upstream_anomalies.stale_data.detect_stale_data_wrapper
0 0 * * * scripts.global_customer_data_health_check.upstream_anomalies.periodic_extracted_count.detect_periodic_extracted_count_checker_wrapper