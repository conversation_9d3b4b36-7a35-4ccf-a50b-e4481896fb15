export const docusignOAuth = (accessToken) => {
  return fetch("/spm/oauth/auth", {
    method: "GET",
    headers: {
      Authorization: `Bear<PERSON> ${accessToken}`,
    },
  });
};

/**
 * Fetches DocuSign permissions for a user
 *
 * Makes an API call to get the DocuSign permissions for the specified user email.
 * The permissions determine what DocuSign operations the user can perform.
 *
 * @param {string} accessToken - The access token for authentication
 * @param {string} email - The email of the user to get permissions for
 * @returns {Promise<Object>} The user's DocuSign permissions
 * @throws {Error} If the API call fails or returns an error response
 */
export const getUserPermission = async (accessToken, email) => {
  try {
    // Make API request to fetch DocuSign permissions for the user
    const response = await fetch(
      `/spm/template/docusign_permissions?email=${encodeURIComponent(email)}`,
      {
        method: "GET",
        headers: {
          // Add authorization header with access token
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    // Check if response was successful
    if (!response.ok) {
      throw new Error(
        "We couldn’t verify your permissions right now. Please try again."
      );
    }

    // Parse and return the JSON response
    return response.json();
  } catch (error) {
    // Log any errors that occur during the API call
    console.error("Error fetching user permission:", error);
    // Re-throw the error to be handled by the caller
    throw error;
  }
};

export const docusignOAuthCallback = (accessToken, data) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  return fetch("/spm/oauth/callback", requestOptions);
};

export const docusignTemplateEditView = (accessToken, data) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  return fetch("/spm/template/edit_view", requestOptions);
};

export const createDocusignTemplate = (accessToken, data) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: data,
  };
  return fetch("/spm/template/create", requestOptions);
};

export const disconnectDocusign = (data, accessToken) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  return fetch("/spm/oauth/disconnect", requestOptions);
};

export const updateDocusignTemplate = (accessToken, data) => {
  const requestOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: data,
  };
  return fetch("/spm/template/update", requestOptions);
};

export const exportTemplateTabsCsv = (accessToken, data) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  return fetch("/spm/template/export-template-tabs-csv", requestOptions);
};

export const exportEnvelopesStatusAsCSV = (accessToken, data) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  return fetch("/spm/template/export-envelopes-status-as-csv", requestOptions);
};

export const exportContractsStatusAsCSV = (accessToken, data) => {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  };
  return fetch("/spm/template/export-contracts-status-as-csv", requestOptions);
};
