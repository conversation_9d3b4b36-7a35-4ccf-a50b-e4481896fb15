import { TrashLottie } from "@everstage/evericons/lotties";
import {
  DotsVerticalIcon,
  EyeIcon,
  PlusSquareIcon,
  TrashIcon,
  UserPlusIcon,
} from "@everstage/evericons/outlined";
import { useSpring, animated } from "@react-spring/web";
import { Col, Dropdown, Menu, Row } from "antd";
import { formatDistanceToNowStrict } from "date-fns";
import React, { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "react-query";
import { Link, useNavigate } from "react-router-dom";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";
import { parse } from "valibot";

import { navPortalAtom } from "~/GlobalStores/atoms";
import {
  EverLoader,
  EverGroupAvatar,
  EverCard,
  EverBadge,
  EverTooltip,
  EverModal,
  EverButton,
  EverButtonGroup,
  EverTg,
  toast,
  EverHotToastMessage,
  EverList,
  EmptyScreen,
  EverNumberBadge,
} from "~/v2/components";
import EverNavPortal from "~/v2/components/EverNavPortal";
import { useCrystalAPI } from "~/v2/features/crystal/api";
import { noData } from "~/v2/images";

import { CreateCrystalViewModal } from "./CreateCrystalViewModal";
import { CrystalAdminBootstrapView } from "./CrystalAdminBootstrapView";
import { ManageUsersModal } from "./ManageUsersModal";
import { CrystalViewsSchema } from "../crystal-schemas";
import { CRYSTAL_VIEW_STATUS } from "../Utils";

export function CrystalAdminView() {
  const [viewDataList, setViewDataList] = useState();
  const crystalAPI = useCrystalAPI();
  const { data, refetch } = useQuery(["getAllCrystalViews"], () =>
    crystalAPI.fetchAllCrystalViews()
  );

  useEffect(() => {
    if (data) {
      const parsedData = parse(CrystalViewsSchema, data.data);
      setViewDataList(parsedData);
    }
  }, [data]);

  return (
    <EverLoader
      indicatorType="spinner"
      spinning={!viewDataList}
      className="crystal"
    >
      {viewDataList && (
        <>
          {viewDataList.length === 0 ? (
            <CrystalAdminBootstrapView />
          ) : (
            <CrystalAdminListView
              refetch={refetch}
              viewDataList={viewDataList}
              crystalAPI={crystalAPI}
            />
          )}
        </>
      )}
    </EverLoader>
  );
}

function CrystalAdminListView({ viewDataList, refetch, crystalAPI }) {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [selectedViewId, setSelectedViewId] = useState();
  const [filterRadioButton, setFilterRadioButton] = useState("all");
  const [showCreateViewModal, setShowCreateViewModal] = useState(false);
  const [manageUserModalData, setManageUserModalData] = useState({});

  const [isManageUsersModalVisible, setIsManageUsersModalVisible] =
    useState(false);
  const [isConfirmationModalVisible, setIsConfirmationModalVisible] =
    useState(false);
  const [payeeList, setPayeeList] = useState();
  const [active, setActive] = useState(0);

  // Get payee list
  const { data, isLoading } = useQuery(["getPayeeList"], () =>
    crystalAPI.fetchPayeesInfo()
  );
  useEffect(() => {
    if (data) {
      setPayeeList(data);
    }
  }, [data]);

  const filterList = [
    {
      label: "All",
      value: "all",
    },
    {
      label: "Draft",
      value: "draft",
    },
    {
      label: "Published",
      value: "published",
    },
  ];

  function onfilterTypeChange(value, i) {
    setFilterRadioButton(value);
    setActive(i);
  }

  function onCreateView(responeData) {
    const { crystalViewId } = responeData;
    // console.log("Received values of form: ", values);
    setShowCreateViewModal(false);
    navigate(`/crystal/${crystalViewId}`);
  }

  function onConfirmDeleteView(crystalViewId) {
    const loadingToastId = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description={"Deleting the Crystal View..."}
        />
      ),
      { position: "top-center", duration: Infinity }
    );
    queryClient
      .fetchQuery({
        queryKey: ["postDeleteCrystalView"],
        queryFn: () => crystalAPI.postDeleteCrystalView(crystalViewId),
      })
      .then(() => {
        refetch();
        toast.dismiss(loadingToastId);
        toast.custom(
          () => (
            <EverHotToastMessage
              type="success"
              description={"Crystal View successfully deleted"}
            />
          ),
          { position: "top-center" }
        );
        setIsConfirmationModalVisible(false);
      })
      .catch((error) => {
        toast.dismiss(loadingToastId);
        toast.custom(
          () => (
            <EverHotToastMessage type="error" description={error.message} />
          ),
          { position: "top-center" }
        );
        console.error(error);
      });
  }

  const [springProps] = useSpring(
    () => ({
      from: { opacity: 0 },
      to: { opacity: 1 },
      // from: { opacity: 0 },
      // enter: { opacity: 1 },
      // leave: { opacity: 0 },
    }),
    []
  );

  return (
    <div className="min-h-96">
      <EverLoader type="spinner" spinning={isLoading}>
        <>
          <NavSegement
            active={active}
            filterList={filterList}
            filterRadioButton={filterRadioButton}
            onfilterTypeChange={onfilterTypeChange}
            setShowCreateViewModal={(value) => setShowCreateViewModal(value)}
            viewDataList={viewDataList}
          />
          {/* //Initiating modals */}
          <CreateCrystalViewModal
            payeeList={payeeList}
            showCreateViewModal={showCreateViewModal}
            onCreate={onCreateView}
            onCancelCreateView={(form) => {
              form.resetFields();
              setShowCreateViewModal(false);
            }}
          />
          <ManageUsersModal
            crystalViewName={manageUserModalData.crystalViewName}
            crystalViewId={manageUserModalData.crystalViewId}
            crystalViewStatus={manageUserModalData.crystalViewStatus}
            isVisible={isManageUsersModalVisible}
            onClose={() => {
              refetch();
              setIsManageUsersModalVisible(false);
            }}
          />
          <DeleteViewConfirmationModal
            isConfirmationModalVisible={isConfirmationModalVisible}
            onConfirmDeleteView={() => {
              onConfirmDeleteView(selectedViewId);
            }}
            onCancelDelete={() => setIsConfirmationModalVisible(false)}
          />
          {viewDataList.length > 0 ? (
            <EverList
              locale={{
                emptyText: <EmptyScreen description="No data" icon={noData} />,
              }}
              dataSource={viewDataList.filter(
                (x) =>
                  x.status === filterRadioButton || filterRadioButton === "all"
              )}
              grid={{
                gutter: 16,
                column: 3,
                xxl: 4,
                xl: 3,
                lg: 2,
                md: 2,
                sm: 1,
                xs: 1,
              }}
              renderItem={({
                crystalViewName,
                lastModifiedBy,
                lastModifiedAt,
                status,
                payees,
                crystalViewId,
              }) => {
                return (
                  <EverList.Item
                    key={crystalViewId}
                    className="relative ease-in-out duration-100"
                  >
                    <animated.div style={springProps}>
                      <CrystalListItem
                        crystalViewName={crystalViewName}
                        lastModifiedAt={lastModifiedAt}
                        payees={payees}
                        status={status}
                        lastModifiedBy={lastModifiedBy}
                        setManageUserModalData={(data) =>
                          setManageUserModalData(data)
                        }
                        crystalViewId={crystalViewId}
                        history={history}
                        setSelectedViewId={(value) => setSelectedViewId(value)}
                        isManageUsersModalVisible={isManageUsersModalVisible}
                        setIsManageUsersModalVisible={(value) =>
                          setIsManageUsersModalVisible(value)
                        }
                        setIsConfirmationModalVisible={(value) =>
                          setIsConfirmationModalVisible(value)
                        }
                      />
                    </animated.div>
                  </EverList.Item>
                );
              }}
            ></EverList>
          ) : null}
        </>
      </EverLoader>
    </div>
  );
}

function NavSegement({
  setShowCreateViewModal,
  active,
  filterList,
  onfilterTypeChange,
  viewDataList,
}) {
  const navPortalLocation = useRecoilValue(navPortalAtom);
  return (
    <EverNavPortal target={navPortalLocation}>
      <Row>
        <Col flex={1}>
          <EverButtonGroup
            className="bg-ever-base-200"
            activeBtnType="text"
            activeBtnColor="primary"
            defActiveBtnIndex={active}
            size="small"
          >
            {filterList.map((x, i) => {
              // Calculate count for each filter type
              const count =
                x.value === "all"
                  ? viewDataList?.length || 0
                  : viewDataList?.filter((item) => item.status === x.value)
                      ?.length || 0;

              return (
                <EverButton
                  key={x.value}
                  onClick={() => {
                    onfilterTypeChange(x.value, i);
                  }}
                  appendIcon={
                    <EverNumberBadge
                      className="bg-ever-base-300 text-ever-base-content"
                      count={Number(count)}
                    />
                  }
                >
                  <div className="flex flex-row gap-2 items-center">
                    {x.label}
                  </div>
                </EverButton>
              );
            })}
          </EverButtonGroup>
        </Col>
        <Col flex={0}>
          <EverButton
            onClick={() => setShowCreateViewModal(true)}
            prependIcon={<PlusSquareIcon />}
            size="small"
          >
            New Simulator
          </EverButton>
        </Col>
      </Row>
    </EverNavPortal>
  );
}

function CrystalListItem({
  setSelectedViewId,
  crystalViewName,
  status,
  setManageUserModalData,
  lastModifiedAt,
  lastModifiedBy,
  payees,
  crystalViewId,
  setIsManageUsersModalVisible,
  setIsConfirmationModalVisible,
}) {
  const showPreviewAsPayeeIcon = payees[0];
  const showDeleteIcon = status === CRYSTAL_VIEW_STATUS.DRAFT;
  return (
    <EverCard
      key={crystalViewId}
      interactive
      shadowSize="none"
      roundedSize="xl"
    >
      <div className="relative">
        <div className="absolute -right-1 -top-1">
          <Dropdown
            trigger={["click"]}
            overlay={
              <Menu>
                <Menu.Item className="!p-0" key="Manage Payees">
                  <div
                    className="flex items-center h-full w-full px-4"
                    onClick={() => {
                      setManageUserModalData({
                        crystalViewName: crystalViewName,
                        crystalViewId: crystalViewId,
                        crystalViewStatus: status,
                      });
                      setIsManageUsersModalVisible(true);
                    }}
                  >
                    <UserPlusIcon
                      title="Manage Payees"
                      className="w-5 h-5 cursor-pointer text-ever-base-content-mid mr-3"
                    />
                    <EverTg.Text className="text-ever-base-content">
                      Manage Payees
                    </EverTg.Text>
                  </div>
                </Menu.Item>

                {showPreviewAsPayeeIcon && (
                  <Menu.Item className="!p-0" key="View as payee">
                    <Link
                      title="View As Payee"
                      to={`/crystal/preview/${crystalViewId}/${payees[0].emailId}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="!text-ever-base-content-mid flex items-center justify-center"
                    >
                      <div className="flex items-center h-full w-full px-4">
                        <EyeIcon className="w-5 h-5 text-ever-base-content-mid mr-3" />
                        <EverTg.Text className="text-ever-base-content">
                          View as payee
                        </EverTg.Text>
                      </div>
                    </Link>
                  </Menu.Item>
                )}

                {showDeleteIcon && (
                  <Menu.Item className="!p-0" key="Delete Simulator">
                    <div
                      className="flex items-center h-full w-full px-4"
                      onClick={() => {
                        setSelectedViewId(crystalViewId);
                        setIsConfirmationModalVisible(true);
                      }}
                    >
                      <TrashIcon
                        title="Delete Simulator"
                        className="w-5 h-5 cursor-pointer text-ever-error mr-3"
                      />
                      <EverTg.Text className="text-ever-error">
                        Delete Simulator
                      </EverTg.Text>
                    </div>
                  </Menu.Item>
                )}
              </Menu>
            }
          >
            <EverButton.Icon
              size="small"
              color="base"
              type="text"
              icon={<DotsVerticalIcon className="text-ever-base-content-mid" />}
            />
          </Dropdown>
        </div>
        <Link to={`/crystal/${crystalViewId}`}>
          <div className="flex flex-col">
            <div
              className={twMerge(
                "flex items-start justify-between h-10",
                showPreviewAsPayeeIcon && showDeleteIcon ? "pr-28" : "pr-20"
              )}
            >
              <EverTooltip
                placement="top"
                title={
                  crystalViewName.charAt(0).toUpperCase() +
                  crystalViewName.slice(1)
                }
              >
                <EverTg.SubHeading3 className="block text-ever-base-content max-w-full whitespace-nowrap overflow-hidden line-clamp-2">
                  {crystalViewName.charAt(0).toUpperCase() +
                    crystalViewName.slice(1)}
                </EverTg.SubHeading3>
              </EverTooltip>
            </div>

            <div className="flex items-end justify-between w-full mt-4">
              <div className="flex flex-col">
                <EverTg.Caption className="text-ever-base-content-mid">
                  Modified by
                </EverTg.Caption>
                <EverTg.SubHeading4 className="!text-ever-base-content">
                  {lastModifiedBy.firstName.charAt(0).toUpperCase() +
                    lastModifiedBy.firstName.slice(1)}{" "}
                  {lastModifiedBy.lastName.charAt(0).toUpperCase() +
                    lastModifiedBy.lastName.slice(1)}
                </EverTg.SubHeading4>
              </div>
              <div className="flex flex-col items-end">
                <EverTg.Caption className="text-ever-base-content-mid">
                  Last updated{" "}
                </EverTg.Caption>
                <EverTg.SubHeading4 className="!text-ever-base-content">
                  {formatDistanceToNowStrict(new Date(lastModifiedAt))} ago
                </EverTg.SubHeading4>
              </div>
            </div>

            <div className="flex items-end gap-2 justify-between mt-5">
              <div>
                {status === CRYSTAL_VIEW_STATUS.DRAFT ? (
                  <EverBadge title="Draft" type="warning" />
                ) : (
                  <EverBadge title="Published" type="success" />
                )}
              </div>
              <EverGroupAvatar
                size="medium"
                forceShowPopover={true}
                groupMaxCount={3}
                limitInPopover={10}
                avatars={payees.map(({ firstName, lastName }) => {
                  return {
                    firstName,
                    lastName,
                  };
                })}
              ></EverGroupAvatar>
            </div>
          </div>
        </Link>
      </div>
    </EverCard>
  );
}

function DeleteViewConfirmationModal({
  isConfirmationModalVisible,
  onConfirmDeleteView,
  onCancelDelete,
}) {
  return (
    <EverModal.Confirm
      destroyOnClose={true}
      title="Are you sure you want to permanently delete the simulator?"
      subtitle="You can't undo this action"
      visible={isConfirmationModalVisible}
      confirmationButtons={[
        <EverButton key="cancel" color="base" onClick={onCancelDelete}>
          Cancel
        </EverButton>,
        <EverButton key="delete" onClick={onConfirmDeleteView}>
          Delete
        </EverButton>,
      ]}
      icon={
        <TrashLottie
          autoplay
          className="h-10 w-10 text-ever-error-content -translate-y-1"
          loop
        />
      }
      iconContainerClasses="bg-ever-error-lite"
    />
  );
}
