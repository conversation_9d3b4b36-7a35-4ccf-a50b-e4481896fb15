import { InfoCircleIcon, UsersIcon } from "@everstage/evericons/outlined";
import { Row, Col } from "antd";
import PropTypes from "prop-types";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQuery, useQueryClient } from "react-query";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import { ANALYTICS_EVENTS, ANALYTICS_PROPERTIES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverModal,
  EverForm,
  EverSelect,
  EverButton,
  EverListItem,
  EverTooltip,
  EverGroupAvatar,
  EverTg,
  toast,
  EverHotToastMessage,
} from "~/v2/components";
import { useCurrentTheme } from "~/v2/components/custom-hooks";
import { useCrystalAPI } from "~/v2/features/crystal/api";

import { CRYSTAL_VIEW_STATUS } from "../Utils";

const { Option } = EverSelect;

ManageUsersModal.propTypes = {
  crystalViewId: PropTypes.string,
  crystalViewName: PropTypes.string,
  crystalViewStatus: PropTypes.string,
  isVisible: PropTypes.bool,
  onClose: PropTypes.func,
};

export function ManageUsersModal({
  crystalViewId,
  crystalViewName,
  crystalViewStatus,
  isVisible,
  onClose,
}) {
  const [form] = EverForm.useForm();
  const queryClient = useQueryClient();
  const crystalAPI = useCrystalAPI();
  const { accessToken } = useAuthStore();
  const { data: payeeList, refetch: refetchPayeeList } = useQuery(
    ["getPayeeList"],
    () => crystalAPI.fetchPayeesInfo(),
    { enabled: isVisible }
  );
  // payee list should be refetched whenever status changes
  useEffect(() => {
    if (crystalViewStatus === CRYSTAL_VIEW_STATUS.PUBLISHED) {
      refetchPayeeList();
    }
  }, [crystalViewStatus]);

  const [disableButton, setDisableButton] = useState(false);
  const mappedPayees =
    payeeList &&
    payeeList.filter((payee) => {
      let flag = false;
      payee.crystalMapping.map(({ viewId, status }) => {
        if (viewId === crystalViewId) {
          if (
            crystalViewStatus === CRYSTAL_VIEW_STATUS.EDIT_MODE &&
            status === CRYSTAL_VIEW_STATUS.EDIT_MODE
          ) {
            flag = true;
          }
          if (
            crystalViewStatus !== CRYSTAL_VIEW_STATUS.EDIT_MODE &&
            status !== CRYSTAL_VIEW_STATUS.EDIT_MODE
          ) {
            flag = true;
          }
        }
      });
      return flag;
    });

  async function onRemoveUser(payeeEmail) {
    setDisableButton(true);
    const loadingToastId = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description={"Removing payee(s)..."}
        />
      ),
      { position: "top-center", duration: Infinity }
    );
    const payeesToMap = [];
    mappedPayees.map((payee) => {
      const { email } = payee;
      if (email !== payeeEmail) {
        payeesToMap.push(email);
      }
    });
    sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.REMOVE_PAYEE, {
      [ANALYTICS_PROPERTIES.REMOVE_PAYEE]: payeeEmail,
    });
    try {
      await queryClient.fetchQuery({
        queryKey: ["postMapPayeeToCrystal"],
        queryFn: () =>
          crystalAPI.postMapPayeeToCrystal(crystalViewId, payeesToMap),
      });
      await refetchPayeeList();
      setTimeout(() => {
        // This timeout is needed to dismiss the loading toast and shows the success toast. If not added, the loading toast is not dismissed and the success toast is shown.
        toast.dismiss(loadingToastId);
        toast.custom(
          () => (
            <EverHotToastMessage
              type="success"
              description={"Payee(s) removed"}
            />
          ),
          { position: "top-center" }
        );
      }, 1000);
      setDisableButton(false);
      form.resetFields();
    } catch (error) {
      setTimeout(() => {
        // This timeout is needed to dismiss the loading toast and shows the success toast. If not added, the loading toast is not dismissed and the success toast is shown.
        toast.dismiss(loadingToastId);
        toast.custom(
          () => (
            <EverHotToastMessage type="error" description={error.message} />
          ),
          { position: "top-center" }
        );
      }, 1000);
      setDisableButton(false);
      // console.error(err);
    }
  }

  async function onAddUsers() {
    const loadingToastId = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description={"Adding payee(s)..."}
        />
      ),
      { position: "top-center", duration: Infinity }
    );
    try {
      const validatedValues = await form.validateFields();
      setDisableButton(true);
      const payeesToMap = [];
      validatedValues.payees.map((payee) => {
        payeesToMap.push(payee);
      });
      sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.MANAGE_PAYEE, {
        [ANALYTICS_PROPERTIES.CRYSTAL_SIMULATOR_NAME]: crystalViewName,
        [ANALYTICS_PROPERTIES.ADDL_PAYEES_TO_CRYSTAL_VIEW]: payeesToMap.length,
      });
      mappedPayees.map(({ email }) => {
        payeesToMap.push(email);
      });

      await queryClient.fetchQuery({
        queryKey: ["postMapPayeeToCrystal"],
        queryFn: () =>
          crystalAPI.postMapPayeeToCrystal(crystalViewId, payeesToMap),
      });
      await refetchPayeeList();
      setTimeout(() => {
        // This timeout is needed to dismiss the loading toast and shows the success toast. If not added, the loading toast is not dismissed and the success toast is shown.
        toast.dismiss(loadingToastId);
        toast.custom(
          () => (
            <EverHotToastMessage
              type="success"
              description={"Payee(s) added"}
            />
          ),
          { position: "top-center" }
        );
      }, 1000);
      form.resetFields();
      setDisableButton(false);
    } catch (error) {
      setTimeout(() => {
        // This timeout is needed to dismiss the loading toast and shows the success toast. If not added, the loading toast is not dismissed and the success toast is shown.
        toast.dismiss(loadingToastId);
        toast.custom(
          () => (
            <EverHotToastMessage type="error" description={error.message} />
          ),
          { position: "top-center" }
        );
      }, 1000);
      setDisableButton(false);
    }
  }

  function handleAddClick() {
    form.submit();
  }

  function handleFinish() {
    onAddUsers();
  }

  return payeeList ? (
    <EverModal
      title="Manage Payees"
      visible={isVisible}
      footer={null}
      keyboard={false}
      maskClosable={false}
      onCancel={onClose}
      width="500px"
    >
      <EverForm
        form={form}
        name="horizontal_login"
        layout="inline"
        onFinish={handleFinish}
        className="gap-3"
      >
        <EverForm.Item
          name="payees"
          rules={[
            {
              required: true,
              message: "Atleast 1 payee should be added",
            },
          ]}
          shouldUpdate
          noStyle
        >
          <EverSelect
            mode="multiple"
            showArrow
            maxTagCount={1}
            placeholder="Select Payee"
            everListOptions={true}
            filterOption
            className="grow w-4/5"
          >
            {payeeList.map((payee, index) => {
              const {
                firstName,
                lastName,
                email,
                crystalMapping,
                hasCommissionPlan,
              } = payee;
              const selectedPayeeDisplay = (
                <div className="flex items-center gap-2">
                  <EverGroupAvatar
                    avatars={[
                      {
                        firstName: firstName,
                        lastName: lastName,
                      },
                    ]}
                  />
                  <div>{`${
                    firstName.charAt(0).toUpperCase() + firstName.slice(1)
                  } ${
                    lastName.charAt(0).toUpperCase() + lastName.slice(1)
                  }`}</div>
                </div>
              );
              // No commission Plan
              if (!hasCommissionPlan) {
                return (
                  <Option
                    role="option"
                    key={email}
                    value={email}
                    disabled={true}
                    title={selectedPayeeDisplay}
                  >
                    <NoCommissionPlan
                      key={index}
                      firstName={firstName}
                      lastName={lastName}
                      index={index}
                    />
                  </Option>
                );
              }
              // Disabled option with payee mapped to another view
              if (
                (crystalViewStatus === CRYSTAL_VIEW_STATUS.PUBLISHED ||
                  crystalViewStatus === CRYSTAL_VIEW_STATUS.EDIT_MODE) &&
                crystalMapping.some(
                  ({ viewName, status, viewId }) =>
                    viewId !== crystalViewId &&
                    viewName !== crystalViewName &&
                    status === CRYSTAL_VIEW_STATUS.PUBLISHED
                )
              ) {
                return (
                  <Option
                    role="option"
                    key={email}
                    value={email}
                    disabled={true}
                    title={selectedPayeeDisplay}
                  >
                    <HasViewOption
                      key={email}
                      firstName={firstName}
                      lastName={lastName}
                      crystalMapping={crystalMapping}
                      index={index}
                    />
                  </Option>
                );
              }
              // Selectable Option
              if (!mappedPayees.map(({ email }) => email).includes(email)) {
                return (
                  <Option
                    key={email}
                    value={email}
                    title={selectedPayeeDisplay}
                  >
                    <SelectableOption
                      key={index}
                      firstName={firstName}
                      lastName={lastName}
                      index={index}
                    />
                  </Option>
                );
              }
              return null;
            })}
          </EverSelect>
        </EverForm.Item>
        <EverForm.Item
          noStyle
          className="justify-center items-center"
          shouldUpdate
        >
          {() => (
            <EverButton
              htmlType="submit"
              onClick={handleAddClick}
              disabled={
                disableButton ||
                !form.isFieldsTouched(true) ||
                form.getFieldsError().some(({ errors }) => errors.length)
              }
            >
              Add
            </EverButton>
          )}
        </EverForm.Item>
      </EverForm>
      <Row align="middle" className="mt-6 px-px">
        <Col>
          <div className="flex items-center">
            <UsersIcon className="h-5 w-5" />
            <EverTg.Caption className="ml-2 text-ever-base-content-mid">
              {mappedPayees.length} Payees added to this crystal
            </EverTg.Caption>
          </div>
        </Col>
      </Row>
      <PayeeList
        mappedPayees={mappedPayees}
        disableButton={disableButton}
        onRemoveUser={onRemoveUser}
      />
    </EverModal>
  ) : null;
}

function PayeeList({ mappedPayees, disableButton, onRemoveUser }) {
  return mappedPayees.map(({ firstName, lastName, email }, index) => {
    return (
      <div className="flex gap-3 items-center mt-4 w-full" key={index}>
        <EverListItem
          className="flex justify-between gap-2"
          prepend={
            <EverGroupAvatar
              size="medium"
              avatars={[
                {
                  firstName: firstName,
                  lastName: lastName,
                },
              ]}
            />
          }
          title={`${firstName.charAt(0).toUpperCase() + firstName.slice(1)} ${
            lastName.charAt(0).toUpperCase() + lastName.slice(1)
          }`}
        />
        <div className="grow"></div>
        <EverTooltip
          overlayInnerStyle={{ whiteSpace: "nowrap" }}
          overlayStyle={{ maxWidth: "none" }}
          title={
            mappedPayees.length === 1 || disableButton
              ? "At least one payee is required in the crystal view"
              : ""
          }
        >
          <span>
            <EverButton
              color="primary"
              type="ghost"
              disabled={mappedPayees.length === 1 || disableButton}
              onClick={() => onRemoveUser(email)}
              size="small"
            >
              Remove
            </EverButton>
          </span>
        </EverTooltip>
      </div>
    );
  });
}

function NoCommissionPlan({ firstName, lastName }) {
  const { t } = useTranslation();
  return (
    <EverListItem
      prepend={
        <EverGroupAvatar
          avatars={[
            {
              firstName: firstName,
              lastName: lastName,
              className: "bg-ever-base-400",
            },
          ]}
        />
      }
      className="py-0 px-0"
      title={
        <div className="flex items-center gap-3">
          {`${firstName.charAt(0).toUpperCase() + firstName.slice(1)} ${
            lastName.charAt(0).toUpperCase() + lastName.slice(1)
          }`}{" "}
          <EverTooltip
            variant="dark"
            placement="bottom"
            title={
              <EverTg.Caption className="text-center">
                <EverTg.Heading4>
                  {firstName.charAt(0).toUpperCase() + firstName.slice(1)}{" "}
                  {lastName.charAt(0).toUpperCase() + lastName.slice(1)}
                </EverTg.Heading4>{" "}
                {t("NOT_MAPPED_TO_COMMISSION_PLAN")}
              </EverTg.Caption>
            }
          >
            <InfoCircleIcon className="h-4 w-4" />
          </EverTooltip>
        </div>
      }
    />
  );
}

function HasViewOption({ firstName, lastName, crystalMapping, index }) {
  const { chartColors } = useCurrentTheme();
  return (
    <EverListItem
      prepend={
        <EverGroupAvatar
          avatars={[
            {
              firstName: firstName,
              lastName: lastName,
              style: {
                backgroundColor: chartColors[index % 7],
              },
            },
          ]}
        />
      }
      className="py-0 px-0"
      title={
        <div className="flex items-center gap-3">
          {`${firstName.charAt(0).toUpperCase() + firstName.slice(1)} ${
            lastName.charAt(0).toUpperCase() + lastName.slice(1)
          }`}{" "}
          <EverTooltip
            variant="dark"
            placement="bottom"
            title={
              <EverTg.Caption className="text-center">
                {firstName.charAt(0).toUpperCase() + firstName.slice(1)}{" "}
                {lastName.charAt(0).toUpperCase() + lastName.slice(1)} is
                already part of <br />
                {crystalMapping.map(
                  ({ viewName, status }, index) =>
                    status === CRYSTAL_VIEW_STATUS.PUBLISHED && (
                      <EverTg.Heading4 key={index}>{viewName}</EverTg.Heading4>
                    )
                )}
              </EverTg.Caption>
            }
          >
            <InfoCircleIcon className="h-4 w-4" />
          </EverTooltip>
        </div>
      }
    />
  );
}
function SelectableOption({ firstName, lastName, index }) {
  const { chartColors } = useCurrentTheme();
  return (
    <>
      <EverListItem
        prepend={
          <EverGroupAvatar
            avatars={[
              {
                firstName: firstName,
                lastName: lastName,
                style: {
                  backgroundColor: chartColors[index % 7],
                },
              },
            ]}
          />
        }
        className="py-0 px-0"
        title={`${firstName.charAt(0).toUpperCase() + firstName.slice(1)} ${
          lastName.charAt(0).toUpperCase() + lastName.slice(1)
        }`}
      />
    </>
  );
}
