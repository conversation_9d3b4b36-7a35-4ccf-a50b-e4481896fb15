import { InfoCircleIcon } from "@everstage/evericons/duotone";
import { SendLottie, TrashLottie } from "@everstage/evericons/lotties";
import {
  AlertTriangleIcon,
  CheckIcon,
  EditIcon,
  PlusSquareIcon,
  ChevronDownIcon,
  XCloseIcon,
  LightbulbIcon,
  EditPencilAltIcon,
  PlusIcon,
} from "@everstage/evericons/outlined";
import { CheckCircleIcon } from "@everstage/evericons/solid";
import { Dropdown, Menu, Alert } from "antd";
import { isEmpty, isEqual } from "lodash";
import React, { useEffect, useState } from "react";
import { DragSource, DropTarget } from "react-dnd";
import { useQuery, useQueryClient } from "react-query";
import { useParams, useNavigate, Link } from "react-router-dom";
import { useRecoilValue, useSetRecoilState } from "recoil";
import { twMerge } from "tailwind-merge";
import { v4 as uuidv4 } from "uuid";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import { ANALYTICS_EVENTS, ANALYTICS_PROPERTIES } from "~/Enums";
import { navPortalAtom, breadcrumbAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverBreadcrumbPortal,
  EverButton,
  EverSelect,
  EverListItem,
  EverTooltip,
  EverTabs,
  EverInput,
  EverBadge,
  EverModal,
  EverGroupAvatar,
  EverTg,
  EverLabel,
  toast,
  EverHotToastMessage,
  EverHotToastBanner,
  EverLoader,
} from "~/v2/components";
// import EverNavPortal from "~/v2/components/EverNavPortal";
import { useCrystalAPI } from "~/v2/features/crystal/api";
import { sadCat } from "~/v2/images";

import { CrystalViewSetupDrawer } from "./CrystalViewSetupDrawer";
import { ManageUsersModal } from "./ManageUsersModal";
import { CrystalTable } from "../CrystalTable";
import { CRYSTAL_VIEW_STATUS } from "../Utils";

const { TabPane } = EverTabs;
const { Option } = EverSelect;

export function CrystalAdminViewSetup() {
  const [viewChangeKey, setViewChangeKey] = useState(uuidv4());
  let { simulatorId } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [showForm, setshowForm] = useState(false);
  // This will be used for crystal table edit mode
  const [formInitialValue, setFormInitialValue] = useState(null);
  const [crystalView, setCrystalView] = useState(null);
  const navPortalLocation = useRecoilValue(navPortalAtom);
  const setBreadcrumbName = useSetRecoilState(breadcrumbAtom);
  // Used to open/close view name edit
  const [isViewNameEditMode, setIsViewNameEditMode] = useState(false);

  // Contains edited View Name
  const [editedViewName, setEditedViewName] = useState(null);
  const [startFetching, setStartFetching] = useState(false);
  const [selectedTab, setSelectedTab] = useState(null);
  const [showAlreadyAddedPayee, setAlreadyAddedPayee] = useState(null);
  const [
    isConfirmationModalVisible,
    setIsConfirmationModalVisibleCreateViewModal,
  ] = useState(false);
  const [isSuccessModalVisible, setIsSuccessModalVisible] = useState(false);
  const [
    isDeleteConfirmationModalVisible,
    setIsDeleteConfirmationModalVisible,
  ] = useState(false);

  const [isEditConfimationModalVisible, setIsEditConfimationModalVisible] =
    useState(false);

  const [
    isCancelEditConfirmationModalVisible,
    setIsCancelEditConfirmationModalVisible,
  ] = useState(false);

  const [selectedPayee, setSelectedPayee] = useState();
  const [usedDsList, setUsedDsList] = useState([]);
  const crystalAPI = useCrystalAPI(simulatorId);
  const {
    data,
    isLoading,
    refetch: refetchCrystalView,
  } = useQuery(["getAdminCrystalView", simulatorId], () =>
    crystalAPI.fetchAdminCrystalView()
  );

  useEffect(() => {
    if (data) {
      if (data.error) {
        //redirect
        navigate("/crystal/page-not-found");
      } else {
        setBreadcrumbName({
          index: 1,
          title:
            data.data?.crystalViewName.charAt(0).toUpperCase() +
            data.data?.crystalViewName.slice(1),
        });
        setCrystalView(data.data);
        setSelectedPayee(data.data.payees[0]?.emailId);
        setStartFetching(true);
        setEditedViewName(data.data.crystalViewName);
        if (data.data.settingsData.length > 0) {
          const defaultTabId = data.data.settingsData[0].crystalTableId;
          const tabExists = data.data.settingsData.some(
            (tab) => tab.crystalTableId === selectedTab
          );
          setSelectedTab(tabExists ? selectedTab : defaultTabId);
          setUsedDsList(
            data.data.settingsData
              .filter((tab) => tab.sourceType === "datasheet")
              .map((tab) => tab.sourceId)
          );
        }
      }
    }
  }, [data]);

  // Manage User Modal
  const [selectedTableIdFromMenu, setSelectedTableIdFromMenu] = useState();
  const [isManageUsersModalVisible, setIsManageUsersModalVisible] =
    useState(false);

  const [disabledPublishBtn, setDisabledPublishBtn] = useState(false);
  const [disableAllButtons, setDisableAllButtons] = useState(false);

  // api to get the crystal view definition
  const [tableViewDefinition, setTableViewDefinition] = useState();
  const { data: viewDefinitionData, refetch: refetchTableViewDefinition } =
    useQuery(
      ["getcrystalTableView", selectedPayee],
      () => crystalAPI.fetchAdminCrystalViewDefinition(selectedPayee),
      { enabled: crystalView == undefined ? false : true }
    );

  useEffect(() => {
    if (viewDefinitionData) {
      setTableViewDefinition(viewDefinitionData.crystalTableViewList);
    }
  }, [viewDefinitionData]);
  // api to fetch custom object data
  const { data: objectData, isLoading: isObjectDataLoading } = useQuery(
    ["getCustomObjectList"],
    () => crystalAPI.fetchSourceMetaData(),
    { enabled: startFetching }
  );

  // api to fetch filterdata which is used in displayConditions
  const { data: filterData, isLoading: isFilterDataLoading } = useQuery(
    ["getFilters"],
    () => crystalAPI.fetchFilters(),
    { enabled: startFetching }
  );

  function onCreateCrystalTable() {
    refetchTableViewDefinition();
    setFormInitialValue({});
    setshowForm(false);
    setViewChangeKey(uuidv4());
  }

  function onEditCrystalTable() {
    refetchTableViewDefinition();
    setFormInitialValue({});
    setshowForm(false);
    setViewChangeKey(uuidv4());
  }

  function EditingPublishedCrystalWarning() {
    return (
      crystalView.status === CRYSTAL_VIEW_STATUS.EDIT_MODE && (
        <div className="flex">
          <EverHotToastBanner
            className="shadow-none"
            type="info"
            description="You're now editing a published Crystal view. Once you're done, hit
        'Update' to ensure your payees see the latest version of this Crystal
        view"
          ></EverHotToastBanner>
        </div>
      )
    );
  }

  async function handleBeforePublish() {
    const loadingToastId = toast.custom(
      () => <EverHotToastMessage type="loading" description="Validating..." />,
      { position: "top-center", duration: Infinity }
    );
    try {
      const response = await queryClient.fetchQuery({
        queryKey: ["fetchAlreadyAddedPayee", crystalView?.crystalViewId],
        queryFn: () => crystalAPI.fetchAlreadyAddedPayee(),
      });
      if (!response.data.canPublish) {
        setAlreadyAddedPayee(
          _prepareAlreadyAddedPayee(response.data, setDisabledPublishBtn)
        );
        toast.dismiss(loadingToastId);
      }
      setIsConfirmationModalVisibleCreateViewModal(true);
      toast.dismiss(loadingToastId);
    } catch (error) {
      toast.dismiss(loadingToastId);
      toast.custom(
        () => <EverHotToastMessage type="error" description={error.message} />,
        { position: "top-center" }
      );
    }
  }

  function Header() {
    const { accessToken } = useAuthStore();
    const { status, payees, crystalViewId, settingsData } = crystalView;
    return (
      <div className="flex items-center justify-end">
        <div className="py-1 flex items-center gap-3">
          <EverLabel className="mr-0" required={false}>
            Showing for
          </EverLabel>
          <EverSelect
            className="w-48"
            defaultValue={selectedPayee}
            onChange={(value) => {
              const selectedPayeeObject = data.data.payees.filter(
                (user) => user.emailId == value
              );
              if (selectedPayeeObject.length > 0) {
                sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.SHOWING_FOR, {
                  [ANALYTICS_PROPERTIES.PAYEE_NAME]:
                    selectedPayeeObject[0]?.firstName +
                    " " +
                    selectedPayeeObject[0]?.lastName,
                });
              }
              setSelectedPayee(value);
            }}
            everListOptions={true}
            dropdownMatchSelectWidth={300}
            size="small"
          >
            {payees.map(({ firstName, lastName, emailId }, index) => {
              return (
                <Option
                  key={index}
                  value={emailId}
                  title={
                    <div className="flex items-center gap-2 max-w-full absolute inset-0 pr-4">
                      <EverGroupAvatar
                        avatars={[
                          {
                            firstName: firstName,
                            lastName: lastName,
                          },
                        ]}
                      />
                      <div className="max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                        {`${
                          firstName.charAt(0).toUpperCase() + firstName.slice(1)
                        } ${
                          lastName.charAt(0).toUpperCase() + lastName.slice(1)
                        }`}
                      </div>
                    </div>
                  }
                >
                  <EverListItem
                    prepend={
                      <EverGroupAvatar
                        avatars={[
                          {
                            firstName: firstName,
                            lastName: lastName,
                          },
                        ]}
                      />
                    }
                    className="py-0 px-0"
                    title={`${
                      firstName.charAt(0).toUpperCase() + firstName.slice(1)
                    } ${lastName.charAt(0).toUpperCase() + lastName.slice(1)}`}
                  />
                </Option>
              );
            })}
          </EverSelect>
          <EverTooltip placement="bottom" title="Manage Payees">
            <EverButton.Icon
              title="Manage Payees"
              color="base"
              type="ghost"
              onClick={() => setIsManageUsersModalVisible(true)}
              disabled={disableAllButtons}
              icon={
                <PlusSquareIcon className="h-5 w-5 text-ever-base-content-mid" />
              }
              size="small"
            ></EverButton.Icon>
          </EverTooltip>

          <Link
            to={`/crystal/preview/${crystalViewId}/${selectedPayee}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            <EverButton
              color="base"
              type="ghost"
              disabled={settingsData.length === 0 ? true : false}
              size="small"
            >
              Preview as payee
            </EverButton>
          </Link>
          {status === CRYSTAL_VIEW_STATUS.EDIT_MODE && (
            <>
              <EverButton
                color="base"
                type="ghost"
                onClick={() => setIsCancelEditConfirmationModalVisible(true)}
                disabled={disableAllButtons}
                size="small"
              >
                Cancel
              </EverButton>
              <EverButton
                onClick={() => setIsEditConfimationModalVisible(true)}
                disabled={disableAllButtons}
                size="small"
              >
                Update
              </EverButton>
            </>
          )}
          {status === CRYSTAL_VIEW_STATUS.DRAFT && (
            <EverButton
              onClick={handleBeforePublish}
              disabled={
                settingsData.length === 0 ? true : false || disableAllButtons
              }
              size="small"
            >
              Publish
            </EverButton>
          )}
          {status === CRYSTAL_VIEW_STATUS.PUBLISHED && (
            <EverButton
              onClick={onClickEditPublishedView}
              disabled={disableAllButtons}
              size="small"
            >
              Edit
            </EverButton>
          )}
        </div>
      </div>
    );
  }

  const onClickAddTable = (_, action) => {
    if (action === "add") {
      setFormInitialValue(null);
      setshowForm(true);
    }
  };

  async function updateViewOrder(updatedCrystalView) {
    const loadingToastId = toast.custom(
      () => (
        <EverHotToastMessage type="loading" description="Updating Order..." />
      ),
      { position: "top-center", duration: Infinity }
    );

    try {
      await queryClient.fetchQuery({
        queryKey: ["postCreateCrystalView"],
        queryFn: () => crystalAPI.updateCrystalViewOrder(updatedCrystalView),
      });
      refetchCrystalView();
      refetchTableViewDefinition();
      toast.dismiss(loadingToastId);
      toast.custom(
        () => (
          <EverHotToastMessage
            type="success"
            description="Crystal View Order Updated"
          />
        ),
        { position: "top-center" }
      );
    } catch (error) {
      toast.dismiss(loadingToastId);
      toast.custom(
        () => (
          <EverHotToastMessage
            type="error"
            description="Something went wrong while updating the order."
          />
        ),
        { position: "top-center" }
      );
    }
  }

  async function onChangeCrystalViewName() {
    if (editedViewName === crystalView.crystalViewName) {
      setIsViewNameEditMode(false);
      return;
    }
    const loadingToastId = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description="Updating Crystal View..."
        />
      ),
      { position: "top-center", duration: Infinity }
    );

    try {
      await queryClient.fetchQuery({
        queryKey: ["postEditCrystalViewName"],
        queryFn: () => crystalAPI.postEditCrystalViewName(editedViewName),
      });

      refetchCrystalView();
      toast.dismiss(loadingToastId);
      toast.custom(
        () => (
          <EverHotToastMessage
            type="success"
            description="Crystal View Updated"
          />
        ),
        { position: "top-center" }
      );

      setIsViewNameEditMode(false);
    } catch (error) {
      toast.dismiss(loadingToastId);
      toast.custom(
        () => <EverHotToastMessage type="error" description={error.message} />,
        { position: "top-center" }
      );
    }
  }

  async function onConfirmDeleteTable(crystalTableId) {
    setDisableAllButtons(true);
    const loadingToastId = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description="Deleting the Crystal Table..."
        />
      ),
      { position: "top-center", duration: Infinity }
    );
    try {
      await queryClient.fetchQuery({
        queryKey: ["postDeleteTableFromView"],
        queryFn: () => crystalAPI.postDeleteTableFromView(crystalTableId),
      });
      if (crystalView.settingsData.length > 0) {
        setSelectedTab(crystalView.settingsData[0].crystalTableId);
      }
      refetchCrystalView();
      refetchTableViewDefinition();
      setDisableAllButtons(false);
      setIsDeleteConfirmationModalVisible(false);
      toast.dismiss(loadingToastId);
      toast.custom(
        () => (
          <EverHotToastMessage
            type="success"
            description="Crystal Table successfully deleted..."
          />
        ),
        { position: "top-center" }
      );
    } catch (error) {
      setDisableAllButtons(false);
      toast.dismiss(loadingToastId);
      toast.custom(
        () => <EverHotToastMessage type="error" description={error.message} />,
        { position: "top-center" }
      );
    }
  }

  async function onClickSaveEditedView() {
    setDisableAllButtons(true);
    const loadingToastId = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description="Updating Crystal Simulator..."
        />
      ),
      { position: "top-center", duration: Infinity }
    );
    try {
      await queryClient.fetchQuery({
        queryKey: ["postUpdateOrCancelEditedView"],
        queryFn: () => crystalAPI.postUpdateOrCancelEditedView("update"),
      });
      setDisableAllButtons(false);
      setIsEditConfimationModalVisible(false);
      toast.dismiss(loadingToastId);
      // navigate(`/crystal/${data.crystalViewId}`);
      refetchCrystalView();
    } catch (error) {
      setDisableAllButtons(false);
      setIsEditConfimationModalVisible(false);
      toast.dismiss(loadingToastId);
      toast.custom(
        () => <EverHotToastMessage type="error" description={error.message} />,
        { position: "top-center" }
      );
    }
  }

  async function onClickEditPublishedView() {
    setDisableAllButtons(true);
    const loadingToastId = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description="Cloning the Crystal View..."
        />
      ),
      { position: "top-center", duration: Infinity }
    );
    try {
      const data = await queryClient.fetchQuery({
        queryKey: ["fetchEditModeClonedViewId"],
        queryFn: () => crystalAPI.fetchEditModeClonedViewId(),
      });
      toast.dismiss(loadingToastId);
      setDisableAllButtons(false);
      navigate(`/crystal/${data.clonedViewId}`);
    } catch (error) {
      setDisableAllButtons(false);
      toast.dismiss(loadingToastId);
      toast.custom(
        () => <EverHotToastMessage type="error" description={error.message} />,
        { position: "top-center" }
      );
    }
  }

  async function onCancelPublishedViewEditMode() {
    setDisableAllButtons(true);
    const loadingToastId = toast.custom(
      () => <EverHotToastMessage type="loading" description="Loading..." />,
      { position: "top-center", duration: Infinity }
    );
    try {
      const data = await queryClient.fetchQuery({
        queryKey: ["postUpdateOrCancelEditedView"],
        queryFn: () => crystalAPI.postUpdateOrCancelEditedView("cancel"),
      });
      setDisableAllButtons(false);
      setIsEditConfimationModalVisible(false);
      toast.dismiss(loadingToastId);
      navigate(`/crystal/${data.crystalViewId}`);
      refetchCrystalView();
    } catch (error) {
      setDisableAllButtons(false);
      setIsEditConfimationModalVisible(false);
      toast.dismiss(loadingToastId);
      toast.custom(
        () => <EverHotToastMessage type="error" description={error.message} />,
        { position: "top-center" }
      );
    }
  }

  async function onPublishViewConfirmation() {
    setDisableAllButtons(true);
    const loadingToastId = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description="Publishing Crystal View..."
        />
      ),
      { position: "top-center", duration: Infinity }
    );
    try {
      await queryClient.fetchQuery({
        queryKey: ["postPublishCrystalView"],
        queryFn: () => crystalAPI.postPublishCrystalView(),
      });
      refetchCrystalView();
      setDisableAllButtons(false);
      setIsConfirmationModalVisibleCreateViewModal(false);
      setIsSuccessModalVisible(true);
      toast.dismiss(loadingToastId);
      toast.custom(
        () => (
          <EverHotToastMessage
            type="success"
            description="Crystal View Updated"
          />
        ),
        { position: "top-center" }
      );
    } catch (error) {
      setDisableAllButtons(false);
      toast.dismiss(loadingToastId);
      toast.custom(
        () => <EverHotToastMessage type="error" description={error.message} />,
        { position: "top-center" }
      );
    }
  }

  function onChangeTableTab(key) {
    setSelectedTab(key);
  }

  function onTabDropCompleted(newOrder) {
    const updatedCrystalSettingsData = [];
    newOrder.forEach((tableId) => {
      const table = crystalView.settingsData.find(
        (table) => table.crystalTableId === tableId
      );
      updatedCrystalSettingsData.push(table);
    });
    updateViewOrder({
      crystalViewId: crystalView.crystalViewId,
      settingsData: updatedCrystalSettingsData,
    });
  }

  return (
    <EverLoader
      indicatorType="spinner"
      spinning={
        isLoading ||
        editedViewName === null ||
        crystalView === null ||
        !selectedPayee ||
        !tableViewDefinition
      }
      className="crystal"
    >
      {editedViewName !== null &&
        crystalView !== null &&
        selectedPayee &&
        tableViewDefinition && (
          <>
            <EverBreadcrumbPortal>
              <div className="flex items-center gap-6">
                <div>
                  {isViewNameEditMode ? (
                    <div className="flex items-center">
                      {/* <EverInput className="w-64" /> */}
                      <EverInput.Group
                        value={editedViewName}
                        onChange={(e) => setEditedViewName(e.target.value)}
                        compact={true}
                        controls={[
                          <EverButton
                            key={1}
                            type="ghost"
                            size="medium"
                            onClick={onChangeCrystalViewName}
                            disabled={disableAllButtons}
                          >
                            <CheckIcon className="h-4 w-4" />
                          </EverButton>,
                          <EverButton
                            key={2}
                            color="error"
                            type="ghost"
                            size="medium"
                            onClick={() => setIsViewNameEditMode()}
                            disabled={disableAllButtons}
                          >
                            {<XCloseIcon className="h-4 w-4" />}
                          </EverButton>,
                        ]}
                        className="w-72"
                      />
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <EverTg.Heading3>
                        {crystalView.crystalViewName.charAt(0).toUpperCase() +
                          crystalView.crystalViewName.slice(1)}
                      </EverTg.Heading3>
                      {crystalView.crystalViewName && (
                        <EverTooltip title="Edit Crystal view name">
                          <div
                            className="flex items-center cursor-pointer"
                            onClick={() => setIsViewNameEditMode(true)}
                            disabled={
                              crystalView.status ===
                                CRYSTAL_VIEW_STATUS.PUBLISHED ||
                              disableAllButtons
                            }
                          >
                            <EditPencilAltIcon className="h-5 w-5 text-ever-base-content-mid" />
                          </div>
                        </EverTooltip>
                      )}
                    </div>
                  )}
                </div>
                <div>
                  {crystalView.status === CRYSTAL_VIEW_STATUS.DRAFT && (
                    //Add draft icon here
                    <EverBadge
                      icon={<EditIcon className="h-3 w-3" />}
                      type="warning"
                      title="Draft"
                    />
                  )}
                  {crystalView.status === CRYSTAL_VIEW_STATUS.PUBLISHED && (
                    // Add published Icon here
                    <EverBadge
                      icon={<CheckCircleIcon className="h-3 w-3" />}
                      type="success"
                      title="Published"
                    ></EverBadge>
                  )}
                </div>
              </div>

              {crystalView.status === CRYSTAL_VIEW_STATUS.EDIT_MODE && (
                <>
                  <span className="ml-2">
                    <EverBadge
                      icon={null}
                      outline
                      title="Edit mode"
                      type="warning"
                    />
                  </span>
                </>
              )}
            </EverBreadcrumbPortal>

            <ManageUsersModal
              crystalViewName={crystalView.crystalViewName}
              crystalViewId={crystalView.crystalViewId}
              crystalViewStatus={crystalView.status}
              isVisible={isManageUsersModalVisible}
              onClose={() => {
                refetchCrystalView();
                setIsManageUsersModalVisible(false);
              }}
            />
            <PublishConfirmationModal
              crystalViewName={crystalView.crystalViewName}
              isConfirmationModalVisible={isConfirmationModalVisible}
              onConfirm={onPublishViewConfirmation}
              onCancel={() =>
                setIsConfirmationModalVisibleCreateViewModal(false)
              }
              disableAllButtons={disableAllButtons}
              showAlreadyAddedPayee={showAlreadyAddedPayee}
              disabledPublishBtn={disabledPublishBtn}
            />
            <DeleteConfirmationModal
              isDeleteConfirmationModalVisible={
                isDeleteConfirmationModalVisible
              }
              status={crystalView.status}
              onConfirmDelete={() => {
                onConfirmDeleteTable(selectedTableIdFromMenu);
              }}
              onCancelDelete={() => setIsDeleteConfirmationModalVisible(false)}
              disableAllButtons={disableAllButtons}
            />
            {crystalView.status === CRYSTAL_VIEW_STATUS.EDIT_MODE && (
              <>
                <CancelEditConfirmationModal
                  isVisible={isCancelEditConfirmationModalVisible}
                  onConfirm={() => {
                    setIsCancelEditConfirmationModalVisible(false);
                    onCancelPublishedViewEditMode();
                  }}
                  onCancel={() =>
                    setIsCancelEditConfirmationModalVisible(false)
                  }
                  disableAllButtons={disableAllButtons}
                />
                <UpdateConfirmationModal
                  noTableExist={crystalView.settingsData.length === 0}
                  isVisible={isEditConfimationModalVisible}
                  onConfirm={() => {
                    setIsEditConfimationModalVisible(false);
                    onClickSaveEditedView();
                  }}
                  onCancel={() => setIsEditConfimationModalVisible(false)}
                  disableAllButtons={disableAllButtons}
                />
              </>
            )}
            <PublishSuccessModal
              crystalViewName={crystalView.crystalViewName}
              isSuccessModalVisible={isSuccessModalVisible}
              onClose={() => setIsSuccessModalVisible(false)}
            />
            {isObjectDataLoading || isFilterDataLoading ? null : (
              <CrystalViewSetupDrawer
                showForm={showForm}
                crystalTableId={selectedTableIdFromMenu}
                crystalViewId={crystalView.crystalViewId}
                refetchCrystalView={refetchCrystalView}
                onCreate={onCreateCrystalTable}
                onEdit={onEditCrystalTable}
                filterData={filterData?.data}
                initialValues={formInitialValue}
                objectDataList={objectData?.data}
                setSelectedTab={setSelectedTab}
                onCancel={(form) => {
                  form.resetFields();
                  setFormInitialValue({});
                  setshowForm(false);
                }}
                usedDsList={usedDsList}
              />
            )}
            <DraggableTabs
              tabBarExtraContent={<Header />}
              activeKey={selectedTab}
              onChange={onChangeTableTab}
              type="editable-card"
              animated={{ inkBar: true }}
              isDraggable={
                crystalView.status === CRYSTAL_VIEW_STATUS.EDIT_MODE ||
                crystalView.status === CRYSTAL_VIEW_STATUS.DRAFT
              }
              navPortalLocation={navPortalLocation}
              warningComponent={<EditingPublishedCrystalWarning />}
              onEdit={onClickAddTable}
              hideAdd={
                crystalView.status === CRYSTAL_VIEW_STATUS.PUBLISHED ||
                crystalView.settingsData.length === 0
              }
              isSettingsDataEmpty={crystalView.settingsData.length === 0}
              addIcon={
                <span className="w-6 h-6 rounded-lg bg-ever-primary-lite flex items-center justify-center">
                  <PlusIcon className="h-4 w-4 text-ever-primary" />
                </span>
              }
              // size="small"
              onTabDropCompleted={(newOrder) => onTabDropCompleted(newOrder)}
              destroyInactiveTabPane={true}
              className={twMerge(
                !(
                  crystalView.status === CRYSTAL_VIEW_STATUS.EDIT_MODE ||
                  crystalView.status === CRYSTAL_VIEW_STATUS.DRAFT
                ) &&
                  "[&>.ant-tabs-nav>.ant-tabs-nav-wrap>.ant-tabs-nav-list>.ant-tabs-tab]:cursor-default"
              )}
            >
              {
                // crystalView and tableViewDefinition, comes from 2 different api triggered at the same time.
                // Check if crystalView.settingsData has data
                crystalView.settingsData.length > 0 &&
                  crystalView.settingsData.map((tableData, index) => {
                    const {
                      crystalTableName,
                      crystalTableDescription,
                      crystalTableId,
                    } = tableData;
                    return (
                      // make sure tableViewDefinition is also updated with crstalView. If not updated tableViewDefinition[index] will be none
                      tableViewDefinition[index] && (
                        <TabPane
                          tab={
                            <span className="flex items-center gap-2">
                              <span>{crystalTableName}</span>
                              <EverTooltip
                                placement="bottom"
                                title={crystalTableDescription}
                              >
                                <InfoCircleIcon className="h-4 w-4 text-ever-base-content-mid" />
                              </EverTooltip>
                              {crystalView.status !==
                              CRYSTAL_VIEW_STATUS.PUBLISHED ? (
                                <TablePopoverMenu
                                  // key={crystalTableId}
                                  status={crystalView.status}
                                  tableData={tableData}
                                  setSelectedTableIdFromMenu={(value) =>
                                    setSelectedTableIdFromMenu(value)
                                  }
                                  setFormInitialValue={(data) =>
                                    setFormInitialValue(data)
                                  }
                                  setshowForm={(data) => setshowForm(data)}
                                  setIsDeleteConfirmationModalVisible={(data) =>
                                    setIsDeleteConfirmationModalVisible(data)
                                  }
                                  crystalTableView={
                                    tableViewDefinition[index].crystalTableView
                                  }
                                />
                              ) : null}
                            </span>
                          }
                          key={crystalTableId}
                          closeIcon={<></>}
                        >
                          {tableViewDefinition.length > 0 &&
                            (isEmpty(
                              tableViewDefinition[index].crystalTableView
                            ) ? (
                              <div className="h-96 flex justify-center items-center bg-ever-warning-lite text-ever-warning-lite-content">
                                The datasheet that was used to create this view
                                has been deleted.
                              </div>
                            ) : (
                              <CrystalTable
                                key={
                                  crystalTableId + selectedPayee + viewChangeKey
                                }
                                queryKey={
                                  crystalTableId + selectedPayee + viewChangeKey
                                }
                                crystalTableId={crystalTableId}
                                columnDefs={
                                  tableViewDefinition[index]?.crystalTableView
                                }
                                payeeEmail={selectedPayee}
                                previewMode={true}
                                isActive
                                handleDataLoaded={() => {}}
                              />
                            ))}
                        </TabPane>
                      )
                    );
                  })
              }
            </DraggableTabs>
            {crystalView.settingsData.length === 0 ? (
              <div className="w-full pl-2 pr-2 min-h-full flex flex-col gap-1 items-center justify-center">
                <img src={sadCat} />
                <EverTg.Heading2 className="text-ever-base-content">
                  Create a view to customize
                </EverTg.Heading2>
                <EverTg.Description>
                  To see the look and feel of how you want to display and
                  process the data in the simulator.
                </EverTg.Description>
                <EverButton
                  prependIcon={<PlusSquareIcon className="h-4 w-4" />}
                  onClick={() => {
                    setFormInitialValue(null);
                    setshowForm(true);
                  }}
                  disabled={disableAllButtons}
                  className="mt-3"
                >
                  Add View
                </EverButton>
              </div>
            ) : null}
          </>
        )}
    </EverLoader>
  );
}

function TablePopoverMenu({
  tableData,
  status,
  setFormInitialValue,
  setshowForm,
  setSelectedTableIdFromMenu,
  setIsDeleteConfirmationModalVisible,
  crystalTableView,
}) {
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);

  function onClickTableMenu(option, tableData) {
    if (option === "edit" + tableData.crystalTableId) {
      setSelectedTableIdFromMenu(tableData.crystalTableId);
      setFormInitialValue(tableData);
      setshowForm(true);
    }
    if (option === "delete" + tableData.crystalTableId) {
      setSelectedTableIdFromMenu(tableData.crystalTableId);
      setIsDeleteConfirmationModalVisible(true);
    }
  }
  const userMenu = (
    <Menu
      key={tableData.crystalTableId}
      onClick={(data) => onClickTableMenu(data.key, tableData)}
    >
      <Menu.Item
        key={"edit" + tableData.crystalTableId}
        disabled={
          status === CRYSTAL_VIEW_STATUS.PUBLISHED || isEmpty(crystalTableView)
        }
        className="text-ever-base-content hover:text-ever-base-content"
      >
        Edit Configuration
      </Menu.Item>
      <Menu.Item
        key={"delete" + tableData.crystalTableId}
        disabled={status === CRYSTAL_VIEW_STATUS.PUBLISHED}
        className="text-ever-base-content text-ever-error hover:text-ever-error hover:!bg-ever-error-lite"
      >
        Delete
      </Menu.Item>
    </Menu>
  );

  return (
    <Dropdown
      key={tableData.crystalTableId}
      overlay={userMenu}
      placement="bottomLeft"
      arrow
      onVisibleChange={(visible) => setIsDropdownVisible(visible)}
    >
      <span className="relative group">
        <ChevronDownIcon
          className={twMerge(
            "h-5 w-5 text-ever-base-content-mid",
            isDropdownVisible && "text-ever-primary"
          )}
        />
        {/*<SettingsIcon
          className={twMerge(
            "h-5 w-5 text-ever-primary group-hover:hidden",
            isDropdownVisible && "hidden"
          )}
        />
        <SettingsIconSolid
          className={twMerge(
            "group-hover:block hidden h-5 w-5 text-ever-primary",
            isDropdownVisible && "block"
          )}
        />*/}
      </span>
    </Dropdown>
  );
}

/** Applies maps for the tabs object */
// function _getMappedTabs(crystalTableViewList) {
//   const newTabsList = crystalTableViewList.map((obj) => {
//     return {
//       id: obj.crystalTableId,
//       name: obj.crystalTableName,
//       displayName: obj.crystalTableName || obj.crystalTableName,
//       active: false,
//       icon: "InfoIcon",
//       colDefs: obj.crystalTableView,
//       tooltip: "Some text here for " + obj.crystalTableName,
//     };
//   });
//   newTabsList[0].active = true;
//   return newTabsList;
// }

function PublishConfirmationModal({
  crystalViewName,
  isConfirmationModalVisible,
  onConfirm,
  disableAllButtons,
  onCancel,
  showAlreadyAddedPayee,
  disabledPublishBtn,
}) {
  return (
    <EverModal.Confirm
      type="warning"
      visible={isConfirmationModalVisible}
      icon={<SendLottie loop autoplay className="w-8 h-8" />}
      confirmationButtons={[
        <EverButton
          key={1}
          color="base"
          onClick={onCancel}
          disabled={disableAllButtons}
        >
          Cancel
        </EverButton>,
        <EverButton
          key={2}
          onClick={onConfirm}
          disabled={disabledPublishBtn || disableAllButtons}
        >
          Yes, Publish
        </EverButton>,
      ]}
      title={
        <>
          <div>Are you sure you want to publish</div>
          <div>
            <strong>{crystalViewName}</strong> crystal?
          </div>
        </>
      }
      subtitle={
        <>
          <div className="flex items-start gap-2">
            <AlertTriangleIcon className="h-6 w-6 text-ever-warning" />
            <div>
              {showAlreadyAddedPayee
                ? showAlreadyAddedPayee.message
                : "All the users added will have access to crystal"}
            </div>
          </div>
          {showAlreadyAddedPayee?.list.map(
            ({ payeeName, crystalViewName }, i) => {
              return (
                <PayeeListItem
                  key={i}
                  payeeName={payeeName}
                  crystalViewName={crystalViewName}
                />
              );
            }
          )}
        </>
      }
    ></EverModal.Confirm>
  );
}

function CancelEditConfirmationModal({
  isVisible,
  onCancel,
  onConfirm,
  disableAllButtons,
}) {
  return (
    <EverModal.Confirm
      visible={isVisible}
      type="warning"
      title="Are you sure you want to cancel all the changes?"
      subtitle="You cannot undo this action"
      confirmationButtons={[
        <EverButton
          key={1}
          color="base"
          onClick={onCancel}
          disabled={disableAllButtons}
        >
          Cancel
        </EverButton>,
        <EverButton key={2} onClick={onConfirm} disabled={disableAllButtons}>
          Confirm
        </EverButton>,
      ]}
    />
  );
}

function UpdateConfirmationModal({
  isVisible,
  noTableExist,
  onConfirm,
  onCancel,
  disableAllButtons,
}) {
  return (
    <EverModal.Confirm
      visible={isVisible}
      title={
        noTableExist
          ? "Are you sure you want to make this update?"
          : "Do you want to update the changes?"
      }
      confirmationButtons={[
        <EverButton
          key={1}
          color="base"
          onClick={onCancel}
          disabled={disableAllButtons}
        >
          Cancel
        </EverButton>,
        <EverButton key={2} onClick={onConfirm} disabled={disableAllButtons}>
          Confirm
        </EverButton>,
      ]}
      subtitle={
        noTableExist ? (
          `A simulator with no table will automatically move to the 'Draft'
      state. All the payees added to this simulator will lose access.`
        ) : (
          <Alert
            type="warning"
            message={
              <>
                <div className="mb-2 text-left flex items-center gap-2">
                  <LightbulbIcon className="w-6 h-6 text-ever-warning" />
                  <EverTg.Heading4>Pro tip</EverTg.Heading4>
                </div>
                <div className="text-left">
                  Before updating the changes, we recommend that you
                  <EverTg.Text className="font-medium">{` 'Preview as Payee' `}</EverTg.Text>
                  to ensure the simulator works as intended for all payees.
                </div>
              </>
            }
          />
        )
      }
      type={"warning"}
      icon={
        noTableExist ? (
          <AlertTriangleIcon className="w-8 h-8" />
        ) : (
          <SendLottie loop autoplay className="w-8 h-8" />
        )
      }
    />
  );
}

function DeleteConfirmationModal({
  isDeleteConfirmationModalVisible,
  onConfirmDelete,
  status,
  onCancelDelete,
  disableAllButtons,
}) {
  return (
    <EverModal.Confirm
      icon={<TrashLottie loop autoplay className="w-8 h-8" />}
      iconContainerClasses="bg-ever-error-lite"
      visible={isDeleteConfirmationModalVisible}
      title="Are you sure you want to permanently delete the table?"
      subtitle={
        status === CRYSTAL_VIEW_STATUS.EDIT_MODE
          ? "This table is a part of a published simulator. After you delete it, your payees can no longer access it. "
          : "You can't undo this action"
      }
      confirmationButtons={[
        <EverButton
          key={1}
          color="base"
          onClick={onCancelDelete}
          disabled={disableAllButtons}
        >
          Cancel
        </EverButton>,
        <EverButton
          key={2}
          onClick={onConfirmDelete}
          disabled={disableAllButtons}
        >
          Confirm
        </EverButton>,
      ]}
    />
  );
}

function PublishSuccessModal({
  crystalViewName,
  isSuccessModalVisible,
  onClose,
}) {
  return (
    <EverModal.Confirm
      type="success"
      visible={isSuccessModalVisible}
      title="Wow, Congratulations!"
      subtitle={`${crystalViewName} crystal are all set!`}
      confirmationButtons={[
        <EverButton key={1} onClick={onClose}>
          Close
        </EverButton>,
      ]}
    />
  );
}

function PayeeListItem({ payeeName, crystalViewName }) {
  let { firstName, lastName } = payeeName;
  return (
    <div className="flex items-center py-1 gap-2">
      <EverGroupAvatar
        avatars={[
          {
            firstName: firstName,
            lastName: lastName,
          },
        ]}
      />
      <div>
        <EverTg.Heading4>
          {firstName.charAt(0).toUpperCase() + firstName.slice(1)}{" "}
          {lastName.charAt(0).toUpperCase() + lastName.slice(1)}
        </EverTg.Heading4>
        <EverTg.Caption className="text-ever-base-content-mid">
          is a part of {crystalViewName}
        </EverTg.Caption>
      </div>
    </div>
  );
}

/** We will pass the response from backend here and map it to the data required by the view component */
function _prepareAlreadyAddedPayee(response, setDisabledPublishBtn) {
  if (
    !response ||
    !response?.alreadyAddedPayees ||
    response?.alreadyAddedPayees.length === 0
  ) {
    return false;
  }
  setDisabledPublishBtn(false);
  if (response.alreadyAddedPayees.length == response.totalAddedUsersCount) {
    setDisabledPublishBtn(true);
  }
  let message = `${response.alreadyAddedPayees.length} out of ${response.totalAddedUsersCount} payees are already added to other views. If you chose to continue to publish, those ${response.alreadyAddedPayees.length} users will not be added to this view. Please remove these users from other views first.`;
  if (response.alreadyAddedPayees.length == 1) {
    message = `${response.alreadyAddedPayees.length} out of ${response.totalAddedUsersCount} payees are already added to other views. If you chose to continue to publish, the user will not be added to this view. Please remove the user from other views first.`;
  }
  return {
    message: message,
    list: response.alreadyAddedPayees.map((payee) => {
      return {
        payeeName: {
          firstName: payee.firstName,
          lastName: payee.lastName,
        },
        crystalViewName: payee.viewsMapped.map((x) => x.viewName).join(" & "),
      };
    }),
  };
}

class TabNode extends React.Component {
  render() {
    const {
      connectDragSource,
      connectDropTarget,
      children = null,
    } = this.props;

    return connectDragSource(connectDropTarget(children));
  }
}

const cardTarget = {
  drop(props, monitor) {
    const dragKey = monitor.getItem().index;
    const hoverKey = props.index;

    if (dragKey === hoverKey) {
      return;
    }

    props.moveTabNode(dragKey, hoverKey);
    monitor.getItem().index = hoverKey;
  },
};

const cardSource = {
  beginDrag(props) {
    return {
      id: props.id,
      index: props.index,
    };
  },
};

const WrapTabNode = DropTarget("DND_NODE", cardTarget, (connect) => ({
  connectDropTarget: connect.dropTarget(),
}))(
  DragSource("DND_NODE", cardSource, (connect, monitor) => ({
    connectDragSource: connect.dragSource(),
    isDragging: monitor.isDragging(),
  }))(TabNode)
);

class DraggableTabs extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      order: [],
    };
  }

  componentDidUpdate() {
    let keys = [];
    React.Children.forEach(this.props.children, (c) => {
      if (c?.key) keys.push(c.key);
    });
    if (!isEqual(keys, this.state.order)) {
      this.updateOrderFromChildren();
    }
  }

  updateOrderFromChildren = () => {
    let keys = [];
    React.Children.forEach(this.props.children, (c) => {
      if (c?.key) keys.push(c.key);
    });
    this.setState({
      order: keys,
    });
  };

  moveTabNode = (dragKey, hoverKey) => {
    const newOrder = this.state.order.slice();
    const { children = null, onTabDropCompleted } = this.props;

    React.Children.forEach(children, (c) => {
      if (newOrder.indexOf(c.key) === -1) {
        newOrder.push(c.key);
      }
    });

    const dragIndex = newOrder.indexOf(dragKey);
    const hoverIndex = newOrder.indexOf(hoverKey);

    newOrder.splice(dragIndex, 1);
    newOrder.splice(hoverIndex, 0, dragKey);
    if (onTabDropCompleted) onTabDropCompleted(newOrder);
  };

  renderTabBar = (props, DefaultTabBar) => (
    <div className="mb-3">
      <div className="ant-tabs ant-tabs-top ant-tabs-editable ant-tabs-editable-card datasheet-tab line-style-tabs pt-1 w-full">
        <DefaultTabBar {...props} className="!m-0 before:hidden">
          {(node) => (
            <WrapTabNode
              key={node.key}
              index={node.key}
              moveTabNode={this.moveTabNode}
            >
              {node}
            </WrapTabNode>
          )}
        </DefaultTabBar>
      </div>
    </div>
  );

  renderNonDraggableTabBar = (props, DefaultTabBar) => (
    <div className="mb-3">
      <div className="ant-tabs ant-tabs-top ant-tabs-editable ant-tabs-editable-card datasheet-tab line-style-tabs pt-1 w-full">
        <DefaultTabBar {...props} className="!m-0 before:hidden">
          {(node) => node}
        </DefaultTabBar>
      </div>
    </div>
  );

  render() {
    const { order } = this.state;
    const {
      isDraggable,
      isSettingsDataEmpty,
      children = null,
      ...tabProps
    } = this.props;

    const tabs = [];
    React.Children.forEach(children, (c) => {
      tabs.push(c);
    });

    const orderTabs = tabs.slice().sort((a, b) => {
      const orderA = order.indexOf(a?.key);
      const orderB = order.indexOf(b?.key);

      if (orderA !== -1 && orderB !== -1) {
        return orderA - orderB;
      }
      if (orderA !== -1) {
        return -1;
      }
      if (orderB !== -1) {
        return 1;
      }

      const ia = tabs.indexOf(a);
      const ib = tabs.indexOf(b);

      return ia - ib;
    });

    return this.props.isDraggable ? (
      <div
        className={twMerge(
          isSettingsDataEmpty ? "" : "h-full",
          "flex flex-col gap-2"
        )}
      >
        {this.props.warningComponent}
        <EverTabs
          className="overflow-visible grow"
          renderTabBar={this.renderTabBar}
          {...tabProps}
        >
          {orderTabs}
        </EverTabs>
      </div>
    ) : (
      <EverTabs {...tabProps} renderTabBar={this.renderNonDraggableTabBar}>
        {tabs}
      </EverTabs>
    );
  }
}
