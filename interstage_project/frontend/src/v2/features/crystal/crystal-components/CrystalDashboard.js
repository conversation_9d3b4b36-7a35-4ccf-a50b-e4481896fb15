import {
  CheckIcon,
  ChevronDownIcon,
  LineChartUpIcon,
} from "@everstage/evericons/outlined";
import { Col, Row, Result, Dropdown } from "antd";
import { cx } from "class-variance-authority";
import { cloneDeep, isNil } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import PropTypes from "prop-types";
import React, { useCallback, useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";
import { useParams } from "react-router-dom";
import { useSetRecoilState } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { breadcrumbAtom } from "~/GlobalStores/atoms";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  <PERSON>Loa<PERSON>,
  EverSelect,
  EverTg,
  <PERSON><PERSON>ard,
  <PERSON><PERSON>utton,
  use<PERSON>urrent<PERSON>heme,
  <PERSON>Label,
  <PERSON><PERSON><PERSON>ist<PERSON><PERSON><PERSON>,
  <PERSON>zyListContent,
  EverListItem,
} from "~/v2/components";
import { useCrystalAPI } from "~/v2/features/crystal/api";

import { CrystalPayout } from "./CrystalPayout";
import { CrystalProjectionWidget } from "./CrystalProjectionWidget";
import CrystalSelectPeriod from "./CrystalSelectPeriod";
import { PayoutGraph } from "./PayoutGraph";
import { QuotaAttainmentGraph } from "./QuotaAttainmentGraph";
import { CrystalDashboardSkeleton } from "./StandardComponents";
import { KEY_NAME_COLUMN } from "./Utils";

const { Option } = EverSelect;

/**
 * Landing page for a payee.  This is also shown to the admin when they are trying to preview the dashboard
 * as someone else
 */

CrystalDashboard.propTypes = {
  onOpenProjectionDrawer: PropTypes.func,
  onPayeeChange: PropTypes.func,
  payeeEmail: PropTypes.string,
  disableAddProjection: PropTypes.bool,
  dashboardData: PropTypes.any,
};

export function CrystalDashboard({
  isLoading,
  onOpenProjectionDrawer,
  onPayeeChange,
  selectedPeriod,
  onPeriodChange,
  payeePeriodOptions,
  payeeEmail,
  disableAddProjection,
  dashboardData,
  dashboardDataError,
  payeePeriodOptionsError,
  onApplyProjection,
  celebrationDelay,
}) {
  const { primary, base, chartColors, warning } = useCurrentTheme();
  const colorsObj = {
    currentPayoutColor: chartColors[45],
    projectedPayoutColor: chartColors[19],
    projectedPayoutTextColor: chartColors[19],
    projectedPayoutBackgroundColor: chartColors[19] + "3A",
    variablePayoutColor: chartColors[12],
    variablePayoutBackgroundColor: chartColors[12] + "3A",
    baseGraphColor: primary.lite.DEFAULT,
    currentAttainmentColor: chartColors[45],
    currentAttainmentTextColor: warning.content.lite,
    currentAttainmentBackgroundColor: chartColors[45] + "3A",
    projectedAttainmentColor: chartColors[19],
    projectedAttainmentTextColor: chartColors[19],
    projectedAttainmentBackgroundColor: chartColors[19] + "3A",
    percentageTextColor: base.content.DEFAULT,
  };

  const dashboardDataPermissions = dashboardData?.permissions ?? {
    blurQuotaAttainment: true,
    blurPayout: true,
    blurVariablePayout: true,
  };

  return (
    <div className="flex flex-col gap-10">
      <DashboardTop
        isLoading={isLoading}
        onOpenProjectionDrawer={onOpenProjectionDrawer}
        onPayeeChange={onPayeeChange}
        selectedPeriod={selectedPeriod}
        onPeriodChange={onPeriodChange}
        payeePeriodOptions={payeePeriodOptions}
        payeeEmail={payeeEmail}
        disableAddProjection={disableAddProjection}
        periodStartDate={dashboardData?.periodStartDate}
        periodEndDate={dashboardData?.periodEndDate}
        simulatorName={dashboardData?.simulatorName}
      ></DashboardTop>

      <CrystalProjectionWidgetsList
        onOpenProjectionDrawer={onOpenProjectionDrawer}
        projections={dashboardData?.projections}
        onApplyProjection={onApplyProjection}
      />
      {!isLoading &&
        isNil(dashboardDataError) &&
        isNil(payeePeriodOptionsError) && (
          <>
            <div
              className={
                dashboardData?.quotaAttainmentList?.length > 0
                  ? "grid grid-cols-1 xl:grid-cols-2 gap-4 overflow-visible"
                  : "overflow-visible"
              }
            >
              <PayoutGraph
                colorsObj={colorsObj}
                payoutData={dashboardData?.payoutData}
                permissions={dashboardDataPermissions}
                celebrationDelay={celebrationDelay}
                width={
                  dashboardData?.quotaAttainmentList?.length === 0
                    ? 1200
                    : undefined
                }
              />

              {dashboardData?.quotaAttainmentList?.length > 0 && (
                <QuotaAttainmentSelector
                  colorsObj={colorsObj}
                  quotaAttainmentList={dashboardData?.quotaAttainmentList}
                  permissions={dashboardDataPermissions}
                  celebrationDelay={celebrationDelay}
                />
              )}
            </div>
            <CrystalPayout
              crystalPayoutData={dashboardData?.crystalPayoutData}
              crystalPayoutByObjects={dashboardData?.crystalPayoutByObjects}
              permissions={dashboardDataPermissions}
            />
          </>
        )}

      {!isLoading && !isNil(payeePeriodOptionsError) && (
        <Result
          title={payeePeriodOptionsError?.message ?? "Something went wrong..."}
        />
      )}
      {!isLoading && !isNil(dashboardDataError) && (
        <Result
          title={dashboardDataError?.message ?? "Something went wrong..."}
        />
      )}
    </div>
  );
}

function QuotaAttainmentSelector({
  quotaAttainmentList,
  permissions,
  celebrationDelay,
  colorsObj,
}) {
  const [selectedGraph, setSelectedGraph] = useState(null);

  useEffect(() => {
    setSelectedGraph(quotaAttainmentList ? quotaAttainmentList[0] : null);
  }, [quotaAttainmentList]);

  const quotasList = quotaAttainmentList || [];

  const { t } = useTranslation();
  return (
    <EverCard
      outlined={true}
      className="w-full h-full flex flex-col"
      shadowSize="none"
      roundedSize="lg"
    >
      <div className="flex items-start justify-between">
        <EverTg.Heading3>{t("QUOTA_ATTAINMENT")}</EverTg.Heading3>
        {quotasList.length > 0 ? (
          <EverSelect
            className="w-64"
            defaultValue={0}
            onChange={(value) => setSelectedGraph(quotaAttainmentList[value])}
            disabled={quotaAttainmentList.length == 1}
            getPopupContainer={(trigger) => trigger.parentNode}
            dropdownAlign={{ points: ["tr", "br"], offset: [0, 6] }}
          >
            {quotasList.map((data, i) => {
              return (
                <Option className="w-72 ellipsis" key={i} value={i}>
                  {data?.quotaAttainmentData?.displayName}
                </Option>
              );
            })}
          </EverSelect>
        ) : null}
      </div>
      {quotasList ? (
        <div className="grow">
          <QuotaAttainmentGraph
            colorsObj={colorsObj}
            quotaAttainmentData={selectedGraph?.quotaAttainmentData}
            permissions={permissions}
            celebrationDelay={celebrationDelay}
          />
        </div>
      ) : (
        <CrystalDashboardSkeleton />
      )}
    </EverCard>
  );
}

const DashboardTop = observer(
  ({
    isLoading,
    onOpenProjectionDrawer,
    onPayeeChange,
    selectedPeriod,
    onPeriodChange,
    payeePeriodOptions,
    payeeEmail,
    disableAddProjection,
    periodStartDate,
    periodEndDate,
    simulatorName = "",
  }) => {
    const employeeStore = useEmployeeStore();
    const setBreadcrumbName = useSetRecoilState(breadcrumbAtom);
    const [payeeName, setPayeeName] = useState("");

    const { hasPermissions } = useUserPermissionStore();

    const formattedDate = moment(periodStartDate).utc().format("MMM yyyy");

    const { t } = useTranslation();

    useEffect(() => {
      if (payeeName && simulatorName) {
        simulatorName =
          simulatorName.charAt(0).toUpperCase() + simulatorName.slice(1);

        setBreadcrumbName([
          ...(hasPermissions(RBAC_ROLES.MANAGE_CRYSTAL)
            ? [
                { index: 1, title: "", hidden: true },
                { index: 2, title: "", hidden: true },
                {
                  index: 3,
                  title: `${simulatorName} - ${payeeName} - ${formattedDate} - Crystal(Payee Preview)`,
                  name: `${simulatorName} - ${payeeName} - ${formattedDate} - Crystal(Payee Preview)`,
                },
              ]
            : []),
        ]);
      }
    }, [payeeName, periodStartDate, periodEndDate]);

    return (
      <div>
        {!hasPermissions(RBAC_ROLES.MANAGE_CRYSTAL) && (
          <Row justify="space-between">
            <Col>
              <EverTg.Heading2 className="mr-2">
                Hey {employeeStore.firstName} 👋
              </EverTg.Heading2>
              <EverTg.Text>{t("SELECT_OPPOURTINITIES")}</EverTg.Text>
            </Col>
          </Row>
        )}
        <Row wrap={false} align="middle">
          <Col flex="auto">
            <ShowingForPayeeSelector
              onPayeeChange={onPayeeChange}
              selectedPeriod={selectedPeriod}
              onPeriodChange={onPeriodChange}
              payeePeriodOptions={payeePeriodOptions}
              payeeEmail={payeeEmail}
              isLoading={isLoading}
              setPayeeName={setPayeeName}
            />
          </Col>
          <Col flex="none mt-4">
            <EverButton
              prependIcon={<LineChartUpIcon className="w-4 h-4" />}
              disabled={
                disableAddProjection ||
                payeePeriodOptions === undefined ||
                payeePeriodOptions?.length === 0
              }
              onClick={() => onOpenProjectionDrawer(null)}
              size="small"
            >
              Select opportunities
            </EverButton>
          </Col>
        </Row>
      </div>
    );
  }
);

function CrystalProjectionWidgetsList({
  onOpenProjectionDrawer,
  projections,
  onApplyProjection,
}) {
  const onRowRemove = useCallback((removedRowList, crystalTableId) => {
    if (removedRowList.length === 0) {
      return;
    }
    let updatedProjectionWidgetData = cloneDeep(projections).map((table) => {
      if (table.crystalTableId === crystalTableId) {
        table.data = table.data.filter(
          (x) => !removedRowList.includes(x[KEY_NAME_COLUMN])
        );
      }
      return table;
    });
    if (
      updatedProjectionWidgetData.filter((x) => x.data.length > 0).length === 0
    ) {
      updatedProjectionWidgetData = [];
    }
    onApplyProjection(updatedProjectionWidgetData);
  });

  return projections && projections.length > 0 ? (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {projections.map((proj, i) => {
        return (
          <div key={i}>
            <CrystalProjectionWidget
              onOpenProjectionDrawer={onOpenProjectionDrawer}
              projection={proj}
              onRowRemove={onRowRemove}
            />
          </div>
        );
      })}
    </div>
  ) : (
    ""
  );
}

/**
 * Component used for the 'Showing for' payee selector.  This is visible only to the admin
 *
 */
const ShowingForPayeeSelector = observer(
  ({
    onPayeeChange,
    selectedPeriod,
    onPeriodChange,
    payeePeriodOptions,
    payeeEmail,
    isLoading,
    setPayeeName,
  }) => {
    const employeeStore = useEmployeeStore();

    const { hasPermissions } = useUserPermissionStore();
    const [periodSearchValue, setPeriodSearchValue] = useState("");

    return (
      <div className="flex items-center gap-3 mt-4">
        <EverLabel className="mr-0">Showing for</EverLabel>
        {(hasPermissions(RBAC_ROLES.MANAGE_CRYSTAL) ||
          employeeStore.hasReportee) && (
          <div data-testid="pt-crystal-payee-dropdown">
            <PayeeDropdown
              payeeEmail={payeeEmail}
              onPayeeChange={onPayeeChange}
              isCrystalLoading={isLoading}
              setPayeeName={setPayeeName}
            />
          </div>
        )}
        <div>
          {isLoading ? (
            <div className="w-72">
              <EverLoader.Skeleton config={[1]} fixedSize={true} />
            </div>
          ) : (
            <CrystalSelectPeriod
              onPeriodChange={onPeriodChange}
              selectedPeriod={selectedPeriod}
              periodSearchValue={periodSearchValue}
              setPeriodSearchValue={setPeriodSearchValue}
              payeePeriodOptions={payeePeriodOptions}
            />
          )}
        </div>
      </div>
    );
  }
);

function PayeeDropdown({
  payeeEmail,
  onPayeeChange,
  isCrystalLoading,
  setPayeeName,
}) {
  const { simulatorId } = useParams();
  const abortController = useRef();
  const [crystalPayeeName, setCrystalPayeeName] = useState("");
  const crystalAPI = useCrystalAPI(simulatorId);
  const { data, isFetching } = useQuery(
    ["fetchCrystalPayeeEmailList"],
    () =>
      crystalAPI.fetchPaginatedPayeesInfo({
        signal: false,
        isPayee: true,
        search_term: payeeEmail,
      }),
    {
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (data?.length > 0) {
      const userFirstAndLastName = payeeEmail
        ? data.find((x) => x.email === payeeEmail)
        : data[0];
      setPayeeName(userFirstAndLastName?.name);
      setCrystalPayeeName(userFirstAndLastName?.name);
    }
  }, [data]);

  if (isFetching || isCrystalLoading) {
    return <EverLoader.Skeleton config={[1]} fixedSize={true} />;
  }

  const getCrystalPayees = async (params) => {
    const { page, searchTerm, options, offset, limit, successCbk, failureCbk } =
      params;
    abortController.current = new AbortController();
    const { signal } = abortController.current;
    try {
      const payeesData = await crystalAPI.fetchPaginatedPayeesInfo({
        signal: signal,
        isPayee: true,
        limit: limit,
        offset: offset,
        ...(page > 0 &&
          options.length > 0 && {
            full_name: options.at(-1).label,
            email: options.at(-1).value,
          }),
        ...(searchTerm && { search_term: searchTerm }),
      });

      const formattedResponse = payeesData.map((x) => ({
        label: x.name,
        value: x.email,
        key: x.email,
      }));
      successCbk(formattedResponse);
    } catch {
      failureCbk();
    }
  };
  const lazyLoadProps = {
    abort: () => {
      abortController?.current?.abort();
    },
    getOptions: async (params) => {
      await getCrystalPayees(params);
    },
  };

  const listItemRenderer = (option, index) => {
    return (
      <EverListItem
        key={`payee_${index}`}
        onSelect={() => {
          onPayeeChange(option.value, simulatorId);
          setCrystalPayeeName(option.label);
          setPayeeName(option.label);
        }}
        title={option.label}
        selectable={true}
        className={cx(
          "my-0.5 mx-2",
          crystalPayeeName === option.label ? "bg-ever-primary-lite" : ""
        )}
        append={
          crystalPayeeName === option.label && (
            <CheckIcon className="w-5 h-5 text-ever-primary" />
          )
        }
      />
    );
  };
  const lazyListContent = (
    <div className="py-2">
      <LazyListContent
        searchPlaceholder="Search User"
        skipSelectAll
        listItemRenderer={listItemRenderer}
        {...lazyLoadProps}
      />
    </div>
  );

  return (
    <Dropdown
      overlay={
        <LazyListWrapper
          content={lazyListContent}
          positionTop={40}
          positionLeft={0}
        />
      }
      trigger={["click"]}
      destroyPopupOnHide={true}
    >
      <EverButton
        className="!px-3 !font-normal"
        type="ghost"
        color="base"
        appendIcon={
          <div>
            <ChevronDownIcon className="text-ever-base-content-mid h-full w-full" />
          </div>
        }
        size="small"
      >
        {crystalPayeeName}
      </EverButton>
    </Dropdown>
  );
}
