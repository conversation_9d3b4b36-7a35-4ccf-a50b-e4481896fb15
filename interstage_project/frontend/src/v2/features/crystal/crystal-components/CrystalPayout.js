import { AgGridReact } from "ag-grid-react";
import PropTypes from "prop-types";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { twMerge } from "tailwind-merge";

import { getLocalizedCurrencyValue } from "~/Utils/CurrencyUtils";
import {
  EverButtonGroup,
  EverButton,
  EverTg,
  EverTabs,
  useCurrentTheme,
} from "~/v2/components";
import { everAgGridOptions } from "~/v2/components/ag-grid";
import { noData } from "~/v2/images";

import { CrystalDashboardSkeleton } from "./StandardComponents";
import { HIDDEN_COLUMN_NAME } from "./Utils";

const { getDefaultOptions } = everAgGridOptions;

const { TabPane } = EverTabs;

// Use https://codesandbox.io/s/quizzical-satoshi-twle02?file=/src/grid.js to play around with this solution

CrystalPayout.propTypes = {
  crystalPayoutByObjects: PropTypes.any,
  crystalPayoutData: PropTypes.any,
};

export function CrystalPayout({
  crystalPayoutData,
  crystalPayoutByObjects,
  permissions,
}) {
  const [tabList, setTabList] = useState([]);
  const [activeKey, setActiveKey] = useState(tabList[0]?.tableName);

  useEffect(() => {
    setTabList([
      ..._tabMapping(crystalPayoutData || [], true),
      ..._tabMapping(crystalPayoutByObjects || [], false),
    ]);
    setActiveKey(tabList[0]?.tableName);
    activeIndex.current = 0;
  }, [crystalPayoutData]);

  const activeIndex = useRef(0);

  const onTabClick = useCallback(
    (clickedName) => {
      let updatedTabList = tabList.map((x, i) => {
        if (clickedName == x.tableName) {
          activeIndex.current = i;
        }
        return x;
      });
      setTabList(updatedTabList);
      setActiveKey(clickedName);
    },
    [tabList]
  );

  useEffect(() => {
    if (!tabList[0]) return;
    tabList[activeIndex.current].active = true;
  }, [tabList]);

  return (
    <div>
      {tabList.length > 1 ? (
        <EverButtonGroup
          className="bg-ever-base-200"
          activeBtnType="text"
          activeBtnColor="primary"
          defActiveBtnIndex={activeIndex.current}
          size="large"
        >
          {tabList &&
            tabList.map((x, i) => (
              <EverButton key={i} onClick={() => onTabClick(x.tableName)}>
                {x.tableName}
              </EverButton>
            ))}
        </EverButtonGroup>
      ) : (
        <div className="h-10 flex items-center">
          <EverTg.Heading3>{tabList[0]?.tableName}</EverTg.Heading3>
        </div>
      )}
      {tabList.length > 0 ? (
        <EverTabs
          activeKey={activeKey}
          defaultActiveKey={tabList[0]?.tableName}
          hideTabList={true}
          className="mt-3"
        >
          {tabList.map((tab, i) => {
            return (
              <TabPane tab={null} key={tab.tableName}>
                {tab.summaryType === true ? (
                  <CrystalPayoutSummaryTable
                    key={i}
                    colHeadData={tab.tableColDefs}
                    name={tab.tableName}
                    rows={tab.rows}
                    currencySymbol={tab.currencySymbol}
                    localeId={tab.localeId}
                    permissions={permissions}
                  ></CrystalPayoutSummaryTable>
                ) : (
                  <CrystalPayoutByObjectTable
                    key={i}
                    colHeadData={tab.tableColDefs}
                    name={tab.tableName}
                    rows={tab.rows}
                    currencySymbol={tab.currencySymbol}
                    localeId={tab.localeId}
                    permissions={permissions}
                  ></CrystalPayoutByObjectTable>
                )}
              </TabPane>
            );
          })}
        </EverTabs>
      ) : (
        <CrystalDashboardSkeleton />
      )}
    </div>
  );
}

function CrystalPayoutSummaryTable({
  colHeadData,
  rows: rowData,
  currencySymbol,
  localeId,
  permissions,
}) {
  const colors = useCurrentTheme();

  const defaultColDef = useMemo(() => {
    return {
      menuTabs: [],
      flex: 1,
      minWidth: 150,
    };
  }, []);

  const autoGroupColumnDef = useMemo(() => {
    return {
      headerName: "Plans",
      minWidth: 300,
      cellRendererParams: {
        suppressCount: true,
      },
    };
  }, []);

  const blurData = permissions.blurPayout;

  let [columnDefs, setColumnDefs] = useState();
  useEffect(() => {
    setColumnDefs(
      colHeadData.map((x, i) => {
        if (i == 0) {
          x.hide = true;
        }
        if (x.aggFunc) {
          x.aggFunc = _customSum;
          x.valueFormatter = (params) =>
            _valueFormatter(params, currencySymbol, x.field, localeId);
          x.cellClass = [_getCellClass(x.field, blurData)];
        }
        return x;
      })
    );
  }, [colHeadData, blurData]);

  const getRowStyle = useCallback(function (params) {
    if (params.node?.data?.criteria === HIDDEN_COLUMN_NAME) {
      return {
        borderColor: colors.base[50],
      };
    }
    return null;
  }, []);

  return (
    <div
      className={twMerge(
        "h-full w-full ag-theme-material row-grouping mb-4",
        rowData.length > 0 ? "" : "min-h-72"
      )}
    >
      <AgGridReact
        {...getDefaultOptions({ type: "sm" })}
        cacheBlockSize={null}
        columnDefs={columnDefs}
        pagination={false}
        defaultColDef={defaultColDef}
        rowData={rowData}
        // setting `height: 0px` in getRowStyle will create a blank space, as that styles will apply after initialization
        // getRowHeight will set the height on initial render (NOTE: getRowHeight expect a value greater than 0)
        getRowHeight={(params) =>
          params.data?.criteria === HIDDEN_COLUMN_NAME ? 1 : 40
        }
        // Setting height to 1px may reveal the border, so applying background color to the border in rowStyle.
        getRowStyle={getRowStyle}
        autoGroupColumnDef={autoGroupColumnDef}
        grandTotalRow={"bottom"}
        suppressAggFuncInHeader={true}
        groupDisplayType={"singleColumn"}
        noRowsOverlayComponent={noRowsOverlayComponent}
        suppressCellFocus={true}
        domLayout={rowData.length > 0 ? "autoHeight" : "normal"}
      ></AgGridReact>
    </div>
  );
}

function CrystalPayoutByObjectTable({
  colHeadData,
  rows: rowData,
  permissions,
  currencySymbol,
  localeId,
}) {
  const defaultColDef = useMemo(() => {
    return {
      menuTabs: [],
      flex: 1,
      minWidth: 100,
    };
  }, []);

  const autoGroupColumnDef = useMemo(() => {
    return {
      headerName: "Plans",
      minWidth: 300,
      cellRendererParams: {
        suppressCount: true,
      },
    };
  }, []);

  const blurData = permissions.blurPayout;

  let [columnDefs, setColumnDefs] = useState();
  useEffect(() => {
    setColumnDefs(
      colHeadData.map((x, i) => {
        if (i === 0) {
          return x;
        }
        const children = x.children ?? [];
        for (const child of children) {
          child.cellClass = [blurData ? "blur" : ""];
          child.valueFormatter = (params) =>
            _valueFormatter(params, currencySymbol, "", localeId);
        }

        return x;
      })
    );
  }, [colHeadData, blurData]);

  return (
    <div className="h-full w-full ag-theme-material row-grouping mb-4">
      <AgGridReact
        {...getDefaultOptions({ type: "sm" })}
        cacheBlockSize={null}
        columnDefs={columnDefs}
        pagination={false}
        defaultColDef={defaultColDef}
        rowData={rowData}
        autoGroupColumnDef={autoGroupColumnDef}
        grandTotalRow={"bottom"}
        suppressAggFuncInHeader={true}
        groupDisplayType={"singleColumn"}
        noRowsOverlayComponent={noRowsOverlayComponent}
        suppressCellFocus={true}
        domLayout="autoHeight"
      ></AgGridReact>
    </div>
  );
}

/** Mapping the payout data according to Tab structure,
 * e.g. setting the first tab as active by default */
function _tabMapping(crystalPayoutData, summaryType) {
  if (!crystalPayoutData || crystalPayoutData.length === 0) return [];
  return crystalPayoutData.map((x) => {
    x.summaryType = summaryType;
    return x;
  });
}

function _customSum(params) {
  var sum = 0;
  if (params && params.values && params.values.length > 0) {
    sum = params.values.reduce((acc, value) => {
      return acc + value;
    }, 0);
    sum = Math.round((sum + Number.EPSILON) * 100) / 100;
  }
  return sum;
}

function _valueFormatter(params, currencySymbol, field, localeId) {
  let formattedVal;
  if (field == "difference") {
    formattedVal =
      params.value || params.value === 0
        ? `${params.value < 0 ? "" : "+"} ${getLocalizedCurrencyValue(
            params.value,
            currencySymbol,
            localeId
          )}`
        : "";
  } else {
    formattedVal =
      params.value || params.value === 0
        ? getLocalizedCurrencyValue(params.value, currencySymbol, localeId)
        : "";
  }
  return formattedVal;
}

function _getCellClass(field, blurData) {
  let cellClass = "";
  switch (field) {
    case "before": {
      cellClass = "text-ever-primary";
      break;
    }
    case "after": {
      cellClass = "text-ever-success";
      break;
    }
    case "difference": {
      cellClass = "text-ever-base-content";
      break;
    }
  }

  cellClass = cellClass + (blurData ? " blur" : "");
  return cellClass;
}

const noRowsOverlayComponent = () => {
  return (
    <div className="flex flex-col gap-4">
      <img src={noData} alt="No Data" />
      <EverTg.Caption>No Data</EverTg.Caption>
    </div>
  );
};
