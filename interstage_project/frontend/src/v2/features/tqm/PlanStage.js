import { EverTg } from "~/v2/components";

const PlanStage = ({ stage }) => {
  let bgColor;
  let borderColor;
  let textColor;
  switch (stage.toLowerCase()) {
    case "published": {
      bgColor = "bg-ever-success/20";
      borderColor = "border-ever-success-lite-content/20";
      textColor = "text-ever-success-lite-content";
      break;
    }
    case "draft": {
      bgColor = "bg-ever-chartColors-22/20";
      borderColor = "border-ever-chartColors-29/20";
      textColor = "text-ever-chartColors-29";
      break;
    }
  }
  return (
    <div
      className={`inline-flex items-center gap-1 px-2 py-[2px] rounded-sm border ${bgColor} ${borderColor}`}
    >
      <EverTg.Caption.Medium className={`text-xs ${textColor}`}>
        {stage}
      </EverTg.Caption.Medium>
    </div>
  );
};

export default PlanStage;
