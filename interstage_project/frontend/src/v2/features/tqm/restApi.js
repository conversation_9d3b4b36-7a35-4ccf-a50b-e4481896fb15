function getRequestOptions(method, accessToken, data) {
  const requestOptions = {
    method: method,
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };
  if (
    (method === "POST" || method === "PATCH" || method === "DELETE") &&
    data
  ) {
    requestOptions.body = JSON.stringify(data);
  }

  return requestOptions;
}

export async function getTerritoryPlans(
  accessToken,
  selectedYear,
  selectedStatus
) {
  try {
    const params = new URLSearchParams();
    if (selectedStatus) params.append("selectedStatus", selectedStatus);
    if (selectedYear) params.append("selectedYear", selectedYear);

    const url = `/tqm/territory-plans${
      params.toString() ? `?${params.toString()}` : ""
    }`;
    const response = await fetch(url, getRequestOptions("GET", accessToken));

    const data = await response.json();
    if (!response.ok) {
      throw new Error(data?.reason || "Failed to fetch plans");
    }

    return data;
  } catch (error) {
    console.error("Error fetching plans:", error);
    return [];
  }
}

export async function createTerritoryPlan(accessToken, payload) {
  const response = await fetch(
    "/tqm/territory-plans",
    getRequestOptions("POST", accessToken, payload)
  );
  const data = await response.json();

  if (!response.ok) {
    throw new Error(data.message || "Failed to create plan");
  }

  return data;
}

export async function updateTerritoryPlan(accessToken, planId, payload) {
  const response = await fetch(
    `/tqm/territory-plans/${planId}`,
    getRequestOptions("PATCH", accessToken, payload)
  );

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.message || "Failed to update plan");
  }

  return true;
}

export async function deleteTerritoryPlan(accessToken, planId) {
  const response = await fetch(
    `/tqm/territory-plans/${planId}`,
    getRequestOptions("DELETE", accessToken)
  );

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.message || "Failed to delete plan");
  }

  return true;
}

export async function copyTerritoryPlan(accessToken, planId) {
  const response = await fetch(
    `/tqm/territory-plans/${planId}/copy`,
    getRequestOptions("POST", accessToken)
  );
  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.message || "Failed to copy plan");
  }

  return data;
}

export async function getTerritoryPlanDetails(accessToken, planId) {
  const response = await fetch(
    `/tqm/territory-plans/${planId}`,
    getRequestOptions("GET", accessToken)
  );

  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.message || "Failed to fetch plan details");
  }

  return data;
}

export async function implementTerritoryPlan(
  accessToken,
  planId,
  implementerEmail
) {
  let url = `/tqm/territory-plans/${planId}/implement`;
  if (implementerEmail) {
    url += `?implementer_email=${implementerEmail}`;
  }
  const response = await fetch(url, getRequestOptions("POST", accessToken));

  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.message || "Failed to implement plan");
  }

  return data;
}

export async function getDatabooks(accessToken) {
  const response = await fetch(
    "/datasheets/view-by?criteria=databooks",
    getRequestOptions("GET", accessToken)
  );

  const data = await response.json();
  if (!response.ok) {
    throw new Error(data?.reason || "Failed to fetch databooks");
  }

  return data.databooks || [];
}
