import { observer } from "mobx-react";
import React, { useEffect, useState, useMemo } from "react";
import { useQuery } from "react-query";
import { useNavigate } from "react-router-dom";
import { useRecoilValue } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { navPortalAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  EverDatePicker,
  EverLoader,
  EverButtonGroup,
  EverButton,
  EverNumberBadge,
  EverNavPortal,
} from "~/v2/components";
import { emptyCommissionPlan } from "~/v2/images";

import GridView from "./grid-view/index.js";
import CreateUpdatePlanModal from "../CreateUpdatePlanModal.js";
import { getTerritoryPlans } from "../restApi.js";

const TerritoryPlansList = observer(() => {
  const { accessToken } = useAuthStore();
  const { hasPermissions } = useUserPermissionStore();
  const navigate = useNavigate();
  const [selectedYear, setSelectedYear] = useState(new Date());
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [filteredPlans, setFilteredPlans] = useState([]);
  const [showCreatePlanModal, setShowCreatePlanModal] = useState(false);
  const navPortalLocation = useRecoilValue(navPortalAtom);

  const canManageAllAdmins = useMemo(() => {
    return hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS);
  }, [hasPermissions]);

  const {
    data: response = { territoryPlans: [], draftCount: 0, publishedCount: 0 },
    isLoading,
    refetch,
  } = useQuery(
    ["territoryPlans"],
    async () => {
      const response = await getTerritoryPlans(accessToken);
      return (
        response || { territoryPlans: [], draftCount: 0, publishedCount: 0 }
      );
    },
    {
      onError: (error) => {
        console.error("Error fetching plans:", error);
      },
    }
  );

  const [localDraftCount, setLocalDraftCount] = useState(0);
  const [localPublishedCount, setLocalPublishedCount] = useState(0);

  const territoryPlans = useMemo(
    () => response.territoryPlans || [],
    [response.territoryPlans]
  );

  // Calculate counts based on whether a year is selected
  const draftCount = selectedYear ? localDraftCount : response.draftCount || 0;
  const publishedCount = selectedYear
    ? localPublishedCount
    : response.publishedCount || 0;
  const allCount = draftCount + publishedCount;

  // Update local counts when response changes
  useEffect(() => {
    setLocalDraftCount(response.draftCount || 0);
    setLocalPublishedCount(response.publishedCount || 0);
  }, [response.draftCount, response.publishedCount]);

  // Define status options for the button group
  const statusOptions = [
    { label: "All", value: "all", count: allCount },
    { label: "Published", value: "published", count: publishedCount },
    { label: "Draft", value: "draft", count: draftCount },
  ];

  useEffect(() => {
    const fetchFilteredPlans = async () => {
      if (!selectedYear && (!selectedStatus || selectedStatus === "all")) {
        setFilteredPlans(territoryPlans);
        return;
      }
      let derivedYear = null;
      if (selectedYear) {
        derivedYear = selectedYear.getFullYear().toString();
      }
      const response = await getTerritoryPlans(
        accessToken,
        derivedYear,
        selectedStatus
      );

      // Update both filtered plans and counts in one block
      setFilteredPlans(response.territoryPlans || []);
      setLocalDraftCount(response.draftCount || 0);
      setLocalPublishedCount(response.publishedCount || 0);
    };

    fetchFilteredPlans();
  }, [territoryPlans, selectedYear, selectedStatus, accessToken]);

  const handlePlanCreated = async (newPlanId) => {
    try {
      const updatedData = await refetch();

      if (!updatedData.data) {
        console.error("Failed to fetch updated plans");
        return;
      }
      // If we have the new plan data, find it in the updated list
      if (newPlanId) {
        const newPlan = updatedData.data.territoryPlans.find(
          (plan) => plan.tplanId === newPlanId
        );
        if (newPlan) {
          navigateToDetails(newPlan);
          return;
        }
      }

      // Fallback: Navigate to the latest plan if we can't find the specific new plan
      if (updatedData.data.territoryPlans.length > 0) {
        const latestPlan = updatedData.data.territoryPlans.at(-1);
        navigateToDetails(latestPlan);
      }
    } catch (error) {
      console.error("Error handling new plan creation:", error);
    }
  };

  const navigateToDetails = (planData) => {
    if (planData.tplanId) {
      navigate(`/planning/${planData.tplanId}`, {
        state: planData.tplanId,
      });
    }
  };

  if (isLoading) {
    return <EverLoader />;
  }

  if (territoryPlans.length === 0) {
    return (
      <div className="place-self-center pt-32">
        <article className="text-wrap">
          <img src={emptyCommissionPlan} alt="Empty Plan" className="pl-36" />
          <div className="pl-24 text-3xl font-semibold">
            Start Building Plans
          </div>
          <div className="text-2xl text-ever-base-content-mid font-light">
            Hierarchies help you organize and connect nodes like regions,
          </div>
          <div className="pl-16 text-2xl text-ever-base-content-mid font-light">
            teams and territories for efficient management.
          </div>
          {canManageAllAdmins && (
            <div className="pt-4 pl-48">
              <EverButton
                size="small"
                onClick={() => setShowCreatePlanModal(true)}
              >
                Create Plan
              </EverButton>
              <CreateUpdatePlanModal
                open={showCreatePlanModal}
                setOpen={setShowCreatePlanModal}
                onPlanCreated={handlePlanCreated}
              />
            </div>
          )}
        </article>
      </div>
    );
  }

  return (
    <div className="w-full h-full">
      <EverNavPortal target={navPortalLocation}>
        <div className="flex justify-between">
          <div className="flex gap-4">
            <EverDatePicker
              className="max-h-8 !w-44"
              picker="year"
              placeholder="Select Fiscal Year"
              value={selectedYear}
              onChange={(value) => {
                setSelectedYear(value);
              }}
            />
            <EverButtonGroup
              className="bg-ever-base-200"
              activeBtnType="text"
              activeBtnColor="primary"
              inactiveButtonClassname="text-ever-base-content"
              defActiveBtnIndex={statusOptions.findIndex(
                (option) => option.value === selectedStatus
              )}
              size="small"
            >
              {statusOptions.map((option) => {
                return (
                  <EverButton
                    key={option.value}
                    onClick={() => setSelectedStatus(option.value)}
                    appendIcon={
                      <EverNumberBadge
                        className="bg-ever-base-300"
                        count={Number(option.count)}
                      />
                    }
                  >
                    {option.label}
                  </EverButton>
                );
              })}
            </EverButtonGroup>
          </div>
          <div>
            {canManageAllAdmins && (
              <>
                <EverButton
                  size="small"
                  onClick={() => setShowCreatePlanModal(true)}
                >
                  Create Plan
                </EverButton>
                <CreateUpdatePlanModal
                  open={showCreatePlanModal}
                  setOpen={setShowCreatePlanModal}
                  onPlanCreated={handlePlanCreated}
                />
              </>
            )}
          </div>
        </div>
      </EverNavPortal>
      <GridView
        selectedYear={selectedYear}
        selectedStatus={selectedStatus}
        filteredPlans={filteredPlans}
        isLoading={isLoading}
      />
    </div>
  );
});

export default TerritoryPlansList;
