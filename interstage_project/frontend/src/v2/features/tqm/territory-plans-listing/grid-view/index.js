import { observer } from "mobx-react";
import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSetRecoilState } from "recoil";

import { breadcrumbAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { EverTg, EverVirtualizerInfinite, EverLoader } from "~/v2/components";
import { emptyCommissionPlan } from "~/v2/images";

import Card from "./card";

const PlanList = observer(({ territoryPlans, isLoading }) => {
  const { hasPermissions } = useUserPermissionStore();
  const canExplore = hasPermissions("explore:territoryplans");

  return (
    <div className="h-full p-4">
      {isLoading ? (
        <EverLoader indicatorType="spinner" />
      ) : territoryPlans.length > 0 ? (
        <EverVirtualizerInfinite
          data={territoryPlans || []}
          RenderItem={Card}
          itemHeight={155}
          messageLastData=""
        />
      ) : (
        <div className="flex flex-col items-center justify-center h-full">
          <img src={emptyCommissionPlan} alt="No plans" className="w-64 h-64" />
          <EverTg.SubHeading1 className="mt-4">
            No Plans Available
          </EverTg.SubHeading1>
          <EverTg.Text className="mt-2 text-ever-base-content-mid">
            {canExplore
              ? "Click on the 'Create Plan' button to create a new plan."
              : "No plans have been created yet."}
          </EverTg.Text>
        </div>
      )}
    </div>
  );
});

const GridView = observer(({ filteredPlans, isLoading }) => {
  const { accessToken } = useAuthStore();
  const userPermissionStore = useUserPermissionStore();
  const navigate = useNavigate();
  const setBreadcrumbName = useSetRecoilState(breadcrumbAtom);

  useEffect(() => {
    userPermissionStore.initializeUserPermission(accessToken);
  }, [accessToken, userPermissionStore]);

  useEffect(() => {
    setBreadcrumbName([
      {
        index: 0,
        name: "Planning",
        title: "Planning",
      },
    ]);
  }, [setBreadcrumbName]);

  const navigateToDetails = (planData) => {
    navigate(`/planning/${planData.id || planData.tplanId}`);
  };

  return (
    <PlanList
      territoryPlans={filteredPlans}
      isLoading={isLoading}
      onNavigateToDetails={navigateToDetails}
    />
  );
});

export default GridView;
