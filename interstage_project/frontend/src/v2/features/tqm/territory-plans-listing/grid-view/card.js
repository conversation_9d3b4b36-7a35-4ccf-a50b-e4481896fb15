import { TrashLottie } from "@everstage/evericons/lotties";
import {
  DotsVerticalIcon,
  CopyIcon,
  Trash03Icon,
  CalendarIcon,
  EditPencilIcon,
  SettingsIcon,
} from "@everstage/evericons/outlined";
//import { TerritoryPlanCardIcon } from "@everstage/evericons/solid";
import { Dropdown, Menu } from "antd";
import { format, parseISO } from "date-fns";
import { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";

import { RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  EverCard,
  EverButton,
  IconButton,
  EverModal,
  EverHotToastMessage,
  toast,
  EverTg,
  showToastMessage,
} from "~/v2/components";

import ConfigurePlanForm from "../../ConfigurePlanForm.js";
import CreateUpdatePlanModal from "../../CreateUpdatePlanModal.js";
import PlanStage from "../../PlanStage.js";
import { deleteTerritoryPlan, copyTerritoryPlan } from "../../restApi.js";

const ContextMenuActions = (props) => {
  const { item } = props;
  const { name } = item || {};

  const { accessToken } = useAuthStore();

  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showConfigureModal, setShowConfigureModal] = useState(false);
  const [showUpdatePlanModal, setShowUpdatePlanModal] = useState(false);

  const navigate = useNavigate();

  const handleDelete = async () => {
    try {
      await deleteTerritoryPlan(accessToken, item.tplanId);
      showToastMessage(Promise.resolve(), {
        messages: {
          success: `${name} deleted successfully`,
        },
      });
      navigate("/planning");
    } catch (error) {
      console.error("Error deleting plan:", error);
      showToastMessage(Promise.reject(error), {
        messages: {
          error: "Error deleting the plan",
        },
      });
    }
  };

  const handleCopy = async () => {
    const toastId = toast.custom(() => (
      <EverHotToastMessage type="loading" description={"Copying plan..."} />
    ));
    try {
      const data = await copyTerritoryPlan(accessToken, item.tplanId);
      toast.remove(toastId);
      if (data) {
        toast.custom(() => (
          <EverHotToastMessage
            type="success"
            description={`${name} cloned successfully`}
          />
        ));
      }
      navigate(`/planning/${data.planId}`);
    } catch (error) {
      toast.remove(toastId);
      if (error.message?.includes("Plan name already exists")) {
        toast.custom(() => (
          <EverHotToastMessage
            type="error"
            description={`Copy of ${name} already exists`}
          />
        ));
      }
      console.error("Error copying plan:", error);
    }
  };

  const handleMenuClick = ({ key }) => {
    switch (key) {
      case "copy": {
        handleCopy();

        break;
      }
      case "delete": {
        setShowDeleteConfirm(true);

        break;
      }
      case "configure": {
        setShowConfigureModal(true);

        break;
      }
      case "edit": {
        setShowUpdatePlanModal(true);

        break;
      }
      // No default
    }
  };

  const menu = (
    <Menu onClick={handleMenuClick}>
      <Menu.Item key="edit">
        <div className="flex gap-2 items-center">
          <EditPencilIcon className="w-4 h-4 text-ever-base-content-mid" />
          <span>Edit</span>
        </div>
      </Menu.Item>
      <Menu.Item key="copy">
        <div className="flex gap-2 items-center">
          <CopyIcon className="w-4 h-4 text-ever-base-content-mid" />
          <span>Clone</span>
        </div>
      </Menu.Item>
      <Menu.Item key="configure">
        <div className="flex gap-2 items-center">
          <SettingsIcon className="w-4 h-4 text-ever-base-content-mid" />
          <span>Configure</span>
        </div>
      </Menu.Item>
      <Menu.Item
        key="delete"
        className="flex gap-2 items-center hover:!bg-ever-error-lite"
      >
        <Trash03Icon className="w-4 h-4 text-ever-error" />
        <EverTg.Text className="text-ever-error-lite-content">
          Delete
        </EverTg.Text>
      </Menu.Item>
    </Menu>
  );

  return (
    <>
      <Dropdown trigger={["click"]} overlay={menu}>
        <IconButton
          color="base"
          type="text"
          size="small"
          className="hover:bg-ever-base-100"
          icon={<DotsVerticalIcon className="text-ever-base-content-mid" />}
          data-testid={`tp-actions-${item.name}`}
        />
      </Dropdown>
      <EverModal.Confirm
        visible={showDeleteConfirm}
        iconContainerClasses="border-ever-error border-solid border bg-transparent"
        icon={
          <TrashLottie
            autoplay
            loop
            className="w-10 h-10 text-ever-error-content"
          />
        }
        type="error"
        mode="danger"
        title="Are you sure to delete this plan?"
        subtitle="This action can't be undone."
        noteMessage=""
        className=""
        confirmationButtons={[
          <EverButton
            type="ghost"
            color="base"
            key="cancel"
            onClick={() => setShowDeleteConfirm(false)}
          >
            Cancel
          </EverButton>,
          <EverButton
            key="delete"
            color="error"
            onClick={() => {
              handleDelete();
              setShowDeleteConfirm(false);
            }}
          >
            Yes, delete
          </EverButton>,
        ]}
      />
      <EverModal
        visible={showConfigureModal}
        onCancel={() => setShowConfigureModal(false)}
        footer={null}
        title="Configure Plan"
        destroyOnClose
      >
        <ConfigurePlanForm tplanId={item.tplanId} />
      </EverModal>
      <CreateUpdatePlanModal
        open={showUpdatePlanModal}
        setOpen={setShowUpdatePlanModal}
        planId={item.tplanId}
        editMode
        onPlanUpdated={() => {
          setShowUpdatePlanModal(false);
          window.location.reload();
        }}
      />
    </>
  );
};

const Card = ({ item }) => {
  const { hasPermissions } = useUserPermissionStore();
  const navigate = useNavigate();

  const canManageAllAdmins = useMemo(() => {
    return hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS);
  }, [hasPermissions]);

  const { name, planStage, effectiveStartDate, effectiveEndDate } = item || {};
  const planName =
    useMemo(() => {
      let _name = name && name.length > 65 ? name.slice(0, 62) + "..." : name;
      _name = _name.charAt(0).toUpperCase() + _name.slice(1);
      return _name;
    }, [name]) || "";

  const navigateToDetails = () => {
    navigate(`/planning/${item.tplanId}`, {
      state: item.tplanId,
    });
  };

  return (
    <EverCard
      interactive
      outlined
      className={
        "m-2 h-[139px] max-h-[139px] border-b-[1.5px] bg-ever-base-25 flex flex-col"
      }
      shadowSize="sm"
      roundedSize="xl"
      onClick={navigateToDetails}
    >
      <div className="flex flex-row gap-1 items-start">
        <div className="flex-grow flex flex-col gap-2">
          <EverTg.Heading4 className="text-lg text-ever-base-content">
            {planName}
          </EverTg.Heading4>
          <div className="flex flex-row text-xs items-center gap-1.5 text-ever-base-content-mid overflow-hidden text-ellipsis whitespace-nowrap">
            <CalendarIcon className="w-4 h-4 flex-shrink-0" />
            <span className="truncate">
              {format(parseISO(effectiveStartDate), "MMM dd, yyyy")} -{" "}
              {format(parseISO(effectiveEndDate), "MMM dd, yyyy")}
            </span>
          </div>
        </div>
        {canManageAllAdmins && (
          <div onClick={(e) => e.stopPropagation()}>
            <ContextMenuActions item={item} />
          </div>
        )}
      </div>
      <div className="mt-auto">
        <PlanStage stage={planStage} />
      </div>
    </EverCard>
  );
};

export default Card;
