import { LinkExternalIcon } from "@everstage/evericons/outlined";
import { CopyIcon } from "@everstage/evericons/solid";
import { useState } from "react";
import { useRecoilValue } from "recoil";

import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverTg,
  EverInput,
  EverHotToastMessage,
  toast,
  EverButton,
} from "~/v2/components";

import { implementTerritoryPlan } from "./restApi";

const handleCopy = (text, message = "Copied to clipboard") => {
  navigator.clipboard.writeText(text);
  toast.custom(() => (
    <EverHotToastMessage type="success" description={message} />
  ));
};

const ConfigurePlanForm = ({ tplanId }) => {
  const { accessToken, email } = useAuthStore();
  const myClient = useRecoilValue(myClientAtom);
  const [isImplementing, setIsImplementing] = useState(false);
  const [implementerEmail, setImplementerEmail] = useState(email);
  const [workbookUrl, setWorkbookUrl] = useState("");

  const handleImplement = async () => {
    if (!implementerEmail) return;
    setIsImplementing(true);
    try {
      const data = await implementTerritoryPlan(
        accessToken,
        tplanId,
        implementerEmail
      );
      if (data.workbookUrl) {
        setWorkbookUrl(data.workbookUrl);
      } else {
        throw new Error("No workbook URL received");
      }
    } catch (error) {
      console.error("Error implementing plan:", error);
      toast.custom(() => (
        <EverHotToastMessage
          type="error"
          description={error.message || "Failed to implement plan"}
        />
      ));
    } finally {
      setIsImplementing(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="space-y-6">
        <div>
          <EverTg.Caption className="mb-2 block text-ever-base-content-mid">
            Client ID
          </EverTg.Caption>
          <div
            className="bg-ever-base p-3 border-ever-base-400 border-solid border rounded-md overflow-x-auto cursor-pointer hover:bg-ever-base-200 transition-colors flex items-center justify-between"
            onClick={() => handleCopy(myClient?.clientId || "N/A")}
            title="Click to copy"
          >
            <code className="text-sm break-all">
              {myClient?.clientId || "N/A"}
            </code>
            <CopyIcon className="w-4 h-4 text-ever-base-content-mid flex-shrink-0 ml-2" />
          </div>
        </div>

        <div>
          <EverTg.Caption className="mb-2 block text-ever-base-content-mid">
            Plan ID
          </EverTg.Caption>
          <div
            className="bg-ever-base p-3 border-ever-base-400 border-solid border rounded-md overflow-x-auto cursor-pointer hover:bg-ever-base-200 transition-colors flex items-center justify-between"
            onClick={() => handleCopy(tplanId || "N/A")}
            title="Click to copy"
          >
            <code className="text-sm break-all">{tplanId || "N/A"}</code>
            <CopyIcon className="w-4 h-4 text-ever-base-content-mid flex-shrink-0 ml-2" />
          </div>
        </div>

        <div>
          <EverTg.Caption className="mb-2 block text-ever-base-content-mid">
            Implementer Email
          </EverTg.Caption>
          <EverInput
            type="email"
            value={implementerEmail}
            onChange={(e) => setImplementerEmail(e.target.value)}
            placeholder="Enter email"
            autocomplete="off"
            className="w-full"
          />
        </div>

        <div className="flex justify-end">
          <EverButton
            color="primary"
            onClick={handleImplement}
            loading={isImplementing}
            disabled={!implementerEmail || isImplementing}
          >
            Submit
          </EverButton>
        </div>

        {isImplementing && (
          <div className="mt-3">
            <EverTg.Text className="text-ever-base-content-mid">
              Generating configuration URL...
            </EverTg.Text>
          </div>
        )}

        {workbookUrl && (
          <div className="mt-6">
            <EverTg.Caption className="mb-2 block text-ever-base-content-mid">
              Configuration URL
            </EverTg.Caption>
            <div
              className="bg-ever-base-100 p-3 rounded-md overflow-x-auto cursor-pointer hover:bg-ever-base-200 transition-colors flex items-center justify-between"
              onClick={() => handleCopy(workbookUrl)}
              title="Click to copy"
            >
              <code className="text-sm break-all">{workbookUrl}</code>
              <CopyIcon className="w-4 h-4 text-ever-base-content-mid flex-shrink-0 ml-2" />
            </div>
            <div className="flex justify-end space-x-3 mt-2">
              <EverButton
                type="secondary"
                onClick={() => window.open(workbookUrl, "_blank")}
                icon={<LinkExternalIcon className="w-4 h-4" />}
              >
                Open in New Tab
              </EverButton>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConfigurePlanForm;
