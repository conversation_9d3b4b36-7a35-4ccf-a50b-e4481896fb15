import { FilterFunnelIcon, CalendarIcon } from "@everstage/evericons/duotone";
import {
  AlignTopArrowIcon,
  EditPencilAltIcon,
  CopyIcon,
  Trash03Icon,
  PlusIcon,
} from "@everstage/evericons/outlined";
import { Dropdown, Menu, Popover } from "antd";
import { debounce, isEmpty, isNil } from "lodash";
import { toJS } from "mobx";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";
import { useRecoilValue } from "recoil";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import {
  exportPayoutsData,
  getPayoutPeriodsForYear,
} from "~/Api/CommissionActionService";
import {
  ANALYTICS_EVENTS,
  ANALYTICS_PROPERTIES,
  CSV_EXPORT_TYPE,
  RBAC_ROLES,
  SEARCH_BOX,
  COMMON_MOMENT_DATE_FORMAT,
  COMMISSION_VIEW,
} from "~/Enums";
import { navPortalAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverNavPortal,
  EverSelect,
  EverButton,
  EverInput,
  EverRadio,
  EverInteractiveChip,
  EverHotToastMessage,
  toast,
  EverNewDatePicker,
  EverLoader,
  EverTg,
  EverDivider,
  EverTooltip,
  EverModal,
  EverHorizontalScroller,
  EverBadge,
} from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import { getFieldNamesMap } from "~/v2/features/commissions/commissions-filter-drawer-new";
import {
  filterTypeMap,
  formatFilterDisplay,
} from "~/v2/features/commissions/commissions-filter-drawer-new/filter-fields";
import {
  COMMISSION_VIEW_PERIOD,
  quickFilters,
  DATE_FORMAT,
} from "~/v2/features/commissions/constants";

import PayoutArearSwitch from "./PayoutArearSwitch";
import { useCommissionStore } from "./store";

export const CustomNavPortal = observer((props) => {
  const {
    gridApi,
    commissionView,
    setCommissionView,
    clearOrderbyFields,
    period,
    isPayout,
    currentDataSource,
    // userSearchTerm,
    // searchDataSource,
    selectedQuickFilter,
    setPeriod,
    setSelectedQuickFilter,
    // setUserSearchTerm,
    // exportableColumns,
    setSearchTerm,
    openDrawer,
    isFilterSelected,
    finalFieldsForFilterAPI,
    date,
    totalRows,
    payoutCount,
    arrearCount,
    getArrear,
    arrearFilter,
    customCalendar,
    showApprovalFeature,
    referenceDataLoading,
    onEditFilter,
    userFilters,
    handleFilterClear,
    handleSetSelectedFilter,
    hasManagePayoutPermission,
  } = props;

  const {
    setDeleteModalOpen,
    payoutFreqList,
    allCustomFields,
    allManagersMap,
    commPlanMap,
    periodOptions,
    rolesMap,
    selectedFilter,
    isFilterApplyClicked,
    setCloneModalOpen,
  } = useCommissionStore();
  const year = moment(date).year();
  const navPortalLocation = useRecoilValue(navPortalAtom);

  const activeCustomFieldsMap = useMemo(() => {
    return allCustomFields.reduce(
      (obj, item) => Object.assign(obj, { [item.systemName]: item }),
      {}
    );
  }, [allCustomFields]);

  const [localSearch, setLocalSearch] = useState();
  const [exportActive, setExportActive] = useState(false);
  const [openPopover, setOpenPopover] = useState(false);
  const [selectablePeriods, setSelectablePeriods] = useState({});
  const [activeYear, setActiveYear] = useState(year);

  const { t } = useTranslation();

  const [showMore, setShowMore] = useState(false);
  const [typeMapper] = useState(filterTypeMap());
  const [savedFiltersLimit, setSavedFiltersLimit] = useState(25);
  const [cloneAlert, setCloneAlert] = useState(false);

  const { isLoading } = useQuery(
    ["getPayoutPeriodsForYear", customCalendar, activeYear],
    () => getPayoutPeriodsForYear(activeYear, accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      enabled: customCalendar && !isEmpty(activeYear),
      onSuccess: (data) => {
        setSelectablePeriods({
          ...selectablePeriods,
          [activeYear]: data.map((item) => {
            return moment(item).format(DATE_FORMAT);
          }),
        });
      },
    }
  );

  const datePickerValue = useMemo(() => moment(period), [period]);

  const disabledDate = (current) => {
    if (isEmpty(selectablePeriods[current.year()])) {
      return true;
    }
    return !selectablePeriods[current.year()].includes(
      current.format(DATE_FORMAT)
    );
  };

  useEffect(() => {
    if (year && isEmpty(activeYear)) {
      setActiveYear(String(year));
    }
  }, [year]);

  useEffect(() => {
    if (userFilters) {
      let limit = 0;
      let index = 0;
      const GAP = 5;
      const MAX_LIMIT = 110;
      for (; index < userFilters.length; index += 1) {
        limit = limit + userFilters[index].filterName.length;
        if (limit > MAX_LIMIT) {
          setSavedFiltersLimit(index);
          setShowMore(true);
          break;
        }
        limit += GAP;
      }
      if (index == userFilters.length) {
        setSavedFiltersLimit(userFilters.length);
        setShowMore(false);
      }
    } else {
      setSavedFiltersLimit(25);
      setShowMore(false);
    }
  }, [userFilters]);

  const onPeriodChange = (val) => {
    setPeriod(val);
    localStorage.setItem(COMMISSION_VIEW_PERIOD, val);
  };

  const handleQuickFilterChange = (value, key, type) => {
    if (selectedQuickFilter.key === key) {
      // IF DESELECTED
      getArrear(null);
      setSelectedQuickFilter({});
    } else {
      // IF SELECTED
      if (type === "paymentStatus") getArrear(value);
      setSelectedQuickFilter({ value, key, type });
    }
  };

  const debounceFn = useMemo(
    () =>
      debounce((value) => {
        if (value.length > 0) {
          sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.SEARCH_PAYOUTS, {
            [ANALYTICS_PROPERTIES.SEARCH_TEXT]: value,
          });
        }
        setSearchTerm(value);
      }, SEARCH_BOX.DEBOUNCE_TIME),
    [setSearchTerm]
  );

  const debounceWrapper = (e) => {
    const searchVal = e.target.value;
    setLocalSearch(searchVal);
    if (
      searchVal.length === 0 ||
      searchVal.length >= SEARCH_BOX.MINIMUM_CHARS
    ) {
      debounceFn(searchVal);
    }
  };

  const { accessToken } = useAuthStore();

  const exportCSV = useCallback(
    (exportType) => {
      const exportStartTime = new Date();
      setOpenPopover(false);
      setExportActive(true);
      const toastId = toast.custom(
        () => (
          <EverHotToastMessage type="loading" description="Exporting data" />
        ),
        { position: "top-center", duration: Infinity }
      );

      let exportFilter = finalFieldsForFilterAPI;

      if (exportType === CSV_EXPORT_TYPE.ALL) {
        exportFilter = null;
      }

      exportPayoutsData(
        {
          date,
          filters: exportFilter,
          columns: selectedFilter?.selectedColumns,
          isApprovalOn: showApprovalFeature,
        },
        accessToken
      )
        .then((response) => {
          if (response.ok) {
            return response;
          } else {
            throw new Error("Request failed");
          }
        })
        .then((response) => {
          return response.blob();
        })
        .then((blobby) => {
          let objectUrl = window.URL.createObjectURL(blobby);
          let anchor = document.createElement("a");
          anchor.href = objectUrl;
          const date = new Date().toLocaleString();
          anchor.download = `Everstage_Payouts_${date}_.csv`;
          anchor.click();

          window.URL.revokeObjectURL(objectUrl);
          anchor.remove();
          toast.remove(toastId);
          toast.custom(
            () => (
              <EverHotToastMessage
                type="success"
                description="Successfully Exported"
              />
            ),
            { position: "top-center" }
          );
          const exportCompletionTime = new Date();
          sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.EXPORTS_CSV, {
            [ANALYTICS_PROPERTIES.TIME_TO_EXPORT]:
              (exportCompletionTime - exportStartTime) / 1000,
          });
          setExportActive(false);
        })
        .catch((error) => {
          console.log(error.message);
          toast.remove(toastId);
          toast.custom(
            () => (
              <EverHotToastMessage
                type="error"
                description="Error while Exporting"
              />
            ),
            { position: "top-center" }
          );
          setExportActive(false);
        });
    },
    [
      accessToken,
      finalFieldsForFilterAPI,
      date,
      toJS(selectedFilter?.selectedColumns).join(),
    ]
  );

  const ConfirmationNode = () => {
    const [value, setValue] = useState(CSV_EXPORT_TYPE.FILTERED);
    const onChange = (e) => {
      setValue(e.target.value);
    };
    return (
      <div className="flex flex-col gap-2">
        <EverRadio.Group value={value} onChange={onChange}>
          <div className="flex flex-col gap-4">
            <EverRadio value={CSV_EXPORT_TYPE.FILTERED}>
              <span className="text-ever-base-content">{`Export ${totalRows} payouts that match the filter`}</span>
            </EverRadio>
            <EverRadio value={CSV_EXPORT_TYPE.ALL}>
              <span className="text-ever-base-content">Export all payouts</span>
            </EverRadio>
          </div>
        </EverRadio.Group>
        <div className="w-full flex justify-end">
          <EverButton
            className="self-end"
            size="small"
            color="primary"
            onClick={() => exportCSV(value)}
          >
            Proceed
          </EverButton>
        </div>
      </div>
    );
  };

  const QuickFilters = () => (
    <div className="flex w-full">
      <div className="flex flex-row items-center gap-2">
        <EverTg.Caption.Medium className="m-0 whitespace-nowrap">
          Quick Filters
        </EverTg.Caption.Medium>
        <div className="flex items-center gap-2 h-8">
          {quickFilters.map((filter) => (
            <EverInteractiveChip
              key={filter.key}
              size="small"
              title={filter.label}
              showIndicator={filter.showIndicator}
              indicatorClass={filter.indicatorClass}
              prepend={filter.icon}
              isSelected={arrearFilter === filter.value}
              onClick={() =>
                handleQuickFilterChange(filter.value, filter.key, filter.type)
              }
            />
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <>
      <EverNavPortal target={navPortalLocation}>
        <div className="flex flex-col gap-3">
          <div className="flex w-full justify-between">
            <PayoutArearSwitch
              gridApi={gridApi}
              commissionView={commissionView}
              setCommissionView={setCommissionView}
              clearOrderbyFields={clearOrderbyFields}
              badgeCountMap={{
                [COMMISSION_VIEW.PAYOUTS]: payoutCount || 0,
                [COMMISSION_VIEW.ARREARS]: arrearCount || 0,
              }}
            />

            <div className="flex items-center gap-3">
              {isPayout && (
                <div className="flex items-center flex-wrap gap-3">
                  <div className="flex items-center gap-1">
                    {customCalendar ? (
                      <EverNewDatePicker.Legacy
                        allowClear={false}
                        format={COMMON_MOMENT_DATE_FORMAT}
                        value={referenceDataLoading ? null : datePickerValue}
                        disabledDate={disabledDate}
                        showToday={false}
                        onChange={(date) =>
                          onPeriodChange(
                            moment(date).endOf("day").format(DATE_FORMAT)
                          )
                        }
                        onPanelChange={(date) => {
                          if (isNil(selectablePeriods[date.year()])) {
                            setActiveYear(String(date.year()));
                          }
                        }}
                        isPickerLoading={isLoading}
                        renderExtraFooter={() => (
                          <>
                            {isLoading ? (
                              <div className="absolute top-0 left-0 h-full w-full">
                                <EverLoader
                                  wrapperClassName="absolute top-0 left-0 z-20"
                                  indicatorType="spinner"
                                  tip=""
                                />
                              </div>
                            ) : null}
                          </>
                        )}
                        size="small"
                      />
                    ) : (
                      <EverSelect
                        className="w-48"
                        onChange={(value) => {
                          onPeriodChange(value);
                        }}
                        showSearch
                        data-testid="period-select"
                        value={referenceDataLoading ? null : period}
                        options={periodOptions}
                        filterOption
                        size="small"
                        prependIcon={
                          referenceDataLoading ? (
                            <EverLoader.SpinnerLottie className="size-4" />
                          ) : (
                            <CalendarIcon className="size-4 text-ever-base-content-mid" />
                          )
                        }
                      />
                    )}
                  </div>
                </div>
              )}
              <div className="flex items-center flex-wrap gap-3 ml-auto">
                <EverInput.Search
                  placeholder="Search by name or email"
                  className="w-64"
                  onChange={(e) => debounceWrapper(e)}
                  value={localSearch}
                  size="small"
                />

                {isPayout && (
                  <>
                    {hasManagePayoutPermission && (
                      <EverButton.Icon
                        icon={
                          <FilterFunnelIcon className="size-4 text-ever-base-content-mid" />
                        }
                        color="base"
                        type="ghost"
                        onClick={openDrawer}
                        size="small"
                        tooltipTitle="Filter"
                      />
                    )}
                    <RBACProtectedComponent
                      permissionId={[RBAC_ROLES.EXPORT_PAYOUTS]}
                    >
                      {isFilterSelected && !isEmpty(currentDataSource) ? (
                        <Popover
                          visible={openPopover}
                          onVisibleChange={(visible) => setOpenPopover(visible)}
                          title="Export Payouts"
                          trigger="click"
                          content={<ConfirmationNode />}
                        >
                          <EverButton.Icon
                            color="base"
                            type="ghost"
                            icon={
                              <AlignTopArrowIcon className="text-ever-base-content-mid" />
                            }
                            size="small"
                            tooltipTitle="Export"
                          />
                        </Popover>
                      ) : (
                        <EverButton.Icon
                          color="base"
                          type="ghost"
                          disabled={
                            isEmpty(currentDataSource) || exportActive
                              ? true
                              : false
                          }
                          icon={
                            <AlignTopArrowIcon className="text-ever-base-content-mid" />
                          }
                          onClick={() => exportCSV(CSV_EXPORT_TYPE.ALL)}
                          size="small"
                          tooltipTitle="Export"
                        />
                      )}
                    </RBACProtectedComponent>
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="flex w-full bg-ever-base-200 rounded pl-3 h-10">
            {isPayout && isFilterSelected ? (
              <div className="h-10 w-full flex items-center grow">
                <div className="h-10 flex col-span-1 gap-2 items-center whitespace-nowrap grow">
                  <EverTg.Caption.Medium className="w-fit m-0">
                    {`${selectedFilter.filterName}: `}{" "}
                  </EverTg.Caption.Medium>

                  <EverHorizontalScroller
                    wrapperClassName="w-full bg-ever-base-200 rounded pr-3 h-10 grow"
                    className="w-full flex items-center flex-nowrap gap-2 h-10"
                  >
                    {Object.keys(selectedFilter.filters).length > 0 ? (
                      Object.keys(selectedFilter.filters).map((filterName) => {
                        if (
                          !isEmpty(selectedFilter.filters[filterName]) ||
                          typeof selectedFilter.filters[filterName] ===
                            "boolean"
                        ) {
                          let filterValue = formatFilterDisplay({
                            filterValue: selectedFilter.filters[filterName],
                            filterName,
                            managerMap: allManagersMap,
                            commPlanMap,
                            payoutFreqList,
                            rolesMap,
                            typeMapper,
                            activeCustomFieldsMap,
                          });

                          const displayMap = getFieldNamesMap(t);

                          return (
                            <div key={filterName} className="w-fit">
                              <EverBadge
                                className="bg-ever-base rounded-full"
                                size="small"
                                type="base"
                                title={
                                  <div className="flex gap-2 items-center">
                                    <EverTg.Caption>
                                      {Object.keys(
                                        activeCustomFieldsMap
                                      ).includes(filterName)
                                        ? activeCustomFieldsMap[filterName]
                                            .displayName
                                        : displayMap[filterName]}
                                    </EverTg.Caption>
                                    <EverTg.Caption className="text-ever-base-content-mid">
                                      {` ${filterValue["type"]}`}
                                    </EverTg.Caption>
                                    <EverTg.Caption className="leading-4">
                                      {filterValue["value"]}
                                    </EverTg.Caption>
                                  </div>
                                }
                              ></EverBadge>
                            </div>
                          );
                        }
                        return null;
                      })
                    ) : (
                      <EverTg.Text className="italic">
                        No filter conditions
                      </EverTg.Text>
                    )}
                  </EverHorizontalScroller>
                </div>
                <div className="h-10 flex items-center justify-end">
                  {hasManagePayoutPermission && (
                    <>
                      <div className="h-full flex items-center gap-2 px-3 border-l border-ever-base-400 border-solid">
                        {!selectedFilter.readOnly && (
                          <EverTooltip title={"Edit View"}>
                            <EverButton.Icon
                              size="small"
                              type="text"
                              color="base"
                              onClick={onEditFilter}
                              icon={
                                <EditPencilAltIcon className="text-ever-base-content-mid hover:text-ever-base-content" />
                              }
                            />
                          </EverTooltip>
                        )}
                        {selectedFilter.filterId !== null && (
                          <EverTooltip title={"Clone View"}>
                            <EverButton.Icon
                              type="text"
                              size="small"
                              color="base"
                              icon={
                                <CopyIcon className="text-ever-base-content-mid hover:text-ever-base-content" />
                              }
                              onClick={() => {
                                if (isFilterApplyClicked) {
                                  setCloneAlert(true);
                                } else {
                                  setCloneModalOpen(true);
                                }
                              }}
                            />
                          </EverTooltip>
                        )}
                        {!selectedFilter.readOnly && (
                          <EverTooltip title={"Delete View"}>
                            <EverButton.Icon
                              type="text"
                              size="small"
                              color="error"
                              icon={<Trash03Icon className="text-ever-error" />}
                              onClick={() => {
                                setDeleteModalOpen(true);
                              }}
                            />
                          </EverTooltip>
                        )}
                      </div>
                      <div className="items-center py-2">
                        <EverDivider type="vertical" />
                      </div>
                    </>
                  )}
                  <EverButton
                    className="p-0"
                    onClick={handleFilterClear}
                    size="small"
                    type="link"
                  >
                    Clear
                  </EverButton>
                </div>
                <EverModal.Confirm
                  visible={cloneAlert}
                  title="Alert"
                  subtitle="There are unsaved changes which will be discarded and the original filter will be cloned. Do you want to proceed?"
                  confirmationButtons={[
                    <EverButton
                      key="no"
                      type="ghost"
                      color="base"
                      onClick={() => {
                        setCloneAlert(false);
                      }}
                    >
                      No
                    </EverButton>,
                    <EverButton
                      key="yes"
                      onClick={() => {
                        setCloneAlert(false);
                        setCloneModalOpen(true);
                      }}
                    >
                      Yes
                    </EverButton>,
                  ]}
                />
              </div>
            ) : isPayout &&
              userFilters.slice(0, savedFiltersLimit).length > 0 ? (
              <div className="flex w-full">
                <div className="flex items-center gap-2">
                  <EverTg.Caption.Medium className="whitespace-nowrap">
                    Saved Views:
                  </EverTg.Caption.Medium>
                  <div className="flex items-center gap-2 h-10">
                    {userFilters.slice(0, savedFiltersLimit).map((filter) => {
                      return (
                        <EverInteractiveChip
                          className="whitespace-nowrap"
                          key={filter.filterId}
                          title={filter.filterName}
                          onClick={() => {
                            handleSetSelectedFilter(filter);
                          }}
                        />
                      );
                    })}
                  </div>
                  <div>
                    {showMore && (
                      <Dropdown
                        overlay={
                          <Menu className="max-h-40 overflow-auto">
                            {userFilters
                              .slice(savedFiltersLimit)
                              .map((filter) => {
                                return (
                                  <Menu.Item
                                    key={filter.filterId}
                                    className="min-h-10"
                                    onClick={() =>
                                      handleSetSelectedFilter(filter)
                                    }
                                  >
                                    <EverTg.Text>
                                      {filter.filterName}
                                    </EverTg.Text>
                                  </Menu.Item>
                                );
                              })}
                          </Menu>
                        }
                        trigger={["click"]}
                      >
                        <EverInteractiveChip
                          className="whitespace-nowrap"
                          key="More"
                          title={
                            <div className="flex items-center gap-0.5">
                              <PlusIcon className="w-3.5 h-3.5" />
                              <EverTg.Text>More</EverTg.Text>
                            </div>
                          }
                        />
                      </Dropdown>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <QuickFilters />
            )}
          </div>
        </div>
      </EverNavPortal>
      {props.children}
    </>
  );
});
