import { gql, useQuery } from "@apollo/client";
import { CalendarIcon } from "@everstage/evericons/outlined";
import { Lightning2Icon } from "@everstage/evericons/solid";
import { format, parseISO, isValid } from "date-fns";
import React, { useState } from "react";

import { EverD<PERSON><PERSON>, EverDrawer, EverTg } from "~/v2/components";
import { TimelineWrapper } from "~/v2/features/approvals/approval-requests/payout-approvals/TimeLineWrapper";
const PLAN_SPIFF = "SPIFF";

const GET_All_COUNTRIES = gql`
  {
    allActiveCountries {
      currencyCode
      currencySymbol
    }
  }
`;

/**
 * Converts an ISO date string to a formatted date string.
 *
 * @param {string} dateString - The date string in ISO format.
 * @returns {string} Formatted date string or an empty string if the date is invalid.
 */
const convertDate = (dateString) => {
  const parsedDate = parseISO(dateString);
  if (!isValid(parsedDate)) {
    return "";
  }
  return format(parsedDate, "dd MMM yyyy");
};

/**
 * TimelineDrawer component displays the timeline of a commission plan approval.
 *
 * @param {Object} props - The component props.
 * @param {boolean} props.visibility - Controls the visibility of the drawer.
 * @param {Function} props.setVisibility - Function to set the visibility state.
 * @param {string} props.planName - Name of the commission plan.
 * @param {string} props.planStartDate - Start date of the plan in ISO format.
 * @param {string} props.planEndDate - End date of the plan in ISO format.
 * @param {string} props.planType - Type of the plan (e.g., SPIFF).
 * @param {string} props.planId - Unique identifier for the plan.
 * @param {Function} props.refetch - Function to refetch the data.
 * @returns {JSX.Element} The rendered TimelineDrawer component.
 */
export function TimelineDrawer({
  visibility,
  setVisibility,
  planName,
  planStartDate,
  planEndDate,
  planType,
  planId,
  refetch,
}) {
  // State to map currency codes to their respective symbols
  const [currencyCodeSymbolMap, setCurrencyCodeSymbolMap] = useState({});

  // Fetch all active countries and populate the currencyCodeSymbolMap state
  useQuery(GET_All_COUNTRIES, {
    onCompleted: (data) => {
      if (data) {
        let currencySymbolMap = {};
        // Iterate through each country to build the currency code-symbol mapping
        for (const country of data.allActiveCountries) {
          currencySymbolMap[country.currencyCode] = country.currencySymbol;
        }
        setCurrencyCodeSymbolMap(currencySymbolMap);
      }
    },
    onError: (error) => {
      console.error(error);
    },
  });

  return (
    <EverDrawer
      title={"Plan Approvals"}
      visible={visibility}
      onClose={() => {
        setVisibility(false);
      }}
      closable={true}
      placement="right"
      width={600}
      //height="95vh"
      //bodyStyle={{ padding: "0px" }}
    >
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-2">
          {/* Display the plan name */}
          <EverTg.SubHeading4 className="text-ever-base-content">
            {planName || ""}
          </EverTg.SubHeading4>
          <div className="flex items-center gap-2">
            {/* If the plan type is SPIFF, display an icon and a divider */}
            {planType === PLAN_SPIFF && (
              <>
                <div className="w-5 h-5 rounded-full bg-ever-chartColors-12/10 flex items-center justify-center">
                  <Lightning2Icon className="w-3 h-3 text-ever-chartColors-12" />
                </div>
                <EverDivider type="vertical" className="h-3 " />
              </>
            )}
            <div className="flex gap-1 items-center">
              {/* Display the calendar icon and the plan start and end dates */}
              <CalendarIcon className="text-ever-base-content-mid w-4 h-4" />
              {planStartDate && (
                <EverTg.Caption className="text-ever-base-content-mid">
                  {convertDate(planStartDate)}
                </EverTg.Caption>
              )}
              {planEndDate && (
                <EverTg.Caption className="text-ever-base-content-mid">
                  - {convertDate(planEndDate)}
                </EverTg.Caption>
              )}
            </div>
          </div>
        </div>

        <div className="overflow-auto h-full">
          {/* Render the TimelineWrapper component with necessary props */}
          <TimelineWrapper
            entityKey={planId}
            refreshRequiredData={() => {}}
            store={{
              currencyCodeSymbolMap: currencyCodeSymbolMap,
              setTimeLineRefetch: () => {},
            }}
            closeDrawer={setVisibility}
            showDeleteButton={false}
            entityType="quote"
            refreshData={refetch}
          />
        </div>
      </div>
    </EverDrawer>
  );
}
