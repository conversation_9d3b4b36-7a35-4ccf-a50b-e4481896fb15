import { GitBranch01Icon, XCloseIcon } from "@everstage/evericons/outlined";
import { CheckCircleIcon } from "@everstage/evericons/solid";
import { debounce } from "lodash";
import { useCallback, useState, useMemo } from "react";
import { useMutation } from "react-query";
import { twMerge } from "tailwind-merge";

import { COMMISSION_PLAN_WORKFLOW_STATUS } from "~/Enums";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { EverButton, EverPopover, EverTg } from "~/v2/components";
import { useFetchApiWithAuth } from "~/v2/hooks";
import { commissionApprovalBanner } from "~/v2/images";

import { RejectApprovalModal } from "./RejectApprovalModal";
import { RevokeApprovalModal } from "./RevokeApprovalModal";
import { TimelineDrawer } from "./TimelineDrawer";

/**
 * ApprovalBanner Component
 *
 * This component displays the approval banner which handles the approval and rejection of commission plans.
 *
 * @param {Object} props - The component props.
 * @param {string} props.workflowStatus - The current workflow status.
 * @param {boolean} props.isApprover - Indicates if the user is an approver.
 * @param {string} props.approvalId - The ID of the approval.
 * @param {Object} props.commissionPlanDetails - Details of the commission plan.
 * @param {Function} props.setShowApprovalBanner - Function to toggle the visibility of the approval banner.
 * @param {String} props.approvalComments - Comments related to the approval.
 * @param {Function} props.refetchApprovalStatus - Function to refetch approval status.
 * @param {number} props.stageOrder - The current stage order of approval.
 * @param {number} props.totalApprovedUsers - Total number of approved users.
 * @param {string} props.actionPerformedBy - Name or ID of the user who performed the action.
 * @param {Function} props.refetchPlanDetails - Function to refetch plan details.
 * @param {Function} props.refreshCommissionPlanList - Function to refresh the commission plan list.
 * @param {Function} props.refetchApprovalBannerCount - Function to refetch approval banner count.
 * @param {Function} props.handleCommissionCanvasExit - Function to handle exiting the commission canvas.
 * @returns {JSX.Element} The rendered ApprovalBanner component.
 */

export function ApprovalBanner({
  workflowStatus,
  isApprover,
  approvalId,
  commissionPlanDetails,
  setShowApprovalBanner,
  approvalComments,
  refetchApprovalStatus,
  stageOrder,
  totalApprovedUsers,
  actionPerformedBy,
  refetchPlanDetails,
  refreshCommissionPlanList,
  refetchApprovalBannerCount,
  handleCommissionCanvasExit,
}) {
  // Importing necessary hooks and utilities from the EmployeeStore and custom hooks
  const { refetch } = useEmployeeStore();

  // State to control the visibility of the Reject Approval Modal
  const [showRejectModal, setShowRejectModal] = useState(false);

  // State to control the visibility of the Revoke Approval Modal
  const [revokeModalVisibility, setRevokeModalVisibility] = useState(false);

  // State to hold the reason provided by the user for revoking an approval
  const [revokeReason, setRevokeReason] = useState("");

  // State to control the visibility of the Timeline Drawer component
  const [timelineVisibility, setTimelineVisibility] = useState(false);

  // Destructuring fetchData function from the custom hook for making authenticated API requests
  const { fetchData } = useFetchApiWithAuth();

  /**
   * useMutation hook to handle the approval of commission plans.
   * Sends a POST request to the approve endpoint with the necessary data.
   * On success, it refetches various pieces of data to update the UI accordingly.
   */

  const onRefresh = () => {
    refetchApprovalBannerCount();
    refetchApprovalStatus();
    refetchPlanDetails();
    refreshCommissionPlanList({ dueTo: "WITHDRAW_COMMISSION_CANVAS" });
    refetch();
  };

  const approveApproval = useMutation(
    (data) => fetchData(`spm/approval_request/approve`, "POST", data),
    {
      onSuccess: () => {
        refetchApprovalStatus();
        refetchPlanDetails();
        refreshCommissionPlanList({ dueTo: "APPROVE_COMMISSION_CANVAS" });
        refetchApprovalBannerCount();
        refetch();
        handleCommissionCanvasExit();
      },
      onError: (error) => {
        console.error(error);
      },
    }
  );

  /**
   * Renders the title section of the approval banner.
   * Includes a popover to view approval comments if the status is an error status.
   *
   * @returns {JSX.Element} The rendered banner title component.
   */
  const renderBannerTitle = () => {
    const renderPopover = (content) => (
      <EverPopover
        showArrow={false}
        content={
          <div className="w-[500px] min-h-auto max-h-52 bg-ever-base break-words overflow-hidden overflow-y-auto pr-6">
            <EverTg.Caption>{content}</EverTg.Caption>
          </div>
        }
        trigger="click"
        showCloseIcon={true}
        className="border border-solid border-ever-base-400 pr-3"
        side="bottom"
      >
        <EverButton className="!pl-3" type="link">
          View reason
        </EverButton>
      </EverPopover>
    );

    const titles = {
      [COMMISSION_PLAN_WORKFLOW_STATUS.APPROVED]: "Approval registered",
      [COMMISSION_PLAN_WORKFLOW_STATUS.REVOKED]: `Approval revoked by ${actionPerformedBy}`,
      [COMMISSION_PLAN_WORKFLOW_STATUS.REQUESTED]: "Approval pending",
      [COMMISSION_PLAN_WORKFLOW_STATUS.REJECTED]: `Approval rejected by ${actionPerformedBy}`,
    };

    const isErrorStatus =
      workflowStatus === COMMISSION_PLAN_WORKFLOW_STATUS.REVOKED ||
      workflowStatus === COMMISSION_PLAN_WORKFLOW_STATUS.REJECTED;

    return (
      <div className="flex items-center gap-3">
        {isErrorStatus ? (
          <EverTg.Heading4 className="text-ever-error-lite-content">
            {titles[workflowStatus]}
          </EverTg.Heading4>
        ) : (
          <EverTg.SubHeading4 className="text-ever-base-content">
            {titles[workflowStatus]}
          </EverTg.SubHeading4>
        )}
        {isErrorStatus && renderPopover(approvalComments)}
      </div>
    );
  };

  // Memoize the debounced function to ensure it's not recreated on every render
  const debouncedMutate = useMemo(
    () =>
      debounce(() => {
        approveApproval.mutate({
          request_id: [approvalId],
          entity_type: "commission_plan",
        });
      }, 500),
    [approveApproval, approvalId]
  ); // Ensure dependencies are correct

  /**
   * Handler for approving an approval request.
   */
  const onApproveApproval = useCallback(() => {
    debouncedMutate();
  }, [debouncedMutate]);

  return (
    <>
      <div
        className={twMerge(
          "h-12 py-2 pr-6 pl-10 border border-solid border-x-0 border-t-0 flex items-center justify-between",
          workflowStatus === COMMISSION_PLAN_WORKFLOW_STATUS.REQUESTED &&
            "bg-ever-warning-lite border-ever-warning-hover",
          workflowStatus === COMMISSION_PLAN_WORKFLOW_STATUS.REVOKED &&
            "bg-ever-error-lite border-ever-error-lite-content",
          workflowStatus === COMMISSION_PLAN_WORKFLOW_STATUS.APPROVED &&
            "bg-ever-success-lite border-ever-success-lite-content",
          workflowStatus === COMMISSION_PLAN_WORKFLOW_STATUS.REJECTED &&
            "bg-ever-error-lite border-ever-error-lite-content"
        )}
      >
        <div className="flex gap-3 items-center">
          <div className="h-8 w-12 flex items-center justify-center">
            <img src={commissionApprovalBanner} className="w-12 h-8" />
          </div>
          {renderBannerTitle()}
        </div>
        <div className="flex gap-2 items-center">
          {workflowStatus === COMMISSION_PLAN_WORKFLOW_STATUS.REQUESTED &&
            !isApprover && (
              <EverButton
                color="base"
                size="small"
                className="!bg-ever-base-25"
                onClick={() => setRevokeModalVisibility(true)}
                type="ghost"
              >
                Revoke
              </EverButton>
            )}
          {workflowStatus === COMMISSION_PLAN_WORKFLOW_STATUS.APPROVED && (
            <EverButton
              type="ghost"
              color="success"
              size="small"
              className="!bg-ever-base-25"
              icon={<CheckCircleIcon className="w-5 h-5 text-ever-success" />}
            >
              Approved
            </EverButton>
          )}
          <EverButton
            type="text"
            color="base"
            size="small"
            icon={
              <GitBranch01Icon className="w-5 h-5 text-ever-base-content-low" />
            }
            onClick={() => setTimelineVisibility(true)}
          >
            Timeline
          </EverButton>
          {isApprover &&
            workflowStatus === COMMISSION_PLAN_WORKFLOW_STATUS.REQUESTED && (
              <>
                <EverButton
                  color="error"
                  size="small"
                  type="ghost"
                  className="!bg-ever-base-25"
                  onClick={() => setShowRejectModal(true)}
                >
                  Reject
                </EverButton>
                <EverButton
                  color="success"
                  size="small"
                  type="ghost"
                  className="!bg-ever-base-25"
                  onClick={() => onApproveApproval()}
                  disabled={approveApproval.isLoading}
                  isLoading={approveApproval.isLoading}
                >
                  Approve
                </EverButton>
              </>
            )}
          {(workflowStatus === COMMISSION_PLAN_WORKFLOW_STATUS.REVOKED ||
            workflowStatus === COMMISSION_PLAN_WORKFLOW_STATUS.APPROVED ||
            workflowStatus === COMMISSION_PLAN_WORKFLOW_STATUS.REJECTED) && (
            <div
              className="cursor-pointer"
              onClick={() => setShowApprovalBanner(false)}
            >
              <XCloseIcon className="w-5 h-5 text-ever-base-content" />
            </div>
          )}
        </div>
      </div>
      {showRejectModal && (
        <RejectApprovalModal
          visibility={showRejectModal}
          setVisibility={setShowRejectModal}
          requestId={approvalId}
          refetchApprovalStatus={refetchApprovalStatus}
          refreshCommissionPlanList={refreshCommissionPlanList}
          refetchApprovalBannerCount={refetchApprovalBannerCount}
          refetchSidebarCount={refetch}
          handleCommissionCanvasExit={handleCommissionCanvasExit}
        />
      )}

      {revokeModalVisibility && (
        <RevokeApprovalModal
          visibility={revokeModalVisibility}
          setVisibility={setRevokeModalVisibility}
          planId={commissionPlanDetails.planId}
          setRevokeReason={setRevokeReason}
          revokeReason={revokeReason}
          stageOrder={stageOrder}
          totalApprovedUsers={totalApprovedUsers}
          refetchApprovalStatus={refetchApprovalStatus}
          refreshCommissionPlanList={refreshCommissionPlanList}
          refetchApprovalBannerCount={refetchApprovalBannerCount}
          refetchSidebarCount={refetch}
        />
      )}

      {timelineVisibility && (
        <TimelineDrawer
          visibility={timelineVisibility}
          setVisibility={setTimelineVisibility}
          planName={commissionPlanDetails.planName}
          planId={commissionPlanDetails.planId}
          planStartDate={commissionPlanDetails.planStartDate}
          planEndDate={commissionPlanDetails.planEndDate}
          planType={commissionPlanDetails.planType}
          refetch={onRefresh}
        />
      )}
    </>
  );
}
