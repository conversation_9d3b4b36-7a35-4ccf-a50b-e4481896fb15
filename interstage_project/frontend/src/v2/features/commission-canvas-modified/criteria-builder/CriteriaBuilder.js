import { RowsDynamicfieldsIcon } from "@everstage/evericons/duocolor";
import {
  StatementColumnIcon,
  DatabaseDatasourceIcon,
  HelpGuideIcon,
} from "@everstage/evericons/duotone";
import { InfoCircleIcon } from "@everstage/evericons/outlined";
import React, { useRef, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useSpring, animated, config } from "react-spring";
import { twMerge } from "tailwind-merge";
import { useImmer } from "use-immer";

import { COMMISSION_TYPE, RBAC_ROLES } from "~/Enums";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { useMountTransition, EverButton, EverTg } from "~/v2/components";
import { EverButtonGroup } from "~/v2/components/EverButtonGroup";
import { EverTooltip } from "~/v2/components/EverTooltip";
import { CRITERIA_COLUMN_COMPONENT } from "~/v2/features/commission-canvas-modified/constants";
import {
  useCommissionCanvasRoot,
  useCommissionPlanType,
} from "~/v2/features/commission-canvas-modified/context";
import { ExpressionBoxHotkeyContext } from "~/v2/features/custom-criteria-designer/CriteriaExpressionBox.js";

import BuildCriteria from "./build-criteria/BuildCriteria";
import DynamicFields from "./dynamic-fields";
import Guides from "./guides";
import { GUIDES_TAB_TYPES } from "./guides/constants";
import OtherCriteriaDataSource from "./OtherCriteriaDataSource";
import SettlementRuleDataSource from "./SettlementRuleDataSource";
import Simulate from "./simulate";
import StatementColumns from "./StatementColumns";

const BUTTONS = {
  DATASOURCE: "Data source",
  STATEMENT_COLUMNS: "Statement columns",
  DYNAMIC_FIELDS: "Dynamic fields",
  PROFILE: "Profile",
  GUIDES: "Guides",
};

function CriteriaBuilder({
  selectedPlanId,
  criteriaType,
  criteriaId,
  criteriaR,
  criteriaW,
  criteriaO,
  criteriaResetExpression,
  triggerCriteriaUpdate,
  selectedPeriodSimulate,
  payeeDetailsSimulate,
  onChangePayeeSimulate,
  onChangePeriodSimulate,
}) {
  const [buildOrSimulate, setBuildOrSimulate] = useState("build");
  const [openModal, setOpenModal] = useImmer({
    datasource: false,
    statementColumns: false,
    dynamicFields: false,
    guides: { visible: false, defaultValues: null },
  });

  const { planType } = useCommissionPlanType();

  const {
    commissionPlanDetails,
    planCriteriasW,
    guideData,
    allTeams,
    areAllCriteriasValid,
    onCriteriaDatasourceChange,
    onCriteriaSortColsConfigChange,
    onCriteriaConfigChange,
    onCriteriaExpressionChange,
    onCriteriaColumnsChange,
    onEnableTeamForCriteria,
    onDisableTeamForCriteria,
    onToggleShowDoNothing,
    getDependentSettlementRuleNames,
    updateCriteriaResetExpression,
    savePlanCriterias,
    selectedCriteriaId,
    isUnderReview,
  } = useCommissionCanvasRoot();

  const panelAnimation = useSpring({
    config: config.stiff,
    transform: openModal.guides.visible ? `translateX(0%)` : `translateX(100%)`,
  });

  const dataSourceHasTransitionedIn = useMountTransition(
    openModal.datasource,
    1000
  );

  const { t } = useTranslation();
  const { hasPermissions } = useUserPermissionStore();
  const hasViewPayouts = hasPermissions(RBAC_ROLES.VIEW_PAYOUTS);

  const expressionValidityRef = useRef(null);

  const _onCriteriaDatasourceChange = (dataSourceData) => {
    const expressionValidity =
      expressionValidityRef.current?.getExpressionValidity() ?? false;

    const newDataSourceData = {
      ...dataSourceData,
      isValid: dataSourceData.isValid && expressionValidity,
    };
    onCriteriaDatasourceChange(criteriaId, newDataSourceData);
  };

  const _onCriteriaConfigChange = (config) => {
    onCriteriaConfigChange(criteriaId, config);
  };

  const _onCriteriaSortColsConfigChange = (newSortCols) => {
    onCriteriaSortColsConfigChange(criteriaId, newSortCols);
  };

  const _onCriteriaExpressionChange = (data, changeReadOnlyMap = false) => {
    onCriteriaExpressionChange(criteriaId, data, changeReadOnlyMap);
  };

  const _updateCriteriaResetExpression = (value) => {
    updateCriteriaResetExpression(criteriaId, value);
  };

  let handleSave = () => {
    return savePlanCriterias();
  };

  const handleToggleForButtons = (d) => {
    setOpenModal((draft) => {
      const defaultValues = {
        datasource: false,
        statementColumns: false,
        dynamicFields: false,
        guides: { visible: false, defaultValues: null },
      };

      switch (d) {
        case BUTTONS.DATASOURCE: {
          defaultValues.datasource = !draft.datasource;
          break;
        }
        case BUTTONS.STATEMENT_COLUMNS: {
          defaultValues.statementColumns = !draft.statementColumns;
          break;
        }
        case BUTTONS.DYNAMIC_FIELDS: {
          defaultValues.dynamicFields = !draft.dynamicFields;
          break;
        }
        case BUTTONS.GUIDES: {
          defaultValues.guides = {
            visible: !draft.guides.visible,
            defaultValues: null,
          };
          break;
        }
      }

      draft.datasource = defaultValues.datasource;
      draft.statementColumns = defaultValues.statementColumns;
      draft.dynamicFields = defaultValues.dynamicFields;
      draft.guides = defaultValues.guides;
    });
  };

  const handleSaveStatementColumns = (columns) => {
    return onCriteriaColumnsChange(
      criteriaId,
      columns,
      CRITERIA_COLUMN_COMPONENT.STATEMENT
    )
      .then(() => {
        setOpenModal((draft) => {
          draft.statementColumns = false;
        });
      })
      .catch(() => {});
  };

  const handleHelpHotkey = () => {
    handleToggleForButtons(BUTTONS.GUIDES);
  };

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.ctrlKey && ["h", "H"].includes(event.key)) {
        if (criteriaId === selectedCriteriaId) {
          handleHelpHotkey();
        }
      }
    };
    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [criteriaId, selectedCriteriaId]);

  const simulateNotAvailable =
    criteriaType === "CustomSchedule" ||
    !hasViewPayouts ||
    commissionPlanDetails.totalPayeesInPlan === 0;

  const showLearnMoreForFunctions = (functionName) => {
    const allFunctionsForGuide = guideData.functions ?? [];
    const isFunctionAvailable =
      allFunctionsForGuide.find(
        (functionObj) => functionObj.key === functionName
      ) ?? null;

    return isFunctionAvailable ? (
      <div
        className="cursor-pointer"
        onClick={() => {
          setOpenModal((draft) => {
            draft.guides = {
              visible: true,
              defaultValues: {
                defaultActiveKey: GUIDES_TAB_TYPES.FUNCTIONS,
                defaultSelectedKey: functionName,
              },
            };
          });
        }}
      >
        <EverTg.Caption className="text-ever-primary underline">
          Learn more
        </EverTg.Caption>
      </div>
    ) : null;
  };

  return (
    <div className="w-full h-full relative">
      <ExpressionBoxHotkeyContext.Provider
        value={{ handleHelpHotkey, overrideHotkey: true }}
      >
        <div className="w-full h-[60px] bg-ever-base-50 py-3 px-6 flex items-center border-0 border-b border-solid border-ever-base-400">
          <div className="relative">
            <EverButtonGroup
              className="bg-ever-base-200"
              activeBtnType="text"
              activeBtnColor="primary"
              defActiveBtnIndex={0}
              size="large"
            >
              <EverButton
                onClick={() => {
                  setBuildOrSimulate("build");
                }}
              >
                Build
              </EverButton>
              <EverButton
                onClick={() => {
                  setBuildOrSimulate("simulate");
                }}
                className={twMerge(simulateNotAvailable && "!pr-8")}
                disabled={simulateNotAvailable}
              >
                Simulate
              </EverButton>
            </EverButtonGroup>
            {simulateNotAvailable ? (
              <div className="absolute top-2.5 right-3">
                <EverTooltip
                  className="ml-2"
                  title={
                    criteriaType === "CustomSchedule"
                      ? "Cannot use simulate for Settlement component"
                      : hasViewPayouts
                      ? "Cannot use simulate as there are no payees in this plan"
                      : t("CANNOT_SIMULATE_SINCE_NO_PERMISSION")
                  }
                >
                  <InfoCircleIcon
                    className="h-5 w-5 text-ever-base-content-mid"
                    data-testid="info-icon"
                  />
                </EverTooltip>
              </div>
            ) : null}
          </div>
          <div className="ml-auto flex items-center gap-2">
            <EverButton
              color="base"
              type="ghost"
              className={twMerge(
                openModal.datasource &&
                  "bg-ever-base-50 shadow-[0px_0px_0px_3px_theme('colors.ever.base.ring'),0px_1px_2px_theme('colors.ever.base.200')]"
              )}
              onClick={() => handleToggleForButtons(BUTTONS.DATASOURCE)}
              prependIcon={
                <DatabaseDatasourceIcon className="w-4 h-4 text-ever-base-content-mid" />
              }
            >
              <EverTg.SubHeading4 className="text-ever-base-content">
                Configuration
              </EverTg.SubHeading4>
            </EverButton>
            {criteriaType !== "CustomSchedule" &&
              planType === COMMISSION_TYPE.COMMISSION_PLAN && (
                <EverButton
                  color="base"
                  type="ghost"
                  onClick={() =>
                    handleToggleForButtons(BUTTONS.STATEMENT_COLUMNS)
                  }
                  prependIcon={
                    <StatementColumnIcon className="w-4 h-4 text-ever-chartColors-26" />
                  }
                >
                  <EverTg.SubHeading4 className="text-ever-base-content">
                    Statement columns
                  </EverTg.SubHeading4>
                </EverButton>
              )}
            {commissionPlanDetails.totalPayeesInPlan !== 0 && (
              <EverButton
                color="base"
                type="ghost"
                onClick={() => handleToggleForButtons(BUTTONS.DYNAMIC_FIELDS)}
                prependIcon={
                  <RowsDynamicfieldsIcon className="w-4 h-4 text-ever-info" />
                }
              >
                <EverTg.SubHeading4 className="text-ever-base-content">
                  Dynamic fields
                </EverTg.SubHeading4>
              </EverButton>
            )}
            <EverButton
              color="base"
              type="ghost"
              onClick={() => handleToggleForButtons(BUTTONS.GUIDES)}
              className={twMerge(
                openModal.guides.visible &&
                  "bg-ever-base-50 shadow-[0px_0px_0px_3px_theme('colors.ever.base.ring'),0px_1px_2px_theme('colors.ever.base.200')]"
              )}
              prependIcon={
                <HelpGuideIcon className="w-4 h-4 text-ever-chartColors-12" />
              }
            >
              Guides
            </EverButton>
          </div>
        </div>
        <div className="w-full h-[calc(100%-128px)] overflow-auto">
          <div
            className={`w-full h-full ${
              buildOrSimulate === "build" ? "py-5 px-6" : "pt-4 px-0"
            }`}
          >
            <div
              className={`${
                buildOrSimulate === "build" ? "w-full h-full" : "hidden"
              }`}
            >
              <BuildCriteria
                ref={expressionValidityRef}
                commissionPlanId={selectedPlanId}
                commissionPlanDetails={commissionPlanDetails}
                planCriteriasW={planCriteriasW}
                criteriaType={criteriaType}
                criteriaR={criteriaR}
                criteriaW={criteriaW}
                criteriaResetExpression={criteriaResetExpression}
                triggerCriteriaUpdate={triggerCriteriaUpdate}
                updateCriteriaResetExpression={_updateCriteriaResetExpression}
                onCriteriaExpressionChange={_onCriteriaExpressionChange}
                showLearnMoreForFunctions={showLearnMoreForFunctions}
                isUnderReview={isUnderReview}
              />
            </div>

            {buildOrSimulate === "simulate" && (
              <Simulate
                commissionPlanDetails={commissionPlanDetails}
                criteriaW={criteriaW}
                criteriaType={criteriaType}
                criteriaId={criteriaId}
                payeeDetails={payeeDetailsSimulate}
                selectedPeriod={selectedPeriodSimulate}
                onChangePayee={onChangePayeeSimulate}
                onChangePeriod={onChangePeriodSimulate}
                onCriteriaColumnsChange={onCriteriaColumnsChange}
                onToggleShowDoNothing={onToggleShowDoNothing}
              />
            )}
          </div>
        </div>
        <div className="flex py-4 px-5 bg-ever-base-50">
          <EverButton
            className={`ml-auto ${
              commissionPlanDetails.planScope.canEdit ? "" : "invisible"
            }`}
            disabled={!areAllCriteriasValid || isUnderReview}
            onClick={handleSave}
          >
            Save
          </EverButton>
        </div>
        {(openModal.datasource || dataSourceHasTransitionedIn) &&
          (criteriaType === "CustomSchedule" ? (
            <SettlementRuleDataSource
              className={twMerge(
                "opacity-0 translate-y-4 transition-all duration-200 ease-in",
                openModal.datasource &&
                  dataSourceHasTransitionedIn &&
                  "opacity-100 translate-y-0"
              )}
              planCriteriasW={planCriteriasW}
              criteriaW={criteriaW}
              criteriaO={criteriaO}
              onCriteriaDatasourceChange={_onCriteriaDatasourceChange}
              isDraft={commissionPlanDetails.isDraft}
              isUnderReview={isUnderReview}
              canEdit={commissionPlanDetails.planScope.canEdit}
              handleCloseModal={() => {
                setOpenModal((draft) => {
                  draft.datasource = false;
                });
              }}
            />
          ) : (
            <OtherCriteriaDataSource
              className={twMerge(
                "opacity-0 translate-y-4 transition-all duration-200 ease-in",
                openModal.datasource &&
                  dataSourceHasTransitionedIn &&
                  "opacity-100 translate-y-0"
              )}
              allTeams={allTeams}
              criteriaType={criteriaType}
              criteriaId={criteriaId}
              criteriaW={criteriaW}
              criteriaO={criteriaO}
              onCriteriaConfigChange={_onCriteriaConfigChange}
              onCriteriaDatasourceChange={_onCriteriaDatasourceChange}
              onCriteriaSortColsConfigChange={_onCriteriaSortColsConfigChange}
              isDraft={commissionPlanDetails.isDraft}
              isUnderReview={isUnderReview}
              canEdit={commissionPlanDetails.planScope.canEdit}
              onEnableTeamForCriteria={onEnableTeamForCriteria}
              onDisableTeamForCriteria={onDisableTeamForCriteria}
              getDependentSettlementRuleNames={getDependentSettlementRuleNames}
              handleCloseModal={() => {
                setOpenModal((draft) => {
                  draft.datasource = false;
                });
              }}
            />
          ))}
        {planType === COMMISSION_TYPE.COMMISSION_PLAN && (
          <StatementColumns
            visible={openModal.statementColumns}
            component={CRITERIA_COLUMN_COMPONENT.STATEMENT}
            commissionPlanDetails={commissionPlanDetails}
            handleClose={() =>
              handleToggleForButtons(BUTTONS.STATEMENT_COLUMNS)
            }
            handleApply={handleSaveStatementColumns}
            criteriaId={criteriaId}
            criteriaType={criteriaType}
            criteriaW={criteriaW}
            isUnderReview={isUnderReview}
          />
        )}

        <DynamicFields
          isDraft={commissionPlanDetails.isDraft}
          canEdit={commissionPlanDetails.planScope.canEdit}
          selectedPlanId={selectedPlanId}
          visible={openModal.dynamicFields}
          handleDynamicFieldClose={() => {
            setOpenModal((draft) => {
              draft.dynamicFields = false;
            });
          }}
          isUnderReview={isUnderReview}
        />
        <animated.div
          style={panelAnimation}
          className="absolute top-16 z-10 right-0 h-[calc(100%-160px)] overflow-hidden w-80 bg-ever-base-25 border border-solid border-ever-base-300 rounded-xl py-4 shadow-sm"
        >
          {openModal.guides.visible ? (
            <Guides
              key={openModal.guides.defaultValues?.defaultSelectedKey ?? null}
              guideData={guideData}
              defaultValues={openModal.guides.defaultValues}
              handleCloseGuides={() => {
                setOpenModal((draft) => {
                  draft.guides = { visible: false, defaultValues: null };
                });
              }}
              resetGuide={() => {
                setOpenModal((draft) => {
                  draft.guides.defaultValues = null;
                });
              }}
            />
          ) : null}
        </animated.div>
      </ExpressionBoxHotkeyContext.Provider>
    </div>
  );
}

export default CriteriaBuilder;
