import {
  ChevronDownIcon,
  EditPencilIcon,
  FilterFunnelIcon,
} from "@everstage/evericons/outlined";
import {
  InfoCircleIcon,
  // SquareIcon
} from "@everstage/evericons/solid";
import { Dropdown, Menu } from "antd";
import { format } from "date-fns";
import { isEmpty, isNull } from "lodash";
import React, { forwardRef } from "react";
import { useRecoilValue } from "recoil";

import { COMMISSION_TYPE, HEADER_STATE } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverButton,
  EverDivider,
  EverInput,
  EverInteractiveChip,
  EverLabel,
  EverLoader,
  EverTg,
  EverTooltip,
} from "~/v2/components";
import {
  DATE_FMT_FOR_DISPLAY,
  FILTER_COMPONENTS,
  FILTER_TEXTS,
  QUICK_FILTERS,
  TEXT,
} from "~/v2/features/commission-canvas-modified/constants";
import {
  useCommissionCanvasRoot,
  useCommissionPlanType,
} from "~/v2/features/commission-canvas-modified/context";
import { lostAstronaut } from "~/v2/images";

import AddUserSlider from "./AddUserSlider";
import {
  BulkRemovePayees,
  BulkUpdatePayeePlanPeriod,
  BulkUpdateSettlementPeriod,
} from "./BulkUpdateActions";
import FilterPayeesDrawer from "./FilterPayeesDrawer";
import { GlobalPlanEditorWrapper } from "./GlobalPlanEditor";
import useManageUsers from "./hooks/useManageUsers";
import Table from "./table";

const ManageUsers = forwardRef(
  ({ selectedPlanId, refetchPlanDetails, refreshCommissionPlanList }, ref) => {
    const {
      commissionPlanDetails,
      payoutFrequencyLabel,
      initialTotalPayeesInPlan,
      triggerManageUsersPayees,
      settlementRulesW,
      updateTriggerManageUsersPayees,
      isUnderReview,
    } = useCommissionCanvasRoot();

    const {
      updateDateData,
      setPageSize,
      savePlanPayeesWrapper,
      setPageNumber,
      isTableDataInitialLoading,
      isTableDataFetchLoading,
      isExportLoading,
      totalSuccessRows,
      // For plan period and settlement end date changes - global actions
      dateDataLatest,
      dateDataApplied,
      isOpenGlobalActionModal,
      planPeriodErrors,
      settlementEndDateErrors,
      handleApplyGlobalAction,
      handleVisibilityGlobalActionModal,
      handleDiscardGlobalAction,
      sessionId,
      actions,

      tableGridRef,
      initialTableGridCountRef,
      filtersComponentForTableRef,

      pageSize,
      totalRows,
      pageNumber,
      getCommonParamsForIndividuals,
      bulkUpdateTriggerState,
      handleBulkUpdateTrigger,
      handleBulkUpdateClose,
      // SELECTION PROPS
      cumulativeSet,
      cumulativeList,
      headerCbxState,
      clearSelection,
      maxSelectionCount,
      onToggleCheckbox,
      onToggleHeaderCbx,
      setHeaderCbxState,
      // Search and filter props
      showFilterDrawer,
      handleFilterDrawerVisibility,
      searchText,
      handleChangeSearchText,
      handleApplyFilter,
      handleRemovePayee,
      handleAddPayees,
      handleBulkModifyPayeesPlanPeriod,
      handleBulkModifyPayeesSettlementEndDate,
      handleBulkRemovePayees,
      handleModifyPayeePlanPeriod,
      handleModifyPayeePlanExclusionPeriod,
      handleModifyPayeeSettlementEndDate,
      handleModifyPayeePlanDoc,
      quickFilterCounts,
      tableQuickFilter,
      handleTableQuickFilterChange,
      tableFilterCounts,
    } = useManageUsers({
      ref,
      selectedPlanId,
      commissionPlanDetails,
      updateTriggerManageUsersPayees,
      refetchPlanDetails,
      refreshCommissionPlanList,
    });

    const myAtom = useRecoilValue(myClientAtom);
    const clientFeatures = getClientFeatures(myAtom);
    const isPlanExclusionEnabled = clientFeatures.enablePlanExclusion;

    const { planType } = useCommissionPlanType();

    const isSettlementRulesPresent = !isEmpty(settlementRulesW);

    const menuListItems = [
      {
        name: "Update plan period",
        key: "update-plan-period",
        disabled: headerCbxState === HEADER_STATE.NONE,
      },
      {
        name: "Update settlement end date",
        key: "update-plan-settlement-period",
        disabled:
          headerCbxState === HEADER_STATE.NONE ||
          !dateDataLatest.isSettlementEndDate,
      },
      {
        name: "Download as CSV",
        key: "download-csv",
        disabled: isExportLoading,
      },
      {
        name: "Remove payees",
        key: "remove-payee",
        className:
          headerCbxState === HEADER_STATE.NONE
            ? ""
            : "text-ever-error-lite-content",
        disabled: headerCbxState === HEADER_STATE.NONE,
      },
    ];

    if (planType === COMMISSION_TYPE.FORECAST_PLAN) {
      menuListItems.splice(1, 1);
    }

    const menuItems = (
      <Menu>
        {menuListItems
          .filter(
            (item) =>
              commissionPlanDetails.planScope.canEdit ||
              item.key === "download-csv"
          )
          .map((item) => {
            return (
              <Menu.Item
                key={item.key}
                value={item.key}
                disabled={item.disabled ?? false}
                className={item.className ?? ""}
                onClick={() => handleBulkUpdateTrigger(item.key)}
              >
                {item.name}
              </Menu.Item>
            );
          })}
      </Menu>
    );

    const selectedRecordsCount =
      headerCbxState === HEADER_STATE.EVERYTHING
        ? totalRows
        : cumulativeList.length;
    const bulkInfoMessage = `${selectedRecordsCount} of ${totalRows} are selected`;

    const handleSelectQuickFilters = (selectedValue) => {
      // If the quick filter has already selected, make it
      // unselected.
      if (selectedValue === tableQuickFilter) {
        handleTableQuickFilterChange(null);
      } else {
        handleTableQuickFilterChange(selectedValue);
      }
    };

    const filterData = [
      {
        value: QUICK_FILTERS.WARNINGS_AND_ERRORS.value,
        label: QUICK_FILTERS.WARNINGS_AND_ERRORS.label,
        showIndicator: true,
        indicatorClass: "text-ever-error",
      },
      {
        value: QUICK_FILTERS.LIMITED_PERIOD.value,
        label: QUICK_FILTERS.LIMITED_PERIOD.label,
        showIndicator: false,
        indicatorClass: "text-ever-warning",
      },
      {
        value: QUICK_FILTERS.ENTIRE_PERIOD.value,
        label: QUICK_FILTERS.ENTIRE_PERIOD.label,
        showIndicator: false,
        indicatorClass: "text-ever-success",
      },
    ];

    // const filterWithIndicator = filterData.map((item) => ({
    //   value: item.value,
    //   label: (
    //     <div
    //       className="flex items-center gap-2 mt-1.5"
    //       title={`${item.label} (${quickFilterCounts.values[item.value]})`}
    //     >
    //       {item.showIndicator ? (
    //         <SquareIcon className={twMerge("w-3 h-3", item.indicatorClass)} />
    //       ) : (
    //         <div className="w-3 h-3"></div>
    //       )}
    //       <EverTg.Text>{item.label}</EverTg.Text>
    //       <div>
    //         {quickFilterCounts.isFetchLoading ? (
    //           <EverLoader.SpinnerLottie className="w-8 h-8" />
    //         ) : (
    //           <EverTg.Heading4>
    //             ({quickFilterCounts.values[item.value]})
    //           </EverTg.Heading4>
    //         )}
    //       </div>
    //     </div>
    //   ),
    // }));

    return (
      <div className="flex h-full w-full">
        {commissionPlanDetails.planScope.canEdit && (
          <AddUserSlider
            payoutFrequencyLabel={payoutFrequencyLabel}
            getCommonParamsForIndividuals={getCommonParamsForIndividuals}
            handleAddPayees={handleAddPayees}
            triggerManageUsersPayees={triggerManageUsersPayees}
          />
        )}
        <div className="flex flex-col bg-ever-base relative w-full overflow-y-auto">
          <div className="w-full bg-ever-base-100 py-3 px-6">
            <EverTg.Text className="font-semibold">
              {commissionPlanDetails.planScope.canEdit
                ? TEXT.MANAGE_USERS_TEXT
                : TEXT.VIEW_USERS_TEXT}
            </EverTg.Text>
            <div className="flex gap-6 mt-2">
              <div className="flex flex-col gap-1">
                <div className="flex">
                  <EverLabel>{TEXT.PLAN_PERIOD} :</EverLabel>
                  <EverTg.SubHeading4 className="text-ever-base-content">
                    {isEmpty(dateDataApplied.planPeriod) ? (
                      "-"
                    ) : (
                      <>
                        {format(
                          dateDataApplied.planPeriod[0],
                          DATE_FMT_FOR_DISPLAY
                        )}
                        {" - "}
                        {format(
                          dateDataApplied.planPeriod[1],
                          DATE_FMT_FOR_DISPLAY
                        )}
                      </>
                    )}
                  </EverTg.SubHeading4>
                </div>
                {!isEmpty(planPeriodErrors) && (
                  <EverTg.Description className="text-ever-error-lite-content w-80 overflow-hidden text-ellipsis whitespace-nowrap">
                    <EverTooltip
                      placement={"topLeft"}
                      title={planPeriodErrors.join(". ")}
                    >
                      {planPeriodErrors.join(". ")}
                    </EverTooltip>
                  </EverTg.Description>
                )}
              </div>
              {planType === COMMISSION_TYPE.COMMISSION_PLAN && (
                <>
                  <EverDivider type="vertical" />
                  <div className="flex flex-col gap-1">
                    <div className="flex">
                      <EverLabel>{TEXT.SETTLEMENT_END_DATE} :</EverLabel>
                      <EverTg.SubHeading4 className="font-normal">
                        {dateDataApplied.isSettlementEndDate &&
                        dateDataApplied.settlementEndDate ? (
                          <>
                            {format(
                              dateDataApplied.settlementEndDate,
                              DATE_FMT_FOR_DISPLAY
                            )}
                          </>
                        ) : (
                          "-"
                        )}
                      </EverTg.SubHeading4>
                    </div>
                    {!isEmpty(settlementEndDateErrors) && (
                      <EverTg.Description className="text-ever-error-lite-content w-80 overflow-hidden text-ellipsis whitespace-nowrap">
                        <EverTooltip
                          placement={"topLeft"}
                          title={settlementEndDateErrors.join(". ")}
                        >
                          {settlementEndDateErrors.join(". ")}
                        </EverTooltip>
                      </EverTg.Description>
                    )}
                  </div>
                </>
              )}
              {commissionPlanDetails.planScope.canEdit && (
                <EverButton
                  className="!p-0 !h-5"
                  size="small"
                  type="link"
                  color="primary"
                  appendIcon={<EditPencilIcon className="w-4 h-4" />}
                  onClick={() => {
                    handleVisibilityGlobalActionModal(true);
                  }}
                  disabled={isUnderReview}
                >
                  Edit
                </EverButton>
              )}
            </div>
          </div>
          {/* When there are no saved payees in the plan, show 
          "Add payees to the plan" text */}
          <div
            className={`flex flex-col w-full h-full ${
              initialTotalPayeesInPlan > 0 ||
              totalRows > 0 ||
              initialTableGridCountRef.current !== null
                ? "flex"
                : "hidden"
            }`}
          >
            {/*
              Here the z-index of 20 to give higher precedence over the
              "Select all" component of validation table, which has z-index of 10.
            */}
            <div className="flex items-center flex-wrap justify-between z-20 px-6">
              <div className="flex grow gap-1 py-2">
                <EverLabel>Quick filters:</EverLabel>
                {/*<div className="flex grow">
                  <EverSelect
                    showIcons
                    showSearch={false}
                    mode=""
                    placeholder="Quick filters"
                    options={filterWithIndicator}
                    allowClear
                    dropdownMatchSelectWidth={true}
                    className="w-60"
                    onChange={handleSelectQuickFilters}
                    loading={quickFilterCounts?.isFetchLoading}
                    size="small"
                  />
                </div>*/}
                <div className="flex grow gap-3">
                  {filterData.map((item) => {
                    return (
                      <EverInteractiveChip
                        key={item.value}
                        title={item.label}
                        size="small"
                        showIndicator={item.showIndicator}
                        indicatorClass={item.indicatorClass ?? ""}
                        className="cursor-pointer"
                        append={
                          <>
                            {quickFilterCounts.isFetchLoading ? (
                              <EverLoader.SpinnerLottie className="w-8 h-8" />
                            ) : (
                              <EverTg.Heading4>
                                {quickFilterCounts.values[item.value]}
                              </EverTg.Heading4>
                            )}
                          </>
                        }
                        isSelected={tableQuickFilter === item.value}
                        onClick={() => handleSelectQuickFilters(item.value)}
                      />
                    );
                  })}
                </div>
              </div>
              <div className="flex items-center ml-auto py-2">
                <EverInput.Search
                  size="small"
                  placeholder={TEXT.SEARCH_PLACEHOLDER}
                  onChange={(event) =>
                    handleChangeSearchText(event.target.value)
                  }
                  value={searchText}
                  className="w-48 mr-3"
                  allowClear
                />
                <div className="mr-3">
                  <EverButton.Icon
                    type="ghost"
                    size="small"
                    color="base"
                    onClick={() => {
                      handleFilterDrawerVisibility(true);
                    }}
                    className="!px-2"
                    icon={
                      // Show a dot indicator if there are any filters applied.
                      <div className="flex items-center justify-center">
                        <FilterFunnelIcon className="w-5 h-5 text-ever-base-content-mid" />
                        {tableFilterCounts > 0 && (
                          <div className="absolute top-[-1px] right-[-3px]">
                            <div className="w-2 h-2 bg-ever-error rounded-full"></div>
                          </div>
                        )}
                      </div>
                    }
                  />
                </div>
                <div>
                  <Dropdown
                    size="small"
                    placeholder="Actions"
                    placement={"bottomRight"}
                    overlay={menuItems}
                    trigger="click"
                  >
                    <EverButton
                      size="small"
                      color="base"
                      type="ghost"
                      appendIcon={
                        <ChevronDownIcon className="size-4 text-ever-base-content-mid" />
                      }
                    >
                      Bulk actions
                    </EverButton>
                  </Dropdown>
                </div>
              </div>
            </div>
            <div className="flex-1">
              <Table
                gridRef={tableGridRef}
                commissionPlanDetails={commissionPlanDetails}
                isTableDataFetchLoading={isTableDataFetchLoading}
                isTableDataInitialLoading={isTableDataInitialLoading}
                onDeleteUser={handleRemovePayee}
                onModifyPayeePlanPeriod={handleModifyPayeePlanPeriod}
                onModifyPayeePlanExclusionPeriod={
                  handleModifyPayeePlanExclusionPeriod
                }
                onModifyPayeeSettlementEndDate={
                  handleModifyPayeeSettlementEndDate
                }
                onModifyPayeePlanDoc={handleModifyPayeePlanDoc}
                pageCount={Math.ceil(totalRows / pageSize)} //number of pages
                pageSize={pageSize} //rows per page
                totalRows={totalRows} //total rows
                currentPage={pageNumber - 1} //current page number
                setPageSize={setPageSize}
                setCurrentPage={setPageNumber}
                headerCbxState={headerCbxState}
                onToggleCheckbox={onToggleCheckbox}
                onToggleHeaderCbx={onToggleHeaderCbx}
                selectedEmailSet={cumulativeSet}
                maxSelectionCount={maxSelectionCount}
                clearSelection={clearSelection}
                dateDataApplied={dateDataApplied}
                isSettlementEndDate={dateDataLatest.isSettlementEndDate}
                settlementEndDate={dateDataLatest.settlementEndDate}
                setHeaderCbxState={setHeaderCbxState}
                isPlanExclusionEnabled={isPlanExclusionEnabled}
              />
            </div>
            <div className="flex px-6 py-[10px] justify-between border-0 border-t border-solid border-ever-base-400">
              <div className="flex items-center gap-2">
                <EverTg.Text className="flex items-center justify-center font-medium min-w-[24px] h-6 px-1 border border-solid border-ever-base-400 bg-ever-base-200 rounded-sm text-center text-ever-base-content">
                  {isNull(totalSuccessRows) ? "--" : totalSuccessRows}
                </EverTg.Text>
                <EverTg.Caption className="text-sm text-ever-base-content-mid font-medium">
                  payee saved
                </EverTg.Caption>
              </div>
              <div className="flex gap-6 items-center">
                {!isEmpty(actions) && (
                  <div className="flex gap-2 items-center">
                    <InfoCircleIcon className="w-4 h-4 text-ever-info" />
                    <EverTg.Caption className="font-medium text-ever-info-lite-content">
                      Click Save to update changes
                    </EverTg.Caption>
                  </div>
                )}
                <EverButton
                  className={`w-[120px] !h-9 ${
                    commissionPlanDetails.planScope.canEdit ? "" : "invisible"
                  }`}
                  disabled={
                    isTableDataFetchLoading || isEmpty(actions) || isUnderReview
                  }
                  onClick={savePlanPayeesWrapper}
                >
                  {TEXT.SAVE}
                </EverButton>
              </div>
            </div>
          </div>

          <div
            className={`flex-1 flex flex-col justify-center items-center ${
              initialTotalPayeesInPlan > 0 ||
              totalRows > 0 ||
              initialTableGridCountRef.current !== null
                ? "hidden"
                : "flex"
            }`}
          >
            <img src={lostAstronaut} />
            {commissionPlanDetails.planScope.canEdit && (
              <EverTg.Heading2 className="text-center mt-2">
                {TEXT.ADD_USERS_TO_PLAN}
              </EverTg.Heading2>
            )}
          </div>
        </div>

        <GlobalPlanEditorWrapper
          visible={isOpenGlobalActionModal}
          planId={commissionPlanDetails.planId}
          dateDataLatest={dateDataLatest}
          dateDataApplied={dateDataApplied}
          sessionId={sessionId}
          actions={actions}
          handleApply={handleApplyGlobalAction}
          handleDiscard={handleDiscardGlobalAction}
          handleModalVisibility={handleVisibilityGlobalActionModal}
          updateDateData={updateDateData}
          planPeriodErrors={planPeriodErrors}
          settlementEndDateErrors={settlementEndDateErrors}
          isSettlementRulesPresent={isSettlementRulesPresent}
          commissionPlanDetails={commissionPlanDetails}
        />

        <BulkUpdatePayeePlanPeriod
          info={bulkInfoMessage}
          visible={bulkUpdateTriggerState === "update-plan-period"}
          dateDataApplied={dateDataApplied}
          handleBulkModifyPayeesPlanPeriod={handleBulkModifyPayeesPlanPeriod}
          handleClose={handleBulkUpdateClose}
        />
        <BulkUpdateSettlementPeriod
          info={bulkInfoMessage}
          visible={bulkUpdateTriggerState === "update-plan-settlement-period"}
          handleBulkModifyPayeesSettlementEndDate={
            handleBulkModifyPayeesSettlementEndDate
          }
          handleClose={handleBulkUpdateClose}
        />
        <BulkRemovePayees
          selectedRecordsCount={selectedRecordsCount}
          visible={bulkUpdateTriggerState === "remove-payee"}
          handleBulkRemovePayees={handleBulkRemovePayees}
          handleClose={handleBulkUpdateClose}
        />

        <FilterPayeesDrawer
          ref={filtersComponentForTableRef}
          title={
            commissionPlanDetails.planScope.canEdit
              ? FILTER_TEXTS.FILTER_IN_MANAGE_USERS
              : FILTER_TEXTS.FILTER_IN_VIEW_USERS
          }
          showDrawer={showFilterDrawer}
          onCloseDrawer={() => handleFilterDrawerVisibility(false)}
          onApplyFilter={handleApplyFilter}
          component={FILTER_COMPONENTS.MANAGE_USERS}
        />
      </div>
    );
  }
);
ManageUsers.displayName = "ManageUsers";

export default ManageUsers;
