import {
  PauseCircleIcon,
  XCloseIcon,
  CheckIcon,
} from "@everstage/evericons/outlined";
import { LoadingIcon, LogOutIcon } from "@everstage/evericons/solid";
import { twMerge } from "tailwind-merge";

import { EverBadge, EverTg } from "~/v2/components";

const statusOptions = {
  approved: {
    label: "Approved",
    bgColor: "bg-ever-success-lite",
    borderColor: "border-ever-success/20",
    textColor: "text-ever-success-lite-content",
    icon: <CheckIcon className="w-4 h-4 text-ever-success" />,
  },
  rejected: {
    label: "Rejected",
    bgColor: "bg-ever-error-lite",
    borderColor: "border-ever-error/20",
    textColor: "text-ever-error-lite-content",
    icon: <XCloseIcon className="w-4 h-4 text-ever-error" />,
  },
  requested: {
    label: "Requested",
    bgColor: "bg-ever-info-lite",
    borderColor: "border-ever-info/25",
    textColor: "text-ever-info-lite-content",
    icon: <LoadingIcon className="w-4 h-4 text-ever-primary" />,
  },
  revoked: {
    label: "Revoked",
    bgColor: "bg-ever-warning-lite",
    borderColor: "border-ever-warning/20",
    textColor: "text-ever-warning-lite-content",
    icon: <PauseCircleIcon className="w-4 h-4 text-ever-warning" />,
  },
  withdrawn: {
    label: "Withdrawn",
    bgColor: "bg-ever-base-100",
    borderColor: "border-ever-base-400",
    textColor: "text-ever-base-content",
    icon: <LogOutIcon className="w-4 h-4 text-ever-base-content-mid" />,
  },
};

export const ApprovalStatusTag = ({ status }) => {
  const statusOption = statusOptions[status];
  if (!statusOption) return null;
  return (
    <EverBadge
      icon={statusOption?.icon}
      title={
        <EverTg.Caption
          className={twMerge("font-medium", statusOption?.textColor)}
        >
          {statusOption?.label}
        </EverTg.Caption>
      }
      type="custom"
      className={twMerge(statusOption?.bgColor, statusOption?.borderColor)}
    />
  );
};
