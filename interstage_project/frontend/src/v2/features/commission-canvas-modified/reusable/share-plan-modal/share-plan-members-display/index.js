import {
  <PERSON><PERSON><PERSON>,
  ChevronDownIcon,
  ChevronRightIcon,
  EditPencilAltIcon,
  EyeIcon,
  FilterLinesIcon,
  SlashCircleIcon,
} from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import { isEmpty } from "lodash";

import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverBadge,
  EverButton,
  EverCheckbox,
  EverDivider,
  EverGroupAvatar,
  EverList,
  EverListItem,
  EverTg,
  EverTooltip,
} from "~/v2/components";
import {
  CircleTextNearAvatar,
  EmptyGroupIcon,
  EmptySelect,
} from "~/v2/features/commission-canvas-modified/reusable/share-plan-modal/components/UtilComponents";
import {
  PLAN_RBAC_PERMISSION_TYPES,
  PLAN_RBAC_PERMISSION_TYPES_LABEL,
  USER,
  USER_GROUP,
} from "~/v2/features/commission-canvas-modified/reusable/share-plan-modal/constants";

const _getMenuItemTooltip = (
  targetType,
  permissionOption,
  permissionOptions
) => {
  if (
    targetType === USER_GROUP ||
    permissionOption !== PLAN_RBAC_PERMISSION_TYPES.CAN_REMOVE
  )
    return "";

  if (
    permissionOptions.length === 2 &&
    permissionOptions.includes(PLAN_RBAC_PERMISSION_TYPES.CAN_EDIT) &&
    permissionOptions.includes(PLAN_RBAC_PERMISSION_TYPES.CAN_REMOVE)
  )
    return "Removing edit permission will keep view-only access due to their default view all plans permission";
  if (
    (permissionOptions.length === 2 &&
      permissionOptions.includes(PLAN_RBAC_PERMISSION_TYPES.CAN_VIEW) &&
      permissionOptions.includes(PLAN_RBAC_PERMISSION_TYPES.CAN_REMOVE)) ||
    (permissionOptions.length === 3 &&
      permissionOptions.includes(PLAN_RBAC_PERMISSION_TYPES.CAN_VIEW) &&
      permissionOptions.includes(PLAN_RBAC_PERMISSION_TYPES.CAN_EDIT) &&
      permissionOptions.includes(PLAN_RBAC_PERMISSION_TYPES.CAN_REMOVE))
  )
    return "Removing will completely remove the user's access to the plan.";

  return "";
};

const _getPermissionTypeMenu = (
  targetType,
  permissionType,
  permissionOptions,
  onMemberChangePermissionAndSave,
  onMemberRemoveShareAndSave
) => {
  const showDividerBeforeRemove =
    permissionOptions.find(
      (option) => option === PLAN_RBAC_PERMISSION_TYPES.CAN_REMOVE
    ) && permissionOptions.length > 1;

  return (
    <Menu
      onClick={(e) => {
        e.domEvent?.stopPropagation();
        if (e.key === permissionType) return;

        if (
          [
            PLAN_RBAC_PERMISSION_TYPES.CAN_VIEW,
            PLAN_RBAC_PERMISSION_TYPES.CAN_EDIT,
          ].includes(e.key)
        )
          onMemberChangePermissionAndSave(e.key);
        else if (PLAN_RBAC_PERMISSION_TYPES.CAN_REMOVE)
          onMemberRemoveShareAndSave();
      }}
    >
      {permissionOptions.map((permissionOption) => {
        const isSelected = permissionOption === permissionType;

        return (
          <>
            {showDividerBeforeRemove &&
              permissionOption === PLAN_RBAC_PERMISSION_TYPES.CAN_REMOVE && (
                <EverDivider />
              )}
            <Menu.Item
              key={permissionOption}
              className={`!pl-0 !pr-0 ${
                isSelected ? "bg-ever-primary-lite rounded-md" : ""
              }`}
            >
              <EverTooltip
                placement="bottomRight"
                title={_getMenuItemTooltip(
                  targetType,
                  permissionOption,
                  permissionOptions
                )}
              >
                <div className="w-full h-full flex items-center">
                  <div className="flex gap-4 px-4">
                    <div className="flex items-center gap-3">
                      {permissionOption ===
                      PLAN_RBAC_PERMISSION_TYPES.CAN_VIEW ? (
                        <EyeIcon className="w-4 h-4 text-ever-base-content-mid" />
                      ) : permissionOption ===
                        PLAN_RBAC_PERMISSION_TYPES.CAN_EDIT ? (
                        <EditPencilAltIcon className="w-4 h-4 text-ever-base-content-mid" />
                      ) : permissionOption ===
                        PLAN_RBAC_PERMISSION_TYPES.CAN_REMOVE ? (
                        <SlashCircleIcon className="w-4 h-4 text-ever-error" />
                      ) : null}
                      <EverTg.Text
                        className={`${
                          permissionOption ===
                          PLAN_RBAC_PERMISSION_TYPES.CAN_REMOVE
                            ? "text-ever-error-hover"
                            : `text-ever-base-content ${
                                isSelected ? "font-medium" : ""
                              }`
                        }`}
                      >
                        {PLAN_RBAC_PERMISSION_TYPES_LABEL[permissionOption]}
                      </EverTg.Text>
                    </div>
                    {isSelected && (
                      <CheckIcon className="w-5 h-5 text-ever-primary" />
                    )}
                  </div>
                </div>
              </EverTooltip>
            </Menu.Item>
          </>
        );
      })}
    </Menu>
  );
};

const pluralize = (count, word, suffix) =>
  `${count} ${word}${count > 1 ? suffix : ""}`;

const _getMembersSplitText = (membersCount) => {
  if (membersCount[USER] <= 0 && membersCount[USER_GROUP] <= 0) return "";
  if (membersCount[USER] <= 0)
    return `(${pluralize(membersCount[USER_GROUP], "group", "s")})`;
  if (membersCount[USER_GROUP] <= 0)
    return `(${pluralize(membersCount[USER], "user", "s")})`;

  return `(${pluralize(membersCount[USER], "user", "s")},
    ${pluralize(membersCount[USER_GROUP], "group", "s")})`;
};

const PermissionSelector = ({
  anyActionInLive,
  targetType,
  permissionType,
  permissionOptions,
  onMemberChangePermissionAndSave,
  onMemberRemoveShareAndSave,
}) => {
  return (
    <Dropdown
      overlay={_getPermissionTypeMenu(
        targetType,
        permissionType,
        permissionOptions,
        onMemberChangePermissionAndSave,
        onMemberRemoveShareAndSave
      )}
      trigger={["click"]}
      onClick={(e) => e.stopPropagation()}
      placement="bottomRight"
      disabled={permissionOptions.length <= 1 || anyActionInLive}
    >
      <EverTooltip
        placement="bottomRight"
        title={
          targetType === USER && permissionOptions.length <= 1
            ? "This user by default has access to all plans. Please revoke their access to all plans for modifying the plan permission."
            : ""
        }
      >
        <div>
          <EverBadge
            type="info"
            className={permissionOptions.length > 1 && "cursor-pointer"}
            title={
              <div className="flex items-center gap-1">
                <span>{PLAN_RBAC_PERMISSION_TYPES_LABEL[permissionType]}</span>
                {permissionOptions.length > 1 && (
                  <ChevronDownIcon className="w-4 h-4 text-ever-info-lite-content" />
                )}
              </div>
            }
          />
        </div>
      </EverTooltip>
    </Dropdown>
  );
};

const SharePlanMembersDisplay = ({
  anyActionInLive,
  sharePlanMembersData,
  membersCount,
  onUserGroupSelect,
  onFilterSharePlanMembers,
  onMemberChangePermissionAndSave,
  onMemberRemoveShareAndSave,
}) => {
  const { email: loggedInUserEmail } = useAuthStore();

  return (
    <div className="flex flex-col gap-2.5 h-full">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <EverTg.Heading2>Shared with</EverTg.Heading2>
          <EverTg.Heading2 className="ml-1 text-ever-base-content-low">
            {_getMembersSplitText(membersCount)}
          </EverTg.Heading2>
        </div>
        <Dropdown
          overlay={
            <Menu
              onClick={(e) => {
                e.domEvent?.stopPropagation();
              }}
            >
              <div className="flex items-center gap-3 px-2 py-2.5 hover:bg-ever-base-100">
                <EverCheckbox
                  checked={
                    sharePlanMembersData.sharePlanMembersSelectedFilters[USER]
                  }
                  label="Users"
                  onChange={(e) => {
                    onFilterSharePlanMembers(USER, e.target.checked);
                    e.stopPropagation();
                  }}
                />
              </div>
              <div className="flex items-center gap-3 px-2 py-2.5 hover:bg-ever-base-100">
                <EverCheckbox
                  checked={
                    sharePlanMembersData.sharePlanMembersSelectedFilters[
                      USER_GROUP
                    ]
                  }
                  label="Groups"
                  onChange={(e) => {
                    onFilterSharePlanMembers(USER_GROUP, e.target.checked);
                    e.stopPropagation();
                  }}
                />
              </div>
            </Menu>
          }
          trigger={["click"]}
          placement="bottomRight"
        >
          <EverButton.Icon
            color="base"
            type="ghost"
            size="small"
            className="!border-ever-base-400 bg-ever-base-25 hover:bg-ever-base-100"
            icon={<FilterLinesIcon className="text-ever-base-content" />}
          />
        </Dropdown>
      </div>
      <div className="h-[calc(100%-34px)]">
        {isEmpty(sharePlanMembersData.sharedMembers) ? (
          <EmptySelect text="No shared members found" />
        ) : (
          <EverList
            size="small"
            itemLayout="vertical"
            bordered={false}
            enableScrollShadow={true}
            dataSource={sharePlanMembersData.sharedMembers}
            renderItem={(item) => (
              <EverListItem
                className="px-0 py-2.5"
                prepend={
                  <div className="flex relative">
                    {item.targetType === USER ||
                    (item.targetType === USER_GROUP &&
                      item.profile.totalUsers >= 1) ? (
                      <>
                        <EverGroupAvatar
                          size="medium"
                          avatars={[
                            {
                              name: item.profile.avatarName,
                              image: item.profile.avatarProfilePicture,
                            },
                          ]}
                        />
                        {item.targetType === USER_GROUP &&
                          item.profile.totalUsers > 1 && (
                            <CircleTextNearAvatar
                              text={`+${item.profile.totalUsers - 1}`}
                            />
                          )}
                      </>
                    ) : (
                      <div className="ml-0.5">
                        <EmptyGroupIcon />
                      </div>
                    )}
                  </div>
                }
                append={
                  <PermissionSelector
                    anyActionInLive={anyActionInLive}
                    targetType={item.targetType}
                    permissionType={item.permissionType}
                    permissionOptions={item.permissionOptions}
                    onMemberChangePermissionAndSave={(permissionType) =>
                      onMemberChangePermissionAndSave(
                        item.targetId,
                        item.targetType,
                        permissionType
                      )
                    }
                    onMemberRemoveShareAndSave={() =>
                      onMemberRemoveShareAndSave(item.targetId, item.targetType)
                    }
                  />
                }
                title={
                  <div className="flex gap-1.5">
                    <EverTg.Text className="truncate font-medium text-ever-base-content">
                      {item.profile.name}
                      {item.targetId === loggedInUserEmail && (
                        <EverTg.Text className="ml-1 text-ever-base-content-low">
                          (You)
                        </EverTg.Text>
                      )}
                    </EverTg.Text>
                    {item.targetType === USER_GROUP && (
                      <ChevronRightIcon className="flex-shrink-0 w-4 h-4 mt-0.5" />
                    )}
                  </div>
                }
                tooltipTitle={item.profile.name}
                selectable={item.targetType === USER_GROUP && !anyActionInLive}
                onSelect={() => onUserGroupSelect(item)}
              />
            )}
            className="overflow-y-auto max-h-full"
          />
        )}
      </div>
    </div>
  );
};

export default SharePlanMembersDisplay;
