import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import React from "react";
import { twMerge } from "tailwind-merge";

import { useDatabookStore } from "~/GlobalStores/DatabookStore";
import { useEverAiStore } from "~/GlobalStores/EverAiStore";
import { Ever<PERSON><PERSON><PERSON>, EverLoader, EverTg } from "~/v2/components";
import { noData } from "~/v2/images";

import { CANVAS_ACTION_MODES, STEPS_ENUM } from "./constants";
import {
  CommissionCanvasRootContextProvider,
  CommissionPlanTypeContextProvider,
} from "./context";
import CriteriaBuilderWrapper from "./criteria-builder";
import { AddCriteriaWrapper } from "./criteria-builder/create-criteria";
import Header from "./Header";
import useCommissionCanvas from "./hooks/useCommissionCanvas";
import ManageUsers from "./manage-users";
import SidePanel from "./SidePanel";
import WelcomeScreen from "./WelcomeScreen";
import { ThinkerAnimation } from "../llm-invocations/lotties/Lotties";

// CommissionCanvasRenderer gets recreated everytime for a new commission plan opened
const CommissionCanvasRenderer = observer(
  ({
    selectedPlanId,
    commissionCanvasActionMode,
    handleCommissionCanvasExit,
    handleCommissionCanvasSuccess,
    refreshCommissionPlanList,
    refetchApprovalBannerCount,
  }) => {
    const canvasState = useCommissionCanvas({
      selectedPlanId,
      commissionCanvasActionMode,
      handleCommissionCanvasExit,
      refreshCommissionPlanList,
    });
    const { isDatabookLoading } = useDatabookStore();
    const { commissionCanvasLoader } = useEverAiStore();

    if (canvasState.isCommissionCanvasLoading || isDatabookLoading) {
      return <EverLoader indicatorType="spinner" />;
    }

    return (
      <CommissionCanvasRootContextProvider canvasState={canvasState}>
        <div className="flex flex-col h-full overflow-hidden">
          {commissionCanvasLoader && (
            <div className="fixed inset-0 bg-ever-base/[0.4] backdrop-filter backdrop-blur-sm z-50">
              <ThinkerAnimation />
            </div>
          )}
          {commissionCanvasActionMode === CANVAS_ACTION_MODES.CREATE && (
            <WelcomeScreen
              handleCommissionCanvasExit={
                canvasState.handleCommissionCanvasExitInternal
              }
              handleCommissionCanvasSuccess={handleCommissionCanvasSuccess}
              refreshCommissionPlanList={refreshCommissionPlanList}
            />
          )}
          <Header
            handleCommissionCanvasExit={
              canvasState.handleCommissionCanvasExitInternal
            }
            refetchPlanDetails={canvasState.refetchPlanDetails}
            refreshCommissionPlanList={refreshCommissionPlanList}
            setWorkflowStatus={canvasState.setWorkflowStatus}
            workflowStatus={canvasState.workflowStatus}
            refetchApprovalBannerCount={refetchApprovalBannerCount}
          />
          {commissionCanvasActionMode === CANVAS_ACTION_MODES.EDIT &&
            (isEmpty(canvasState.commissionPlanDetails) ? (
              <div className="flex flex-col h-full items-center justify-center">
                <img src={noData} className="w-52 h-52" />
                <EverTg.Heading3>Could not find the plan</EverTg.Heading3>
              </div>
            ) : (
              <div className={twMerge("flex h-[calc(100%_-_68px)]")}>
                <div className="flex flex-col h-full py-4 w-60 flex-shrink-0 bg-ever-base-100 border-0 border-r border-solid border-ever-base-400 overflow-y-auto">
                  <SidePanel />
                </div>
                {canvasState.selectedStep === STEPS_ENUM.MANAGE_USERS && (
                  <ManageUsers
                    ref={canvasState.manageUsersRef}
                    selectedPlanId={selectedPlanId}
                    refetchPlanDetails={canvasState.refetchPlanDetails}
                    refreshCommissionPlanList={refreshCommissionPlanList}
                  />
                )}
                {canvasState.selectedStep === STEPS_ENUM.CRITERIA && (
                  <CriteriaBuilderWrapper selectedPlanId={selectedPlanId} />
                )}
                <AddCriteriaWrapper
                  visible={canvasState.showAddCriteriaModal}
                  onCancel={canvasState.handleCloseAddCriteriaModal}
                />
              </div>
            ))}
        </div>
      </CommissionCanvasRootContextProvider>
    );
  }
);

const CommissionCanvas = ({
  visible,
  commissionCanvasActionMode,
  selectedPlanId,
  handleCommissionCanvasExit,
  handleCommissionCanvasSuccess,
  refreshCommissionPlanList,
  refetchApprovalBannerCount,
}) => {
  return (
    <EverDrawer
      visible={visible}
      bodyStyle={{ padding: 0 }}
      closable={false}
      destroyOnClose
      height="100%"
      push={false}
    >
      {visible && (
        <CommissionCanvasRenderer
          key={selectedPlanId ?? "create_canvas"}
          selectedPlanId={selectedPlanId}
          commissionCanvasActionMode={commissionCanvasActionMode}
          handleCommissionCanvasExit={handleCommissionCanvasExit}
          handleCommissionCanvasSuccess={handleCommissionCanvasSuccess}
          refreshCommissionPlanList={refreshCommissionPlanList}
          refetchApprovalBannerCount={refetchApprovalBannerCount}
        />
      )}
    </EverDrawer>
  );
};

const CommissionCanvasWrapper = (props) => {
  return (
    <CommissionPlanTypeContextProvider planType={props.planType}>
      <CommissionCanvas {...props} />
    </CommissionPlanTypeContextProvider>
  );
};

export default CommissionCanvasWrapper;
