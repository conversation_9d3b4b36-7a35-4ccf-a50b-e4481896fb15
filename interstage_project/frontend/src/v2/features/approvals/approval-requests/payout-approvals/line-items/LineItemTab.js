//import { XCloseIcon } from "@everstage/evericons/outlined";
import { InfoCircleIcon } from "@everstage/evericons/solid";
import { useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useQuery, useMutation } from "react-query";
import { useLocation } from "react-router-dom";

import {
  getPlanCriteriaList as getPlanCriteriaList<PERSON>pi,
  getSubRequests,
} from "~/Api/ApprovalWorkflowService";
import { HEADER_STATE, RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { EverTg, EverTabs, EverLoader } from "~/v2/components";
import { sadIcecream } from "~/v2/images";

import { ApproveModal } from "./ApproveModal";
import { LineItemTableData } from "./LineItemTable";
import { RejectModal } from "./RejectModal";

/**
 * @param {object} props
 * @param {object} props.tab - selected tab from tablist, which tab object contains planId and criteriaId.
 * @param {array} props.staticColumns - static columns for line item table.
 * @param {boolean} props.hideCheckboxColumn - boolean to add a checkbox column to the table.
 * @param {string} props.period - selected period to get approval requests.
 * @param {string} props.status - selected status to get approval requests. ex: requested,all
 * @param {string} props.selectedEmail - selected email to get approval requests.
 * @param {object} props.lineItemTableRef - This ref, named lineItemTableRef, is associated with the line item table component.
 * @param {string} [props.stageId] - stageid represents the current stage of approval in the timeline box.
 * @param {function} [props.rolesRefetch] - Function to update the approval count of the payee in the approval tab icon.
 * @param {function} props.getPlanCriteriaList - Function to retrieve the updated list of criteria by triggering the criteriaList API.
 * @param {string} props.approverEmail - approver email for whom the sub approval requests are being fetched(email selected from dropdown filter).
 * @param {function} props.refetchTimelineData - Refetch the timeline data.
 * @param {function} props.applyFiltersFun - Refetch the payouts table

 *  @returns {React.Component}
 * @description This component is used to render the line item tab.
 *
 **/

function RenderTab({
  tab,
  staticColumns,
  hideCheckboxColumn,
  period,
  status,
  selectedEmail,
  lineItemTableRef,
  stageId,
  approverEmail,
  rolesRefetch,
  refetchTimelineData,
  applyFiltersFun,
}) {
  const { t } = useTranslation();
  const { accessToken } = useAuthStore();

  const [approveModalVisibility, setApproveModalVisibility] = useState(false);
  const [rejectModalVisibility, setRejectModalVisibility] = useState(false);
  const [selectedSubRequestId, setSelectedSubRequestId] = useState([]);
  //const [summationMsgVisibility, setSummationMsgVisibility] = useState(true);
  const [bulkSelectedSubRequestId, setBulkSelectedSubRequestId] = useState([]);
  const [aresubRequestsBulkSelected, setAresubRequestsBulkSelected] =
    useState(false);
  const [bulkActionType, setBulkActionType] = useState(null);
  useEffect(() => {
    if (bulkSelectedSubRequestId) {
      setSelectedSubRequestId &&
        setSelectedSubRequestId(bulkSelectedSubRequestId);
    }
  }, [bulkSelectedSubRequestId]);

  const { isLoading, refetch, isRefetching } = useQuery(
    ["getSubRequests"],
    async () => {
      const res = await getSubRequests(accessToken, {
        period: period,
        payee: selectedEmail,
        criteriaId: tab.criteriaId,
        planId: tab.planId,
      });
      return res;
    },
    {
      refetchOnWindowFocus: false,
      enabled: false,
      onError: () => {
        console.log("error");
      },
      onSuccess: (data) => {
        if (data?.subRequestIds) {
          if (data?.subRequestIds.length > 0) {
            const allSubRequestsIds = data?.subRequestIds;
            if (bulkActionType === "approve") {
              setApproveModalVisibility(true);
            } else if (bulkActionType === "reject") {
              setRejectModalVisibility(true);
            }
            setSelectedSubRequestId(allSubRequestsIds);
          }
        }
      },
    }
  );

  const openApproveModal = (requestIds, headerCbxState) => {
    if (headerCbxState === HEADER_STATE.EVERYTHING) {
      setBulkActionType("approve");
      refetch();
    } else {
      setApproveModalVisibility(true);
      setSelectedSubRequestId(requestIds);
    }
  };
  const openRejectModal = (requestIds, headerCbxState) => {
    if (headerCbxState === HEADER_STATE.EVERYTHING) {
      setBulkActionType("reject");
      refetch();
    } else {
      setRejectModalVisibility(true);
      setSelectedSubRequestId(requestIds);
    }
  };

  return (
    <>
      <ApproveModal
        setConfirmModalVisibility={setApproveModalVisibility}
        confirmModalVisibility={approveModalVisibility}
        subRequestIds={selectedSubRequestId}
        setSelectedSubRequestId={setSelectedSubRequestId}
        rolesRefetch={rolesRefetch}
        refetchTimelineData={refetchTimelineData}
        lineItemTableRef={lineItemTableRef}
        aresubRequestsBulkSelected={aresubRequestsBulkSelected}
        setAresubRequestsBulkSelected={setAresubRequestsBulkSelected}
        setBulkSelectedSubRequestId={setBulkSelectedSubRequestId}
        applyFiltersFun={applyFiltersFun}
      />
      <RejectModal
        setRejectModalVisibility={setRejectModalVisibility}
        rejectModalVisibility={rejectModalVisibility}
        subRequestIds={selectedSubRequestId}
        setSelectedSubRequestId={setSelectedSubRequestId}
        rolesRefetch={rolesRefetch}
        refetchTimelineData={refetchTimelineData}
        lineItemTableRef={lineItemTableRef}
        aresubRequestsBulkSelected={aresubRequestsBulkSelected}
        setAresubRequestsBulkSelected={setAresubRequestsBulkSelected}
        setBulkSelectedSubRequestId={setBulkSelectedSubRequestId}
        applyFiltersFun={applyFiltersFun}
      />

      <div className="pt-1 h-full w-full flex flex-col">
        {tab.isLineItemLevel === false && (
          <div className="px-4 py-2 bg-ever-info-lite border border-solid border-ever-info rounded-md mb-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <InfoCircleIcon className="h-4 w-4 self-center text-ever-info " />
              <EverTg.Caption className="text-ever-info-lite-content">
                {t("COMMISSION")} shown here is the total{" "}
                {t("COMMISSION").toLowerCase()} earned by the user for the
                entire criteria not for each item.
              </EverTg.Caption>
            </div>
            {/* <div
              className="flex items-center cursor-pointer"
              onClick={() => {
                setSummationMsgVisibility(false);
              }}
            >
              <XCloseIcon className="text-ever-primary-lite-content w-4 h-4" />
            </div> */}
          </div>
        )}
        <LineItemTableData
          planId={tab.planId}
          criteriaId={tab.criteriaId}
          staticColumns={staticColumns}
          setBulkSelectedSubRequestId={setBulkSelectedSubRequestId}
          bulkSelectedSubRequestId={bulkSelectedSubRequestId}
          hideCheckboxColumn={hideCheckboxColumn}
          period={period}
          status={status}
          selectedEmail={selectedEmail}
          lineItemTableRef={lineItemTableRef}
          stageId={stageId}
          openApproveModal={openApproveModal}
          openRejectModal={openRejectModal}
          approverEmail={approverEmail}
          setAresubRequestsBulkSelected={setAresubRequestsBulkSelected}
          isLoading={isLoading || isRefetching}
        />
      </div>
    </>
  );
}

/**
 * @param {object} props
 * @param {array} props.staticColumns - static columns for line item table.
 * @param {boolean} props.hideCheckboxColumn - boolean to add a checkbox column to the table.
 * @param {string} props.period - selected period to get approval requests.
 * @param {string} props.status - selected status to get approval requests. ex: requested,all
 * @param {string} props.selectedEmail - selected email to get approval requests .
 * @param {object} props.lineItemTableRef - This ref, named lineItemTableRef, is associated with the line item table component.
 
 * @param {string} [props.stageId] - stageid represents the current stage of approval in the timeline box.
 * @param {function} [props.rolesRefetch] - Function to update the approval count of the payee in the approval tab icon.
 * @param {string} props.approverEmail - approver email for whom the sub approval requests are being fetched(email selected from dropdown filter).
 * @param {function} props.setApprover - set the approver email
 * @param {function} props.refetchTimelineData - refetch timeline data
 * @param {function} props.applyFiltersFun - refetch payouts table.
* @returns {React.Component}
 * @description This component is used to render the line item tab.
 */
export const LineItemTab = ({
  staticColumns,
  hideCheckboxColumn = false,
  period,
  status,
  selectedEmail,
  lineItemTableRef,
  stageId,
  rolesRefetch,
  approverEmail,
  refetchTimelineData,
  setApprover,
  applyFiltersFun,
}) => {
  const location = useLocation();
  const { hasPermissions } = useUserPermissionStore();

  const queryParams = new URLSearchParams(location.search);
  const criteriaIdFromQueryParam = queryParams.get("criteria_id");

  const [tabKey, setTabKey] = useState("tab-0");
  const { accessToken } = useAuthStore();
  const [tabs, setTabs] = useState([]);

  const { isLoading, mutate } = useMutation(
    (payload) => getPlanCriteriaListApi(payload, accessToken),
    {
      onError: () => {
        console.log("error");
      },
      onSuccess: (data) => {
        if (data?.planCriteriaList) {
          if (data?.planCriteriaList.length == 0) {
            setApprover && setApprover(null);
          }
          setTabs(data?.planCriteriaList);
        }
      },
    }
  );

  const getPlanCriteriaList = useCallback(async () => {
    const payload = {
      period: period,
      status: status,
      payees: selectedEmail,
    };
    if (stageId) {
      payload.stageId = stageId;
      payload.approverEmail = approverEmail;
    } else if (hasPermissions(RBAC_ROLES.VIEW_REQUESTAPPROVALS)) {
      payload.approverEmail = approverEmail;
    }
    mutate(payload);
  }, [mutate, period, status, selectedEmail, stageId, approverEmail]);
  useEffect(() => {
    if (period !== "all") {
      getPlanCriteriaList();
    }
  }, [period, getPlanCriteriaList, approverEmail]);

  useEffect(() => {
    if (tabs.length < Number.parseInt(tabKey.split("-")[1]) + 1) {
      setTabKey(`tab-0`);
    }
  }, [tabs, tabKey]);

  useEffect(() => {
    for (const [index, tab] of tabs.entries()) {
      if (tab.criteriaId === criteriaIdFromQueryParam) {
        setTabKey(`tab-${index}`);
        break; // Exit the loop after finding the matching tab, assuming there's only one match
      }
    }
  }, [criteriaIdFromQueryParam, tabs]);

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <EverLoader.LogoLottie className="w-20 h-20" />
      </div>
    );
  }

  if (tabs?.length === 0) {
    return (
      <div className="w-full h-full justify-center flex flex-col items-center gap-4">
        <img src={sadIcecream} alt="no data found" />
        <div className="flex flex-col gap-2">
          <EverTg.Heading3>No approval requests</EverTg.Heading3>
        </div>
      </div>
    );
  }

  return (
    <>
      <EverTabs
        className="h-full w-full overflow-auto flex-auto"
        onChange={setTabKey}
        activeKey={tabKey}
        key={tabKey}
      >
        {tabs.map((tab, index) => (
          <EverTabs.TabPane
            tab={
              <div
                className="flex gap-2 items-center"
                data-testid={`testId-${tab.planName}-${tab.criteriaName}`}
              >
                <EverTg.Caption
                  className={`text-ever-primary px-1.5 py-0.5 rounded-md ${
                    `tab-${index}` === tabKey
                      ? "bg-ever-primary-lite"
                      : "bg-ever-base"
                  }`}
                >
                  {tab.planName}
                </EverTg.Caption>
                <EverTg.SubHeading4 className="text-ever-base-content">
                  {tab.criteriaName}
                </EverTg.SubHeading4>
              </div>
            }
            key={`tab-${index}`}
          >
            <RenderTab
              tab={tab}
              staticColumns={staticColumns}
              hideCheckboxColumn={hideCheckboxColumn}
              period={period}
              status={status}
              selectedEmail={selectedEmail}
              lineItemTableRef={lineItemTableRef}
              stageId={stageId}
              rolesRefetch={rolesRefetch}
              approverEmail={approverEmail}
              refetchTimelineData={refetchTimelineData}
              applyFiltersFun={applyFiltersFun}
            />
          </EverTabs.TabPane>
        ))}
      </EverTabs>
    </>
  );
};
