import { gql, useQuery } from "@apollo/client";
import { CalendarIcon } from "@everstage/evericons/duotone";
import { endOfMonth, format, getYear, isValid, parseISO } from "date-fns";
import { debounce, isEmpty, isNil } from "lodash";
import { observer } from "mobx-react";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQuery as useReactQuery, useMutation } from "react-query";
import { useLocation } from "react-router-dom";
import { useRecoilValue, useSetRecoilState } from "recoil";
import { parse } from "valibot";

import {
  getApprovalConfig,
  getCommAdjustments,
} from "~/Api/ApprovalWorkflowService";
import { getPayoutPeriodsForYear } from "~/Api/CommissionActionService";
import { DATE_FORMATS, SEARCH_BOX } from "~/Enums";
import {
  breadcrumbAtom,
  myClient<PERSON>tom,
  navPortalAtom,
} from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { sortCallbackUtil } from "~/Utils/sortColumnsUtils";
import {
  EverButton,
  EverButtonGroup,
  EverDatePicker,
  EverInput,
  EverLabel,
  EverLoader,
  EverNavPortal,
  EverNumberBadge,
  EverSelect,
  EverTg,
  message,
} from "~/v2/components";

import { allCommissionAdjustmentsApprovalsSchema } from "./commission-adjustments-schemas";
import CommissionAdjustmentsTable from "./CommissionAdjustmentsTable";
import { APPROVAL_STATUS_FILTER } from "../approval-requests/payout-approvals/constants";
import { GET_All_COUNTRIES } from "../approval-requests/payout-approvals/graphql";
import { CustomBreadCrumb } from "../CustomBreadCrumb";

/**
 * @typedef {import("~/Utils/sortColumnsUtils").SortInfoType} SortInfoType
 */

const { DDMMMYYYY_datefns } = DATE_FORMATS;
const LONG_DATE_FORMAT = "dd-MMMM-yyyy";

const GET_PERIOD_LABELS = gql`
  {
    periodLabelList {
      label
      value
    }
  }
`;

const Render = observer(() => {
  const [searchTerm, setSearchTerm] = useState();
  const [pageSize, setPageSize] = useState(20);
  const [tableData, setTableData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [requestStatus, setRequestStatus] = useState(
    APPROVAL_STATUS_FILTER.ALL_REQUESTS
  );
  const [currencyCodeSymbolMap, setCurrencyCodeSymbolMap] = useState({});
  const [approvalsCountData, setApprovalsCountData] = useState({
    all: 0,
    pending: 0,
  });
  const [filterPeriod, setFilterPeriod] = useState(null);
  const [periodOptions, setPeriodOptions] = useState([]);
  const [selectablePeriods, setSelectablePeriods] = useState({});
  const [activeYear, setActiveYear] = useState(null);
  const [orderbyFields, setOrderbyFields] = useState(
    /** @type {SortInfoType[]}*/ ([])
  );

  const { accessToken } = useAuthStore();
  const navPortalLocation = useRecoilValue(navPortalAtom);
  const myAtom = useRecoilValue(myClientAtom);
  const setBreadcrumbName = useSetRecoilState(breadcrumbAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const { t } = useTranslation();
  const { rolesRefetch } = useEmployeeStore();

  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const urlPeriodFilter = params.get("period");
  const urlStatusFilter = params.get("status");
  const urlSearchFilter = params.get("search");
  const urlParamsSet = useRef(false);

  const { data: counrtiesData } = useQuery(GET_All_COUNTRIES);
  const customCalendar = clientFeatures?.customCalendar;

  const { loading: periodsLoading } = useQuery(GET_PERIOD_LABELS, {
    fetchPolicy: "no-cache",
    onCompleted: (data) => {
      if (data && data.periodLabelList && !customCalendar) {
        setPeriodOptions([
          { label: "All", value: "all" },
          ...data.periodLabelList,
        ]);
        setFilterPeriod("all");
      }
    },
  });

  const debounceFn = useMemo(
    () =>
      debounce((value) => {
        setSearchTerm(value);
      }, SEARCH_BOX.DEBOUNCE_TIME),
    [setSearchTerm]
  );

  const debounceWrapper = (value) => {
    const searchVal = value.target.value;
    if (
      searchVal.length === 0 ||
      searchVal.length >= SEARCH_BOX.MINIMUM_CHARS
    ) {
      debounceFn(searchVal);
    }
  };

  const onStatusChange = (value) => {
    setRequestStatus(value);
    setCurrentPage(1);
  };

  const updateActiveYear = (year) => {
    if (isNil(selectablePeriods[year])) {
      setActiveYear(String(year));
    }
  };

  function disabledDate(current) {
    if (isEmpty(selectablePeriods[getYear(current)])) {
      return true;
    }
    return !selectablePeriods[getYear(current)].includes(
      format(current, LONG_DATE_FORMAT)
    );
  }

  const sortCallback = (column) => {
    sortCallbackUtil(column, orderbyFields, setOrderbyFields);
  };

  const { isLoading } = useReactQuery(
    ["getPayoutPeriodsForYear", customCalendar, activeYear],
    () => getPayoutPeriodsForYear(activeYear, accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      enabled: customCalendar && !isEmpty(activeYear),
      onSuccess: (data) => {
        setSelectablePeriods({
          ...selectablePeriods,
          [activeYear]: data.map((item) => {
            return format(new Date(item), LONG_DATE_FORMAT, {
              timeZone: "UTC",
            });
          }),
        });
      },
    }
  );

  const { mutate: getCommAdjMutate } = useMutation(
    (payload) => getCommAdjustments(payload, accessToken),
    {
      onSuccess: (response) => {
        if (response.ok) {
          response.json().then((data) => {
            const tableRows = data?.adjData?.adjustmentsData?.map((ele) => {
              ele.instanceData = JSON.parse(ele.instanceData);
              return ele;
            });
            const parsedData = parse(
              allCommissionAdjustmentsApprovalsSchema,
              tableRows
            );
            setTableData(parsedData);
            setApprovalsCountData(data?.adjData?.counts);
            rolesRefetch();

            // To set the url search params
            let searchParams = new URLSearchParams(location.search);
            if (searchTerm) {
              searchParams.set("search", searchTerm);
            }

            if (filterPeriod) {
              searchParams.set(
                "period",
                filterPeriod && filterPeriod !== "all"
                  ? customCalendar
                    ? format(filterPeriod, "yyyy-MM-dd")
                    : format(endOfMonth(new Date(filterPeriod)), "yyyy-MM-dd")
                  : "all"
              );
            }

            if (requestStatus === APPROVAL_STATUS_FILTER.PENDING_REQUESTS) {
              searchParams.set("status", requestStatus);
            } else {
              searchParams.set("status", APPROVAL_STATUS_FILTER.ALL_REQUESTS);
            }
            const newSearchString = searchParams.toString();
            if (`?${newSearchString}` !== location.search) {
              window.history.replaceState(
                null,
                "",
                `${location.pathname}?${newSearchString}`
              );
            }
          });
        } else {
          response.json().then((data) => {
            if (data?.message) {
              message.error(data?.message);
            }
          });
        }
      },
    }
  );

  function getCommAdjustData() {
    const body = {
      status: requestStatus,
      period:
        filterPeriod && filterPeriod !== "all"
          ? customCalendar
            ? format(filterPeriod, "yyyy-MM-dd")
            : format(endOfMonth(new Date(filterPeriod)), "yyyy-MM-dd")
          : "all",
      offsetValue: (currentPage - 1) * pageSize,
      limitValue: pageSize,
      searchTerm: searchTerm || "",
      orderbyFields,
    };
    getCommAdjMutate(body);
  }

  useEffect(() => {
    if (counrtiesData) {
      let currencySymbolMap = {};
      counrtiesData.allActiveCountries.map((country) => {
        currencySymbolMap[country.currencyCode] = country.currencySymbol;
      });
      setCurrencyCodeSymbolMap(currencySymbolMap);
    }
  }, [counrtiesData]);

  useEffect(() => {
    if (urlParamsSet.current) {
      getCommAdjustData();
    }
  }, [
    requestStatus,
    currentPage,
    pageSize,
    searchTerm,
    filterPeriod,
    urlParamsSet.current,
    orderbyFields,
  ]);

  useEffect(() => {
    if (!urlParamsSet.current && customCalendar !== undefined) {
      if (urlPeriodFilter)
        setFilterPeriod(
          urlPeriodFilter === "all"
            ? "all"
            : customCalendar
            ? new Date(urlPeriodFilter)
            : format(new Date(urlPeriodFilter), "MMMM-yyyy", {
                timeZone: "UTC",
              })
        );
      if (urlSearchFilter) setSearchTerm(urlSearchFilter);
      if (urlStatusFilter) setRequestStatus(urlStatusFilter);
      urlParamsSet.current = true;
    }
  }, [customCalendar]);

  useEffect(() => {
    setBreadcrumbName({
      index: 0,
      title: `${t("COMMISSION_ADJUSTMENTS")} - Approvals`,
      disabled: true,
    });
  }, []);

  const { data: approvalConfigData } = useReactQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );
  return (
    <div className="w-full h-full">
      <CustomBreadCrumb
        approvalView={{
          value: "commission_adjustments",
          label: t("COMMISSION_ADJUSTMENTS"),
          link: "/approvals/commission-adjustments",
        }}
        approvalConfigData={approvalConfigData}
      />
      <EverNavPortal target={navPortalLocation}>
        <div className="flex justify-between items-center">
          <EverButtonGroup
            className="bg-ever-base-200"
            activeBtnType="text"
            activeBtnColor="primary"
            defActiveBtnIndex={
              requestStatus
                ? requestStatus === APPROVAL_STATUS_FILTER.ALL_REQUESTS
                  ? 0
                  : 1
                : 0
            }
            size="small"
            activeButtonClassname="!pr-1"
            inactiveButtonClassname="!pr-1"
          >
            <EverButton
              onClick={() =>
                onStatusChange(APPROVAL_STATUS_FILTER.ALL_REQUESTS)
              }
            >
              <div className="flex flex-row gap-2 items-center">
                All
                <EverNumberBadge count={approvalsCountData.all} />
              </div>
            </EverButton>
            <EverButton
              onClick={() =>
                onStatusChange(APPROVAL_STATUS_FILTER.PENDING_REQUESTS)
              }
            >
              <div className="flex flex-row gap-2 items-center">
                Pending Requests
                <EverNumberBadge count={approvalsCountData.pending} />
              </div>
            </EverButton>
          </EverButtonGroup>

          <div className="flex gap-3">
            <div className="flex gap-3 whitespace-nowrap items-center">
              {customCalendar ? (
                <>
                  <EverTg.Text>Show Period</EverTg.Text>
                  <EverDatePicker
                    allowClear={false}
                    wrapperClassname="h-9"
                    format={DDMMMYYYY_datefns}
                    defaultValue={
                      isValid(parseISO(filterPeriod))
                        ? parseISO(filterPeriod)
                        : null
                    }
                    disabledDate={disabledDate}
                    showToday={false}
                    onChange={(date) => setFilterPeriod(date)}
                    onPanelChange={(date) => updateActiveYear(getYear(date))}
                    size="small"
                    isPickerLoading={isLoading}
                    renderExtraFooter={() => (
                      <>
                        {isLoading ? (
                          <div className="absolute top-0 left-0 h-full w-full">
                            <EverLoader
                              wrapperClassName="absolute top-0 left-0 z-20"
                              indicatorType="spinner"
                            />
                          </div>
                        ) : null}
                      </>
                    )}
                  />
                </>
              ) : (
                <div className="flex items-center">
                  <EverLabel>Period</EverLabel>
                  <EverSelect
                    className="w-44"
                    value={filterPeriod}
                    onChange={(val) => setFilterPeriod(val)}
                    options={periodOptions}
                    size="small"
                    prependIcon={
                      periodsLoading ? (
                        <EverLoader.SpinnerLottie />
                      ) : (
                        <CalendarIcon className="w-4 h-4 text-ever-base-content-mid" />
                      )
                    }
                  />
                </div>
              )}
            </div>
            <EverInput.Search
              className="w-64"
              size="small"
              allowClear
              placeholder="Search by name or email"
              onChange={(e) => debounceWrapper(e)}
            />
          </div>
        </div>
      </EverNavPortal>
      <CommissionAdjustmentsTable
        rowData={tableData}
        pageSize={pageSize}
        setPageSize={setPageSize}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        currencyCodeSymbolMap={currencyCodeSymbolMap}
        accessToken={accessToken}
        refetchRequests={getCommAdjustData}
        approvalsCountData={approvalsCountData}
        requestStatus={requestStatus}
        searchTerm={searchTerm}
        getCommAdjustData={getCommAdjustData}
        customCalendar={customCalendar}
        totalPendingRows={approvalsCountData.pending}
        filterPeriod={filterPeriod}
        rolesRefetch={rolesRefetch}
        orderbyFields={orderbyFields}
        sortCallback={sortCallback}
      />
    </div>
  );
});

export default Render;
