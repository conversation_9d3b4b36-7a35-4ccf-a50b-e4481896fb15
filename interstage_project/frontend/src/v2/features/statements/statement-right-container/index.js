import { gql, useQuery } from "@apollo/client";
import { CoinsStackedIcon, TargetIcon } from "@everstage/evericons/duocolor";
import {
  CheckCircleIcon,
  TableIcon,
  ReceiptCheckIcon,
} from "@everstage/evericons/duotone";
import { EyeOffIcon } from "@everstage/evericons/outlined";
import { isEmpty, sortBy } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQuery as useReactQuery } from "react-query";
import { useLocation } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import { getApprovalConfig } from "~/Api/ApprovalWorkflowService";
import {
  APPROVAL_ENTITY_TYPES,
  APPROVAL_STATUS,
  ENTITY_KEY_DELIMITER,
  RBAC_ROLES,
  REQUEST_STATUS,
} from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { formatCurrencyWrapper } from "~/Utils/CurrencyUtils";
import {
  currentUTCTimeMsInString,
  formatAnyToDateYYYYMMDD,
  formatDateDDMMYYYY,
} from "~/Utils/DateUtils";
import {
  EverTabs,
  EverModal,
  EverHotToastMessage,
  toast,
  EverTg,
} from "~/v2/components";
import {
  ApprovalCard,
  processErrorMessage,
  TimelineWrapper,
  ApprovalWorkFlowTemplateSelectorWrapper,
} from "~/v2/features/approvals";
import CommissionOverview from "~/v2/features/statements/commission-overview";
import LastCalculatedInfo from "~/v2/features/statements/payee-header/LastCalculatedInfo";
import PayoutDetails from "~/v2/features/statements/payout-details";
import QuotaAttainment from "~/v2/features/statements/quota-attainment";

import PayoutTransactionComponent from "../PayoutTransactionData";

const USER_APPROVAL_DATA = gql`
  query getRequestForEntityKey(
    $entityKey: String!
    $status: String!
    $approver: String!
  ) {
    getRequestForEntityKey(
      entityKey: $entityKey
      status: $status
      approver: $approver
    ) {
      approvalRequestId
      stageInstanceId
      period
      approvalStage {
        dueDate
        stageName
        notes
      }
      approvalInstance {
        instanceData
      }
      payeeDetails {
        firstName
        lastName
        employeeEmailId
      }
    }
  }
`;

const getPlanDetails = (
  { planOverview, criteriaQEMap = {}, period = null, commPeriod = null },
  key,
  hasPermissions
) => {
  const unsortedPlanArray = Object.values(planOverview);
  const mainPlans = unsortedPlanArray.filter(
    (plan) => plan.planType === "MAIN"
  );
  const otherPlans = unsortedPlanArray.filter(
    (plan) => plan.planType !== "MAIN"
  );

  const sortedMainPlans = sortBy(mainPlans, ["planDisplayOrder"]);
  const sortedOtherPlans = sortBy(otherPlans, ["planDisplayOrder"]);

  const planArray = [...sortedMainPlans, ...sortedOtherPlans];
  const mappedPlanDetails = {};
  const formattedPlanDetails = planArray.map((plan, index) => {
    const filteredChildren = [];
    const criteriaDetails = sortBy(Object.values(plan.criteriaDetails), [
      "criteriaDisplayOrder",
    ]);
    mappedPlanDetails[plan.planId] = {
      planType: key,
      planId: plan.planId,
      planName: plan.planName,
      amount: plan.amount,
      criteriaDetails: [],
    };
    for (const [index, criteriaDetail] of criteriaDetails.entries()) {
      if (
        hasPermissions(RBAC_ROLES.VIEW_HIDDENCRITERIA) ||
        !criteriaDetail.isHiddenCriteria
      ) {
        if (
          Object.keys(criteriaQEMap).includes(criteriaDetail.criteriaId) &&
          criteriaQEMap[criteriaDetail.criteriaId]
        ) {
          criteriaDetail.quotaErosion =
            criteriaQEMap[criteriaDetail.criteriaId].quotaCurrencySymbol +
            formatCurrencyWrapper(
              criteriaQEMap[criteriaDetail.criteriaId].quotaErosion
            );
        }
        mappedPlanDetails[plan.planId].criteriaDetails.push(criteriaDetail);
        filteredChildren.push({
          planType: key,
          planId: plan.planId,
          planName: plan.planName,
          id: `${key}_plan_${index}_criteria_${index}`,
          period,
          commPeriod,
          ...criteriaDetail,
        });
      }
    }
    return {
      ...plan,
      id: `${key}_plan_${index}`,
      children: filteredChildren,
    };
  });
  return {
    planDetails: formattedPlanDetails,
    mappedPlanDetails,
  };
};

const getPayoutDetails = (payoutDetails, key) => {
  const formattedPayoutDetails = payoutDetails.map((periodData, index) => {
    return {
      id: `${key}_period_${index}`,
      period: periodData["period"],
      amount: periodData["amount"],
    };
  });
  return formattedPayoutDetails;
};

const getPrevDeferredDetails = (prevDeferredDetails, key, hasPermissions) => {
  const _prevDeferredDetails = Object.values(prevDeferredDetails);
  let mappedPrevDefDetails = {};
  const formattedPrevDeferredDetails = _prevDeferredDetails.map(
    (details, index) => {
      const { planDetails, mappedPlanDetails } = getPlanDetails(
        {
          planOverview: details.planDetails,
          period: details.period,
          commPeriod: details.commPeriod,
        },
        key,
        hasPermissions
      );
      mappedPrevDefDetails[details.period] = mappedPlanDetails;
      return {
        ...details,
        id: `${key}_prevDeferredDetails_${index}`,
        children: planDetails,
      };
    }
  );

  return {
    prevDeferredDetails: formattedPrevDeferredDetails,
    mappedPrevDefDetails,
  };
};

export function getCommissionTypes(t) {
  return {
    earnedCommissions: {
      label: t("EARNED_COMMISSIONS"),
      key: "EARNED_COMMISSIONS",
    },
    deferredCommissions: {
      label: t("DEFERRED_COMMISSIONS"),
      key: "DEFERRED_COMMISSIONS",
    },
    currentPayouts: {
      label: t("PAYOUT_FROM_CURRENT_PERIOD"),
      key: "CURRENT_PERIOD_PAYOUT",
    },
    previousDeferredCommissions: {
      label: t("PREVIOUSLY_DEFERRED_COMMISSIONS"),
      key: "PREV_DEFERRED_COMMISSIONS",
    },
    payoutArrears: {
      label: t("PAYOUT_ARREARS"),
      key: "PAYOUT_ARREARS",
    },
    adjustments: {
      label: t("ADJUSTMENTS"),
      key: "ADJUSTMENTS",
    },
    draws: {
      label: t("DRAW_ADJUSTMENTS"),
      key: "DRAW_ADJUSTMENTS",
    },
  };
}

const constructTableData = (
  overviewData,
  criteriaQEMap,
  showCommissionPercent,
  showSettlementView,
  hasPermissions,
  t
) => {
  const payoutSummary = [];
  const commissionSummary = [];
  // mappedOverviewData - used to get periods in previous deferred commissions
  const mappedOverviewData = {};
  const commissionTypes = getCommissionTypes(t);

  /**
   * The way payout summary and commission summary are constructed is:
   *
   * For settlement view, payout summary will have
   *  - Payout from current period
   *  - Payout from previously deferred commissions
   * commission summary will have
   *  - Earned Commissions
   *  - Deferred Commissions
   *
   * For non-settlement view, only payout summary will be there and it will have only
   * - Earned Commissions
   *
   * All other details like arrears, draws and adjustments are present in payout summary
   */
  if (overviewData) {
    if (!isEmpty(overviewData?.earnedCommissionDetails)) {
      const { planDetails, mappedPlanDetails } = getPlanDetails(
        {
          planOverview: overviewData.earnedCommissionDetails,
          criteriaQEMap,
        },
        "earnedCommissions",
        hasPermissions
      );
      mappedOverviewData["earnedCommissions"] = mappedPlanDetails;
      showSettlementView
        ? commissionSummary.push({
            id: "earnedCommissions",
            commissionName: commissionTypes["earnedCommissions"].label,
            amount: overviewData.earnedCommission,
            children: planDetails,
          })
        : payoutSummary.push({
            id: "earnedCommissions",
            commissionName: "Plans",
            ...(showCommissionPercent && {
              commissionPercentColumn: `${t("COMMISSION")} %`,
            }),
            amount: "",
            children: planDetails,
          });
    }

    if (
      showSettlementView &&
      !isEmpty(overviewData?.deferredCommissionDetails)
    ) {
      const { planDetails, mappedPlanDetails } = getPlanDetails(
        {
          planOverview: overviewData.deferredCommissionDetails,
        },
        "deferredCommissions",
        hasPermissions
      );
      mappedOverviewData["deferredCommissions"] = mappedPlanDetails;
      commissionSummary.push({
        id: "deferredCommissions",
        commissionName: commissionTypes["deferredCommissions"].label,
        amount: overviewData.deferredCommission,
        children: planDetails,
      });
    }

    if (showSettlementView && !isEmpty(overviewData?.currentPayoutDetails)) {
      const { planDetails, mappedPlanDetails } = getPlanDetails(
        {
          planOverview: overviewData.currentPayoutDetails,
        },
        "currentPayouts",
        hasPermissions
      );
      mappedOverviewData["currentPayouts"] = mappedPlanDetails;
      payoutSummary.push({
        id: "currentPayouts",
        commissionName: commissionTypes["currentPayouts"].label,
        amount: overviewData.currentPayout,
        children: planDetails,
      });
    }

    if (
      showSettlementView &&
      !isEmpty(overviewData?.previousCommissionDeferredDetails)
    ) {
      const { prevDeferredDetails, mappedPrevDefDetails } =
        getPrevDeferredDetails(
          overviewData.previousCommissionDeferredDetails,
          "previousDeferredCommissions",
          hasPermissions
        );
      mappedOverviewData["previousDeferredCommissions"] = mappedPrevDefDetails;
      payoutSummary.push({
        id: "previousDeferredCommissions",
        commissionName: commissionTypes["previousDeferredCommissions"].label,
        amount: overviewData.previousCommissionDeferred,
        children: prevDeferredDetails,
      });
    }

    if (!isEmpty(overviewData?.payoutArrearsDetails)) {
      payoutSummary.push({
        id: "payoutArrears",
        commissionName: commissionTypes["payoutArrears"].label,
        amount: overviewData.payoutArrears,
        children: getPayoutDetails(
          overviewData.payoutArrearsDetails,
          "payoutArrears"
        ),
      });
    }
    if (!isEmpty(overviewData?.adjustmentsDetails)) {
      payoutSummary.push({
        id: "adjustments",
        commissionName: commissionTypes["adjustments"].label,
        amount: overviewData.adjustments,
        children: overviewData.adjustmentsDetails.map((adjustment, index) => ({
          ...adjustment,
          id: `adjustments_${index}`,
        })),
      });
    }
    if (!isEmpty(overviewData?.drawsDetails) > 0) {
      payoutSummary.push({
        id: "draws",
        commissionName: commissionTypes["draws"].label,
        amount: overviewData.draws,
        children: overviewData.drawsDetails.map((draw, index) => ({
          ...draw,
          id: `draws_${index}`,
        })),
      });
    }
  }
  return {
    totalPayout: overviewData?.totalPayout ?? 0,
    payoutSummary: payoutSummary.filter((data) => data && data),
    commissionSummary: commissionSummary.filter((data) => data && data),
    mappedOverviewData,
    currentPayout: overviewData?.currentPayout,
  };
};

const StatementRightContainer = observer((props) => {
  const {
    store,
    selectedPeriod,
    payeePeriodOptions,
    clientFeatures,
    quotaOptions,
    overviewData,
    employeeEmailId,
    displayCurrency,
    refetchTimelineData,
    setRefetchTimeLineData,
    refreshData,
    isLocked,
    showCommissionPayouts,
    showApprovalBanner,
    approvalRequestedCount,
    updatedTime,
    showQuotaCards,
    refetchApprovalStatusData,

    localeId,
    setShowApprovalBanner,
  } = props;

  const { email: loggeduser, accessToken } = useAuthStore();
  const { showSettlementView, baseCurrency, setSelectedPeriod, criteriaQEMap } =
    store;
  const showCommissionPercent = clientFeatures?.showCommissionPercent;
  const currencySymbol = overviewData?.payeeCurrencySymbol || "";
  const { hasPermissions } = useUserPermissionStore();

  const [drawerData, setDrawerData] = useState({});
  const [approvalLoading, setApprovalLoading] = useState(false);
  const [showApprovalTemplateSelector, setShowApprovalTemplateSelector] =
    useState(false);
  const [approvalParams, setApprovalParams] = useState(null);
  const [approvalData, setApprovalData] = useState({});
  const { rolesRefetch } = useEmployeeStore();

  const [payoutApprovalsEnabled, setPayoutApprovalsEnabled] = useState(false);

  const { data: approvalConfigData } = useReactQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  const queryParams = new URLSearchParams(useLocation()?.search || {});
  const queryParamPeriod = queryParams.get("period");
  const queryParamStatus = queryParams.get("status");
  const queryParamEmail = queryParams.get("email");
  const queryParamStageId = queryParams.get("stage_id");

  useEffect(() => {
    if (queryParamPeriod) {
      payeePeriodOptions.find((period) => {
        const splitedPeriod = period.value?.split("<->");
        if (
          splitedPeriod[splitedPeriod?.length - 1]?.trim() === queryParamPeriod
        ) {
          setSelectedPeriod(period.value);
        }
      });
    }
  }, [queryParamPeriod]);

  useEffect(() => {
    if (approvalConfigData?.status === "SUCCESS") {
      const config = approvalConfigData?.data;
      setPayoutApprovalsEnabled(config?.payoutApprovals?.enabled || false);
    }
  }, [approvalConfigData]);

  const { t } = useTranslation();

  const period = payeePeriodOptions.find((period) => {
    const selectedValue =
      !isEmpty(selectedPeriod) && Object.keys(selectedPeriod)?.[0];
    return period.value === selectedValue;
  });

  const entityKey =
    employeeEmailId +
    ENTITY_KEY_DELIMITER +
    formatAnyToDateYYYYMMDD(period?.value?.split(" <-> ")[1]);

  const {
    data: arData,
    error: arError,
    refetch: arRefetch,
  } = useQuery(USER_APPROVAL_DATA, {
    variables: {
      entityKey: entityKey,
      status: REQUEST_STATUS.REQUESTED,
      approver: loggeduser,
    },
    fetchPolicy: "network-only",
  });

  const {
    totalPayout,
    payoutSummary,
    commissionSummary,
    mappedOverviewData,
    currentPayout,
  } = constructTableData(
    overviewData,
    criteriaQEMap,
    showCommissionPercent,
    showSettlementView,
    hasPermissions,
    t
  );

  const variables = {
    payeeEmail: employeeEmailId,
    psd: formatDateDDMMYYYY(period?.value?.split(" <-> ")[0]),
    ped: formatDateDDMMYYYY(period?.value?.split(" <-> ")[1]),
    displayCurrency: displayCurrency,
  };

  const updateDrawerData = (data) => {
    setDrawerData((drawerData) => {
      return {
        ...drawerData,
        ...variables,
        ...data,
      };
    });
  };

  useEffect(() => {
    if (arError) {
      toast.custom(
        () => (
          <EverHotToastMessage
            type="error"
            description="Error while fetching approval data"
          />
        ),
        { position: "top-center", duration: 3 }
      );
    }
    if (arData && arData.getRequestForEntityKey) {
      const data = arData.getRequestForEntityKey;
      const instanceData = data?.approvalInstance?.instanceData
        ? JSON.parse(data?.approvalInstance?.instanceData)
        : {};
      setApprovalData({
        fullName: `${data?.payeeDetails?.firstName} ${data?.payeeDetails?.lastName}`,
        employeeEmailId: data?.payeeDetails?.employeeEmailId,
        payoutAmount: instanceData?.payout,
        currency: instanceData?.currency,
        period: data?.period,
        approvalRequestId: data?.approvalRequestId,
        dueDate: data?.approvalStage?.dueDate,
        notes: data?.approvalStage?.notes,
      });
    } else {
      setApprovalData({});
    }
  }, [arData, arError]);

  useEffect(() => {
    arRefetch();
  }, [approvalRequestedCount]);

  const { approveApprovalRequest } = store;

  const refreshTimeLine = () => {
    store.timeLineRefetch?.();
  };

  const refreshRequiredData = () => {
    arRefetch();
  };

  const handleApproveRequest = (requestData) => {
    setApprovalLoading(true);
    const toastId = toast.custom(
      () => (
        <EverHotToastMessage
          type="loading"
          description="Approving request..."
        />
      ),
      { position: "top-center", duration: Infinity }
    );
    approveApprovalRequest({
      requestId: [requestData?.approvalRequestId],
    })
      .then((response) => {
        toast.remove(toastId);
        if (response.status == "SUCCESS") {
          toast.custom(
            () => (
              <EverHotToastMessage
                type="success"
                description={`You've approved the request!`}
              />
            ),
            { position: "top-center" }
          );
        } else {
          console.log("Error", response.message);
          processErrorMessage(response.message);
        }
      })
      .finally(() => {
        arRefetch();
        setApprovalLoading(false);
        refreshTimeLine();
        refetchApprovalStatusData();
        rolesRefetch();
      });
  };

  const onRequestApproval = () => {
    if (
      isLocked &&
      ![APPROVAL_STATUS.REQUESTED, APPROVAL_STATUS.APPROVED].includes(
        overviewData.approvalStatus
      )
    ) {
      setShowApprovalTemplateSelector(true);
      onClickRequestApproval([overviewData.payeeEmailId], false);
    } else {
      EverModal.warning({
        title: t("CANNOT_SEND_APPROVAL"),
      });
    }
  };

  const onClickRequestApproval = (payeeIds, bulk_mode) => {
    const payeeIdAmountCurrency = payeeIds.map((email) => {
      return {
        email_id: email,
        currency: overviewData.payeeCurrency,
        payout: overviewData.totalPayout,
        name: overviewData.payeeName,
      };
    });

    const ped = moment.utc(overviewData.periodEndDate);

    const data = {
      entity_type: APPROVAL_ENTITY_TYPES.PAYOUT,
      bulk_mode: bulk_mode,
      instance_params: {
        date: ped.format("DD-MM-YYYY"),
        instance_details: payeeIdAmountCurrency,
      },
    };
    setApprovalParams(data);
  };

  const selectedOverview = isEmpty(drawerData)
    ? {}
    : drawerData.planType === "previousDeferredCommissions"
    ? mappedOverviewData[drawerData.planType][drawerData.period]
    : mappedOverviewData[drawerData.planType];
  const tabDefaultKey =
    clientFeatures?.showApprovalFeature &&
    payoutApprovalsEnabled &&
    queryParamPeriod &&
    queryParamStatus &&
    queryParamEmail &&
    queryParamStageId
      ? "4"
      : "1";
  return (
    <div className="w-full" key={tabDefaultKey}>
      {!isEmpty(approvalData) &&
        showApprovalBanner &&
        clientFeatures?.showApprovalFeature && (
          <div className="mb-4">
            <ApprovalCard
              approvalData={approvalData}
              store={store}
              handleApproveRequest={handleApproveRequest}
              refetchData={arRefetch}
              refreshTimeLine={refreshTimeLine}
              refetchApprovalStatusData={refetchApprovalStatusData}
              loading={approvalLoading}
              closeDrawer={refreshTimeLine}
              customCalendar={clientFeatures?.customCalendar}
            />
          </div>
        )}
      <EverTabs
        className="h-full"
        defaultActiveKey={tabDefaultKey}
        onChange={() => {}}
        tabBarExtraContent={
          !clientFeatures?.showPayoutTableBreakdown && (
            <div className="pb-2 flex items-center justify-end">
              <LastCalculatedInfo
                updatedTime={updatedTime}
                showSettlementView={showSettlementView}
              />
            </div>
          )
        }
      >
        <EverTabs.TabPane
          tab={
            <span className="flex items-center gap-2">
              <CoinsStackedIcon className="w-5 h-5" />
              {t("PAYOUT_SUMMARY")}
            </span>
          }
          key="1"
        >
          <div
            className={twMerge(
              "w-full h-full flex flex-col gap-6",
              hasPermissions(RBAC_ROLES.VIEW_HIDDENCRITERIA) ? "pt-3" : "pt-6"
            )}
          >
            <HiddenCriteriaInfo
              viewHiddenCriteria={hasPermissions(
                RBAC_ROLES.VIEW_HIDDENCRITERIA
              )}
            />
            {!hasPermissions(RBAC_ROLES.VIEW_PAYOUTVALUEOTHERS) &&
            employeeEmailId !== loggeduser ? (
              <div className="h-full flex items-center justify-center">
                <div>{`You don't have permission to view this`}</div>
              </div>
            ) : payoutSummary.length > 0 ? (
              <CommissionOverview
                view="payoutSummary"
                rowData={payoutSummary}
                updateDrawerData={updateDrawerData}
                currencySymbol={currencySymbol}
                localeId={localeId}
                totalPayout={totalPayout}
                currentPayout={currentPayout}
                periodLabel={period?.label || payeePeriodOptions[0].label}
                paidAmount={overviewData?.totalPaidAmount}
                pendingAmount={overviewData?.pendingAmount}
                paymentStatus={overviewData?.paymentStatus}
                employeeEmailId={employeeEmailId}
                hasActiveSettlementRules={
                  overviewData?.hasActiveSettlementRules
                }
                previousCommissionDeferred={
                  overviewData?.previousCommissionDeferred
                }
                variablePay={overviewData?.variablePay}
                showCommissionPercent={showCommissionPercent}
                showSettlementView={showSettlementView}
                showCommissionPayouts={showCommissionPayouts}
              />
            ) : (
              <></>
            )}
          </div>
        </EverTabs.TabPane>

        {showSettlementView && (
          <EverTabs.TabPane
            tab={
              <span className="flex items-center gap-2">
                <ReceiptCheckIcon className="w-5 h-5" />
                {t("COMMISSION_SUMMARY")}
              </span>
            }
            key="2"
          >
            <div
              className={twMerge(
                "w-full h-full flex flex-col gap-6",
                hasPermissions(RBAC_ROLES.VIEW_HIDDENCRITERIA) ? "pt-3" : "pt-6"
              )}
            >
              <HiddenCriteriaInfo
                viewHiddenCriteria={hasPermissions(
                  RBAC_ROLES.VIEW_HIDDENCRITERIA
                )}
              />
              {!hasPermissions(RBAC_ROLES.VIEW_PAYOUTVALUEOTHERS) &&
              employeeEmailId !== loggeduser ? (
                <div className="h-full flex items-center justify-center">
                  <div>{`You don't have permission to view this`}</div>
                </div>
              ) : commissionSummary.length > 0 ? (
                <CommissionOverview
                  view="commissionSummary"
                  rowData={commissionSummary}
                  updateDrawerData={updateDrawerData}
                  currencySymbol={currencySymbol}
                  localeId={localeId}
                  totalPayout={totalPayout}
                  currentPayout={currentPayout}
                  periodLabel={period?.label || payeePeriodOptions[0].label}
                  store={store}
                  paidAmount={overviewData?.totalPaidAmount}
                  pendingAmount={overviewData?.pendingAmount}
                  paymentStatus={overviewData?.paymentStatus}
                  employeeEmailId={employeeEmailId}
                  hasActiveSettlementRules={
                    overviewData?.hasActiveSettlementRules
                  }
                  previousCommissionDeferred={
                    overviewData?.previousCommissionDeferred
                  }
                  variablePay={overviewData?.variablePay}
                  showCommissionPercent={showCommissionPercent}
                  showSettlementView={showSettlementView}
                  showCommissionPayouts={showCommissionPayouts}
                />
              ) : (
                <></>
              )}
            </div>
          </EverTabs.TabPane>
        )}

        {hasPermissions(RBAC_ROLES.VIEW_QUOTAS) && quotaOptions.length > 0 && (
          <EverTabs.TabPane
            tab={
              <span className="flex items-center gap-2">
                <TargetIcon className="w-5 h-5" />
                {t("QUOTA_ATTAINMENT")}
              </span>
            }
            key="3"
          >
            <div className="w-full h-full pt-6">
              <QuotaAttainment
                quotaOptions={quotaOptions}
                store={store}
                showQuotaCards={showQuotaCards}
              />
            </div>
          </EverTabs.TabPane>
        )}
        {clientFeatures?.showApprovalFeature && payoutApprovalsEnabled && (
          <EverTabs.TabPane
            tab={
              <span className="flex items-center gap-2">
                <CheckCircleIcon
                  className="w-5 h-5"
                  data-testid="approvals-tab"
                />
                Approvals
              </span>
            }
            key="4"
          >
            <div className="w-full h-full pt-6 flex flex-col gap-4 relative">
              <TimelineWrapper
                entityKey={entityKey}
                store={store}
                refreshRequiredData={refreshRequiredData}
                refetchTimelineDataFromProps={refetchTimelineData}
                onRequestApproval={onRequestApproval}
                refetchApprovalStatusData={refetchApprovalStatusData}
                refreshData={refreshData}
                isLocked={isLocked}
                period={formatAnyToDateYYYYMMDD(
                  period?.value?.split(" <-> ")[1]
                )}
                arRefetch={arRefetch}
                setShowApprovalBanner={setShowApprovalBanner}
                showDeleteButton={true}
              />
              {showApprovalTemplateSelector && (
                <ApprovalWorkFlowTemplateSelectorWrapper
                  requestParams={approvalParams}
                  showSelector={showApprovalTemplateSelector}
                  setShowSelector={setShowApprovalTemplateSelector}
                  refetch={() => {
                    setRefetchTimeLineData(currentUTCTimeMsInString());
                    refreshData();
                    arRefetch();
                    refetchApprovalStatusData();
                  }}
                />
              )}
            </div>
          </EverTabs.TabPane>
        )}
        {clientFeatures?.showPayoutTableBreakdown && (
          <EverTabs.TabPane
            tab={
              <span className="flex items-center gap-2">
                <TableIcon className="w-5 h-5" data-testid="approvals-tab" />
                Breakdown
              </span>
            }
            key="5"
          >
            <PayoutTransactionComponent
              selectedPeriod={selectedPeriod}
              employeeEmailId={employeeEmailId}
            ></PayoutTransactionComponent>
          </EverTabs.TabPane>
        )}
      </EverTabs>
      <PayoutDetails
        handleClose={() => setDrawerData({})}
        currencySymbol={currencySymbol}
        localeId={localeId}
        drawerData={drawerData}
        periodLabel={period?.label || payeePeriodOptions[0].label}
        showSettlementView={showSettlementView}
        selectedOverview={selectedOverview}
        updateDrawerData={updateDrawerData}
        showCommissionPayouts={showCommissionPayouts}
        isBaseCurrency={baseCurrency === displayCurrency}
      />
    </div>
  );
});

export default StatementRightContainer;

function HiddenCriteriaInfo({ viewHiddenCriteria }) {
  return (
    <>
      {viewHiddenCriteria && (
        <div className="px-3 py-2 gap-x-2 items-center flex !bg-ever-info-lite rounded-md border border-solid border-ever-chartColors-19">
          <EyeOffIcon className="h-4 w-4 text-ever-info-hover" />
          <EverTg.Description className="text-xs leading-4 text-ever-info-lite-content font-medium">
            Denotes components that are hidden from payees.
          </EverTg.Description>
        </div>
      )}
    </>
  );
}
