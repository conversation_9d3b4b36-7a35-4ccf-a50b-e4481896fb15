import { DownloadIcon } from "@everstage/evericons/outlined";
import { AgGridReact } from "ag-grid-react";
import { useSupabasePostgresChanges } from "everstage-supabase";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useRef,
} from "react";
import { Link } from "react-router-dom";

import { SUPABASE_CONSTANTS, ACTIVITY_LOGS_STATUS } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { isFileValid } from "~/Utils/ActivityLogUtils";
import { EverTg, EverButton, EverLoader, EverFormatter } from "~/v2/components";
import { everAgGridOptions } from "~/v2/components/ag-grid";
import { useModules } from "~/v2/hooks";
import { noTasks } from "~/v2/images";

import { fetchAllActivity } from "../activity-logs/api";
import { statusMap, downloadFile, FileHeader } from "../activity-logs/index";

export default function RecentActivity() {
  const { accessToken } = useAuthStore();
  const modules = useModules();

  const [gridApi, setGridApi] = useState(null);
  const [rowData, setRowData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const { getDefaultOptions } = everAgGridOptions;
  const gridRef = useRef();
  const { timeZone } = useEmployeeStore();
  const userTimeZone = timeZone ? timeZone : "UTC";

  const basePath = modules.isCPQ ? "/cpq" : "";

  const realtimePostgresChangesFilter = [
    {
      event: SUPABASE_CONSTANTS.BULK_IMPORT_DATA.EVENT_TYPE[0],
      filter: `task_name=eq.${SUPABASE_CONSTANTS.BULK_IMPORT_DATA.TASK_NAME}`,
    },
    {
      event: SUPABASE_CONSTANTS.BULK_IMPORT_DATA.EVENT_TYPE[1],
      filter: `task_name=eq.${SUPABASE_CONSTANTS.BULK_IMPORT_DATA.TASK_NAME}`,
    },
  ];

  const channelPrefix = SUPABASE_CONSTANTS.BULK_IMPORT_DATA.CHANNEL_NAME;

  let updates = useSupabasePostgresChanges(realtimePostgresChangesFilter, {
    channelPrefix,
  });

  useEffect(() => {
    if (updates && rowData) {
      const eventType = updates?.eventType;
      if (eventType === SUPABASE_CONSTANTS.BULK_IMPORT_DATA.EVENT_TYPE[0]) {
        fetchRecentActivity();
        return;
      }

      let newRowData = [...rowData];
      const {
        task_id: taskId,
        status: status,
        completed_at: completedAt,
        result: { file: file } = {},
      } = updates?.new?.data || {};

      const index = newRowData.findIndex((row) => row.id === taskId);
      if (index >= 0) {
        newRowData[index].status = status;
        newRowData[index].completedAt = completedAt
          ? completedAt.toString().replace(" ", "T")
          : completedAt;
        newRowData[index].file = file;
      }

      gridApi?.setGridOption("rowData", newRowData);
    }
  }, [updates]);

  useEffect(() => {
    fetchRecentActivity();
  }, []);

  const fetchRecentActivity = useCallback(async () => {
    setIsLoading(true);
    const data = {
      pageSize: 5,
      pageNumber: 0,
      filters: {
        job: ["DATAIMPORT"],
      },
    };
    const response = await fetchAllActivity(data, accessToken);
    if (response.ok) {
      const responseData = await response.json();
      const { data } = responseData;
      setRowData(data.rows);
    } else {
      console.log("Error fetching recent activity:", response);
    }
    setIsLoading(false);
  }, []);

  const onGridReady = (params) => {
    setGridApi(params.api);
  };

  const columnDefs = [
    {
      headerName: "Activity",
      field: "taskName",
    },
    {
      headerName: "Object",
      field: "object",
    },
    {
      headerName: "Status",
      field: "status",
      cellRenderer: (rowData) => {
        const value = rowData.value;
        if (!Object.keys(statusMap).includes(value)) return "-";
        return statusMap[value];
      },
    },
    {
      headerName: "Start",
      field: "createdAt",
      sortable: true,
      sort: "desc",
      cellRenderer: (rowData) => {
        const value = rowData.value;
        if (value) {
          return (
            <EverFormatter.DateTime
              targetTimeZone={userTimeZone}
              date={value}
              type="shortTime"
              className="font-normal"
            />
          );
        }
        return "-";
      },
    },
    {
      headerName: "End",
      field: "completedAt",
      sortable: true,
      sort: "desc",
      cellRenderer: (rowData) => {
        const value = rowData.value;
        if (value) {
          return (
            <EverFormatter.DateTime
              targetTimeZone={userTimeZone}
              date={value}
              type="shortTime"
              className="font-normal"
            />
          );
        }
        return "-";
      },
    },
    {
      headerName: "By",
      field: "createdBy",
      cellRenderer: (rowData) => {
        if (!rowData.value) {
          return "-";
        }
        const { firstName, lastName } = rowData.value;
        const fullName = `${
          firstName.charAt(0).toUpperCase() + firstName.slice(1)
        } ${lastName.charAt(0).toUpperCase() + lastName.slice(1)}`;

        return fullName;
      },
    },
    {
      headerName: "File",
      field: "file",
      width: 100,
      headerComponentFramework: FileHeader,
      cellRenderer: (rowData) => {
        const statusValue = rowData.node.data.status;
        const value = rowData.value;
        const activity = "DATA_IMPORT";
        return onDownloadFile(statusValue, value, activity);
      },
    },
  ];

  const onDownloadFile = (statusValue, filePath, activity) => {
    if (filePath === undefined) return null; //We won't get any file name when the task is in Processing, Waiting in queue

    return ![
      ACTIVITY_LOGS_STATUS.PROCESSING,
      ACTIVITY_LOGS_STATUS.PENDING,
    ].includes(statusValue) && isFileValid(filePath, 120) ? (
      <DownloadIcon
        className="w-5 h-5 text-ever-base-content-mid cursor-pointer"
        onClick={() => {
          downloadFile(filePath, accessToken, activity);
        }}
      />
    ) : null;
  };

  const defaultColDef = useMemo(() => ({
    sortable: false,
    menuTabs: [],
    resizable: true,
  }));

  function NoDataImportTasks() {
    return (
      <>
        <div className="flex flex-col items-center justify-center">
          <img src={noTasks} className="w-56 h-56" />
          <div className="flex flex-col text-center gap-2">
            <EverTg.Heading2 className="text-ever-base-content">
              Ready to record your data import activities?
            </EverTg.Heading2>
            <EverTg.Description className="text-ever-base-content-mid">
              Start importing data to track and view your historical data import
              activities in this section.
            </EverTg.Description>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="h-full w-full flex flex-col space-y-2">
        <EverTg.SubHeading4 className="text-ever-base-content">
          Recent activities
        </EverTg.SubHeading4>
        {isLoading ? (
          <div className="h-full w-full flex items-center justify-center">
            <EverLoader.SpinnerLottie className="w-20 h-20" />
          </div>
        ) : rowData.length === 0 ? (
          <NoDataImportTasks />
        ) : (
          <div className="flex flex-col space-y-4">
            <div className="flex gap-1">
              <EverTg.Text className="text-ever-base-content-mid">
                Track, review, and analyze system events to ensure data
                integrity. To view all activities, go to
              </EverTg.Text>
              <Link to={`${basePath}/settings/activity-logs`}>
                <EverButton
                  type="link"
                  color="primary"
                  className={"!px-0 !py-0 !h-max"}
                >
                  Activity Logs
                </EverButton>
              </Link>
            </div>
            <div ref={gridRef} className="ag-theme-material h-[300px] w-full">
              <AgGridReact
                {...getDefaultOptions({ type: "md" })}
                columnDefs={columnDefs}
                defaultColDef={defaultColDef}
                rowData={rowData}
                onGridReady={onGridReady}
                domLayout="autoHeight"
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
}
