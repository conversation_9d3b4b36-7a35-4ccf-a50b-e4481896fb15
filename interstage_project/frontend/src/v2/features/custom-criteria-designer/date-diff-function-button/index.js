import { ArrowLeftOutlined, FunctionOutlined } from "@ant-design/icons";
import { Modal, Button, Space, Spin, Row, Col, Typography } from "antd";
import { includes, isEmpty } from "lodash";
import { observer } from "mobx-react";
import React, { useState } from "react";

import { DATATYPE } from "~/Enums";
import { useVariableStore } from "~/GlobalStores/VariableStore";
import { getTokenFromName } from "~/Utils/ExpressionDesignerUtils";
import { EverSelect } from "~/v2/components";
import { ModalTransition } from "~/v2/legacy/components/CustomModal";

const Option = EverSelect.Option;

const DateDiffFunctionButton = observer((props) => {
  const {
    name,
    onSelection,
    enabled,
    onFocusChange,
    datasheetId,
    buttonStyleProps,
    dsVariableStore,
  } = props;
  const [visible, showModal] = useState(false);
  const [token1, setToken1] = useState(null);
  const [token2, setToken2] = useState(null);
  const [period, setPeriod] = useState("Day");
  const [diffType, setDiffType] = useState("Fiscal");

  const { optionsMapByDS, variableTokens, loading } = dsVariableStore;
  const { sDataTypes } = useVariableStore();

  const optionsMap = optionsMapByDS[datasheetId];

  const onCancel = () => {
    showModal(false);
    setToken1(null);
    setToken2(null);
    setPeriod("Day");
    setDiffType("Fiscal");
  };

  return (
    <div>
      <Button
        onClick={() => {
          showModal(true);
          onFocusChange(false);
        }}
        disabled={!enabled}
        size={"small"}
        icon={<FunctionOutlined />}
        style={buttonStyleProps}
      >
        {name}
      </Button>
      <Modal
        destroyOnClose={true}
        visible={visible}
        centered={true}
        title={
          <Space direction="horizontal">
            <ArrowLeftOutlined
              style={{ marginTop: 5 }}
              onClick={() => {
                onFocusChange(true);
                onCancel();
              }}
            />
            <Typography.Text>{name}</Typography.Text>
          </Space>
        }
        onOk={() => {
          let diffTypeLabel = includes(["Quarter", "Halfyear", "Year"], period)
            ? `,${diffType}`
            : "";
          let fnDisplayLabel = `${name}(${token1.name},${token2.name},${period}${diffTypeLabel})`;
          let fnArgs = [token1, token2, period, diffType];
          onSelection({
            functionName: name,
            name: fnDisplayLabel,
            args: fnArgs,
            dataType: DATATYPE.INTEGER,
          });
          onCancel();
        }}
        onCancel={onCancel}
        width={800}
        cancelButtonProps={{ className: "btn-cancel" }}
        okText="Apply"
        okButtonProps={{
          disabled:
            isEmpty(token1) ||
            isEmpty(token2) ||
            isEmpty(period) ||
            (includes(["Quarter", "Halfyear", "Year"], period) &&
              isEmpty(diffType)),
        }}
        {...ModalTransition}
      >
        <Row>
          <Col span={24}>
            <Space>
              {`${name} (`}
              <EverSelect
                className="w-44"
                showArrow={true}
                placeholder="Start Date Column"
                onChange={(v) => {
                  setToken1(getTokenFromName(v, optionsMap));
                }}
                notFoundContent={
                  loading ? (
                    <>
                      <Spin size="small" /> Loading Variables
                    </>
                  ) : null
                }
                showSearch
              >
                {getSelectOptions(
                  getVariables(variableTokens, datasheetId, sDataTypes)
                )}
              </EverSelect>
              <EverSelect
                className="w-44"
                showArrow={true}
                placeholder="End Date Column"
                onChange={(v) => {
                  setToken2(getTokenFromName(v, optionsMap));
                }}
                notFoundContent={
                  loading ? (
                    <>
                      <Spin size="small" /> Loading Variables
                    </>
                  ) : null
                }
                showSearch
              >
                {getSelectOptions(
                  getVariables(variableTokens, datasheetId, sDataTypes)
                )}
              </EverSelect>
              <EverSelect
                className="w-32"
                showArrow={true}
                value={period}
                placeholder="Select Unit"
                onChange={(v) => setPeriod(v)}
              >
                <Option value="Day">Day</Option>
                <Option value="Month">Month</Option>
                <Option value="Quarter">Quarter</Option>
                <Option value="Halfyear">Halfyear</Option>
                <Option value="Year">Year</Option>
              </EverSelect>
              {includes(["Quarter", "Halfyear", "Year"], period) && (
                <EverSelect
                  className="w-32"
                  showArrow={true}
                  value={diffType}
                  onChange={setDiffType}
                >
                  <Option value="Fiscal">FISCAL</Option>
                  <Option value="Calendar">CALENDAR</Option>
                </EverSelect>
              )}
              {")"}
            </Space>
          </Col>
        </Row>
      </Modal>
    </div>
  );
});

const getVariables = (variableTokens, datasheetId, sDataTypes) => {
  return variableTokens.filter((x) => {
    const value =
      x.datasheetId === datasheetId &&
      (x.dataType === DATATYPE.DATE ||
        sDataTypes[DATATYPE.DATE].compatibleTypes.includes(x.dataType));
    return value;
  });
};

const getSelectOptions = (options) => {
  return options.map((option) => (
    <Option value={option.name} key={option.name}>
      {option.name}
    </Option>
  ));
};

export default DateDiffFunctionButton;
