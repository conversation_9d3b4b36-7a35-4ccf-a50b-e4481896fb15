import {
  InfoCircleIcon,
  ArrowNarrowRightIcon,
  TrashIcon,
} from "@everstage/evericons/outlined";
import { Table } from "antd";
import { sortBy, uniqBy } from "lodash";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMutation, useQuery } from "react-query";
import { twMerge } from "tailwind-merge";
import { parse } from "valibot";

import {
  getApprovalConfig,
  getCommissionAdjustmentThreshold,
  getPayeeCurrencies,
  getUsersToNotifyOnSkipApproval,
  saveCommissionAdjustmentThreshold,
  saveUsersToNotifyOnSkipApproval,
  updateApprovalConfig,
} from "~/Api/ApprovalWorkflowService";
import { PAYOUT_APPROVAL_TYPES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { capitalizeFirstLetter } from "~/Utils";
import {
  EverTg,
  EverSwitch,
  EverRadio,
  EverLoader,
  message,
  EverTooltip,
  EverLabel,
  EverSelect,
  EverButton,
  EverInput,
  useOnClickOutside,
  EverModal,
} from "~/v2/components";

import { ApprovalTypeModal } from "./ApprovalTypeModal";
import { MultiTabSelector, selectTypes } from "./MultiTabSelector";
import { approvalListSchema, currenciesSchema } from "./workflows-schemas";

/**
 * Settings component for managing various approval settings.
 * @component
 * @returns {JSX.Element} The rendered Settings component.
 */

export function Settings() {
  // State variables to manage the settings
  const [isPayoutEnabled, setPayoutEnabled] = useState(false);
  const [isDatasheetEnabled, setDatasheetEnabled] = useState(false);
  const [isCommissionEnabled, setCommissionEnabled] = useState(false);
  const [isCommissionPlanEnabled, setCommissionPlanEnabled] = useState(false);
  const [approvalType, setApprovalType] = useState(
    PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL
  );
  const { accessToken } = useAuthStore();
  const { t } = useTranslation();

  const [approvalModalVisibility, setApprovalModalVisibility] = useState(false);

  // COMMISSION ADJUSTMENTS - START

  const [currency, setCurrency] = useState();
  const [currencyOptions, setCurrencyOptions] = useState([]);
  const [thresholdRange, setThresholdRange] = useState({
    min: null,
    max: null,
  });
  const [selectorSelected, setSelectorSelected] = useState(false);
  const [rowData, setRowData] = useState([]);
  const [notifyData, setNotifyData] = useState({
    dynamic: [],
    groups: [],
    users: [],
  });
  const usersUpdateRef = useRef(null);

  const { mutate: mutateSaveTreshold } = useMutation(
    (payload) => saveCommissionAdjustmentThreshold(payload, accessToken),
    {
      onSuccess: (response) => {
        if (response.ok) {
          response.json().then(() => {
            message.success({
              content: "Threshold values saved successfully.",
            });
          });
          getThresholdData();
        } else {
          response.json().then((data) => {
            if (data?.message) {
              message.error({ content: data?.message });
            } else {
              message.error({ content: "Saving threshold failed." });
            }
          });
        }
      },
    }
  );

  const { mutate: mutateSaveNotifyUsers } = useMutation(
    (payload) => saveUsersToNotifyOnSkipApproval(payload, accessToken),
    {
      onSuccess: (response) => {
        if (response.ok) {
          response.json().then(() => {
            message.success({
              content: "User notify list saved.",
            });
          });
          usersUpdateRef.current = null;
        } else {
          response.json().then((data) => {
            if (data?.message) {
              message.error({ content: data?.message });
            } else {
              message.error({
                content: "Saving Skip Approval Notify data failed.",
              });
            }
          });
          usersUpdateRef.current = null;
        }
      },
    }
  );

  const columns = [
    {
      title: "Currency",
      dataIndex: "currency",
      key: "currency",
      width: 140,
      className: "!pl-3",
    },
    {
      title: "Auto Approval threshold",
      dataIndex: "thresholdRange",
      key: "thresholdRange",
      width: 240,
      render: (range) => {
        return (
          <div className="flex gap-3 items-center">
            {range.min}
            <ArrowNarrowRightIcon className="w-3 h-3 text-ever-primary-hover" />
            {range.max}
          </div>
        );
      },
    },
    {
      title: "",
      dataIndex: "",
      key: "Delete",
      align: "center",
      render: (props) => {
        return (
          <EverButton.Icon
            icon={<TrashIcon />}
            size="small"
            color="error"
            type="text"
            className="ml-5"
            onClick={() =>
              onUpdateThreshold({
                type: "remove",
                deleteCurrency: props.currency,
              })
            }
          />
        );
      },
    },
  ];
  const dropdownRef = useRef(null);
  useOnClickOutside(dropdownRef, () => setSelectorSelected(false));

  function handleNotifyDataChange({ type, payload }) {
    let newData;
    switch (type) {
      case selectTypes.USER: {
        newData = {
          ...notifyData,
          users: payload,
        };
        break;
      }
      case selectTypes.DYNAMIC: {
        newData = {
          ...notifyData,
          dynamic: payload,
        };
        break;
      }
      case selectTypes.GROUP: {
        newData = {
          ...notifyData,
          groups: payload,
        };
        break;
      }
      default: {
        break;
      }
    }
    setNotifyData(newData);
    if (usersUpdateRef.current) clearTimeout(usersUpdateRef.current);
    usersUpdateRef.current = setTimeout(() => {
      saveSkipApprovalNotifyData(newData);
    }, 2000);
  }

  async function onUpdateThreshold({ type, deleteCurrency }) {
    const data = {};
    rowData.map((ele) => {
      data[ele.currency] = {
        min: ele.thresholdRange.min,
        max: ele.thresholdRange.max,
      };
    });

    if (type === "add") {
      if (thresholdRange.min >= thresholdRange.max) {
        return message.error(
          "Threshold's max value should be greater than min value."
        );
      }
      data[currency] = {
        min: thresholdRange.min,
        max: thresholdRange.max,
      };
    } else if (type === "remove") {
      delete data[deleteCurrency];
    }

    const payload = {
      thresholdValues: {
        ...data,
      },
    };

    return mutateSaveTreshold(payload);
  }
  async function getCurrencies() {
    return new Promise((resolve, reject) => {
      getPayeeCurrencies(accessToken).then((response) => {
        if (response.ok) {
          response.json().then((data) => {
            const parsedData = parse(currenciesSchema, data?.data);
            let opt = [];
            parsedData?.forEach((curr) => {
              opt.push({
                label: curr,
                value: curr,
              });
            });
            let uniqueOpt = uniqBy(opt, "label");
            setCurrencyOptions(sortBy(uniqueOpt, ["label"]));
          });
          resolve();
        } else {
          response.json().then((data) => {
            if (data?.message) {
              message.error({ content: data?.message });
            } else {
              message.error({ content: "Fetching currencies data failed." });
            }
          });
          reject();
        }
      });
    });
  }
  async function getThresholdData() {
    return new Promise((resolve, reject) => {
      getCommissionAdjustmentThreshold(accessToken).then((response) => {
        if (response.ok) {
          response.json().then((data) => {
            const currs = Object.keys(data.thresholdValues);
            let tableRowData = [];
            for (let curr of currs) {
              tableRowData.push({
                currency: curr.toUpperCase(),
                thresholdRange: {
                  min: data.thresholdValues[curr].min,
                  max: data.thresholdValues[curr].max,
                },
              });
            }
            setRowData(tableRowData);
          });
          resolve();
        } else {
          response.json().then((data) => {
            if (data?.message) {
              message.error({ content: data?.message });
            } else {
              message.error({ content: "Fetching threshold data failed." });
            }
          });
          reject();
        }
      });
    });
  }
  async function getSkipApprovalNotifyData() {
    return new Promise((resolve, reject) => {
      getUsersToNotifyOnSkipApproval(accessToken).then((response) => {
        if (response.ok) {
          response.json().then((data) => {
            const notifyDataBE = data?.usersToNotifyOnSkipApproval;
            const parsedData = parse(approvalListSchema, notifyDataBE);
            setNotifyData({
              dynamic: parsedData.dynamic.map((ele) => {
                return { label: capitalizeFirstLetter(ele), value: ele };
              }),
              groups: parsedData.groups.map((ele) => {
                return { label: ele.groupName, value: ele.groupId };
              }),
              users: parsedData.users.map((ele) => {
                return {
                  label: ele.fullName,
                  value: ele.employeeEmailId,
                  profileImage: ele.profilePicture,
                };
              }),
            });
          });
          resolve();
        } else {
          response.json().then((data) => {
            if (data?.message) {
              message.error({ content: data?.message });
            } else {
              message.error({
                content: "Fetching Skip Approval Notify data failed.",
              });
            }
          });
          reject();
        }
      });
    });
  }
  async function saveSkipApprovalNotifyData(notifyData) {
    const payload = {
      users_to_notify_on_skip_approval: {
        dynamic: notifyData.dynamic.map((ele) => ele.value),
        groups: notifyData.groups.map((ele) => ele.value),
        users: notifyData.users.map((ele) => ele.value),
      },
    };
    return mutateSaveNotifyUsers(payload);
  }

  function handleCommissionChange(value) {
    if (value === false) {
      EverModal.confirm({
        title: `Are you sure you want to turn off ${t(
          "COMMISSION"
        )} Adjustments approvals?`,
        subtitle: `Disabling the ${t(
          "COMMISSION"
        )} adjustment setting will result in the cancellation of all approvals currently in the requested status, and the approval statuses of retrospective adjustments will be hidden.`,
        okText: "Confirm",
        cancelText: "Cancel",
        centered: true,
        onOk: () => {
          setCommissionEnabled(value);
          triggerUpdate(
            isPayoutEnabled,
            approvalType,
            isDatasheetEnabled,
            value,
            isCommissionPlanEnabled
          );
        },
      });
    } else {
      setCommissionEnabled(value);
      triggerUpdate(
        isPayoutEnabled,
        approvalType,
        isDatasheetEnabled,
        value,
        isCommissionPlanEnabled
      );
    }
  }
  function handleCommissionPlanChange(value) {
    if (value === false) {
      EverModal.confirm({
        title: `Are you sure you want to turn off ${t(
          "COMMISSION"
        )} Plan approvals?`,
        subtitle: `Disabling the ${t(
          "COMMISSION"
        )} Plan setting will result in the cancellation of all approvals currently in the requested status. Are you sure?`,
        okText: "Confirm",
        cancelText: "Cancel",
        centered: true,
        onOk: () => {
          setCommissionPlanEnabled(value);
          triggerUpdate(
            isPayoutEnabled,
            approvalType,
            isDatasheetEnabled,
            isCommissionEnabled,
            value
          );
        },
      });
    } else {
      setCommissionPlanEnabled(value);
      triggerUpdate(
        isPayoutEnabled,
        approvalType,
        isDatasheetEnabled,
        isCommissionEnabled,
        value
      );
    }
  }

  useEffect(() => {
    if (isCommissionEnabled) {
      getThresholdData();
      getCurrencies();
      getSkipApprovalNotifyData();
    }
  }, [isCommissionEnabled]);

  // COMMISSION ADJUSTMENTS - END

  const updateApprovalConfiguration = useMutation(
    (payload) => updateApprovalConfig(payload, accessToken),
    {
      onError: (error) => {
        console.log("error");
        message.dismiss();
        setPayoutEnabled(true);
        setApprovalType(approvalType);
        const msg =
          error?.message ||
          "There was an issue updating this setting. Please try again later.";
        message.error(msg);
      },
      onSuccess: () => {
        message.dismiss();
        message.success("Approval settings updated successfully");
        window.location.reload();
      },
    }
  );

  const { data, isLoading } = useQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (data?.status === "SUCCESS") {
      const config = data?.data;
      const approvalType = config?.payoutApprovals?.lineItemLevelApproval
        ? PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL
        : PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL;
      setPayoutEnabled(config?.payoutApprovals?.enabled ?? false);
      setApprovalType(approvalType);
      setDatasheetEnabled(config?.datasheetApprovals?.enabled ?? false);
      setCommissionEnabled(
        config?.commissionAdjustmentApprovals?.enabled ?? false
      );
      setCommissionPlanEnabled(
        config?.commissionPlanApprovals?.enabled ?? false
      );
    }
  }, [data]);

  const triggerUpdate = (
    isApprovalEnabled,
    approvalType,
    isDatasheetEnabled,
    isCommissionEnabled,
    isCommissionPlanEnabled
  ) => {
    message.loading("Updating Approval Config...");
    const notifyUsersObject = {
      users: notifyData.users.map((user) => user.value),
      groups: notifyData.groups.map((group) => group.value),
      dynamic: notifyData.dynamic.map((dynamicItem) => dynamicItem.value),
    };
    const thresholdValuesObject = {};
    rowData.forEach((item) => {
      let currency = item.currency.toLowerCase();
      thresholdValuesObject[currency] = {
        min: item.thresholdRange.min,
        max: item.thresholdRange.max,
      };
    });
    updateApprovalConfiguration.mutate({
      approval_config: {
        payout_approvals: {
          enabled: isApprovalEnabled,
          line_item_level_approval:
            approvalType === PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL,
          statement_level_approval:
            approvalType === PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL,
        },
        datasheet_approvals: {
          enabled: isDatasheetEnabled,
        },
        commission_adjustment_approvals: {
          enabled: isCommissionEnabled,
          threshold_values: isCommissionEnabled ? thresholdValuesObject : {},
          users_to_notify_on_skip_approval: isCommissionEnabled
            ? notifyUsersObject
            : {
                users: [],
                groups: [],
                dynamic: [],
              },
        },
        commission_plan_approvals: {
          enabled: isCommissionPlanEnabled,
        },
      },
    });
  };

  if (isLoading) {
    return (
      <div className="flex  w-full h-full items-center justify-center">
        <EverLoader.SpinnerLottie className="w-20 h-20" />
      </div>
    );
  }

  const handleApprovalTypeChange = () => {
    const result =
      approvalType === PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL
        ? PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL
        : PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL;
    setApprovalType(result);
    triggerUpdate(
      isPayoutEnabled,
      result,
      isDatasheetEnabled,
      isCommissionEnabled,
      isCommissionPlanEnabled
    );
  };
  // JSX for the Settings component
  return (
    <div className="pl-1 pb-8">
      <ApprovalTypeModal
        setApprovalModalVisibility={setApprovalModalVisibility}
        approvalModalVisibility={approvalModalVisibility}
        handleApprovalTypeChange={handleApprovalTypeChange}
        setPayoutEnabled={setPayoutEnabled}
      />
      <div className="flex flex-col gap-8">
        {/* Payouts Approval Section */}
        <div className="flex gap-3 items-start">
          {/* Switch for Payouts Approval */}
          <EverSwitch
            size="small"
            className="mt-0.5"
            checked={isPayoutEnabled}
            onChange={(value) => {
              setPayoutEnabled(value);

              if (value === false) {
                setApprovalModalVisibility(true);
              } else {
                triggerUpdate(
                  value,
                  approvalType,
                  isDatasheetEnabled,
                  isCommissionEnabled,
                  isCommissionPlanEnabled
                );
              }
            }}
          />
          {/* Information about Payouts Approval */}
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
              <EverTg.SubHeading4 className="text-ever-base-content flex items-center gap-1">
                Approval for {t("PAYOUTS")}
              </EverTg.SubHeading4>
              <EverTg.Text className="text-ever-base-content-mid">
                When enabled, users can request approvals for{" "}
                {t("PAYOUTS").toLowerCase()} before processing them
              </EverTg.Text>
            </div>

            {/* Radio buttons for approval type */}
            <div className="relative flex items-center">
              <EverRadio.Group
                className="gap-2"
                onChange={() => {
                  setApprovalModalVisibility(true);
                }}
                value={approvalType}
                disabled={!isPayoutEnabled}
              >
                <EverRadio
                  value={PAYOUT_APPROVAL_TYPES.STATEMENT_LEVEL}
                  label="Statement level approvals"
                />
                <EverRadio
                  value={PAYOUT_APPROVAL_TYPES.LINE_ITEM_LEVEL}
                  label="Line item level approvals"
                />
              </EverRadio.Group>
              <EverTooltip
                title={
                  "Line items in hidden criteria, adjustments and draws are not considered for approval"
                }
              >
                <InfoCircleIcon className="w-4 h-4" />
              </EverTooltip>
            </div>
          </div>
        </div>

        {/* <div className="flex gap-3 items-start">
       
        <EverSwitch
          size="small"
          className="mt-0.5"
          checked={isDatasheetEnabled}
          onChange={(value) => {
            setDatasheetEnabled(value);
            triggerUpdate(
              isPayoutEnabled,
              approvalType,
              value,
              isCommissionEnabled
            );
          }}
        />
        
        <div className="flex flex-col gap-2">
          <EverTg.SubHeading4 className="text-ever-base-content">
            Approval for Datasheet adjustments
          </EverTg.SubHeading4>
          <EverTg.Text className="text-ever-base-content-mid">
            When enabled, only approved adjustments will be considered by
            commission plans.
          </EverTg.Text>
        </div>
     </div>  */}

        <div className="flex gap-3 items-start">
          <EverSwitch
            size="small"
            className="mt-0.5"
            checked={isCommissionEnabled}
            onChange={(value) => {
              handleCommissionChange(value);
            }}
          />

          <div className="flex flex-col gap-2">
            <EverTg.SubHeading4 className="text-ever-base-content">
              Approval for {t("COMMISSION_ADJUSTMENTS")}
            </EverTg.SubHeading4>
            <EverTg.Text className="text-ever-base-content-mid">
              When enabled, only approved {t("ADJUSTMENTS").toLowerCase()} will
              be added to the statement.
            </EverTg.Text>
          </div>
        </div>
        <div
          className={twMerge(
            "transition-all h-max pl-11 w-[550px]",
            isCommissionEnabled ? "h-max" : "h-0 hidden"
          )}
        >
          <div className="flex flex-col gap-2 w-[412px]">
            <div>
              <EverLabel required>
                Add the threshold amount for automatic approval
              </EverLabel>
            </div>
            <div className="flex gap-4">
              <div className="flex">
                <EverSelect
                  onChange={setCurrency}
                  placeholder="Currency"
                  options={currencyOptions}
                  className={`w-[100px] h-9 [&>.ant-select>.ant-select-selector]:!rounded-r-none [&>.ant-select>.ant-select-selector]:!border-r-0`}
                  getPopupContainer={(trigger) => trigger.parentNode}
                ></EverSelect>
                <div className="flex">
                  <EverInput.Number
                    placeholder="From"
                    onChange={(value) => {
                      setThresholdRange({ ...thresholdRange, min: value });
                    }}
                    bordered={false}
                    className="!rounded-none [&>.ant-input-number-input-wrap>.ant-input-number-input]:!rounded-none !border-r-0 w-36"
                    disabled={!currency}
                    precision={0}
                  ></EverInput.Number>
                  <div className="h-full flex items-center border-t border-b border-r-0 border-l-0 border-solid border-ever-base-400 px-1">
                    <ArrowNarrowRightIcon className="w-4 h-4 text-ever-primary-hover" />
                  </div>
                  <EverInput.Number
                    placeholder="To"
                    className="!rounded-l-none [&>.ant-input-number-input-wrap>.ant-input-number-input]:!rounded-l-none !border-l-0 w-36"
                    onChange={(value) => {
                      setThresholdRange({ ...thresholdRange, max: value });
                    }}
                    disabled={!currency}
                    precision={0}
                  ></EverInput.Number>
                </div>
              </div>
              <EverButton
                color="primary"
                type="filled"
                onClick={() => onUpdateThreshold({ type: "add" })}
                disabled={
                  thresholdRange.min === null || thresholdRange.max === null
                }
              >
                Update
              </EverButton>
            </div>
          </div>
          <Table
            dataSource={rowData}
            columns={columns}
            pagination={false}
            size="small"
            bordered
            className="mt-6 w-full thresholdTable"
          />
          <div className="flex flex-col mt-6 gap-3 w-[506px]">
            <EverTg.SubHeading4 className="text-ever-base-content">
              Notify when approval is overriden
            </EverTg.SubHeading4>
            <MultiTabSelector
              siderMuliTabSelectorRef={dropdownRef}
              placeholder="Choose who needs to be notified"
              isSelectorSelected={selectorSelected}
              isSelectorUnSelected={selectorSelected}
              setIsSelectorSelectedAction={setSelectorSelected}
              dynamicSelectedData={notifyData.dynamic}
              setSelectedData={handleNotifyDataChange}
              groupSelectedData={notifyData.groups}
              userSelectedData={notifyData.users}
            />
          </div>
        </div>
        <div className="flex gap-3 items-start">
          <EverSwitch
            size="small"
            className="mt-0.5"
            checked={isCommissionPlanEnabled}
            onChange={(value) => {
              handleCommissionPlanChange(value);
            }}
          />

          <div className="flex flex-col gap-2">
            <EverTg.SubHeading4 className="text-ever-base-content">
              Approval for {t("COMMISSION_PLANS")}
            </EverTg.SubHeading4>
            <EverTg.Text className="text-ever-base-content-mid">
              When enabled, users can submit plans for approval prior to
              publishing them
            </EverTg.Text>
          </div>
        </div>
      </div>
    </div>
  );
}
