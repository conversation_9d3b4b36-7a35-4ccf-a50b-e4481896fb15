import { EyeOffIcon, ScissorsCutIcon } from "@everstage/evericons/duocolor";
import { EraserIcon, RowsIcon } from "@everstage/evericons/duotone";
import { FunctionIcon } from "@everstage/evericons/outlined";
import { AgGridReact } from "ag-grid-react";
import { Dropdown, Menu } from "antd";
import { cloneDeep, isEmpty } from "lodash";
import { observer } from "mobx-react";
import React, {
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
  useImperativeHandle,
  useMemo,
} from "react";
import { useQuery } from "react-query";
import { useLocation } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import {
  DATA_ORIGIN,
  RBAC_ROLES,
  DATASHEET_VIEW_ID,
  PIVOT_AGG_MAP,
  DATATYPE,
} from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { useVariableStore } from "~/GlobalStores/VariableStore";
import { capitalizeFirstLetter } from "~/Utils";
import { addSerialNumber } from "~/Utils/agGridUtils";
import { formatCurrencyWrapper } from "~/Utils/CurrencyUtils";
import { sortCallbackUtil } from "~/Utils/sortColumnsUtils";
import {
  EverHotToastNotification,
  EverTg,
  EverTooltip,
  IconButton,
  toast,
  EverFormatter,
} from "~/v2/components";
import { CustomHeader, DynamicPagination } from "~/v2/components/ag-grid";
import {
  AgGridIconsMap,
  dataTypeColorMap,
} from "~/v2/components/ag-grid/AgGridIconsMap";
import { getDefaultOptions } from "~/v2/components/ag-grid/ever-ag-grid-options";
import {
  comparator,
  dateComparator,
  numComparator,
} from "~/v2/components/ag-grid/utils";
import { formatDate } from "~/v2/components/ever-formatter/EverFormatter";
import { dogTearingNewspaper, noDataAvailable } from "~/v2/images";

import { AdjustmentDrawer } from "./adjustment-drawer";
import { FormulaFieldModal } from "./formula-field-modal";
import { LoaderTable } from "./LoaderTable";
import { CustomiseColumns } from "./sidebar/CustomiseColumns";
import { PivotTable } from "./sidebar/PivotTable";
import { DatasheetContext } from "../../DatasheetStore";
import { getDataSheetData, getViewTableData } from "../../restApi";

/**
 * Represents the DataTable component.
 *
 * @param {Object} props - The props for the DataTable component.
 * @param {Array} props.tabs - The tabs array to show the tab headers.
 * @param {Function} props.setTabs - Setter for the tabs state used when table data is fetched.
 * @param {Function} props.setDsVariables - Setter function to set Datasheet variables used in expression box.
 * @param {string} props.viewId - View Id of the current tab visible in UI.
 * @param {Object} props.inProgressAdjustments - Object containing the adjustments that are in progress.
 * @param {Object} props.canvasRef - Reference to the canvas element.
 * @param {Object} props.gridRef - Reference to the ag-grid table.
 * @param {Object} props.gridApi - API for interacting with the grid.
 * @param {string} props.activeKey - The active key for the current tab.
 * @param {Object} props.adjustedRowKeys - Object containing the keys of adjusted rows.
 * @param {Function} props.editAdjustmentMenu - Function to generate the dropdown menu for each row.
 * @param {boolean} props.isEditAdjustment - Indicates if an adjustment is being edited.
 * @param {Function} props.setIsEditAdjustment - Setter function for the isEditAdjustment state.
 * @param {string} props.adjustmentOperation - The current adjustment operation for Adjustment Drawer.
 * @param {Function} props.setAdjustmentOperation - Setter function for the adjustmentOperation state.
 * @param {Object} props.adjustmentRecord - The current adjustment record user for Adjustment Drawer.
 * @param {Function} props.setAdjustmentRecord - Setter function for the adjustmentRecord state.
 * @param {Function} props.setAllDataCount - Setter function for the total data count.
 * @returns {JSX.Element} React component
 */

export const DataTable = observer(
  ({
    tabs,
    setTabs,
    setDsVariables,
    viewId,
    inProgressAdjustments,
    canvasRef,
    gridRef,
    gridApi,
    activeKey,
    adjustedRowKeys,
    editAdjustmentMenu,
    isEditAdjustment,
    setIsEditAdjustment,
    adjustmentOperation,
    setAdjustmentOperation,
    adjustmentRecord,
    setAdjustmentRecord,
    setAllDataCount,
    pivotConfig,
    setPivotConfig,
    handleCreateViewSuccess,
    pivotConfigPayload,
    setPivotConfigPayload,
  }) => {
    const COLUMN_WIDTHS_KEY = "datasheetTableWidths";
    const COLUMN_WIDTHS_STACK = "datasheetTableWidthsStack";
    const datasheetStore = useContext(DatasheetContext);
    const { pathname, search, hash } = useLocation();

    const { dataTypesById } = useVariableStore();
    const { accessToken } = useAuthStore();
    const { hasPermissions } = useUserPermissionStore();

    const parentDivRef = useRef(null);

    const isURLSynced = useRef(false);
    const URLErrorShown = useRef(false);

    const [tableData, setTableData] = useState("init");
    const [pageSize, setPageSize] = useState(50);
    const [currentPage, setCurrentPage] = useState(1);
    const [displayPagination, setDisplayPagination] = useState(false);
    const [columns, setColumns] = useState([]);
    const [adjColumns, setAdjColumns] = useState(null);
    const [isError, setIsError] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");
    const [orderModel, setOrderModel] = useState([]);
    const [formulaFieldVisible, setFormulaFieldVisible] = useState(false);
    const [formulaFieldDetails, setFormulaFieldDetails] = useState({});
    const [gridLoading, setGridLoading] = useState(false);

    // This useEffect initializes the pivotConfig from datasheet details on the first load.
    // It checks if pivotConfig is empty to avoid overwriting user changes when datasheet details are updated again.
    useEffect(() => {
      const newConfig = {};
      // Check if datasheet details are available
      if (datasheetStore?.datasheetDetails) {
        const clonedDatasheetDetails = cloneDeep(
          datasheetStore?.datasheetDetails
        );
        // Iterate over each view in the datasheet details
        clonedDatasheetDetails?.views.forEach((view) => {
          // Populate newConfig with view-specific pivot data and set pivot mode to true
          newConfig[view?.view_id] = {
            pivotData: view?.pivot_data || {},
            isPivotMode: true,
          };
        });
        // Update the pivotConfig state with the new configuration
        // Retain existing pivot data for the 'ALL_DATA' view if it exists
        setPivotConfig({
          ...newConfig,
          ...(pivotConfig[DATASHEET_VIEW_ID.ALL_DATA]
            ? {
                [DATASHEET_VIEW_ID.ALL_DATA]:
                  pivotConfig[DATASHEET_VIEW_ID.ALL_DATA],
              }
            : {}),
        });
      }
    }, [datasheetStore.datasheetDetails]);
    const columnStateApplied = useRef(false);

    function getQueryParams() {
      const queryParams = {
        pageSize,
        pageNumber: currentPage,
        ...(datasheetStore.dsFilterExpressions[activeKey] &&
        datasheetStore.dsFilterExpressions[activeKey]?.length > 0 &&
        (activeKey === DATASHEET_VIEW_ID.ALL_DATA ||
          !hasPermissions(RBAC_ROLES.MANAGE_DATABOOK))
          ? { filters: datasheetStore.dsFilterExpressions[activeKey] }
          : !hasPermissions(RBAC_ROLES.MANAGE_DATABOOK)
          ? { filters: [] }
          : {}),

        ...(activeKey === DATASHEET_VIEW_ID.ALL_DATA &&
        !isEmpty(pivotConfigPayload?.[DATASHEET_VIEW_ID.ALL_DATA]?.pivotData) &&
        pivotConfigPayload?.[DATASHEET_VIEW_ID.ALL_DATA]?.isPivotMode
          ? {
              pivot_details: pivotConfigPayload?.[activeKey]?.pivotData,
            }
          : !pivotConfigPayload?.[activeKey]?.isPivotMode &&
            !isEmpty(pivotConfigPayload?.[activeKey]?.pivotData)
          ? { pivot_details: {} }
          : {}),
      };
      if (orderModel.length > 0)
        queryParams.sort_columns_by = orderModel.map((ele) => {
          return { col: ele.column, order: ele.order };
        });
      return queryParams;
    }

    function setColumnWidths(params) {
      const savedColWidths =
        JSON.parse(localStorage.getItem(COLUMN_WIDTHS_KEY)) || {};
      if (
        savedColWidths[datasheetStore.datasheetId] &&
        savedColWidths[datasheetStore.datasheetId][activeKey]
      ) {
        const currentColState =
          savedColWidths[datasheetStore.datasheetId][activeKey];
        const currentColIds = params.api
          .getColumnState()
          .map((col) => col.colId);

        // Filter out columns that no longer exist
        const filteredColState = currentColState.filter((colState) =>
          currentColIds.includes(colState.colId)
        );

        // Add new columns to the saved state
        const newColumns = currentColIds
          .filter(
            (colId) =>
              !filteredColState.some((colState) => colState.colId === colId)
          )
          .map((colId) => ({
            colId,
            width: params.api
              .getColumnState()
              .find((col) => col.colId === colId).width,
          }));

        const updatedColState = [...filteredColState, ...newColumns];

        // Retain the order of columns based on currentColIds
        const orderedColState = currentColIds.map((colId) =>
          updatedColState.find((colState) => colState.colId === colId)
        );
        params.api.applyColumnState({
          state: orderedColState,
          applyOrder: true,
        });

        columnStateApplied.current = true; // Set the flag to true after applying the state
      }
    }

    function saveColumnStateToLocalstorage(
      datasheetId,
      activeKey,
      columnState
    ) {
      const savedColWidths =
        JSON.parse(localStorage.getItem(COLUMN_WIDTHS_KEY)) || {};
      let savedColStack =
        JSON.parse(localStorage.getItem(COLUMN_WIDTHS_STACK)) || [];

      const currentWidths = savedColWidths[datasheetId] || {};

      savedColWidths[datasheetId] = {
        ...currentWidths,
        [activeKey]: columnState,
      };

      savedColStack = savedColStack.filter((id) => id !== datasheetId);
      savedColStack.unshift(datasheetId);

      if (savedColStack.length > 10) {
        const oldestId = savedColStack.pop();
        delete savedColWidths[oldestId];
      }

      localStorage.setItem(COLUMN_WIDTHS_KEY, JSON.stringify(savedColWidths));
      localStorage.setItem(COLUMN_WIDTHS_STACK, JSON.stringify(savedColStack));
    }

    const { isLoading, isFetching, refetch } = useQuery(
      [
        "getTableData",
        orderModel,
        datasheetStore.dsFilterExpressions[activeKey],
        pivotConfigPayload,
        activeKey,
        pageSize,
        currentPage,
      ],
      () => {
        setIsError(false);
        setErrorMessage("");
        if (
          datasheetStore.isDatasheetDetailsRefetching ||
          datasheetStore.datasheetDetailsIsLoading
        )
          return;
        return activeKey === DATASHEET_VIEW_ID.ALL_DATA ||
          activeKey === DATASHEET_VIEW_ID.ALL_ADJUSTMENTS
          ? getDataSheetData(
              accessToken,
              datasheetStore.datasheetId,
              getQueryParams()
            )
          : getViewTableData(
              accessToken,
              datasheetStore.datasheetId,
              activeKey,
              getQueryParams()
            );
      },
      {
        retry: false,
        cacheTime: 0,
        refetchOnWindowFocus: false,
        enabled: activeKey !== DATASHEET_VIEW_ID.ALL_ADJUSTMENTS,
        onSettled: (data, error) => {
          if (error) {
            console.log("Error occurred:", error);
            setIsError(true);
            error.message && setErrorMessage(error.message);
          } else {
            setTableData(data);
            const currTabs = [...tabs];
            currTabs.forEach((ele) => {
              if (
                ele.view_id === activeKey &&
                activeKey !== DATASHEET_VIEW_ID.ALL_ADJUSTMENTS
              ) {
                ele.total_records = data.total_records;
              }
            });
            setTabs([...currTabs]);
            if (pivotConfig[activeKey]) {
              setPivotConfig({
                ...pivotConfig,
                [activeKey]: {
                  ...pivotConfig[activeKey],
                  total_records: data.total_records,
                },
              });
            }
            setDsVariables(data.variables);
            datasheetStore.setAdjustmentVariables(data.variables);
            activeKey === DATASHEET_VIEW_ID.ALL_DATA &&
              setAllDataCount(data.total_records);
            columnStateApplied.current = false;
            datasheetStore.setHasHiddenColumns(
              data?.hidden_columns?.length > 0
            );
            datasheetStore.setHiddenColumns(data?.hidden_columns);
          }
        },
      }
    );
    datasheetStore.setTableDataRefetch(refetch);

    useImperativeHandle(canvasRef, () => ({
      dsDataRefetch: refetch,
    }));

    function onFormulaFieldClose() {
      setFormulaFieldVisible(false);
      setFormulaFieldDetails({});
    }

    const menuItemsCallback = useCallback((params) => {
      if (params.column.pinned === "left") {
        return [
          {
            name: "Pin to Right",
            action: () => {
              const column = params.column;
              params.api.setColumnPinned(column, "right");
            },
          },
          {
            name: "Unpin",
            action: () => {
              const column = params.column;
              params.api.setColumnPinned(column, null);
            },
          },
        ];
      } else if (params.column.pinned === "right") {
        return [
          {
            name: "Pin to Left",
            action: () => {
              const column = params.column;
              params.api.setColumnPinned(column, "left");
            },
          },
          {
            name: "Unpin",
            action: () => {
              const column = params.column;
              params.api.setColumnPinned(column, null);
            },
          },
        ];
      } else {
        return [
          {
            name: "Pin to Left",
            action: () => {
              const column = params.column;
              params.api.setColumnPinned(column, "left");
            },
          },
          {
            name: "Pin to Right",
            action: () => {
              const column = params.column;
              params.api.setColumnPinned(column, "right");
            },
          },
        ];
      }
    }, []);

    const sortCallback = (column) => {
      sortCallbackUtil(column, orderModel, setOrderModel);
    };

    const menu = (params) => {
      function openAdjustmentDrawer(operation) {
        setAdjustmentOperation(operation);
        setAdjustmentRecord(params);
        updateURL(operation, params);
      }
      return (
        <Menu>
          <EverTg.Caption.Medium className="text-ever-base-content">
            Adjust Data
          </EverTg.Caption.Medium>
          <Menu.Item
            className="!py-2 !px-3 flex gap-2 !h-8"
            key="ignoreRecord"
            onClick={() => {
              openAdjustmentDrawer("Ignore");
            }}
            disabled={false}
          >
            <div className="flex gap-2  items-center">
              <EyeOffIcon className="w-4 h-4 text-ever-base-content-mid" />
              <EverTg.Caption className="text-ever-base-content">
                Ignore Record
              </EverTg.Caption>
            </div>
          </Menu.Item>
          <Menu.Item
            className="!py-2 !px-3 flex gap-2 !h-8"
            key="updateRecord"
            onClick={() => {
              openAdjustmentDrawer("Update");
            }}
            disabled={false}
          >
            <div className={twMerge("flex gap-2 items-center")}>
              <RowsIcon className="w-4 h-4 text-ever-base-content-mid" />
              <EverTg.Caption className="text-ever-base-content">
                Update Record
              </EverTg.Caption>
            </div>
          </Menu.Item>
          <Menu.Item
            className="!py-2 !px-3 flex gap-2 !h-8"
            key="splitRecord"
            onClick={() => {
              openAdjustmentDrawer("Split");
            }}
            disabled={false}
          >
            <div className={twMerge("flex gap-2 items-center")}>
              <ScissorsCutIcon className="w-4 h-4 text-ever-base-content-mid" />
              <EverTg.Caption className="text-ever-base-content">
                Split Record
              </EverTg.Caption>
            </div>
          </Menu.Item>
        </Menu>
      );
    };

    // Define the custom cell renderer function outside the useEffect hook
    const customCellRenderer = (params) => {
      const isAdjustmentInProgress =
        inProgressAdjustments[params.data.row_key] === true;
      const isAdjustedRow = adjustedRowKeys[params.data.row_key];
      let displayValue =
        params.valueFormatted && params.valueFormatted !== "Invalid Number"
          ? params.valueFormatted
          : params.value;
      displayValue =
        displayValue && params?.colDef?.dataType === "Date"
          ? formatDate({
              date: params.value,
              type: "date",
            })
          : displayValue;

      return (
        <div className="flex items-center">
          {displayValue}
          {(isAdjustedRow || isAdjustmentInProgress) && (
            <AdjustmentTag applied={!isAdjustmentInProgress} />
          )}
        </div>
      );
    };

    // The column definitions for AgGrid are set here

    function getColDefs(vars) {
      return [
        ...vars.map((var_obj, index) => {
          const isCalculatedField =
            var_obj.field_order > 0 || var_obj?.meta_data?.infix != null;
          const datatype = dataTypesById[var_obj.data_type_id];
          const sortInfo = orderModel.find((item) => {
            return item.column === var_obj.system_name;
          });

          const baseColDef = {
            dataType: datatype,
            field: var_obj.system_name,
            headerName: var_obj.display_name,
            sortable: false,
            comparator: comparator,
            width: 80,
            headerComponentParams: {
              datasheetTable: true,
              ...(hasPermissions(RBAC_ROLES.MANAGE_DATABOOK)
                ? {
                    sortable: true,
                    serverSideSortable: true,
                    sortOrder: sortInfo?.order ?? "",
                    sortByField: var_obj.system_name,
                    sortCallback,
                  }
                : {}),
              ...(isCalculatedField
                ? {
                    isCalculatedField: true,
                    customMenuIcon: (
                      <EverTooltip
                        title="Formula Field. Click to view formula."
                        placement="bottom"
                        overlayClassName="dataSheetTooltip"
                      >
                        <div
                          className="flex items-center m-0 mr-1 border border-solid border-ever-base-400 rounded-sm gap-2 p-1"
                          onClick={(event) => {
                            event.stopPropagation();
                            setFormulaFieldVisible(true);
                            setFormulaFieldDetails(var_obj);
                          }}
                        >
                          <FunctionIcon className="w-4 h-4" />
                          <div
                            className={twMerge(
                              "h-4 w-4 rounded-sm flex items-center justify-center",
                              dataTypeColorMap[datatype]
                            )}
                          >
                            <AgGridIconsMap
                              className="w-3.5 h-3.5"
                              name={datatype}
                            />
                          </div>
                        </div>
                      </EverTooltip>
                    ),
                  }
                : { menuIcon: datatype }),
            },
            ...(datatype === "Percentage" && {
              comparator: numComparator,
              type: "rightAligned",
              valueGetter: (params) => {
                if (params.data[var_obj.system_name] === "") return null;
                return params.data[var_obj.system_name];
              },
            }),
            ...(datatype === "Integer" && {
              comparator: numComparator,
              type: "rightAligned",
              valueGetter: (params) => {
                if (params.data[var_obj.system_name] === "") return null;
                return params.data[var_obj.system_name];
              },
              cellRenderer: (params) => (
                <EverTooltip
                  title={formatCurrencyWrapper(params.value, {
                    decimalPlaces: 6,
                    truncate: true,
                  })}
                  placement="bottom"
                  overlayClassName="dataSheetTooltip"
                >
                  {formatCurrencyWrapper(params.value)}
                </EverTooltip>
              ),
            }),
            ...(datatype === "Date" && {
              comparator: dateComparator,
              cellRenderer: (params) => {
                if (params?.value) {
                  return (
                    <EverFormatter.Date
                      className="font-normal"
                      date={params?.value}
                    />
                  );
                }

                return "";
              },
            }),
            ...(datatype === "Boolean" && {
              cellDataType: false,
              cellRenderer: (params) => {
                return <div className="flex">{params.valueFormatted}</div>;
              },
              valueFormatter: (params) => {
                if (params.value === true) {
                  return "true";
                } else if (params.value === false) {
                  return "false";
                } else {
                  return "";
                }
              },
            }),
          };

          // Add AdjustmentTag to the first column
          if (index === 0) {
            baseColDef.cellRenderer = customCellRenderer;
          }

          return baseColDef;
        }),
      ];
    }

    function getColumns() {
      if (
        isEmpty(tableData?.pivot_columns || {}) &&
        !isEmpty(dataTypesById) &&
        !isEmpty(tableData.variables)
      ) {
        const hiddenColumns =
          (tableData?.hidden_columns?.length > 0 &&
            tableData.hidden_columns.map((col) => col)) ||
          [];
        const updatedVariables = tableData?.variables?.filter(
          (variable) => !hiddenColumns.includes(variable?.system_name)
        );

        const colDefArrData = getColDefs(updatedVariables);
        const colDefArrAdj = getColDefs(tableData?.variables);

        if (
          datasheetStore.datasheetDetails.data_origin !=
            DATA_ORIGIN.COMMISSION_OBJECT &&
          datasheetStore.datasheetDetails.data_origin !=
            DATA_ORIGIN.INTER_OBJECT &&
          datasheetStore.datasheetDetails.data_origin !=
            DATA_ORIGIN.FORECAST_OBJECT &&
          datasheetStore.datasheetDetails.data_origin !=
            DATA_ORIGIN.INTER_FORECAST_OBJECT &&
          hasPermissions(RBAC_ROLES.MANAGE_DATASHEETADJUSTMENTS, false)
        ) {
          colDefArrData.push({
            headerName: "Adjust",
            field: "action",
            pinned: "right",
            lockPinned: true,
            sortable: false,
            filter: false,
            width: 80,
            maxWidth: 120,
            cellStyle: { paddingRight: 0 },
            cellClassRules: {
              "!bg-ever-base-25": () => true,
              adjustColumn: () => true,
            },
            headerClass: "!pl-6 !pr-0",
            suppressColumnsToolPanel: true,
            valueGetter: (params) => {
              return params.data;
            },
            cellRenderer: (params) => {
              const isAdjustmentInProgress =
                inProgressAdjustments[params.data.row_key] === true;
              const isAdjustedRow = adjustedRowKeys[params.data.row_key];
              if (isAdjustedRow) {
                return (
                  <div className="flex justify-center">
                    <Dropdown
                      trigger={["click"]}
                      overlay={editAdjustmentMenu(
                        adjustedRowKeys[params.data.row_key],
                        true
                      )}
                      placement="bottomRight"
                      disabled={isAdjustmentInProgress}
                    >
                      <IconButton
                        type="text"
                        color="base"
                        icon={<EraserIcon className="!w-4 !h-4" />}
                        size="small"
                        className="!w-6 !h-6"
                      />
                    </Dropdown>
                  </div>
                );
              }

              return (
                <div className="flex justify-center">
                  <Dropdown
                    trigger={["click"]}
                    overlay={menu(params.value)}
                    disabled={isAdjustmentInProgress}
                  >
                    <IconButton
                      type="text"
                      color="base"
                      icon={<EraserIcon className="!w-4 !h-4" />}
                      size="small"
                      className="!w-6 !h-6"
                    />
                  </Dropdown>
                </div>
              );
            },
          });
        }

        setAdjColumns(colDefArrAdj);
        setColumns(colDefArrData);
      }
    }

    const currentAppliedPivot = pivotConfig?.[activeKey]?.pivotData || {};

    useEffect(() => {
      getColumns();
    }, [inProgressAdjustments, tableData, orderModel]);

    /**
     * Updates the datasheet loading state based on data fetching status.
     *
     * Triggers when `isLoading` or `isFetching` changes, setting the table's loading state
     * in `datasheetStore` to `true` if either is `true`, otherwise `false`.
     *
     * This datasheetStore state is used in FilterPanel.js
     */

    useEffect(() => {
      datasheetStore.setTableDataLoading(isLoading || isFetching);
    }, [isLoading, isFetching]);

    // The column definitions for AgGrid are set here, when the pivot columns are changed
    useEffect(() => {
      if (
        !isEmpty(tableData?.pivot_columns || {}) &&
        !isEmpty(currentAppliedPivot) &&
        viewId !== DATASHEET_VIEW_ID.ALL_ADJUSTMENTS
      ) {
        const systemNameToDisplayNameMap = tableData.variables.reduce(
          (acc, variable) => ({
            ...acc,
            [variable.system_name]: variable.display_name,
          }),
          {}
        );

        const cols = [
          ...tableData.pivot_columns.index_columns.map((var_obj) => {
            const datatype = dataTypesById[columnTypeMap[var_obj]];
            return {
              field: var_obj,
              headerName: systemNameToDisplayNameMap[var_obj],
              sortable: hasPermissions(RBAC_ROLES.MANAGE_DATABOOK),
              headerComponentParams: {
                datasheetTable: true,
                menuIcon: datatype,
              },
              ...(datatype === "Percentage" && {
                comparator: numComparator,
                type: "rightAligned",
              }),
              ...(datatype === "Integer" && {
                comparator: numComparator,
                type: "rightAligned",
                cellRenderer: (params) => (
                  <EverTooltip
                    title={formatCurrencyWrapper(params.value, {
                      decimalPlaces: 6,
                      truncate: true,
                    })}
                    placement="bottom"
                    overlayClassName="roundedTooltip"
                  >
                    {formatCurrencyWrapper(params.value)}
                  </EverTooltip>
                ),
              }),
              ...(datatype === "Date" && {
                cellRenderer: (params) => {
                  if (params?.value) {
                    return (
                      <EverFormatter.Date
                        className="font-normal"
                        date={params?.value}
                      />
                    );
                  }
                  return "";
                },
              }),
              ...(datatype === "Boolean" && {
                valueFormatter: (params) => {
                  if (params.value === true) {
                    return "true";
                  } else if (params.value === false) {
                    return "false";
                  } else {
                    return "";
                  }
                },
              }),
            };
          }),
        ];

        const hierarchy = {};
        for (let i = 0; i < tableData.pivot_columns.pivot_columns.length; i++) {
          const split = tableData.pivot_columns.pivot_columns[i].split("__");
          if (!(split[0] in hierarchy)) {
            hierarchy[split[0]] = [];
          }
          hierarchy[split[0]].push({
            label: split[1],
            value: tableData.pivot_columns.pivot_columns[i],
          });
        }

        for (const parentHeader in hierarchy) {
          const colName = systemNameToDisplayNameMap[parentHeader];
          const datatype = dataTypesById[columnTypeMap[parentHeader]];
          const pivotColumnAggType =
            currentAppliedPivot["aggfunc"][parentHeader];
          // While displaying Pivot Data, datatype of column in pivot data will not necessarily be same as datatype of column in datasheet
          // example: In string type column, if we apply pivot with agg func as count or distinct count then datatype of column in pivot data will be integer and not string
          cols.push({
            headerName: currentAppliedPivot["aggfunc"][parentHeader]
              ? `${
                  PIVOT_AGG_MAP[currentAppliedPivot["aggfunc"][parentHeader]]
                } of ${colName}`
              : colName,
            children: hierarchy[parentHeader].map((child) => {
              return {
                field: child["value"],
                headerName: child["label"],
                sortable: hasPermissions(RBAC_ROLES.MANAGE_DATABOOK),
                // Right aligning integer and percentage fields and pivot data having agg func count and distinct count
                ...((datatype === "Integer" ||
                  datatype === "Percentage" ||
                  pivotColumnAggType == "count" ||
                  pivotColumnAggType == "nunique") && {
                  type: "rightAligned",
                }),
                // When column datatype is Date with agg function "max" or "min" only then data will be in date type
                // else in every agg func case(count, distinct count, sum ...) , data will be in numComparator comparable type
                comparator:
                  datatype == "Date" &&
                  (pivotColumnAggType == "min" || pivotColumnAggType == "max")
                    ? dateComparator
                    : numComparator,
                valueFormatter: (params) => {
                  if (params?.value) {
                    if (datatype === "Integer") {
                      return formatCurrencyWrapper(params.value, {
                        decimalPlaces: 2,
                        truncate: true,
                      });
                    } else if (
                      // only if date dataype columm has agg func as min or max then only it will be in date format else it will be in num format
                      datatype === "Date" &&
                      (pivotColumnAggType == "min" ||
                        pivotColumnAggType == "max")
                    ) {
                      return formatDate({
                        date: params.value,
                      });
                    }
                    return params.value;
                  }

                  return null;
                },
              };
            }),
          });
        }
        setColumns(cols);
      }
    }, [tableData, currentAppliedPivot]);

    useEffect(() => {
      tableData.total_records <= pageSize
        ? setDisplayPagination(false)
        : setDisplayPagination(true);
    }, [tableData, pageSize]);

    useEffect(() => {
      setCurrentPage(1);
      setPageSize(50);
      setOrderModel([]);
      datasheetStore.adjustmentTableDataRefetch &&
        datasheetStore.adjustmentTableDataRefetch();

      // Add datasheet ID to the front of savedColStack when the datasheet ID changes
      const savedColWidths =
        JSON.parse(localStorage.getItem(COLUMN_WIDTHS_KEY)) || {};
      if (savedColWidths[datasheetStore.datasheetId]) {
        saveColumnStateToLocalstorage(
          datasheetStore.datasheetId,
          activeKey,
          savedColWidths[datasheetStore.datasheetId][activeKey]
        );
      }
    }, [datasheetStore.datasheetId]);

    function showURLErrorToast(errorText) {
      if (URLErrorShown.current == false) {
        toast.custom(
          (t) => (
            <EverHotToastNotification
              type="error"
              title="Invalid URL"
              description={errorText}
              toastId={t.id}
            />
          ),
          { position: "top-right" }
        );
        URLErrorShown.current = true;
      }
    }

    function updateURL(operation, record) {
      if (location.pathname === "/datasheet") {
        window.history.replaceState(
          null,
          "",
          `${location.pathname}/adjustments/${operation.toLowerCase()}?id=${
            datasheetStore.datasheetId
          }&rowKey=${record?.row_key}`
        );
      }
    }

    function syncURL() {
      if (window.location.href.includes("adjustments")) {
        const pathNameChars = pathname.split("/");
        const adjustmentTypes = ["ignore", "update", "split"];
        const searchParams = new URLSearchParams(search);

        if (pathNameChars[2] == "adjustments" && isURLSynced.current == false) {
          if (adjustmentTypes.includes(pathNameChars[3])) {
            const urlRowKey = searchParams.get("rowKey");
            const record = tableData?.data?.filter((ele) => {
              return (
                ele.row_key ===
                decodeURIComponent(`${urlRowKey}${hash ? hash : ""}`)
              );
            });
            if (record && record?.length !== 0) {
              setAdjustmentRecord(record[0]);
              setAdjustmentOperation(capitalizeFirstLetter(pathNameChars[3]));
              isURLSynced.current = true;
            } else {
              return showURLErrorToast("Record not found.");
            }
          } else if (pathNameChars[3] !== "edit") {
            return showURLErrorToast("Check URL path again.");
          }
          if (pathNameChars[3] === "edit") {
            const urlAdjustmentId = searchParams.get("adjustmentId");
            const record = datasheetStore.adjustmentData[urlAdjustmentId];

            if (record && record?.length !== 0) {
              setIsEditAdjustment(true);
              setAdjustmentRecord(record);
              isURLSynced.current = true;
            } else {
              return showURLErrorToast(`Adjustment not found.`);
            }
          }
        } else if (
          pathNameChars.length > 2 &&
          pathNameChars[2] != "adjustments"
        ) {
          return showURLErrorToast("Check URL path again.");
        }
      }
    }

    const columnTypeMap = useMemo(() => {
      const dataTypeMap = {};
      tableData?.variables?.forEach((variable) => {
        dataTypeMap[variable.system_name] = variable.data_type_id;
      });
      return dataTypeMap;
    }, [tableData]);

    const sideBar = () => {
      return {
        toolPanels: [
          {
            id: "customise-columns",
            labelKey: "",
            labelDefault: "",
            iconKey: "",
            toolPanel: CustomiseColumns,
            toolPanelParams: {
              gridRef: gridRef.current[viewId],
              customiseColumns: tableData?.variables || [],
              hiddenColumns: tableData?.hidden_columns?.map((col) => col) || [],
              datasheetDetails: datasheetStore.datasheetDetails,
              refetch: refetch,
              viewId: viewId,
            },
          },
          {
            id: "pivot-table",
            labelKey: "",
            labelDefault: "",
            iconKey: "",
            toolPanel: PivotTable,
            toolPanelParams: {
              gridRef: gridRef.current[viewId],
              columns:
                tableData?.variables
                  ?.filter(
                    (variable) =>
                      dataTypesById[variable.data_type_id] !==
                      DATATYPE.HIERARCHY
                  )
                  .map((variable) => {
                    return {
                      label: variable?.display_name,
                      value: variable?.system_name,
                      dataTypeId: variable?.data_type_id,
                      variableId: variable?.variable_id,
                    };
                  }) || [],
              columnTypeMap: columnTypeMap,
              viewId,
              datasheetId: datasheetStore.datasheetId,
              pivotConfig,
              setPivotConfig,
              setPivotConfigPayload,
              pivotConfigPayload,
              defaultPivotMode: !isEmpty(tableData?.pivot_columns || {}),
              refetchTableData: refetch,
              handleCreateViewSuccess,
            },
          },
        ],
      };
    };

    const autoSizeStrategy = useMemo(() => {
      const savedColWidths =
        JSON.parse(localStorage.getItem(COLUMN_WIDTHS_KEY)) || {};
      if (
        savedColWidths[datasheetStore.datasheetId] &&
        savedColWidths[datasheetStore.datasheetId][activeKey]
      ) {
        return {};
      }

      const autoSizeStrategyObject =
        tableData?.variables?.length - tableData?.hidden_columns?.length >= 7
          ? { autoSizeStrategy: { type: "fitCellContents" } }
          : { autoSizeStrategy: { type: "fitGridWidth" } };
      return autoSizeStrategyObject;
    }, [
      datasheetStore.datasheetId,
      activeKey,
      tableData?.variables,
      tableData?.hidden_columns,
    ]);

    useEffect(() => {
      if (tableData.data && datasheetStore.adjustmentData) syncURL();
    }, [tableData, datasheetStore.adjustmentData, gridRef.current[viewId]]);

    return (
      <>
        {isError ? (
          <div className="w-full h-full flex flex-col gap-2 items-center justify-center">
            <img src={dogTearingNewspaper} />{" "}
            <EverTg.Heading2>Some Error has occurred</EverTg.Heading2>
            <EverTg.Caption>{errorMessage}</EverTg.Caption>
          </div>
        ) : isLoading ||
          isFetching ||
          datasheetStore.isDatasheetDetailsRefetching ? (
          <div className="w-full h-full">
            <LoaderTable />
          </div>
        ) : (
          <div
            ref={parentDivRef}
            className="ag-theme-material no-border zebra-grid w-full h-full datasheet-table datasheet-v2-table border-t-0 border-r border-b-0 border-l border-solid border-ever-base-400"
          >
            <AgGridReact
              loading={gridLoading}
              {...getDefaultOptions({
                type: "md",
                disableDefaultAutoSizeStrategy: true,
              })}
              suppressMovableColumns={true}
              loadingOverlayComponent={() => <></>}
              ref={(el) => (gridRef.current[viewId] = el)}
              columnDefs={columns}
              onColumnResized={(params) => {
                if (
                  params.finished &&
                  (params.source === "uiColumnResized" ||
                    params.source === "autosizeColumns")
                ) {
                  saveColumnStateToLocalstorage(
                    datasheetStore.datasheetId,
                    activeKey,
                    params.api.getColumnState()
                  );
                }
              }}
              onColumnPinned={(params) => {
                saveColumnStateToLocalstorage(
                  datasheetStore.datasheetId,
                  activeKey,
                  params.api.getColumnState()
                );
              }}
              onDisplayedColumnsChanged={(params) => {
                if (!columnStateApplied.current) {
                  setColumnWidths(params);
                }
              }}
              rowData={
                tableData === "init"
                  ? []
                  : addSerialNumber({
                      rowData: tableData.data,
                      pageSize: pageSize,
                      currentPage: currentPage,
                    })
              }
              rowHeight={32}
              sideBar={sideBar()}
              headerHeight={32}
              components={{
                agColumnHeader: (params) => CustomHeader(params, "small", true),
              }}
              onGridColumnsChanged={() => {}}
              onRowDataUpdated={() => {}}
              onFirstDataRendered={() => {
                setGridLoading(true);
                setTimeout(() => {
                  setGridLoading(false);
                }, 200);
              }}
              onGridReady={(params) => {
                gridApi.current = params.api;
                params.api.setSideBarVisible(false);
                params.api.closeToolPanel();
                if (!isEmpty(params.api) && !columnStateApplied.current) {
                  setColumnWidths(params);
                }
              }}
              getMainMenuItems={menuItemsCallback}
              rowBuffer={pageSize}
              suppressColumnMoveAnimation={true}
              suppressColumnVirtualisation={true}
              enableRangeSelection={true}
              getContextMenuItems={() => {
                return ["autoSizeAll", "separator", "copy", "copyWithHeaders"];
              }}
              paginationPageSize={pageSize}
              noRowsOverlayComponentParams={{
                title: "No data is available",
                imgSrc: noDataAvailable,
                subTitle: "",
              }}
              suppressFieldDotNotation={true}
              getRowClass={(params) => {
                const isAdjustmentInProgress =
                  inProgressAdjustments[params.data.row_key] === true;
                const isAdjustedRow = adjustedRowKeys[params.data.row_key];
                let className = "";
                if (isAdjustedRow) className = "already-adjusted-rows";
                if (isAdjustmentInProgress) className = "adjusted-rows";
                return className;
              }}
              {...autoSizeStrategy}
            />
          </div>
        )}
        {!isLoading && !isFetching && !isError && displayPagination && (
          <DynamicPagination
            minHeight={"50px"}
            rowPerPageOption={[50, 100]}
            pageCount={Math.ceil(tableData.total_records / pageSize)}
            pageSize={pageSize}
            totalRows={tableData.total_records}
            setPageSize={setPageSize}
            currentPage={currentPage - 1}
            setCurrentPage={setCurrentPage}
            gridRef={{
              current: gridRef.current[viewId],
            }}
            wrapperClassName="h-12"
          />
        )}
        {formulaFieldVisible && (
          <FormulaFieldModal
            visible={formulaFieldVisible}
            onClose={onFormulaFieldClose}
            formulaFieldDetails={formulaFieldDetails}
          />
        )}
        {(adjustmentOperation !== "" || isEditAdjustment) && (
          <AdjustmentDrawer
            record={adjustmentRecord}
            columns={adjColumns}
            primaryVariables={tableData?.variables?.map((ele) => {
              if (ele.is_primary) return ele.system_name;
            })}
            closeDrawer={() => {
              setAdjustmentOperation("");
              setAdjustmentRecord(null);
              setIsEditAdjustment(false);
              window.history.replaceState(
                null,
                "",
                `datasheet?id=${datasheetStore.datasheetId}&viewId=${
                  viewId || DATASHEET_VIEW_ID.ALL_DATA
                }`
              );
            }}
            visible={adjustmentOperation !== "" || isEditAdjustment}
            operation={adjustmentOperation}
            accessToken={accessToken}
            datasheetId={datasheetStore.datasheetId}
            isEditAdjustment={isEditAdjustment}
          />
        )}
      </>
    );
  }
);

function AdjustmentTag({ applied }) {
  return (
    <div
      className={twMerge(
        "ml-1.5 px-1.5 py-px rounded-md flex items-center",
        applied
          ? "bg-ever-chartColors-54/10 text-ever-chartColors-54"
          : "text-ever-warning-lite-content bg-ever-warning-lite-content/10"
      )}
    >
      <EverTg.Caption>
        {applied ? "Adjusted" : "Pending Refresh"}
      </EverTg.Caption>
    </div>
  );
}
