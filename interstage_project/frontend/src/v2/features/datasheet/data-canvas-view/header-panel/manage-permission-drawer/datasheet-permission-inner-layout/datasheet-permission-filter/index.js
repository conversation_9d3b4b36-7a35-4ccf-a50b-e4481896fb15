import { Trash03Icon } from "@everstage/evericons/outlined";
import { PlusCircleIcon } from "@everstage/evericons/solid";
import { Col, Row } from "antd";
import { isEmpty } from "lodash";
import React from "react";

import { useVariableStore } from "~/GlobalStores/VariableStore";
import { EverTg, EverSelect } from "~/v2/components";

import { DataSheetPermissionFilterValue } from "./DataSheetPermissionFilterValue";

const baseFilterOption = (input, option) =>
  option.label.toLowerCase().includes(input.toLowerCase());

const optGroupFilterOption = (input, selectOptions) => {
  try {
    for (const option of selectOptions.options) {
      const optionLabel = option.children;
      const isMatch = optionLabel.toLowerCase().includes(input.toLowerCase());
      if (isMatch) return true;
    }

    return false;
  } catch {
    return false;
  }
};

export function DataSheetPermissionFilter({
  dropDownOperators,
  userAssignedProperties,
  datasheetAllColumns,
  filters,
  onAddRowPermission,
  onFilterColumnChange,
  onFilterOperatorChange,
  onFilterCategoryChange,
  onFilterValueChange,
  onRemoveRowPermission,
}) {
  const { dataTypesById } = useVariableStore();

  function getDatasheetOptions(filterId) {
    const otherFilters = new Set(
      filters
        .filter((filter) => filter.filterId !== filterId)
        .map((filter) => filter.colName)
    );
    const filteredColumns = datasheetAllColumns.filter(
      (column) => !otherFilters.has(column.system_name)
    );

    const datasheetOptions = filteredColumns.map((column) => {
      return {
        value: column.system_name,
        label: column.display_name,
        dataType: dataTypesById[column.data_type_id],
      };
    });

    return datasheetOptions;
  }

  return (
    <>
      {!isEmpty(filters) && (
        <div className="mt-2 ">
          {filters.map((filter) => {
            return (
              <div
                key={filter.filterId}
                className="flex w-full border-ever-base-400 border border-solid  rounded-lg mb-2"
              >
                <div className="w-[calc(100%_-_45px)] rounded-ss-lg rounded-es-lg px-6 py-5 border border-solid border-l-0 border-t-0 border-b-0 border-ever-base-400 bg-ever-base-50">
                  <Row className="p-2 gap-y-2">
                    <Col className="mr-2">
                      <EverSelect
                        className="w-64"
                        placeholder="Column"
                        value={filter.colName}
                        onChange={(_, option) =>
                          onFilterColumnChange(filter.filterId, option)
                        }
                        options={getDatasheetOptions(filter.filterId)}
                        showSearch
                        allowClear
                        filterOption={baseFilterOption}
                      />
                    </Col>
                    <Col className="mr-2">
                      {filter.dataType == "Date" ? (
                        <EverSelect
                          className="w-64"
                          placeholder="Operator"
                          value={filter.operator}
                          onChange={(v) =>
                            onFilterOperatorChange(
                              filter.filterId,
                              dropDownOperators[filter.dataType].find(
                                (opt) => opt.value == v
                              )
                            )
                          }
                          showSearch
                          allowClear
                          filterOption={optGroupFilterOption}
                        >
                          <EverSelect.OptGroup
                            label={
                              <div>
                                <EverTg.Text className="text-ever-base-content-mid">
                                  ABSOLUTE
                                </EverTg.Text>
                              </div>
                            }
                          >
                            {dropDownOperators[filter.dataType]
                              .filter(
                                (opt) =>
                                  opt.value == "IS_EMPTY" ||
                                  opt.value == "IS_NOT_EMPTY" ||
                                  opt.needsOperand
                              )
                              .map((opt) => {
                                return (
                                  <EverSelect.Option
                                    key={opt.value}
                                    title={opt.label}
                                    value={opt.value}
                                  >
                                    {opt.label}
                                  </EverSelect.Option>
                                );
                              })}
                          </EverSelect.OptGroup>
                          <EverSelect.OptGroup
                            label={
                              <div>
                                <EverTg.Text className="text-ever-base-content-mid">
                                  RELATIVE
                                </EverTg.Text>
                              </div>
                            }
                          >
                            {dropDownOperators[filter.dataType]
                              .filter(
                                (opt) =>
                                  !(
                                    opt.value == "IS_EMPTY" ||
                                    opt.value == "IS_NOT_EMPTY" ||
                                    opt.needsOperand
                                  )
                              )
                              .map((opt) => {
                                return (
                                  <EverSelect.Option
                                    key={opt.value}
                                    value={opt.value}
                                  >
                                    {opt.label}
                                  </EverSelect.Option>
                                );
                              })}
                          </EverSelect.OptGroup>
                        </EverSelect>
                      ) : (
                        <EverSelect
                          className="w-60"
                          placeholder="Operator"
                          showSearch
                          allowClear
                          filterOption={baseFilterOption}
                          value={filter.operator}
                          options={dropDownOperators[filter.dataType]}
                          onChange={(_, option) =>
                            onFilterOperatorChange(filter.filterId, option)
                          }
                        />
                      )}
                    </Col>
                    {filter.needsOperand &&
                      !filter.multiValued &&
                      filter.operator !== "BELONGS_TO" && (
                        <Col className="mr-2">
                          <EverSelect
                            className="w-64"
                            placeholder="Compare With"
                            value={filter.valueCategory}
                            onChange={(value) =>
                              onFilterCategoryChange(filter.filterId, value)
                            }
                            options={[
                              {
                                value: "user_field",
                                label: "User Field",
                              },
                              { value: "custom_value", label: "Custom Value" },
                            ]}
                            allowClear
                          />
                        </Col>
                      )}
                    <Col className="mr-2">
                      <DataSheetPermissionFilterValue
                        userAssignedProperties={userAssignedProperties}
                        filter={filter}
                        onFilterValueChange={onFilterValueChange}
                      />
                    </Col>
                  </Row>
                </div>
                <div className="flex self-center justify-center w-14">
                  <Trash03Icon
                    className="w-5 h-5 text-ever-error cursor-pointer"
                    onClick={() => onRemoveRowPermission(filter.filterId)}
                  />
                </div>
              </div>
            );
          })}
        </div>
      )}
      <div className="mt-3">
        <div
          className="flex items-center cursor-pointer w-[166px]"
          onClick={onAddRowPermission}
        >
          <PlusCircleIcon className="w-5 h-5 text-ever-primary mr-2" />
          <EverTg.Heading4 className="text-ever-primary ">
            Add row permission
          </EverTg.Heading4>
        </div>
      </div>
    </>
  );
}
