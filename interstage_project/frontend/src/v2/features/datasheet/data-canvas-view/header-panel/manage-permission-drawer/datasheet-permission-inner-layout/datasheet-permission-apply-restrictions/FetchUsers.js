import { isNil } from "lodash";
import React from "react";

import { COMPONENTS } from "~/Enums/index";
import {
  EverTg,
  Space,
  useAbortiveLazyRESTQuery,
  LazyListContent,
  EverGroupAvatar,
} from "~/v2/components";
import { emptyFace } from "~/v2/images";

import { MultiTabDropdownSelectTabPaneCommonItem } from "./MultiTabDropdownSelectTabPaneCommonItem";
import { convertKeysToPascalCase } from "../../../../../utils";

const EmptyUsers = () => {
  return (
    <span className="flex flex-col items-center text-center">
      <Space direction="vertical" className="mt-10">
        <img src={emptyFace} />
        <EverTg.SubHeading3 className="leading-5 mt-8">
          No users
        </EverTg.SubHeading3>
      </Space>
    </span>
  );
};

const FetchUsers = ({ selectedUsers, onClickItem }) => {
  const [getUsers, { abort }] = useAbortiveLazyRESTQuery(
    "spm/employee-v2",
    "GET",
    {
      queryParams: {
        component: "databooks",
      },
      onCompleted: (response, callbacks) => {
        if (isNil(response)) {
          callbacks.failureCbk();
        } else {
          const result = convertKeysToPascalCase(response);

          callbacks.successCbk(result);
        }
      },
      onError: (_, callbacks) => {
        callbacks.failureCbk();
      },
    }
  );

  const usersLazyLoadProps = {
    abort,
    getOptions: async (params) => {
      const { searchTerm, offset, limit } = params;
      const queryParams = {
        user_status: "Active",
        offset_value: offset,
        limit_value: limit,
        component: COMPONENTS.DATABOOKS,
      };
      if (searchTerm) queryParams.search_term = searchTerm;
      await getUsers({
        ...params,
        queryParams,
      });
    },
  };

  return (
    <LazyListContent
      listHeight={214}
      listItemHeight={68}
      skipSelectAll
      showSearch
      noDataText={<EmptyUsers />}
      listItemRenderer={(item) => {
        const isSelected = selectedUsers.some(
          ({ value }) => value === item.employeeEmailId
        );

        return (
          <MultiTabDropdownSelectTabPaneCommonItem
            label={item.fullName}
            description={item.employeeEmailId}
            icon={
              <EverGroupAvatar
                size="large"
                avatars={[
                  {
                    firstName: item.firstName,
                    lastName: item.secondName,
                    className: "w-10 h-10",
                  },
                ]}
              />
            }
            isSelected={isSelected}
            onClickItem={() =>
              onClickItem({ value: item.employeeEmailId, label: item.fullName })
            }
          />
        );
      }}
      {...usersLazyLoadProps}
    />
  );
};

export default FetchUsers;
