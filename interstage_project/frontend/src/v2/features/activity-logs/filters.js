import { isEmpty } from "lodash";
import React, { useEffect, useState } from "react";

import { ACTIVITY_LOGS_JOBS, ACTIVITY_LOGS_STATUS, RBAC_ROLES } from "~/Enums";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  Ever<PERSON>ard,
  EverNewDatePicker,
  EverButton,
  EverLabel,
  EverDivider,
  EverCheckbox,
} from "~/v2/components";

export default function Filters({
  showFilter,
  setShowFilter,
  allFilters,
  setAllFilters,
  setCurrentPage,
}) {
  const { hasPermissions } = useUserPermissionStore();

  const [appliedFilters, setAppliedFilters] = useState(null);

  useEffect(() => {
    setAppliedFilters(allFilters);
  }, [allFilters]);

  const statusType = [
    { label: "Completed", value: ACTIVITY_LOGS_STATUS.DONE },
    { label: "Running", value: ACTIVITY_LOGS_STATUS.PROCESSING },
    { label: "Waiting in queue", value: ACTIVITY_LOGS_STATUS.PENDING },
    { label: "Partially failed", value: ACTIVITY_LOGS_STATUS.PARTIALLY_FAILED },
    { label: "Failed", value: ACTIVITY_LOGS_STATUS.FAILED },
  ];

  const jobType = [
    { label: "Data Import", value: ACTIVITY_LOGS_JOBS.DATAIMPORT },
    {
      label: "Contracts Status Export",
      value: ACTIVITY_LOGS_JOBS.CONTRACTS_STATUS_EXPORT,
    },
    {
      label: "Bulk Download Statements",
      value: ACTIVITY_LOGS_JOBS.BULK_DOWNLOAD_STATEMENTS,
    },
    {
      label: "Datasheet Export",
      value: ACTIVITY_LOGS_JOBS.DATASHEET_EXPORT,
    },
  ];

  const hasDeleteUsers = hasPermissions(RBAC_ROLES.DELETE_USERS);

  if (hasDeleteUsers) {
    jobType.push({
      label: "Bulk Delete Users",
      value: ACTIVITY_LOGS_JOBS.BULK_DELETE_USERS,
    });
  }

  const onStatusChange = (checkedValues) => {
    if (isEmpty(checkedValues)) {
      checkedValues = null;
    }
    setAppliedFilters({ ...appliedFilters, status: checkedValues });
  };

  const onJobChange = (checkedValues) => {
    if (isEmpty(checkedValues)) {
      checkedValues = null;
    }
    setAppliedFilters({ ...appliedFilters, job: checkedValues });
  };

  const onDateChange = (dates) => {
    const selectedRange = {
      startDate: dates?.[0],
      endDate: dates?.[1],
    };
    setAppliedFilters({ ...appliedFilters, ["period"]: selectedRange });
  };

  const onApply = () => {
    setAllFilters(appliedFilters);
    setShowFilter(false);
    // If in case we are on last page(say 10) and we apply filters, we should reset the page to 1,
    // because after applying filters, there might be less than 10 pages.
    setCurrentPage(1);
  };

  const onCancel = () => {
    setAppliedFilters(allFilters);
    setShowFilter(false);
  };

  return (
    <>
      {showFilter && (
        <EverCard className="mt-12 absolute z-10 w-316 h-527" shadowSize="lg">
          <div className="flex flex-col gap-3 overflow-auto">
            <div className="flex flex-col gap-2">
              <EverLabel className="font-medium text-ever-base-content">
                Choose Period
              </EverLabel>
              <div className="flex flex-row">
                <EverNewDatePicker.RangePicker
                  value={[
                    appliedFilters.period?.startDate,
                    appliedFilters.period?.endDate,
                  ]}
                  onChange={onDateChange}
                  allowClear
                />
              </div>
            </div>

            <div className="flex flex-col gap-2">
              {" "}
              <EverLabel className="font-medium text-ever-base-content">
                Status
              </EverLabel>
              <EverCheckbox.Group
                value={appliedFilters.status}
                onChange={onStatusChange}
                className="flex flex-col gap-1"
              >
                {statusType.map((status) => (
                  <div
                    key={status.value}
                    className="flex m-1 items-center checkbox-tick h-max"
                  >
                    <EverCheckbox
                      key={status.value}
                      value={status.value}
                      label={status.label}
                    />
                  </div>
                ))}
              </EverCheckbox.Group>
            </div>

            <div className="flex flex-col gap-2">
              {" "}
              <EverLabel className="font-medium text-ever-base-content">
                Job type
              </EverLabel>
              <EverCheckbox.Group
                value={appliedFilters.job}
                onChange={onJobChange}
                className="flex flex-col gap-1"
              >
                {jobType.map((job) => (
                  <div
                    key={job.value}
                    className="flex m-1 items-center checkbox-tick h-max"
                  >
                    <EverCheckbox
                      key={job.value}
                      value={job.value}
                      label={job.label}
                    />
                  </div>
                ))}
              </EverCheckbox.Group>
            </div>
            <EverDivider />
            <div className="flex flex-row gap-2 justify-end">
              <EverButton
                size="medium"
                type="text"
                color="base"
                onClick={onCancel}
              >
                Cancel
              </EverButton>
              <EverButton
                size="medium"
                color="primary"
                type="filled"
                onClick={onApply}
              >
                Apply
              </EverButton>
            </div>
          </div>
        </EverCard>
      )}
    </>
  );
}
