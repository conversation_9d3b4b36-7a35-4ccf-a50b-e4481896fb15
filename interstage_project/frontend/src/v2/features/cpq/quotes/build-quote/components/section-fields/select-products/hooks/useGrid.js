import { PlusCircleIcon } from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import { useMemo, useCallback } from "react";

import { HEADER_STATE } from "~/Enums";
import { cellRenderers } from "~/v2/components/ag-grid";
import { TruncatedText } from "~/v2/features/cpq/components";
import { DefaultCellRenderer } from "~/v2/features/cpq/components/ag-grid";
import { useBuildQuote } from "~/v2/features/cpq/quotes/build-quote/hooks";
import { numberFormatter } from "~/v2/features/cpq/utils";

import { usePhase } from "./usePhase";

const { CheckboxRenderer, GroupHeaderCheckbox } = cellRenderers;

const BILLING_FREQUENCY = {
  ONE_TIME: "one-time",
  DAILY: "daily",
  MONTHLY: "monthly",
  QUARTERLY: "quarterly",
  HALF_YEARLY: "half-yearly",
  ANNUAL: "annual",
};

const BILLING_FREQUENCY_ABBR = {
  [BILLING_FREQUENCY.ONE_TIME]: "",
  [BILLING_FREQUENCY.DAILY]: "d",
  [BILLING_FREQUENCY.MONTHLY]: "mo",
  [BILLING_FREQUENCY.QUARTERLY]: "qtr",
  [BILLING_FREQUENCY.HALF_YEARLY]: "hy",
  [BILLING_FREQUENCY.ANNUAL]: "yr",
};

export const CustomCellRenderer = ({
  value,
  billingFrequency = "",
  discountPercent = null,
}) => {
  const billingFreq = BILLING_FREQUENCY_ABBR[billingFrequency];

  const text = `${value}${billingFreq ? ` /${billingFreq}` : ""}${
    discountPercent !== null ? ` (${discountPercent})` : ""
  }`;

  return (
    <TruncatedText text={text}>
      {value} {billingFreq && <span>/{billingFreq} </span>}
      {discountPercent !== null && discountPercent !== "0%" && (
        <span className="text-ever-base-content-mid">
          {" "}
          (-{discountPercent})
        </span>
      )}
    </TruncatedText>
  );
};

const useGrid = () => {
  // const resizeTimeOut = useRef(null);

  const { isEditMode } = useBuildQuote();
  const {
    phase,
    skuPricePoint,
    gridApi,
    selectedProducts,
    qliLoading,
    updatingRows,
    // isMultiPhase,
    setGridApi,
    setIsAddProductsModalOpen,
    onSelectProducts,
    onSelectAllProducts,
  } = usePhase();

  const defaultColDef = useMemo(() => {
    return {
      flex: 1,
      sortable: false,
      suppressHeaderMenuButton: true,
      filter: true,
      headerClass: "!px-5",
      cellClassRules: {
        "!px-3": (params) =>
          params.colDef.field !== "sn" && params.colDef.field !== "checkbox",
        "!border-0": () => true,
      },
      cellDataType: false,
      resizable: false,
    };
  }, []);

  const columnDefs = useMemo(() => {
    const columnDefs = [];
    if (isEditMode) {
      columnDefs.push({
        field: "checkbox",
        colId: "checkbox",
        headerName: "",
        minWidth: 60,
        maxWidth: 60,
        pinned: "left",
        lockPosition: "left",
        type: "rightAligned",
        cellClass: "!px-2 justify-end",
        headerClass: "!px-2 [&_div]:justify-end",
        rowDrag: (params) => params.data.key !== "addProduct",
        cellRenderer: (params) =>
          params.data.key !== "addProduct" && (
            <CheckboxRenderer
              value={selectedProducts.has(params.data.sku)}
              {...params}
            />
          ),
        cellRendererParams: {
          onToggleCheckbox: onSelectProducts,
        },
        headerComponent: (params) => (
          <GroupHeaderCheckbox
            {...params}
            value={
              selectedProducts.size > 0
                ? selectedProducts.size === phase.row_data.length
                  ? HEADER_STATE.EVERYTHING
                  : HEADER_STATE.PARTIAL
                : HEADER_STATE.NONE
            }
          />
        ),
        headerComponentParams: {
          onToggleHeaderCbx: onSelectAllProducts,
        },
      });
    }

    columnDefs.push(
      {
        field: "sn",
        headerName: "S.NO",
        minWidth: 42,
        maxWidth: 42,
        cellClass: "justify-center !px-2",
        headerClass: "!px-2 [&_div]:text-center",
        pinned: "left",
        lockPosition: "left",
        cellRenderer: ({ node }) => node.rowIndex + 1,
      },
      {
        field: "product_name",
        headerName: "NAME",
        minWidth: isEditMode ? 200 : 300,
        maxWidth: isEditMode ? 200 : 300,
        headerClass: "!px-3",
        pinned: "left",
        lockPosition: "left",
        cellRenderer: ({ value, data }) => {
          if (data.key === "addProduct") {
            return (
              <div
                className="h-7 w-full font-medium flex items-center gap-1.5 px-2 py-1 cursor-pointer rounded-md border border-solid border-ever-base-200 bg-ever-info-lite text-ever-info hover:bg-ever-base hover:shadow-sm"
                onClick={() => setIsAddProductsModalOpen(true)}
              >
                <PlusCircleIcon className="w-4 h-4" />
                Add Product
              </div>
            );
          }
          return (
            <TruncatedText text={value} className="font-medium">
              {value}
            </TruncatedText>
          );
        },
      },
      {
        field: "quantity",
        headerName: "UNITS",
        minWidth: 160,
        maxWidth: 160,
        cellRenderer: DefaultCellRenderer,
        cellRendererParams: (params) => ({
          hidden: params.data.key === "addProduct",
          displayValue: params.valueFormatted ?? "",
        }),
        valueFormatter: ({ value, data }) => {
          const formattedNumber = numberFormatter(value, {
            // TODO: Make this locale dynamic based on the selected format locale
            locale: "en-US",
          });
          const unitOfMeasure = data?.unit_of_measure?.toLowerCase() ?? "";
          return `${formattedNumber} ${unitOfMeasure}`;
        },
        type: "rightAligned",
        cellClass: "justify-end [&_input]:!text-right",
        headerClass: "!px-5",
      },
      {
        field: "list_unit_price",
        headerName: "LIST UNIT PRICE",
        minWidth: 130,
        cellRenderer: (params) => {
          return (
            <DefaultCellRenderer {...params}>
              {params.displayValue}
            </DefaultCellRenderer>
          );
        },
        cellRendererParams: (params) => {
          const canEdit =
            !isEmpty(skuPricePoint[params.data.sku]) && isEditMode;

          return {
            hidden: params.data.key === "addProduct",
            displayValue:
              canEdit && isEmpty(params.data.pricepoint_data) ? (
                <span className="text-ever-info-lite-content">Set price</span>
              ) : (
                <CustomCellRenderer
                  value={params.valueFormatted}
                  billingFrequency={params.data.billing_frequency}
                />
              ),
          };
        },
        valueFormatter: ({ value }) => {
          return numberFormatter(value, {
            // TODO: Make this locale dynamic based on the selected format locale
            precision: 2, // TODO: make this dynamic
            innerPrefix: "$",
            locale: "en-US",
          });
        },
        type: "rightAligned",
        cellClass: "justify-end [&_input]:!text-right",
        headerClass: "!px-5",
      },
      {
        field: "net_unit_price",
        headerName: "NET UNIT PRICE",
        minWidth: 150,
        cellRenderer: (params) => {
          return (
            <DefaultCellRenderer {...params}>
              {params.displayValue}
            </DefaultCellRenderer>
          );
        },
        cellRendererParams: (params) => {
          const canEdit =
            !isEmpty(skuPricePoint[params.data.sku]) && isEditMode;

          return {
            hidden: params.data.key === "addProduct",
            displayValue:
              canEdit && isEmpty(params.data.pricepoint_data) ? (
                <span className="text-ever-info-lite-content">Set price</span>
              ) : (
                <CustomCellRenderer
                  value={params.valueFormatted}
                  billingFrequency={params.data.billing_frequency}
                  discountPercent={
                    params.value > 0
                      ? numberFormatter(params.data.discount_percent, {
                          innerSuffix: "%",
                          // TODO: Make this locale dynamic based on the selected format locale
                          precision: 2,
                          locale: "en-US",
                        })
                      : null
                  }
                />
              ),
          };
        },
        valueFormatter: ({ value }) => {
          return numberFormatter(value, {
            // TODO: Make this locale dynamic based on the selected format locale
            precision: 2, // TODO: make this dynamic
            innerPrefix: "$",
            locale: "en-US",
          });
        },
        type: "rightAligned",
        cellClass: "justify-end [&_input]:!text-right",
        headerClass: "!px-5",
      },
      {
        field: "duration",
        headerName: "DURATION",
        minWidth: 130,
        maxWidth: 130,
        cellRenderer: DefaultCellRenderer,
        cellRendererParams: (params) => ({
          hidden: params.data.key === "addProduct",
          displayValue: params.value ?? "",
        }),
        type: "rightAligned",
        cellClass: "justify-end [&_input]:!text-right",
        headerClass: "!px-5",
      },
      {
        field: "prorated_net_total",
        headerName: "TOTAL",
        minWidth: 130,
        cellRenderer: DefaultCellRenderer,
        cellRendererParams: (params) => ({
          hidden: params.data.key === "addProduct",
          displayValue: params.valueFormatted ?? "",
        }),
        valueFormatter: ({ value }) => {
          return numberFormatter(value, {
            // TODO: Make this locale dynamic based on the selected format locale
            precision: 2, // TODO: make this dynamic
            innerPrefix: "$",
            locale: "en-US",
          });
        },
        type: "rightAligned",
        cellClass: "justify-end [&_input]:!text-right !font-medium",
        headerClass: "!px-5",
      }
    );

    return columnDefs;
  }, [
    qliLoading,
    updatingRows,
    isEditMode,
    skuPricePoint,
    selectedProducts,
    phase.row_data,
    onSelectProducts,
    onSelectAllProducts,
    setIsAddProductsModalOpen,
  ]);

  const onGridReady = useCallback(
    (params) => {
      setGridApi(params);
      const nodes = [];
      params.api.forEachNode((node) => {
        if (node.data.key !== "addProduct") {
          nodes.push(node);
        }
      });
      params.api.flashCells({
        rowNodes: nodes,
        flashDuration: 1000,
        fadeDuration: 750,
      });
    },
    [setGridApi]
  );

  // const onWindowResize = useCallback(() => {
  //   if (gridApi && !isNil(isMultiPhase)) {
  //     clearTimeout(resizeTimeOut.current);
  //     resizeTimeOut.current = setTimeout(
  //       () => gridApi.api.sizeColumnsToFit(),
  //       500
  //     );
  //   }
  // }, [gridApi, isMultiPhase]);

  // useEffect(() => {
  //   onWindowResize();
  //   window.addEventListener("resize", onWindowResize);
  //   return () => {
  //     window.removeEventListener("resize", onWindowResize);
  //   };
  // }, [onWindowResize]);

  return {
    gridApi,
    defaultColDef,
    columnDefs,
    onGridReady,
  };
};

export default useGrid;
