import { isPast, endOfDay } from "date-fns";
import { isEqual } from "lodash";
import { useCallback } from "react";
import { useController, useFormContext } from "react-hook-form";
import { twMerge } from "tailwind-merge";

import { EverInput } from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";
import { FIELD_TYPE } from "~/v2/features/cpq/constants";
import { TAG_TYPE } from "~/v2/features/cpq/quotes/build-quote/constants";
import { useBuildQuote } from "~/v2/features/cpq/quotes/build-quote/hooks";
import { getRules } from "~/v2/features/cpq/quotes/build-quote/utils";

import FIELD_TYPE_COMPONENTS from "./field-types";
import SelectProducts from "./select-products";

const Field = ({ fieldData }) => {
  const {
    formId,
    formBuilderId,
    formSpec,
    startPolling,
    isEditMode,
    evaluateRule,
    sourceRuleFields,
    dependentFieldMap,
    ruleEvaluatingFields,
    setRuleEvaluatingFields,
  } = useBuildQuote();
  const { id, label, label_placement, properties, help_text, field_type, tag } =
    fieldData;
  const { is_mandatory, is_hidden, is_read_only } = properties;
  const isRuleEvaluating =
    ruleEvaluatingFields.has(id) ||
    (dependentFieldMap[id] || []).some((field) =>
      ruleEvaluatingFields.has(field)
    );
  const isReadOnly = is_read_only || !isEditMode || isRuleEvaluating;
  const additionalProps = {};

  const { getValues } = useFormContext();
  const {
    field,
    fieldState: { error },
  } = useController({
    name: id,
    rules: getRules(field_type, properties),
  });

  const evaluateRuleAction = useCallback(
    (formData) => {
      setRuleEvaluatingFields((prev) => {
        prev.add(id);
        return prev;
      });
      evaluateRule.mutate({
        form_id: formId,
        form_builder_id: formBuilderId,
        changed_field: id,
        form_data: formData,
        form_spec: formSpec,
      });
    },
    [formId, formBuilderId, id, evaluateRule, setRuleEvaluatingFields, formSpec]
  );

  const handleChange = useCallback(
    (event) => {
      const value = getValueFromEvent(event, field_type);
      if (!isEqual(value, field.value)) {
        if (sourceRuleFields.includes(id) && !shouldTriggerOnBlur(field_type)) {
          evaluateRuleAction({ ...getValues(), [id]: value });
        }
        startPolling();
        field.onChange(event);
      }
    },
    [
      id,
      field,
      field_type,
      sourceRuleFields,
      startPolling,
      getValues,
      evaluateRuleAction,
    ]
  );

  const handleBlur = useCallback(() => {
    if (sourceRuleFields.includes(id) && shouldTriggerOnBlur(field_type)) {
      evaluateRuleAction(getValues());
    }
  }, [sourceRuleFields, id, field_type, evaluateRuleAction, getValues]);

  if (is_hidden) {
    return null;
  }

  if (field_type === FIELD_TYPE.TABLE && tag === TAG_TYPE.SELECT_PRODUCT) {
    return <SelectProducts />;
  }

  // Field 11 is a valid till field
  if (id === "field11") {
    additionalProps.disabledDate = (current) =>
      current && isPast(endOfDay(current));
  }

  const FieldComponent =
    FIELD_TYPE_COMPONENTS[field_type] || DefaultFieldComponent;

  return (
    <LabeledField
      key={id}
      label={label}
      labelPlacement={label_placement}
      required={is_mandatory}
      className="section-fields__input-container"
      helpText={help_text}
      error={error}
    >
      <fieldset
        id={`field_${field_type}_${id}`}
        disabled={isReadOnly}
        className={twMerge(
          isReadOnly && "field-disabled",
          error && "input-error"
        )}
      >
        <FieldComponent
          field={{
            ...field,
            onChange: isReadOnly ? null : handleChange,
            onBlur: isReadOnly ? null : handleBlur,
          }}
          loading={isRuleEvaluating}
          fieldData={fieldData}
          additionalProps={additionalProps}
          properties={{
            ...properties,
            is_read_only: isReadOnly,
          }}
        />
      </fieldset>
    </LabeledField>
  );
};

export default Field;

function getValueFromEvent(event, fieldType) {
  switch (fieldType) {
    case FIELD_TYPE.CURRENCY:
    case FIELD_TYPE.NUMBER:
    case FIELD_TYPE.SINGLE_SELECT:
    case FIELD_TYPE.MULTI_SELECT:
    case FIELD_TYPE.DATE:
    case FIELD_TYPE.DATE_TIME:
    case FIELD_TYPE.LOOKUP: {
      return event;
    }
    case FIELD_TYPE.CHECKBOX: {
      return event.target.checked;
    }
    default: {
      return event.target.value;
    }
  }
}

function shouldTriggerOnBlur(fieldType) {
  return [
    FIELD_TYPE.CURRENCY,
    FIELD_TYPE.NUMBER,
    FIELD_TYPE.EMAIL,
    FIELD_TYPE.MULTI_LINE,
    FIELD_TYPE.SINGLE_LINE,
    FIELD_TYPE.URL,
  ].includes(fieldType);
}

function DefaultFieldComponent({ field }) {
  return <EverInput {...field} value={field.value?.toString()} disabled />;
}
