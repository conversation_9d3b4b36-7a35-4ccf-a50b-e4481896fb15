import { isNil } from "lodash";
import { useState } from "react";

import { EverSwitch, EverInput } from "~/v2/components";

export default function MinMaxField({ label, field, onChange }) {
  const { min, max } = field.properties;
  const [checked, setChecked] = useState(!isNil(min) || !isNil(max));

  const handleChange = (checked) => {
    setChecked(checked);
    const { min, max, ...rest } = field.properties;
    onChange({
      ...field,
      properties: { ...rest },
    });
  };

  const handleMinChange = (value) => {
    const { min, ...rest } = field.properties;
    onChange({
      ...field,
      properties: {
        ...rest,
        ...(value !== "" && !isNil(value) && { min: value }),
      },
    });
  };

  const handleMaxChange = (value) => {
    const { max, ...rest } = field.properties;
    onChange({
      ...field,
      properties: {
        ...rest,
        ...(value !== "" && !isNil(value) && { max: value }),
      },
    });
  };

  return (
    <div className="flex flex-col gap-4">
      <EverSwitch checked={checked} label={label} onChange={handleChange} />
      {checked && (
        <div>
          <div className="flex flex-col gap-2">
            <EverInput.Number
              className="w-full !h-9 [&_.ant-input-number-input-wrap]:!h-9 [&_.ant-input-number-input]:!h-9"
              value={min}
              onChange={handleMinChange}
              placeholder="MIN"
              min={1}
            />
            <EverInput.Number
              className="w-full !h-9 [&_.ant-input-number-input-wrap]:!h-9 [&_.ant-input-number-input]:!h-9"
              value={max}
              onChange={handleMaxChange}
              placeholder="MAX"
              min={1}
            />
          </div>
          {max < min && (
            <div className="text-ever-error mt-1">
              Max value should be greater than Min value
            </div>
          )}
        </div>
      )}
    </div>
  );
}
