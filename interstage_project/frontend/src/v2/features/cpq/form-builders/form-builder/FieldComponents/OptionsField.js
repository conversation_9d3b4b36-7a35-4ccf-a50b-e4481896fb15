import {
  DotsVerticalIcon,
  PlusCircleIcon,
  StarIcon,
  MinusCircleIcon,
} from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import React, { useMemo } from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { twMerge } from "tailwind-merge";

import { EverButton } from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";
import { FIELD_TYPE } from "~/v2/features/cpq/constants";

export default function OptionsField({ label, field, onChange }) {
  const { options = [], field_type, value } = field;
  const isMultipleSelect = field_type === FIELD_TYPE.MULTI_SELECT;

  const duplicateValues = useMemo(() => {
    const values = options.map((option) => option.value);
    return new Set(
      values.filter(
        (value, index, self) => !isEmpty(value) && self.indexOf(value) !== index
      )
    );
  }, [options]);

  const handleLabelChange = (event, index) => {
    const newOptions = [...options];
    newOptions[index].label = event.target.value;
    newOptions[index].value = event.target.value;
    onChange({
      ...field,
      options: newOptions,
    });
  };

  const handleDefaultValueChange = (value) => {
    if (!isEmpty(value)) {
      const { value: fieldValue } = field;
      const newValue = isMultipleSelect
        ? fieldValue.includes(value)
          ? fieldValue.filter((v) => v !== value)
          : [...fieldValue, value]
        : fieldValue === value
        ? ""
        : value;
      onChange({
        ...field,
        value: newValue,
      });
    }
  };

  const handleAddOption = () => {
    const newOptions = [...options, { label: "", value: "" }];
    onChange({
      ...field,
      options: newOptions,
    });
  };

  const handleRemoveOption = (index) => {
    const newOptions = [...options];
    const removedOption = newOptions.splice(index, 1);
    if (isMultipleSelect) {
      const [{ value: removedValue }] = removedOption;
      const updatedValue = duplicateValues.has(removedValue)
        ? value
        : value.filter((v) => v !== removedValue);

      onChange({
        ...field,
        options: newOptions,
        value: updatedValue,
      });
    } else {
      onChange({
        ...field,
        options: newOptions,
        value: removedOption[0].value === value ? "" : value,
      });
    }
  };

  const onDragEnd = (result) => {
    const { source, destination } = result;
    if (!destination) return;

    const newOptions = [...options];
    const [removed] = newOptions.splice(source.index, 1);
    newOptions.splice(destination.index, 0, removed);

    onChange({
      ...field,
      options: newOptions,
    });
  };

  return (
    <div>
      <LabeledField
        label={label}
        className="gap-2"
        labelClassName="text-xs"
        required
      >
        <div className="flex flex-col gap-2 p-3 border border-solid border-ever-base-300 rounded-md">
          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable key="options" droppableId="options">
              {(provided) => (
                <div
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                  className="flex flex-col gap-2"
                >
                  {options.map((option, index) => (
                    <React.Fragment key={index}>
                      <Draggable draggableId={String(index)} index={index}>
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className={twMerge(
                              "flex items-center border border-solid border-ever-base-400 rounded-sm p-2",
                              duplicateValues.has(option.value) &&
                                "border-ever-error"
                            )}
                          >
                            <div className="flex items-center justify-center mr-1 px-1.5">
                              <DotsVerticalIcon className="w-1 h-3 text-ever-base-content-low" />
                              <DotsVerticalIcon className="w-1 h-3 text-ever-base-content-low ml-0.5" />
                            </div>
                            <div className="flex justify-between gap-2 w-full mr-4">
                              <input
                                type="text"
                                value={option.label}
                                className="w-full border-0 outline-0 transition-all"
                                placeholder="Placeholder"
                                onChange={(e) => handleLabelChange(e, index)}
                              />
                              <div
                                className={twMerge(
                                  isEmpty(option.value)
                                    ? "cursor-not-allowed"
                                    : "cursor-pointer"
                                )}
                                onClick={() =>
                                  handleDefaultValueChange(option.value)
                                }
                              >
                                <StarIcon
                                  className={twMerge(
                                    "w-5 h-5 text-ever-base-content-low",
                                    !isEmpty(option.value) &&
                                      (isMultipleSelect
                                        ? value.includes(option.value)
                                        : option.value === value) &&
                                      "text-ever-primary fill-ever-primary"
                                  )}
                                />
                              </div>
                              <div
                                className="cursor-pointer"
                                onClick={() => handleRemoveOption(index)}
                              >
                                <MinusCircleIcon className="w-5 h-5 text-ever-error-hover" />
                              </div>
                            </div>
                            {provided.placeholder}
                          </div>
                        )}
                      </Draggable>
                    </React.Fragment>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
          <EverButton
            type="dashed"
            size="small"
            prependIcon={<PlusCircleIcon className="w-5 h-5 p-px" />}
            onClick={handleAddOption}
          >
            Add Option
          </EverButton>
        </div>
      </LabeledField>
    </div>
  );
}
