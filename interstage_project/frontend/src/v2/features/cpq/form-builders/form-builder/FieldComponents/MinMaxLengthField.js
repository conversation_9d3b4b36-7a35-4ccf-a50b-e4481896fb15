import { isNil } from "lodash";
import { useState } from "react";

import { EverSwitch, EverInput } from "~/v2/components";

export default function MinMaxLengthField({ label, field, onChange }) {
  const { min_length, max_length } = field.properties;
  const [checked, setChecked] = useState(
    !isNil(min_length) || !isNil(max_length)
  );

  const handleChange = (checked) => {
    setChecked(checked);
    const { min_length, max_length, ...rest } = field.properties;
    onChange({
      ...field,
      properties: { ...rest },
    });
  };

  const handleMinLengthChange = (value) => {
    const { min_length, ...rest } = field.properties;
    onChange({
      ...field,
      properties: {
        ...rest,
        ...(value !== "" && !isNil(value) && { min_length: value }),
      },
    });
  };

  const handleMaxLengthChange = (value) => {
    const { max_length, ...rest } = field.properties;
    onChange({
      ...field,
      properties: {
        ...rest,
        ...(value !== "" && !isNil(value) && { max_length: value }),
      },
    });
  };

  return (
    <div className="flex flex-col gap-4">
      <EverSwitch checked={checked} label={label} onChange={handleChange} />
      {checked && (
        <div>
          <div className="flex gap-4">
            <EverInput.Number
              className="w-full !h-9 [&_.ant-input-number-input-wrap]:!h-9 [&_.ant-input-number-input]:!h-9"
              value={min_length}
              onChange={handleMinLengthChange}
              placeholder="MIN"
              min={1}
            />
            <EverInput.Number
              className="w-full !h-9 [&_.ant-input-number-input-wrap]:!h-9 [&_.ant-input-number-input]:!h-9"
              value={max_length}
              onChange={handleMaxLengthChange}
              placeholder="MAX"
              min={1}
            />
          </div>
          {max_length < min_length && (
            <div className="text-ever-error mt-1">
              Max length should be greater than Min length
            </div>
          )}
        </div>
      )}
    </div>
  );
}
