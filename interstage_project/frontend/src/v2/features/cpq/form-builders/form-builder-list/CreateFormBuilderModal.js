import { ArrowCircleRightIcon } from "@everstage/evericons/outlined";
import { isEmpty } from "lodash";
import { useForm, useWatch, Controller } from "react-hook-form";

import { EverModal, EverButton, EverTg, EverInput } from "~/v2/components";
import { LabeledField } from "~/v2/features/cpq/components";

const CreateFormBuilderModal = ({
  visible,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const {
    control,
    formState: { errors },
    reset,
    handleSubmit,
  } = useForm({
    mode: "onChange",
    defaultValues: {
      name: "",
      description: "",
    },
  });
  const name = useWatch({ control, name: "name" });

  const handleClose = () => {
    reset();
    onClose();
  };

  const handleCreateQuoteForm = ({ name, description }) => {
    onSubmit({
      form_builder_name: name,
      form_builder_description: description,
    });
  };

  return (
    <EverModal
      visible={visible}
      onCancel={!loading && handleClose}
      destroyOnClose
      width={640}
    >
      <div className="flex flex-col items-center justify-between py-10 px-24 gap-12 w-full">
        <div className="flex flex-col w-full gap-3 text-center">
          <EverTg.Heading1>Create New Form </EverTg.Heading1>
          <EverTg.Text className="text-ever-base-content-mid">
            Create and configure new form with customised sections and fields
            for seamless quoting.
          </EverTg.Text>
        </div>
        <div className="flex flex-col gap-8 w-full">
          <div className="flex flex-col gap-4">
            <LabeledField
              label="Name"
              className="gap-2"
              labelClassName="text-xs"
              error={errors.name}
              required
            >
              <Controller
                control={control}
                name="name"
                rules={{
                  required: "Form name is required",
                }}
                render={({ field }) => (
                  <EverInput {...field} placeholder="Enter form name" />
                )}
              />
            </LabeledField>
            <LabeledField
              label="Description"
              className="gap-2"
              labelClassName="text-xs"
            >
              <Controller
                control={control}
                name="description"
                render={({ field }) => (
                  <EverInput.TextArea
                    {...field}
                    placeholder="Enter description"
                    autoSize={{ minRows: 2, maxRows: 2 }}
                    maxLength={250}
                  />
                )}
              />
            </LabeledField>
          </div>
          <EverButton
            appendIcon={<ArrowCircleRightIcon className="w-5 h-5" />}
            onClick={handleSubmit(handleCreateQuoteForm)}
            disabled={isEmpty(name)}
            loading={loading}
          >
            Add form
          </EverButton>
        </div>
      </div>
    </EverModal>
  );
};

export default CreateFormBuilderModal;
