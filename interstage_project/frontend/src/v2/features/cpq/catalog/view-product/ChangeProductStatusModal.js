import {
  InfoCircleIcon,
  CheckCircleBrokenIcon,
} from "@everstage/evericons/outlined";
import { format, isBefore } from "date-fns";
import React, { useEffect, useState } from "react";
import { useMutation } from "react-query";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  EverDatePicker,
  EverModal,
  EverRadio,
  EverTg,
  message,
} from "~/v2/components";

import { ANIMATION_TYPES, AnimatedWrapper } from "../../components";
import { useFetchApiWithAuth } from "../../hooks";

const STATUS_PROPS = {
  activate: {
    icon: (
      <div className="rounded-full bg-ever-primary-lite w-16 h-16 flex items-center justify-center">
        <CheckCircleBrokenIcon className="text-ever-info w-8 h-8" />
      </div>
    ),
    title: "Mark as Active?",
    description: "Reps can include active products in quotes",
    radioText: "Activate now",
    successMessage: "Product activated successfully.",
    scheduleSuccessMessage: "Product activation scheduled successfully.",
    payload_key: "schedule_start_date",
  },
  deactivate: {
    icon: (
      <div className="rounded-full bg-ever-primary-lite w-16 h-16 flex items-center justify-center">
        <InfoCircleIcon className="text-ever-info w-8 h-8" />
      </div>
    ),
    title: "Mark as Inactive?",
    description: "Reps cannot include inactive products in quotes",
    radioText: "Deactivate now",
    successMessage: "Product deactivated successfully.",
    scheduleSuccessMessage: "Product deactivation scheduled successfully.",
    payload_key: "schedule_end_date",
  },
};

export function ChangeProductStatusModal({
  visible,
  productId,
  operation = "activate",
  updatingScheduleDate,
  scheduleStartDate,
  scheduleEndDate,
  handleClose,
  refetchProduct,
}) {
  const { fetchData } = useFetchApiWithAuth();

  const [startNow, setStartNow] = useState(true);
  const [scheduleDate, setScheduleDate] = useState(null);

  const changeProductStatusRequest = useMutation(
    (payload) =>
      fetchData(`/ninja/cpq/product_catalog/${operation}`, "POST", payload),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: async () => {
        message.success(
          startNow
            ? STATUS_PROPS[operation].successMessage
            : STATUS_PROPS[operation].scheduleSuccessMessage
        );
        refetchProduct();
        handleCloseModal();
      },
    }
  );

  function handleRadioButtonToggle(value) {
    setStartNow(value === "fromNow");
  }

  function handleScheduleDateChange(value) {
    setScheduleDate(value);
  }

  function handleCloseModal() {
    setStartNow(false);
    setScheduleDate(null);
    handleClose();
  }

  function handleUpdateProduct() {
    const payload = {
      product_id: productId,
    };
    payload[STATUS_PROPS[operation].payload_key] = startNow
      ? format(new Date(), "yyyy-MM-dd")
      : format(scheduleDate, "yyyy-MM-dd");
    changeProductStatusRequest.mutate(payload);
  }

  useEffect(() => {
    if (updatingScheduleDate) {
      setStartNow(false);
      setScheduleDate(
        operation === "activate"
          ? new Date(scheduleStartDate)
          : new Date(scheduleEndDate)
      );
    }
  }, [operation, updatingScheduleDate, scheduleStartDate, scheduleEndDate]);

  return (
    <EverModal
      visible={visible}
      onCancel={handleCloseModal}
      destroyOnClose
      centered
    >
      <div className="flex flex-col  items-center justify-center py-8 px-10">
        {STATUS_PROPS[operation]?.icon}
        <div className="flex flex-col gap-2 text-center mt-8">
          <EverTg.Heading2>{STATUS_PROPS[operation].title}</EverTg.Heading2>
          <EverTg.Text className="text-ever-base-content-mid">
            {STATUS_PROPS[operation].description}
          </EverTg.Text>
        </div>
        <div className="flex flex-col gap-4 justify-center items-center mt-6">
          <EverRadio.Group
            onChange={(e) => handleRadioButtonToggle(e.target.value)}
            value={startNow ? "fromNow" : "scheduleLater"}
          >
            <EverRadio value={"fromNow"}>
              {STATUS_PROPS[operation].radioText}
            </EverRadio>
            <EverRadio value={"scheduleLater"}>Schedule for later</EverRadio>
          </EverRadio.Group>
          <AnimatedWrapper
            isVisible={!startNow}
            motionProps={{
              type: ANIMATION_TYPES.EXPAND_COLLAPSE,
            }}
          >
            <EverDatePicker
              value={scheduleDate}
              onChange={(value) => handleScheduleDateChange(value)}
              disabled={startNow}
              className="w-80"
              placeholder="Select date"
              allowClear={false}
              format="MMM dd, yyyy"
              disabledDate={(current) => {
                return current && isBefore(current, new Date());
              }}
            />
          </AnimatedWrapper>
        </div>
        <div className="flex w-full items-center mt-12 gap-4">
          <EverButton
            type="ghost"
            color="base"
            onClick={handleCloseModal}
            className="w-48"
          >
            Cancel
          </EverButton>
          <EverButton
            type="filled"
            color="primary"
            onClick={handleUpdateProduct}
            className="w-48"
            disabled={
              changeProductStatusRequest.isLoading ||
              (!startNow && !scheduleDate)
            }
          >
            Confirm
          </EverButton>
        </div>
      </div>
    </EverModal>
  );
}
