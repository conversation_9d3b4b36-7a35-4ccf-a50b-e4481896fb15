import { PencilLineIcon } from "@everstage/evericons/duotone";
import {
  CopyIcon,
  TrashIcon,
  InfoCircleIcon,
  PauseCircleIcon,
} from "@everstage/evericons/outlined";
import { DotsVerticalIcon } from "@everstage/evericons/solid";
import { Dropdown, Menu } from "antd";
import { format } from "date-fns";
import { debounce } from "lodash";
import React, { useRef, useState } from "react";
import { useMutation } from "react-query";
import { useNavigate } from "react-router-dom";

import { capitalizeFirstLetter } from "~/Utils";
import {
  EverBadge,
  EverButton,
  EverDivider,
  EverLoader,
  EverModal,
  EverTg,
  message,
} from "~/v2/components";

import { ChangeProductStatusModal } from "./ChangeProductStatusModal";
import { TruncatedText } from "../../components";
import { useFetchApiWithAuth } from "../../hooks";
import { getStatusBadgeProps } from "../../utils";
import { AddProductModal } from "../AddProductModal";
import { CloneProductModal } from "../CloneProductModal";
import {
  hasDatePassed,
  PRODUCT_ACTIONS,
  PRODUCT_ACTIONS_SUCCESS_MESSAGES,
} from "../utils";

const dropdownOptions = [
  {
    label: "Mark as Inactive",
    value: "markAsInactive",
    icon: <PauseCircleIcon className="w-4 h-4 text-ever-base-content-mid" />,
  },
  {
    label: "divider",
    value: "divider",
  },
  {
    label: "Clone",
    value: "clone",
    icon: <CopyIcon className="w-4 h-4 text-ever-base-content-mid" />,
  },
  {
    label: "Delete",
    value: "delete",
    icon: <TrashIcon className="w-4 h-4 text-ever-error" />,
  },
];

export function ProductHeader({
  product,
  refetchProduct,
  isFetching,
  hasPricepoints,
}) {
  const { fetchData } = useFetchApiWithAuth();
  const navigate = useNavigate();

  const [activateProductModalVisible, setActivateProductModalVisible] =
    useState(false);
  const [modalOperation, setModalOperation] = useState(null);
  const [updatingScheduleDate, setUpdatingScheduleDate] = useState(false);
  const [createProductModalVisible, setCreateProductModalVisible] =
    useState(false);
  const [showCloneProductModal, setShowCloneProductModal] = useState(false);

  const modifyProductRequest = useMutation(
    ({ action, productId, payload = {}, method = "POST" }) =>
      fetchData(
        `/ninja/cpq/product_catalog/${action}/${productId}`,
        method,
        payload
      ),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: (res, { action }) => {
        message.success(
          res?.message || PRODUCT_ACTIONS_SUCCESS_MESSAGES[action]
        );
        if (action === PRODUCT_ACTIONS.DELETE) {
          navigate("/cpq/product-catalog");
        }
      },
    }
  );

  function getBadgeProps() {
    const badgeProps = getStatusBadgeProps(
      capitalizeFirstLetter(product.status)
    );
    return badgeProps;
  }

  function handleCloseStatusModal() {
    setUpdatingScheduleDate(false);
    setActivateProductModalVisible(false);
    setModalOperation(null);
  }

  function handleMenuButtonClick(value) {
    switch (value) {
      case "markAsInactive": {
        setModalOperation("deactivate");
        setActivateProductModalVisible(true);
        break;
      }
      case "clone": {
        setShowCloneProductModal(true);
        break;
      }
      case "delete": {
        modifyProductRequest.mutate({
          action: PRODUCT_ACTIONS.DELETE,
          productId: product.product_id,
          method: "DELETE",
        });
        break;
      }
      default: {
        break;
      }
    }
  }

  const dropdownMenu = () => {
    return (
      <Menu>
        {dropdownOptions.map((option) => {
          if (
            product.status === "inactive" &&
            (option.value === "markAsInactive" || option.value === "divider")
          ) {
            return null;
          }
          if (option.value === "divider") {
            return <EverDivider key={option.value} />;
          }
          return (
            <Menu.Item
              key={option.value}
              className={
                option.value === "delete" ? "hover:!bg-ever-error-lite" : ""
              }
              onClick={() => handleMenuButtonClick(option.value)}
            >
              <div className="flex items-center gap-2">
                {option.icon}
                <EverTg.Caption
                  className={option.value === "delete" ? "text-ever-error" : ""}
                >
                  {option.label}
                </EverTg.Caption>
              </div>
            </Menu.Item>
          );
        })}
      </Menu>
    );
  };

  return (
    <>
      <div className="flex flex-col">
        <div className="w-full flex items-center justify-between px-8 pt-3 pb-4 bg-ever-base-50 border-t-0 border-r-0 border-b border-l-0 border-solid border-ever-base-400">
          <div className="w-4/5 flex flex-col gap-1">
            <div className="w-4/5 flex gap-2.5 items-center">
              {isFetching ? (
                <EverLoader.Skeleton
                  config={[2]}
                  className="h-6 !p-0 !pl-1 !w-24"
                />
              ) : (
                <TruncatedText text={product.name}>
                  <EverTg.Heading2>{product.name}</EverTg.Heading2>
                </TruncatedText>
              )}
              {isFetching ? (
                <EverLoader.Skeleton
                  config={[2]}
                  className="h-5 !p-0 !pl-1 !w-16"
                />
              ) : (
                <EverBadge
                  title={capitalizeFirstLetter(product.status)}
                  {...getBadgeProps()}
                />
              )}
            </div>
            {isFetching ? (
              <EverLoader.Skeleton
                config={[2]}
                className="h-6 !p-0 !pl-1 !w-1/2"
              />
            ) : (
              <TruncatedText text={product.description || "-"}>
                <EverTg.Text>
                  {product.description ||
                    "Description has not been added for this product."}
                </EverTg.Text>
              </TruncatedText>
            )}
          </div>
          {isFetching ? (
            <div className="flex gap-3">
              <EverLoader.Skeleton
                config={[2]}
                className="h-12 !p-0 !pl-1 !w-12"
              />
              <EverLoader.Skeleton
                config={[2]}
                className="h-12 !p-0 !pl-1 !w-24"
              />
              <EverLoader.Skeleton
                config={[2]}
                className="h-12 !p-0 !pl-1 !w-12"
              />
            </div>
          ) : (
            <div className="flex gap-3">
              <EverButton.Icon
                size="small"
                color="base"
                type="ghost"
                icon={<PencilLineIcon className="text-ever-base-content-mid" />}
                onClick={() => {
                  setCreateProductModalVisible(true);
                }}
              />
              {product.status === "inactive" &&
                (!product.schedule_start_date ||
                  hasDatePassed(product.schedule_start_date)) && (
                  <EverButton
                    size="small"
                    color="primary"
                    type="filled"
                    tooltipTitle={
                      hasPricepoints
                        ? ""
                        : "Add at least 1 price point to make this product active"
                    }
                    disabled={!hasPricepoints}
                    onClick={() => {
                      setModalOperation("activate");
                      setActivateProductModalVisible(true);
                    }}
                  >
                    Activate Product
                  </EverButton>
                )}
              <Dropdown trigger={["click"]} overlay={dropdownMenu()}>
                <EverButton.Icon
                  size="small"
                  color="base"
                  type="filled"
                  icon={<DotsVerticalIcon />}
                />
              </Dropdown>
            </div>
          )}
        </div>
        <ScheduleDateFooter
          product={product}
          setUpdatingScheduleDate={setUpdatingScheduleDate}
          setModalOperation={setModalOperation}
          setActivateProductModalVisible={setActivateProductModalVisible}
          refetchProduct={refetchProduct}
        />
      </div>
      {activateProductModalVisible && (
        <ChangeProductStatusModal
          visible={activateProductModalVisible}
          productId={product.product_id}
          operation={modalOperation}
          updatingScheduleDate={updatingScheduleDate}
          scheduleStartDate={product.schedule_start_date}
          scheduleEndDate={product.active_till}
          handleClose={handleCloseStatusModal}
          refetchProduct={refetchProduct}
        />
      )}
      <AddProductModal
        visible={createProductModalVisible}
        setVisible={setCreateProductModalVisible}
        isEdit={true}
        product={product}
        refetchProduct={refetchProduct}
      />
      {showCloneProductModal && (
        <CloneProductModal
          visible={showCloneProductModal}
          setVisible={setShowCloneProductModal}
          productId={product.product_id}
          productName={product.name}
        />
      )}
    </>
  );
}

function ScheduleDateFooter({
  product,
  setUpdatingScheduleDate,
  setModalOperation,
  setActivateProductModalVisible,
  refetchProduct,
}) {
  let operation = null;
  const { fetchData } = useFetchApiWithAuth();
  const modalRef = useRef(null);

  const changeProductStatusRequest = useMutation(
    (payload) =>
      fetchData(`/ninja/cpq/product_catalog/${operation}`, "POST", payload),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: async (response) => {
        message.success(
          response?.message || "Schedule date deleted successfully."
        );
        refetchProduct();
        modalRef.current.destroy();
      },
    }
  );

  if (
    product.schedule_start_date &&
    !hasDatePassed(product.schedule_start_date)
  ) {
    operation = "activate";
  }
  if (product.active_till && !hasDatePassed(product.active_till)) {
    operation = "deactivate";
  }
  if (!operation) {
    return null;
  }

  function handleDeleteSchedule() {
    const payload = {
      product_id: product.product_id,
      delete_schedule: true,
    };
    changeProductStatusRequest.mutate(payload);
  }

  const debouncedDeleteSchedule = debounce(handleDeleteSchedule, 300);

  function handleDeleteScheduleDate() {
    modalRef.current = EverModal.confirm({
      title: "Do you want to delete this schedule?",
      subtitle: "This action cannot be undone",
      icon: <TrashIcon className="w-8 h-8 text-ever-error" />,
      iconContainerClasses: "bg-ever-error-lite",
      confirmationButtons: [
        <EverButton
          key="cancel"
          type="filled"
          color="base"
          onClick={() => closeModal()}
        >
          Cancel
        </EverButton>,
        <EverButton
          key="delete"
          onClick={debouncedDeleteSchedule}
          type="filled"
          color="error"
        >
          Yes, delete
        </EverButton>,
      ],
    });
    const closeModal = () => {
      modalRef.current.destroy();
    };
  }

  return (
    <div className="flex gap-2 h-10 bg-ever-info-lite rounded-b-lg px-8 py-2 items-center justify-between shadow-md">
      <div className="flex items-center gap-2">
        <InfoCircleIcon className="w-4 h-4 text-ever-info" />
        <EverTg.Caption className="text-ever-info-hover font-medium">
          {product.name} is scheduled to be{" "}
          {operation === "deactivate" ? "inactive" : "active"} on{" "}
          {operation === "deactivate"
            ? product?.active_till
              ? format(new Date(product?.active_till), "MMM d, yyyy")
              : ""
            : operation === "activate"
            ? product?.schedule_start_date
              ? format(new Date(product?.schedule_start_date), "MMM d, yyyy")
              : ""
            : ""}
        </EverTg.Caption>
      </div>
      <div className="flex items-center gap-2">
        <EverButton
          size="small"
          color="primary"
          type="link"
          icon={<PencilLineIcon />}
          onClick={() => {
            setUpdatingScheduleDate(true);
            if (product.status === "active") {
              setModalOperation("deactivate");
            } else {
              setModalOperation("activate");
            }
            setActivateProductModalVisible(true);
          }}
        >
          Change date
        </EverButton>
        <EverDivider type="vertical" />
        <EverButton.Icon
          size="small"
          color="error"
          type="link"
          icon={<TrashIcon />}
          onClick={handleDeleteScheduleDate}
        />
      </div>
    </div>
  );
}
