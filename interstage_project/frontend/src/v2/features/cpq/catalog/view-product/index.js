import { format } from "date-fns";
import React, { useState } from "react";
import { useQuery } from "react-query";
import { useLocation } from "react-router-dom";
import { twMerge } from "tailwind-merge";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>oader, EverTg, message } from "~/v2/components";

import { PricePointsView } from "./product-details";
import { ProductHeader } from "./ProductHeader";
import { TruncatedText } from "../../components";
import { useFetchApiWithAuth } from "../../hooks";
import { getDisplayNameforKeys, PRODUCT_DETAILS_MAP } from "../utils";

export default function ViewProduct() {
  const { fetchData } = useFetchApiWithAuth();
  const location = useLocation();

  const [currentProduct, setCurrentProduct] = useState({});
  const [productHasPricepoints, setProductHasPricepoints] = useState(false);

  const { refetch, isFetching } = useQuery(
    ["getProduct"],
    () => {
      return fetchData(
        `/ninja/cpq/product_catalog/product/${location.pathname
          .split("/")
          .pop()}`,
        "GET"
      );
    },
    {
      cacheTime: 0,
      refetchOnWindowFocus: false,
      retry: false,
      onError: (error) => {
        message.error(error?.message || "Something went wrong");
      },
      onSettled: (data) => {
        handleProductChange(data);
      },
    }
  );

  function handleProductChange(product) {
    setCurrentProduct(product);
  }

  return (
    <div className="w-full h-full flex flex-col">
      <ProductHeader
        product={currentProduct}
        refetchProduct={refetch}
        isFetching={isFetching}
        hasPricepoints={productHasPricepoints}
      />
      <div className={twMerge("w-full flex gap-6 px-8 py-6 flex-grow")}>
        <div className="w-[80%] h-full">
          <PricePointsView
            productId={currentProduct?.product_id}
            setProductHasPricepoints={setProductHasPricepoints}
          />
        </div>
        <div className="h-full flex flex-col gap-5 border border-solid border-ever-base-400 rounded-2xl py-5 px-4 w-[360px]">
          <EverTg.Heading4>Product Details</EverTg.Heading4>
          <div className="flex flex-col gap-4">
            {Object.keys(PRODUCT_DETAILS_MAP).map((key) => (
              <div className="flex justify-between" key={key}>
                <EverLabel className="min-w-32">
                  {PRODUCT_DETAILS_MAP[key].label}
                </EverLabel>
                {isFetching ? (
                  <EverLoader.Skeleton
                    config={[2]}
                    className={twMerge(
                      "h-3.5 !p-0 !pl-1",
                      PRODUCT_DETAILS_MAP[key].loaderWidth
                    )}
                  />
                ) : (
                  <TruncatedText text={currentProduct[key]}>
                    <EverTg.Text>
                      {PRODUCT_DETAILS_MAP[key].type === "date"
                        ? currentProduct[key]
                          ? format(new Date(currentProduct[key]), "MMM d, yyyy")
                          : "-"
                        : PRODUCT_DETAILS_MAP[key].getDisplayName
                        ? getDisplayNameforKeys(currentProduct[key])
                        : currentProduct[key] || "-"}
                    </EverTg.Text>
                  </TruncatedText>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
