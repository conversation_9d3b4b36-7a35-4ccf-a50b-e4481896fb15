import { CalendarPlusIcon } from "@everstage/evericons/duotone";
import { isBefore } from "date-fns";
import { debounce } from "lodash";
import React, { useEffect, useState, useCallback } from "react";

import {
  EverModal,
  EverRadio,
  EverTg,
  EverButton,
  EverDatePicker,
} from "~/v2/components";
import { AnimatedWrapper, ANIMATION_TYPES } from "~/v2/features/cpq/components";

import { hasDatePassed } from "../utils";

export function SchedulePricePointModal({
  visible,
  setVisible,
  pricePoint,
  handleConfirm,
  dateKey = "schedule_start_date",
  isDateUpdating = false,
  handleDelete = () => {},
  allowDelete = false,
  isConfirmEventLoading = false,
}) {
  const [startNow, setStartNow] = useState("fromNow");
  const [scheduleStartDate, setScheduleStartDate] = useState(null);

  function handleRadioButtonToggle(value) {
    setStartNow(value);
  }

  function handleScheduleDateChange(value) {
    setScheduleStartDate(value);
  }

  function handleCloseModal() {
    setVisible(false);
    setStartNow("fromNow");
    setScheduleStartDate(null);
  }

  useEffect(() => {
    scheduleStartDate !== null && setStartNow("scheduleLater");
  }, [scheduleStartDate]);

  useEffect(() => {
    !hasDatePassed(pricePoint[dateKey]) &&
      setScheduleStartDate(
        pricePoint[dateKey] ? new Date(pricePoint[dateKey]) : null
      );
  }, [pricePoint, dateKey]);

  // Create a debounced version of handleConfirm
  const debouncedHandleConfirm = useCallback(
    debounce((date) => {
      handleConfirm(date);
    }, 500),
    [handleConfirm]
  );

  return (
    <EverModal
      visible={visible}
      onCancel={handleCloseModal}
      destroyOnClose
      centered
    >
      <div className="flex flex-col  items-center justify-center py-8 px-10">
        <div className="rounded-full bg-ever-primary-lite w-16 h-16 flex items-center justify-center">
          <CalendarPlusIcon className="text-ever-info w-8 h-8" />
        </div>
        <div className="flex flex-col gap-2 text-center mt-8">
          <EverTg.Heading2>
            When do you want changes to take effect?
          </EverTg.Heading2>
        </div>
        <div className="flex flex-col gap-4 justify-center items-center mt-6">
          <EverRadio.Group
            onChange={(e) => handleRadioButtonToggle(e.target.value)}
            value={startNow}
          >
            <EverRadio value={"fromNow"}>From now</EverRadio>
            <EverRadio value={"scheduleLater"}>Schedule for later</EverRadio>
          </EverRadio.Group>
          <AnimatedWrapper
            isVisible={startNow === "scheduleLater"}
            motionProps={{
              type: ANIMATION_TYPES.EXPAND_COLLAPSE,
            }}
          >
            <EverDatePicker
              value={scheduleStartDate}
              onChange={(value) => handleScheduleDateChange(value)}
              disabled={startNow === "fromNow"}
              className="w-80"
              placeholder="Select date"
              allowClear={false}
              format="MMM dd, yyyy"
              disabledDate={(current) => {
                return current && isBefore(current, new Date());
              }}
            />
          </AnimatedWrapper>
        </div>
        <div className="flex w-full items-center mt-12 gap-4">
          <EverButton
            type="ghost"
            color="base"
            onClick={
              isDateUpdating && allowDelete ? handleDelete : handleCloseModal
            }
            className="w-48"
          >
            {isDateUpdating && allowDelete ? "Delete schedule" : "Cancel"}
          </EverButton>
          <EverButton
            type="filled"
            color="primary"
            onClick={() =>
              debouncedHandleConfirm(
                startNow === "fromNow" ? new Date() : scheduleStartDate
              )
            }
            className="w-48"
            loading={isConfirmEventLoading}
            disabled={
              isConfirmEventLoading ||
              (startNow === "scheduleLater" && !scheduleStartDate)
            }
          >
            {isDateUpdating ? "Update schedule" : "Confirm"}
          </EverButton>
        </div>
      </div>
    </EverModal>
  );
}
