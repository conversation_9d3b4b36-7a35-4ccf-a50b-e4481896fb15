import { CopyIcon, Trash03Icon } from "@everstage/evericons/outlined";

import { getLocalizedCurrencyValue } from "~/Utils/CurrencyUtils";
import { EverLoader } from "~/v2/components";

import {
  SingleSkeletonCell,
  SkeletonCellHeader,
} from "../components/ag-grid/AgGridLoaderTable";

export const BILLING_TYPES = [
  { label: "One Time", value: "one_time" },
  { label: "Recurring", value: "recurring" },
];

export const CATEGORY_TYPES = [
  { label: "Software", value: "software" },
  { label: "Services", value: "services" },
  { label: "Add-Ons", value: "add-ons" },
];

export function productStatusMap(status) {
  switch (status) {
    case "active": {
      return "Active";
    }
    case "inactive": {
      return "Inactive";
    }
    default: {
      return status;
    }
  }
}

export const PRODUCT_ACTIONS = {
  CLONE: "clone",
  DELETE: "delete",
};

export const PRODUCT_MENU_ITEMS = [
  {
    icon: <CopyIcon />,
    label: "Clone",
    action: PRODUCT_ACTIONS.CLONE,
  },
  {
    icon: <Trash03Icon />,
    label: "Delete",
    action: PRODUCT_ACTIONS.DELETE,
  },
];

export const DELETE_PRODUCT_CONFIRMATION_TEXT = {
  title: "Are you sure to remove this product?",
  subtitle: "This action cannot be undone",
};

export const PRODUCT_DETAILS_MAP = {
  sku: { label: "Product SKU", type: "string", loaderWidth: "!w-16" },
  billing_type: {
    label: "Billing type",
    type: "string",
    getDisplayName: true,
    loaderWidth: "!w-20",
  },
  category: {
    label: "Category",
    type: "string",
    getDisplayName: true,
    loaderWidth: "!w-16",
  },
  charge_unit: {
    label: "Charged on",
    type: "string",
    loaderWidth: "!w-16",
  },
  // build_cost: { label: "Cost to build", type: "number" },
  created_on: { label: "Created on", type: "date", loaderWidth: "!w-20" },
  last_modified: {
    label: "Last modified on",
    type: "date",
    loaderWidth: "!w-20",
  },
  active_till: { label: "Active till", type: "date", loaderWidth: "!w-24" },
  // managed_by: { label: "Managed by", type: "string", loaderWidth: "!w-16" },
};

export const PRICE_POINT_ACTIONS = {
  EDIT: "edit",
  DEACTIVATE: "deactivate",
  ACTIVATE: "activate",
};

export const PRICE_POINT_PRICE_MODELS = {
  FLAT_FEE: "flatfee",
  VOLUME: "volume",
  TIERED: "tiered",
  PER_UNIT: "perunit",
};

export const FILTER_PRICE_POINTS_BUTTONS = [
  {
    label: "Flat Fee",
    value: PRICE_POINT_PRICE_MODELS.FLAT_FEE,
  },
  {
    label: "Volume",
    value: PRICE_POINT_PRICE_MODELS.VOLUME,
  },
  {
    label: "Tiered",
    value: PRICE_POINT_PRICE_MODELS.TIERED,
  },
  //   {
  //     label: "% of products",
  //     value: "percOfProds",
  //   },
  {
    label: "Per unit",
    value: PRICE_POINT_PRICE_MODELS.PER_UNIT,
  },
];

export const PRICE_MODEL_TYPES = [
  { label: "Volume", value: PRICE_POINT_PRICE_MODELS.VOLUME },
  { label: "Tiered", value: PRICE_POINT_PRICE_MODELS.TIERED },
  { label: "Per unit", value: PRICE_POINT_PRICE_MODELS.PER_UNIT },
  { label: "Flat fee", value: PRICE_POINT_PRICE_MODELS.FLAT_FEE },
];

export function getDisplayNameforKeys(key) {
  switch (key) {
    case "one-time": {
      return "One Time";
    }
    case "daily": {
      return "Daily";
    }
    case "monthly": {
      return "Monthly";
    }
    case "quarterly": {
      return "Quarterly";
    }
    case "half-yearly": {
      return "Half Yearly";
    }
    case "annual": {
      return "Annual";
    }
    case PRICE_POINT_PRICE_MODELS.VOLUME: {
      return "Volume";
    }
    case PRICE_POINT_PRICE_MODELS.TIERED: {
      return "Tiered";
    }
    case PRICE_POINT_PRICE_MODELS.PER_UNIT: {
      return "Per Unit";
    }
    case PRICE_POINT_PRICE_MODELS.FLAT_FEE: {
      return "Flat Fee";
    }
    case "one_time": {
      return "One Time";
    }
    case "recurring": {
      return "Recurring";
    }
    case "software": {
      return "Software";
    }
    case "services": {
      return "Services";
    }
    case "add-ons": {
      return "Add-Ons";
    }
    default: {
      return key;
    }
  }
}

export const DRAWER_STEPS = {
  BASIC_INFO: 0,
  PRICE_SETTINGS: 1,
  SHOW_EDITS: 2,
};

export const PRICE_POINT_TABLE_SKELETON_COLUMN_DEFS = [
  {
    field: "0",
    headerComponent: SkeletonCellHeader,
    cellRenderer: SingleSkeletonCell,
  },
  {
    field: "1",
    headerComponent: SkeletonCellHeader,
    cellRenderer: SingleSkeletonCell,
  },
  {
    field: "5",
    headerComponent: SkeletonCellHeader,
    cellRenderer: SingleSkeletonCell,
  },
  {
    field: "3",
    width: 80,
    headerComponent: SkeletonCellHeader,
    cellRenderer: SingleSkeletonCell,
  },
  {
    field: "2",
    width: 100,
    type: "rightAligned",
    headerComponent: SkeletonCellHeader,
    cellRenderer: SingleSkeletonCell,
    cellRendererParams: { rightAligned: true },
  },
];

export const PRODUCT_TABLE_SKELETON_COLUMN_DEFS = [
  {
    field: "0",
    minWidth: 340,
    headerComponent: SkeletonCellHeader,
    cellRenderer: () => {
      return (
        <div className="flex flex-col gap-2">
          <EverLoader.Skeleton
            config={[2]}
            className="h-3.5 !p-0 !pl-1 !w-64"
          />
        </div>
      );
    },
  },
  {
    field: "1",
    headerComponent: SkeletonCellHeader,
    minWidth: 216,
    cellRenderer: SingleSkeletonCell,
  },
  {
    field: "5",
    headerComponent: SkeletonCellHeader,
    cellRenderer: SingleSkeletonCell,
  },
  {
    field: "3",
    headerComponent: SkeletonCellHeader,
    cellRenderer: SingleSkeletonCell,
  },
  {
    field: "4",
    headerComponent: SkeletonCellHeader,
    cellRenderer: SingleSkeletonCell,
  },
  {
    field: "2",
    minWidth: 260,
    type: "rightAligned",
    headerComponent: SkeletonCellHeader,
    cellRenderer: SingleSkeletonCell,
    cellRendererParams: { rightAligned: true },
  },
];

export function hasDatePassed(timestamp) {
  if (!timestamp) {
    return false;
  }
  const currentDate = new Date();
  const dateToCheck = new Date(timestamp);

  return dateToCheck < currentDate;
}

export const PRICE_POINT_STATE_MAP = {
  ACTIVE: "Active",
  INACTIVE: "Inactive",
  SCHEDULED_ACTIVATION: "Scheduled Activation",
  SCHEDULED_DEACTIVATION: "Scheduled Deactivation",
  SCHEDULED_UPDATE: "Scheduled Update",
};

export const PRODUCT_ACTIONS_SUCCESS_MESSAGES = {
  [PRODUCT_ACTIONS.INACTIVE]: "Product marked as inactive successfully",
  [PRODUCT_ACTIONS.CLONE]: "Product cloned successfully",
  [PRODUCT_ACTIONS.DELETE]: "Product deleted successfully",
};

export function getFormattedAmount(amount) {
  const formattedAmount = getLocalizedCurrencyValue(amount, "$", "en-US", 2);
  return Number.isInteger(formattedAmount)
    ? formattedAmount
    : formattedAmount.replace(/\.00$/, "");
}
