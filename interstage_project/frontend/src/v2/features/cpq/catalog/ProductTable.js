import { AgGridReact } from "ag-grid-react";
import { debounce } from "lodash";
import React, { useMemo, useRef, useState } from "react";

import {
  everAgGridCallbacks,
  everAgGridOptions,
} from "~/v2/components/ag-grid";

export function ProductTable({
  rowData,
  columnDefs,
  getRowId,
  rowClassRules,
  autoSizeStrategy,
}) {
  const tableRef = useRef(null);
  const [_gridApi, setGridApi] = useState(null);

  const defaultColumnDefs = {
    flex: 1,
    suppressHeaderMenuButton: true,
    sortable: true,
    headerClass: "!text-ever-base-content-mid !uppercase !text-xs",
  };

  const defaultRowClassRules = useMemo(
    () => ({
      "more-menu-button-active": (params) => params.node.data.isHovered,
    }),
    []
  );

  // Create a debounced version of the resize function
  const resizeColumns = useMemo(
    () =>
      debounce((params) => {
        if (params?.api) {
          setTimeout(() => {
            if (autoSizeStrategy === "autoSizeAll") {
              params.api.autoSizeAllColumns();
            } else if (autoSizeStrategy === "fitGridWidth") {
              params.api.fitGridWidth();
            }
          }, 100);
        }
      }, 100),
    []
  );

  const onGridReady = (params) => {
    setGridApi(params);
    resizeColumns(params);
  };

  return (
    <div ref={tableRef} className="w-full h-full mt-2 mb-5 overflow-hidden">
      <div className="ag-theme-material w-full h-full overflow-y-auto quote-list-table">
        <AgGridReact
          {...everAgGridOptions.getDefaultOptions()}
          getRowId={getRowId}
          onGridReady={onGridReady}
          onFirstDataRendered={resizeColumns}
          onModelUpdated={resizeColumns}
          onGridSizeChanged={resizeColumns}
          onColumnResized={resizeColumns}
          rowData={rowData}
          defaultColDef={defaultColumnDefs}
          columnDefs={columnDefs}
          rowClassRules={rowClassRules || defaultRowClassRules}
          rowHeight={40}
          headerHeight={48}
          suppressContextMenu={true}
          onCellValueChanged={(props) => {
            everAgGridCallbacks.onCellValueChangedStyled(props);
          }}
          rowBuffer={50}
          pagination={false}
        />
      </div>
    </div>
  );
}
