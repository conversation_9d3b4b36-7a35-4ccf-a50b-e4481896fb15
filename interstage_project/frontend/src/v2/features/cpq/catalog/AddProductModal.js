import { ArrowCircleRightIcon } from "@everstage/evericons/outlined";
import { cloneDeep, debounce } from "lodash";
import React, { useEffect, useState } from "react";
import { useMutation, useQuery } from "react-query";

import {
  EverButton,
  EverInput,
  EverModal,
  EverSelect,
  EverForm,
  EverTg,
  message,
} from "~/v2/components";

import { BILLING_TYPES, CATEGORY_TYPES } from "./utils";
import { useFetchApiWithAuth } from "../hooks";

const initialState = {
  name: "",
  sku: "",
  billingType: null,
  category: null,
  description: "",
  chargeUnit: "",
  buildCost: "",
};

export function AddProductModal({
  visible,
  setVisible,
  isEdit,
  product,
  refetchProducts = () => {},
  refetchProduct = () => {},
}) {
  const [form] = EverForm.useForm();
  const { fetchData } = useFetchApiWithAuth();
  const [existingProducts, setExistingProducts] = useState([]);
  const [productNameMap, setProductNameMap] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useQuery(
    ["getProducts"],
    () => {
      return fetchData(`/ninja/cpq/product_catalog/products`, "GET");
    },
    {
      cacheTime: 0,
      refetchOnWindowFocus: false,
      retry: false,
      onError: (error) => {
        message.error(error?.message || "Something went wrong");
      },
      onSettled: (data) => {
        setExistingProducts(data.products);
        const nameMap = {};
        for (const product of data.products) {
          nameMap[product.name] = true;
        }
        setProductNameMap(nameMap);
      },
    }
  );

  const createProductRequest = useMutation(
    (data) => fetchData("/ninja/cpq/product_catalog/create", "POST", data),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
        setIsSubmitting(false);
      },
      onSuccess: async () => {
        message.success(
          isEdit
            ? "Product updated successfully"
            : "Product created successfully"
        );
        setVisible(false);
        setIsSubmitting(false);
        if (isEdit) {
          refetchProduct();
        } else {
          refetchProducts();
        }
        form.resetFields();
      },
    }
  );

  function handleFinish(values) {
    setIsSubmitting(true);
    const payload = cloneDeep(values);
    if (isEdit) {
      payload.product_id = product.product_id;
    }
    payload.sku = payload.sku.trim();
    payload.name = payload.name.trim();
    payload.description = payload.description.trim();
    createProductRequest.mutate(payload);
  }

  // Debounced version of handleFinish
  const debouncedHandleFinish = debounce((values) => {
    handleFinish(values);
  }, 300);

  // Custom validator for SKU uniqueness
  const validateUniqueSKU = (_, value) => {
    if (value.trim() === "") {
      return Promise.reject(new Error("SKU is required"));
    }
    if (isEdit && value.trim() === product.sku) {
      return Promise.resolve();
    }
    if (existingProducts.some((p) => p.sku === value.trim())) {
      return Promise.reject(new Error("SKU must be unique"));
    }
    return Promise.resolve();
  };

  const validateUniqueName = (_, value) => {
    if (value.trim() === "") {
      return Promise.reject(new Error("Product name is required"));
    }
    if (isEdit && value.trim() === product.name.trim()) {
      return Promise.resolve();
    }
    if (productNameMap[value.trim()]) {
      return Promise.reject(new Error("Product name must be unique"));
    }
    return Promise.resolve();
  };

  useEffect(() => {
    if (isEdit && visible) {
      form.setFieldsValue(product);
    }
  }, [isEdit, product, form, visible]);

  return (
    <EverModal
      visible={visible}
      closable={true}
      onCancel={() => {
        form.resetFields();
        setVisible(false);
      }}
      maskClosable={false}
      destroyOnClose
      centered
      className="!w-[760px]"
      bodyStyle={{ padding: "0", maxHeight: "75vh" }}
    >
      <div className="flex flex-col items-center justify-center w-full gap-10 relative">
        <div className="flex flex-col gap-2.5 pt-14 px-28 sticky top-0 bg-white w-[90%] z-10 pb-2">
          <EverTg.Heading2 className="text-center">
            {isEdit ? "Edit Product" : "Add Product"}
          </EverTg.Heading2>
          <EverTg.Text className="text-center text-ever-base-content-mid">
            Create a product and set price
          </EverTg.Text>
        </div>
        <EverForm
          form={form}
          layout="vertical"
          onFinish={debouncedHandleFinish}
          initialValues={initialState}
          className="w-full"
        >
          <EverForm.Item
            label="Name"
            name="name"
            rules={[
              { required: true, message: "Please enter the product name" },
              { validator: validateUniqueName },
            ]}
            className="px-28"
          >
            <EverInput placeholder="Enter product name" />
          </EverForm.Item>
          <EverForm.Item
            label="Code / SKU"
            name="sku"
            rules={[
              { required: true, message: "Please enter the SKU code" },
              { validator: validateUniqueSKU },
            ]}
            className="px-28"
          >
            <EverInput placeholder="Enter SKU code for this product" />
          </EverForm.Item>
          <EverForm.Item
            label="Billing Type"
            name="billing_type"
            rules={[
              { required: true, message: "Please select a billing type" },
            ]}
            className="px-28"
          >
            <EverSelect
              placeholder="Select billing type"
              options={BILLING_TYPES}
            />
          </EverForm.Item>
          <EverForm.Item
            label="Category"
            name="category"
            rules={[
              { required: true, message: "Please select product category" },
            ]}
            className="px-28"
          >
            <EverSelect
              placeholder="Select product category"
              options={CATEGORY_TYPES}
            />
          </EverForm.Item>
          <EverForm.Item
            label="Description"
            name="description"
            rules={[{ required: false }]}
            className="px-28"
          >
            <EverInput.TextArea
              maxLength={500}
              showCount={true}
              placeholder="Product description appears on customer facing quotes and reps can modify them."
              className="[&>.ant-input]:min-h-[66px]"
            />
          </EverForm.Item>
          <EverForm.Item
            label="What do you charge this product on?"
            name="charge_unit"
            rules={[{ required: false }]}
            className="px-28"
          >
            <EverInput placeholder="Users, API requests, etc" />
          </EverForm.Item>
          {/* <EverForm.Item
            label="Cost to build"
            name="buildCost"
            rules={[{ required: false }]}
            className="w-full"
          >
            <EverInput.Number
              className="w-full"
              placeholder="Original cost of building this product"
            />
          </EverForm.Item> */}
          <EverForm.Item className="sticky bottom-0 bg-white w-full py-7 px-28 rounded-b-xl !mb-0 shadow-xl">
            <EverButton
              appendIcon={<ArrowCircleRightIcon />}
              htmlType="submit"
              color="primary"
              className="w-full"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {isEdit ? "Save Changes" : "Add Product"}
            </EverButton>
          </EverForm.Item>
        </EverForm>
      </div>
    </EverModal>
  );
}
