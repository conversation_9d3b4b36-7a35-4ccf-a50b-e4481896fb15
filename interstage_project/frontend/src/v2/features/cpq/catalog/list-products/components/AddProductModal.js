import { ArrowCircleRightIcon } from "@everstage/evericons/outlined";
import React from "react";
import { useNavigate } from "react-router-dom";

import {
  EverButton,
  EverInput,
  EverModal,
  EverSelect,
  EverTg,
  EverForm,
} from "~/v2/components";

import { BILLING_TYPES, CATEGORY_TYPES } from "./utils";

const initialState = {
  name: "",
  sku: "",
  billingType: null,
  category: null,
  description: "",
  chargeUnit: "",
  buildCost: "",
};

export default function AddProductModal({ visible, setVisible }) {
  const [form] = EverForm.useForm();
  const navigate = useNavigate();

  function handleFinish(values) {
    navigate(`/cpq/product-catalog/${values.name}`);
  }

  return (
    <EverModal
      visible={visible}
      closable={true}
      onCancel={() => {
        setVisible(false);
      }}
      maskClosable={false}
      destroyOnClose
      className="!w-[760px]"
      bodyStyle={{ padding: "56px 120px" }}
    >
      <div className="flex flex-col items-center justify-center w-[505px]">
        <div className="flex flex-col gap-2.5">
          <EverTg.Heading2 className="text-center">Add Product</EverTg.Heading2>
          <EverTg.Text className="text-center">
            Create and configure new products with customised details, pricing,
            and attributes for seamless quoting.
          </EverTg.Text>
        </div>
        <EverForm
          form={form}
          layout="vertical"
          onFinish={handleFinish}
          initialValues={initialState}
          className="w-full"
        >
          <EverForm.Item
            label="Name"
            name="name"
            rules={[
              { required: true, message: "Please enter the product name" },
            ]}
          >
            <EverInput placeholder="Enter product name" />
          </EverForm.Item>
          <EverForm.Item
            label="Code / SKU"
            name="sku"
            rules={[{ required: true, message: "Please enter the SKU code" }]}
          >
            <EverInput placeholder="Enter SKU code for this product" />
          </EverForm.Item>
          <EverForm.Item
            label="Billing Type"
            name="billingType"
            rules={[
              { required: true, message: "Please select a billing type" },
            ]}
          >
            <EverSelect
              placeholder="Select billing type"
              options={BILLING_TYPES}
            />
          </EverForm.Item>
          <EverForm.Item
            label="Category"
            name="category"
            rules={[
              { required: true, message: "Please select product category" },
            ]}
          >
            <EverSelect
              placeholder="Select product category"
              options={CATEGORY_TYPES}
            />
          </EverForm.Item>
          <EverForm.Item
            label="Description"
            name="description"
            rules={[{ required: false }]}
          >
            <EverInput.TextArea
              maxLength={500}
              showCount={true}
              placeholder="Product description appears on customer facing quotes and reps can modify them."
            />
          </EverForm.Item>
          <EverForm.Item
            label="What do you charge this product on?"
            name="chargeUnit"
            rules={[{ required: false }]}
          >
            <EverInput placeholder="Users, API requests, etc" />
          </EverForm.Item>
          <EverForm.Item
            label="Cost to build"
            name="buildCost"
            rules={[{ required: false }]}
            className="w-full"
          >
            <EverInput.Number
              className="w-full"
              placeholder="Original cost of building this product"
            />
          </EverForm.Item>

          <EverForm.Item>
            <EverButton
              appendIcon={<ArrowCircleRightIcon />}
              htmlType="submit"
              color="primary"
              className="w-full"
            >
              Add Product
            </EverButton>
          </EverForm.Item>
        </EverForm>
      </div>
    </EverModal>
  );
}
