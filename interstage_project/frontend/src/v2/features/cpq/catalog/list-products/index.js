import {
  LayersThreeIcon,
  Server01Icon,
  // Users02Icon,
} from "@everstage/evericons/duotone";
import { ChevronDownIcon, PlusSquareIcon } from "@everstage/evericons/outlined";
import { debounce } from "lodash";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useQuery } from "react-query";

import { EverButton, EverInput, EverTg, message } from "~/v2/components";
import { useFetchApiWithAuth } from "~/v2/features/cpq/hooks";
import {
  // profileAvatar1,
  // profileAvatar2,
  // profileAvatar3,
  // profileAvatar4,
  // profileAvatar5,
  manPlayingDarts,
  emptyBg,
} from "~/v2/images";

import ListProductsTable from "./components/ListProductsTable.js";
import { AgGridLoaderTable } from "../../components/ag-grid/AgGridLoaderTable.js";
import CustomDropDown from "../../components/CustomDropDown.js";
import { AddProductModal } from "../AddProductModal.js";
import BulkUploadCPQ from "../bulk-uploader-cpq";
import {
  BILLING_TYPES,
  CATEGORY_TYPES,
  productStatusMap,
  PRODUCT_TABLE_SKELETON_COLUMN_DEFS,
} from "../utils.js";

const fieldDefs = [
  {
    label: "Name",
    key: "name",
    required: true,
  },
  {
    label: "Code / SKU",
    key: "sku",
    required: true,
  },
  {
    label: "Billing Type",
    key: "billingType",
    required: true,
    type: "Dropdown",
    values: BILLING_TYPES,
  },
  {
    label: "Category",
    key: "category",
    required: true,
    type: "Dropdown",
    values: CATEGORY_TYPES,
  },
  {
    label: "Description",
    key: "description",
  },
  {
    label: "What do you charge this product on?",
    key: "chargeUnit",
  },
  {
    label: "Cost to build",
    key: "buildCost",
  },
];

const SAMPLE_TEMPLATE_DATA = [
  [
    "Macbook Pro",
    "MCBK-PRO",
    "Monthly",
    "Services",
    "This is the pro model",
    "Units",
    1000,
  ],
  [
    "Premiere Pro",
    "PRM-PRO",
    "Monthly",
    "Software",
    "This is the editing software",
    "Users",
    100,
  ],
];

function valdiateBulkUpload() {
  console.log("validateBulkUpload");
}

function importBulkUpload() {
  console.log("importBulkUpload");
}

export default function Catalog() {
  const { fetchData } = useFetchApiWithAuth();

  const [createProductModalVisible, setCreateProductModalVisible] =
    useState(false);
  const [bulkUploadVisible, setBulkUploadVisible] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [allProducts, setAllProducts] = useState(null);
  const [currentProducts, setCurrentProducts] = useState([]);
  const [statusFilter, setStatusFilter] = useState([]);
  const [categoryFilter, setCategoryFilter] = useState([]);
  // const [managedByFilter, setManagedByFilter] = useState([]);
  // const [addProductDropdownOpen, setAddProductDropdownOpen] = useState(false);

  const { refetch, isFetching } = useQuery(
    ["getProducts"],
    () => {
      return fetchData(`/ninja/cpq/product_catalog/products`, "GET");
    },
    {
      cacheTime: 0,
      refetchOnWindowFocus: false,
      retry: false,
      onError: (error) => {
        message.error(error?.message || "Something went wrong");
      },
      onSettled: (data) => {
        setCurrentProducts(data.products);
        setAllProducts(data.products);
      },
    }
  );

  // const addProductDropdownMenu = (
  //   <Menu className="w-72">
  //     <Menu.Item
  //       key="manual"
  //       className="!px-4 !py-2 !h-16"
  //       onClick={handleAddProductManually}
  //     >
  //       <div className="flex flex-col gap-1">
  //         <div className="flex gap-2 items-center">
  //           <PlusSquareIcon className="w-4 h-4 text-ever-base-content-mid" />
  //           <EverTg.SubHeading4 className="text-ever-base-content">
  //             Add Manually ✨
  //           </EverTg.SubHeading4>
  //         </div>
  //         <EverTg.Description>Create a product from scratch</EverTg.Description>
  //       </div>
  //     </Menu.Item>
  //     <Menu.Item
  //       key="bulk"
  //       className="!px-4 !py-2 !h-16"
  //       onClick={handleImportInBulk}
  //     >
  //       <div className="flex flex-col gap-1">
  //         <div className="flex gap-2 items-center">
  //           <Download04Icon className="w-4 h-4 stroke-[1.2] text-ever-base-content-mid" />
  //           <EverTg.SubHeading4 className="text-ever-base-content">
  //             Import Products ⚡️
  //           </EverTg.SubHeading4>
  //         </div>
  //         <EverTg.Description>
  //           Add products in bulk using a CSV file.
  //         </EverTg.Description>
  //       </div>
  //     </Menu.Item>
  //   </Menu>
  // );

  // const managedByOptions = useMemo(() => {
  //   const uniqueEmails = new Set();
  //   return allProducts
  //     ?.map((product) => ({
  //       avatar: product.managed_by.avatar,
  //       label: product.managed_by.name,
  //       value: product.managed_by.email,
  //     }))
  //     .filter((option) => {
  //       if (uniqueEmails.has(option.value)) {
  //         return false;
  //       }
  //       uniqueEmails.add(option.value);
  //       return true;
  //     });
  // }, [allProducts]);

  const productStatusOptions = useMemo(() => {
    const statusCount = { active: 0, inactive: 0 };
    if (allProducts)
      for (const product of allProducts) {
        const status = product.status;
        statusCount[status]++;
      }

    return Object.entries(statusCount).map(([status, count]) => ({
      label: productStatusMap(status),
      value: status,
      count: count,
    }));
  }, [allProducts]);

  const debouncedSearch = useMemo(
    () =>
      debounce((query) => {
        setSearchQuery(query);
      }, 500),
    []
  );

  const handleSearchChange = useCallback(
    (event) => {
      const query = event.target.value;
      const trimmedQuery = query.trim();

      setSearchInput(query);
      debouncedSearch(trimmedQuery);
    },
    [debouncedSearch]
  );

  function handleAddProductManually() {
    setCreateProductModalVisible(true);
    // setAddProductDropdownOpen(false);
  }

  function handleImportInBulk() {
    setBulkUploadVisible(true);
    // setAddProductDropdownOpen(false);
  }

  const applyFilters = useCallback(
    (products) => {
      return products
        ?.filter(
          (product) =>
            statusFilter.length === 0 || statusFilter.includes(product.status)
        )
        .filter(
          (product) =>
            categoryFilter.length === 0 ||
            categoryFilter.includes(product.category)
        )
        .filter((product) => {
          if (!searchQuery) return true;
          const lowerCaseQuery = searchQuery.toLowerCase();
          return (
            product.name.toLowerCase().includes(lowerCaseQuery) ||
            product.sku.toLowerCase().includes(lowerCaseQuery)
          );
        });
    },
    [statusFilter, categoryFilter, searchQuery]
  );

  const filterProducts = useCallback(() => {
    setCurrentProducts(applyFilters(allProducts));
  }, [allProducts, applyFilters]);

  useEffect(() => {
    filterProducts();
  }, [searchQuery, categoryFilter, statusFilter, filterProducts]);

  return (
    <div className="w-full h-full px-6 pt-3 pb-4">
      <div className="flex flex-col h-full w-full">
        {allProducts?.length > 0 && (
          <div className="flex pb-2 items-center justify-between">
            <div>
              <EverInput.Search
                className="w-80"
                placeholder="Search by Name or SKU"
                value={searchInput}
                onChange={handleSearchChange}
                size="small"
                disabled={isFetching}
              />
            </div>
            <div className="flex gap-3">
              <CustomDropDown
                multi
                statusCount
                setHandlerState={setStatusFilter}
                options={productStatusOptions}
                disabled={isFetching}
              >
                <EverButton
                  size="small"
                  type="ghost"
                  color="base"
                  prependIcon={
                    <Server01Icon className="w-4 h-4 text-ever-base-content-mid shrink-0" />
                  }
                  appendIcon={
                    <ChevronDownIcon className="w-4 h-4 text-ever-base-content-mid" />
                  }
                >
                  <EverTg.Text className="text-base-content font-normal">
                    Status
                  </EverTg.Text>
                </EverButton>
              </CustomDropDown>
              <CustomDropDown
                search
                multi
                setHandlerState={setCategoryFilter}
                searchPlaceholder="Search Category"
                options={CATEGORY_TYPES}
                disabled={isFetching}
              >
                <EverButton
                  size="small"
                  type="ghost"
                  color="base"
                  prependIcon={
                    <LayersThreeIcon className="!w-[18px] !h-[18px] text-ever-base-content-mid shrink-0" />
                  }
                  appendIcon={
                    <ChevronDownIcon className="w-4 h-4 text-ever-base-content-mid" />
                  }
                >
                  <EverTg.Text className="text-base-content font-normal">
                    Category
                  </EverTg.Text>
                </EverButton>
              </CustomDropDown>
              {/* <CustomDropDown
                search
                multi
                avatar
                setHandlerState={setManagedByFilter}
                searchPlaceholder="Search Managed By"
                options={managedByOptions}
                disabled={isFetching}
              >
                <EverButton
                  size="small"
                  type="ghost"
                  color="base"
                  prependIcon={
                    <Users02Icon className="!w-[18px] !h-[18px] text-ever-base-content-mid shrink-0" />
                  }
                  appendIcon={
                    <ChevronDownIcon className="w-4 h-4 text-ever-base-content-mid" />
                  }
                >
                  <EverTg.Text className="text-base-content font-normal">
                    Managed By
                  </EverTg.Text>
                </EverButton>
              </CustomDropDown> */}
              {/* <Dropdown
                overlay={addProductDropdownMenu}
                trigger={["click"]}
                visible={addProductDropdownOpen}
                onVisibleChange={() =>
                  setAddProductDropdownOpen(!addProductDropdownOpen)
                }
                disabled={isFetching}
              > */}
              <EverButton
                size="small"
                prependIcon={<PlusSquareIcon className="w-4 h-4" />}
                onClick={handleAddProductManually}
                // appendIcon={<ChevronDownIcon className="w-4 h-4" />}
              >
                Add Product
              </EverButton>
              {/* </Dropdown> */}
            </div>
          </div>
        )}
        <div className="flex flex-col h-full w-full">
          {isFetching || allProducts === null ? (
            <div className="flex justify-center items-center h-full">
              <AgGridLoaderTable
                columnDefs={PRODUCT_TABLE_SKELETON_COLUMN_DEFS}
                rowCount={6}
                agGridProps={{
                  rowHeight: 40,
                  headerHeight: 40,
                }}
              />
            </div>
          ) : allProducts?.length === 0 ? (
            <NoProductPresent
              handleAddProductManually={handleAddProductManually}
              handleImportInBulk={handleImportInBulk}
            />
          ) : (
            <ListProductsTable
              rowData={currentProducts}
              refetchProducts={refetch}
            />
          )}
        </div>
      </div>
      <AddProductModal
        visible={createProductModalVisible}
        setVisible={setCreateProductModalVisible}
        refetchProducts={refetch}
      />
      <BulkUploadCPQ
        title="Bulk Import"
        fieldDefs={fieldDefs}
        templateData={SAMPLE_TEMPLATE_DATA}
        validateDataService={valdiateBulkUpload}
        importDataService={importBulkUpload}
        isVisible={bulkUploadVisible}
        handleCloseUpload={() => setBulkUploadVisible(false)}
      />
    </div>
  );
}

function NoProductPresent({ handleAddProductManually }) {
  return (
    <div
      className="flex flex-col justify-center rounded-2xl items-center w-full h-full bg-cover bg-top"
      style={{ backgroundImage: `url(${emptyBg})` }}
    >
      <div className="flex flex-col items-center justify-center">
        <img src={manPlayingDarts} className="mb-8" />
        <div className="flex flex-col items-center gap-1.5 mb-6">
          <EverTg.Heading3>
            It&apos;s time add your first product ⚡️
          </EverTg.Heading3>
          <EverTg.Description className="text-center">
            Start building your catalog by configuring product details, pricing,
            and attributes to streamline your quoting process.
          </EverTg.Description>
        </div>
        <div className="flex gap-6">
          {/* <EverButton
            size="small"
            type="ghost"
            color="primary"
            prependIcon={<Download04Icon />}
            onClick={handleImportInBulk}
          >
            Import in bulk
          </EverButton> */}
          <EverButton
            size="small"
            color="primary"
            prependIcon={<PlusSquareIcon />}
            onClick={handleAddProductManually}
          >
            Add Product
          </EverButton>
        </div>
      </div>
    </div>
  );
}
