import React from "react";
import { useMutation } from "react-query";

import { capitalizeFirstLetter } from "~/Utils";
import { EverTg, message } from "~/v2/components";

import MenuButton from "./cell-renderers/MenuButton";
import ProductNameCellRenderer from "./cell-renderers/ProductNameCellRenderer";
import { StatusBadge, TruncatedText } from "../../../components";
// import OwnerCellRenderer from "../../../components/ag-grid/OwnerCellRenderer";
import { useFetchApiWithAuth } from "../../../hooks";
import { ProductTable } from "../../ProductTable";
import {
  getDisplayNameforKeys,
  PRODUCT_ACTIONS,
  PRODUCT_ACTIONS_SUCCESS_MESSAGES,
} from "../../utils";

export default function ListProductsTable({ rowData, refetchProducts }) {
  const { fetchData } = useFetchApiWithAuth();

  const modifyProductRequest = useMutation(
    ({ action, productId, payload = {}, method = "POST" }) =>
      fetchData(
        `/ninja/cpq/product_catalog/${action}/${productId}`,
        method,
        payload
      ),
    {
      onError: (error) => {
        console.log("error", error);
        message.error(error?.message || "Something went wrong");
      },
      onSuccess: (res, { action }) => {
        message.success(
          res?.message || PRODUCT_ACTIONS_SUCCESS_MESSAGES[action]
        );
        refetchProducts();
      },
    }
  );

  const columnDefs = [
    {
      field: "name",
      headerName: "Product Name",
      cellRenderer: ProductNameCellRenderer,
      cellRendererParams: (params) => ({
        productName: params?.data?.name,
        productId: params?.data?.id,
        description: params?.data?.description,
      }),
    },
    {
      field: "status",
      headerName: "Status",
      cellRenderer: StatusBadge,
      cellRendererParams: (params) => ({
        status: capitalizeFirstLetter(params?.data?.status),
      }),
    },
    {
      field: "category",
      headerName: "Category",
      cellRenderer: (params) => {
        return (
          <EverTg.Text>
            {getDisplayNameforKeys(params?.data?.category)}
          </EverTg.Text>
        );
      },
    },
    {
      field: "sku",
      headerName: "Code / SKU",
      cellRenderer: (params) => {
        return (
          <TruncatedText text={params?.data?.sku}>
            <EverTg.Text>
              {capitalizeFirstLetter(params?.data?.sku)}
            </EverTg.Text>
          </TruncatedText>
        );
      },
    },
    {
      field: "billingType",
      headerName: "Billing Type",
      cellRenderer: (params) => {
        return (
          <EverTg.Text>
            {capitalizeFirstLetter(
              getDisplayNameforKeys(params?.data?.billing_type)
            )}
          </EverTg.Text>
        );
      },
    },
    // {
    //   field: "managed_by",
    //   headerName: "Managed by",
    //   cellRenderer: OwnerCellRenderer,
    //   cellRendererParams: (params) => ({
    //     ownerId: params?.data?.managed_by?.name,
    //     src: params?.data?.managed_by?.avatar,
    //   }),
    // },
    {
      field: "charge_unit",
      headerName: "Charged On",
      maxWidth: 220,
      cellRenderer: (params) => {
        const unit = capitalizeFirstLetter(params?.data?.charge_unit);
        return (
          <TruncatedText text={unit}>
            <EverTg.Text>{unit ? unit : "-"}</EverTg.Text>
          </TruncatedText>
        );
      },
    },
    {
      field: "moreButton",
      headerName: "ACTIONS",
      cellClass: "flex justify-end pr-7",
      headerClass: "text-ever-base-content-mid",
      type: "rightAligned",
      maxWidth: 120,
      cellRenderer: MenuButton,
      cellRendererParams: (params) => ({
        productId: params?.data?.id,
        handleProductAction: handleProductAction,
        setIsHovered: (visible) => {
          params.api.applyTransaction({
            update: [{ ...params.data, isHovered: visible }],
          });
        },
        refetchProducts,
        productName: params?.data?.name,
      }),
    },
  ];

  function handleProductAction({ action, productId }) {
    switch (action) {
      case PRODUCT_ACTIONS.CLONE: {
        modifyProductRequest.mutate({
          action: PRODUCT_ACTIONS.CLONE,
          productId,
          method: "POST",
          payload: {
            clone_pricepoints: true,
            product_id: productId,
          },
        });
        break;
      }
      case PRODUCT_ACTIONS.DELETE: {
        modifyProductRequest.mutate({
          action: PRODUCT_ACTIONS.DELETE,
          productId,
          method: "DELETE",
        });
        break;
      }
    }
  }

  return (
    <ProductTable
      rowData={rowData}
      columnDefs={columnDefs}
      getRowId={(params) => params.data.id}
      autoSizeStrategy="fitGridWidth"
    />
  );
}
