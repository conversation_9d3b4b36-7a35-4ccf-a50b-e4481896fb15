import { File02Icon } from "@everstage/evericons/outlined";
import React from "react";
import { Link } from "react-router-dom";

import { EverTg, EverTooltip } from "~/v2/components";
import { TruncatedText } from "~/v2/features/cpq/components";

export default function ProductNameCellRenderer({
  productName,
  productId,
  description,
}) {
  return (
    <div className="flex gap-1 items-center w-full">
      <TruncatedText text={productName} className="max-w-[85%]">
        <EverTg.SubHeading4>
          <Link
            to={`/cpq/product-catalog/${productId}?name=${productName}`}
            className="!text-ever-base-content hover:underline
            hover:text-ever-base-content"
          >
            {productName}
          </Link>
        </EverTg.SubHeading4>
      </TruncatedText>
      {description && description.length > 0 && (
        <EverTooltip title={description}>
          <File02Icon className="text-ever-base-content-mid !h-4 !w-4" />
        </EverTooltip>
      )}
    </div>
  );
}
