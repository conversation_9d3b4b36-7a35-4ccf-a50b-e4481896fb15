import { AgGridReact } from "ag-grid-react";
import { useEffect, useMemo, useRef } from "react";
import { v4 as uuidv4 } from "uuid";

import { everAgGridOptions } from "~/v2/components/ag-grid";

import { useTierTableHandlers } from "../quotes/build-quote/components/section-fields/select-products/hooks";
import TierInteractionButton from "../quotes/build-quote/components/section-fields/select-products/price-point-drawer/TierInteractionButton";

const { getDefaultOptions } = everAgGridOptions;

const TierTable = ({
  tierData,
  onValueChange,
  columnDefs,
  instance,
  onCellEditingStarted = () => {},
  onCellEditingStopped = () => {},
}) => {
  const gridApiRef = useRef(null);

  const {
    rowPositions,
    rowHoveringId,
    updateRowPositions,
    updateCellValues,
    onGridReady,
    onCellMouseOver,
    onCellMouseOut,
    onMouseEnter,
    onMouseLeave,
    onModelUpdated,
    onAddTier,
    onRemoveTier,
  } = useTierTableHandlers({ onValueChange, instance });

  const tiers = useMemo(
    () =>
      (tierData || []).map((item) => ({
        ...item,
        id: item.id || uuidv4(),
      })),
    [tierData]
  );

  useEffect(() => {
    if (gridApiRef.current) {
      gridApiRef.current.applyTransaction({ update: tiers });
    }
  }, [tiers]);

  const defaultColDef = useMemo(() => {
    return {
      flex: 1,
      sortable: false,
      suppressHeaderMenuButton: true,
      filter: true,
      headerClass: "!px-4",
      cellClassRules: {
        "!px-2": (params) =>
          params.colDef.field !== "sn" && params.colDef.field !== "checkbox",
        "!border-0": () => true,
      },
      cellDataType: false,
    };
  }, []);

  const rowClassRules = useMemo(
    () => ({
      "!bg-ever-info-lite": (params) => params.node.data.isHovered,
    }),
    []
  );

  return (
    <div className="price-point-tier-table ag-theme-material rowHoverEnabled w-full gap-1 flex flex-col flex-auto">
      <AgGridReact
        {...getDefaultOptions({ type: "sm" })}
        getRowId={(params) => params.data.id}
        rowHeight={40}
        domLayout="autoHeight"
        columnDefs={columnDefs}
        defaultColDef={defaultColDef}
        rowClassRules={rowClassRules}
        rowData={tiers}
        onGridReady={(params) => {
          gridApiRef.current = params.api;
          onGridReady(params);
        }}
        suppressBrowserResizeObserver={true}
        enableRangeSelection={false}
        pagination={false}
        suppressCellFocus={true}
        suppressMovableColumns={true}
        suppressContextMenu={true}
        suppressRowClickSelection={true}
        suppressColumnVirtualisation={true}
        suppressRowVirtualisation={true}
        animateRows={true}
        onCellMouseOver={onCellMouseOver}
        onCellMouseOut={onCellMouseOut}
        onFirstDataRendered={updateRowPositions}
        onBodyScrollEnd={updateRowPositions}
        onModelUpdated={onModelUpdated}
        onCellValueChanged={updateCellValues}
        onCellEditingStarted={onCellEditingStarted}
        onCellEditingStopped={onCellEditingStopped}
      />
      <TierInteractionButton
        rowPositions={rowPositions}
        activeKey={rowHoveringId}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        onAddTier={onAddTier}
        onRemoveTier={onRemoveTier}
        instance={instance}
      />
    </div>
  );
};

export { TierTable };
