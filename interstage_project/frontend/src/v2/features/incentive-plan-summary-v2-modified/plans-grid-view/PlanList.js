import { CalendarIcon } from "@everstage/evericons/duotone";
import { observer } from "mobx-react";
import React, { useState, useMemo } from "react";
import { useMutation } from "react-query";

import { COMMISSION_TYPE, PLAN_STATUS, PLANTYPE } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import {
  EverCard,
  EverTg,
  EverGroupAvatar,
  showToastMessage,
  EverVirtualizerInfinite,
  EverLink,
  EverFormatter,
} from "~/v2/components";
import { ApprovalStatusTag } from "~/v2/features/commission-canvas-modified/reusable/ApprovalStatusTag";
import PlanTag from "~/v2/features/commission-canvas-modified/reusable/PlanTag";
import {
  PLAN_ACTION_MESSAGES,
  TOTAL_PAYEES_TO_SHOW_IN_GROUP_AVATAR_TAGS,
  TOTAL_PAYEES_TO_SHOW_IN_GROUP_AVATAR,
} from "~/v2/features/incentive-plan-summary-v2-modified/constants";
import {
  cloneCommissionPlan,
  cloneToForecast,
} from "~/v2/features/incentive-plan-summary-v2-modified/services/http";
import { useIncentivePlanSummaryStore } from "~/v2/features/incentive-plan-summary-v2-modified/store";
import { noData } from "~/v2/images";

import ContextMenuActions from "./ContextMenuActions";

const { SubHeading3 } = EverTg;

const RenderPlanCard = ({
  item,
  onCanvasOpenForEdit,
  onClonePlan,
  onDeletePlan,
  onCloneForecast,
  onSharePlan,
  planType: planTypeFromStore,
}) => {
  const {
    planId,
    planName,
    planStatus,
    planPeriod,
    planType,
    payeesInPlan,
    planApprovalDetails,
    totalPayeesInPlan,
  } = item || {};

  const payeesWithClassName = [
    ...payeesInPlan,
    ...Array(totalPayeesInPlan - payeesInPlan.length).fill({}),
  ].map((payee) => ({
    ...payee,
    className: "",
  }));

  return (
    <div className="px-2 py-2 grid">
      <EverCard interactive shadowSize="none" roundedSize="xl">
        <div className="relative">
          <ContextMenuActions
            item={item}
            onClonePlan={onClonePlan}
            onDeletePlan={onDeletePlan}
            onCloneForecast={onCloneForecast}
            onSharePlan={onSharePlan}
          />
          <div onClick={() => onCanvasOpenForEdit(planId)}>
            <div className="flex flex-col gap-1.5 h-20">
              <SubHeading3
                className="block mb-1 text-ever-base-content line-clamp-2 w-[calc(100%-2.5rem)]"
                title={planName}
              >
                {planName}
              </SubHeading3>

              <div className="flex items-center gap-1.5">
                <CalendarIcon className="w-4 h-4 text-ever-base-content-mid" />
                <EverFormatter.Date
                  className="leading-3 text-ever-base-content-mid"
                  date={planPeriod.from}
                />{" "}
                -{" "}
                <EverFormatter.Date
                  className="leading-3 text-ever-base-content-mid"
                  date={planPeriod.to}
                />
              </div>
            </div>

            <div className="flex items-end justify-between mt-4">
              <div className="flex gap-2">
                <PlanTag type={planStatus} />
                {planType === PLANTYPE.SPIFF ? <PlanTag type="SPIFF" /> : null}
                {planTypeFromStore === COMMISSION_TYPE.COMMISSION_PLAN ? (
                  <ApprovalStatusTag status={planApprovalDetails?.status} />
                ) : null}
              </div>
              <div className="flex items-end justify-between">
                <EverGroupAvatar
                  forceShowPopover={true}
                  size="medium"
                  groupMaxCount={TOTAL_PAYEES_TO_SHOW_IN_GROUP_AVATAR_TAGS}
                  limitInPopover={
                    TOTAL_PAYEES_TO_SHOW_IN_GROUP_AVATAR -
                    TOTAL_PAYEES_TO_SHOW_IN_GROUP_AVATAR_TAGS
                  }
                  avatars={payeesWithClassName}
                />
              </div>
            </div>
          </div>
        </div>
      </EverCard>
    </div>
  );
};

const PlanList = observer(
  ({
    allCommissionPlans,
    isLoading,
    fetchNextData,
    canFetchMore,
    initialLoad,
    onCanvasOpenForEdit,
    resetLazyComponent,
    refetchAllPlansFiscalYears,
    searchValue,
    isSearchInProgress,
    setPlanCount,
    planCountInFiscalYear,
    onSharePlan,
  }) => {
    const { urlPrefix, planType } = useIncentivePlanSummaryStore();
    const { accessToken } = useAuthStore();

    const [deletedItems, setDeletedItems] = useState([]);

    const clonePlan = useMutation(
      (payload) => cloneCommissionPlan(urlPrefix, payload, accessToken),
      {
        onSuccess: (response) => {
          const planId = response?.planId ?? null;
          if (planId) {
            onCanvasOpenForEdit(planId);
          }
          resetLazyComponent();
        },
      }
    );

    const cloneForecast = useMutation(
      (payload) =>
        cloneToForecast(
          { planId: payload["planId"] },
          accessToken,
          payload["cloneType"]
        ),
      {
        onSuccess: (response, variables) => {
          const { cloneType } = variables;
          const planId = response?.planId ?? null;
          if (cloneType === "clone" && planId) {
            onCanvasOpenForEdit(planId);
          }
          resetLazyComponent();
        },
      }
    );

    const onClonePlan = (planId, cloneWithPayees = true) => {
      const clonePromise = clonePlan.mutateAsync({ planId, cloneWithPayees });
      showToastMessage(clonePromise, {
        messages: PLAN_ACTION_MESSAGES["clone"],
      });
    };

    const updatePlanCount = (planId) => {
      const updatedPlanCount = planCountInFiscalYear;
      const planStatus = allCommissionPlans.find(
        (plan) => plan.planId === planId
      )?.planStatus;
      if (!planStatus) return;

      const decrementCountByStatus = {
        [PLAN_STATUS.DRAFT]: () => {
          updatedPlanCount.draftPlansCount -= 1;
        },
        [PLAN_STATUS.PUBLISHED]: () => {
          updatedPlanCount.publishedPlansCount -= 1;
        },
      };

      updatedPlanCount.allPlansCount -= 1;
      decrementCountByStatus[planStatus]?.();

      setPlanCount({ ...updatedPlanCount });
    };

    const onDeletePlan = (planId) => {
      refetchAllPlansFiscalYears();
      setDeletedItems([...deletedItems, planId]);
      updatePlanCount(planId);
    };

    const onCloneForecast = (planId, cloneCommissionPlan = true) => {
      const cloneType = cloneCommissionPlan ? "clone_commission_plan" : "clone";

      const clonePromise = cloneForecast.mutateAsync({ planId, cloneType });
      showToastMessage(clonePromise, {
        messages: {
          loading: "Cloning to forecast...",
          success: (data) => {
            return (
              <>
                {cloneType === "clone" ? (
                  `Plan cloned with name - ${data?.planName}`
                ) : (
                  <div className="flex flex-row gap-1">
                    {data?.planName} successfully added to Forecast.
                    <EverLink
                      href="/forecasts"
                      label="Take me there"
                      target="_blank"
                    />
                  </div>
                )}
              </>
            );
          },
          error: (errorMsg) => {
            return <>{errorMsg ?? "Unable to clone to forecast."}</>;
          },
        },
      });
    };

    const unDeletedCommissionPlans = useMemo(() => {
      return allCommissionPlans.filter(
        (plan) => !deletedItems.includes(plan.planId)
      );
    }, [allCommissionPlans, deletedItems]);

    return (
      <div className="h-full">
        {allCommissionPlans.length > 0 ? (
          <EverVirtualizerInfinite
            data={unDeletedCommissionPlans}
            RenderItem={RenderPlanCard}
            renderItemProps={{
              onCanvasOpenForEdit,
              onClonePlan,
              onDeletePlan,
              onCloneForecast,
              onSharePlan,
              planType,
            }}
            canFetchMore={canFetchMore}
            fetchNextData={fetchNextData}
            isLoading={isLoading}
            showSpinner={!isSearchInProgress}
            initialLoad={initialLoad}
            messageLastData=""
            animate
            itemHeight={176}
          />
        ) : isLoading ? null : (
          <div className="flex h-full w-full items-center justify-center">
            <div className="flex flex-col items-center justify-center">
              <img src={noData} className="w-52 h-52" />
              <EverTg.SubHeading4 className="text-ever-base-content">
                {searchValue.length === 0
                  ? "Nothing to show"
                  : "No matches found"}
              </EverTg.SubHeading4>
            </div>
          </div>
        )}
      </div>
    );
  }
);

export default PlanList;
