import {
  ChevronDownIcon,
  ChevronRightIcon,
} from "@everstage/evericons/outlined";
import moment from "moment";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { twMerge } from "tailwind-merge";

import { formatDate } from "~/Utils/DateUtils";
import {
  EverButton,
  EverGroupAvatar,
  EverLoader,
  EverTg,
  EverTooltip,
} from "~/v2/components";

const DEFAULT_CELL_PADDING = 23;
const ICON_WIDTH = 20;
const ICON_MARGIN = 8;
const ICON_TOTAL_WIDTH = ICON_WIDTH + ICON_MARGIN;
const SUB_LEVEL_LEFT_POS = 32;
const LETTER_WIDTH = 8;
const getLeftPosByLevel = (level) => level * SUB_LEVEL_LEFT_POS;

const ViewMoreButton = (props) => {
  const { onClick, text = "View More" } = props;
  const [loading, setLoading] = useState(false);

  const handleClick = () => {
    if (!loading) {
      setLoading(true);
      onClick();
    }
  };

  return (
    <EverButton
      type="link"
      className="text-xs !px-0.5 flex-row-reverse gap-3"
      loading={loading}
      {...(!loading && {
        appendIcon: <ChevronDownIcon className="w-4 h-4" />,
      })}
      onClick={handleClick}
    >
      {text}
    </EverButton>
  );
};

export const NameCellComponent = (props) => {
  const { value, searchValue, data, onClick = null } = props;
  const searchMathIndex = value
    .toLowerCase()
    .indexOf(searchValue.toLowerCase());
  let title = value;

  if (searchMathIndex > -1) {
    const beforeStr = value.substring(0, searchMathIndex);
    const midStr = value.substring(
      searchMathIndex,
      searchMathIndex + searchValue.length
    );
    const afterStr = value.substring(searchMathIndex + searchValue.length);
    title = (
      <>
        {beforeStr}
        <span className="text-ever-primary">{midStr}</span>
        {afterStr}
      </>
    );
  }

  return (
    <div
      key={data.key}
      className="flex items-center overflow-hidden text-ellipsis cursor-pointer w-full"
      onClick={onClick}
    >
      <EverGroupAvatar
        avatars={[
          {
            image: data.profilePicture,
            name: value,
          },
        ]}
      />
      <span className="mx-2 overflow-hidden text-ellipsis" title={value}>
        {title}
      </span>
      <ChevronRightIcon className="w-5 h-5 text-ever-base-content-mid shrink-0 ml-auto" />
    </div>
  );
};

export const GroupCellRenderer = (props) => {
  const ref = useRef(null);
  const {
    api,
    node,
    value,
    data,
    onViewMore,
    disabledKeys,
    updateWidthList,
    reporteeByManagerLoading,
  } = props;

  const [expanded, setExpanded] = useState(node.expanded);

  useEffect(() => {
    const expandListener = (event) => {
      const isExpanded = event.node.expanded;
      setExpanded(isExpanded);
      if (!isExpanded) {
        updateWidthList(data.key, { collapsed: true });
      }
    };

    const parentKey = node.parent.key || node.rowIndex;
    const width =
      ref?.current?.scrollWidth +
        getLeftPosByLevel(node.level) +
        ICON_TOTAL_WIDTH +
        50 ||
      getLeftPosByLevel(node.level) +
        ICON_TOTAL_WIDTH +
        value.length * LETTER_WIDTH +
        78;
    updateWidthList(parentKey, { width });

    node.addEventListener("expandedChanged", expandListener);

    return () => {
      node.removeEventListener("expandedChanged", expandListener);
    };
  }, []);

  const onClick = useCallback(() => {
    api.setRowNodeExpanded(node, !node.expanded);
  }, [node]);

  const renderValue = (data) => {
    const exited = data.exitDate ? moment.utc().isAfter(data.exitDate) : false;
    if (data.hasMoreData) {
      return <ViewMoreButton ref={ref} onClick={() => onViewMore(data)} />;
    }
    return (
      <div ref={ref} className="flex items-center gap-2 w-full min-w-0">
        <EverGroupAvatar
          size="medium"
          avatars={[
            {
              image: data.profilePicture,
              name: data.name,
            },
          ]}
        />
        <span className="min-w-0" title={value}>
          {exited ? (
            <EverTooltip
              placement="right"
              title={`Exited on ${formatDate(data.exitDate)}`}
              mouseEnterDelay={0.4}
            >
              <EverTg.Heading4
                className={twMerge(
                  "truncate",
                  exited ? "text-ever-error" : "text-ever-base-content-mid"
                )}
              >
                {value}
              </EverTg.Heading4>
            </EverTooltip>
          ) : (
            <EverTg.Heading4 className="truncate">{value}</EverTg.Heading4>
          )}
        </span>
      </div>
    );
  };

  return (
    <div
      className={twMerge(
        "flex w-full h-full",
        reporteeByManagerLoading && "cursor-not-allowed"
      )}
    >
      <div
        style={{
          paddingLeft: `${getLeftPosByLevel(node.level)}px`,
        }}
        className={twMerge(
          "flex items-center min-w-0",
          reporteeByManagerLoading && "pointer-events-none",
          node.data.group
            ? disabledKeys.includes(data.key)
              ? "cursor-not-allowed"
              : "cursor-pointer"
            : "cursor-default"
        )}
        onClick={
          node.data.group && !disabledKeys.includes(data.key) ? onClick : null
        }
      >
        {node.data.group ? (
          <div
            className={twMerge(
              "mr-2 w-5 h-5 flex items-center justify-center text-ever-base-content",
              disabledKeys.includes(data.key) &&
                "opacity-50 cursor-not-allowed pointer-events-none"
            )}
          >
            {expanded ? (
              <ChevronDownIcon className="w-5 h-5 text-ever-base-content-low" />
            ) : (
              <ChevronRightIcon className="w-5 h-5 text-ever-base-content-low" />
            )}
          </div>
        ) : (
          <div className="mr-2 w-5 h-5" />
        )}
        {renderValue(data)}
      </div>
    </div>
  );
};

export const LoadingCellComponent = (props) => {
  return props.node.level > 0 ? (
    <EverLoader.SpinnerLottie
      style={{
        marginLeft: `${
          getLeftPosByLevel(props.node.level) + DEFAULT_CELL_PADDING
        }px`,
      }}
      className="w-6 h-6"
    />
  ) : null;
};

export const LoadingOverlayComponent = ({ loading }) => {
  return (
    <div className="w-60 h-44 flex items-center justify-center">
      <EverLoader
        size={12}
        indicatorType="spinner"
        tip={loading ? "Generating reporting based teams" : ""}
      />
    </div>
  );
};
