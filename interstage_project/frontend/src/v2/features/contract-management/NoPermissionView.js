import { RefreshCwIcon } from "@everstage/evericons/outlined";
import React from "react";
import { useSetRecoilState } from "recoil";

import { contentLoaderAtom } from "~/GlobalStores/atoms";
import { EverTg, EverButton } from "~/v2/components";

import NoPermissionImage from "./images/NoPermission.svg?react";
import { PermissionDetailsModal } from "./PermissionDetailsModal";

const renderNoPermissionImage = () => {
  return <NoPermissionImage className="w-48" />;
};
/**
 * @typedef {Object} NoPermissionViewProps
 * @property {Object} userPermissions - The current user's DocuSign permissions.
 * @property {Object} accountPermissions - The DocuSign account-level permissions.
 * @property {boolean} hasAllPermissions - Whether the user has all required permissions to manage contracts.
 * @property {number} missingUserPermissionsCount - Number of missing user-level permissions.
 * @property {number} missingAccountPermissionsCount - Number of missing account-level permissions.
 * @property {Function} refetchUserPermissions - Callback to refetch the user's permissions from the server.
 * @property {Function} openDocusign - Callback to open the DocuSign configuration page in a new tab.
 * @property {boolean} open - Whether the permissions modal is open.
 * @property {Function} setOpen - Callback to set the open state of the permissions modal.
 */

/**
 * NoPermissionView Component
 *
 * This component displays a view for users who lack the required DocuSign permissions to manage contracts.
 * It provides guidance on which permissions are missing, how to resolve them, and allows users to refresh permissions or navigate to DocuSign.
 *
 * @param {NoPermissionViewProps} props
 */
export function NoPermissionView({
  userPermissions, // User-level DocuSign permissions
  accountPermissions, // Account-level DocuSign permissions
  hasAllPermissions, // Boolean: all required permissions present
  missingUserPermissionsCount, // Count of missing user permissions
  missingAccountPermissionsCount, // Count of missing account permissions
  refetchUserPermissions, // Function to refetch permissions
  openDocusign, // Function to open DocuSign
  open,
  setOpen,
}) {
  // Set content loader state using Recoil for global loading indication
  const setContentLoader = useSetRecoilState(contentLoaderAtom);
  // Main render block
  return (
    <>
      {/* Main container for the no-permission view, centered vertically and horizontally */}
      <div className="flex flex-col items-center justify-center h-full gap-6">
        {/* Illustration for no permission state */}
        {renderNoPermissionImage()}
        {/* Heading and description for user guidance */}
        <div className="flex flex-col gap-2 items-center">
          <EverTg.Heading2 className="text-ever-base-content">
            Check your DocuSign permissions to continue
          </EverTg.Heading2>
          <EverTg.Caption className="text-ever-base-content-mid">
            To create and manage contracts, the right DocuSign permissions must
            be enabled. If you&apos;ve just updated them, click refresh to
            proceed.
          </EverTg.Caption>
        </div>
        {/* Action buttons: Refresh and View Permissions */}
        <div className="flex gap-4">
          {/* Refresh button triggers loader and permission refetch */}
          <EverButton
            type="ghost"
            color="base"
            className="gap-2"
            onClick={() => {
              setContentLoader({ showLoader: true }); // Show loader
              refetchUserPermissions(); // Refetch permissions
              setOpen(false);
            }}
          >
            <RefreshCwIcon className="w-4 h-4 text-ever-base-content-mid" />
            Refresh
          </EverButton>
          {/* Button to open the permissions modal */}
          <EverButton onClick={() => setOpen(true)}>
            View Permissions
          </EverButton>
        </div>
        <PermissionDetailsModal
          open={open}
          setOpen={setOpen}
          userPermissions={userPermissions}
          accountPermissions={accountPermissions}
          hasAllPermissions={hasAllPermissions}
          missingUserPermissionsCount={missingUserPermissionsCount}
          missingAccountPermissionsCount={missingAccountPermissionsCount}
          refetchUserPermissions={refetchUserPermissions}
          openDocusign={openDocusign}
        />
      </div>
    </>
  );
}
