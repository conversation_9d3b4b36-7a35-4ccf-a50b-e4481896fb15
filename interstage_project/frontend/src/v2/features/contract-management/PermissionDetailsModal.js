import { RefreshCwIcon, LinkExternalIcon } from "@everstage/evericons/outlined";
import { AlertCircleIcon, CheckCircleIcon } from "@everstage/evericons/solid";
import React from "react";
import { useSetRecoilState } from "recoil";

import { contentLoaderAtom } from "~/GlobalStores/atoms";
import { EverTg, EverButton, EverModal, EverDivider } from "~/v2/components";

export function PermissionDetailsModal({
  open,
  setOpen,
  userPermissions,
  accountPermissions,
  hasAllPermissions,
  missingUserPermissionsCount,
  missingAccountPermissionsCount,
  refetchUserPermissions,
  openDocusign,
}) {
  // Set content loader state using Recoil for global loading indication
  const setContentLoader = useSetRecoilState(contentLoaderAtom);
  /**
   * Renders an icon based on whether a permission is enabled or not.
   * @param {boolean} enabled - Whether the permission is enabled.
   * @returns {JSX.Element} - The corresponding icon.
   */
  const renderIcon = (enabled) => {
    if (enabled) {
      // Show check icon for enabled permissions
      return <CheckCircleIcon className="w-4 h-4 text-ever-success shrink-0" />;
    }
    // Show alert icon for missing permissions
    return <AlertCircleIcon className="w-4 h-4 text-ever-warning shrink-0" />;
  };
  {
    /* Modal to show detailed permission requirements and guidance */
  }
  return (
    <EverModal
      visible={open}
      onCancel={() => setOpen(false)}
      title={
        <div className="flex flex-col gap-1 border-b border-ever-base-300 pb-2">
          {/* Modal title and subtitle */}
          <EverTg.Heading3>
            Configure DocuSign to manage contracts{" "}
          </EverTg.Heading3>
          <EverTg.Caption className="text-ever-base-content-mid">
            These settings must be configured in your DocuSign account to manage
            contracts
          </EverTg.Caption>
        </div>
      }
      footer={
        <div className="flex justify-end gap-2">
          {/* Refresh button in modal footer, also closes modal */}
          <EverButton
            type="ghost"
            color="base"
            className="gap-2"
            onClick={() => {
              setContentLoader({ showLoader: true });
              refetchUserPermissions();
              setOpen(false);
            }}
          >
            <RefreshCwIcon className="w-4 h-4 text-ever-base-content-mid" />
            Refresh
          </EverButton>
          {/* Button to open DocuSign in a new tab */}
          <EverButton onClick={openDocusign}>
            <LinkExternalIcon className="w-4 h-4 text-ever-base" />
            Go to DocuSign
          </EverButton>
        </div>
      }
    >
      <div className="flex flex-col gap-6">
        {/* Show missing permissions warning if not all permissions are present */}
        {!hasAllPermissions && (
          <div className="bg-ever-info-lite rounded-lg px-4 py-3 border border-ever-base-100">
            {/* Display count of missing settings and warning icon */}
            {missingUserPermissionsCount + missingAccountPermissionsCount} out
            of 8 settings marked with a (
            <span className="inline-flex items-center align-middle">
              <AlertCircleIcon className="w-4 h-4 text-ever-warning shrink-0" />
            </span>
            ) need attention. Please review and update them in your DocuSign
            account to proceed.
          </div>
        )}
        {/* DocuSign pricing plan info section */}
        <div className="flex flex-col gap-1">
          <EverTg.Heading3>Docusign pricing plan</EverTg.Heading3>
          <EverTg.Text>
            Ensure you opt for the Business Pro plan or higher in DocuSign to
            access the Bulk Send feature.
          </EverTg.Text>
        </div>
        <EverDivider />
        {/* User permission profile requirements section */}
        <div className="flex flex-col gap-3">
          <div className="flex flex-col gap-1">
            <EverTg.Heading3>Permission profiles</EverTg.Heading3>
            <EverTg.Text>
              Manage permissions for your DocuSign user under User and groups
              Permission profile. In the Permissions profiles section, go to
              User Permissions for the respective DocuSign user.
            </EverTg.Text>
          </div>
          {/* List of user-level permissions with status icons */}
          <div className="flex flex-col gap-3">
            {/* Each permission row shows an icon and description */}
            <div className="flex items-center gap-2">
              {renderIcon(userPermissions?.canSendEnvelope === "true")}
              <EverTg.Text>
                Enable{" "}
                <EverTg.SubHeading4 className="text-ever-base-content">
                  Allow users to send envelopes
                </EverTg.SubHeading4>{" "}
              </EverTg.Text>
            </div>
            <div className="flex items-center gap-2">
              {renderIcon(userPermissions?.bulkSend === "true")}
              <EverTg.Text>
                Enable{" "}
                <EverTg.SubHeading4 className="text-ever-base-content">
                  Allow sending to bulk list
                </EverTg.SubHeading4>
              </EverTg.Text>
            </div>
            <div className="flex items-center gap-2">
              {renderIcon(userPermissions?.disableDocumentUpload === "false")}
              <EverTg.Text>
                Uncheck{" "}
                <EverTg.SubHeading4 className="text-ever-base-content">
                  Disable document upload option
                </EverTg.SubHeading4>{" "}
              </EverTg.Text>
            </div>
            <div className="flex items-center gap-2">
              {renderIcon(
                userPermissions?.allowRecipientLanguageSelection === "true"
              )}
              <EverTg.Text>
                Enable{" "}
                <EverTg.SubHeading4 className="text-ever-base-content">
                  Allow language selection
                </EverTg.SubHeading4>
              </EverTg.Text>
            </div>
          </div>
        </div>
        <EverDivider />
        {/* Template section permission */}
        <div className="flex flex-col gap-4">
          <EverTg.Heading3>Templates section</EverTg.Heading3>
          <div className="flex items-center gap-2">
            {renderIcon(userPermissions?.canManageTemplates === "share")}
            <EverTg.Text>
              Choose{" "}
              <EverTg.SubHeading4 className="text-ever-base-content">
                Share (Users can create, use and share templates).
              </EverTg.SubHeading4>{" "}
            </EverTg.Text>
          </div>
        </div>
        <EverDivider />
        {/* Account-level sending settings section */}
        <div className="flex flex-col gap-4">
          <EverTg.Heading3>Sending settings</EverTg.Heading3>
          <div className="flex flex-col gap-3">
            <div className="flex items-center gap-2">
              {renderIcon(accountPermissions?.enableBulkRecipients === "true")}
              <EverTg.Text>
                Under the{" "}
                <EverTg.SubHeading4 className="text-ever-base-content">
                  Recipient roles{" "}
                </EverTg.SubHeading4>{" "}
                section, select the{" "}
                <EverTg.SubHeading4 className="text-ever-base-content">
                  Enable bulk recipients{" "}
                </EverTg.SubHeading4>
                option
              </EverTg.Text>
            </div>
            <div className="flex items-center gap-2">
              {renderIcon(accountPermissions?.setRecipEmailLang === "true")}
              <EverTg.Text>
                Under the{" "}
                <EverTg.SubHeading4 className="text-ever-base-content">
                  Fields and properties section{" "}
                </EverTg.SubHeading4>
                , select the{" "}
                <EverTg.SubHeading4 className="text-ever-base-content">
                  Enable custom email and language for each recipient{" "}
                </EverTg.SubHeading4>
                option.
              </EverTg.Text>
            </div>
          </div>
        </div>
        <EverDivider />
        {/* Reminders and expiration settings section */}
        <div className="flex flex-col gap-4">
          <EverTg.Heading3>Reminders and Expiration</EverTg.Heading3>
          <div className="flex items-center gap-2">
            {renderIcon(accountPermissions?.userOverrideEnabled === "true")}
            <EverTg.Text>
              Under the{" "}
              <EverTg.SubHeading4 className="text-ever-base-content">
                Reminders and expiration{" "}
              </EverTg.SubHeading4>
              section, select the{" "}
              <EverTg.SubHeading4 className="text-ever-base-content">
                Allow senders to override account defaults
              </EverTg.SubHeading4>{" "}
              option
            </EverTg.Text>
          </div>
        </div>
      </div>
    </EverModal>
  );
}
