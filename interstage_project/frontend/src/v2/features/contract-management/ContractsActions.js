import {
  DotsVerticalIcon,
  FileCheck02Icon,
  FolderCheckIcon,
} from "@everstage/evericons/outlined";
import { Menu, Dropdown } from "antd";
import { observer } from "mobx-react";
import React from "react";

import { archiveContract<PERSON>pi, activateContract<PERSON>pi } from "~/Api/ContractService";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { EverButton, EverTooltip, message } from "~/v2/components";

const ContractsActions = observer(
  ({ contract, activateContract, archiveContract, showAsDropdown = true }) => {
    const { accessToken } = useAuthStore();

    const onArchive = () => {
      const data = {
        templateId: contract.template_id,
      };
      archiveContractApi(data, accessToken).then((response) => {
        if (response.ok) {
          message.success("Moved to archive!");
          archiveContract(contract.template_id);
        } else {
          message.error("Failed to archive!");
        }
      });
    };

    const onActive = () => {
      const data = {
        templateId: contract.template_id,
      };
      activateContractApi(data, accessToken).then((response) => {
        if (response.ok) {
          message.success("Moved to active!");
          activateContract(contract.template_id);
        } else {
          message.error("Failed to active!");
        }
      });
    };

    const menu = (
      <Menu>
        {!contract.is_archived && (
          <Menu.Item
            key="archive"
            icon={
              <FolderCheckIcon className="w-5 h-5 text-ever-base-content-mid" />
            }
            /** @param {any} e */
            onClick={(e) => {
              e.domEvent.stopPropagation();
              onArchive();
            }}
          >
            <span className="text-ever-base-content">Archive</span>
          </Menu.Item>
        )}
        {contract.is_archived && (
          <Menu.Item
            key="active"
            icon={
              <FileCheck02Icon className="w-5 h-5 text-ever-base-content-mid" />
            }
            /** @param {any} e */
            onClick={(e) => {
              e.domEvent.stopPropagation();
              onActive();
            }}
          >
            <span className="text-ever-base-content">Move to active</span>
          </Menu.Item>
        )}
      </Menu>
    );

    return (
      <div className="context-menu">
        {showAsDropdown ? (
          <div className="absolute -right-1 -top-1">
            <Dropdown
              trigger={["click"]}
              overlay={menu}
              overlayStyle={{
                minWidth: 120,
              }}
              /** @param {import('react').MouseEvent} e */
              onClick={(e) => {
                // Prevent the click event from bubbling up to the parent Link component
                e.stopPropagation();
              }}
            >
              <EverButton.Icon
                size="small"
                color="base"
                type="text"
                icon={
                  <DotsVerticalIcon className="text-ever-base-content-mid" />
                }
                /** @param {import('react').MouseEvent} e */
                onClick={(e) => {
                  // Prevent the click event from bubbling up to the parent Link component
                  e.stopPropagation();
                }}
              />
            </Dropdown>
          </div>
        ) : (
          <div className="flex gap-3 items-center justify-end pr-10">
            {!contract.is_archived && (
              <EverTooltip title="Archive">
                <FolderCheckIcon
                  className="w-5 h-5 cursor-pointer"
                  key="archive"
                  /** @param {import('react').MouseEvent} e */
                  onClick={(e) => {
                    e.stopPropagation();
                    onArchive();
                  }}
                />
              </EverTooltip>
            )}
            {contract.is_archived && (
              <EverTooltip title="Move to active">
                <FileCheck02Icon
                  className="w-5 h-5 cursor-pointer"
                  key="active"
                  /** @param {import('react').MouseEvent} e */
                  onClick={(e) => {
                    e.stopPropagation();
                    onActive();
                  }}
                />
              </EverTooltip>
            )}
          </div>
        )}
      </div>
    );
  }
);

export default ContractsActions;
