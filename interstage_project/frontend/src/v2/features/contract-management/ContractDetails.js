import { useQuery, gql } from "@apollo/client";
import { FilterFunnelIcon } from "@everstage/evericons/duotone";
import {
  AlignTopArrowIcon,
  EditPencilIcon,
} from "@everstage/evericons/outlined";
import {
  AlertOctagonIcon,
  CheckCircleIcon,
  HourglassIcon,
  XCircleIcon,
} from "@everstage/evericons/solid";
import { Table, Popover } from "antd";
import { cloneDeep, isEmpty } from "lodash";
import { observer, useLocalStore } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useQuery as useReactQuery } from "react-query";
import { useParams, useLocation } from "react-router-dom";
import { useSetRecoilState, useRecoilValue } from "recoil";

import {
  docusignOAuth,
  exportEnvelopesStatusAsCSV,
  getUserPermission,
} from "~/Api/DocusignService";
import {
  breadcrumbAtom,
  contentLoader<PERSON>tom,
  myClientAtom,
  navPortalAtom,
} from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useContractsStore } from "~/GlobalStores/ContractsStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { permissionsCount } from "~/Utils/contracts";
import { formatDateDDMMMYYYY } from "~/Utils/DateUtils";
import {
  EverBadge,
  EverBreadcrumbPortal,
  EverButton,
  EverCheckbox,
  EverNewDatePicker,
  EverDivider,
  EverInput,
  EverLabel,
  EverLoader,
  EverNavPortal,
  EverTg,
  message,
  EverFormatter,
  EverTooltip,
} from "~/v2/components";
import {
  formatDate,
  convertUtcToAnyTimeZone,
} from "~/v2/components/ever-formatter/EverFormatter";
import { dogTearingNewspaper } from "~/v2/images";

import CreateContractDrawer from "./create-contract-drawer";
import CreateContractStore from "./create-contract-drawer/store";
import EnvelopeActions from "./EnvelopeActions";
import { PermissionDetailsModal } from "./PermissionDetailsModal";
import secure from "./SecureComponent";
import SendEnvelopeButton from "./send-envelope-button";

const ContractDeatils = observer(() => {
  const { contractId } = useParams();
  const { email, accessToken } = useAuthStore();

  const [envelopeDetails, setEnvelopeDetails] = useState([]);
  const [showFiltersMenu, setShowFiltersMenu] = useState(false);
  const [filteredEnvelopeDetails, setFilteredEnvelopeDetails] = useState([]);
  const [fromDateFilter, setFromDateFilter] = useState();
  const [toDateFilter, setToDateFilter] = useState();
  const [searchText, setSearchText] = useState("");
  const [filters, setFilters] = useState([]);
  const [showEditContractView, setShowEditContractView] = useState(false);
  const [initialiseData, setInitialiseData] = useState(true);

  const setBreadcrumbName = useSetRecoilState(breadcrumbAtom);
  const navPortalLocation = useRecoilValue(navPortalAtom);
  const setContentLoader = useSetRecoilState(contentLoaderAtom);
  const contentLoader = useRecoilValue(contentLoaderAtom);
  const location = useLocation();
  const { contract_name } = location.state ? location.state : {};
  const [open, setOpen] = useState(false);

  const {
    contracts,
    updateContract,
    addToContracts,
    hasAllPermissions,
    loading: getAllTemplatesLoading,
    missingUserPermissionsCount,
    missingAccountPermissionsCount,
    userPermissions,
    accountPermissions,
    setUserPermissions,
    setAccountPermissions,
  } = useContractsStore();

  const [contractDetails, setContractDetails] = useState(
    contracts.filter((contract) => contract.template_id == contractId).pop()
  );

  const store = useLocalStore(() => new CreateContractStore());
  const { updateLocalStoreForContract, setContractAction, templateName } =
    store;

  const [isTooltipsOpen, setIsTooltipsOpen] = useState(false);

  // Recoil atom value for client configuration and settings
  const myAtom = useRecoilValue(myClientAtom);

  // Extract feature flags and settings from client configuration
  const clientFeatures = getClientFeatures(myAtom);

  // Feature flag for enabling contract permissions, defaults to false
  const enableContractPermissions =
    clientFeatures?.enableContractPermissions || false;

  const GET_TEMPLATE_DETAILS = gql`
    query TemplateDetails(
      $emailId: String!
      $templateId: String!
      $include: String
    ) {
      templateDetails(
        emailId: $emailId
        templateId: $templateId
        include: $include
      )
    }
  `;

  // Destructure loading state and refetch function from useQuery hook
  const {
    isLoading: isLoadingUserPermissions,
    isFetching: isFetchingUserPermissions,
    refetch: refetchUserPermissions,
  } = useReactQuery(
    // Query key for caching
    ["get_user_permission_for_contract_details"],
    // Query function that calls API to get user permissions
    () => {
      return getUserPermission(accessToken, email);
    },
    {
      retry: false,
      cacheTime: 0,
      refetchInterval: 600000,
      refetchOnWindowFocus: false,
      // Only run query if contract permissions are enabled
      enabled: enableContractPermissions,
      // Success callback when permissions are fetched
      onSuccess: (data) => {
        // Destructure account and user permissions from response
        const { accountPermissions, userPermissions } = data?.data || {};
        // Update permission states with fetched data
        setAccountPermissions(accountPermissions || {});
        setUserPermissions(userPermissions || {});
        // Calculate missing permissions counts
        const { missingAccountPermissionsCount, missingUserPermissionsCount } =
          permissionsCount({ accountPermissions, userPermissions });

        // If content loader is showing
        if (contentLoader.showLoader) {
          // Hide the loader
          setContentLoader({ showLoader: false });
          // If no permissions are missing
          if (
            missingAccountPermissionsCount === 0 &&
            missingUserPermissionsCount === 0
          ) {
            // Show success message
            message.success(
              "All permissions are set. You can now create contracts."
            );
          } else {
            // Show warning if permissions are missing
            message.warning(
              "Some permissions are still missing in your DocuSign account. Please review and try again.."
            );
          }
        }
      },
      // Error callback for failed requests
      onError: (error) => {
        // If content loader is showing
        if (contentLoader.showLoader) {
          // Hide the loader
          setContentLoader({ showLoader: false });
        }
        // Show error message with error details if available
        message.error(
          error?.message ||
            "We couldn’t verify your permissions right now. Please try again."
        );
      },
    }
  );

  const { loading, data, refetch } = useQuery(GET_TEMPLATE_DETAILS, {
    fetchPolicy: getAllTemplatesLoading ? "cache-only" : "cache-first",
    variables: {
      emailId: email,
      templateId: contractId,
      include:
        "[documents,lock_information,envelopes_details,template_details]",
    },
    skip: !contracts.find((contract) => contract.template_id === contractId),
  });

  useEffect(() => {
    if (
      !getAllTemplatesLoading &&
      isEmpty(
        contracts.filter((contract) => contract.template_id == contractId).pop()
      )
    ) {
      addToContracts(contractId, "[template_details,envelopes_details]");
    }
  }, [getAllTemplatesLoading]);

  const setFilterOption = (checked, option) => {
    let _filters = [...filters];
    if (checked) {
      if (option == "all") {
        _filters = ["all", "sent", "completed", "declined", "voided"];
      } else {
        _filters.push(option);
        if (_filters.length == 4) {
          _filters.push("all");
        }
      }
    } else {
      if (option == "all") {
        _filters = [];
      } else {
        _filters = _filters.filter((filter) => filter != option);
        _filters = _filters.filter((filter) => filter != "all");
      }
    }
    setFilters(_filters);
  };

  useEffect(() => {
    let _envelopeDetails = [...envelopeDetails];
    if (!isEmpty(searchText)) {
      _envelopeDetails = envelopeDetails.filter((record) => {
        return (
          record.recipients.filter((recipient) =>
            recipient["name"].toLowerCase().includes(searchText)
          ).length > 0 || record["sender"].toLowerCase().includes(searchText)
        );
      });
    }
    setFilteredEnvelopeDetails(_envelopeDetails);
  }, [searchText]);

  useEffect(() => {
    if (data?.templateDetails) {
      const contractDetails = JSON.parse(data["templateDetails"]);
      updateContract(contractDetails);
    }
  }, [data]);

  useEffect(() => {
    setContractDetails(
      contracts.filter((contract) => contract.template_id == contractId).pop()
    );
  }, [contracts]);

  const updateContractDetails = () => {
    const envelopeDetails = contractDetails["envelopes_for_template"];
    envelopeDetails?.forEach((envelope) => {
      if (!envelope["email_subject"]) {
        envelope["email_subject"] = `Please DocuSign: ${templateName}`;
      }
      envelope["key"] = envelope["envelope_id"];
    });
    setEnvelopeDetails(envelopeDetails);
    setFilteredEnvelopeDetails(envelopeDetails);
    setBreadcrumbName(contractDetails.template_name);
    updateLocalStoreForContract(contractDetails);
    setInitialiseData(false);
  };

  useEffect(() => {
    if (contractDetails && !getAllTemplatesLoading) {
      updateContractDetails();
    }
  }, [contractDetails, getAllTemplatesLoading]);

  useEffect(() => {
    if (contractDetails && !getAllTemplatesLoading && initialiseData) {
      updateContractDetails();
    }
  }, [initialiseData]);

  useEffect(() => {
    const { template_name } = contractDetails || {};
    setBreadcrumbName([
      {
        index: 0,
        name: "Contracts",
        title: "Contracts",
        url: "/contracts",
      },
      {
        index: 1,
        name: contract_name ?? template_name,
        title: contract_name ?? template_name,
      },
    ]);
  }, [contractDetails]);

  const onApply = () => {
    let _envelopeDetails = [...envelopeDetails];
    let selectedFilters = cloneDeep(filters);
    if (selectedFilters.includes("all") || filters.length == 0) {
      _envelopeDetails = envelopeDetails;
    } else {
      if (selectedFilters.includes("sent")) {
        selectedFilters.push("delivered", "created");
      }
      _envelopeDetails = _envelopeDetails.filter((_envelopeDetail) =>
        selectedFilters.includes(_envelopeDetail.status)
      );
    }
    if (fromDateFilter) {
      _envelopeDetails = _envelopeDetails.filter(
        (_envelopeDetail) =>
          formatDateDDMMMYYYY(_envelopeDetail.sent_date_time) >=
          formatDateDDMMMYYYY(fromDateFilter)
      );
    }
    if (toDateFilter) {
      _envelopeDetails = _envelopeDetails.filter(
        (_envelopeDetail) =>
          formatDateDDMMMYYYY(_envelopeDetail.sent_date_time) <=
          formatDateDDMMMYYYY(toDateFilter)
      );
    }
    setFilteredEnvelopeDetails(_envelopeDetails);
    setShowFiltersMenu(false);
  };

  const getStatus = (record) => {
    if (record.status == "completed") {
      return (
        <EverBadge
          type="success"
          className="bg-ever-success-lite"
          title="Completed"
          icon={<CheckCircleIcon className="text-ever-success w-3.5 h-3.5" />}
        />
      );
    }
    if (record.status == "voided") {
      return (
        <EverBadge
          type="error"
          title="Voided"
          icon={<AlertOctagonIcon className="text-ever-error w-3.5 h-3.5" />}
        />
      );
    }

    if (["sent", "delivered", "created"].includes(record.status)) {
      if (
        record.recipients.filter((r) => r.status == "autoresponded").length > 0
      ) {
        return (
          <Popover
            trigger="hover"
            placement="bottom"
            content={
              <div className="flex flex-col gap-4">
                {record.recipients
                  .filter((r) => r.status == "autoresponded")
                  .map((r, index) => {
                    return (
                      <div className="flex flex-col" key={index}>
                        <EverTg.Text className="text-ever-base-content">
                          Email undelivered
                        </EverTg.Text>
                        <EverTg.Text className="text-ever-base-content">
                          {r.name}
                        </EverTg.Text>
                        <EverTg.Text className="text-ever-base-content">
                          Sent on {""}
                          {r.sent_at &&
                            formatDate({
                              date: convertUtcToAnyTimeZone(r.sent_at),
                            })}
                        </EverTg.Text>
                      </div>
                    );
                  })}
              </div>
            }
          >
            <div className="w-fit cursor-pointer">
              <EverBadge
                type="error"
                title="Failed"
                icon={<XCircleIcon className="text-ever-error w-3.5 h-3.5" />}
              />
            </div>
          </Popover>
        );
      } else {
        return (
          <Popover
            trigger="hover"
            placement="bottom"
            content={
              <div className="flex flex-col gap-4">
                {record.recipients
                  .filter((r) => r.status == "sent")
                  .map((r, index) => {
                    return (
                      <div className="flex flex-col" key={index}>
                        <EverTg.Text className="text-ever-base-content">
                          Awaiting
                        </EverTg.Text>
                        <EverTg.Text className="text-ever-base-content">
                          {r.name}
                        </EverTg.Text>
                        <EverTg.Text className="text-ever-base-content">
                          Sent on{" "}
                          {r.sent_at &&
                            formatDate({
                              date: convertUtcToAnyTimeZone(r.sent_at),
                            })}
                        </EverTg.Text>
                      </div>
                    );
                  })}
              </div>
            }
          >
            <div className="w-fit cursor-pointer">
              <EverBadge
                type="warning"
                title={`Awaiting (${record["completed"]} /
            ${record["awaiting"] + record["completed"]} done)`}
                icon={
                  <HourglassIcon className="text-ever-warning w-3.5 h-3.5" />
                }
              />
            </div>
          </Popover>
        );
      }
    }
    if (record.status == "declined") {
      return (
        <EverBadge
          type="error"
          title="Declined"
          icon={<XCircleIcon className="text-ever-error w-3.5 h-3.5" />}
        />
      );
    }
    if (record.status == "deleted") {
      return (
        <EverBadge
          type="error"
          title="Deleted"
          icon={<XCircleIcon className="text-ever-error w-3.5 h-3.5" />}
        />
      );
    }
    return null;
  };

  const onClear = () => {
    setFilters([]);
    setFromDateFilter(null);
    setToDateFilter(null);
    setFilteredEnvelopeDetails(envelopeDetails);
  };

  const onEditContract = () => {
    setShowEditContractView(true);
    setContractAction("Update Contract");
  };

  /**
   * Opens DocuSign OAuth flow in a new tab
   *
   * This function initiates the DocuSign OAuth authentication process by:
   * 1. Making an API call to get the DocuSign authorization URL using the current access token
   * 2. Extracting the origin URL from the response
   * 3. Opening the DocuSign login page in a new browser tab
   *
   * @throws {Error} If the API call fails or response is invalid
   */
  const openDocusign = () => {
    docusignOAuth(accessToken)
      .then((response) => {
        if (response.ok) return response.text();
        else throw new Error("Error occurred");
      })
      .then((data) => {
        const url = new URL(data.slice(1, -1)).origin || "";
        window.open(url, "_blank").focus();
      })
      .catch((error) => {
        console.log(error.message);
      });
  };

  const columns = [
    {
      title: "Subject",
      dataIndex: "subject",
      key: "subject",
      width: "25%",
      render: (_, record) => {
        return (
          <div className="flex flex-col">
            <div>
              <EverTg.Text>{record.email_subject}</EverTg.Text>
            </div>
            <EverTg.Description>
              <span>
                To:{" "}
                {record.recipients.map((recipient, index) => {
                  if (index > 0) return `, ${recipient["name"]}`;
                  return recipient["name"];
                })}
              </span>
            </EverTg.Description>
          </div>
        );
      },
    },
    {
      title: "Sent By",
      dataIndex: "sentBy",
      key: "sentBy",
      render: (text, record) => {
        return <EverTg.Text>{record.sender}</EverTg.Text>;
      },
    },
    {
      title: "Sent On",
      dataIndex: "sentOn",
      key: "sentOn",
      render: (text, record) => {
        if (record.sent_date_time) {
          return (
            <EverFormatter.DateTime
              date={record?.sent_date_time}
              targetTimeZone={"local"}
              className=" font-normal"
            />
          );
        }
        return "";
      },
    },
    {
      title: "Last Updated On",
      dataIndex: "lastUpadtedOn",
      key: "lastUpadtedOn",
      render: (_, record) => {
        if (record.last_modified_date_time) {
          return (
            <EverFormatter.DateTime
              date={record?.last_modified_date_time}
              targetTimeZone={"local"}
              className=" font-normal"
            />
          );
        }
        return "";
      },
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (_, record) => {
        return getStatus(record);
      },
    },
    {
      title: <div className="flex items-center justify-end pr-10">Actions</div>,
      dataIndex: "actions",
      key: "actions",
      width: "15%",
      render: (_, record) => {
        return <EnvelopeActions envelope={record} templateId={contractId} />;
      },
    },
  ];
  const menu = (
    <div className="w-80">
      <EverLabel className="text-xs font-semibold">Status</EverLabel>
      <div className="mt-4">
        <div className="mb-5">
          <EverCheckbox
            checked={filters.filter((fil) => fil == "all").length == 1}
            onChange={(e) => setFilterOption(e.target.checked, "all")}
          >
            All
          </EverCheckbox>
        </div>
        <div className="my-5">
          <EverCheckbox
            checked={filters.filter((fil) => fil == "sent").length == 1}
            onChange={(e) => setFilterOption(e.target.checked, "sent")}
          >
            In Progress
          </EverCheckbox>
        </div>
        <div className="my-5">
          <EverCheckbox
            checked={filters.filter((fil) => fil == "completed").length == 1}
            onChange={(e) => setFilterOption(e.target.checked, "completed")}
          >
            Completed
          </EverCheckbox>
        </div>
        <div className="my-5">
          <EverCheckbox
            checked={filters.filter((fil) => fil == "declined").length == 1}
            onChange={(e) => setFilterOption(e.target.checked, "declined")}
          >
            Declined
          </EverCheckbox>
        </div>
        <div className="my-5">
          <EverCheckbox
            checked={filters.filter((fil) => fil == "voided").length == 1}
            onChange={(e) => setFilterOption(e.target.checked, "voided")}
          >
            Voided
          </EverCheckbox>
        </div>
      </div>
      <EverDivider type="horizontal" />
      <div className="mt-4">
        <EverLabel className="text-xs font-semibold">Sent Date</EverLabel>
        <div className="flex items-center justify-between py-4">
          <div className="flex flex-col gap-2">
            <EverLabel>From</EverLabel>
            <EverNewDatePicker
              value={fromDateFilter}
              onChange={(date) => setFromDateFilter(date)}
              className="w-36"
              disabledDate={(current) => current > toDateFilter}
            />
          </div>
          <div className="flex flex-col gap-2">
            <EverLabel>To</EverLabel>
            <EverNewDatePicker
              value={toDateFilter}
              onChange={(date) => setToDateFilter(date)}
              className="w-36"
              disabledDate={(current) => current < fromDateFilter}
            />
          </div>
        </div>
      </div>
      <EverDivider className="my-3 -mx-4 w-auto" />
      <div className="flex items-center justify-between">
        <div>
          <EverButton size="small" onClick={onClear} type="link" color="base">
            Clear
          </EverButton>
        </div>
        <div className="flex items-center gap-2">
          <EverButton
            size="small"
            onClick={() => setShowFiltersMenu(false)}
            color="base"
            type="ghost"
          >
            Cancel
          </EverButton>
          <EverButton size="small" onClick={() => onApply()}>
            Apply
          </EverButton>
        </div>
      </div>
    </div>
  );

  const handleExportEnvelopesStatus = () => {
    return new Promise((settled) => {
      const requestData = {
        emailId: email,
        templateId: contractId,
        templateName: templateName || contractId,
      };
      exportEnvelopesStatusAsCSV(accessToken, requestData)
        .then((response) => {
          if (response.ok) {
            return response;
          } else {
            throw new Error("Request failed");
          }
        })
        .then((response) => response.blob())
        .then((blobby) => {
          let objectUrl = window.URL.createObjectURL(blobby);
          let anchor = document.createElement("a");
          anchor.href = objectUrl;
          anchor.download = `${
            contractDetails?.template_name || contractId
          }.csv`;
          anchor.click();

          window.URL.revokeObjectURL(objectUrl);
          message.success("Envelopes status exported successfully");
        })
        .catch((error) => {
          message.error("Error while exporting envelopes status!");
          console.log(error.message);
        })
        .finally(() => {
          settled();
        });
    });
  };

  return (
    <>
      <CreateContractDrawer
        showdrawer={showEditContractView}
        setShowdrawer={setShowEditContractView}
        store={store}
        resetData={() => setInitialiseData(true)}
        refetchTemplateDetails={refetch}
      />
      {!loading && !getAllTemplatesLoading && !contractDetails ? (
        <></>
      ) : (
        <>
          {loading ||
          getAllTemplatesLoading ||
          ((isLoadingUserPermissions || isFetchingUserPermissions) &&
            !contentLoader.showLoader) ? (
            <EverLoader indicatorType="spinner" />
          ) : (
            <>
              <EverBreadcrumbPortal dividerIcon={<></>}>
                <div className="flex gap-2 items-center ml-4">
                  <EverBadge
                    icon={<CheckCircleIcon className="text-ever-success" />}
                    className="bg-ever-success-lite text-ever-success-lite-content"
                    title={`Signed: ${contractDetails["completed"]}`}
                  ></EverBadge>
                  <EverBadge
                    icon={<HourglassIcon className="text-ever-info" />}
                    className="bg-ever-info-lite text-ever-info-lite-content"
                    title={`Awaiting: ${contractDetails["awaiting"]}`}
                  ></EverBadge>
                </div>
              </EverBreadcrumbPortal>

              <EverNavPortal target={navPortalLocation}>
                <div className="flex items-center justify-between">
                  <EverInput.Search
                    size="small"
                    placeholder="Search by name"
                    className="w-48"
                    value={searchText}
                    onChange={(e) =>
                      setSearchText(e.target.value.toLowerCase())
                    }
                  />

                  <div className="flex items-center gap-3">
                    <Popover
                      visible={showFiltersMenu}
                      content={menu}
                      title={null}
                      trigger="click"
                      placement="bottomLeft"
                      onVisibleChange={(visible) => setShowFiltersMenu(visible)}
                    >
                      <div>
                        <EverTooltip title="Filters">
                          <EverButton.Icon
                            size="small"
                            icon={
                              <FilterFunnelIcon className="size-4 text-ever-base-content-mid" />
                            }
                            color="base"
                            type="ghost"
                          />
                        </EverTooltip>
                      </div>
                    </Popover>
                    <EverTooltip title="Export">
                      <EverButton.Icon
                        size="small"
                        onClick={handleExportEnvelopesStatus}
                        icon={
                          <AlignTopArrowIcon className="size-4 text-ever-base-content-mid" />
                        }
                        type="ghost"
                        color="base"
                      />
                    </EverTooltip>
                    <EverTooltip
                      title={
                        !hasAllPermissions && enableContractPermissions
                          ? isTooltipsOpen && (
                              <p>
                                You don&apos;t have necessary{" "}
                                <span
                                  className="text-ever-base underline font-medium cursor-pointer"
                                  onClick={() => {
                                    setOpen(true);
                                    setIsTooltipsOpen(false);
                                  }}
                                >
                                  DocuSign permissions
                                </span>{" "}
                                to edit contracts.Enable them to proceed.
                              </p>
                            )
                          : "Edit Contract"
                      }
                    >
                      <div onMouseEnter={() => setIsTooltipsOpen(true)}>
                        <EverButton.Icon
                          size="small"
                          onClick={() => onEditContract()}
                          icon={
                            <EditPencilIcon className="size-4 text-ever-base-content-mid" />
                          }
                          color="base"
                          disabled={
                            !hasAllPermissions && enableContractPermissions
                          }
                          type="ghost"
                        />
                      </div>
                    </EverTooltip>
                    <SendEnvelopeButton
                      templateId={contractId}
                      templateName={templateName}
                    />
                  </div>
                </div>
              </EverNavPortal>
              {!isEmpty(filteredEnvelopeDetails) ? (
                <Table
                  className="py-4"
                  dataSource={filteredEnvelopeDetails}
                  columns={columns}
                  size="small"
                  pagination={false}
                />
              ) : (
                <div className="flex flex-col items-center justify-center w-full h-full -translate-y-10">
                  <img src={dogTearingNewspaper} className="w-56" />
                  <EverTg.Heading2>No envelopes found</EverTg.Heading2>
                </div>
              )}
              <PermissionDetailsModal
                open={open}
                setOpen={setOpen}
                missingUserPermissionsCount={missingUserPermissionsCount}
                missingAccountPermissionsCount={missingAccountPermissionsCount}
                hasAllPermissions={hasAllPermissions}
                refetchUserPermissions={refetchUserPermissions}
                openDocusign={openDocusign}
                userPermissions={userPermissions}
                accountPermissions={accountPermissions}
              />
            </>
          )}
        </>
      )}
    </>
  );
});

export default secure(ContractDeatils);
