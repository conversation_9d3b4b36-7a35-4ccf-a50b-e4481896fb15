/**
 * Slack notification configuration component for workflow builder
 * Allows users to configure Slack notifications with various options like message content,
 * recipients, and test functionality
 */

import {
  AlertTriangleIcon,
  InfoCircleIcon,
} from "@everstage/evericons/outlined";
import { get, isEqual, isEmpty, cloneDeep } from "lodash";
import { observer } from "mobx-react";
import { useState, useMemo, useEffect } from "react";

import { useDSVariableStore } from "~/GlobalStores/DSVariableStore";
import {
  EverButton,
  EverForm,
  EverLabel,
  EverTg,
  EverTooltip,
} from "~/v2/components";

import CustomColumns from "./email-slack-components/ColumnConfig";
import {
  MultiTabDropdownSelectForm,
  ModeSection,
} from "./email-slack-components/CommonComponents";
import CustomEditor from "./email-slack-components/custom-ck-editor";
import TestEmailModal from "./email-slack-components/TestEmailModal";
import useCustomContext from "./email-slack-components/useCustomContext";
import { ACTION_TYPE_ICONS, NOTIFICATION_TYPES } from "../../constants";
import { useWorkflowBuilder } from "../../hooks";
import { Header, ContentWrapper, Footer } from "../PanelComponents";

/**
 * Main Slack component that renders the configuration form
 * @param {Object} props - Component props
 * @param {Object} props.activeNode - Current active node in workflow
 * @param {Function} props.handleActionClear - Function to clear action
 * @param {Function} props.setNodeError - Function to set node error state
 */
const Slack = observer(({ activeNode, handleActionClear, setNodeError }) => {
  // Get workflow builder utilities
  const {
    getNodes,
    updateActionNode,
    isEditMode,
    updateConfigPanelState,
    validateErrors,
    updateWorkflowValidation,
  } = useWorkflowBuilder();
  const disableFields = !isEditMode;

  // Extract configuration from active node
  const config = useMemo(
    () => get(activeNode, "data.componentParams.action.params", {}),
    [activeNode.id]
  );

  // Get databook and datasheet IDs from workflow
  const { databookId, datasheetId } = useMemo(
    () => get(getNodes(), "0.data.componentParams", {}),
    [getNodes]
  );

  // Prepare datasheet IDs for variable store
  const datasheetIds = useMemo(
    () => (datasheetId ? [datasheetId] : []),
    [datasheetId]
  );

  // Initialize datasheet variable store
  const dsVariableStore = useDSVariableStore(datasheetIds);

  // State for notification name
  const [name, setName] = useState(
    get(activeNode, "data.componentParams.name", "")
  );

  const [formErrors, setFormErrors] = useState({});

  const {
    form,
    isLoading,
    isContextLoading,
    editorKey,
    editorContext,
    initialValues,
    allColumns,
    columnVisible,
    showTestEmailModal,
    slackStatus,
    testAction,
    setIsDisabled,
    isConsolidated,
    isTestButtonDisabled,
    setColumnVisible,
    setShowTestEmailModal,
    updateFormValue,
    onFormValuesChange,
    processConfigUpdate,
    getDropdownProps,
  } = useCustomContext({
    id: activeNode.id,
    notificationType: NOTIFICATION_TYPES.SLACK,
    config,
    databookId,
    datasheetId,
    dsVariableStore,
  });

  // Update block validation on component mount
  useEffect(() => {
    updateBlockValidation();
  }, []);

  /**
   * Updates the validation state of the block based on form values
   * Handles validation for Slack-specific fields
   */
  const updateBlockValidation = () => {
    const values = form.getFieldsValue();

    if (
      !isEmpty(values?.to?.toEmails) ||
      !isEmpty(values?.to?.toUserGroups) ||
      !isEmpty(values?.to?.toDsCols) ||
      !isEmpty(values?.to?.slackChannels)
    ) {
      let newValidationError = cloneDeep(validateErrors);
      if (newValidationError[activeNode.id]) {
        delete newValidationError[activeNode.id];
        //
        updateWorkflowValidation({
          validateErrors: {
            ...newValidationError,
          },
        });
        setNodeError((prevState) => {
          const { [activeNode.id]: _, ...newState } = prevState; // Destructure to exclude the key
          return newState;
        });
      } else {
        setNodeError((prevState) => {
          const { [activeNode.id]: _, ...newState } = prevState; // Destructure to exclude the key
          return newState;
        });
      }
    } else {
      setNodeError({
        [activeNode.id]: {
          message: "Then block is incomplete",
          isValid: false,
        },
      });
    }
  };

  /**
   * Handles name change for the notification
   * @param {string} newName - The new name for the notification
   */
  const handleNameChange = (newName) => {
    setIsDisabled(false);
    setName(newName);
    updateActionNode(processConfigUpdate, newName);
  };

  /**
   * Handles saving the action by validating the form
   */
  const handleActionSave = async () => {
    const columnConfig = form.getFieldValue("columnConfig") || [];
    const hasSelectedColumns = columnConfig.some((col) => col.isSelected);
    let currentErrors = { ...formErrors };
    if (!hasSelectedColumns) {
      currentErrors = {
        ...currentErrors,
        columnConfig: ["Please select at least one column"],
      };
      setFormErrors(currentErrors);
    } else {
      delete currentErrors.columnConfig;
      setFormErrors(currentErrors);
    }

    form
      .validateFields()
      .then(() => {
        if (!formErrors) {
          updateConfigPanelState((prevState) => ({
            ...prevState,
            panelExpanded: !prevState.panelExpanded,
          }));
        }
      })
      .catch(({ errorFields }) => {
        const errors = { ...currentErrors };
        errorFields.forEach((field) => {
          errors[field.name[0]] = field.errors;
        });
        setFormErrors(errors);
      });
  };

  return (
    <>
      <TestEmailModal
        showTestEmailModal={showTestEmailModal}
        setShowTestEmailModal={setShowTestEmailModal}
        testAction={testAction}
        type={NOTIFICATION_TYPES.SLACK}
      />
      <Header
        defaultName={name}
        icon={
          <div className="flex items-center p-2 bg-[#EFECFB] rounded-lg">
            {ACTION_TYPE_ICONS["slack"]}
          </div>
        }
        onChangeCbk={handleNameChange}
        editable={slackStatus}
      />

      <EverForm
        form={form}
        layout="vertical"
        className="custom-period-form flex flex-col h-full overflow-auto"
        initialValues={initialValues}
        onValuesChange={async (changedValues, allValues) => {
          onFormValuesChange(changedValues, allValues);
          await updateActionNode(processConfigUpdate, name);
          updateBlockValidation();

          // Remove form errors for changed fields
          if (Object.keys(changedValues).some((key) => formErrors[key])) {
            const newErrors = { ...formErrors };
            Object.keys(changedValues).forEach((key) => {
              delete newErrors[key];
            });
            setFormErrors(newErrors);
          }
        }}
      >
        <EverForm.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            !isEqual(prevValues.columnConfig, currentValues.columnConfig) ||
            prevValues.attachmentFilename !== currentValues.attachmentFilename
          }
        >
          {({ getFieldValue }) => (
            <CustomColumns
              visible={columnVisible}
              setVisible={setColumnVisible}
              isRequired={isConsolidated()}
              datasheetId={datasheetId}
              dsVariableStore={dsVariableStore}
              isLoading={isLoading}
              columnConfig={getFieldValue("columnConfig") || []}
              setColumnConfig={(value, skipButtonEnable = false) =>
                updateFormValue("columnConfig", value, skipButtonEnable)
              }
              disabled={disableFields}
              onFormValuesChange={onFormValuesChange}
              updateActionNode={updateActionNode}
              processConfigUpdate={processConfigUpdate}
              name={name}
              form={form}
              formErrors={formErrors}
              setFormErrors={setFormErrors}
            />
          )}
        </EverForm.Item>
        <ContentWrapper>
          {/* Display error message if Slack is not configured */}
          {!slackStatus && (
            <div className="flex items-center gap-2 bg-ever-error-lite/80 rounded px-4 py-2">
              <div className="flex items-center">
                <AlertTriangleIcon className="!w-6 !h-6 text-ever-error" />
              </div>
              <EverTg.Text className="text-ever-error text-xs">
                Slack is not configured for you or there is an issue with the
                configuration. Please contact your administrator.
              </EverTg.Text>
            </div>
          )}
          <ModeSection
            isLoading={isLoading}
            allColumns={allColumns}
            disabled={disableFields}
            notificationType={NOTIFICATION_TYPES.SLACK}
            toggleColumnVisibility={() => setColumnVisible(!columnVisible)}
          />
          <MultiTabDropdownSelectForm
            label="To"
            disabled={disableFields}
            {...getDropdownProps("to", true)}
          />
          <div>
            <div className="flex items-center mb-2">
              <EverLabel required>Message Content</EverLabel>
              <EverTooltip title="Maximum length is 3000 characters. Excess characters will be truncated">
                <InfoCircleIcon className="w-4 h-4 text-ever-base-content-mid cursor-help" />
              </EverTooltip>
            </div>
            <EverForm.Item
              name="message"
              rules={[
                {
                  required: true,
                  message: "Message content is required",
                },
              ]}
              noStyle
            >
              <CustomEditor
                key={editorKey}
                type={NOTIFICATION_TYPES.SLACK}
                isLoading={isContextLoading}
                smartContext={editorContext}
                disabled={disableFields}
              />
            </EverForm.Item>
            {formErrors.message && (
              <div className="text-ever-error text-sm mt-1">
                {formErrors.message[0]}
              </div>
            )}
          </div>
        </ContentWrapper>
        <EverForm.Item noStyle shouldUpdate>
          {() => (
            <>
              {!disableFields && (
                <Footer
                  onBack={handleActionClear}
                  onNext={handleActionSave}
                  nextBtnProps={{
                    title: slackStatus ? "" : "Slack is not configured",
                    className: "m-0",
                  }}
                  nextBtnText="Next"
                >
                  <EverTooltip
                    title={
                      slackStatus
                        ? isTestButtonDisabled()
                          ? "Fill all mandatory fields"
                          : ""
                        : "Slack is not configured"
                    }
                  >
                    <div className="ml-auto">
                      <EverButton
                        type="ghost"
                        color="primary"
                        className="bg-ever-base"
                        onClick={() => setShowTestEmailModal(true)}
                        disabled={!slackStatus || isTestButtonDisabled()}
                      >
                        Test Slack Message
                      </EverButton>
                    </div>
                  </EverTooltip>
                </Footer>
              )}
            </>
          )}
        </EverForm.Item>
      </EverForm>
    </>
  );
});

export default Slack;
