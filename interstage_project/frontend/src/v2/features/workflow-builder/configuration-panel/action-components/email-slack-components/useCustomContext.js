import { useQuery } from "@apollo/client";
import { HtmlToMarkdown } from "@ckeditor/ckeditor5-markdown-gfm/src/html2markdown/html2markdown";
import { LockIcon, HashIcon, MailIcon } from "@everstage/evericons/outlined";
import { UsersIcon } from "@everstage/evericons/solid";
import { isEmpty } from "lodash";
import { useState, useMemo, useRef, useEffect } from "react";
import { useMutation, useQuery as useReactQuery } from "react-query";

import { TRIGGER_TYPES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { EverForm, message } from "~/v2/components";

import { LABEL, FetchUsers } from "./CommonComponents";
import { EMAIL_TYPE, NOTIFICATION_TYPES } from "../../../constants";
import { GET_USER_GROUPS_DATA } from "../../../services/graphql";
import {
  getSmartContext,
  getSlackChannels,
  sendTestEmail,
} from "../../../services/restAPI";
import { flatten, getContextValues } from "../../../utils";
import {
  TABLE_PLACEHOLDER,
  TABLE_VALUE,
} from "../email-slack-components/custom-ck-editor/ckEditorUtils";

// Helper function to get field mappings based on notification type and field name
// This function returns a mapping of field types (user, userGroup, datasheet, channels) to their corresponding form field names
// The mapping varies based on the notification type (email, Slack, in-app) and the field name (to, cc, bcc, replyTo)
const getFieldsByName = (name, notificationType) => {
  switch (name) {
    case "to": {
      return {
        user: "toEmails",
        userGroup: "toUserGroups",
        datasheet: "toDsCols",
        ...(notificationType === NOTIFICATION_TYPES.SLACK
          ? { channels: "slackChannels" }
          : {}),
      };
    }
    case "cc": {
      return {
        user: "ccEmails",
        userGroup: "ccUserGroups",
        datasheet: "ccDsCols",
      };
    }
    case "bcc": {
      return {
        user: "bccEmails",
        userGroup: "bccUserGroups",
        datasheet: "bccDsCols",
      };
    }
    case "replyTo": {
      return {
        user: "replyToEmails",
      };
    }
    default: {
      return {};
    }
  }
};

/**
 * Custom hook for managing notification configuration state and operations
 * Handles email, Slack, and in-app notifications with various modes and configurations
 *
 * The hook manages:
 * - Form state and validation
 * - Context data for templates
 * - Recipient selection (users, groups, datasheet fields)
 * - Notification content (subject, message)
 * - Test functionality
 * - Mode-specific behavior (individual, consolidated, group)
 *
 * @param {Object} params - Configuration parameters
 * @param {string} params.id - Unique identifier for the notification
 * @param {string} params.notificationType - Type of notification (email, Slack, in-app)
 * @param {Object} params.config - Current notification configuration
 * @param {string} params.databookId - ID of the associated databook
 * @param {string} params.datasheetId - ID of the associated datasheet
 * @param {Object} params.dsVariableStore - Store containing datasheet variables
 * @param {Object[]} params.dsVariableStore.variables - Array of datasheet variables
 * @param {boolean} params.dsVariableStore.loading - Loading state of datasheet variables
 *
 * @returns {Object} State and operations for notification configuration
 */
const useCustomContext = ({
  id,
  notificationType,
  config,
  databookId,
  datasheetId,
  dsVariableStore: { variables, loading },
}) => {
  // State initialization
  // initial ref tracks if this is the first render to prevent unnecessary form updates
  const initial = useRef(true);
  const [form] = EverForm.useForm();
  const { email, accessToken } = useAuthStore();
  const entity = TRIGGER_TYPES.DATASHEET_DATA;

  // Editor and context state
  // editorKey ensures editor re-renders when mode or datasheet changes
  const [editorKey, setEditorKey] = useState("");
  // editorContext stores available variables for template
  const [editorContext, setEditorContext] = useState([]);
  // contextColMap maps column names to their metadata
  const [contextColMap, setContextColMap] = useState({});
  // columnVisible controls visibility of column selection UI
  const [columnVisible, setColumnVisible] = useState(false);
  // showTestEmailModal controls test email modal visibility
  const [showTestEmailModal, setShowTestEmailModal] = useState(false);
  // emailOptions stores available email fields for selection
  const [emailOptions, setEmailOptions] = useState([]);
  // allColumns stores all available columns for selection
  const [allColumns, setAllColumns] = useState([]);
  // contextData stores both smart and consolidated context
  const [contextData, setContextData] = useState({
    smartContext: [],
    consolidatedContext: [],
  });
  // isContextLoading tracks loading state of context data
  const [isContextLoading, setContextLoading] = useState(!!datasheetId);
  // slackStatus tracks if Slack integration is available
  const [slackStatus, setSlackStatus] = useState(true);
  // isDisabled controls form submission button state
  const [isDisabled, setIsDisabled] = useState(!isEmpty(config));

  // Helper function to check if notification is in consolidated mode
  // Consolidated mode means all recipients get the same email with data
  const isConsolidated = (mode = form.getFieldValue("mode")) =>
    mode === EMAIL_TYPE.CONSOLIDATED || mode === EMAIL_TYPE.CONSOLIDATED_GROUP;

  // Fetch user groups data for recipient selection
  const { data: userGroupData, loading: isUserGroupsLoading } = useQuery(
    GET_USER_GROUPS_DATA,
    {
      fetchPolicy: "network-only", // Always fetch fresh data
    }
  );

  // Memoize user groups to prevent unnecessary re-renders
  const userGroups = useMemo(
    () => userGroupData?.userGroups || [],
    [userGroupData]
  );

  // Fetch smart context data for the datasheet
  // Smart context provides available variables for templates
  const { isLoading: isSmartContextLoading } = useReactQuery(
    ["getSmartContext", datasheetId, entity],
    () => getSmartContext(datasheetId, entity, accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      enabled: !loading && !!datasheetId && !!entity,
      onSuccess: (data) => setContext(data),
      onError: () => setContextLoading(false),
    }
  );

  // Fetch Slack channels if notification type is Slack
  // Only enabled when notification type is Slack
  const { data: slackChannelOptions, isLoading: isSlackChannelLoading } =
    useReactQuery(
      ["getSlackChannels", email],
      () => getSlackChannels(accessToken, email),
      {
        retry: false,
        cacheTime: 0,
        refetchOnWindowFocus: false,
        enabled: notificationType === NOTIFICATION_TYPES.SLACK,
        onSuccess: () => setSlackStatus(true),
        onError: () => setSlackStatus(false),
      }
    );

  // Mutation for sending test emails
  // Handles success and error cases with appropriate messages
  const testEmail = useMutation(
    (testConfig) => {
      return sendTestEmail(testConfig, accessToken);
    },
    {
      onSuccess: (status) => {
        status && setShowTestEmailModal(false);
        message.success("Test email sent successfully");
      },
      onError: (error) => {
        message.error(
          `Failed to send test email due to ${
            error?.message || "unknown error"
          }`
        );
      },
    }
  );

  // Combined loading state for all async operations
  const isLoading =
    loading ||
    isSmartContextLoading ||
    isUserGroupsLoading ||
    isSlackChannelLoading;

  // Update editor context based on notification mode
  // Filters out embedded tables for non-email notifications
  const updateEditorContext = (smartContext, consolidatedContext) => {
    if (isConsolidated()) {
      const notificationContext =
        notificationType === NOTIFICATION_TYPES.EMAIL
          ? consolidatedContext
          : consolidatedContext.filter((ele) => {
              return !ele[0].includes("embeddedTable");
            });
      setEditorContext(notificationContext);
    } else {
      setEditorContext(smartContext);
    }
  };

  // Process and set context data from API response
  // Parses JSON strings and filters data based on type
  const setContext = (data) => {
    const dataList = JSON.parse(data?.contextList || "[]");
    const colMap = JSON.parse(data?.meta?.contextColMap || "{}");

    const consolidatedData = [];
    const filteredList = [];
    for (const element of dataList) {
      const key = element[0];
      if (!key.includes('"')) {
        consolidatedData.push(element);
      }
      if (!key.includes("embeddedTable")) {
        filteredList.push(element);
      }
    }

    setContextColMap(colMap);
    setContextData({
      smartContext: filteredList,
      consolidatedContext: consolidatedData,
    });
    updateEditorContext(filteredList, consolidatedData);
    setContextLoading(false);
  };

  // Update email options based on mode and partition
  // Handles different modes (individual, consolidated, group)
  const updateEmailOptions = (mode, partitionBy) => {
    const options = [];
    if (mode === EMAIL_TYPE.CONSOLIDATED_GROUP && partitionBy?.value) {
      options.push({
        value: partitionBy.value,
        label: partitionBy.label,
      });
    } else if (mode === EMAIL_TYPE.INDIVIDUAL) {
      const columns = flatten(variables, contextColMap);
      Object.values(columns)
        .filter((column) => column.dataTypeId === 12)
        .map((column) =>
          options.push({
            value: column.systemName,
            label: column.name,
          })
        );
    }
    setEmailOptions(options);
  };

  // Effect to handle mode changes
  // Updates column visibility and form state
  useEffect(() => {
    const mode = form.getFieldValue("mode");
    if (mode && columnVisible !== isConsolidated(mode)) {
      setColumnVisible(isConsolidated(mode));
    }
  }, [form.getFieldValue("mode")]);

  // Update form state when mode changes
  // Resets editor key and updates email options
  function updateFormStateForMode(mode, partitionBy) {
    setEditorKey(id + mode + datasheetId);
    updateEmailOptions(mode, partitionBy);
  }

  // Initialize form values
  // Handles different types of recipients and formats data appropriately
  const initialValues = useMemo(() => {
    const columns = flatten(variables, contextColMap);

    // Helper functions to format different types of recipients
    // Each function handles a specific recipient type (email, user group, datasheet column)
    const modifiedEmails = (emails) =>
      emails.map((email) => ({
        value: email,
        label: email,
      }));

    const modifiedReplyTo = (replyTo) =>
      replyTo ? { value: replyTo, label: replyTo } : null;

    const modifiedUserGroups = (groups) =>
      groups.map((group) => ({
        value: group,
        label: userGroups.find((userGroup) => userGroup.userGroupId === group)
          ?.userGroupName,
      }));

    const modifiedDsCols = (dsCols) =>
      dsCols.map((col) => ({
        value: col,
        label: columns[col]?.name || col,
      }));

    // Get email columns and format options
    const emailColumns = Object.values(columns).filter(
      (column) => column.dataTypeId === 12
    );

    const columnOptions = emailColumns?.map((column) => ({
      value: column.systemName,
      label: column.name,
    }));

    // Format Slack channels with appropriate metadata
    const selectedChannels = (config.slackChannels || []).map((channel) => ({
      value: channel,
      label: slackChannelOptions?.find(
        (slackChannel) => slackChannel.id === channel
      )?.name,
      selectKey: "channels",
    }));

    // Set initial mode and partition
    const mode = config.mode || EMAIL_TYPE.INDIVIDUAL;
    const partitionBy = isEmpty(config.partitionBy)
      ? columnOptions[0]
      : config.partitionBy;
    updateFormStateForMode(mode, partitionBy);
    setAllColumns(columnOptions);

    // Construct initial form data with all necessary fields
    const newData = {
      mode,
      partitionBy,
      to: {
        toEmails: config?.toEmailsFull || modifiedEmails(config.toEmails || []),
        toUserGroups: modifiedUserGroups(config.toUserGroups || []),
        toDsCols: modifiedDsCols(config.toDsCols || []),
        slackChannels: selectedChannels,
      },
      cc: {
        ccEmails: config?.ccEmailsFull || modifiedEmails(config.ccEmails || []),
        ccUserGroups: modifiedUserGroups(config.ccUserGroups || []),
        ccDsCols: modifiedDsCols(config.ccDsCols || []),
      },
      bcc: {
        bccEmails:
          config?.bccEmailsFull || modifiedEmails(config.bccEmails || []),
        bccUserGroups: modifiedUserGroups(config.bccUserGroups || []),
        bccDsCols: modifiedDsCols(config.bccDsCols || []),
      },
      subject: config.subject || "",
      message: config.rawMessage || config.message || "",
      replyTo: {
        replyToEmails: config?.replyToFull || modifiedReplyTo(config.replyTo),
      },
      attachmentFilename: config.attachmentFilename?.split(".csv")[0] || "",
      columnConfig: config.columnConfig || [],
      notificationStatus: config?.inAppParams?.notificationStatus,
      withCelebration: config?.inAppParams?.withCelebration || false,
      celebrationType: config?.inAppParams?.celebrationType || "single",
      oneTime: config?.oneTime ?? true,
    };

    if (!initial.current && !isLoading) {
      form.setFieldsValue(newData);
    }
    initial.current = false;

    return newData;
  }, [config, isLoading]);

  // Process and validate configuration updates
  // Handles form validation and payload construction
  const processConfigUpdate = async (
    { ...configDataByType },
    skipValidation = false
  ) => {
    const getPayload = (formValues) => {
      // Process message content
      // Handles markdown conversion for supported notification types
      const rawMessage =
        formValues.message.replace(TABLE_PLACEHOLDER, TABLE_VALUE) || "";
      const isMarkdownSupported = [
        NOTIFICATION_TYPES.SLACK,
        NOTIFICATION_TYPES.IN_APP,
      ].includes(notificationType);
      const html2markdown = new HtmlToMarkdown();
      const message = isMarkdownSupported
        ? html2markdown.parse(rawMessage)
        : rawMessage;
      const mentionValues = getContextValues(message);

      // Process subject content
      // Extracts variables used in subject line
      const subjectValues = getContextValues(formValues.subject);
      for (const value of subjectValues) {
        if (!mentionValues.includes(value)) {
          mentionValues.push(value);
        }
      }

      // Filter context based on used variables
      // Only includes variables that are actually used in the message
      const currContext = editorContext.filter((ele) =>
        mentionValues.includes(ele[0])
      );
      let selectedColumns = formValues.columnConfig
        .filter((column) => column.isSelected)
        .map((column) => column.systemName);

      // Construct final configuration payload
      // Includes all necessary fields for the notification
      const updatedConfig = {
        databookId,
        datasheetId,
        notificationType,
        ownerEmailId: email,
        mode: formValues.mode,
        partitionBy:
          formValues.mode === EMAIL_TYPE.CONSOLIDATED_GROUP
            ? formValues.partitionBy
            : {},
        toEmails: formValues?.to?.toEmails?.map((email) => email.value),
        toEmailsFull: formValues?.to?.toEmails,
        ccEmails: formValues?.cc?.ccEmails?.map((email) => email.value),
        ccEmailsFull: formValues?.cc?.ccEmails,
        bccEmails: formValues?.bcc?.bccEmails?.map((email) => email.value),
        bccEmailsFull: formValues?.bcc?.bccEmails,
        toUserGroups: formValues?.to?.toUserGroups?.map((group) => group.value),
        ccUserGroups: formValues?.cc?.ccUserGroups?.map((group) => group.value),
        bccUserGroups: formValues?.bcc?.bccUserGroups?.map(
          (group) => group.value
        ),
        toDsCols: formValues?.to?.toDsCols?.map((col) => col.value),
        ccDsCols: formValues?.cc?.ccDsCols?.map((col) => col.value),
        bccDsCols: formValues?.bcc?.bccDsCols?.map((col) => col.value),
        slackChannels: formValues?.to?.slackChannels?.map(
          (channel) => channel.value
        ),
        subject: formValues.subject,
        message,
        rawMessage,
        context: JSON.stringify(currContext),
        replyTo: formValues?.replyTo?.replyToEmails?.value,
        replyToFull: formValues?.replyTo?.replyToEmails,
        attachmentFilename:
          formValues.mode === EMAIL_TYPE.INDIVIDUAL
            ? ""
            : `${formValues.attachmentFilename}.csv`,
        selectedColumns: isConsolidated() ? selectedColumns : [],
        columnConfig: isConsolidated() ? formValues.columnConfig : [],
        sgTemplate: {}, // SendGrid template is not enabled currently
        slackTemplate: "", // Slack template is not enabled currently
        inAppParams: {
          notificationStatus: formValues?.notificationStatus,
          withCelebration: formValues?.withCelebration,
          celebrationType: formValues?.celebrationType,
        },
        oneTime: formValues?.oneTime,
        ...configDataByType,
      };

      return updatedConfig;
    };

    // Validate form if not skipped
    // Handles attachment filename validation
    if (!skipValidation) {
      await form.validateFields().catch((error) => {
        console.error("Validation failed:", error);
        const isAttachmentError = error.errorFields.some(
          (field) => field.name[0] === "attachmentFilename"
        );
        if (isAttachmentError && !columnVisible) {
          setColumnVisible(true);
        }
      });
    }
    return getPayload(form.getFieldsValue());
  };

  // Handle test action
  // Processes configuration and sends test notification
  const testAction = async (configDataByType) => {
    const testConfig = await processConfigUpdate(configDataByType);
    testEmail.mutate(testConfig);
  };

  // Update form field value
  // Handles form updates and button state
  const updateFormValue = (key, value, skipButtonEnable = false) => {
    if (!skipButtonEnable) {
      setIsDisabled(false);
    }
    form.setFieldsValue({ [key]: value });
  };

  // Handle form value changes
  // Updates form state based on changed values
  const onFormValuesChange = (changedValues, allValues) => {
    setIsDisabled(false);
    if ("mode" in changedValues) {
      updateEditorContext(
        contextData.smartContext,
        contextData.consolidatedContext
      );
      updateFormStateForMode(allValues.mode, allValues.partitionBy);
      form.setFieldsValue({
        toDsCols: [],
        ccDsCols: [],
        bccDsCols: [],
        replyTo: "",
        subject: "",
        message: "",
      });
    }
    if ("partitionBy" in changedValues) {
      updateEmailOptions(allValues.mode, allValues.partitionBy);
    }
  };

  // Get dropdown configuration props
  // Handles recipient selection UI configuration
  const getDropdownProps = (name, hasRules = false) => {
    const fields = getFieldsByName(name, notificationType);
    const { user, userGroup, datasheet, channels } = fields;

    // Handle removal of selected items
    // Updates form state when items are removed
    const onRemove = (onChangeCbk) => (list) => {
      const newFieldValue = {};
      const fieldValue = form.getFieldValue(name);
      const listValues = new Set(list.map((item) => item.value));
      for (const key of Object.keys(fieldValue)) {
        newFieldValue[key] = fieldValue[key].filter((item) =>
          listValues.has(item.value)
        );
      }
      onChangeCbk(newFieldValue);
    };

    // Get tabs configuration for dropdown
    // Configures different tabs based on field type and notification type
    const getTabs = (onChangeCbk) => {
      const onItemClick = (key, option) => {
        const fieldValue = form.getFieldValue(name);
        const selectedTags = fieldValue[key].some(
          (tag) => tag.value === option.value
        )
          ? fieldValue[key].filter((tag) => tag.value !== option.value)
          : [...fieldValue[key], option];
        onChangeCbk({ ...fieldValue, [key]: selectedTags });
      };

      let tabs = [];

      // Configure tabs based on field type
      if (name === "replyTo") {
        tabs = [
          {
            name: "Users",
            key: "users",
            searchPlaceholder: "Search by user name",
            isLazy: true,
            selectedValues: form.getFieldValue(name)[user]
              ? [form.getFieldValue(name)[user]]
              : [],
            renderLazyList: () => (
              <FetchUsers
                selectedUsers={
                  form.getFieldValue(name)[user]
                    ? [form.getFieldValue(name)[user]]
                    : []
                }
                onClickItem={(option) => {
                  onChangeCbk({ [user]: option });
                }}
              />
            ),
          },
        ];
      } else {
        tabs = [
          {
            name: "Users",
            key: "users",
            searchPlaceholder: "Search by user name",
            isLazy: true,
            selectedValues: form.getFieldValue(name)[user],
            renderLazyList: () => (
              <FetchUsers
                selectedUsers={form.getFieldValue(name)[user]}
                onClickItem={(option) => onItemClick(user, option)}
              />
            ),
          },
          {
            name: "User Groups",
            key: "groups",
            isLazy: false,
            loading: false,
            searchPlaceholder: "Search by group name",
            showSearch: true,
            tabClass: "h-72 overflow-auto",
            selectedValues: form.getFieldValue(name)[userGroup],
            options: userGroups.map((group) => {
              return {
                value: group.userGroupId,
                label: group.userGroupName,
                selectKey: "groups",
                icon: (
                  <UsersIcon className="h-5 w-5 mt-1.5 text-ever-success-content-lite" />
                ),
                avatarClass: "!bg-ever-success/20 flex justify-center",
              };
            }),
            onToggleItem: (option) => onItemClick(userGroup, option),
          },
        ];
      }

      // Add datasheet fields tab if applicable
      if (
        form.getFieldValue("mode") === EMAIL_TYPE.CONSOLIDATED_GROUP ||
        form.getFieldValue("mode") === EMAIL_TYPE.INDIVIDUAL
      ) {
        if (name !== "replyTo") {
          tabs.push({
            name: "Datasheet Fields",
            key: "tags",
            isLazy: false,
            loading: false,
            searchPlaceholder: "Search by column name",
            showSearch: true,
            tabClass: "h-72 overflow-auto",
            selectedValues: form.getFieldValue(name)[datasheet],
            options: emailOptions.map((option) => ({
              value: option.value,
              label: option.label,
              selectKey: "tags",
              icon: (
                <MailIcon className="h-5 w-5 mt-1.5 text-ever-info-pressed" />
              ),
              avatarClass: "!bg-ever-info-ring/70 flex justify-center",
            })),
            onToggleItem: (option) => onItemClick(datasheet, option),
          });
        }
      }

      // Add Slack channels tab if applicable
      if (notificationType === NOTIFICATION_TYPES.SLACK && name !== "replyTo") {
        tabs.push({
          name: "Channels",
          key: "channels",
          isLazy: false,
          loading: false,
          searchPlaceholder: "Search by channel name",
          selectedValues: form.getFieldValue(name)[channels],
          showSearch: true,
          tabClass: "h-72 overflow-auto",
          options: slackChannelOptions?.map((channel) => ({
            value: channel?.id,
            label: channel?.name,
            icon: channel?.isPrivate ? (
              <LockIcon className="h-5 w-5 mt-1.5 text-ever-chartColors-4" />
            ) : (
              <HashIcon className="h-5 w-5 mt-1.5 text-ever-chartColors-4" />
            ),
            avatarClass: "!bg-ever-chartColors-30",
          })),
          onToggleItem: (option) => onItemClick(channels, option),
        });
      }

      return tabs;
    };

    // Get validation rules if required
    // Ensures at least one recipient is selected
    const getRules = () => [
      {
        required: true,
        validator: (_, value) => {
          const values = Object.values(value).flat();
          return values.length === 0
            ? Promise.reject(`${LABEL[name]} is required`)
            : Promise.resolve();
        },
      },
    ];

    return {
      name,
      fields,
      notificationType,
      getTabs,
      onRemove,
      ...(hasRules ? { rules: getRules() } : {}),
    };
  };

  // Check if test button should be disabled
  // Validates form state based on notification type and mode
  const isTestButtonDisabled = () => {
    const formFields = form.getFieldsValue();
    let testDisabled =
      !formFields.to || Object.values(formFields.to).flat().length === 0;
    switch (notificationType) {
      case NOTIFICATION_TYPES.EMAIL: {
        testDisabled =
          testDisabled ||
          !formFields.subject ||
          form.getFieldError("replyTo").length > 0 ||
          ([EMAIL_TYPE.CONSOLIDATED, EMAIL_TYPE.CONSOLIDATED_GROUP].includes(
            formFields.mode
          ) &&
            !formFields.attachmentFilename);
        break;
      }
      case NOTIFICATION_TYPES.SLACK: {
        testDisabled =
          testDisabled ||
          !formFields.message ||
          ([EMAIL_TYPE.CONSOLIDATED, EMAIL_TYPE.CONSOLIDATED_GROUP].includes(
            formFields.mode
          ) &&
            !formFields.attachmentFilename);
        break;
      }
      case NOTIFICATION_TYPES.IN_APP: {
        testDisabled =
          testDisabled ||
          !formFields.subject ||
          !formFields.notificationStatus ||
          (formFields.notificationStatus === "success" &&
            formFields.withCelebration &&
            !formFields.celebrationType);
        break;
      }
    }
    return testDisabled;
  };

  // Return hook values and functions
  // Exposes necessary state and operations to components
  return {
    form,
    isLoading,
    isContextLoading,
    editorKey,
    editorContext,
    allColumns,
    initialValues,
    columnVisible,
    showTestEmailModal,
    slackStatus,
    isDisabled,
    testAction,
    setIsDisabled,
    isConsolidated,
    isTestButtonDisabled,
    setColumnVisible,
    setShowTestEmailModal,
    updateFormValue,
    onFormValuesChange,
    processConfigUpdate,
    getDropdownProps,
  };
};

export default useCustomContext;
