import { gql } from "@apollo/client";
import {
  InfoCircleIcon,
  SettingsPanelIcon,
  CheckIcon,
} from "@everstage/evericons/outlined";
import { isEmpty, isNil, isEqual } from "lodash";
import { useRef } from "react";
import { twMerge } from "tailwind-merge";

import { COMPONENTS } from "~/Enums";
import {
  EverForm,
  MultiTabDropdownSelect,
  EverSelect,
  EverTg,
  EverButton,
  EverList,
  Space,
  useAbortiveLazyQuery,
  LazyListContent,
  EverGroupAvatar,
} from "~/v2/components";
import { emptyFace } from "~/v2/images";

import {
  EMAIL_TYPE,
  NOTIFICATION_TYPES,
  SEND_EMAIL_OPTIONS,
} from "../../../constants";

const LABEL = {
  to: "To",
  cc: "Cc",
  bcc: "Bcc",
  replyTo: "Reply To",
};

/**
 * MultiTabDropdownSelectTabPaneCommonItem is a React functional component.
 * Renders a common item in a multi-tab dropdown select tab pane.
 *
 * @param {Object} props The properties passed to the component.
 * @param {string} props.label The label to display.
 * @param {string} props.description The description to display.
 * @param {string|JSX.Element} props.icon The icon to display. The icon can be a string or a JSX element.
 * @param {boolean} props.isSelected Whether the item is selected.
 * @param {Function} props.onClickItem The function to call when the item is clicked.
 * @param {boolean} [props.isClickable=true] Whether the item is clickable.
 * @returns {JSX.Element} A div that contains the common item.
 */
const MultiTabDropdownSelectTabPaneCommonItem = ({
  label,
  description,
  icon,
  isSelected,
  onClickItem,
  isClickable = true,
}) => {
  const onClick = () => {
    if (isClickable) onClickItem();
  };

  return (
    <div
      className={`flex items-center justify-between w-full py-2 px-4 rounded hover:bg-ever-base-100 ${
        isClickable ? "cursor-pointer" : ""
      }`}
      onClick={onClick}
    >
      <EverList.Item.Meta
        avatar={
          icon ? (
            typeof icon === "string" ? (
              <img src={icon} className="m-auto h-12 w-10" />
            ) : (
              <>{icon}</>
            )
          ) : null
        }
        title={
          <EverTg.Text
            className={`font-medium ${isSelected ? "text-ever-primary" : ""}`}
          >
            {label}
          </EverTg.Text>
        }
        description={
          isEmpty(description) ? null : (
            <EverTg.Description>{description}</EverTg.Description>
          )
        }
      />
      {isSelected && <CheckIcon className="text-ever-primary w-5 h-5" />}
    </div>
  );
};

const GET_USERS = gql`
  query AllEmployees(
    $userStatus: String
    $searchTerm: String
    $offsetValue: Int
    $limitValue: Int
    $component: String
  ) {
    employeeNameDetails(
      userStatus: $userStatus
      searchTerm: $searchTerm
      offsetValue: $offsetValue
      limitValue: $limitValue
      component: $component
    ) {
      employeeEmailId
      fullName
    }
  }
`;

const EmptyUsers = () => {
  return (
    <span className="flex flex-col items-center text-center">
      <Space direction="vertical" className="mt-10">
        <img src={emptyFace} />
        <EverTg.SubHeading3 className="text-ever-base-content leading-5 mt-8">
          No users
        </EverTg.SubHeading3>
      </Space>
    </span>
  );
};

/**
 * FetchUsers is a React functional component that fetches and displays a list of users.
 * It uses the useAbortiveLazyQuery hook to get the users from the server.
 *
 * @param {Object} props The properties passed to the component.
 * @param {Array} props.selectedUsers The selected users. Each user is an object with a value and label.
 * @param {Function} props.onClickItem The function to call when a list item is clicked. The user is passed as an argument.
 * @returns {JSX.Element} A LazyListContent that contains the list of users.
 */
const FetchUsers = ({ selectedUsers, onClickItem }) => {
  const initialLoadRef = useRef(true);

  const [getUsers, { variables, abort, data }] = useAbortiveLazyQuery(
    GET_USERS,
    {
      fetchPolicy: "network-only",
      onCompleted: (responseData) => {
        initialLoadRef.current = false;
        const response = responseData?.employeeNameDetails;

        if (isNil(response)) {
          variables.failureCbk();
        } else {
          const formattedResponse = response.map((option) => {
            return {
              label: option.fullName,
              value: option.employeeEmailId,
              text: option.employeeEmailId,
            };
          });

          variables.successCbk(formattedResponse);
        }
      },
      onError: () => {
        variables.failureCbk();
      },
    }
  );

  const usersLazyLoadProps = {
    abort,
    getOptions: async (params) => {
      const { searchTerm, offset, limit } = params;
      await getUsers({
        ...params,
        userStatus: "Active",
        searchTerm,
        offsetValue: offset,
        limitValue: limit,
        component: COMPONENTS.SETTINGS,
      });
    },
  };

  return (
    <LazyListContent
      listHeight={360}
      listItemHeight={68}
      skipSelectAll
      showSearch={
        initialLoadRef.current && isEmpty(data?.employeeNameDetails)
          ? false
          : true
      }
      noDataText={<EmptyUsers />}
      listItemRenderer={(item) => {
        const isSelected = selectedUsers.some(
          (selectedUser) => selectedUser.value === item.value
        );

        return (
          <MultiTabDropdownSelectTabPaneCommonItem
            label={item.label}
            description={item.text}
            icon={
              <EverGroupAvatar
                avatars={[
                  {
                    firstName: item.label.split(" ")[0],
                    lastName: item.label.split(" ")[1],
                  },
                ]}
                size="large"
              />
            }
            isSelected={isSelected}
            onClickItem={() =>
              onClickItem({ value: item.value, label: item.label })
            }
          />
        );
      }}
      {...usersLazyLoadProps}
    />
  );
};

/**
 * `MultiTabDropdownSelectForm` is a React functional component that renders a form item with a multi-tab dropdown select.
 *
 * @param {Object} props - The properties passed to the component.
 * @param {boolean} [props.visible=true] - Whether the form item is visible.
 * @param {string} props.name - The name of the form item.
 * @param {string} props.label - The label of the form item.
 * @param {Object} props.fields - The fields for the dropdown select.
 * @param {Function} props.getTabs - Function to get the tabs for the dropdown select.
 * @param {Function} props.onRemove - Function to handle removing a selected option.
 * @param {boolean} props.disabled - Whether the dropdown select is disabled.
 * @param {string} [props.notificationType] - The type of the notification.
 * @param {string} [props.className=""] - The CSS class to apply to the form item.
 * @param {Object} props.props - Additional properties to pass to the form item.
 *
 * @returns {JSX.Element} A form item with a multi-tab dropdown select.
 */
const MultiTabDropdownSelectForm = ({
  visible = true,
  name,
  label,
  fields,
  getTabs,
  onRemove,
  disabled,
  notificationType,
  className = "",
  ...props
}) => {
  return (
    <EverForm.Item
      noStyle
      shouldUpdate={(prevValues, currentValues) =>
        prevValues.mode !== currentValues.mode ||
        !isEqual(
          prevValues[name][fields.user],
          currentValues[name][fields.user]
        ) ||
        !isEqual(
          prevValues[name][fields.userGroup],
          currentValues[name][fields.userGroup]
        ) ||
        !isEqual(
          prevValues[name][fields.datasheet],
          currentValues[name][fields.datasheet]
        )
      }
      hidden={!visible}
    >
      {() => (
        <EverForm.Item
          name={name}
          label={label}
          className={twMerge("mb-0", className)}
          {...props}
        >
          <CustomMultiSelect
            getTabs={getTabs}
            onRemove={onRemove}
            disabled={disabled}
          />
        </EverForm.Item>
      )}
    </EverForm.Item>
  );
};

/**
 * `CustomMultiSelect` is a React functional component that renders a multi-tab dropdown select with custom behavior.
 *
 * @param {Object} props - The properties passed to the component.
 * @param {Function} props.getTabs - Function to get the tabs for the dropdown select.
 * @param {Function} props.onRemove - Function to handle removing a selected option.
 * @param {boolean} props.disabled - Whether the dropdown select is disabled.
 * @param {Function} [props.onChange] - Form function to handle changing the selected options.
 *
 * @returns {JSX.Element} A multi-tab dropdown select with custom behavior.
 */
const CustomMultiSelect = ({
  getTabs,
  onRemove,
  disabled,
  onChange = (value) => value,
}) => {
  // Close any open dropdowns in ModeSection
  const closeOtherDropdowns = () => {
    // Find all open dropdowns with the EverSelect class and close them
    const openSelects = document.querySelectorAll(
      ".ant-select-dropdown:not(.ant-select-dropdown-hidden)"
    );
    openSelects.forEach((dropdown) => {
      if (dropdown.parentNode) {
        // Trigger a click outside event to close the dropdown
        const clickEvent = new MouseEvent("mousedown", {
          bubbles: true,
          cancelable: true,
          view: window,
        });
        document.body.dispatchEvent(clickEvent);
      }
    });
  };

  return (
    <MultiTabDropdownSelect
      placeholder="Select..."
      selectClassName="w-full"
      tabsClassName="h-96"
      multiSelectOverflow="default"
      maxTagCount={10}
      tabs={getTabs(onChange)}
      showSearch={true}
      searchValue=""
      handleSearch={() => ""}
      onRemove={onRemove(onChange)}
      disabled={disabled}
      dropdownMatchSelectWidth
      onMouseDown={(e) => {
        e.stopPropagation();
      }}
      onDropdownVisibleChange={(visible) => {
        if (visible) {
          closeOtherDropdowns();
        }
      }}
    />
  );
};

/**
 * `ModeSection` is a React functional component that renders a section of a form for configuring the mode of a notification.
 *
 * @param {Object} props - The properties passed to the component.
 * @param {boolean} props.isLoading - Whether the api's are in progress.
 * @param {Object[]} props.allColumns - The available columns for the "group records by" select.
 * @param {boolean} props.disabled - Whether the form items are disabled.
 * @param {string} props.notificationType - The type of the notification.
 * @param {Function} props.toggleColumnVisibility - Function to handle toggling the visibility of the columns.
 *
 * @returns {JSX.Element} A form item that contains the mode section of the form.
 */
const ModeSection = ({
  isLoading,
  allColumns,
  disabled,
  notificationType,
  toggleColumnVisibility,
}) => {
  return (
    <EverForm.Item
      noStyle
      shouldUpdate={(prevValues, currentValues) =>
        prevValues.mode !== currentValues.mode ||
        prevValues.partitionBy !== currentValues.partitionBy
      }
    >
      {({ getFieldValue }) => {
        const mode = getFieldValue("mode");
        return (
          <>
            <div className="flex flex-col gap-2 mt-4">
              <EverTg.Text>Delivery criteria</EverTg.Text>
              <EverForm.Item name="mode" noStyle>
                <EverSelect
                  size="small"
                  className="w-full"
                  options={SEND_EMAIL_OPTIONS}
                  disabled={disabled}
                  dropdownMatchSelectWidth
                />
              </EverForm.Item>
              <EverForm.Item
                noStyle
                hidden={mode !== EMAIL_TYPE.CONSOLIDATED_GROUP || isLoading}
              >
                <div className="mt-2">
                  <EverTg.Text>Group records</EverTg.Text>
                </div>
                <div>
                  <EverForm.Item
                    name="partitionBy"
                    noStyle
                    rules={[
                      {
                        required: mode === EMAIL_TYPE.CONSOLIDATED_GROUP,
                        message: "Group by is required",
                      },
                    ]}
                  >
                    <EverSelect
                      size="small"
                      className="w-full"
                      options={allColumns}
                      labelInValue
                      disabled={disabled}
                      defaultValue={getFieldValue("partitionBy")}
                      dropdownMatchSelectWidth
                    />
                  </EverForm.Item>
                </div>
              </EverForm.Item>
            </div>
            {(mode === EMAIL_TYPE.CONSOLIDATED ||
              mode === EMAIL_TYPE.CONSOLIDATED_GROUP) && (
              <div className="flex flex-col gap-4">
                <div className="flex items-center gap-2 bg-ever-primary/5 rounded px-4 py-2">
                  <div>
                    <InfoCircleIcon className="!w-6 !h-6 text-ever-info" />
                  </div>
                  <EverTg.Text className="text-ever-base-content text-xs">
                    You can download the CSV file with the data from the message
                    {notificationType === NOTIFICATION_TYPES.EMAIL &&
                      " and there’s also an option to insert the data table in the message"}
                  </EverTg.Text>
                </div>
                <div className="flex items-center gap-2 mb-2">
                  <EverButton
                    size="small"
                    type="link"
                    className="!p-0"
                    onClick={toggleColumnVisibility}
                  >
                    <SettingsPanelIcon className="w-6 h-6 shrink-0" />
                    <EverTg.Text className="whitespace-normal text-justify">
                      Configure filename and columns
                    </EverTg.Text>
                  </EverButton>
                </div>
              </div>
            )}
          </>
        );
      }}
    </EverForm.Item>
  );
};

export { LABEL, MultiTabDropdownSelectForm, ModeSection, FetchUsers };
