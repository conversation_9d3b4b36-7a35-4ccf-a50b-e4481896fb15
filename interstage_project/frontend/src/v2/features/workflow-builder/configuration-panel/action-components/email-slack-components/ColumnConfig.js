import { DotsVerticalIcon, XIcon } from "@everstage/evericons/outlined";
import { cloneDeep } from "lodash";
import { useEffect } from "react";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { createPortal } from "react-dom";
import { twMerge } from "tailwind-merge";

import { FIXED_COLUMNS } from "~/Enums";
import { useVariableStore } from "~/GlobalStores/VariableStore";
import {
  EverCard,
  EverCheckbox,
  EverInput,
  EverTg,
  IconButton,
  EverList,
  EverForm,
  EverLoader,
  EverLabel,
} from "~/v2/components";

import { Header, DataTypeIcon, ContentWrapper } from "../../PanelComponents";

function flatten(data) {
  const flattenData = [];
  for (const variable of data) {
    flattenData.push({
      modelName: variable.meta.modelName,
      systemName: variable.meta.systemName,
      dataTypeId: variable.meta.dataTypeId,
      category: variable.meta.category,
      name: variable.name,
      databookId: variable.databookId,
      datasheetId: variable.datasheetId,
      isSelected: false,
    });
  }
  return flattenData;
}

/**
 * DraggableItem is a React functional component that renders a draggable item in a column configuration.
 * It uses the provided prop to get the draggable props and the inner ref.
 * The EverCheckbox's checked state is the column's isSelected state, and its onChange function is the toggleColumnSelection function with the column's system name as an argument.
 *
 * @param {Object} props The properties passed to the component.
 * @param {Object} props.column The column to render. The column is an object with a system name, isSelected state, data type id, and name.
 * @param {Array} props.fixedColumns The list of fixed columns. Each fixed column is a string that represents the system name of a column.
 * @param {Function} props.toggleColumnSelection The function to call when the column selection is toggled. The system name of the column is passed as an argument.
 * @param {Object} props.dataTypesById The map of data type ids to data types. Each key is a data type id and each value is a data type.
 * @param {Object} props.provided The provided draggable props and inner ref.
 * @param {boolean} props.disabled Whether the checkbox is disabled.
 * @returns {JSX.Element} A div that contains the draggable item.
 */
const DraggableItem = ({
  column,
  fixedColumns,
  toggleColumnSelection,
  dataTypesById,
  provided,
  disabled,
}) => {
  return (
    <div
      ref={provided.innerRef}
      {...provided.draggableProps}
      {...provided.dragHandleProps}
    >
      <div className="flex mb-4 items-center">
        {!fixedColumns.includes(column.systemName) && (
          <div className="flex mr-3">
            <DotsVerticalIcon className="w-1 h-4 text-ever-base-content-low" />
            <DotsVerticalIcon className="w-2 h-4 text-ever-base-content-low" />
          </div>
        )}
        <EverCard className="!w-full py-2.5 px-3 flex justify-between rounded-lg">
          <div className="flex items-center gap-3">
            <EverCheckbox
              checked={column.isSelected}
              onChange={() => toggleColumnSelection(column.systemName)}
              disabled={disabled}
            />
            <DataTypeIcon name={dataTypesById[column.dataTypeId]} />
            <EverTg.Text>{column.name}</EverTg.Text>
          </div>
        </EverCard>
      </div>
    </div>
  );
};

/**
 * CustomColumns is a React functional component that manages the custom columns in a workflow.
 * It maintains the column configuration, visibility, and attachment filename in state.
 * It provides functions for handling the cancel, drag end, column selection toggle, select change, and attachment filename change.
 * It renders a portal that includes a header, content wrapper, and draggable list of columns.
 * The draggable list of columns includes a Droppable that renders a clone of the DraggableItem and a DraggableItem for each column.
 *
 * @param {Object} props The properties passed to the component.
 * @param {Object} props.dsVariableStore - The store for the datasheet variables.
 * @param {Object[]} props.dsVariableStore.variables - The variables for the datasheet.
 * @param {Array} props.columnConfig The initial column configuration to use.
 * @param {Function} props.setColumnConfig The function to call when the column configuration changes.
 * @param {string} props.datasheetId The id of the data sheet to use.
 * @param {boolean} props.visible Whether the portal is visible.
 * @param {boolean} props.isLoading Whether the api's are in progress or not.
 * @param {boolean} props.isRequired Whether the file name field is required or not.
 * @param {Function} props.setVisible The function to call when the visibility of the portal changes.
 * @param {boolean} props.disabled Whether the component is disabled.
 * @param {Function} props.onFormValuesChange The function to call when form values change.
 * @param {Function} props.updateActionNode The function to call when an action node is updated.
 * @param {Function} props.processConfigUpdate The function to call when a config is updated.
 * @param {string} props.name The name of the process.
 * @param {Object} props.form The form instance from EverForm.
 * @param {Object} props.formErrors The form errors object.
 * @param {Function} props.setFormErrors The function to call when the form errors change.
 * @returns {JSX.Element} A portal that contains the custom columns.
 */
const CustomColumns = ({
  isLoading,
  isRequired,
  dsVariableStore: { variables },
  columnConfig,
  setColumnConfig,
  datasheetId,
  visible,
  setVisible,
  disabled,
  onFormValuesChange,
  updateActionNode,
  processConfigUpdate,
  name,
  form,
  formErrors = {},
  setFormErrors,
}) => {
  const { dataTypesById } = useVariableStore();

  const fixedColumns = Object.values(FIXED_COLUMNS);

  useEffect(() => {
    if (!isLoading) {
      try {
        if (datasheetId) {
          const columns = Object.values(flatten(variables)).filter(
            (x) => x.datasheetId === datasheetId
          );

          const config = [];
          for (const column of columnConfig || []) {
            if (column.isSelected) {
              config.push(column.systemName);
            }
          }
          const selectedItems = [];
          const notSelectedDefaultItems = [];
          const notSelectedItems = [];
          const fixedItems = [];
          for (const column of columns) {
            if (config.includes(column.systemName)) {
              selectedItems.push({ ...column, isSelected: true });
            } else {
              notSelectedItems.push(column);
            }
          }
          selectedItems.sort(
            (a, b) =>
              config.indexOf(a.systemName) - config.indexOf(b.systemName)
          );
          notSelectedItems.sort((a, b) =>
            a.systemName.localeCompare(b.systemName)
          );
          setColumnConfig(
            [
              ...selectedItems,
              ...notSelectedItems,
              ...notSelectedDefaultItems,
              ...fixedItems,
            ],
            true
          );
        } else {
          setColumnConfig([], true);
        }
      } catch (error) {
        console.log(error);
        setColumnConfig([], true);
      }
    }
  }, [isLoading]);

  const handleCancel = () => {
    setVisible(false);
  };

  function handleOnDragEnd(result) {
    try {
      if (
        !result.destination ||
        fixedColumns.includes(
          columnConfig[result?.destination?.index]?.systemName
        )
      )
        return;

      const clonedListItems = cloneDeep(columnConfig);
      const [reorderedItem] = clonedListItems.splice(result.source.index, 1);
      clonedListItems.splice(result.destination.index, 0, reorderedItem);

      setColumnConfig(clonedListItems);
      onFormValuesChange(
        { columnConfig: clonedListItems },
        { ...form.getFieldsValue(), columnConfig: clonedListItems }
      );
      updateActionNode(processConfigUpdate, name);
    } catch (error) {
      console.log(error);
    }
  }

  const toggleColumnSelection = (systemName) => {
    if (disabled) return;

    const clonedColumns = cloneDeep(columnConfig);
    const toggledColumnIndex = clonedColumns.findIndex(
      (column) => column.systemName === systemName
    );
    if (toggledColumnIndex >= 0) {
      clonedColumns[toggledColumnIndex].isSelected =
        !clonedColumns[toggledColumnIndex].isSelected;
    }
    const newAllColumns = [...clonedColumns];
    setColumnConfig(newAllColumns);
    onFormValuesChange(
      { columnConfig: newAllColumns },
      { ...form.getFieldsValue(), columnConfig: newAllColumns }
    );
    updateActionNode(processConfigUpdate, name);
    if (newAllColumns.some((column) => column.isSelected)) {
      delete formErrors.columnConfig;
      setFormErrors(formErrors);
    }
  };

  const onSelectChange = (event) => {
    if (disabled) return;

    const value = event.target.checked;
    const clonedColumns = cloneDeep(columnConfig);
    for (const column of clonedColumns) {
      column.isSelected = value;
    }
    const newAllColumns = [...clonedColumns];
    setColumnConfig(newAllColumns);
    onFormValuesChange(
      { columnConfig: newAllColumns },
      { ...form.getFieldsValue(), columnConfig: newAllColumns }
    );
    updateActionNode(processConfigUpdate, name);
    if (newAllColumns.some((column) => column.isSelected)) {
      delete formErrors.columnConfig;
      setFormErrors(formErrors);
    }
  };

  const allColumnsSelected =
    columnConfig.length > 0 &&
    columnConfig.every((column) => column.isSelected === true);

  return createPortal(
    <div
      className={twMerge(
        "flex flex-col overflow-y-auto border-0 border-l border-solid border-ever-base-400 absolute 2xl:w-96 w-72 h-full top-0 left-0 transition-all duration-300 bg-ever-base-50",
        visible ? "-translate-x-full" : "translate-x-0"
      )}
    >
      <Header defaultName="Configure table">
        <IconButton
          className="ml-auto"
          icon={<XIcon />}
          type="ghost"
          size="small"
          color="base"
          onClick={handleCancel}
        />
      </Header>
      <ContentWrapper className="pr-4">
        <EverLoader spinning={isLoading}>
          <EverForm.Item
            className="flex-col [&>div]:text-left"
            name="attachmentFilename"
            label="File name"
            rules={[
              {
                required: isRequired,
                message: "File name is required",
              },
            ]}
          >
            <EverInput
              className="w-full"
              placeholder="Add attachment filename"
              suffix=".csv"
              disabled={disabled}
            />
          </EverForm.Item>
          <div className="py-2">
            <EverLabel required>Fields selection</EverLabel>
          </div>
          {formErrors.columnConfig && (
            <div className="text-ever-error text-sm mb-2">
              {formErrors.columnConfig[0]}
            </div>
          )}
          <div className="py-2 pr-2">
            <EverCheckbox
              checked={allColumnsSelected}
              onChange={onSelectChange}
              className="fz-14-imp fw-semi-bold"
              disabled={disabled}
            >
              <EverTg.Description>Select all fields</EverTg.Description>
            </EverCheckbox>
          </div>
          <div className="custom-scroll pr-2">
            <EverForm.Item name="columnConfig">
              <DragDropContext onDragEnd={handleOnDragEnd}>
                <Droppable
                  droppableId="dropId"
                  renderClone={(provided, _, rubric) => {
                    const column = columnConfig[rubric.source.index];
                    return (
                      <DraggableItem
                        column={column}
                        fixedColumns={fixedColumns}
                        toggleColumnSelection={toggleColumnSelection}
                        dataTypesById={dataTypesById}
                        provided={provided}
                        disabled={disabled}
                      />
                    );
                  }}
                >
                  {(provided) => (
                    <div ref={provided.innerRef} {...provided.droppableProps}>
                      <EverList rowKey="systemName" dataSource={columnConfig}>
                        {columnConfig.map((column, index) => {
                          return (
                            <Draggable
                              key={index}
                              className="draggable"
                              draggableId={`draggable-${index}`}
                              index={index}
                              isDragDisabled={
                                fixedColumns.includes(column.systemName) ||
                                disabled
                              }
                            >
                              {(provided) => (
                                <DraggableItem
                                  column={column}
                                  fixedColumns={fixedColumns}
                                  toggleColumnSelection={toggleColumnSelection}
                                  dataTypesById={dataTypesById}
                                  provided={provided}
                                  disabled={disabled}
                                />
                              )}
                            </Draggable>
                          );
                        })}
                      </EverList>
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </EverForm.Item>
          </div>
        </EverLoader>
      </ContentWrapper>
    </div>,
    document.querySelector("#config_column")
  );
};

export default CustomColumns;
