/**
 * Email notification configuration component for workflow builder
 * Allows users to configure email notifications with various options like subject, message content,
 * recipients (To, CC, BCC), attachments, and test functionality
 */

import { InfoCircleIcon } from "@everstage/evericons/outlined";
import { Mentions } from "antd";
import { get, isEqual, isEmpty, cloneDeep } from "lodash";
import { observer } from "mobx-react";
import { useState, useMemo, useEffect } from "react";

import { useDSVariableStore } from "~/GlobalStores/DSVariableStore";
import {
  EverButton,
  EverSwitch,
  EverForm,
  EverLabel,
  EverTooltip,
} from "~/v2/components";

import CustomColumns from "./email-slack-components/ColumnConfig";
import {
  MultiTabDropdownSelectForm,
  ModeSection,
  LABEL,
} from "./email-slack-components/CommonComponents";
import CustomEditor from "./email-slack-components/custom-ck-editor";
import TestEmailModal from "./email-slack-components/TestEmailModal";
import useCustomContext from "./email-slack-components/useCustomContext";
import { ACTION_TYPE_ICONS, NOTIFICATION_TYPES } from "../../constants";
import { useWorkflowBuilder } from "../../hooks";
import {
  Header,
  ContentWrapper,
  Footer,
  renderMentionSuggestionsList,
} from "../PanelComponents";

/**
 * Main Email component that renders the configuration form
 * @param {Object} props - Component props
 * @param {Object} props.activeNode - Current active node in workflow
 * @param {Function} props.handleActionClear - Function to clear action
 * @param {Function} props.setNodeError - Function to set node error state
 */
const Email = observer(({ activeNode, handleActionClear, setNodeError }) => {
  // Get workflow builder utilities
  const {
    getNodes,
    isEditMode,
    updateConfigPanelState,
    validateErrors,
    updateWorkflowValidation,
    updateActionNode,
  } = useWorkflowBuilder();
  const disableFields = !isEditMode;

  // Extract configuration from active node
  const config = useMemo(
    () => get(activeNode, "data.componentParams.action.params", {}),
    [activeNode.id]
  );

  // Get databook and datasheet IDs from workflow
  const { databookId, datasheetId } = useMemo(
    () => get(getNodes(), "0.data.componentParams", {}),
    [getNodes]
  );

  // Prepare datasheet IDs for variable store
  const datasheetIds = useMemo(
    () => (datasheetId ? [datasheetId] : []),
    [datasheetId]
  );

  // Initialize datasheet variable store
  const dsVariableStore = useDSVariableStore(datasheetIds);

  // State for notification name and visibility of CC/BCC fields
  const [name, setName] = useState(
    get(activeNode, "data.componentParams.name", "")
  );
  const [showCC, setShowCC] = useState(config.ccEmails?.length > 0);
  const [showBCC, setShowBCC] = useState(config.bccEmails?.length > 0);
  const [showReplyTo, setShowReplyTo] = useState(config.replyTo?.length > 0);

  const [formErrors, setFormErrors] = useState({});

  // Initialize form and context using custom hook
  const {
    form,
    isLoading,
    isContextLoading,
    editorKey,
    editorContext,
    initialValues,
    allColumns,
    columnVisible,
    showTestEmailModal,
    testAction,
    setIsDisabled,
    isConsolidated,
    isTestButtonDisabled,
    setColumnVisible,
    setShowTestEmailModal,
    updateFormValue,
    onFormValuesChange,
    processConfigUpdate,
    getDropdownProps,
  } = useCustomContext({
    id: activeNode.id,
    notificationType: NOTIFICATION_TYPES.EMAIL,
    config,
    databookId,
    datasheetId,
    dsVariableStore,
  });

  // Update block validation on component mount
  useEffect(() => {
    updateBlockValidation();
  }, []);

  /**
   * Updates the validation state of the block based on form values
   * Handles validation for email-specific fields like subject and recipients
   */
  const updateBlockValidation = () => {
    const values = form.getFieldsValue();
    let newValidationError = cloneDeep(validateErrors);
    const nodeErrors = newValidationError[activeNode.id] || "";

    // Determine if the subject and recipients are present
    const hasSubject = !!values?.subject;
    const hasRecipients =
      !isEmpty(values?.to?.toEmails) ||
      !isEmpty(values?.to?.toUserGroups) ||
      !isEmpty(values?.to?.toDsCols);

    // Helper function to update validation errors by removing specific error types
    const updateValidationErrors = (errorMessage) => {
      newValidationError[activeNode.id] = nodeErrors
        .replace(new RegExp(`\\b${errorMessage}\\b`), "") // Remove specific error message
        .replace(/\s*,\s*/g, ",") // Normalize spaces around commas
        .replace(/,+/g, ",") // Replace multiple consecutive commas with one
        .replace(/^,|,$/g, ""); // Remove leading or trailing commas// Remove leading or trailing commas

      // Update the workflow validation state
      updateWorkflowValidation({
        validateErrors: { ...newValidationError },
      });
    };

    // Check if the subject is present but recipients are missing
    if (
      hasSubject &&
      nodeErrors.includes("Subject not present") &&
      !hasRecipients
    ) {
      updateValidationErrors("Subject not present");
    }

    // Check if recipients are present but the subject is missing
    if (
      hasRecipients &&
      nodeErrors.includes("Recipients not present") &&
      !hasSubject
    ) {
      updateValidationErrors("Recipients not present");
    }

    // If both subject and recipients are present, clear any existing errors
    if (hasSubject && hasRecipients) {
      if (nodeErrors) {
        delete newValidationError[activeNode.id];
        updateWorkflowValidation({
          validateErrors: { ...newValidationError },
        });
      }
      // Remove the node error from the state
      setNodeError((prevState) => {
        const { [activeNode.id]: _, ...newState } = prevState;
        return newState;
      });
    } else {
      // Set an error if the block is incomplete
      setNodeError({
        [activeNode.id]: {
          message: "Then block is incomplete",
          isValid: false,
        },
      });
    }
  };

  /**
   * Handles name change for the notification
   * @param {string} newName - The new name for the notification
   */
  const handleNameChange = (newName) => {
    setIsDisabled(false);
    setName(newName);
    updateActionNode(processConfigUpdate, newName);
  };

  /**
   * Handles saving the action by validating the form
   */
  const handleActionSave = async () => {
    const columnConfig = form.getFieldValue("columnConfig") || [];
    const hasSelectedColumns = columnConfig.some((col) => col.isSelected);
    let currentErrors = { ...formErrors };
    if (!hasSelectedColumns) {
      currentErrors = {
        ...currentErrors,
        columnConfig: ["Please select at least one column"],
      };
      setFormErrors(currentErrors);
    } else {
      delete currentErrors.columnConfig;
      setFormErrors(currentErrors);
    }
    form.validateFields().then(() => {
      if (!formErrors) {
        updateConfigPanelState((prevState) => ({
          ...prevState,
          panelExpanded: !prevState.panelExpanded,
        }));
      }
    });
  };

  return (
    <>
      <TestEmailModal
        showTestEmailModal={showTestEmailModal}
        setShowTestEmailModal={setShowTestEmailModal}
        testAction={testAction}
        type={NOTIFICATION_TYPES.EMAIL}
      />
      <Header
        defaultName={name}
        icon={
          <div className="flex items-center p-2 bg-[#EFECFB] rounded-lg">
            {ACTION_TYPE_ICONS["email"]}
          </div>
        }
        onChangeCbk={handleNameChange}
        editable
      />
      <EverForm
        form={form}
        layout="vertical"
        className="custom-period-form flex flex-col h-full overflow-auto"
        initialValues={initialValues}
        onValuesChange={async (changedValues, allValues) => {
          onFormValuesChange(changedValues, allValues);
          await updateActionNode(processConfigUpdate, name);
          updateBlockValidation();
        }}
      >
        <EverForm.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            !isEqual(prevValues.columnConfig, currentValues.columnConfig) ||
            prevValues.attachmentFilename !== currentValues.attachmentFilename
          }
        >
          {({ getFieldValue }) => (
            <CustomColumns
              visible={columnVisible}
              setVisible={setColumnVisible}
              isRequired={isConsolidated()}
              datasheetId={datasheetId}
              isLoading={isLoading}
              dsVariableStore={dsVariableStore}
              columnConfig={getFieldValue("columnConfig") || []}
              setColumnConfig={(value, skipButtonEnable = false) =>
                updateFormValue("columnConfig", value, skipButtonEnable)
              }
              disabled={disableFields}
              onFormValuesChange={onFormValuesChange}
              updateActionNode={updateActionNode}
              processConfigUpdate={processConfigUpdate}
              name={name}
              form={form}
              formErrors={formErrors}
              setFormErrors={setFormErrors}
            />
          )}
        </EverForm.Item>
        <ContentWrapper>
          <ModeSection
            isLoading={isLoading}
            allColumns={allColumns}
            disabled={disableFields}
            notificationType={NOTIFICATION_TYPES.EMAIL}
            toggleColumnVisibility={() => setColumnVisible(!columnVisible)}
          />
          <MultiTabDropdownSelectForm
            label={LABEL.to}
            disabled={disableFields}
            {...getDropdownProps("to", true)}
          />
          {/* CC/BCC toggle buttons */}
          {!disableFields && (
            <div className="flex gap-3">
              <EverButton
                className="!p-0 !h-5"
                size="small"
                type="link"
                onClick={() => setShowCC(!showCC)}
              >
                Cc
              </EverButton>
              <EverButton
                className="!p-0 !h-5"
                size="small"
                type="link"
                onClick={() => setShowBCC(!showBCC)}
              >
                Bcc
              </EverButton>
            </div>
          )}
          <MultiTabDropdownSelectForm
            visible={showCC}
            label={LABEL.cc}
            disabled={disableFields}
            {...getDropdownProps("cc")}
          />
          <MultiTabDropdownSelectForm
            visible={showBCC}
            label={LABEL.bcc}
            disabled={disableFields}
            {...getDropdownProps("bcc")}
          />
          <EverForm.Item
            name="subject"
            label="Subject"
            append={
              <EverTooltip title="Subject is limited to 10,000 characters and remaining characters will be truncated.">
                <InfoCircleIcon className="w-4 h-4 text-ever-base-content-mid cursor-help" />
              </EverTooltip>
            }
            className="mb-0"
            rules={[{ required: true, message: "Subject is required" }]}
          >
            <Mentions
              autoSize={{ minRows: 1, maxRows: 3 }}
              placeholder="Enter a subject line"
              className="!min-h-10 w-full rounded-lg flex items-center"
              prefix={["{{"]}
              maxLength={998}
              disabled={disableFields}
              onBlur={(e) => {
                form.setFieldsValue({ subject: e.target.value.trim() });
              }}
            >
              {renderMentionSuggestionsList(editorContext)}
            </Mentions>
          </EverForm.Item>
          <EverForm.Item
            name="message"
            label="Email Content"
            append={
              <EverTooltip title="The total size of your email, including attachments, must be less than 30MB.">
                <InfoCircleIcon className="w-4 h-4 text-ever-base-content-mid cursor-help" />
              </EverTooltip>
            }
            className="mb-0"
          >
            <CustomEditor
              key={editorKey}
              type={NOTIFICATION_TYPES.EMAIL}
              isLoading={isContextLoading}
              smartContext={editorContext}
              disabled={disableFields}
            />
          </EverForm.Item>
          {/* Reply To section */}
          <div className="flex items-center gap-2">
            <EverLabel>Add Reply To</EverLabel>
            <EverSwitch
              checked={showReplyTo}
              onChange={() => setShowReplyTo(!showReplyTo)}
              disabled={disableFields}
            />
          </div>
          <MultiTabDropdownSelectForm
            visible={showReplyTo}
            label={LABEL.replyTo}
            disabled={disableFields}
            {...getDropdownProps("replyTo")}
          />
        </ContentWrapper>
        <EverForm.Item noStyle shouldUpdate>
          {() => (
            <>
              {!disableFields && (
                <Footer
                  onBack={handleActionClear}
                  onNext={handleActionSave}
                  nextBtnProps={{
                    className: "m-0",
                  }}
                  nextBtnText="Next"
                >
                  <EverTooltip
                    title={
                      isTestButtonDisabled() && "Fill all mandatory fields"
                    }
                  >
                    <div className="ml-auto">
                      <EverButton
                        type="ghost"
                        color="primary"
                        className="bg-ever-base"
                        onClick={() => setShowTestEmailModal(true)}
                        disabled={isTestButtonDisabled()}
                      >
                        Send test email
                      </EverButton>
                    </div>
                  </EverTooltip>
                </Footer>
              )}
            </>
          )}
        </EverForm.Item>
      </EverForm>
    </>
  );
});

export default Email;
