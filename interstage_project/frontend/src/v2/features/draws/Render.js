import { Col, Row } from "antd";
import { useLocalStore } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";

import { everHeaderHeightAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useViewport } from "~/v2/components/custom-hooks";
import EmployeeExplorer from "~/v2/features/quota/quota-summary/employee-explorer-new";

import DrawsSummary from "./DrawsSummary";
import DrawsSummaryStore from "./store";

const Render = () => {
  const drawsSummaryStore = useLocalStore(() => new DrawsSummaryStore());
  const { email, isLoggedInAsUser } = useAuthStore();
  const { height } = useViewport();
  const headerHeight = useRecoilValue(everHeaderHeightAtom);

  const getHeight = () =>
    height - headerHeight - 128 - (isLoggedInAsUser ? 56 : 0);

  const [showExplorer, setShowExplorer] = useState("hide");
  const [newHeight, setNewHeight] = useState(getHeight());

  useEffect(() => {
    setNewHeight(getHeight());
  }, [height, headerHeight]);

  useEffect(() => {
    if (showExplorer === false) {
      drawsSummaryStore.setSelectedPayee(email);
    }
  }, [showExplorer]);

  return (
    <div className="flex h-full">
      {showExplorer && (
        <div className="bg-ever-primary-content text-ever-base-content w-96 h-full">
          <div className="relative pr-5 pt-2 pl-6 h-full">
            <EmployeeExplorer
              store={drawsSummaryStore}
              setShowExplorer={setShowExplorer}
              height={newHeight}
            />
            <div className="-top-[1.3rem] -bottom-[0.1px] right-0 absolute w-px bg-ever-base-300"></div>
          </div>
        </div>
      )}
      <Row
        className={twMerge(
          showExplorer === "hide" ? "invisible" : "visible",
          "h-full w-full overflow-hidden"
        )}
      >
        <Col span={24} className="h-full w-full">
          <DrawsSummary store={drawsSummaryStore} />
        </Col>
      </Row>
    </div>
  );
};

export default Render;
