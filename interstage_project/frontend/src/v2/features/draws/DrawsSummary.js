import { gql, useQuery } from "@apollo/client";
import { Table } from "antd";
import { observer } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { formatCurrency } from "~/Utils/CurrencyUtils";
import { getClientFiscalYear } from "~/Utils/DateUtils";
import { EverLabel, EverTg, EverEmptyData } from "~/v2/components";
import { EverDatePicker } from "~/v2/components/ever-date-picker/EverDatePicker";
import { EverLoader } from "~/v2/components/ever-loader";
import { EverBreadcrumbPortal } from "~/v2/components/EverBreadcrumbPortal";
// import RBACProtectedComponent from "~/v2/components/RBACWrapper/RBACProtectedComponent";
import { lostAstronaut, dogTearingNewspaper } from "~/v2/images";

import DrawsButton from "./draws-button";

const GET_EMPLOYEE_DRAWS = gql`
  query EmployeeDraws($employeeEmailId: String!) {
    employeeDraws(employeeEmailId: $employeeEmailId) {
      drawYear
      draws {
        drawPeriod
        drawAmount
        drawTypeName
      }
      employeeEmailId
      employee {
        employeeEmailId
        firstName
        lastName
        employeePayroll {
          payoutFrequency
          joiningDate
          effectiveEndDate
        }
      }
    }
  }
`;

const GET_EMPLOYEE = gql`
  query Employee($emailId: String!) {
    employeeQuotaDrawModule(emailId: $emailId) {
      firstName
      lastName
      employeeEmailId
      employeePayroll {
        payoutFrequency
        joiningDate
        effectiveEndDate
      }
    }
  }
`;

const DrawsSummary = observer((props) => {
  const { store } = props;
  const { selectedPayee } = store;
  const [empYear, setEmpYear] = useState();
  const [drawsData, setDrawsData] = useState();
  const [employee, setEmployee] = useState();
  const startMonth = useRecoilValue(myClientAtom).fiscalStartMonthZero;
  const [empFullName, setEmpFullName] = useState("");

  const { hasPermissions } = useUserPermissionStore();

  const { data, loading, refetch } = useQuery(GET_EMPLOYEE_DRAWS, {
    variables: { employeeEmailId: selectedPayee },
    fetchPolicy: "no-cache",
    notifyOnNetworkStatusChange: true,
    skip: selectedPayee === null,
  });

  const { data: empData } = useQuery(GET_EMPLOYEE, {
    variables: { emailId: selectedPayee },
    fetchPolicy: "no-cache",
    skip: selectedPayee === null,
  });

  const RenderDraws = ({
    drawsData,
    empYear,
    store,
    selectedPayee,
    empFullName,
  }) => {
    if (drawsData && empYear) {
      return (
        <div className="py-4 px-6 w-full">
          <div className="flex flex-col gap-6 w-full">
            <DrawsTable data={drawsData} store={store} />
          </div>
        </div>
      );
    } else {
      return (
        <NoDrawsWrapper
          selectedPayee={selectedPayee}
          empFullName={empFullName}
        />
      );
    }
  };

  const NoDrawsWrapper = ({ selectedPayee, empFullName }) => {
    const { t } = useTranslation();
    if (!selectedPayee) {
      return (
        <EverEmptyData
          title="Empower your sales team and fuel their motivation"
          description={t("OFFER_DRAWS")}
          imgSrc={lostAstronaut}
        />
      );
    }

    return (
      <div className="flex items-center justify-center h-full transform">
        <div className="flex flex-col h-full items-center gap-1 justify-center -translate-y-16">
          <img src={dogTearingNewspaper} />
          {hasPermissions(RBAC_ROLES.MANAGE_DRAWS) ? (
            <>
              <EverTg.Heading2 className="text-ever-base-content">
                No draws set for {empFullName}.
              </EverTg.Heading2>
              <EverTg.Text className="text-ever-base-content-mid flex items-center">
                Click
                <DrawsButton
                  employee={employee}
                  fiscalYear={empYear}
                  onFinish={(value) => {
                    setEmpYear(value);
                    refetch();
                  }}
                  buttonType="link"
                  buttonText="Add Draw Schedule"
                />
                to set it up.
              </EverTg.Text>
            </>
          ) : (
            <>
              <EverTg.Heading2 className="text-ever-base-content">
                Don&apos;t see any Draws? Fear not; your admin holds the pen.
              </EverTg.Heading2>
              <EverTg.Text className="text-ever-base-content-mid flex items-center">
                Once your admin sketches some spectacular Draw plans for you,
                you&apos;ll see them here.
              </EverTg.Text>
            </>
          )}
          {/* <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_DRAWS}>
            <div className="flex items-center">
              Click &quot;
              <EverButton size="small" color="base" type="link">
                Add Draw Schedule
              </EverButton>
              &quot; to setup draws.
            </div>
          </RBACProtectedComponent> */}
        </div>
      </div>
    );
  };

  useEffect(() => {
    store.setPeriodSchedule(startMonth);
  }, [startMonth]);

  useEffect(() => {
    if (empData && empData.employeeQuotaDrawModule) {
      setEmpFullName(
        `${empData.employeeQuotaDrawModule.firstName} ${empData.employeeQuotaDrawModule.lastName}`
      );
      setEmployee(empData.employeeQuotaDrawModule);
      let fiscalYearMoment = getClientFiscalYear(startMonth);
      setEmpYear(fiscalYearMoment);
    }
  }, [empData]);

  useEffect(() => {
    if (
      data &&
      data.employeeDraws &&
      data.employeeDraws.length > 0 &&
      empYear
    ) {
      setDrawsData(
        data.employeeDraws.find((o) => o?.drawYear === empYear.format("YYYY"))
      );
    } else {
      setDrawsData(null);
    }
  }, [data, empYear]);

  useEffect(() => {
    if (selectedPayee) {
      setDrawsData();
      refetch();
    }
  }, [selectedPayee]);

  return (
    <>
      {employee && selectedPayee && (
        <EverBreadcrumbPortal dividerIcon={<></>}>
          <div className="flex ml-auto">
            <DrawsButton
              employee={employee}
              fiscalYear={empYear}
              onFinish={(value) => {
                setEmpYear(value);
                refetch();
              }}
            />
          </div>
        </EverBreadcrumbPortal>
      )}
      <div className="flex flex-col h-full">
        <div className="flex items-center pt-2 px-6 flex-wrap sticky top-0">
          {employee && selectedPayee && (
            <div className="flex items-center">
              <EverLabel>Fiscal year</EverLabel>
              <EverDatePicker.Legacy
                picker="year"
                allowClear={false}
                placeholder="Select Year"
                value={empYear}
                onChange={(val) => setEmpYear(val)}
                className="w-22 text-xs"
              />
            </div>
          )}
        </div>
        {loading ? (
          <div className="flex h-[90%] justify-center items-center">
            <EverLoader.SpinnerLottie className="h-12 w-12" />
          </div>
        ) : (
          <RenderDraws
            drawsData={drawsData}
            empYear={empYear}
            store={store}
            selectedPayee={selectedPayee}
            empFullName={empFullName}
          />
        )}
      </div>
    </>
  );
});

export default DrawsSummary;

const DrawsTable = (props) => {
  const { data, store } = props;
  const { periodSchedule } = store;
  const getColumns = (draws) => {
    if (draws) {
      let len = draws.draws.length;
      let cols = [];
      if (len === 1) {
        cols = periodSchedule["Annual"];
      } else if (len === 2) {
        cols = periodSchedule["Halfyearly"];
      } else if (len === 4) {
        cols = periodSchedule["Quarterly"];
      } else if (len === 12) {
        cols = periodSchedule["Monthly"];
      }
      return cols.map((x) => ({
        title: x,
        key: x,
        dataIndex: x,
        align: "center",
        render: (text) => {
          return formatCurrency(text, { defaultReturnValue: "-" });
        },
      }));
    } else {
      return [];
    }
  };
  const cols = getColumns(data);
  const colsWithHeader = [
    {
      title: "",
      key: "rowHeader",
      dataIndex: "rowHeader",
      width: 223,
      fixed: "left",
    },
  ].concat(cols);

  const getRows = (cols, data) => {
    let rows = [];
    let nonRecoverableG = { rowHeader: "Non-recoverable guarantee" };
    let nonRecoverable = { rowHeader: "Non-recoverable" };
    let recoverableG = { rowHeader: "Recoverable guarantee" };
    let recoverable = { rowHeader: "Recoverable" };
    let allowanceAvailed = { rowHeader: "Draw allowance availed" };
    let balanceRecoverable = { rowHeader: "Draw balance recoverable" };
    let balanceSum = 0;
    let allowanceSum = 0;
    let withdrawnAmount = 0;
    let showR,
      showRG,
      showNR,
      showNRG = false;
    cols.forEach((x) => {
      let nonRecoverableAmt = 0;
      let nonRecoverableGAmt = 0;
      let recoverableAmt = 0;
      let recoverableGAmt = 0;
      data.draws.map((drawList) => {
        if (drawList.length > 0) {
          drawList.map((draw) => {
            if (draw.drawPeriod === x.key) {
              if (draw.drawTypeName === "NR") {
                nonRecoverableAmt = parseFloat(draw.drawAmount);
                if (draw.drawAmount !== 0) showNR = true;
              } else if (draw.drawTypeName === "NRG") {
                nonRecoverableGAmt = parseFloat(draw.drawAmount);
                if (draw.drawAmount !== 0) showNRG = true;
              } else if (draw.drawTypeName === "R") {
                recoverableAmt = parseFloat(draw.drawAmount);
                if (draw.drawAmount !== 0) showR = true;
              } else if (draw.drawTypeName === "RG") {
                recoverableGAmt = parseFloat(draw.drawAmount);
                if (draw.drawAmount !== 0) showRG = true;
              }
            }
          });
        }
      });
      nonRecoverable[x.dataIndex] = nonRecoverableAmt;
      nonRecoverableG[x.dataIndex] = nonRecoverableGAmt;
      recoverable[x.dataIndex] = recoverableAmt;
      recoverableG[x.dataIndex] = recoverableGAmt;
      allowanceSum +=
        nonRecoverableAmt +
        nonRecoverableGAmt +
        recoverableAmt +
        recoverableGAmt;
      allowanceAvailed[x.dataIndex] = allowanceSum;
      balanceSum += recoverableAmt + recoverableGAmt;
      balanceRecoverable[x.dataIndex] = balanceSum - withdrawnAmount;
    });
    if (showNR) rows.push(nonRecoverable);
    if (showNRG) rows.push(nonRecoverableG);
    if (showR) rows.push(recoverable);
    if (showRG) rows.push(recoverableG);
    rows.push(allowanceAvailed);
    rows.push(balanceRecoverable);
    return rows;
  };

  return (
    <div className="w-full">
      <Table
        columns={colsWithHeader}
        dataSource={getRows(cols, data)}
        pagination={false}
        scroll={{ x: true }}
        bordered={false}
        size="small"
      />
    </div>
  );
};
