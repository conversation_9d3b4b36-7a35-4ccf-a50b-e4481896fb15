import { InfoCircleIcon } from "@everstage/evericons/outlined";
import { Space } from "antd";
import { isEmpty } from "lodash";
import React from "react";

import { EverCheckbox, EverLabel, EverTooltip } from "~/v2/components";

const PermissionsCheckboxTree = ({
  isRootLevel,
  permissions,
  disabled,
  onPermissionsChange,
  renderBelowPermission,
}) => {
  // TEMPORARY: Special handling for territory plans permissions.
  //
  // Context: "explore:territoryplans" is a sub permission of "view:territoryplans". Currently,
  // we dont want to expose the "view:territoryplans" permissions to end-users but still maintain
  // it internally.
  //
  // Solution: If the first permission is "view:territoryplans", we display only its sub-permissions
  // rather than the permission itself.
  const filteredPermissions =
    permissions?.[0]?.permissionId === "view:territoryplans"
      ? permissions?.[0]?.permissions ?? []
      : permissions;
  return (
    <>
      {filteredPermissions.map((permission) => (
        <div key={permission.permissionId} className="mt-6">
          <EverCheckbox
            checked={permission.value}
            disabled={disabled}
            onChange={(e) => {
              onPermissionsChange(permission.permissionId, e.target.checked);
              // TEMPORARY: Special cascading logic for territory plans permissions.
              // When "explore:territoryplans" is unchecked, automatically uncheck "view:territoryplans" as well
              if (
                permission.permissionId === "explore:territoryplans" &&
                !e.target.checked
              ) {
                onPermissionsChange("view:territoryplans", false);
              }
            }}
          >
            <Space direction="horizontal" className="ml-1">
              <EverLabel
                className={`${
                  isRootLevel ? "font-medium" : ""
                } pointer-events-none text-ever-base-content`}
              >
                {permission.permissionName}
              </EverLabel>
              {!isEmpty(permission.permissionDescription) && (
                <EverTooltip title={permission.permissionDescription}>
                  <div className="flex">
                    <InfoCircleIcon className="w-4 h-4 text-ever-base-content" />
                  </div>
                </EverTooltip>
              )}
            </Space>
          </EverCheckbox>
          {renderBelowPermission(permission)}
          <div className="mt-6 ml-8">
            <PermissionsCheckboxTree
              isRootLevel={false}
              permissions={permission.permissions}
              disabled={disabled}
              onPermissionsChange={onPermissionsChange}
              renderBelowPermission={renderBelowPermission}
            />
          </div>
        </div>
      ))}
    </>
  );
};

export default PermissionsCheckboxTree;
