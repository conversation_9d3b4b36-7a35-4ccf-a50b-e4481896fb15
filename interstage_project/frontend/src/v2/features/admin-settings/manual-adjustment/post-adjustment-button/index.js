import { gql, useQuery } from "@apollo/client";
import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { Dropdown, Menu } from "antd";
import { format, getYear, isValid, parseISO } from "date-fns";
import { debounce, isEmpty, isNil } from "lodash";
import { observer } from "mobx-react";
import React, { useState, useEffect, Fragment, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useQuery as useReactQuery } from "react-query";
import { useRecoilValue } from "recoil";

import { getApprovalConfig } from "~/Api/ApprovalWorkflowService";
import { getPayoutPeriodsForYear } from "~/Api/CommissionActionService";
import { RBAC_ROLES, SEARCH_BOX } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverButton,
  EverButtonGroup,
  EverNewDatePicker,
  EverInput,
  EverLoader,
  EverNumberBadge,
  EverSelect,
  EverTg,
} from "~/v2/components";

import AdjustmentModal from "./AdjustmentModal";
import postAdjustmentStore from "./store";

const LONG_DATE_FORMAT = "dd-MMMM-yyyy";

const GET_PERIOD_LABELS = gql`
  {
    periodLabelList {
      label
      value
    }
  }
`;

const PostAdjustmentButton = observer((props) => {
  const {
    onFinish,
    label,
    setSearchTerm,
    setDrawSearchTerm,
    activeKey,
    filterPeriod,
    setFilterPeriod,
  } = props;
  const store = postAdjustmentStore;
  const [showModal, setShowModal] = useState(false);
  const [adjustmentType, setAdjustmentType] = useState(false);
  const [selectablePeriods, setSelectablePeriods] = useState({});
  const [periodOptions, setPeriodOptions] = useState([]);
  const [activeYear, setActiveYear] = useState(null);

  const { accessToken } = useAuthStore();
  const { hasPermissions } = useUserPermissionStore();
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);

  const customCalendar = clientFeatures?.customCalendar || false;

  const { t } = useTranslation();

  const { loading: periodsLoading } = useQuery(GET_PERIOD_LABELS, {
    fetchPolicy: "no-cache",
    onCompleted: (data) => {
      if (data && data.periodLabelList && !customCalendar) {
        setPeriodOptions([
          { label: "All", value: "all" },
          ...data.periodLabelList,
        ]);
        setFilterPeriod("all");
      }
    },
  });

  const { isLoading } = useReactQuery(
    ["getPayoutPeriodsForYear", customCalendar, activeYear],
    () => getPayoutPeriodsForYear(activeYear, accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      enabled: customCalendar && !isEmpty(activeYear),
      onSuccess: (data) => {
        setSelectablePeriods({
          ...selectablePeriods,
          [activeYear]: data.map((item) => {
            return format(new Date(item), LONG_DATE_FORMAT, {
              timeZone: "UTC",
            });
          }),
        });
      },
    }
  );

  const { data } = useReactQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  const debounceFn = useMemo(
    () =>
      debounce((value) => {
        setSearchTerm(value);
      }, SEARCH_BOX.DEBOUNCE_TIME),
    [setSearchTerm]
  );

  const debounceWrapper = (value) => {
    const searchVal = value.target.value;
    if (
      searchVal.length === 0 ||
      searchVal.length >= SEARCH_BOX.MINIMUM_CHARS
    ) {
      debounceFn(searchVal);
    }
  };

  useEffect(() => {
    if (accessToken) {
      store.setAccessToken(accessToken);
    }
  }, [accessToken]);

  useEffect(() => {
    if (isEmpty(selectablePeriods)) {
      setActiveYear(String(getYear(new Date())));
    }
  }, []);

  const menu = (
    <Menu
      onClick={(e) => {
        setAdjustmentType(e.key);
        setShowModal(true);
      }}
    >
      {hasPermissions(RBAC_ROLES.MANAGE_COMMISSIONADJUSTMENT) && (
        <Menu.Item key="commission">
          {" "}
          <EverTg.Text className="text-ever-base-content">
            {t("COMMISSION")}
          </EverTg.Text>
        </Menu.Item>
      )}
      {hasPermissions(RBAC_ROLES.MANAGE_DRAWS) && (
        <Menu.Item key="drawRecover">
          <EverTg.Text className="text-ever-base-content">
            Recover Draw
          </EverTg.Text>
        </Menu.Item>
      )}
    </Menu>
  );

  const updateActiveYear = (year) => {
    if (isNil(selectablePeriods[year])) {
      setActiveYear(String(year));
    }
  };

  function disabledDate(current) {
    if (isEmpty(selectablePeriods[getYear(current)])) {
      return true;
    }
    return !selectablePeriods[getYear(current)].includes(
      format(current, LONG_DATE_FORMAT)
    );
  }

  return (
    <Fragment>
      <div className="flex gap-3 justify-end">
        {activeKey == "1" && (
          <>
            <div className="flex gap-3 items-center">
              {customCalendar ? (
                <>
                  <EverTg.Text>Show Period</EverTg.Text>
                  <EverNewDatePicker
                    allowClear={false}
                    wrapperClassname="h-9"
                    defaultValue={
                      isValid(parseISO(filterPeriod))
                        ? parseISO(filterPeriod)
                        : null
                    }
                    disabledDate={disabledDate}
                    showToday={false}
                    onChange={(date) => setFilterPeriod(date)}
                    onPanelChange={(date) => updateActiveYear(getYear(date))}
                    size="small"
                    isPickerLoading={isLoading}
                    renderExtraFooter={() => (
                      <>
                        {isLoading ? (
                          <div className="absolute top-0 left-0 h-full w-full">
                            <EverLoader
                              wrapperClassName="absolute top-0 left-0 z-20"
                              indicatorType="spinner"
                            />
                          </div>
                        ) : null}
                      </>
                    )}
                  />
                </>
              ) : (
                <div className="flex gap-2 items-center">
                  <EverSelect
                    className="w-44"
                    value={filterPeriod}
                    onChange={(val) => setFilterPeriod(val)}
                    options={periodOptions}
                    size="small"
                    suffixIcon={
                      periodsLoading ? (
                        <EverLoader.SpinnerLottie />
                      ) : (
                        <ChevronDownIcon className="w-4 h-4 text-ever-base-content-low" />
                      )
                    }
                  />
                </div>
              )}
            </div>
            {data?.data?.commissionAdjustmentApprovals?.enabled && (
              <EverButtonGroup
                className="bg-ever-base-200"
                activeBtnType="text"
                activeBtnColor="primary"
                defActiveBtnIndex={store.tableFilter === "requested" ? 1 : 0}
                size="small"
                activeButtonClassname="!pr-1"
                inactiveButtonClassname="!pr-1"
              >
                <EverButton
                  onClick={() => {
                    setSearchTerm("");
                    store.setTableFilter("all");
                  }}
                >
                  <div className="flex flex-row gap-2 items-center">
                    All
                    <EverNumberBadge count={store.adjustmentCounts?.all || 0} />
                  </div>
                </EverButton>
                <EverButton
                  onClick={() => {
                    setSearchTerm("");
                    store.setTableFilter("requested");
                  }}
                >
                  <div className="flex flex-row gap-2 items-center">
                    Pending Requests
                    <EverNumberBadge
                      count={store.adjustmentCounts?.pending || 0}
                    />
                  </div>
                </EverButton>
              </EverButtonGroup>
            )}
            <EverInput.Search
              placeholder="Search"
              className="w-64"
              onChange={(e) => debounceWrapper(e)}
              size="small"
            />
          </>
        )}
        {activeKey == "2" && (
          <EverInput.Search
            placeholder="Search"
            className="w-64"
            onChange={(event) => setDrawSearchTerm(event.target.value)}
            size="small"
          />
        )}
        <Dropdown trigger="click" overlay={menu}>
          <EverButton
            className="!mr-1"
            appendIcon={<ChevronDownIcon />}
            size="small"
          >
            {label}
          </EverButton>
        </Dropdown>
      </div>
      <AdjustmentModal
        showModal={showModal}
        setShowModal={setShowModal}
        adjustmentType={adjustmentType}
        store={store}
        onFinish={onFinish}
        approvalConfig={data?.data}
      />
    </Fragment>
  );
});

export default PostAdjustmentButton;
