import { makeAutoObservable } from "mobx";

class DataSyncStore {
  supportUserEmail = null;
  selectedDatabooks = [];
  selectedObject = "";
  currentJob = "";
  databookButtonLoading = false;
  connectorButtonLoading = false;
  taskDetail = {};
  databookButtonStatus = true;
  databookOption = "all-databooks";
  copyObjectsOption = "both";
  copyFromDate = null;
  copyButtonStatus = true;
  copyButtonLoading = false;
  connections = [];
  drawerVisible = false;
  statusBarVisible = false;
  isModalVisible = false;
  detailedStatus = {};
  reportEtlButtonLoading = false;
  reportObjectId = null;
  taskStatus = false;
  startSync = false;
  onPageLoad = true;
  selectedConnectors = [];
  connectorOption = "all-connectors";
  connectorButtonStatus = true;
  upstreamSyncFlag = false;
  upstreamRunPrevPeriodFlag = false;
  hardDeleteSyncFlag = false;
  reportEtlMode = "custom";
  selectedRoEtlPayeeList = [];
  roEtlPayeeOption = "all-payees";
  reportEtlDate = null;
  enableReportEtlButton = false;
  isDateRequired = false;
  haveFivetranSync = false;
  isConnectorSync = false;

  //commission-settlement-forecast
  commissionDate = null;
  commissionEndDate = null;
  settlementDate = null;
  forecastDate = null;

  commissionPayeeOption = "all-payees";
  settlementPayeeOption = "all-payees";
  forecastPayeeOption = "all-payees";

  commissionSelectedPayeeList = [];
  settlementSelectedPayeeList = [];
  forecastSelectedPayeeList = [];

  commissionSelectedPlans = [];
  settlementSelectedPlans = [];
  forecastSelectedPlans = [];

  commissionButtonStatus = true;
  settlementButtonStatus = true;
  forecastButtonStatus = true;

  commissionButtonLoading = false;
  settlementButtonLoading = false;
  forecastButtonLoading = false;

  commissionRefreshDataBooks = false;
  settlementRefreshDataBooks = false;
  forecastRefreshDataBooks = false;

  commissionRunPrevPeriod = false;
  forecastRunPrevPeriod = false;

  constructor() {
    makeAutoObservable(this);
  }

  setCopyButtonLoading = (value) => {
    this.copyButtonLoading = value;
  };

  setCopyButtonStatus = (value) => {
    this.copyButtonStatus = value;
  };

  setCopyFromDate = (value) => {
    this.copyFromDate = value;
  };

  setCopyObjectsOption = (value) => {
    this.copyObjectsOption = value;
  };

  setOnPageLoad = (value) => {
    this.onPageLoad = value;
  };

  setStartSync = (value) => {
    this.startSync = value;
  };
  setTaskStatus = (value) => {
    this.taskStatus = value;
  };

  setDetailedStatus = (value) => {
    this.detailedStatus = value;
  };

  setStatusBarVisible = (value) => {
    this.statusBarVisible = value;
  };
  setDrawerVisible = (value) => {
    this.drawerVisible = value;
  };
  setIsModalVisible = (value) => {
    this.isModalVisible = value;
  };
  setConnections = (value) => {
    this.connections = value;
  };

  setSupportUserEmail = (value) => {
    this.supportUserEmail = value;
  };

  setDatabookOption = (value) => {
    this.databookOption = value;
  };

  setSelectedDatabooks = (value) => {
    this.selectedDatabooks = value;
  };
  setSelectedObject = (value) => {
    this.selectedObject = value;
  };
  setCurrentJob = (value) => {
    this.currentJob = value;
  };

  setDatabookButtonLoading = (value) => {
    this.databookButtonLoading = value;
  };
  setConnectorButtonLoading = (value) => {
    this.connectorButtonLoading = value;
  };
  setReportEtlButtonLoading = (value) => {
    this.reportEtlButtonLoading = value;
  };
  setTaskDetail = (value) => {
    this.taskDetail = value;
  };

  setDatabookButtonStatus = (value) => {
    this.databookButtonStatus = value;
  };
  setReportObjectId = (value) => {
    this.reportObjectId = value;
  };
  setSelectedConnectors = (value) => {
    this.selectedConnectors = value;
  };
  setConnectorOption = (value) => {
    this.connectorOption = value;
  };
  setConnectorButtonStatus = (value) => {
    this.connectorButtonStatus = value;
  };
  setUpstreamSyncFlag = (value) => {
    this.upstreamSyncFlag = value;
  };
  setUpstreamRunPrevPeriodFlag = (value) => {
    this.upstreamRunPrevPeriodFlag = value;
  };
  setHardDeleteSyncFlag = (value) => {
    this.hardDeleteSyncFlag = value;
  };

  setReportEtlMode = (value) => {
    this.reportEtlMode = value;
  };
  setSelectedRoEtlPayeeList = (value) => {
    this.selectedRoEtlPayeeList = value;
  };
  setRoEtlPayeeOption = (value) => {
    this.roEtlPayeeOption = value;
  };
  setReportEtlDate = (value) => {
    this.reportEtlDate = value;
  };
  setEnableReportEtlButton = (value) => {
    this.enableReportEtlButton = value;
  };
  setIsDateRequired = (value) => {
    this.isDateRequired = value;
  };

  //commission-settlement-forecast
  setCommissionDate = (value) => {
    this.commissionDate = value;
  };
  setCommissionEndDate = (value) => {
    this.commissionEndDate = value;
  };
  setSettlementDate = (value) => {
    this.settlementDate = value;
  };
  setForecastDate = (value) => {
    this.forecastDate = value;
  };

  setCommissionSelectedPlans = (value) => {
    this.commissionSelectedPlans = value;
  };
  setSettlementSelectedPlans = (value) => {
    this.settlementSelectedPlans = value;
  };
  setForecastSelectedPlans = (value) => {
    this.forecastSelectedPlans = value;
  };

  setCommissionPayeeOption = (value) => {
    this.commissionPayeeOption = value;
  };
  setSettlementPayeeOption = (value) => {
    this.settlementPayeeOption = value;
  };
  setForecastPayeeOption = (value) => {
    this.forecastPayeeOption = value;
  };

  setCommissionSelectedPayeeList = (value) => {
    this.commissionSelectedPayeeList = value;
  };
  setSettlementSelectedPayeeList = (value) => {
    this.settlementSelectedPayeeList = value;
  };
  setForecastSelectedPayeeList = (value) => {
    this.forecastSelectedPayeeList = value;
  };

  setCommissionRefreshDataBooks = (value) => {
    this.commissionRefreshDataBooks = value;
  };
  setSettlementRefreshDataBooks = (value) => {
    this.settlementRefreshDataBooks = value;
  };
  setForecastRefreshDataBooks = (value) => {
    this.forecastRefreshDataBooks = value;
  };

  setCommissionButtonStatus = (value) => {
    this.commissionButtonStatus = value;
  };
  setSettlementButtonStatus = (value) => {
    this.settlementButtonStatus = value;
  };
  setForecastButtonStatus = (value) => {
    this.forecastButtonStatus = value;
  };

  setCommissionButtonLoading = (value) => {
    this.commissionButtonLoading = value;
  };
  setSettlementButtonLoading = (value) => {
    this.settlementButtonLoading = value;
  };
  setForecastButtonLoading = (value) => {
    this.forecastButtonLoading = value;
  };

  setCommissionRunPrevPeriod = (value) => {
    this.commissionRunPrevPeriod = value;
  };
  setForecastRunPrevPeriod = (value) => {
    this.forecastRunPrevPeriod = value;
  };

  setHaveFivetranSync = (value) => {
    this.haveFivetranSync = value;
  };

  setIsConnectorSync = (value) => {
    this.isConnectorSync = value;
  };

  resetStore = () => {
    this.setCommissionDate(null);
    this.setCommissionEndDate(null);
    this.setSettlementDate(null);
    this.setForecastDate(null);

    this.setSettlementPayeeOption("all-payees");
    this.setCommissionPayeeOption("all-payees");
    this.setForecastPayeeOption("all-payees");

    this.setCommissionRefreshDataBooks(false);
    this.setSettlementRefreshDataBooks(false);
    this.setForecastRefreshDataBooks(false);

    this.setCommissionRunPrevPeriod(false);
    this.setForecastRunPrevPeriod(false);

    this.setCommissionSelectedPayeeList([]);
    this.setSettlementSelectedPayeeList([]);
    this.setForecastSelectedPayeeList([]);

    this.setCommissionSelectedPlans([]);
    this.setSettlementSelectedPlans([]);
    this.setForecastSelectedPlans([]);

    this.setCommissionButtonStatus(true);
    this.setSettlementButtonStatus(true);
    this.setForecastButtonStatus(true);

    this.setDatabookOption("all-databooks");
    this.setSelectedDatabooks([]);
    this.setStartSync(false);
    this.setSelectedObject("");
    this.setReportObjectId(null);
    this.setTaskStatus(false);
    this.setCopyFromDate(null);
    this.setHaveFivetranSync(false);
    this.setIsConnectorSync(false);
  };
  resetButtonLoading = () => {
    this.setCommissionButtonLoading(false);
    this.setSettlementButtonLoading(false);
    this.setForecastButtonLoading(false);

    this.setDatabookButtonLoading(false);
    this.setConnectorButtonLoading(false);
    this.setReportEtlButtonLoading(false);
    this.setCopyButtonLoading(false);
  };
}

export default DataSyncStore;
