import { AlertTriangleIcon, CheckIcon } from "@everstage/evericons/outlined";
import { AgGridReact } from "ag-grid-react";
import moment from "moment-timezone";
import PropTypes from "prop-types";
import React from "react";
import ReactHTMLParser from "react-html-parser";

import { EverTooltip, EverBadge } from "~/v2/components";
import {
  everAgGridOptions,
  everAgGridCallbacks,
} from "~/v2/components/ag-grid";

const { getDefaultOptions } = everAgGridOptions;

const statusColorCode = {
  completed: "text-ever-success-content-lite",
  partiallyfailed: "text-ever-warning-content-lite",
  failed: "text-ever-error-content-lite",
};

const renderLogo = (status) => {
  switch (status) {
    case "partially failed":
      return <AlertTriangleIcon />;
    case "failed":
      return <AlertTriangleIcon />;
    case "completed":
      return <CheckIcon />;
    default:
      return "";
  }
};

/**
 * DetailedActivityViewProps definition.
 * @typedef DetailedActivityViewProps
 * @property {GroupedSyncRunLog[]} groupedSyncRunLog
 * @property {string} userTimezone
 */

/**
 * @component
 * @param {DetailedActivityViewProps} props
 * @returns {import("react").ReactElement}
 */

const durationFormatter = (rowData, timezone) => {
  const data = rowData.data;
  if (data?.end_time && data?.start_time) {
    const duration = moment.duration(
      moment(data.end_time)
        .tz(timezone)
        .diff(moment(data.start_time).tz(timezone))
    );
    let secs = Math.floor(duration.asSeconds());
    let mins = 0;
    let hrs = 0;
    if (secs > 59) {
      mins = Math.floor(secs / 60);
      secs = secs % 60;
      if (mins > 59) {
        hrs = Math.floor(mins / 60);
        mins = mins % 60;
      }
    }

    const hours = (hrs && `${hrs}h `) || "";
    const mintus = (mins && `${mins}m `) || "";
    return `${hours}${mintus}${secs}s`;
  } else {
    return "-";
  }
};

const DetailedActivityView = ({ groupedSyncRunLog, userTimezone }) => {
  const columnDefs = [
    {
      field: "task",
      headerName: "Task",
      cellRenderer: "agGroupCellRenderer",
    },
    {
      field: "",
      headerName: "Status",
      cellRenderer: (rowData) => (
        <>
          {rowData.data.status == "failed" ? (
            <EverBadge
              title="Failed"
              icon={renderLogo("failed")}
              type="error"
            />
          ) : rowData.data.status == "partially_failed" ? (
            <EverBadge
              title="Partially Failed"
              icon={renderLogo("partially failed")}
              type="warning"
            />
          ) : (
            <EverBadge
              title="Completed"
              icon={renderLogo("completed")}
              type="success"
            />
          )}
        </>
      ),
      valueFormatter: (rowData) => {
        const status = rowData.data?.status;

        if (status === "failed") return "Failed";
        if (status === "partially_failed") return "Partially Failed";
        return "Completed";
      },
    },
    {
      field: "start_time",
      headerName: "Start time (UTC)",
      cellRenderer: (rowData) => {
        const value = rowData.value;
        if (value) {
          return moment(value).tz(userTimezone).format("lll");
        }
        return "-";
      },
    },
    {
      field: "end_time",
      headerName: "End time (UTC)",
      cellRenderer: (rowData) => {
        const value = rowData.value;
        if (value) {
          return moment(value).tz(userTimezone).format("lll");
        }
        return "-";
      },
    },
    {
      field: "",
      headerName: "Duration",
      cellRenderer: (rowData) => durationFormatter(rowData, userTimezone),
      valueFormatter: (rowData) => durationFormatter(rowData, userTimezone),
    },
  ];

  const detailCellRendererParams = {
    detailGridOptions: {
      rowSelection: "multiple",
      columnDefs: [
        {
          field: "task",
          headerName: "Task",
        },
        {
          field: "status",
          headerName: "Status",
          cellRenderer: (rowData) => {
            const value = rowData.value;
            let showToolTip = false;
            if (value) {
              const status = value.toString().toLowerCase().replace(" ", "");
              const colorCode = statusColorCode[status];
              if (status === "partiallyfailed" || status === "failed") {
                showToolTip = true;
              }
              const result = (
                <div className={`flex mb-0 mr-10 gap-1 ${colorCode}`}>
                  <EverBadge
                    title={
                      value == "completed"
                        ? "Completed"
                        : value === "failed"
                        ? "Failed"
                        : value === "skipped"
                        ? "Skipped"
                        : "Partially Failed"
                    }
                    icon={renderLogo(value)}
                    type={
                      value == "completed"
                        ? "success"
                        : value === "failed"
                        ? "error"
                        : "warning"
                    }
                  />
                </div>
              );
              return showToolTip ? (
                <EverTooltip
                  mouseEnterDelay={0.5}
                  title={"Reach Everstage support for assistance"}
                >
                  {result}
                </EverTooltip>
              ) : (
                result
              );
            }
            return "-";
          },
        },
        {
          field: "start_time",
          headerName: "Start time (UTC)",
          cellRenderer: (rowData) => {
            const value = rowData.value;
            if (value) {
              return moment(value).tz(userTimezone).format("lll");
            }
            return "-";
          },
        },
        {
          field: "end_time",
          headerName: "End time (UTC)",
          cellRenderer: (rowData) => {
            const value = rowData.value;
            if (value) {
              return moment(value).tz(userTimezone).format("lll");
            }
            return "-";
          },
        },
        {
          field: "",
          headerName: "Duration",
          cellRenderer: (rowData) => durationFormatter(rowData, userTimezone),
          valueFormatter: (rowData) => durationFormatter(rowData, userTimezone),
        },
        {
          field: "upstream_deleted_fields",
          headerName: "Upstream deleted fields",
          cellRenderer: (rowData) => {
            const fields = rowData?.data?.upstream_deleted_fields;
            if (Array.isArray(fields) && fields.length > 0) {
              return fields.join(", ");
            }
            return "-";
          },
        },
        {
          field: "error_info",
          headerName: "Error message",
          cellRenderer: (rowData) => {
            return ReactHTMLParser(rowData.data.error_info);
          },
        },
        {
          field: "error_details",
          headerName: "Error Details",
          cellRenderer: (rowData) => {
            return ReactHTMLParser(
              rowData?.data?.ai_parsed_error?.detailed_message
            );
          },
        },
      ],
      defaultColDef: {
        sortable: true,
      },
      onFirstDataRendered: (params) => {
        everAgGridCallbacks.adjustColumnWidth(params);
      },
    },

    getDetailRowData: (params) => {
      params.successCallback(params.data.error_tasks);
    },
  };

  return (
    <div className="ag-theme-material zebra-grid h-screen w-full">
      <AgGridReact
        {...getDefaultOptions({ type: "sm" })}
        columnDefs={columnDefs}
        rowData={groupedSyncRunLog}
        masterDetail={true}
        detailCellRendererParams={detailCellRendererParams}
      />
    </div>
  );
};

DetailedActivityView.propTypes = {
  groupedSyncRunLog: PropTypes.arrayOf(
    PropTypes.shape({
      task: PropTypes.string,
      status: PropTypes.string,
      start_time: PropTypes.string,
      end_time: PropTypes.string,
      error_info: PropTypes.string,
      error_tasks: PropTypes.arrayOf(
        PropTypes.shape({
          task: PropTypes.string,
          status: PropTypes.string,
          start_time: PropTypes.string,
          end_time: PropTypes.string,
          error_info: PropTypes.string,
        })
      ),
    }).isRequired
  ),
  userTimezone: PropTypes.string,
};

export default DetailedActivityView;
