import { useQuery } from "@apollo/client";
import { ClockRewindIcon } from "@everstage/evericons/outlined";
import { observer, useLocalStore } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useRecoilValue } from "recoil";

import { RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { EverModal, EverDrawer, EverTg } from "~/v2/components";

import { DataSyncView } from "./data-sync-view";
import { DetailedView } from "./DetailedView";
import { GET_PROFILE_DETAILS } from "./graphql";
import { PastActivities } from "./PastActivities";
import { StatusBar } from "./StatusBar";
import DataSyncStore from "./store";
import { UserEmail } from "./UserEmail";

const dataSyncSettings = observer((props) => {
  const store = useLocalStore(() => new DataSyncStore(), props);
  const {
    taskDetail,
    setSupportUserEmail,
    statusBarVisible,
    setStatusBarVisible,
    detailedStatus,
    isModalVisible,
    setIsModalVisible,
    haveFivetranSync,
    isConnectorSync,
    setIsConnectorSync,
  } = store;
  const { hasPermissions } = useUserPermissionStore();
  const [userTimeZone, setUserTimeZone] = React.useState("UTC");
  const [drawerVisible, setDrawerVisible] = React.useState(false);

  const [detailViewVisible, setDetailViewVisible] = useState(false);
  const queryParams = new URLSearchParams(useLocation()?.search || "");

  const { data } = useQuery(GET_PROFILE_DETAILS, {
    fetchPolicy: "no-cache",
  });

  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);

  useEffect(() => {
    if (data?.payeeProfileDetails) {
      const profileData = JSON.parse(data.payeeProfileDetails);
      const { timezone } = profileData;
      if (timezone.includes("GMT")) {
        setUserTimeZone(timezone.split(" ")[1]);
      } else {
        setUserTimeZone(timezone);
      }
    }
  }, [data]);

  useEffect(() => {
    if (queryParams.get("show-recent-activites")) {
      setDrawerVisible(true);
    }
  }, []);

  const handleStatusBarVisible = (value) => {
    setStatusBarVisible(value);
  };

  const handleDetailViewVisible = () => {
    setDetailViewVisible(!detailViewVisible);
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    setIsConnectorSync(false);
  };

  const handleModalSubmit = (email) => {
    setSupportUserEmail(email);
    setIsModalVisible(false);
    setIsConnectorSync(false);
  };

  const handleSkipAndRun = () => {
    setSupportUserEmail("");
    setIsModalVisible(false);
    setIsConnectorSync(false);
  };

  return (
    <div className="commission-data-sync-page">
      <UserEmail
        isModalVisible={isModalVisible}
        handleModalCancel={handleModalCancel}
        handleModalSubmit={handleModalSubmit}
        handleSkipAndRun={handleSkipAndRun}
        haveFivetranSync={haveFivetranSync}
        isConnectorSync={isConnectorSync}
      />
      <EverDrawer
        title="Past Activities"
        placement="right"
        width={1360}
        destroyOnClose={true}
        className={"past-activites-drawer"}
        onClose={() => {
          setDrawerVisible(false);
        }}
        visible={drawerVisible}
      >
        <PastActivities userTimeZone={userTimeZone} />
      </EverDrawer>
      <div className={"mt-3"}>
        {statusBarVisible &&
          (!clientFeatures.runSyncForMultiplePeriod ||
            !hasPermissions(RBAC_ROLES.MANAGE_MULTI_PERIOD_SYNC) ||
            taskDetail.task != "Commission calculation") && (
            <StatusBar
              taskDetail={taskDetail}
              handleStatusBarVisible={handleStatusBarVisible}
              handleDetailViewVisible={handleDetailViewVisible}
              userTimeZone={userTimeZone}
            />
          )}
        <div className="flex justify-between items-center w-[700px]">
          <EverTg.Heading2>{`Here's what you can do`}</EverTg.Heading2>
          <div
            className="flex items-center gap-2 cursor-pointer"
            onClick={async () => {
              setDrawerVisible(true);
            }}
          >
            <ClockRewindIcon className="w-5 h-5" />
            <EverTg.Text>show past activities</EverTg.Text>
          </div>
        </div>
        <DataSyncView store={store} />

        <EverModal
          title="Detailed View"
          visible={detailViewVisible}
          onOk={handleDetailViewVisible}
          onCancel={handleDetailViewVisible}
        >
          <DetailedView detailedStatus={detailedStatus} />
        </EverModal>
      </div>
    </div>
  );
});

export default dataSyncSettings;
