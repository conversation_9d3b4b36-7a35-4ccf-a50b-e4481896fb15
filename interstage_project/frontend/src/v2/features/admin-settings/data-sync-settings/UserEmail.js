import React, { useState } from "react";

import {
  EverButton,
  EverModal,
  EverInput,
  EverTg,
  Space,
} from "~/v2/components";

export const UserEmail = (props) => {
  const {
    isModalVisible,
    handleModalCancel,
    handleModalSubmit,
    handleSkipAndRun,
    haveFivetranSync,
    isConnectorSync,
  } = props;
  const [isDisabled, setIsDisabled] = useState(true);
  const [email, setEmail] = useState("");
  return (
    <EverModal
      title="Notify when complete"
      visible={isModalVisible}
      onCancel={handleModalCancel}
      footer={null}
    >
      <Space direction="vertical">
        <EverTg.Description>
          Enter Email ID of the person who needs to be notified once job is
          complete
        </EverTg.Description>
        {isConnectorSync && haveFivetranSync && (
          <EverTg.Caption.Medium>
            Note: It may take longer than usual as this will trigger fivetran
            sync as well.
          </EverTg.Caption.Medium>
        )}
        <div className="text-ever-error-content">
          <EverInput
            type="email"
            onChange={(event) => {
              const email = event.target.value;
              if (
                new RegExp(/[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,15}/g).test(
                  email
                )
              ) {
                setIsDisabled(false);
                setEmail(email);
              } else {
                setIsDisabled(true);
              }
            }}
          />
        </div>
        <div className="flex justify-between p-0">
          <EverButton type="link" htmlType="button" onClick={handleSkipAndRun}>
            {`Skip & Run`}
          </EverButton>
          <EverButton
            disabled={isDisabled}
            onClick={() => handleModalSubmit(email)}
          >
            Submit
          </EverButton>
        </div>
      </Space>
    </EverModal>
  );
};
