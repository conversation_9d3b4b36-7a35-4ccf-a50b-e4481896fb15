import { useQuery } from "@apollo/client";
import { observer } from "mobx-react";
import React, { useEffect, useState } from "react";

import { RBAC_ROLES } from "~/Enums";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  EverSelect,
  EverButton,
  EverCheckbox,
  EverRadio,
  Space,
} from "~/v2/components";

import { CONNECTION_LIST, DETAILED_SYNC_STATUS } from "../graphql";
import { runWrapperSync } from "../restApi";

export const ConnectorSync = observer((props) => {
  const {
    renderPreparingText,
    isValidUser,
    store,
    handleError,
    startPolling,
    prevPeriodLabel,
    clientFeatures,
  } = props;

  const {
    connections,
    taskStatus,
    connectorButtonLoading,
    setDetailedStatus,
    setConnections,
    setStatusBarVisible,
    supportUserEmail,
    setIsModalVisible,
    setCurrentJob,
    currentJob,
    setConnectorButtonLoading,
    setTaskStatus,
    setStartSync,
    taskDetail,
    selectedConnectors,
    setSelectedConnectors,
    connectorOption,
    setConnectorOption,
    connectorButtonStatus,
    setConnectorButtonStatus,
    upstreamSyncFlag,
    setUpstreamSyncFlag,
    hardDeleteSyncFlag,
    setHardDeleteSyncFlag,
    setHaveFivetranSync,
    upstreamRunPrevPeriodFlag,
    setUpstreamRunPrevPeriodFlag,
    setIsConnectorSync,
  } = store;
  const { accessToken, email } = useAuthStore();
  const { hasPermissions } = useUserPermissionStore();

  const [hardDeleteSyncIntegrationIds, setHardDeleteSyncIntegrationIds] =
    useState([]);

  const { data: connectionList } = useQuery(CONNECTION_LIST, {
    fetchPolicy: "no-cache",
  });

  useEffect(() => {
    if (supportUserEmail !== null) {
      if (currentJob === "wrapper-sync") {
        handleWrapperSync();
      }
    }
  }, [supportUserEmail]);

  useEffect(() => {
    if (
      (taskDetail?.task === "End to End Sync" ||
        taskDetail?.task === "Upstream Sync") &&
      hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS)
    )
      connectorStartPolling(5000);
  }, [taskDetail, hasPermissions]);

  const {
    startPolling: connectorStartPolling,
    stopPolling: connectorStopPolling,
  } = useQuery(DETAILED_SYNC_STATUS, {
    fetchPolicy: "no-cache",
    notifyOnNetworkStatusChange: true,
    skip:
      !hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS) ||
      currentJob === "" ||
      currentJob !== "wrapper-sync",
    onCompleted: (res) => {
      if (res?.detailedSyncStatus && currentJob === "wrapper-sync") {
        const status = JSON.parse(res.detailedSyncStatus);
        setDetailedStatus(status);
        if (status.sync_end_time !== "None") {
          connectorStopPolling();
        }
      } else {
        connectorStopPolling();
      }
    },
    onError: (error) => {
      console.log(error);
    },
  });

  useEffect(() => {
    if (connectionList?.connectedObjects) {
      setConnections(connectionList.connectedObjects);
      const hardDeleteSyncObjects = connectionList.connectedObjects.filter(
        (obj) => obj?.hasHardDeleteSync
      );
      const hardDeleteSyncIntegrationIds = hardDeleteSyncObjects.map(
        (obj) => obj?.integrationId
      );
      setHardDeleteSyncIntegrationIds(hardDeleteSyncIntegrationIds);
      checkHaveFivetranSync(
        new Set(
          connectionList.connectedObjects.map((obj) => obj?.integrationId)
        )
      );
    }
  }, [connectionList]);

  /**
   * Checks if any of the provided connector integration IDs have Fivetran sync.
   * @param {Set<number>} connectorIntegrationIds - A set of integration IDs to check.
   */
  const checkHaveFivetranSync = (connectorIntegrationIds) => {
    if (!clientFeatures?.fivetranSync) {
      return;
    }
    const haveFivetranSync = connectionList.connectedObjects.some(
      (obj) =>
        connectorIntegrationIds.has(obj?.integrationId) && obj?.isFivetranSync
    );
    setHaveFivetranSync(haveFivetranSync || false);
  };

  const handleWrapperSync = async () => {
    setStatusBarVisible(false);
    setCurrentJob("wrapper-sync");
    if (isValidUser() && supportUserEmail === null) {
      setIsConnectorSync(true);
      setIsModalVisible(true);
    } else {
      setConnectorButtonLoading(true);
      const response = await runWrapperSync(accessToken, {
        email: isValidUser() ? supportUserEmail : email,
        allObjectsFlag: connectorOption === "all-connectors" ? true : false,
        integrationIds: selectedConnectors,
        upstreamSyncFlag: upstreamSyncFlag,
        hardDeleteSyncFlag: hardDeleteSyncFlag,
        runPreviousPeriodSync: upstreamRunPrevPeriodFlag,
      });
      setTaskStatus(true);

      if (response.ok) {
        startPolling(5000);
        setStartSync(true);
        if (hasPermissions(RBAC_ROLES.MANAGE_ALL_ADMINS))
          connectorStartPolling(5000);
      } else {
        setTaskStatus(false);
        setConnectorButtonLoading(false);
        handleError(response, "Connector sync task submission failed");
      }
    }
  };

  return (
    <div className="pl-5 pt-3">
      <Space direction="vertical" size="middle">
        <EverRadio.Group
          onChange={(event) => {
            setConnectorOption(event.target.value);
            setSelectedConnectors([]);
            if (event.target.value !== "all-connectors") {
              setConnectorButtonStatus(true);
            } else {
              setConnectorButtonStatus(false);
              setHardDeleteSyncFlag(false);
              checkHaveFivetranSync(
                new Set(
                  connectionList.connectedObjects.map(
                    (obj) => obj?.integrationId
                  )
                )
              );
            }
          }}
          value={connectorOption}
          className="flex gap-3 !mb-0"
        >
          <EverRadio value={"all-connectors"} label="All Connectors" />
          <EverRadio
            value={"selected-connectors"}
            label="Selected Connectors"
          />
        </EverRadio.Group>
        {connectorOption === "selected-connectors" && (
          <EverSelect
            mode="multiple"
            maxTagCount={3}
            showSearch
            placeholder="Select connectors"
            className=""
            filterOption={(input, option) => {
              return option.label.toLowerCase().includes(input.toLowerCase());
            }}
            options={connections.map((option) => {
              return {
                label: `${option.name} (${option.serviceName})`,
                value: option.integrationId,
              };
            })}
            onChange={(value) => {
              setHardDeleteSyncFlag(false);
              if (value.length > 0) {
                setConnectorButtonStatus(false);
              } else {
                setConnectorButtonStatus(true);
              }
              setSelectedConnectors(value);
              if (value.length > 1) {
                setHardDeleteSyncFlag(false);
              }
              checkHaveFivetranSync(new Set(value));
            }}
            value={selectedConnectors}
            getPopupContainer={(trigger) => trigger.parentNode}
          />
        )}
        <div className="flex">
          <EverCheckbox
            checked={upstreamSyncFlag}
            onChange={(event) => {
              setUpstreamSyncFlag(event.target.checked);
              if (!event.target.checked) {
                setHardDeleteSyncFlag(false);
              } else {
                setUpstreamRunPrevPeriodFlag(false);
              }
            }}
            label={`Run only upstream sync`}
          />
          {upstreamSyncFlag &&
            selectedConnectors.length === 1 &&
            hardDeleteSyncIntegrationIds.some(
              (item) => selectedConnectors.indexOf(item) !== -1
            ) &&
            connectorOption === "selected-connectors" && (
              <EverCheckbox
                checked={hardDeleteSyncFlag}
                onChange={(event) => {
                  setHardDeleteSyncFlag(event.target.checked);
                }}
                label={`Additionally manage hard deletes during sync`}
              />
            )}
          {!upstreamSyncFlag && (
            <EverCheckbox
              checked={upstreamRunPrevPeriodFlag}
              onChange={(event) => {
                setUpstreamRunPrevPeriodFlag(event.target.checked);
              }}
              label={prevPeriodLabel}
            />
          )}
        </div>
        <div className="flex">
          <EverButton
            onClick={handleWrapperSync}
            //loading={connectorButtonLoading}
            disabled={
              taskStatus
                ? true
                : connectorOption === "all-connectors"
                ? false
                : connectorButtonStatus
            }
          >
            Run
          </EverButton>
          {connectorButtonLoading && renderPreparingText()}
        </div>
      </Space>
    </div>
  );
});
