import { Col, Row, Space } from "antd";
import { observer } from "mobx-react";
import PropTypes from "prop-types";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import {
  EverButton,
  EverChip,
  EverLabel,
  EverModal,
  EverSelect,
  EverTg,
} from "~/v2/components";

import reportEnrichmentFilterStore from "./store";

const { Option } = EverSelect;

const selectTags = ({ label }) => (
  <EverChip size="small" title={label} iconClassName="hidden" />
);

const FiltersDrawer = ({
  visible,
  setVisible,
  datasheetIdToNameMap,
  commPlanIdToNameMap,
  criteriaIdToNameMap,
  databookIdToNameMap,
  clearSelection,
}) => {
  const { filters, setFilters } = reportEnrichmentFilterStore;

  const [localFilters, setLocalFilters] = useState({ ...filters });

  const { t } = useTranslation();

  const onCommissionPlanChange = (value) => {
    setLocalFilters({ ...localFilters, commissionPlanId: value });
  };

  const onCriteriaChange = (value) => {
    setLocalFilters({ ...localFilters, criteriaId: value });
  };

  const onDatabookChange = (value) => {
    setLocalFilters({ ...localFilters, databookId: value });
  };

  const onDatasheetChange = (value) => {
    setLocalFilters({ ...localFilters, datasheetId: value });
  };

  const handleOnClose = () => {
    setLocalFilters({ ...filters });
    setVisible(false);
  };

  return (
    <>
      <EverModal
        className="!w-[50%]"
        title="Filters"
        visible={visible}
        onCancel={handleOnClose}
        centered={false}
        footer={
          <div className="flex justify-between w-full">
            <EverButton
              onClick={() => {
                setLocalFilters({
                  commissionPlanId: [],
                  criteriaId: [],
                  databookId: [],
                  datasheetId: [],
                });
              }}
              type="link"
            >
              Clear all
            </EverButton>
            <Space>
              <EverButton color="base" type="text" onClick={handleOnClose}>
                Cancel
              </EverButton>
              <EverButton
                type="filled"
                color="primary"
                onClick={() => {
                  clearSelection();
                  setFilters({ ...localFilters });
                  setVisible(false);
                }}
              >
                Apply
              </EverButton>
            </Space>
          </div>
        }
      >
        <>
          <EverTg.Text className="text-ever-error">
            * Drop down values in the fields may not be restricted to the values
            present in the Report Enrichment table.
          </EverTg.Text>
          <Row gutter={[32, 32]}>
            <Col span={8} className="flex flex-col">
              <div className="flex h-10 items-center justify-between">
                <EverLabel>{t("COMMISSION_PLAN")}</EverLabel>
                {localFilters.commissionPlanId &&
                  localFilters.commissionPlanId.length > 0 && (
                    <EverButton
                      onClick={() => {
                        onCommissionPlanChange([]);
                      }}
                      type="link"
                      className="!p-0"
                    >
                      Clear
                    </EverButton>
                  )}
              </div>
              <EverSelect
                showArrow
                mode="multiple"
                maxTagCount="responsive"
                showSearch
                value={localFilters.commissionPlanId}
                placeholder="Select"
                optionFilterProp="children"
                onChange={onCommissionPlanChange}
                filterOption={(input, option) =>
                  option.children.toLowerCase().includes(input.toLowerCase())
                }
                tagRender={selectTags}
                dropdownMatchSelectWidth={true}
              >
                {commPlanIdToNameMap &&
                  Object.keys(commPlanIdToNameMap).map((el) => (
                    <Option key={el} value={el}>
                      {commPlanIdToNameMap[el]}
                    </Option>
                  ))}
              </EverSelect>
            </Col>
            <Col span={8} className="flex flex-col">
              <div className="flex h-10 items-center justify-between">
                <EverLabel>{t("COMM_CRITERIA")}</EverLabel>
                {localFilters.criteriaId &&
                  localFilters.criteriaId.length > 0 && (
                    <EverButton
                      onClick={() => {
                        onCriteriaChange([]);
                      }}
                      type="link"
                      className="!p-0"
                    >
                      Clear
                    </EverButton>
                  )}
              </div>
              <EverSelect
                showArrow
                mode="multiple"
                maxTagCount="responsive"
                showSearch
                value={localFilters.criteriaId}
                placeholder="Select"
                optionFilterProp="children"
                onChange={onCriteriaChange}
                filterOption={(input, option) =>
                  option.children.toLowerCase().includes(input.toLowerCase())
                }
                tagRender={selectTags}
                dropdownMatchSelectWidth={true}
              >
                {criteriaIdToNameMap &&
                  Object.keys(criteriaIdToNameMap).map((el) => (
                    <Option key={el} value={el}>
                      {criteriaIdToNameMap[el]}
                    </Option>
                  ))}
              </EverSelect>
            </Col>
            <Col span={8} className="flex flex-col">
              <div className="flex h-10 items-center justify-between">
                <EverLabel>Databook</EverLabel>
                {localFilters.databookId &&
                  localFilters.databookId.length > 0 && (
                    <EverButton
                      onClick={() => {
                        onDatabookChange([]);
                      }}
                      type="link"
                      className="!p-0"
                    >
                      Clear
                    </EverButton>
                  )}
              </div>
              <EverSelect
                showArrow
                mode="multiple"
                maxTagCount="responsive"
                showSearch
                value={localFilters.databookId}
                placeholder="Select"
                optionFilterProp="children"
                onChange={onDatabookChange}
                filterOption={(input, option) =>
                  option.children.toLowerCase().includes(input.toLowerCase())
                }
                tagRender={selectTags}
                dropdownMatchSelectWidth={true}
              >
                {databookIdToNameMap &&
                  Object.keys(databookIdToNameMap).map((el) => (
                    <Option key={el} value={el}>
                      {databookIdToNameMap[el]}
                    </Option>
                  ))}
              </EverSelect>
            </Col>
            <Col span={8} className="flex flex-col">
              <div className="flex h-10 items-center justify-between">
                <EverLabel>Datasheet</EverLabel>
                {localFilters.datasheetId &&
                  localFilters.datasheetId.length > 0 && (
                    <EverButton
                      onClick={() => {
                        onDatasheetChange([]);
                      }}
                      type="link"
                      className="!p-0"
                    >
                      Clear
                    </EverButton>
                  )}
              </div>
              <EverSelect
                showArrow
                mode="multiple"
                maxTagCount="responsive"
                showSearch
                value={localFilters.datasheetId}
                placeholder="Select"
                optionFilterProp="children"
                onChange={onDatasheetChange}
                filterOption={(input, option) =>
                  option.children.toLowerCase().includes(input.toLowerCase())
                }
                tagRender={selectTags}
                dropdownMatchSelectWidth={true}
              >
                {datasheetIdToNameMap &&
                  Object.keys(datasheetIdToNameMap).map((el) => (
                    <Option key={el} value={el}>
                      {datasheetIdToNameMap[el]}
                    </Option>
                  ))}
              </EverSelect>
            </Col>
          </Row>
        </>
      </EverModal>
    </>
  );
};

FiltersDrawer.propTypes = {
  visible: PropTypes.bool.isRequired,
};

export default observer(FiltersDrawer);
