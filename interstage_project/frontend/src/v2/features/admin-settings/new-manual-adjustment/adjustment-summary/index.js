/* eslint-disable unicorn/no-array-for-each */
/* eslint-disable unicorn/consistent-function-scoping */
/* eslint-disable unicorn/no-array-callback-reference */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable unicorn/consistent-destructuring */

import { gql, useQuery as useQueryApollo } from "@apollo/client";
import { Server01Icon, CalendarIcon } from "@everstage/evericons/duotone";
import {
  ChevronDownIcon,
  EditPencilAltIcon,
  CheckIcon,
  SettingsIcon,
} from "@everstage/evericons/outlined";
import { Lightning2Icon } from "@everstage/evericons/solid";
import { Dropdown, Menu } from "antd";
import { isValid, parse, getYear, parseISO, format } from "date-fns";
import { cloneDeep, isEmpty, isNil } from "lodash";
import { observer } from "mobx-react";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQuery } from "react-query";
import { Link } from "react-router-dom";
import { useRecoilValue, useRecoilState } from "recoil";
import { twMerge } from "tailwind-merge";

import { getApprovalConfig } from "~/Api/ApprovalWorkflowService";
import { getPayoutPeriodsForYear } from "~/Api/CommissionActionService";
import { COMPONENTS, RBAC_ROLES, COMMON_FNS_DATE_FORMAT } from "~/Enums";
import {
  myClientAtom,
  navPortalAtom,
  everContentRightOpenAtom,
} from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverBadge,
  EverButton,
  EverButtonGroup,
  EverBreadcrumbPortal,
  EverNewDatePicker,
  EverChip,
  EverNavPortal,
  EverTg,
  EverLoader,
  EverDropdownMenu,
  EverHorizontalScroller,
  EverTooltip,
} from "~/v2/components";
import { EverNumberBadge } from "~/v2/components/ever-labels";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import { NotAuthorized } from "~/v2/components/router";
import { DATE_FILTER_LIST } from "~/v2/compound-components/DateAndRangeFilter";
import { NUMBER_FILTER_LIST } from "~/v2/compound-components/NumberFilter";
import { TEXT_FILTER_LIST } from "~/v2/compound-components/TextFilter";

import AdjustmentFilterDrawerNew from "./AdjustmentFilterDrawerNew";
import CommissionTable from "./CommissionTable";
import DrawsTable from "./DrawTable";
import { useRecoverDrawQuery } from "./service";
import AdjustmentCustomCategorySettings from "../adjusment-custom-category-settings";
import PostAdjustmentButton from "../post-adjustment-button";
import postAdjustmentStore from "../post-adjustment-button/store";

const GET_EMPLOYEES = gql`
  query AllEmployees(
    $userStatus: String
    $searchTerm: String
    $offsetValue: Int
    $limitValue: Int
    $component: String
  ) {
    allEmployeesWithLimit(
      userStatus: $userStatus
      searchTerm: $searchTerm
      offsetValue: $offsetValue
      limitValue: $limitValue
      component: $component
    ) {
      employeeEmailId
      firstName
      lastName
      userRole
      canUserManageAdmins
      employeePayroll {
        payoutFrequency
        joiningDate
        effectiveEndDate
      }
      employeeDraw {
        drawYear
        draws
      }
      employeePlanDetails {
        planId
        planName
        planCriterias {
          criteriaId
          criteriaName
        }
      }
      employeeSpiffPlanDetails {
        planId
        planName
        planCriterias {
          criteriaId
          criteriaName
        }
      }
    }
  }
`;

const GET_PERIOD_LABELS = gql`
  {
    periodLabelList {
      label
      value
    }
  }
`;
const ALL_ACTIVE_COUNTIRES = gql`
  query NonEmployeeData {
    allActiveCountries {
      countryCode
      countryName
      currencyCode
    }
  }
`;

const GET_COMMISSION_PERIODS = gql`
  query commissionPeriods {
    allPayeesCommissionPeriods {
      periodStartDate
      periodEndDate
      periodLabel
    }
  }
`;
const ALL_PUBLISHED_PLANS = gql`
  {
    allPublishedPlans {
      planId
      planName
    }
  }
`;
export const ALL_REASON_CATEGORIES = gql`
  query AllCustomCategories($moduleName: String) {
    allCustomCategories(moduleName: $moduleName) {
      customCategoryId
      customCategoryName
      isActive
    }
  }
`;

const LONG_DATE_FORMAT = "dd-MMMM-yyyy";
const supportedDateFormats = [
  "MMM-yyyy",
  "MMMM-yyyy",
  "dd/MM/yyyy",
  LONG_DATE_FORMAT,
  COMMON_FNS_DATE_FORMAT,
];

const parseDate = (dateString) => {
  if (isValid(dateString)) {
    return dateString;
  }
  let parsedDate;
  for (const fmt of supportedDateFormats) {
    parsedDate = parse(dateString, fmt, new Date());
    if (isValid(parsedDate)) break;
  }
  return parsedDate;
};

const TABLE_FILTER_STATUS_MAP = {
  all: "All",
  requested: "Pending",
  approved: "Approved",
  aborted: "Aborted",
  rejected: "Rejected",
};

const AdjustmentSummary = observer((props) => {
  const { store } = props;
  const {
    filters,
    setFilters,
    filtersInit,
    isFilterApplied,
    setAllCommissionPeriods,
    getAllPeriodOptions,
    tableFilter,
    // totalAdjustments,
    onFinish,
  } = store;
  const { accessToken, email } = useAuthStore();
  const { hasPermissions } = useUserPermissionStore();
  const [searchTerm, setSearchTerm] = useState("");
  const [drawSearchTerm, setDrawSearchTerm] = useState("");
  const [filterPeriod, setFilterPeriod] = useState("");
  const [activeKey, setActiveKey] = useState(0);
  const [payeeList, setPayeeList] = useState({});
  const [activeYear, setActiveYear] = useState(null);
  const [reasonCategoryOptions, setReasonCategoryOptions] = useState([]);
  const [selectablePeriods, setSelectablePeriods] = useState({});
  const [allPublishedPlans, setAllPublishedPlans] = useState([]);
  const [isCommAdjApprovalActivated, setIsCommAdjApprovalActivated] =
    useState(false);
  const [periodOptions, setPeriodOptions] = useState([]);
  const [commissionCount, setCommissionCount] = useState(0);
  const [drawCount, setDrawCount] = useState(0);
  const [fetchDrawCount, setFetchDrawCount] = useState(false);
  const navPortalLocation = useRecoilValue(navPortalAtom);
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const customCalendar = clientFeatures?.customCalendar || false;
  const showApprovalFeature = clientFeatures?.showApprovalFeature;
  const [payeeOptions, setPayeeOptions] = useState([]);
  const [addedByOptions, setAddedByOptions] = useState([]);
  const [rightContentOpen, setRightContentOpen] = useRecoilState(
    everContentRightOpenAtom
  );
  const [countries, setCountries] = useState([]);
  const [appliedFilters, setAppliedFilters] = useState(filtersInit);
  const [isModalVisible, setModalVisible] = useState(false);

  const { t } = useTranslation();

  const { data: approvalConfig, isLoading: approvalConfigLoading } = useQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (data) => {
        setIsCommAdjApprovalActivated(
          data?.data?.commissionAdjustmentApprovals?.enabled
        );
      },
    }
  );
  const { isLoading } = useQuery(
    ["getPayoutPeriodsForYear", customCalendar, activeYear],
    () => getPayoutPeriodsForYear(activeYear, accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      enabled: customCalendar && !isEmpty(activeYear),
      onSuccess: (data) => {
        setSelectablePeriods({
          ...selectablePeriods,
          [activeYear]: data.map((item) => {
            return format(new Date(item), LONG_DATE_FORMAT, {
              timeZone: "UTC",
            });
          }),
        });
      },
    }
  );

  useEffect(() => {
    if (isEmpty(selectablePeriods)) {
      setActiveYear(String(getYear(new Date())));
    }
  }, []);

  const handleReasonCategoriesData = (data) => {
    const newReasonCategoryOptions = [];
    data?.allCustomCategories
      .filter((item) => item.isActive == true)
      .forEach((option) => {
        newReasonCategoryOptions.push({
          label: option["customCategoryName"],
          value: option["customCategoryName"],
        });
      });

    setReasonCategoryOptions(newReasonCategoryOptions);
  };

  const { loading: periodsLoading } = useQueryApollo(GET_PERIOD_LABELS, {
    fetchPolicy: "no-cache",
    onCompleted: (data) => {
      if (data && data.periodLabelList && !customCalendar) {
        setPeriodOptions([
          { label: "All", value: "all" },
          ...data.periodLabelList,
        ]);
        setFilterPeriod("all");
      }
    },
  });
  const { loading: publishedPlansQueryLoading } = useQueryApollo(
    ALL_PUBLISHED_PLANS,
    {
      fetchPolicy: "no-cache",
      onCompleted: (data) => {
        if (data) {
          setAllPublishedPlans(data.allPublishedPlans);
          let plansToDisplayNameMap = {};
          for (const plan of data.allPublishedPlans) {
            plansToDisplayNameMap[plan.planId] = plan.planName;
          }
        }
      },
    }
  );

  useQueryApollo(ALL_ACTIVE_COUNTIRES, {
    onCompleted: (allData) => {
      setCountries(allData.allActiveCountries || []);
    },
    fetchPolicy: "no-cache",
  });

  const filterOptions = (data) =>
    !data.canUserManageAdmins &&
    (data.employeeEmailId !== email ||
      hasPermissions(RBAC_ROLES.MANAGE_OWNDATA));

  const { refetch: refetchReasonCategories } = useQueryApollo(
    ALL_REASON_CATEGORIES,
    {
      fetchPolicy: "no-cache",
      variables: { moduleName: "ADJUSTMENTS" },
      onCompleted: handleReasonCategoriesData,
    }
  );
  useQueryApollo(GET_EMPLOYEES, {
    fetchPolicy: "no-cache",
    variables: { component: COMPONENTS.PAYOUTS_STATEMENTS },
    onCompleted: (data) => {
      const newOptions = [];
      const newAddedByOptions = [];
      const newPayeeList = cloneDeep(payeeList);
      data?.allEmployeesWithLimit.forEach((option, index) => {
        newAddedByOptions.push({
          label: `${option.firstName} ${option.lastName}`,
          value: option["employeeEmailId"],
          key: `${option["employeeEmailId"]}_${index}`,
        });
        if (filterOptions(option)) {
          newPayeeList[option["employeeEmailId"]] = { ...option };
          newOptions.push({
            label: `${option.firstName} ${option.lastName}`,
            value: option["employeeEmailId"],
            key: `${option["employeeEmailId"]}_${index}`,
          });
        }
      });
      setAddedByOptions(newAddedByOptions);
      setPayeeOptions(newOptions);
      setPayeeList(newPayeeList);
    },
  });

  useQueryApollo(GET_COMMISSION_PERIODS, {
    fetchPolicy: "network-only",
    onCompleted: (data) => {
      if (data?.allPayeesCommissionPeriods) {
        setAllCommissionPeriods(data.allPayeesCommissionPeriods);
      }
    },
  });
  const tableFilterMenu = [
    { name: "All", key: "all" },
    { name: "Pending", key: "requested" },
    { name: "Approved", key: "approved" },
    { name: "Aborted", key: "aborted" },
    { name: "Rejected", key: "rejected" },
  ];
  const menuItem = (
    <EverDropdownMenu
      menu={tableFilterMenu}
      activeState={tableFilter}
      onClick={(e) => {
        store.setTableFilter(e.key);
      }}
    />
  );
  const periodMenuItem = (
    <Menu
      className="w-full max-h-64 overflow-y-auto"
      onClick={({ key }) => setFilterPeriod(key)}
    >
      {periodOptions?.map((item) => (
        <Menu.Item
          key={item.value}
          className={twMerge(
            "!py-3 px-3 flex gap-2 flex items-center justify-between gap-x-2",
            item.value === filterPeriod ? "rounded-md bg-ever-primary-lite" : ""
          )}
        >
          <EverTg.Text
            className={twMerge(
              "cursor-pointer",
              item.value === filterPeriod
                ? "text-ever-primary font-medium"
                : "text-ever-base-content"
            )}
          >
            {item.label}
          </EverTg.Text>
          {item.value === filterPeriod ? (
            <CheckIcon className="w-4 h-4 text-ever-primary stroke-2" />
          ) : null}
        </Menu.Item>
      ))}
    </Menu>
  );

  const displayValueMap = [
    { key: "Line Item", value: "lineItem" },
    { key: "Commission Plan", value: "commissionPlan" },
    { key: "Payees", value: "payees" },
    { key: "Statement Period", value: "statementPeriod" },
    { key: "Commission Adjustment Amount", value: "adjustmentAmount" },
    { key: "Adjustement Added By", value: "adjustmentAddedBy" },
    { key: "Commission Adjustment Currency", value: "adjustmentCurrency" },
    { key: "Reason Category", value: "reasonCategory" },
  ];
  const FiltersBadge = ({ title, values, onClose }) => {
    // Destructure the object to get 'type' and 'value'
    const { type, value } = values || {}; // Handle cases where `values` might be undefined

    return (
      <EverChip
        size="small"
        title={
          <div className="flex flex-row gap-2 overflow-auto max-h-24">
            <EverTg.Caption.Medium>
              {displayValueMap.find((item) => item.value === title)?.key}
            </EverTg.Caption.Medium>
            {/* Render the type if it exists */}
            {type && (
              <EverTg.Caption className="text-ever-base-content-mid">
                {type}
              </EverTg.Caption>
            )}
            <EverTg.Caption.Medium>
              {/* Render the value */}
              {value}
            </EverTg.Caption.Medium>
          </div>
        }
        onClose={onClose}
        iconClassName="text-ever-base-content-low"
        className="bg-ever-base-25 rounded-full"
      />
    );
  };

  const getFilterTypeMap = () => {
    const typeMap = {};
    for (const type of TEXT_FILTER_LIST) {
      typeMap[type.value] = type.label;
    }
    for (const type of NUMBER_FILTER_LIST) {
      typeMap[type.value] = type.label;
    }
    for (const type of DATE_FILTER_LIST) {
      typeMap[type.value] = type.label;
    }
    return typeMap;
  };

  const updateActiveYear = (year) => {
    if (isNil(selectablePeriods[year])) {
      setActiveYear(String(year));
    }
  };

  function disabledDate(current) {
    if (isEmpty(selectablePeriods[getYear(current)])) {
      return true;
    }
    return !selectablePeriods[getYear(current)].includes(
      format(current, LONG_DATE_FORMAT)
    );
  }
  const formatFilterDisplay = (filterName, filterValue) => {
    const typeMapper = getFilterTypeMap();
    const type = typeMapper[filterValue["type"]];
    const mapPlanIdToName = (planId) => {
      const plan = allPublishedPlans.find((p) => p.planId === planId);
      return plan ? plan.planName : planId; // Return the planName if found, otherwise return the planId
    };
    const mapPeriodToLabel = (statementPeriod) => {
      const allPeriods = getAllPeriodOptions();
      const periods = allPeriods?.find((p) => p.value === statementPeriod);
      return periods.label;
    };
    if (filterName === "commissionPlan") {
      if (type === "Is Empty" || type === "Is Not Empty") {
        return { type: type, value: filterValue["value"] };
      } else {
        const planNames = filterValue["value"].map(mapPlanIdToName).join(", ");
        return { type: type, value: planNames };
      }
    }
    if (filterName === "statementPeriod") {
      const statementPeriods = filterValue["value"]
        .map(mapPeriodToLabel)
        .join(", ");
      return { type: type, value: statementPeriods };
    }

    if (
      typeof filterValue === "object" &&
      "type" in filterValue &&
      "value1" in filterValue
    ) {
      const value1 = String(filterValue["value1"]);
      if (filterValue["value2"]) {
        const value2 = String(filterValue["value2"]);
        return { type: type, value: value1 + " to " + value2 };
      } else {
        return { type: type, value: value1 };
      }
    } else if (
      typeof filterValue === "object" &&
      "type" in filterValue &&
      "value" in filterValue
    ) {
      let value = Array.isArray(filterValue["value"])
        ? filterValue["value"].join(", ")
        : filterValue["value"];
      return { type: type, value: value };
    } else if (Array.isArray(filterValue)) {
      return { type: "In", value: filterValue.join(", ") };
    } else {
      return { type: "In", value: String(filterValue) };
    }
  };

  const handleChangeFilters = (filterName, value) => {
    setFilters({ ...filters, [filterName]: value });
    setAppliedFilters({ ...appliedFilters, [filterName]: value });
  };
  const renderTags = (filters) => {
    // Mapping through the filters object to create JSX elements
    const filterProps = Object.keys(filters).map((filterName) => {
      // Check if the filter has a value or is a boolean
      if (
        !isEmpty(filters[filterName]) ||
        typeof filters[filterName] === "boolean"
      ) {
        return (
          <FiltersBadge
            key={filterName}
            title={filterName}
            values={formatFilterDisplay(filterName, filters[filterName])}
            onClose={() => handleChangeFilters(filterName, null)}
          />
        );
      }
      return null; // Return null if no valid filter
    });

    return (
      <div className="flex items-center gap-2">
        {filterProps?.filter(Boolean)} {/* Filter out null values */}
      </div>
    );
  };
  const formProps = {
    periodsLoading,
    periodOptions,
  };

  const handleCloseFilter = () => {
    setRightContentOpen({ ...rightContentOpen, "settings/adjustments": false });
  };

  const handleOpenFilterBar = () => {
    setRightContentOpen({ ...rightContentOpen, "settings/adjustments": true });
  };

  // Effect to update the counts from the store
  useEffect(() => {
    if (postAdjustmentStore.adjustmentCounts) {
      setCommissionCount(postAdjustmentStore.adjustmentCounts.all || 0);
    }
  }, [postAdjustmentStore.adjustmentCounts]);

  // Use React Query to fetch draw count
  const { data: drawCountData } = useRecoverDrawQuery({
    enabled: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      if (data && data.data) {
        setDrawCount(data.data.length || 0);
      }
    },
    onError: (error) => {
      console.error("Error fetching draw count:", error);
    },
  });

  // Refetch draw count when fetchDrawCount changes
  useEffect(() => {
    if (drawCountData && drawCountData.data) {
      setDrawCount(drawCountData.data.length || 0);
    }
  }, [drawCountData, fetchDrawCount]);

  useEffect(() => {
    if (accessToken) {
      store.setAccessToken(accessToken);
    }
  }, [accessToken]);

  const onClearAll = () => {
    setFilters(filtersInit);
    setAppliedFilters(filtersInit);
  };

  const onModalCancel = () => {
    setModalVisible(false);
  };

  return (
    <div className="h-full flex flex-col gap-y-2">
      <EverBreadcrumbPortal dividerIcon={<></>}>
        <AdjustmentFilterDrawerNew
          closeDrawer={handleCloseFilter}
          allPayees={payeeOptions}
          addedByOptions={addedByOptions}
          statementPeriods={getAllPeriodOptions()}
          adjustmentCurrency={countries}
          reasonCategory={reasonCategoryOptions}
          filters={filters}
          setFilters={setFilters}
          appliedFilters={appliedFilters}
          setAppliedFilters={setAppliedFilters}
          allPublishedPlans={allPublishedPlans}
          publishPlanLoading={publishedPlansQueryLoading}
          {...formProps}
        ></AdjustmentFilterDrawerNew>
        {activeKey === 0 ? (
          showApprovalFeature ? (
            <div className="flex gap-4 items-center ml-auto">
              <EverBadge
                type="custom"
                icon={<Lightning2Icon className="w-4 h-4" />}
                title="Hey"
                className="bg-ever-chartColors-2 text-ever-chartColors-20 border-ever-chartColors-2"
              />
              <EverTg.SubHeading4>
                {isCommAdjApprovalActivated
                  ? "Create templates for Approvals, "
                  : `Enable the Approval Workflow for ${t(
                      "COMMISSION_ADJUSTMENTS"
                    )}, `}
                <Link
                  to="/settings/workflows"
                  state={isCommAdjApprovalActivated ? null : "settings"}
                  className="text-ever-primary"
                >
                  take me there
                </Link>
              </EverTg.SubHeading4>
              <EverTooltip title="Settings">
                <EverButton.Icon
                  size="small"
                  color="base"
                  type="ghost"
                  icon={
                    <SettingsIcon className="h-5 w-5 text-ever-base-content-mid" />
                  }
                  onClick={() => {
                    setModalVisible(true);
                  }}
                />
              </EverTooltip>
            </div>
          ) : (
            <div className="flex gap-4 items-center ml-auto">
              <EverButton
                color="base"
                type="ghost"
                prependIcon={
                  <SettingsIcon className="h-5 w-5 text-ever-base-content-mid" />
                }
                onClick={() => {
                  setModalVisible(true);
                }}
              >
                Settings
              </EverButton>
            </div>
          )
        ) : null}
      </EverBreadcrumbPortal>
      <AdjustmentCustomCategorySettings
        isVisible={isModalVisible}
        onModalCancel={onModalCancel}
        onSuccessCallback={() => {
          refetchReasonCategories().then((data) => {
            handleReasonCategoriesData(data.data);
          });
          onFinish();
          onClearAll();
          onModalCancel();
        }}
      />

      <EverNavPortal target={navPortalLocation}>
        <div className="pb-2 gap-2 flex flex-col">
          <div className="flex w-full justify-between gap-3">
            <EverButtonGroup
              className="bg-ever-base-200"
              activeBtnType="text"
              activeBtnColor="primary"
              defActiveBtnIndex={activeKey}
              size="small"
            >
              <EverButton
                onClick={() => {
                  setActiveKey(0);
                }}
                appendIcon={
                  <EverNumberBadge
                    className="bg-ever-base-300 text-ever-base-content"
                    count={Number(commissionCount)}
                  />
                }
              >
                {t("COMMISSION")}
              </EverButton>
              <EverButton
                onClick={() => {
                  setActiveKey(1);
                  setFetchDrawCount((prev) => !prev); // Toggle to trigger refetch
                }}
                appendIcon={
                  <EverNumberBadge
                    className="bg-ever-base-300 text-ever-base-content"
                    count={Number(drawCount)}
                  />
                }
              >
                Recover Draw
              </EverButton>
            </EverButtonGroup>

            {activeKey === 0 ? (
              <div className="flex items-center gap-3 w-full justify-end">
                <div className="flex gap-3">
                  {customCalendar ? (
                    <EverNewDatePicker
                      allowClear
                      wrapperClassname="w-40 h-8"
                      className="w-40"
                      placeholder="Select Period"
                      defaultValue={
                        isValid(parseISO(filterPeriod))
                          ? parseISO(filterPeriod)
                          : null
                      }
                      disabledDate={disabledDate}
                      showToday={false}
                      onChange={(date) => {
                        if (date) {
                          setFilterPeriod(date);
                        } else {
                          setFilterPeriod(null);
                        }
                      }}
                      onPanelChange={(date) => updateActiveYear(getYear(date))}
                      size="small"
                      value={filterPeriod}
                      picker="date"
                      isPickerLoading={isLoading}
                      renderExtraFooter={() => (
                        <>
                          {isLoading ? (
                            <div className="absolute top-0 left-0 h-full w-full">
                              <EverLoader
                                wrapperClassName="absolute top-0 left-0 z-20"
                                indicatorType="spinner"
                              />
                            </div>
                          ) : null}
                        </>
                      )}
                    />
                  ) : (
                    <Dropdown overlay={periodMenuItem} trigger={["click"]}>
                      <div className="w-60 flex justify-between gap-2 items-center h-8 px-4 cursor-pointer border border-ever-base-400 bg-ever-primary-content rounded">
                        <div className="flex gap-2 items-center">
                          <CalendarIcon className="w-4 h-4 text-ever-base-content-mid" />
                          <EverTg.Text className="text-ever-base-content">
                            Period:{" "}
                            <span className="font-bold">
                              {periodOptions?.find(
                                (opt) => opt.value === filterPeriod
                              )?.label || ""}
                            </span>
                          </EverTg.Text>
                        </div>
                        {periodsLoading ? (
                          <EverLoader.SpinnerLottie className="w-4 h-4" />
                        ) : (
                          <div className="flex">
                            <ChevronDownIcon className="w-4 h-4 text-ever-base-content-low" />
                          </div>
                        )}
                      </div>
                    </Dropdown>
                  )}
                  {isCommAdjApprovalActivated ? (
                    <Dropdown overlay={menuItem} trigger={["click"]}>
                      <div className="cursor-pointer flex h-8 items-center px-2 py-3 rounded border-ever-base-400 bg-ever-primary-content border-solid border gap-2 justify-between">
                        <Server01Icon className="w-4 h-4 mr-0.5 text-ever-base-content-mid"></Server01Icon>
                        <EverTg.Text className="text-ever-base-content whitespace-nowrap">
                          Approval Status:
                        </EverTg.Text>
                        <EverTg.Text className="text-ever-base-content font-bold">
                          {TABLE_FILTER_STATUS_MAP[store.tableFilter] || "All"}
                        </EverTg.Text>
                        <ChevronDownIcon className="w-4 h-4 text-ever-base-content-low" />
                      </div>
                    </Dropdown>
                  ) : null}
                </div>
              </div>
            ) : null}
            <div className="flex flex-wrap justify-end w-full lg:w-auto">
              <PostAdjustmentButton
                onFinish={store.onFinish}
                label={t("ADD_ADJUSTMENT")}
                searchTerm={searchTerm}
                statementPeriods={getAllPeriodOptions()}
                setSearchTerm={setSearchTerm}
                setDrawSearchTerm={setDrawSearchTerm}
                activeKey={activeKey}
                filterPeriod={filterPeriod}
                filters={filters}
                parseDate={parseDate}
                approvalConfig={approvalConfig?.data}
                tableFilter={tableFilter}
                reasonCategoryOptions={reasonCategoryOptions}
                handleOpenFilterBar={handleOpenFilterBar}
                {...formProps}
              />
            </div>
          </div>

          {isFilterApplied && (
            <div className="h-10 bg-ever-base-200 rounded-lg flex items-center">
              <EverTg.Text className="pl-2 whitespace-nowrap">
                Applied Filters
              </EverTg.Text>
              <EverHorizontalScroller
                className="flex items-center h-10 gap-2 flex-nowrap px-3"
                wrapperClassName="overflow-hidden pr-1"
              >
                <div className={twMerge("flex flex-row gap-2")}>
                  {renderTags(filters)}
                </div>
              </EverHorizontalScroller>
              <div className="grow"></div>
              <div
                className="cursor-pointer mr-3"
                onClick={handleOpenFilterBar}
              >
                <EditPencilAltIcon className="w-4 h-4 text-ever-base-content-mid" />
              </div>
              <EverButton onClick={onClearAll} type="text" size="small">
                Clear all
              </EverButton>
            </div>
          )}
        </div>
      </EverNavPortal>

      <div className="flex-grow">
        {activeKey === 0 ? (
          <RBACProtectedComponent
            permissionId={RBAC_ROLES.MANAGE_COMMISSIONADJUSTMENT}
            placeholderComponent={<NotAuthorized />}
          >
            <div className="flex flex-col gap-2 h-full">
              {approvalConfigLoading ? (
                <EverLoader spinning />
              ) : (
                <div className="flex-grow">
                  <CommissionTable
                    statementPeriods={getAllPeriodOptions()}
                    store={store}
                    searchTerm={searchTerm}
                    setSearchTerm={setSearchTerm}
                    filterPeriod={filterPeriod}
                    setFilterPeriod={setFilterPeriod}
                    approvalConfig={approvalConfig?.data}
                    customCalendar={customCalendar}
                    filters={filters}
                    parseDate={parseDate}
                  />
                </div>
              )}
            </div>
          </RBACProtectedComponent>
        ) : null}

        {activeKey === 1 ? (
          <RBACProtectedComponent
            permissionId={RBAC_ROLES.MANAGE_DRAWS}
            placeholderComponent={<NotAuthorized />}
          >
            <DrawsTable store={store} drawSearchTerm={drawSearchTerm} />
          </RBACProtectedComponent>
        ) : null}
      </div>
    </div>
  );
});

export default AdjustmentSummary;
