import { SettingsAltIcon } from "@everstage/evericons/duotone";
import {
  SearchIcon,
  FileQuestionIcon,
  LoadingIcon,
  SyncRefreshIcon,
} from "@everstage/evericons/outlined";
import {
  AlertCircleIcon,
  CheckCircleIcon,
  CircleGlowIcon,
} from "@everstage/evericons/solid";
import { useSupabasePostgresChanges } from "everstage-supabase";
import React, { useState, useEffect, useMemo } from "react";
import { useQuery, useQueryClient } from "react-query";
import { useNavigate } from "react-router-dom";
import { useRecoilValue } from "recoil";

import { INTEGRATION, SUPABASE_CONSTANTS } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import {
  EverTg,
  EverCard,
  EverButton,
  EverBadge,
  EverLoader,
  toast,
  EverHotToastMessage,
  EverModal,
} from "~/v2/components";
import { useHrisAPI } from "~/v2/features/admin-settings/hris-integration/api";
import { ConfigurationDrawer } from "~/v2/features/admin-settings/hris-integration/configuration-drawer/ConfigurationDrawer";
import {
  noIntegrations as NointegrationsIcon,
  chargebee as ChargebeeIcon,
  closeLogo as CloseIcon,
  csvIcon as CSVIcon,
  freshsales as FreshsalesIcon,
  hubspot as HubspotIcon,
  quickbooks as QuickBooksIcon,
  s3Icon as S3Icon,
  salesforce as SalesforceIcon,
  sqlIcon as SQLIcon,
  stripe as StripeIcon,
  zoho as ZohoIcon,
  workday as WorkdayIcon,
  merge as MergeIcon,
  gong as GongIcon,
  defaultIntegration as DefaultIntegrationIcon,
} from "~/v2/images";

const integrations = {
  [INTEGRATION.CHARGEBEE]: {
    icon: ChargebeeIcon,
    displayName: "Chargebee",
  },
  [INTEGRATION.CLOSE]: {
    icon: CloseIcon,
    displayName: "Close",
  },
  [INTEGRATION.FRESHSALES]: {
    icon: FreshsalesIcon,
    displayName: "Freshsales",
  },
  [INTEGRATION.HUBSPOT]: {
    icon: HubspotIcon,
    displayName: "Hubspot",
  },
  [INTEGRATION.QUICKBOOKS]: {
    icon: QuickBooksIcon,
    displayName: "Quickbooks",
  },
  [INTEGRATION.SALESFORCE]: {
    icon: SalesforceIcon,
    displayName: "Salesforce",
  },
  [INTEGRATION.ZOHO]: {
    icon: ZohoIcon,
    displayName: "Zoho",
  },
  [INTEGRATION.STRIPE]: {
    icon: StripeIcon,
    displayName: "Stripe",
  },
  [INTEGRATION.S3]: {
    icon: S3Icon,
    displayName: "S3",
  },
  [INTEGRATION.CSV]: {
    icon: CSVIcon,
    displayName: "CSV",
  },
  [INTEGRATION.SQL]: {
    icon: SQLIcon,
    displayName: "SQL",
  },
  [INTEGRATION.WORKDAY]: {
    icon: WorkdayIcon,
    displayName: "Workday",
  },
  [INTEGRATION.MERGE]: {
    icon: MergeIcon,
    displayName: "Merge",
  },
  [INTEGRATION.GONG]: {
    icon: GongIcon,
    displayName: "Gong",
  },
};

export function HrisIntegrationHome() {
  const myAtom = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myAtom);
  const HRISAPI = useHrisAPI();
  const {
    data: hrisData,
    isFetching: hrisDataFetching,
    refetch: hrisDataRefetch,
    isLoading: hrisDataLoading,
    isStale: hrisDataStale,
  } = useQuery(["getHRISData"], () => HRISAPI.getHRISData(), {
    retry: false,
    cacheTime: 0,
    refetchOnWindowFocus: false,
  });

  if (hrisDataLoading || hrisData === null) {
    return <EverLoader spinning={true} />;
  }
  return hrisData?.data?.activeHrisConnections.length > 0 ? (
    <IntegrationSteps
      hrisDataRefetch={hrisDataRefetch}
      hrisData={hrisData}
      hrisDataFetching={hrisDataFetching}
      hrisDataStale={hrisDataStale}
      clientFeatures={clientFeatures}
    />
  ) : (
    <Nointegration />
  );
}

function Nointegration() {
  return (
    <div className="h-full flex items-center justify-center">
      <div className="flex items-center justify-center h-full transform -translate-y-16">
        <div className="flex flex-col h-full items-center gap-1 justify-center">
          <img src={NointegrationsIcon} />
          <EverTg.Text className="text-ever-base-content">
            You can reach out to Everstage support to setup Integrations.
          </EverTg.Text>
        </div>
      </div>
    </div>
  );
}

function IntegrationSteps({
  hrisData,
  hrisDataRefetch,
  hrisDataFetching,
  hrisDataStale,
  clientFeatures,
}) {
  const [showConfigDrawer, setShowConfigDrawer] = useState(false);
  const [isSyncInProgress, setIsSyncInProgress] = useState(false);
  const [isImportInProgress, setIsImportInProgress] = useState(false);
  const {
    activeHrisConnections,
    hrisConfig,
    dataReviewType,
    connectedObjects,
    isSyncRunning,
  } = hrisData.data;
  const HRISAPI = useHrisAPI();
  const isConfigIncomplete = hrisConfig.length > 0 ? false : true;
  const fivetranSyncEnabled = clientFeatures?.fivetranSync;

  const { data: runningImportTasks, refetch: runningImportTasksRefetch } =
    useQuery(["getRunningImportTasks"], () => HRISAPI.getRunningImportTasks(), {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    });

  useEffect(() => {
    if (runningImportTasks?.data?.length > 0) {
      setIsImportInProgress(true);
    } else {
      setIsImportInProgress(false);
    }
  }, [runningImportTasks]);

  const realtimePostgresChangesFilter = [
    {
      event: SUPABASE_CONSTANTS.HRIS_SYNC.EVENT_TYPE[0],
      filter: `task_name=eq.${SUPABASE_CONSTANTS.HRIS_SYNC.TASK_NAME}`,
    },
    {
      event: SUPABASE_CONSTANTS.HRIS_SYNC.EVENT_TYPE[1],
      filter: `task_name=eq.${SUPABASE_CONSTANTS.HRIS_SYNC.TASK_NAME}`,
    },
  ];
  const channelPrefix = SUPABASE_CONSTANTS.BULK_IMPORT_DATA.CHANNEL_NAME;
  const updates = useSupabasePostgresChanges(realtimePostgresChangesFilter, {
    channelPrefix,
  });

  const hrisImportFilters = [
    {
      event: SUPABASE_CONSTANTS.HRIS_IMPORT_RECORDS.EVENT_TYPE[0],
      filter: `task_name=eq.${SUPABASE_CONSTANTS.HRIS_IMPORT_RECORDS.TASK_NAME}`,
    },
    {
      event: SUPABASE_CONSTANTS.HRIS_IMPORT_RECORDS.EVENT_TYPE[1],
      filter: `task_name=eq.${SUPABASE_CONSTANTS.HRIS_IMPORT_RECORDS.TASK_NAME}`,
    },
  ];
  const hrisImportFiltersPrefix =
    SUPABASE_CONSTANTS.HRIS_IMPORT_RECORDS.CHANNEL_NAME;
  const hrisImportFiltersPrefixUpdates = useSupabasePostgresChanges(
    hrisImportFilters,
    {
      hrisImportFiltersPrefix,
    }
  );

  useEffect(() => {
    if (hrisImportFiltersPrefixUpdates) {
      if (
        hrisImportFiltersPrefixUpdates?.new.data.status ===
        SUPABASE_CONSTANTS.HRIS_IMPORT_RECORDS.COMPLETED
      ) {
        runningImportTasksRefetch();
        toast.custom(
          () => (
            <EverHotToastMessage
              type="success"
              description="Data Imported successfully"
            />
          ),
          { position: "top-center" }
        );
      } else if (
        hrisImportFiltersPrefixUpdates?.new.data.status ===
        SUPABASE_CONSTANTS.HRIS_IMPORT_RECORDS.FAILED
      ) {
        runningImportTasksRefetch();
        toast.custom(
          () => (
            <EverHotToastMessage
              type="error"
              description="Data Import failed. Please contact support team for help"
            />
          ),
          { position: "top-center" }
        );
      }
    }
  }, [hrisImportFiltersPrefixUpdates]);
  const tasksToTrack = useMemo(() => {
    return connectedObjects.map((obj) => {
      return obj.taskName;
    });
  }, [connectedObjects]);

  useEffect(() => {
    if (!hrisDataFetching || hrisDataStale) {
      if (isSyncRunning) {
        //show loader
        setIsSyncInProgress(true);
      } else {
        setIsSyncInProgress(false);
      }
    }
  }, [hrisData, hrisDataFetching, hrisDataStale, isSyncRunning]);

  useEffect(() => {
    if (updates && tasksToTrack.includes(updates?.new.data.task_name)) {
      if (updates?.eventType === SUPABASE_CONSTANTS.HRIS_SYNC.EVENT_TYPE[1]) {
        if (
          updates?.new.data.status === SUPABASE_CONSTANTS.HRIS_SYNC.COMPLETED ||
          updates?.new.data.status === SUPABASE_CONSTANTS.HRIS_SYNC.FAILED
        ) {
          hrisDataRefetch();
        }
      } else if (
        updates?.eventType === SUPABASE_CONSTANTS.HRIS_SYNC.EVENT_TYPE[0]
      ) {
        setIsSyncInProgress(true);
      }
    }
  }, [updates]);

  return (
    <div className="flex flex-col w-11/12">
      {isConfigIncomplete ? (
        <>
          <EverTg.Heading3 className="flex items-center">
            <CheckCircleIcon className="w-6 h-6 text-ever-primary" />
            <span className="pl-3">Integrations</span>
          </EverTg.Heading3>
          <div className="my-1 ml-3 border-l-2 border-r-0 border-y-0 border-solid border-ever-base-400">
            <Integrations
              activeHrisConnections={activeHrisConnections}
              isConfigIncomplete={isConfigIncomplete}
              isSyncInProgress={isSyncInProgress}
              isImportInProgress={isImportInProgress}
              setIsSyncInProgress={setIsSyncInProgress}
              connectedObjects={connectedObjects}
            />
          </div>
          <EverTg.Heading3 className="flex items-center">
            <LoadingIcon className="w-6 h-6 mb-1 mt-1 text-ever-primary" />
            <span className="pl-3">Configurations</span>
          </EverTg.Heading3>
          <div className="my-1 ml-3 border-l-2 border-r-0 border-y-0 border-dashed border-ever-base-400">
            <Configurations
              isConfigIncomplete={isConfigIncomplete}
              isImportInProgress={isImportInProgress}
              isSyncInProgress={isSyncInProgress}
            />
          </div>
          <ConfigurationDrawer
            open={showConfigDrawer}
            hrisConfig={hrisConfig}
            hrisDataRefetch={hrisDataRefetch}
            dataReviewType={dataReviewType}
            setOpen={setShowConfigDrawer}
          />
          <EverTg.Heading3 className="flex items-center">
            <CircleGlowIcon className="size-6 -mb-2 text-ever-primary rounded-full ring-4 ring-opacity-50 ring-blue-200 ring-offset-0" />
            <span className="pl-3">Data Review</span>
          </EverTg.Heading3>
          <DataReview
            dataReviewType={dataReviewType}
            isConfigIncomplete={isConfigIncomplete}
            isSyncInProgress={isSyncInProgress}
            isImportInProgress={isImportInProgress}
          />
        </>
      ) : (
        <>
          <EverTg.Heading3>Integrations</EverTg.Heading3>
          <Integrations
            activeHrisConnections={activeHrisConnections}
            isConfigIncomplete={isConfigIncomplete}
            isSyncInProgress={isSyncInProgress}
            isImportInProgress={isImportInProgress}
            setIsSyncInProgress={setIsSyncInProgress}
            connectedObjects={connectedObjects}
          />
          <EverTg.Heading3>Configurations</EverTg.Heading3>
          <Configurations
            isConfigIncomplete={isConfigIncomplete}
            isSyncInProgress={isSyncInProgress}
            isImportInProgress={isImportInProgress}
          />
          <ConfigurationDrawer
            open={showConfigDrawer}
            setOpen={setShowConfigDrawer}
            hrisDataRefetch={hrisDataRefetch}
            dataReviewType={dataReviewType}
            hrisConfig={hrisConfig}
            refetchHrisData={hrisDataRefetch}
          />
          <EverTg.Heading3>Data Review</EverTg.Heading3>
          <DataReview
            dataReviewType={dataReviewType}
            isConfigIncomplete={isConfigIncomplete}
            isSyncInProgress={isSyncInProgress}
            isImportInProgress={isImportInProgress}
          />
        </>
      )}
    </div>
  );

  function Integrations({
    activeHrisConnections,
    isConfigIncomplete,
    isSyncInProgress,
    isImportInProgress,
    setIsSyncInProgress,
    connectedObjects,
  }) {
    const HRISAPI = useHrisAPI();
    const queryClient = useQueryClient();

    const connectionMap = useMemo(() => {
      let _map = {};
      for (const connection of activeHrisConnections) {
        let _objects = [];
        for (const obj of connectedObjects) {
          if (obj.accessTokenConfigId === connection.accessTokenConfigId) {
            _objects.push(obj);
          }
        }
        _map[connection.accessTokenConfigId] = _objects;
      }
      return _map;
    }, [activeHrisConnections, connectedObjects]);

    async function onRunSyncForObject(accessTokenConfigId) {
      const data = {
        accessTokenConfigId: accessTokenConfigId,
      };
      let loadingToast = null;
      try {
        // setIsSyncInProgress(true);
        const loadingToast = toast.custom(
          () => (
            <EverHotToastMessage
              type="loading"
              description={"Submitting data sync task..."}
            />
          ),
          { position: "top-center", duration: Number.POSITIVE_INFINITY }
        );
        const responseMsg = await queryClient.fetchQuery({
          queryKey: ["runSyncForObject"],
          queryFn: () => HRISAPI.runUpstreamSyncForObject(data),
        });
        toast.dismiss(loadingToast);
        toast.custom(
          () => (
            <EverHotToastMessage type="success" description={responseMsg} />
          ),
          { position: "top-center" }
        );
        // setIsSyncInProgress(true);
      } catch (error) {
        toast.dismiss(loadingToast);
        toast.custom(
          () => (
            <EverHotToastMessage type="error" description={error.message} />
          ),
          { position: "top-center" }
        );
        setIsSyncInProgress(false);
      }
    }

    return (
      <div
        className={`flex flex-col space-y-4 pt-1 pb-6 ${
          isConfigIncomplete && "pl-6"
        } `}
      >
        <EverTg.SubHeading4>
          Everstage syncs user data from the following sources
        </EverTg.SubHeading4>
        {activeHrisConnections.map((integration) => {
          return (
            <IntegrationCard
              key={integration.connectionName}
              title={integration.connectionName}
              integrationIcon={true}
              serviceName={integration.serviceName}
              syncInProgress={isSyncInProgress}
              importInProgress={isImportInProgress}
              extraContent={
                connectionMap[integration.accessTokenConfigId].length > 0 ? (
                  <EverButton
                    size="small"
                    onClick={() => {
                      EverModal.confirm({
                        title: "Are you sure you want to sync now?",
                        subtitle:
                          "While this sync is running, you cannot trigger sync for another connection, review updates or modify configurations.",
                        noteMessage:
                          fivetranSyncEnabled &&
                          integration?.isFivetranConnection
                            ? "Note: It may take longer than usual as this will trigger fivetran sync as well."
                            : null,
                        okText: "confirm",
                        noteMessageClasses: "!font-medium",
                        cancelText: "cancel",
                        centered: true,
                        onOk: () => {
                          setIsSyncInProgress(true);
                          onRunSyncForObject(integration.accessTokenConfigId);
                        },
                      });
                    }}
                  >
                    Sync now
                  </EverButton>
                ) : (
                  <div className="flex flex-row gap-6 items-center">
                    <span className="text-xs"> No objects to sync</span>
                    <EverButton size="small" disabled={true}>
                      Sync now
                    </EverButton>
                  </div>
                )
              }
            />
          );
        })}
      </div>
    );
  }

  function Configurations({
    isConfigIncomplete,
    isSyncInProgress,
    isImportInProgress,
  }) {
    return (
      <div
        className={`flex flex-col space-y-4 pt-4 pb-6 ${
          isConfigIncomplete && "pl-6"
        }`}
      >
        <IntegrationCard
          key="config-1"
          icon={<SettingsAltIcon className="w-5 h-5" />}
          title="Configure fields to sync, setup review strategy and handle user deactivation"
          interactive={true}
          syncInProgress={isSyncInProgress}
          importInProgress={isImportInProgress}
          onClickCard={() => {
            setShowConfigDrawer(true);
          }}
          extraContent={
            isConfigIncomplete && (
              <EverBadge
                icon={<AlertCircleIcon className="w-3 h-3" />}
                title="Not configured"
                type="warning"
              />
            )
          }
        />
      </div>
    );
  }

  function DataReview({
    dataReviewType,
    isConfigIncomplete,
    isSyncInProgress,
    isImportInProgress,
  }) {
    const [totalUpdates, setTotalUpdates] = useState(null);
    const HRISAPI = useHrisAPI();
    const { data: updatesCount } = useQuery(
      ["getNewUpdatesCount"],
      () => HRISAPI.getNewUpdatesCount(),
      {
        retry: false,
        cacheTime: 0,
        enabled: isConfigIncomplete ? false : true,
        refetchOnWindowFocus: false,
      }
    );

    useEffect(() => {
      if (updatesCount) {
        setTotalUpdates(updatesCount?.count);
      }
    }, [updatesCount]);

    const navigate = useNavigate();
    function onClickDataReview() {
      if (isConfigIncomplete) return;
      navigate("review-updates");
    }
    function onClickProcessedIgnoredRecords() {
      if (isConfigIncomplete) return;
      navigate("processed-ignored-records");
    }
    return (
      <div
        className={`flex flex-col space-y-4 pt-4 pb-6 ${
          isConfigIncomplete && "pl-10"
        }`}
      >
        {dataReviewType === "manual-review" && (
          <IntegrationCard
            key="data-review-1"
            cardDisabled={isConfigIncomplete}
            icon={<SearchIcon className="w-4 h-4" />}
            title="Review new updates before import"
            onClickCard={onClickDataReview}
            interactive={true}
            syncInProgress={isSyncInProgress}
            importInProgress={isImportInProgress}
            extraContent={
              !isConfigIncomplete &&
              totalUpdates !== null && (
                <EverBadge
                  icon={<AlertCircleIcon className="w-3 h-3" />}
                  title={
                    totalUpdates === 0
                      ? "No new updates"
                      : `${totalUpdates} new updates to review`
                  }
                  type="base"
                />
              )
            }
          />
        )}
        <IntegrationCard
          key="data-review-2"
          cardDisabled={isConfigIncomplete}
          onClickCard={onClickProcessedIgnoredRecords}
          icon={<FileQuestionIcon className="w-5 h-5" />}
          className="mt-2"
          syncInProgress={isSyncInProgress}
          importInProgress={isImportInProgress}
          title="See updates that were processed or ignored"
          interactive={true}
          extraContent={null}
        />
      </div>
    );
  }
}

function IntegrationCard({
  title,
  extraContent,
  interactive = false,
  cardDisabled = false,
  icon = null,
  integrationIcon = false,
  serviceName = "",
  syncInProgress = false,
  importInProgress = false,
  onClickCard = () => {},
}) {
  return (
    <EverCard
      className={`${
        (cardDisabled || syncInProgress || importInProgress) &&
        "bg-ever-base-50"
      }`}
      interactive={interactive}
      onClick={() => {
        if (cardDisabled || syncInProgress || importInProgress) return;
        onClickCard();
      }}
    >
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center">
            {integrationIcon ? (
              <div
                className={`w-10 h-10 flex items-center justify-center rounded-lg rounded-tl-none ${
                  cardDisabled && "opacity-60"
                }`}
              >
                {integrations[serviceName]?.icon ? (
                  <img
                    src={integrations[serviceName]?.icon}
                    className="flex max-w-[56px] w-auto max-h-full"
                  />
                ) : (
                  <img
                    src={DefaultIntegrationIcon}
                    className="flex max-w-[80px] w-auto max-h-full"
                  />
                )}
              </div>
            ) : (
              <div
                className={`w-10 h-10 flex items-center justify-center bg-ever-chartColors-2 rounded-lg rounded-tl-none ${
                  cardDisabled && "opacity-60"
                }`}
              >
                {icon}
              </div>
            )}
            <div className="pl-4">
              <span
                className={`font-normal text-sm ${
                  cardDisabled ? "text-gray-300" : null
                }`}
              >
                {title}
              </span>
            </div>
          </div>
        </div>
        {syncInProgress || importInProgress ? (
          <>
            {syncInProgress && (
              <span className="text-xs text-ever-primary">
                <SyncRefreshIcon className="w-4 h-4 -mb-1" /> Sync in progress
              </span>
            )}
            {importInProgress && (
              <span className="text-xs text-ever-primary">
                <SyncRefreshIcon className="w-4 h-4 -mb-1" /> Data import in
                progress
              </span>
            )}
          </>
        ) : (
          extraContent
        )}
      </div>
    </EverCard>
  );
}
