import { ChevronDownIcon } from "@everstage/evericons/outlined";
import { VariableIcon } from "@everstage/evericons/solid";
import { Row, Col } from "antd";
import { pascalCase } from "change-case";
import { includes } from "lodash";
import React, { useState } from "react";

import { DATATYPE } from "~/Enums";
import {
  EverTg,
  EverModal,
  EverForm,
  EverSelect,
  EverButton,
  EverInput,
  EverNewDatePicker,
  EverRadio,
} from "~/v2/components";

const ConstantFunctionButton = (props) => {
  const {
    onSelection,
    enabled,
    excludeTypes,
    initialDataType = DATATYPE.INTEGER,
  } = props;
  const [visible, showModal] = useState(false);
  const [form] = EverForm.useForm();
  form.setFieldsValue({ dataType: initialDataType });
  const name = "CONSTANT";

  const getOptions = () => {
    return [
      { label: "Number", value: DATATYPE.INTEGER },
      { label: "Percentage", value: DATATYPE.PERCENTAGE },
      { label: "Email", value: DATATYPE.EMAIL },
      { label: "Text", value: DATATYPE.STRING },
      { label: "Date", value: DATATYPE.DATE },
      { label: "True/False", value: DATATYPE.BOOLEAN },
      { label: "Array", value: DATATYPE.ARRAY },
    ].filter((item) => !includes(excludeTypes, item.value));
  };

  const getSelectOptions = (options) => {
    return options.map((option) => {
      return (
        <EverSelect.Option value={option.value} key={option.value}>
          {option.label}
        </EverSelect.Option>
      );
    });
  };

  const bgColor = enabled
    ? "bg-ever-chartColors-37 text-ever-base-content cursor-pointer"
    : "bg-ever-base-100 text-ever-base-content-low cursor-not-allowed";

  const buttonClass = `h-7 gap-2 rounded  flex justify-center items-center p-2 ${bgColor}`;

  return (
    <div>
      <div
        className={buttonClass}
        onClick={() => {
          enabled && showModal(true);
        }}
        // disabled={!enabled}
      >
        <VariableIcon />
        <EverTg.Text className="text-ever-base-content font-semibold">
          {pascalCase(name)}
        </EverTg.Text>
      </div>
      <EverModal
        destroyOnClose={true}
        visible={visible}
        centered={true}
        title={<EverTg.Heading3>Add Constant</EverTg.Heading3>}
        footer={[
          <EverButton
            key="cancelBtn"
            type="filled"
            color="base"
            size="small"
            onClick={() => {
              form.resetFields();
              showModal(false);
            }}
          >
            Cancel
          </EverButton>,
          <EverButton
            key="acceptBtn"
            type="filled"
            color="primary"
            size="small"
            onClick={() => {
              form
                .validateFields()
                .then((values) => {
                  let constantValueKey = values.dataType + "Constant";
                  let label = values[constantValueKey];
                  let dataType = values.dataType;
                  let enteredValues = values[constantValueKey];
                  if (values.dataType === DATATYPE.DATE) {
                    enteredValues = enteredValues.format("YYYY-MM-DD");
                    // Since value is moment object, localized time will be converted to utc while
                    // converting it as json param and date might be reduced/increased by an day on conversion,
                    // so formatting it to avoid the conversions
                    values[constantValueKey] =
                      values[constantValueKey].format("YYYY/MM/DD");
                    label = values[constantValueKey];
                  } else if (values.dataType === DATATYPE.PERCENTAGE) {
                    label = values[constantValueKey] + "%";
                  } else if (values.dataType === DATATYPE.EMAIL) {
                    label = values[constantValueKey].toLowerCase();
                    enteredValues = enteredValues.toLowerCase();
                  } else if (values.dataType === DATATYPE.ARRAY) {
                    label = "Array - ";
                    if (values.ArrayConstant) {
                      let arrValues = values.ArrayConstant.arrayValues;
                      let typeValue = values.ArrayConstant.typeValue;
                      let convertArray = [];
                      arrValues.map((value, index) => {
                        label = label + value;
                        if (index !== arrValues.length - 1) {
                          label = label + ", ";
                        }
                        if (typeValue === "numberField")
                          convertArray.push(parseFloat(value));
                        else convertArray.push(value);
                      });
                      if (typeValue === "numberField")
                        dataType = DATATYPE.INTARRAY;
                      else dataType = DATATYPE.STRINGARRAY;

                      enteredValues = convertArray;
                    }
                  }
                  form.resetFields();
                  onSelection({
                    functionName: name,
                    name: label,
                    dataType: dataType,
                    args: [dataType, enteredValues],
                  });
                  showModal(false);
                })
                .catch((info) => {
                  console.log("Validate Failed:", info);
                });
            }}
          >
            Add
          </EverButton>,
        ]}
        onCancel={() => {
          form.resetFields();
          showModal(false);
        }}
      >
        <EverForm
          form={form}
          layout="vertical"
          name="constant creator"
          className="bg-ever-base-50 px-4 py-5 border border-solid border-ever-base-400 rounded-lg"
          //style={{ backgroundColor: "#F3F6FD", borderRadius: 4, padding: 10 }}
        >
          <Row gutter={[16, 0]}>
            <Col span={12}>
              <EverForm.Item name="dataType" label="Data Type">
                <EverSelect
                  placeholder="Select Type"
                  className={"w-full"}
                  allowClear
                  //style={{ width: "100%" }}
                  suffixIcon={<ChevronDownIcon />}
                >
                  {getSelectOptions(getOptions())}
                </EverSelect>
              </EverForm.Item>
            </Col>
            <Col span={12}>
              <EverForm.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.dataType !== currentValues.dataType
                }
              >
                {({ getFieldValue }) => {
                  if (getFieldValue("dataType") !== DATATYPE.ARRAY) {
                    return getFormItem(getFieldValue("dataType"));
                  } else {
                    return (
                      <EverForm.Item label={"ArrayConstant"}>
                        <EverInput.Group compact>
                          <EverForm.Item
                            name={["ArrayConstant", "typeValue"]}
                            rules={[
                              { required: true, message: "Type is required" },
                            ]}
                          >
                            <EverSelect
                              placeholder="Select Type"
                              //className={"w-full"}
                              className={"w-52"}
                              allowClear
                              suffixIcon={<ChevronDownIcon />}
                            >
                              <EverSelect.Option value="numberField">
                                Number Field
                              </EverSelect.Option>
                              <EverSelect.Option value="textField">
                                Text Field
                              </EverSelect.Option>
                            </EverSelect>
                          </EverForm.Item>
                          <EverForm.Item
                            className="ml-0"
                            name={["ArrayConstant", "arrayValues"]}
                            rules={[
                              {
                                required: true,
                                message: "Please enter values",
                              },
                              () => ({
                                validator(rule, value) {
                                  let datatype = form.getFieldValue([
                                    "ArrayConstant",
                                    "typeValue",
                                  ]);
                                  if (value && datatype === "numberField") {
                                    let isValid = true;
                                    value.map((v) => {
                                      if (isNaN(v) || isNaN(parseFloat(v))) {
                                        isValid = false;
                                      }
                                    });
                                    if (isValid) return Promise.resolve();
                                    else
                                      return Promise.reject(
                                        "Enter valid numbers"
                                      );
                                  }
                                  return Promise.resolve();
                                },
                              }),
                            ]}
                          >
                            <EverSelect
                              mode="tags"
                              //className={"w-full"}
                              className={"w-52"}
                              allowClear
                              maxTagTextLength={16}
                              suffixIcon={<ChevronDownIcon />}
                            >
                              {[]}
                            </EverSelect>
                          </EverForm.Item>
                        </EverInput.Group>
                      </EverForm.Item>
                    );
                  }
                }}
              </EverForm.Item>
            </Col>
          </Row>
        </EverForm>
      </EverModal>
    </div>
  );
};

const getFormItem = (dataType) => {
  return (
    <EverForm.Item
      name={dataType + "Constant"}
      label="Constant Value"
      rules={[
        {
          required: true,
          message: "Please enter a constant!",
        },
        ...(dataType === DATATYPE.EMAIL
          ? [{ type: "email", message: `Please enter a valid email!` }]
          : []),
      ]}
    >
      {getFieldForDataType(dataType)}
    </EverForm.Item>
  );
};

const getFieldForDataType = (dataType) => {
  if (dataType === DATATYPE.INTEGER)
    return <EverInput.Number className="w-full" />;
  if (dataType === DATATYPE.STRING || dataType === DATATYPE.EMAIL)
    return <EverInput className="" />;
  if (dataType === DATATYPE.DATE)
    return (
      <EverNewDatePicker.Legacy
        className="w-full"
        //suffixIcon={<i className="esi-fw esi-calendar" />}
        //style={{ width: "50%" }}
      />
    );
  if (dataType === DATATYPE.BOOLEAN)
    return (
      <EverRadio.Group>
        <EverRadio value="True">True</EverRadio>
        <EverRadio value="False">False</EverRadio>
      </EverRadio.Group>
    );
  if (dataType === DATATYPE.PERCENTAGE)
    return <EverInput.Number className="w-full" />;

  return null;
};
export default ConstantFunctionButton;
