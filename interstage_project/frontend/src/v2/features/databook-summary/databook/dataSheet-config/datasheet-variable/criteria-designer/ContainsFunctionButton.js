import { FunctionIcon } from "@everstage/evericons/outlined";
import { Space, Row, Col } from "antd";
import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import React, { useState } from "react";

import { DATATYPE, TOKENTYPE, TOKENCATEGORY } from "~/Enums";
import { searchTokenByCallback } from "~/Utils/ExpressionDesignerUtils";
import {
  EverInteractiveChip,
  EverSelect,
  EverModal,
  EverButton,
  EverInput,
  EverTg,
  EverLoader,
} from "~/v2/components";

import { useVariables } from "./useVariables";

export const ContainsFunctionButton = observer((props) => {
  const {
    name,
    onSelection,
    enabled,
    onFocusChange,
    datasheetId,
    dsVariableStore,
    hideCalculatedFields,
    columnTokens,
  } = props;
  const [visible, showModal] = useState(false);
  const [token, setToken] = useState(null);
  const [stringToken, setStringToken] = useState(null);
  const [isDatasheetField, setIsDatasheetField] = useState(false);
  const { loading } = dsVariableStore;
  const requiredVariables = useVariables({
    datasheetId,
    dataTypes: [DATATYPE.STRING, DATATYPE.HIERARCHY],
    excludeSelectedCalculatedField: true,
    dsVariableStore,
    hideCalculatedFields,
    columnTokens,
  });

  const onCancel = () => {
    showModal(false);
    setToken(null);
    setStringToken(null);
  };

  const getSelectOptions = (remove_hierarchy = false) => {
    let finalVariables = requiredVariables;
    if (remove_hierarchy) {
      finalVariables = requiredVariables.filter(
        (stringToken) => stringToken.dataType != DATATYPE.HIERARCHY
      );
    }
    return finalVariables.map((option) => (
      <EverSelect.Option
        value={option.meta.systemName}
        label={option.name}
        type={option.type}
        key={option.meta.systemName}
        dataType={option.dataType}
      >
        {option.name}
      </EverSelect.Option>
    ));
  };

  const getIsDisabled = () => {
    return (
      isEmpty(token) ||
      isEmpty(stringToken) ||
      (!isDatasheetField && !stringToken.name)
    );
  };

  const disableClassName = enabled
    ? "text-ever-base-content"
    : "text-ever-base-content-low";

  const chipClassName = `font-medium rounded-xl bg-ever-base-100 ${disableClassName}`;

  return (
    <div>
      <EverInteractiveChip
        onClick={() => {
          showModal(true);
          onFocusChange(false);
        }}
        disabled={!enabled}
        className={chipClassName}
        prepend={<FunctionIcon className="w-3 h-3" />}
        title={name}
      />

      <EverModal
        destroyOnClose={true}
        visible={visible}
        centered={true}
        width={700}
        title={
          <Space direction="horizontal">
            <EverTg.Heading3>{name}</EverTg.Heading3>
          </Space>
        }
        onCancel={onCancel}
        okButtonProps={{
          disabled: getIsDisabled(),
        }}
        footer={
          <Row>
            <Col className={"flex justify-end"} span={24} align="right">
              <EverButton type="filled" color="base" onClick={onCancel}>
                Cancel
              </EverButton>
              <EverButton
                type="filled"
                color="primary"
                disabled={getIsDisabled()}
                onClick={() => {
                  const fnParam = stringToken?.name ?? stringToken;
                  const fnDisplayLabel = `${name}(${token.name}, ${fnParam})`;
                  const fnArgs = [token, stringToken];
                  onSelection({
                    functionName: name,
                    name: fnDisplayLabel,
                    args: fnArgs,
                    dataType: DATATYPE.BOOLEAN,
                  });
                  showModal(false);
                  setToken(null);
                  setStringToken(null);
                }}
              >
                Apply
              </EverButton>
            </Col>
          </Row>
        }
      >
        <Row>
          <Col span={24}>
            <Space>
              <EverTg.SubHeading4 className="text-ever-base-content">{`${name} (`}</EverTg.SubHeading4>
              <EverSelect
                className={"w-40"}
                showArrow={true}
                filterOption
                allowClear
                placeholder="Text field"
                onChange={(v) => {
                  setToken(
                    searchTokenByCallback(
                      v,
                      (option) => {
                        return option.meta.systemName;
                      },
                      requiredVariables
                    )
                  );
                }}
                notFoundContent={
                  loading ? (
                    <div className="w-full h-full flex items-center">
                      <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
                      Variables
                    </div>
                  ) : null
                }
                showSearch
              >
                {getSelectOptions()}
              </EverSelect>

              <EverSelect
                showArrow={true}
                allowClear
                className={"w-44"}
                placeholder="Constant/datasheet field"
                onChange={(val) => {
                  setIsDatasheetField(val === "Datasheet field");
                  setStringToken(null);
                }}
              >
                <EverSelect.Option value="Constant" key="Constant">
                  Constant
                </EverSelect.Option>
                <EverSelect.Option
                  value="Datasheet field"
                  key="Datasheet field"
                >
                  Datasheet field
                </EverSelect.Option>
              </EverSelect>

              {isDatasheetField ? (
                <EverSelect
                  className={"w-40"}
                  showArrow={true}
                  filterOption
                  allowClear
                  placeholder="Query text"
                  onChange={(variable) => {
                    setStringToken(
                      searchTokenByCallback(
                        variable,
                        (option) => {
                          return option.meta.systemName;
                        },
                        requiredVariables
                      )
                    );
                  }}
                  notFoundContent={
                    loading ? (
                      <div className="w-full h-full flex items-center">
                        <EverLoader.SpinnerLottie className="w-10 h-10" />{" "}
                        Loading Variables
                      </div>
                    ) : null
                  }
                  showSearch
                >
                  {
                    getSelectOptions(true)
                    /* passing true to remove hierarchy column data type from options */
                  }
                </EverSelect>
              ) : (
                <EverInput
                  className="w-40"
                  placeholder="Query text"
                  onChange={(event) => {
                    const value = event.target.value;
                    const token = {
                      functionName: "CONSTANT",
                      name: value,
                      args: [DATATYPE.STRING, value],
                      dataType: DATATYPE.STRING,
                      type: TOKENTYPE.VARIABLE,
                      tokenCategory: TOKENCATEGORY.DYNAMIC,
                    };
                    setStringToken(token);
                  }}
                />
              )}
              <EverTg.SubHeading4 className="text-ever-base-content">
                {")"}
              </EverTg.SubHeading4>
            </Space>
          </Col>
        </Row>
      </EverModal>
    </div>
  );
});

export default ContainsFunctionButton;
