import { useQuery, gql } from "@apollo/client";
import { FunctionIcon } from "@everstage/evericons/outlined";
import { Space, Row, Col, Empty } from "antd";
import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import React, { useState, useMemo } from "react";

import { DATATYPE } from "~/Enums";
import { searchTokenByCallback } from "~/Utils/ExpressionDesignerUtils";
import {
  EverInteractiveChip,
  EverSelect,
  EverModal,
  EverButton,
  EverTg,
  EverLoader,
} from "~/v2/components";

import { useVariables } from "./useVariables";

const GET_TIMEZONE_LIST = gql`
  query AllNotificationTimezones {
    allNotificationTimezones
  }
`;

const DateTimezoneFunctionButton = observer((props) => {
  const {
    systemName,
    displayName,
    onSelection,
    enabled,
    onFocusChange,
    datasheetId,

    dsVariableStore,
    hideCalculatedFields,
    columnTokens,
  } = props;

  const { loading: tzLoading, data: tzData } = useQuery(GET_TIMEZONE_LIST, {
    fetchPolicy: "cache-first",
  });
  const { timezones, timezoneToLabel } = useMemo(() => {
    if (isEmpty(tzData)) return { timezones: [], timezoneToLabel: {} };

    const timezoneItems = tzData.allNotificationTimezones.map((item) =>
      JSON.parse(item)
    );

    const timezoneMap = {};
    timezoneItems.forEach((timezone) => {
      timezoneMap[timezone.value] = timezone.label;
    });

    return { timezones: timezoneItems, timezoneToLabel: timezoneMap };
  }, [tzData]);

  const [visible, showModal] = useState(false);

  const [token, setToken] = useState(null);
  const [fromTimezone, setFromTimezone] = useState("UTC");
  const [toTimezone, setToTimezone] = useState(null);

  const { loading } = dsVariableStore;

  const dateVariables = useVariables({
    datasheetId,
    dataTypes: [DATATYPE.DATE],
    excludeSelectedCalculatedField: true,
    dsVariableStore,
    hideCalculatedFields,
    columnTokens,
  });

  const onCancel = () => {
    showModal(false);
    setToken(null);
    setFromTimezone("UTC");
    setToTimezone(null);
  };

  const getSelectOptions = () => {
    return dateVariables.map((option) => (
      <EverSelect.Option
        value={option.meta.systemName}
        key={option.meta.systemName}
      >
        {option.name}
      </EverSelect.Option>
    ));
  };

  const disableClassName = enabled
    ? "text-ever-base-content"
    : "text-ever-base-content-low";

  const chipClassName = `font-medium rounded-xl bg-ever-base-100 ${disableClassName}`;

  return (
    <div>
      <EverInteractiveChip
        onClick={() => {
          showModal(true);
          onFocusChange(false);
        }}
        disabled={!enabled}
        prepend={<FunctionIcon className="w-3 h-3" />}
        title={displayName}
        className={chipClassName}
      />
      {/* {displayName}
      </Button> */}
      <EverModal
        destroyOnClose={true}
        visible={visible}
        centered={true}
        width={900}
        title={
          <Space direction="horizontal">
            <EverTg.Heading3>{displayName}</EverTg.Heading3>
          </Space>
        }
        onCancel={onCancel}
        footer={
          <Row>
            <Col className={"flex justify-end"} span={24} align="right">
              <EverButton type="filled" color="base" onClick={onCancel}>
                Cancel
              </EverButton>
              <EverButton
                type="filled"
                color="primary"
                disabled={
                  isEmpty(token) || isEmpty(fromTimezone) || isEmpty(toTimezone)
                }
                onClick={() => {
                  const fnDisplayLabel = `${displayName}(${token.name},${timezoneToLabel[fromTimezone]},${timezoneToLabel[toTimezone]})`;
                  const fnArgs = [token, fromTimezone, toTimezone];
                  onSelection({
                    functionName: systemName,
                    name: fnDisplayLabel,
                    args: fnArgs,
                    dataType: DATATYPE.DATE,
                  });
                  onCancel();
                }}
              >
                Apply
              </EverButton>
            </Col>
          </Row>
        }
      >
        <Row>
          <Col span={24}>
            <Space>
              <EverTg.SubHeading4 className="text-ever-base-content">
                {`${displayName} (`}
              </EverTg.SubHeading4>
              <EverSelect
                className={"w-44"}
                showArrow
                showSearch
                allowClear
                filterOption
                placeholder="Select Date Column"
                notFoundContent={
                  loading ? (
                    <div className="w-full h-full flex items-center">
                      <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
                      Variables
                    </div>
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )
                }
                onChange={(v) => {
                  setToken(
                    searchTokenByCallback(
                      v,
                      (option) => {
                        return option.meta.systemName;
                      },
                      dateVariables
                    )
                  );
                }}
              >
                {getSelectOptions()}
              </EverSelect>
              <EverSelect
                className={"w-60"}
                showArrow
                showSearch
                allowClear
                placeholder="From Timezone"
                value={fromTimezone}
                options={timezones}
                notFoundContent={
                  tzLoading ? (
                    <div className="w-full h-full flex items-center">
                      <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
                      Variables
                    </div>
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )
                }
                onChange={(value) => setFromTimezone(value)}
                filterOption={(input, option) =>
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              />
              <EverSelect
                className={"w-60"}
                showArrow
                showSearch
                allowClear
                placeholder="To Timezone"
                value={toTimezone}
                options={timezones}
                notFoundContent={
                  tzLoading ? (
                    <div className="w-full h-full flex items-center">
                      <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
                      Variables
                    </div>
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  )
                }
                onChange={(value) => setToTimezone(value)}
                filterOption={(input, option) =>
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              />
              <EverTg.SubHeading4 className="text-ever-base-content">
                {")"}
              </EverTg.SubHeading4>
            </Space>
          </Col>
        </Row>
      </EverModal>
    </div>
  );
});

export default DateTimezoneFunctionButton;
