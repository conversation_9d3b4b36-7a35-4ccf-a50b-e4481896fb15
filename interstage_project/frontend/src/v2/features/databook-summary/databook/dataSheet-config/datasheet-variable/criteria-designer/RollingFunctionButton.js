import { FunctionIcon } from "@everstage/evericons/outlined";
import { Col, Row, Space } from "antd";
import { cloneDeep, isEmpty } from "lodash";
import { observer } from "mobx-react";
import React, { useState } from "react";
import { twMerge } from "tailwind-merge";

import { DATATYPE } from "~/Enums";
import { searchTokenByCallback } from "~/Utils/ExpressionDesignerUtils";
import {
  EverInteractiveChip,
  EverModal,
  EverButton,
  EverTg,
  EverSelect,
  EverRadio,
  EverLabel,
  EverLoader,
} from "~/v2/components";

import { useVariables } from "./useVariables";

const orderByOptions = [
  { label: "Ascending", value: "asc" },
  { label: "Descending", value: "desc" },
];

const SelectDropdown = ({
  title,
  placeholder,
  options,
  className,
  onChange,
  loading,
  ...remainingProps
}) => (
  <Row className={twMerge("mb-6", className)}>
    <Col span={24}>
      <EverLabel className="mb-1">{title}</EverLabel>
      <EverSelect
        className="w-full"
        placeholder="Select Input Variable"
        onChange={onChange}
        notFoundContent={
          loading ? (
            <>
              <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
              Variables
            </>
          ) : null
        }
        showArrow
        showSearch
        allowClear
        {...remainingProps}
      >
        {options.map((option) => (
          <EverSelect.Option
            value={option.meta?.systemName || option.name}
            key={option.meta?.systemName || option.name}
          >
            {option.name}
          </EverSelect.Option>
        ))}
      </EverSelect>
    </Col>
  </Row>
);

const RollingFunctionButton = observer((props) => {
  const {
    name,
    strategy,
    onSelection,
    enabled,
    onFocusChange,
    datasheetId,
    dsVariableStore,
    hideCalculatedFields,
    columnTokens,
  } = props;
  const [visible, showModal] = useState(false);
  const [inputVariable, setInputVariable] = useState(null);
  const [partitionBy, setPartitionBy] = useState([]);
  const [orderBy, setOrderBy] = useState([]);
  const [sortData, setSortData] = useState({});

  const { variableTokens, loading } = dsVariableStore;

  const variableProps = {
    datasheetId,
    excludeSelectedCalculatedField: true,
    dsVariableStore,
    hideCalculatedFields,
    columnTokens,
  };

  const integerVariables = useVariables({
    dataTypes: [DATATYPE.INTEGER],
    ...variableProps,
  });

  const allVariables = useVariables({
    dataTypes: variableTokens.map((x) => x.dataType),
    ...variableProps,
  });

  const handleOpenModal = () => {
    showModal(true);
    onFocusChange(false);
  };

  const onCancel = () => {
    showModal(false);
    setInputVariable(null);
    setPartitionBy([]);
    setOrderBy([]);
    setSortData({});
  };

  const onColumnOrderChange = (systemName, order) => {
    const newSortData = cloneDeep(sortData);
    newSortData[systemName] = {
      columnName: systemName,
      order,
    };
    setSortData(newSortData);
  };

  const handleOrderByChange = (v) => {
    const variables = v.map((x) =>
      searchTokenByCallback(
        x,
        (option) => {
          return option.meta.systemName;
        },
        allVariables
      )
    );
    const sortDataKeys = Object.keys(sortData);
    const newSortData = {};
    variables.forEach((variable) => {
      const systemName = variable.meta.systemName;
      if (sortDataKeys.includes(systemName)) {
        newSortData[systemName] = sortData[systemName];
      } else {
        newSortData[systemName] = {
          columnName: systemName,
          order: orderByOptions[0].value,
        };
      }
    });
    setOrderBy(variables);
    setSortData(newSortData);
  };

  const handleApply = () => {
    const fnDisplayLabel = `${name}(${inputVariable.name}${
      partitionBy.length > 0
        ? `, Partition by ${partitionBy.map((x) => x.name).join(", ")}`
        : ""
    })`;
    const sub_payload = {
      rolling: {
        valueColumn: inputVariable.meta.systemName,
        partition: partitionBy.map((p) => p.meta.systemName),
        sortData: Object.values(sortData),
        rollingStrategy: strategy,
      },
    };
    onSelection({
      functionName: "Rolling",
      name: fnDisplayLabel,
      args: sub_payload,
      dataType: DATATYPE.INTEGER,
    });
    onCancel();
  };

  return (
    <div>
      <EverInteractiveChip
        onClick={handleOpenModal}
        disabled={!enabled}
        className="font-medium rounded-xl bg-ever-base-100 text-ever-base-content"
        prepend={<FunctionIcon className="w-3 h-3" />}
        title={name}
      />
      <EverModal
        visible={visible}
        title={
          <Space direction="horizontal">
            <EverTg.Heading3>{name}</EverTg.Heading3>
          </Space>
        }
        onCancel={onCancel}
        cancelButtonProps={{ className: "btn-cancel" }}
        footer={
          <Row>
            <Col className={"flex justify-end"} span={24} align="right">
              <EverButton type="filled" color="base" onClick={onCancel}>
                Cancel
              </EverButton>
              <EverButton
                type="filled"
                color="primary"
                disabled={isEmpty(inputVariable) || isEmpty(orderBy)}
                onClick={handleApply}
              >
                Apply
              </EverButton>
            </Col>
          </Row>
        }
        destroyOnClose
        centered
      >
        <SelectDropdown
          title="Input Variable"
          placeholder="Select Input Variable"
          filterOption
          onChange={(v) =>
            setInputVariable(
              searchTokenByCallback(
                v,
                (option) => {
                  return option.meta.systemName;
                },
                integerVariables
              )
            )
          }
          loading={loading}
          options={integerVariables}
        />
        <SelectDropdown
          mode="multiple"
          title="Partition By"
          filterOption
          placeholder="Select Partition"
          onChange={(v) =>
            setPartitionBy(
              v.map((x) =>
                searchTokenByCallback(
                  x,
                  (option) => {
                    return option.meta.systemName;
                  },
                  allVariables
                )
              )
            )
          }
          loading={loading}
          options={allVariables}
        />
        <SelectDropdown
          mode="multiple"
          title="Order By"
          placeholder="Select Order"
          onChange={handleOrderByChange}
          loading={loading}
          filterOption
          options={allVariables}
          className="mb-4"
        />
        <Row>
          <Col span={24} className="flex flex-col gap-y-4">
            {orderBy.map((variable) => {
              const key = variable.meta.systemName;
              return (
                <div key={key} className="flex items-center justify-between">
                  <EverLabel>{variable.name}</EverLabel>
                  <EverRadio.Group
                    className="whitespace-nowrap"
                    options={orderByOptions}
                    onChange={(event) =>
                      onColumnOrderChange(key, event.target.value)
                    }
                    value={sortData?.[key]?.order || orderByOptions[0].value}
                  />
                </div>
              );
            })}
          </Col>
        </Row>
      </EverModal>
    </div>
  );
});

export default RollingFunctionButton;
