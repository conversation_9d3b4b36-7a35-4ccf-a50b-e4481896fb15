import { ChevronDownIcon, FunctionIcon } from "@everstage/evericons/outlined";
import { Space, Row, Col } from "antd";
import { includes, isEmpty } from "lodash";
import { observer } from "mobx-react";
import React, { useState } from "react";

import { DATATYPE } from "~/Enums";
import { searchTokenByCallback } from "~/Utils/ExpressionDesignerUtils";
import {
  EverModal,
  EverSelect,
  EverTg,
  EverButton,
  EverInteractiveChip,
  EverLoader,
} from "~/v2/components";

import { useVariables } from "./useVariables";

const DateDiffFunctionButton = observer((props) => {
  const {
    name,
    onSelection,
    enabled,
    onFocusChange,
    datasheetId,
    dsVariableStore,
    hideCalculatedFields,
    columnTokens,
  } = props;
  const [visible, showModal] = useState(false);
  const [token1, setToken1] = useState(null);
  const [token2, setToken2] = useState(null);
  const [period, setPeriod] = useState("Day");
  const [diffType, setDiffType] = useState("Fiscal");
  const { loading } = dsVariableStore;
  const dateVariables = useVariables({
    datasheetId,
    dataTypes: [DATATYPE.DATE],
    excludeSelectedCalculatedField: true,
    dsVariableStore,
    hideCalculatedFields,
    columnTokens,
  });

  const onCancel = () => {
    showModal(false);
    setToken1(null);
    setToken2(null);
    setPeriod("Day");
    setDiffType("Fiscal");
  };

  const getSelectOptions = () => {
    return dateVariables.map((option) => (
      <EverSelect.Option
        value={option.meta.systemName}
        key={option.meta.systemName}
      >
        {option.name}
      </EverSelect.Option>
    ));
  };

  const disableClassName = enabled
    ? "text-ever-base-content"
    : "text-ever-base-content-low";

  const chipClassName = `font-medium rounded-xl bg-ever-base-100 ${disableClassName}`;

  return (
    <div>
      <EverInteractiveChip
        onClick={() => {
          showModal(true);
          onFocusChange(false);
        }}
        disabled={!enabled}
        prepend={<FunctionIcon className={`w-3 h-3 ${disableClassName}`} />}
        title={name}
        className={chipClassName}
      />

      <EverModal
        destroyOnClose={true}
        visible={visible}
        centered={true}
        title={
          <Space direction="horizontal">
            <EverTg.Heading3>{name}</EverTg.Heading3>
          </Space>
        }
        onCancel={onCancel}
        width={800}
        //okText="Apply"

        footer={
          <Row>
            <Col className={"flex justify-end"} span={24} align="right">
              <EverButton type="filled" color="base" onClick={onCancel}>
                Cancel
              </EverButton>
              <EverButton
                type="filled"
                color="primary"
                disabled={
                  isEmpty(token1) ||
                  isEmpty(token2) ||
                  isEmpty(period) ||
                  (includes(["Quarter", "Halfyear", "Year"], period) &&
                    isEmpty(diffType))
                }
                onClick={() => {
                  const diffTypeLabel = includes(
                    ["Quarter", "Halfyear", "Year"],
                    period
                  )
                    ? `,${diffType}`
                    : "";
                  const fnDisplayLabel = `${name}(${token1.name},${token2.name},${period}${diffTypeLabel})`;
                  const fnArgs = [token1, token2, period, diffType];
                  onSelection({
                    functionName: name,
                    name: fnDisplayLabel,
                    args: fnArgs,
                    dataType: DATATYPE.INTEGER,
                  });
                  onCancel();
                }}
              >
                Apply
              </EverButton>
            </Col>
          </Row>
        }
      >
        <Row>
          <Col span={24}>
            <Space>
              <EverTg.SubHeading4 className="text-ever-base-content">{`${name} (`}</EverTg.SubHeading4>
              <EverSelect
                className={"w-44"}
                showArrow={true}
                filterOption
                suffixIcon={<ChevronDownIcon />}
                placeholder="Start Date Column"
                onChange={(v) => {
                  setToken1(
                    searchTokenByCallback(
                      v,
                      (option) => {
                        return option.meta.systemName;
                      },
                      dateVariables
                    )
                  );
                }}
                notFoundContent={
                  loading ? (
                    <div className="w-full h-full flex items-center">
                      <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
                      Variables
                    </div>
                  ) : null
                }
                showSearch
              >
                {getSelectOptions()}
              </EverSelect>
              <EverSelect
                className={"w-44"}
                showArrow={true}
                filterOption
                suffixIcon={<ChevronDownIcon />}
                placeholder="End Date Column"
                onChange={(v) => {
                  setToken2(
                    searchTokenByCallback(
                      v,
                      (option) => {
                        return option.meta.systemName;
                      },
                      dateVariables
                    )
                  );
                }}
                notFoundContent={
                  loading ? (
                    <div className="w-full h-full flex items-center">
                      <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
                      Variables
                    </div>
                  ) : null
                }
                showSearch
              >
                {getSelectOptions()}
              </EverSelect>
              <EverSelect
                className={"w-32"}
                showArrow={true}
                suffixIcon={<ChevronDownIcon />}
                value={period}
                placeholder="Select Unit"
                onChange={(v) => setPeriod(v)}
              >
                <EverSelect.Option value="Day">Day</EverSelect.Option>
                <EverSelect.Option value="Month">Month</EverSelect.Option>
                <EverSelect.Option value="Quarter">Quarter</EverSelect.Option>
                <EverSelect.Option value="Halfyear">Halfyear</EverSelect.Option>
                <EverSelect.Option value="Year">Year</EverSelect.Option>
              </EverSelect>
              {includes(["Quarter", "Halfyear", "Year"], period) && (
                <EverSelect
                  className={"w-32"}
                  showArrow={true}
                  suffixIcon={<ChevronDownIcon />}
                  value={diffType}
                  onChange={setDiffType}
                >
                  <EverSelect.Option value="Fiscal">FISCAL</EverSelect.Option>
                  <EverSelect.Option value="Calendar">
                    CALENDAR
                  </EverSelect.Option>
                </EverSelect>
              )}
              <EverTg.SubHeading4 className="text-ever-base-content">
                {")"}
              </EverTg.SubHeading4>
            </Space>
          </Col>
        </Row>
      </EverModal>
    </div>
  );
});

export default DateDiffFunctionButton;
