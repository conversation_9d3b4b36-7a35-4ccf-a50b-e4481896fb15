import { FunctionIcon } from "@everstage/evericons/outlined";
import { Space, Row, Col } from "antd";
import { isEmpty } from "lodash";
import { observer } from "mobx-react";
import React, { useState } from "react";

import { DATATYPE } from "~/Enums";
import { searchTokenByCallback } from "~/Utils/ExpressionDesignerUtils";
import {
  EverInteractiveChip,
  EverSelect,
  EverModal,
  EverButton,
  EverTg,
  EverLoader,
} from "~/v2/components";

import { useVariables } from "./useVariables";

const SimpleStringFunctionButton = observer((props) => {
  const {
    name,
    onSelection,
    enabled,
    onFocusChange,
    datasheetId,

    returnType,
    dsVariableStore,
    hideCalculatedFields,
    columnTokens,
  } = props;
  const [visible, showModal] = useState(false);
  const [token1, setToken1] = useState(null);

  const { loading } = dsVariableStore;
  const stringVariables = useVariables({
    datasheetId,
    dataTypes: [DATATYPE.STRING],
    excludeSelectedCalculatedField: true,
    dsVariableStore,
    hideCalculatedFields,
    columnTokens,
  });

  const onCancel = () => {
    showModal(false);
    setToken1(null);
  };

  const getSelectOptions = () => {
    return stringVariables.map((option) => (
      <EverSelect.Option
        value={option.meta.systemName}
        key={option.meta.systemName}
      >
        {option.name}
      </EverSelect.Option>
    ));
  };

  const disableClassName = enabled
    ? "text-ever-base-content"
    : "text-ever-base-content-low";

  const chipClassName = `font-medium rounded-xl bg-ever-base-100 ${disableClassName}`;

  return (
    <div>
      <EverInteractiveChip
        onClick={() => {
          showModal(true);
          onFocusChange(false);
        }}
        disabled={!enabled}
        prepend={<FunctionIcon className="w-3 h-3" />}
        title={name}
        className={chipClassName}
      />
      {/* {name}
      </Button> */}
      <EverModal
        destroyOnClose={true}
        visible={visible}
        centered={true}
        title={
          <Space direction="horizontal">
            <EverTg.Heading3>{name}</EverTg.Heading3>
          </Space>
        }
        onCancel={onCancel}
        width={700}
        footer={
          <Row>
            <Col className={"flex justify-end"} span={24} align="right">
              <EverButton type="filled" color="base" onClick={onCancel}>
                Cancel
              </EverButton>
              <EverButton
                type="filled"
                color="primary"
                disabled={isEmpty(token1)}
                onClick={() => {
                  const fnDisplayLabel = `${name}(${token1.name})`;
                  const fnArgs = [token1];
                  onSelection({
                    functionName: name,
                    name: fnDisplayLabel,
                    args: fnArgs,
                    dataType: returnType,
                  });
                  onCancel();
                }}
              >
                Apply
              </EverButton>
            </Col>
          </Row>
        }
      >
        <Row>
          <Col span={24}>
            <Space>
              <EverTg.SubHeading4 className="text-ever-base-content">{`${name} (`}</EverTg.SubHeading4>
              <EverSelect
                showArrow={true}
                className={"w-52"}
                filterOption
                allowClear
                placeholder="Select String Column"
                onChange={(v) => {
                  setToken1(
                    searchTokenByCallback(
                      v,
                      (option) => {
                        return option.meta.systemName;
                      },
                      stringVariables
                    )
                  );
                }}
                notFoundContent={
                  loading ? (
                    <div className="w-full h-full flex items-center">
                      <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
                      Variables
                    </div>
                  ) : null
                }
                showSearch
              >
                {getSelectOptions()}
              </EverSelect>
              <EverTg.SubHeading4 className="text-ever-base-content">
                {")"}
              </EverTg.SubHeading4>
            </Space>
          </Col>
        </Row>
      </EverModal>
    </div>
  );
});

export default SimpleStringFunctionButton;
