import { gql, useQuery } from "@apollo/client";
import { cloneDeep, isEmpty, orderBy } from "lodash";
import { observer, useLocalStore } from "mobx-react";
import moment from "moment";
import React, { useEffect, useRef, useState } from "react";
import { useQuery as useReactQuery } from "react-query";
import { useLocation } from "react-router-dom";

import { getPayoutFrequencyList } from "~/Api/CommissionPlanService";
import { getFilteredEmployees } from "~/Api/EmployeeService";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { sortCallbackUtil } from "~/Utils/sortColumnsUtils";
import { EverHotToastBanner, EverLoader, toast } from "~/v2/components";
import { getAllUserRoles } from "~/v2/features/admin-settings/user-roles/services";

import EmployeeSummary from "./employee-summary";
import EmployeeViewStore from "./store";

/**
 * @typedef {import("~/Utils/sortColumnsUtils").SortInfoType} SortInfoType
 */

const GET_QUICK_FILTERS_COUNT = gql`
  query GetQuickFiltersCount {
    quickFiltersCount
  }
`;

const GET_MANAGERS = gql`
  query GetManagers {
    allManagers {
      managerDetails {
        employeeEmailId
        firstName
        lastName
      }
    }
  }
`;

const GET_NON_EMPLOYEES = gql`
  query NonEmployeeData {
    allActiveCountries {
      countryCode
      countryName
      currencyCode
    }
    activeCustomFieldsByClient {
      systemName
      isMandatory
      options
      fieldType
      displayOrder
      displayName
    }
    activeCustomFieldsByClientImportUser {
      systemName
      isMandatory
      options
      fieldType
      displayName
      isEffectiveDated
    }
  }
`;

const sortAllUserRoles = (userRoles) => {
  const sortedUserRoles = orderBy(
    userRoles,
    [
      (userRole) =>
        moment(userRole.createdAt, moment.HTML5_FMT.DATETIME_LOCAL_MS),
    ],
    ["asc"]
  );
  return sortedUserRoles;
};

const EmployeeView = observer(() => {
  const employeeViewStore = useLocalStore(() => new EmployeeViewStore());
  const { accessToken } = useAuthStore();

  const abortController = useRef();

  const [orderbyFields, setOrderbyFields] = useState(
    /** @type {SortInfoType[]}*/ ([])
  );
  const [pageSize, setPageSize] = useState(20);
  const [totalRows, setTotalRows] = useState(25);
  const [pageNumber, setPageNumber] = useState(1);
  const [searchTerm, setSearchTerm] = useState(() => {
    const queryParams = new URLSearchParams(window.location.search);
    const userSearchParam = queryParams.get("search");
    return userSearchParam || "";
  });
  const [filterMode, setFilterMode] = useState({
    value: false,
    constraints: null,
  });
  const [eLoadingREST, setELoadingREST] = useState(false);
  const [quickFilterCount, setQuickFilterCount] = useState({});
  const [alluserRoles, setAllUserRoles] = useState([]);

  const {
    activeCustomFieldsMap,
    setActiveCustomFields,
    activeCustomFieldsForImportUserMap,
    setActiveCustomFieldsForImportUser,
    setEmployees,
    setCountries,
    setManagers,
    countries,
    employees,
    managerMap,
    createUserRoleMap,
    createDisplayNameToUserRoleMap,
    displayNameToUserRoleMap,
    userRolesToDisplayNameMap,
    setPayoutFreqList,
    payoutFreqList,
    payoutFrequencyLabelMap,
    payoutFrequencyValueMap,
  } = employeeViewStore;

  const { refetch: qFilterRefetch, loading: qLoading } = useQuery(
    GET_QUICK_FILTERS_COUNT,
    {
      onCompleted: (qData) => {
        setQuickFilterCount(JSON.parse(qData.quickFiltersCount));
      },
      fetchPolicy: "no-cache",
      notifyOnNetworkStatusChange: true,
    }
  );

  const location = useLocation();

  // Country and custom fields data fetch
  const { loading } = useQuery(GET_NON_EMPLOYEES, {
    onCompleted: (allData) => {
      setCountries(allData.allActiveCountries || []);
      setActiveCustomFields(allData.activeCustomFieldsByClient || []);
      setActiveCustomFieldsForImportUser(
        allData.activeCustomFieldsByClientImportUser || []
      );
    },
    fetchPolicy: "no-cache",
  });

  // manager details data fetch
  const { loading: managerLoading, refetch: managerRefetch } = useQuery(
    GET_MANAGERS,
    {
      onCompleted: (managerData) => {
        setManagers(managerData.allManagers || []);
      },
      fetchPolicy: "no-cache",
    }
  );

  useReactQuery(
    ["getPayoutFrequencyList", "users"],
    () => getPayoutFrequencyList(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
      onSuccess: (response) => {
        setPayoutFreqList(response);
      },
    }
  );

  const getUsersData = async (signal) => {
    setELoadingREST(true);

    // setting the seach param in url
    let searchParams = new URLSearchParams(window.location.search);
    if (searchTerm) searchParams.set("search", searchTerm);
    else searchParams.delete("search");
    const newSearchString = searchParams.toString();
    if (`?${newSearchString}` !== window.location.search) {
      window.history.replaceState(
        null,
        "",
        `${location.pathname}${newSearchString ? `?${newSearchString}` : ""}`
      );
    }

    //REST CALL
    const requestObj = cloneDeep(filterMode.constraints) ?? {};
    if (!isEmpty(requestObj)) {
      requestObj["custom_field"] = {};
    }
    //group custom objects together
    Object.keys(requestObj).forEach((item) => {
      if (item.startsWith("cf_")) {
        requestObj["custom_field"] = {
          ...requestObj["custom_field"],
          [item]: requestObj[item],
        };
        delete requestObj[item];
      }
    });

    const retVal = await getFilteredEmployees(
      {
        data: requestObj,
        pagination: {
          offsetValue: pageNumber - 1,
          limitValue: pageSize,
        },
        searchTerm: searchTerm || null,
        orderbyFields,
      },
      accessToken,
      signal
    );
    if (retVal.ok) {
      const jsonVal = await retVal.json();
      setTotalRows(jsonVal.count);
      setEmployees(jsonVal.data || []);
    } else {
      toast.custom(() => (
        <EverHotToastBanner type="error" description="Error while fetching" />
      ));
    }
    setELoadingREST(false);
  };

  const sortCallback = (column) => {
    sortCallbackUtil(column, orderbyFields, setOrderbyFields);
  };

  const handleChangeOrderbyFields = (sortCols) => {
    setOrderbyFields(sortCols);
  };

  // Reset page number to 1 on filters or search text changes
  const resetPageNumber = () => {
    setPageNumber(1);
  };

  const handleChangeFilterMode = (values) => {
    setFilterMode(values);
    resetPageNumber();
  };

  useEffect(() => {
    getAllUserRoles(accessToken)
      .then((userRoles) => {
        /*
          userRoles => [{ displayName : String, rolePermissionId: String  }]
        */
        if (!isEmpty(userRoles)) {
          const sortedAllUserRoles = sortAllUserRoles(userRoles);
          setAllUserRoles(sortedAllUserRoles);
          createUserRoleMap(sortedAllUserRoles);
          createDisplayNameToUserRoleMap(sortedAllUserRoles);
        } else {
          setAllUserRoles([]);
          createUserRoleMap([]);
          createDisplayNameToUserRoleMap([]);
        }
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  // everytime pagination changes || searchterm changes, this useEffect will be triggered.
  useEffect(() => {
    // abort pending network call
    if (abortController.current) {
      abortController.current.abort();
    }
    abortController.current = new AbortController();
    const { signal } = abortController.current;

    getUsersData(signal);
  }, [pageNumber, pageSize, searchTerm, filterMode, orderbyFields]);

  return (
    <>
      {loading && managerLoading ? (
        <EverLoader size={5} />
      ) : (
        <div className="h-full">
          <EmployeeSummary
            countries={countries}
            managerMap={managerMap}
            employees={employees}
            eLoading={eLoadingREST}
            qFilterLoading={qLoading}
            refreshEmployees={() => {
              getUsersData();
              qFilterRefetch();
            }}
            refreshManagers={managerRefetch}
            activeCustomFieldsMap={activeCustomFieldsMap}
            // -- search props -- //
            setSearchTerm={setSearchTerm}
            searchTerm={searchTerm}
            // -- pagination props -- //
            pageCount={Math.ceil(totalRows / pageSize)} //number of pages
            pageSize={pageSize} //rows per page
            totalRows={totalRows} //total rows
            currentPage={pageNumber - 1} //current page number
            setPageSize={setPageSize}
            setCurrentPage={setPageNumber}
            handleChangeFilterMode={handleChangeFilterMode}
            resetPageNumber={resetPageNumber}
            orderbyFields={orderbyFields}
            handleChangeOrderbyFields={handleChangeOrderbyFields}
            sortCallback={sortCallback}
            filterMode={filterMode}
            activeCustomFieldsForImportUserMap={
              activeCustomFieldsForImportUserMap
            }
            quickFilterCount={quickFilterCount}
            alluserRoles={alluserRoles}
            userRolesToDisplayNameMap={userRolesToDisplayNameMap}
            displayNameToUserRoleMap={displayNameToUserRoleMap}
            payoutFreqList={payoutFreqList}
            payoutFrequencyLabelMap={payoutFrequencyLabelMap}
            payoutFrequencyValueMap={payoutFrequencyValueMap}
          />
        </div>
      )}
    </>
  );
});

export default EmployeeView;
