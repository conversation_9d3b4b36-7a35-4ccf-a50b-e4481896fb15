import { XCloseIcon } from "@everstage/evericons/outlined";
import { AlertTriangleIcon } from "@everstage/evericons/solid";
import { AgGridReact } from "ag-grid-react";
import { Space } from "antd";
import { cx } from "class-variance-authority";
// import { debounce } from "lodash";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { twMerge } from "tailwind-merge";

import { sendAuthInvite, updateUserSource } from "~/Api/EmployeeService";
import { HEADER_STATE, RBAC_ROLES } from "~/Enums";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { menuItemsCallback } from "~/Utils/agGridUtils";
import {
  EverButton,
  EverDivider,
  EverHotToastMessage,
  EverHotToastNotification,
  EverPopConfirm,
  EverTg,
  EverModal,
  EverSelect,
  toast,
  useLocalStorage,
  EverInteractiveChip,
} from "~/v2/components";
import {
  DynamicPagination,
  CustomHeader,
  everAgGridOptions,
  everAgGridCallbacks,
} from "~/v2/components/ag-grid";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import { searchingWithMagGlass } from "~/v2/images";

const onBulkInvite = ({
  refreshEmployees,
  selectedEmailSet,
  accessToken,
  headerCbxState,
  filterMode,
  searchTerm,
  clearSelection,
}) => {
  toast.custom(() => (
    <EverHotToastMessage type="loading" description="Sending Invites" />
  ));
  headerCbxState = headerCbxState === HEADER_STATE.EVERYTHING;
  return sendAuthInvite(
    {
      allSelected: headerCbxState,
      emailIds: headerCbxState ? [] : Array.from(selectedEmailSet),
      filterMode,
      searchTerm,
    },
    accessToken
  )
    .then((response) => {
      if (response.ok) {
        response.json().then((data) => {
          if (data.succeededEmails.length > 0 && data.failedEmails.length > 0) {
            toast.custom(() => (
              <EverHotToastMessage
                type="warning"
                description="Invites sent partially"
              />
            ));
            toast.custom((t) => (
              <EverHotToastNotification
                type="error"
                title="Failed emails"
                description={data.failedEmails.join(", ")}
                toastId={t.id}
              />
            ));
          } else if (data.succeededEmails.length > 0) {
            toast.custom(() => (
              <EverHotToastMessage
                type="success"
                description="Invites sent successfully"
              />
            ));
          } else {
            toast.custom(() => (
              <EverHotToastMessage
                type="error"
                description="Bulk invite failed"
              />
            ));
          }
        });

        clearSelection();
        refreshEmployees();
      }
    })
    .catch((err) => console.log("CONFIG ADD ERROR" + err));
};

async function onChangeUserSource({
  refreshEmployees,
  selectedEmailSet,
  accessToken,
  headerCbxState,
  filterMode,
  searchTerm,
  sourceType,
  clearSelection,
}) {
  const toastId = toast.custom((t) => (
    <EverHotToastMessage
      type="loading"
      description="Changing user source"
      toastId={t.id}
    />
  ));
  headerCbxState = headerCbxState === HEADER_STATE.EVERYTHING;
  return updateUserSource(
    {
      allSelected: headerCbxState,
      emailIds: headerCbxState ? [] : Array.from(selectedEmailSet),
      filterMode,
      searchTerm,
      updatedUserSource: sourceType,
    },
    accessToken
  )
    .then((response) => {
      if (response.ok) {
        response.json().then((data) => {
          toast.remove(toastId);
          if (data.status === "success") {
            toast.custom((t) => (
              <EverHotToastMessage
                type="success"
                description="User source updated successfully"
                toastId={t.id}
              />
            ));
          } else {
            toast.custom((t) => (
              <EverHotToastMessage
                type="error"
                description="User source updation failed"
                toastId={t.id}
              />
            ));
          }
        });
        clearSelection();
        refreshEmployees();
      } else {
        response.json().then((data) => {
          toast.remove(toastId);
          if (data) {
            toast.custom((t) => (
              <EverHotToastMessage
                type="error"
                description={data}
                toastId={t.id}
              />
            ));
          } else {
            toast.custom((t) => (
              <EverHotToastMessage
                type="error"
                description="User source updation failed"
                toastId={t.id}
              />
            ));
          }
        });
      }
    })
    .catch((err) => console.log("CONFIG ADD ERROR" + err));
}

const UsersTable = ({
  columnDefs,
  rowData,
  pageCount,
  pageSize,
  totalRows,
  currentPage,
  setPageSize,
  setCurrentPage,
  eLoading,
  selectedEmailSet,
  headerCbxState,
  setHeaderCbxState,
  accessToken,
  refreshEmployees,
  filterMode,
  searchTerm,
  clearSelection,
  maxSelectionCount,
  orderbyFields,
  handleChangeOrderbyFields,
}) => {
  const [displayPagination, setDisplayPagination] = useState(false);
  const gridRef = useRef();
  const parentDivRef = useRef();
  const [popconfirmVisible, setPopConfirmVisible] = useState(false);
  const [sourceTypeModalVisible, setSourceTypeModalVisible] = useState(false);
  const [sourceType, setSourceType] = useState(null);

  const [tableState, setTableState] = useLocalStorage(
    "everstage_users_table",
    null
  );
  const { hasPermissions } = useUserPermissionStore();
  const hasManageUsers = hasPermissions(RBAC_ROLES.MANAGE_USERS);

  const sideBar = useMemo(() => {
    return {
      toolPanels: [
        {
          width: 250,
          id: "columns",
          labelKey: "columns",
          labelDefault: "Customize Columns",
          iconKey: "columns",
          toolPanel: "agColumnsToolPanel",
          toolPanelParams: {
            suppressRowGroups: true,
            suppressValues: true,
            suppressPivots: true,
            suppressPivotMode: true,
            suppressColumnFilter: false,
            suppressColumnSelectAll: false,
            suppressColumnExpandAll: true,
          },
        },
      ],
      defaultToolPanel: "columns",
    };
  }, []);

  const defaultColDef = useMemo(() => {
    return {
      menuTabs: ["generalMenuTab"],
      sortable: false,
      minWidth: 100,
      filter: true,
      resizable: true,
    };
  }, []);

  const noRowsOverlayComponent = () => {
    return (
      <Space direction={"vertical"} size={"large"}>
        <img src={searchingWithMagGlass} className="w-60" />
        <EverTg.SubHeading1>No Users Found</EverTg.SubHeading1>
        <EverTg.Text>
          {"We couldn't find users that match your filters or search keywords."}
        </EverTg.Text>
      </Space>
    );
  };

  const getRowId = useCallback((params) => {
    return params.data.employeeEmailId;
  }, []);

  const saveTableState = useCallback(() => {
    const allState = gridRef.current.api.getColumnState();
    const orderAndVisibilityState = allState.map((state) => ({
      colId: state.colId,
      hide: state.hide,
    }));
    setTableState(orderAndVisibilityState);

    // Removing sort property if any for a column when the column is de-selected
    const visibleCols = allState
      .filter((state) => !state.hide)
      .map((state) => state.colId);

    const filterOrderbyFields = orderbyFields.filter((field) =>
      visibleCols.includes(field.column)
    );

    if (filterOrderbyFields.length !== orderbyFields.length) {
      handleChangeOrderbyFields(filterOrderbyFields);
    }
  }, [setTableState]);

  useEffect(() => {
    // If the user don't have manage:users permissions, we won't show the
    // checkbox column in UI. This code is related to checkbox selection in ag-grid.
    if (!hasManageUsers) return;

    // THIS IS FOR UPDATING SELECTION UI IN AG-GRID FROM 'selectedEmailSet'
    const stopSelection =
      selectedEmailSet.size >= maxSelectionCount &&
      headerCbxState !== HEADER_STATE.EVERYTHING;
    gridRef?.current?.api?.forEachNode((node) => {
      const { employeeEmailId } = node.data;
      if (stopSelection) {
        node.setDataValue(
          "checkbox",
          selectedEmailSet.has(employeeEmailId) ? true : "blocked"
        );
      } else {
        node.setDataValue("checkbox", selectedEmailSet.has(employeeEmailId));
      }
    });
  }, [selectedEmailSet]);

  const selectedCount =
    headerCbxState === HEADER_STATE.EVERYTHING
      ? `${totalRows}`
      : selectedEmailSet.size;

  useEffect(() => {
    gridRef?.current?.api?.ensureIndexVisible(0);
  }, [currentPage]);

  return (
    <>
      <div
        className={`h-full flex flex-col ${
          eLoading ? "opacity-70" : "opacity-100"
        }`}
      >
        <div
          className={cx(
            "flex items-center gap-6 transition-all duration-300 ease-out z-10 text-ever-primary",
            selectedEmailSet.size <= 0 &&
              headerCbxState !== HEADER_STATE.EVERYTHING
              ? "h-0 m-0 flex-none opacity-0 translate-y-[-32px] pointer-events-none"
              : "h-auto mb-5 flex-1 opacity-100 translate-y-0"
          )}
        >
          <div className="flex items-center w-full px-4 py-2 rounded-lg bg-ever-base-100">
            <div className="flex items-center gap-2">
              <EverInteractiveChip
                size="medium"
                title={
                  <EverTg.Text className="text-ever-base-content">
                    <span className="font-semibold">{selectedCount}</span>{" "}
                    selected
                  </EverTg.Text>
                }
                showIndicator={false}
                append={
                  <XCloseIcon
                    className="w-4 h-4 text-ever-base-content cursor-pointer"
                    onClick={clearSelection}
                  />
                }
              />

              {headerCbxState !== HEADER_STATE.EVERYTHING &&
                totalRows > pageSize && (
                  <>
                    {/* <EverDivider className="h-[24px]" type="vertical" /> */}

                    <EverButton
                      type="link"
                      size="small"
                      onClick={() => {
                        setHeaderCbxState(HEADER_STATE.EVERYTHING);
                      }}
                    >
                      <EverTg.Text>Select All {totalRows}</EverTg.Text>
                    </EverButton>
                  </>
                )}
              <EverDivider className="ml-2 mr-4 h-6" type="vertical" />
            </div>
            <EverPopConfirm
              title={`You’re about to invite ${selectedCount} user(s) to Everstage. Do you want to proceed?`}
              visible={popconfirmVisible}
              placement="top"
              onConfirm={() => {
                onBulkInvite({
                  refreshEmployees,
                  selectedEmailSet,
                  accessToken,
                  headerCbxState,
                  filterMode,
                  searchTerm,
                  clearSelection,
                });
                setPopConfirmVisible(false);
              }}
              onCancel={() => setPopConfirmVisible(false)}
              okText="Yes, proceed"
              cancelText="No, cancel"
            >
              <EverModal
                visible={sourceTypeModalVisible}
                onCancel={() => {
                  setSourceType(null);
                  setSourceTypeModalVisible(false);
                }}
                footer={null}
              >
                <Space
                  className="flex items-center justify-center"
                  direction="vertical"
                  size="small"
                >
                  <AlertTriangleIcon className="w-10 h-10 text-ever-warning" />
                  <EverTg.Heading3>
                    {" "}
                    {`Configure how you'd like to manage updates to selected users`}
                  </EverTg.Heading3>
                  {/* <EverTg.Heading3>
                    Please move them to a new role before deleting.
                  </EverTg.Heading3> */}
                  {/* <EverTg.Description>{`You can't undo this action`}</EverTg.Description> */}
                  <EverSelect
                    className="w-56 mt-4"
                    placeholder="Select new role"
                    allowClear
                    showArrow
                    showSearch
                    value={sourceType}
                    onChange={(value) => {
                      setSourceType(value);
                    }}
                  >
                    <EverSelect.Option value="manual">
                      Manually managed
                    </EverSelect.Option>
                    <EverSelect.Option value="hris">
                      Managed via integrations
                    </EverSelect.Option>
                  </EverSelect>
                  {sourceType === "hris" && (
                    <EverTg.Description className="mt-2">
                      <span className="font-medium">Important:</span> User
                      fields mapped in HRIS integration will become read-only
                      for selected users when you update this.
                    </EverTg.Description>
                  )}

                  <Space size="middle" direction="horizontal" className="mt-4">
                    <EverButton
                      type="ghost"
                      color="base"
                      onClick={() => {
                        setSourceType(null);
                        setSourceTypeModalVisible(false);
                      }}
                    >
                      Cancel
                    </EverButton>
                    <EverButton
                      disabled={sourceType === null}
                      // loading={isActionLoading}
                      onClick={() => {
                        setSourceTypeModalVisible(false);
                        onChangeUserSource({
                          refreshEmployees,
                          selectedEmailSet,
                          accessToken,
                          headerCbxState,
                          filterMode,
                          searchTerm,
                          clearSelection,
                          sourceType,
                        });
                      }}
                    >
                      Ok, update
                    </EverButton>
                  </Space>
                </Space>
              </EverModal>
              <RBACProtectedComponent permissionId={RBAC_ROLES.MANAGE_USERS}>
                <div className="flex flex-row items-center gap-2 text-ever-base-content">
                  <EverInteractiveChip
                    size="medium"
                    title={"Send Invite"}
                    showIndicator={false}
                    onClick={() => setPopConfirmVisible(true)}
                  />
                  <showSourceConfirmModal />
                  <EverInteractiveChip
                    size="medium"
                    title={"Set User Source"}
                    showIndicator={false}
                    onClick={() => setSourceTypeModalVisible(true)}
                  />
                </div>
              </RBACProtectedComponent>
              {/* <EverButton
                color="primary"
                type="filled"
                className="ml-[15px]"
                onClick={() => setPopConfirmVisible(true)}
              >
                <EverTg.Text>Send Invite</EverTg.Text>
              </EverButton> */}
            </EverPopConfirm>
          </div>
        </div>
        <div
          ref={parentDivRef}
          className={twMerge(
            "ag-theme-material zebra-grid rowHoverEnabled rowSelectable cellHeight100 overlay999 h-full w-full gap-1 flex flex-col flex-auto",
            eLoading ? "blur-sm" : "blur-0"
          )}
        >
          <AgGridReact
            getRowId={getRowId}
            onGridReady={(params) => {
              params.api.closeToolPanel();
            }}
            loading={eLoading}
            columnDefs={columnDefs}
            rowData={eLoading ? [] : rowData}
            {...everAgGridOptions.getDefaultOptions({ type: "md" })}
            rowHeight={56}
            suppressMenuHide={true}
            components={{ agColumnHeader: CustomHeader }}
            defaultColDef={defaultColDef}
            getMainMenuItems={menuItemsCallback}
            suppressCellFocus={true}
            enableCellTextSelection={true}
            ensureDomOrder={true}
            onFirstDataRendered={(params) => {
              setDisplayPagination(true);
              everAgGridCallbacks.adjustColumnWidth(params);
              try {
                if (tableState) {
                  params.api.applyColumnState({
                    state: tableState,
                    applyOrder: true,
                  });
                }
              } catch (e) {
                console.log("Error in restoring table state : ", e);
              }
            }}
            ref={gridRef}
            sideBar={sideBar}
            noRowsOverlayComponent={noRowsOverlayComponent}
            suppressContextMenu={false}
            rowSelection={"multiple"}
            suppressRowClickSelection={true}
            getContextMenuItems={() => {
              return ["copy", "copyWithHeaders"];
            }}
            onColumnVisible={(params) => {
              if (params.source === "toolPanelUi") {
                saveTableState();
              }
            }}
            paginationPageSize={pageSize}
            cacheBlockSize={pageSize}
            autoSizeStrategy={{
              type: "fitCellContents",
            }}
            processUnpinnedColumns={() => {
              // Prevents columns from getting unpinned automatically if center viewport size is lower than 50px
            }}
          ></AgGridReact>
          {displayPagination && (
            <DynamicPagination
              key={`${pageSize}${currentPage}`}
              minHeight={"50px"}
              rowPerPageOption={[20, 50, 100]}
              pageCount={pageCount}
              pageSize={pageSize}
              totalRows={totalRows}
              setPageSize={setPageSize}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              gridRef={gridRef}
            ></DynamicPagination>
          )}
        </div>
      </div>
    </>
  );
};

export default UsersTable;
