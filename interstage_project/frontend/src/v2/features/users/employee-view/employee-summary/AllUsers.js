import {
  EditIcon,
  LogInIcon,
  LogOutHalfIcon,
  MarkerPinIcon,
  SendIcon,
  UserCircleIcon,
  Trash03Icon,
  ReverseLeftIcon,
} from "@everstage/evericons/outlined";
import { DotsHorizontalIcon } from "@everstage/evericons/solid";
import { Divider, Dropdown, Menu } from "antd";
import { camelCase, cloneDeep, isEmpty, isNil, isNull } from "lodash";
import { observer } from "mobx-react";
import moment from "moment";
import PropTypes from "prop-types";
import React, { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useRecoilValue } from "recoil";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import {
  sendAuthInvite,
  exportRemoveUserValidations,
  initiateRevertExit,
  validateAndRemoveUser,
} from "~/Api/EmployeeService";
import { loginAsUser } from "~/Api/LoginLogoutService";
import {
  HEADER_STATE,
  MAPPING_STATUS,
  RBAC_ROLES,
  ANALYTICS_EVENTS,
  ANALYTICS_PROPERTIES,
  PROFILE_MODALS,
} from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { comparator, dateComparator, numComparator } from "~/Utils/agGridUtils";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { formatCurrency } from "~/Utils/CurrencyUtils";
import {
  EverButton,
  EverHotToastMessage,
  EverModal,
  EverTg,
  toast,
  useCurrentTheme,
  EverFormatter,
} from "~/v2/components";
import { cellRenderers } from "~/v2/components/ag-grid";
import { formatDate } from "~/v2/components/ever-formatter/EverFormatter";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import { UserStatusIndicator } from "~/v2/components/StatusIndicator";
import useProfileHook from "~/v2/features/profile-settings/profileHook";
import ProfileModal from "~/v2/features/profile-settings/ProfileModal";

import UsersTable from "./table";
import PlanRenderer from "./table/PlanRenderer.js";
import useCheckboxSelection from "./useCheckboxSelection";
import AddUserButton from "../add-user-button";
import EmployeeExitButton from "../employee-exit-button";
import MapPayeeButton from "../map-payee-button";

const { Text } = EverTg;

const { AvatarWithEmailRenderer, CheckboxRenderer, GroupHeaderCheckbox } =
  cellRenderers;

const RevertExitModal = ({ record, revertUserExit }) => {
  const [toggleEverModal, setToggleEverModal] = useState(false);
  return (
    <>
      <EverButton
        type="text"
        color="primary"
        size="small"
        onClick={() => setToggleEverModal(true)}
        prependIcon={<ReverseLeftIcon />}
        className="w-full justify-start"
      >
        Revert Exit
      </EverButton>
      <EverModal.Confirm
        visible={toggleEverModal}
        width={739}
        icon={<ReverseLeftIcon className="w-8 h-8" />}
        confirmationButtons={[
          <EverButton
            key="cancel"
            color="base"
            onClick={() => setToggleEverModal(false)}
            type="ghost"
          >
            Cancel
          </EverButton>,
          <EverButton
            key="accept"
            onClick={() => revertUserExit(record.employeeEmailId)}
          >
            Confirm
          </EverButton>,
        ]}
        subtitle={
          <div className="flex justify-center items-center flex-col gap-4 w-[643px]">
            <EverTg.Text>
              Are you sure do you want to revert the exit? This action cannot be
              undone.
            </EverTg.Text>
            <div className="flex flex-start gap-6">
              <div>
                <span className="text-ever-base-content font-semibold">
                  Exit Date:{" "}
                </span>
                <EverFormatter.Date
                  date={record?.exitDate}
                  className="font-normal"
                />
              </div>
              <div>
                <span className="text-ever-base-content font-semibold">
                  Deactivated Date:{" "}
                </span>
                <EverFormatter.Date
                  date={record?.deactivationDate}
                  className="font-normal"
                />
              </div>
              <div>
                <span className="text-ever-base-content font-semibold">
                  Last Commission Date:{" "}
                </span>
                <EverFormatter.Date
                  date={record?.lastCommissionDate}
                  className="font-normal"
                />
              </div>
            </div>
          </div>
        }
        title="Revert the exit action?"
      />
    </>
  );
};
const SendInviteModal = ({ record, sendInvite }) => {
  const [showInviteModal, setShowInviteModal] = useState(false);

  const inviteStatus =
    record.status === "Invited" || record.status === "Active";
  const inviteText = inviteStatus ? "Resend Invite" : "Send Invite";

  return (
    <>
      <EverButton
        type="text"
        color="base"
        size="small"
        onClick={() => setShowInviteModal(true)}
        prependIcon={<SendIcon className="text-ever-base-content-mid" />}
        className="w-full justify-start"
      >
        <Text>{inviteText}</Text>
      </EverButton>
      <EverModal.Confirm
        visible={showInviteModal}
        width={640}
        icon={<SendIcon className="w-8 h-8" />}
        confirmationButtons={[
          <EverButton
            key="cancel"
            color="base"
            onClick={() => setShowInviteModal(false)}
            type="ghost"
          >
            Cancel
          </EverButton>,
          <EverButton
            key="accept"
            onClick={() => sendInvite(record.employeeEmailId)}
          >
            Yes, send
          </EverButton>,
        ]}
        subtitle={`Are you sure you want to ${
          inviteStatus ? "resend" : "send"
        } the invite? This action cannot be undone.`}
        title={inviteStatus ? "Resend Invite?" : "Send an invite?"}
      />
    </>
  );
};

const getUpdatedUserFilter = (filters, emailId) => {
  let isAnyFilterApplied = false;

  const newFilters = cloneDeep(filters);

  if (Array.isArray(newFilters.reportingManager?.value)) {
    const reportingManager = newFilters.reportingManager.value.filter(
      (id) => id !== emailId
    );

    newFilters.reportingManager.value = isEmpty(reportingManager)
      ? null
      : reportingManager;
  }

  isAnyFilterApplied = Object.values(newFilters).some(
    (filter) => !isEmpty(filter) || typeof filter === "boolean"
  );

  return [newFilters, isAnyFilterApplied];
};

const RemoveUserModal = ({
  record,
  removeUser,
  accessToken,
  refreshManagers,
  handleChangeFilterMode,
  filters,
  setFilters,
  appliedFilters,
  setAppliedFilters,
}) => {
  const [_, setIsLoading] = useState(false);

  const handleRemoveUserInternal = async () => {
    setIsLoading(true);

    try {
      const emailId = record.employeeEmailId;
      const { status, reason, subReason, exportType } = await removeUser(
        emailId
      );
      if (status === "CAN_DELETE") {
        EverModal.confirm({
          width: 700,
          title: "Are you sure you want to delete this user?",
          subtitle: reason,
          centered: true,
          iconContainerClasses: "bg-ever-error-lite",
          icon: <Trash03Icon className="w-8 h-8 text-ever-error" />,
          confirmationButtons: [
            <EverButton
              key="cancel"
              color="base"
              type="ghost"
              onClick={() => {
                EverModal.destroyAll();
                setIsLoading(false);
              }}
            >
              Cancel
            </EverButton>,
            <EverButton
              key="delete"
              color="error"
              onClick={async () => {
                EverModal.destroyAll();

                const finalResponse = await removeUser(emailId, true);
                if (finalResponse.status === "SUCCESS") {
                  toast.custom(() => (
                    <EverHotToastMessage
                      type="success"
                      description="User has been deleted successfully"
                    />
                  ));
                  const [newFilters, isAnyNewFilters] = getUpdatedUserFilter(
                    filters,
                    emailId
                  );
                  const [newAppliedFilters] = getUpdatedUserFilter(
                    appliedFilters,
                    emailId
                  );
                  setFilters(newFilters);
                  setAppliedFilters(newAppliedFilters);

                  handleChangeFilterMode({
                    value: isAnyNewFilters,
                    constraints: newFilters,
                  });
                  refreshManagers();
                }
                setIsLoading(false);
              }}
            >
              Delete
            </EverButton>,
          ],
          ...(subReason && {
            noteMessage: (
              <div className="flex items-center justify-center gap-1">
                <EverButton
                  type="link"
                  className="!px-0 !text-sm underline font-medium"
                  onClick={() =>
                    exportRemoveUserValidations(
                      { emailId, exportType },
                      accessToken
                    )
                  }
                >
                  Download
                </EverButton>
                <EverTg.SubHeading4>{subReason}</EverTg.SubHeading4>
              </div>
            ),
          }),
        });
      } else if (status === "RESTRICTED") {
        setIsLoading(false);
        EverModal.info({
          width: 700,
          title: "This user cannot be deleted",
          subtitle: reason,
          centered: true,
          okText: "Got it",
          onOk: () => {},
          ...(subReason && {
            noteMessage: (
              <div className="flex items-center justify-center gap-1">
                <EverButton
                  type="link"
                  className="!px-0 !text-sm underline font-medium"
                  onClick={() =>
                    exportRemoveUserValidations(
                      { emailId, exportType },
                      accessToken
                    )
                  }
                >
                  Download
                </EverButton>
                <EverTg.SubHeading4>{subReason}</EverTg.SubHeading4>
              </div>
            ),
          }),
        });
      }
    } catch (error) {
      toast.custom(() => (
        <EverHotToastMessage
          type="error"
          description="Error Occured, try again later!"
        />
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveUser = () => {
    handleRemoveUserInternal();
  };

  return (
    <EverButton
      type="text"
      color="error"
      size="small"
      onClick={handleRemoveUser}
      prependIcon={<Trash03Icon className="text-ever-error" />}
      className="w-full justify-start"
    >
      <EverTg.Text>Delete user</EverTg.Text>
    </EverButton>
  );
};

const AllUsers = observer(
  ({
    refreshEmployees,
    refreshManagers,
    allUsers,
    activeCustomFieldsMap,
    pageCount,
    pageSize,
    totalRows,
    currentPage,
    setPageSize,
    setCurrentPage,
    orderbyFields,
    handleChangeOrderbyFields,
    sortCallback,
    eLoading,
    filterMode,
    searchTerm,
    alluserRoles,
    payoutFreqList,
    payoutFrequencyLabelMap,
    handleChangeFilterMode,
    filters,
    setFilters,
    appliedFilters,
    setAppliedFilters,
  }) => {
    const { accessToken, email, isLoggedInAsUser } = useAuthStore();
    const { isAllowedToImpersonate, hasPermissions } = useUserPermissionStore();
    const myClient = useRecoilValue(myClientAtom);
    const clientFeatures = getClientFeatures(myClient);
    const [selectedUser, setSelectedUser] = useState(null);
    const hasManageUsers = hasPermissions(RBAC_ROLES.MANAGE_USERS);

    const { t } = useTranslation();

    const {
      cumulativeSet,
      headerCbxState,
      setHeaderCbxState,
      setSelectedEmailSet,
      clearSelection,
      maxSelectionCount,
    } = useCheckboxSelection({
      data: allUsers || [],
      currentPage,
      pageSize,
      key: "employeeEmailId",
      maxSelectionCount: 100,
    });

    const {
      openModal,
      setOpenModal,
      imageLoading,
      setImageLoading,
      prompt,
      setPrompt,
      generatedImageBase64,
      setGeneratedImageBase64,
      profileImageBase64,
      setProfileImageBase64,
      generatedImageUrl,
      setGeneratedImageUrl,
      imageStyle,
      setImageStyle,
      onRemove,
      saveProfilePicture,
      sendPrompt,
      email: profileEmail,
      setEmail,
    } = useProfileHook();

    const authEmail = email || null;

    const { primary } = useCurrentTheme();

    let agColumns = useMemo(() => {
      const sendInvite = (emailId) => {
        const toastId = toast.custom(() => (
          <EverHotToastMessage type="loading" description="Sending Invite" />
        ));
        sendAuthInvite({ emailIds: [emailId] }, accessToken).then(
          (response) => {
            toast.remove(toastId);
            if (response.ok) {
              response.json().then((res) => {
                if (res.failedEmails.length <= 0) {
                  toast.custom(() => (
                    <EverHotToastMessage
                      type="success"
                      description="Invite sent Successfully"
                    />
                  ));
                } else {
                  toast.custom(() => (
                    <EverHotToastMessage
                      type="error"
                      description="Invitation failed"
                    />
                  ));
                }
              });
              refreshEmployees();
            } else {
              toast.custom(() => (
                <EverHotToastMessage
                  type="error"
                  description="Send invitation failed"
                />
              ));
            }
          }
        );
      };

      const handleLoginAs = async (params) => {
        toast.custom(() => (
          <EverHotToastMessage
            type="loading"
            description={`Logging in as ${params.firstName} ${params.lastName}`}
          />
        ));
        const data = {
          email: params?.employeeEmailId,
          name: params.firstName + " " + params.lastName,
          loginClientId: myClient.clientId,
        };
        sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.LOGIN_AS_USER, {
          [ANALYTICS_PROPERTIES.USER_NAME]:
            params.firstName + " " + params.lastName,
          [ANALYTICS_PROPERTIES.USER_ROLE]: params.userRole,
        });
        let res = await loginAsUser(data, accessToken);
        if (res.ok) {
          window.location.href = "/";
        } else {
          res = await res.json();
          const errorMessage = res?.error
            ? res.error
            : `Something went wrong, please try again later`;
          toast.custom(() => (
            <EverHotToastMessage type="error" description={errorMessage} />
          ));
        }
      };

      const revertUserExit = (emailId) => {
        const data = {
          emailId: emailId,
        };
        initiateRevertExit(data, accessToken)
          .then((response) => {
            if (response.ok) {
              refreshEmployees();
              toast.custom(() => (
                <EverHotToastMessage
                  type="success"
                  description="Exit Reverted Successful!"
                />
              ));
            }
          })
          .catch((error) => {
            console.error("Error occurred:", error);
          });
      };

      const removeUser = async (emailId, removeUserFlag = false) => {
        const data = {
          emailId: emailId,
          remove_user: removeUserFlag,
        };

        try {
          const response = await validateAndRemoveUser(data, accessToken);
          if (!response.ok) {
            throw new Error("Error occurred while removing user");
          }

          const responseData = await response.json();

          return responseData;
        } catch (error) {
          console.error("Error occurred while removing user:", error);
          toast.custom(() => (
            <EverHotToastMessage
              type="error"
              description="Error occurred while removing the user."
            />
          ));
          return {
            status: "ERROR",
            reason: "An error occurred while removing the user.",
          };
        }
      };

      const getMenu = (record) => {
        const isExitDateNotNull = !record?.exitDate;
        const isCurrentUser = record.employeeEmailId === authEmail;
        const showRemoveUserButton =
          hasPermissions(RBAC_ROLES.DELETE_USERS) && !isCurrentUser;
        const showImpersonateButton =
          !isLoggedInAsUser &&
          !isCurrentUser &&
          (isEmpty(record?.exitDate) ||
            moment.utc().isSameOrBefore(record?.exitDate)) &&
          (isEmpty(record?.deactivationDate) ||
            moment.utc().isSameOrBefore(record?.deactivationDate)) &&
          isAllowedToImpersonate(record.userRolePermissionID) &&
          hasPermissions(RBAC_ROLES.ALLOW_IMPERSONATION);
        const showExitButton = isExitDateNotNull && !isCurrentUser;
        const showRevertExitButton =
          hasPermissions(RBAC_ROLES.MANAGE_USERS) &&
          !showExitButton &&
          !isCurrentUser;
        const showRemoveActions =
          showRemoveUserButton || showExitButton || showRevertExitButton;
        const isLoggedInUserEmail = record.employeeEmailId === authEmail;

        const impersonateButton = (
          <Menu.Item key="loginAs" className="!px-0">
            <EverButton
              type="text"
              color="base"
              size="small"
              onClick={() => handleLoginAs(record)}
              prependIcon={<LogInIcon className="text-ever-base-content-mid" />}
              className="w-full justify-start"
            >
              <Text>Login as user</Text>
            </EverButton>
          </Menu.Item>
        );

        const menuWithoutManageUsers = showImpersonateButton && (
          <Menu.ItemGroup title="Access">{impersonateButton}</Menu.ItemGroup>
        );

        const menuWithManageUsers = (
          <>
            <Menu.ItemGroup title="Profile">
              <Menu.Item key="edit" className="!px-0">
                <AddUserButton
                  isLoggedInUserEmail={isLoggedInUserEmail}
                  onUserAdded={refreshEmployees}
                  buttonType="text"
                  buttonText="Edit"
                  savedUserDetails={record}
                  segmentEntryPoint="Users"
                  alluserRoles={alluserRoles}
                  buttonIcon={
                    <EditIcon className="text-ever-base-content-mid" />
                  }
                  buttonColor="base"
                  buttonClassName="w-full justify-start"
                />
              </Menu.Item>
              {["ALL", "ADMIN"].includes(
                clientFeatures?.profilePicturePermission
              ) ? (
                <Menu.Item key="propic" className="!px-0 w-max">
                  <EverButton
                    type="text"
                    color="base"
                    size="small"
                    prependIcon={
                      <UserCircleIcon className="text-ever-base-content-mid" />
                    }
                    onClick={() => {
                      setEmail(record.employeeEmailId);
                      setSelectedUser({
                        firstName: record.firstName,
                        lastName: record.lastName,
                        image: record.profilePicture,
                      });
                      setOpenModal(PROFILE_MODALS.GENERATE);
                    }}
                  >
                    <Text>Change Profile Picture</Text>
                  </EverButton>
                </Menu.Item>
              ) : null}
              <Menu.Item key="map" className="!px-0">
                <MapPayeeButton
                  emailId={record.employeeEmailId}
                  onUserAdded={() => {
                    refreshEmployees();
                    refreshManagers();
                  }}
                  label="Map Payee"
                  segmentEntryPoint="Users"
                  alluserRoles={alluserRoles}
                  payoutFreqList={payoutFreqList}
                  icon={
                    <MarkerPinIcon className="text-ever-base-content-mid" />
                  }
                  iconSize="small"
                  buttonColor="base"
                  mode="icon-and-label"
                  buttonClassName="w-full justify-start"
                  buttonType="text"
                />
              </Menu.Item>
            </Menu.ItemGroup>
            {(record.status !== "Inactive" || showImpersonateButton) && (
              <>
                <Divider className="my-1" />
                <Menu.ItemGroup title="Access">
                  {record.status !== "Inactive" && (
                    <Menu.Item key="invite" className="!px-0">
                      <SendInviteModal
                        record={record}
                        sendInvite={sendInvite}
                      />
                    </Menu.Item>
                  )}
                  {showImpersonateButton && impersonateButton}
                </Menu.ItemGroup>
              </>
            )}
            {showRemoveActions && (
              <Menu.ItemGroup>
                <Divider className="my-1" />
                {showRemoveUserButton ? (
                  <Menu.Item key="removeUser" className="!px-0">
                    <RemoveUserModal
                      record={record}
                      removeUser={removeUser}
                      accessToken={accessToken}
                      refreshManagers={refreshManagers}
                      handleChangeFilterMode={handleChangeFilterMode}
                      filters={filters}
                      setFilters={setFilters}
                      appliedFilters={appliedFilters}
                      setAppliedFilters={setAppliedFilters}
                    />
                  </Menu.Item>
                ) : null}
                {showExitButton ? (
                  <Menu.Item key="exit" className="!px-0">
                    <EmployeeExitButton
                      emailId={record.employeeEmailId}
                      payoutFrequencyLabelMap={payoutFrequencyLabelMap}
                      onSaveExitDate={refreshEmployees}
                      label="Initiate Exit"
                      buttonIcon={<LogOutHalfIcon />}
                      component="dropdown"
                      buttonClassName="w-full justify-start"
                    />
                  </Menu.Item>
                ) : null}
                {showRevertExitButton ? (
                  <Menu.Item key="revertExit" className="!px-0">
                    <RevertExitModal
                      record={record}
                      revertUserExit={revertUserExit}
                    />
                  </Menu.Item>
                ) : null}
              </Menu.ItemGroup>
            )}
          </>
        );

        // returns null when there are no menu items possible.
        if (!hasManageUsers && !menuWithoutManageUsers) {
          return null;
        }

        return (
          <Menu className="w-max">
            {hasManageUsers ? menuWithManageUsers : menuWithoutManageUsers}
          </Menu>
        );
      };

      const onToggleCheckbox = ({ e, data }) => {
        if (e.target.checked) {
          setSelectedEmailSet(
            (previousState) =>
              new Set([...previousState, data?.employeeEmailId])
          );
        } else {
          setSelectedEmailSet(
            (previousState) =>
              new Set(
                [...previousState].filter((x) => x !== data?.employeeEmailId)
              )
          );
        }
        setHeaderCbxState(HEADER_STATE.PARTIAL);
      };

      const onToggleHeaderCbx = (e) => {
        e.stopPropagation();
        e.preventDefault();
        if (headerCbxState === HEADER_STATE.NONE) {
          setHeaderCbxState(HEADER_STATE.ALL);
        } else {
          setHeaderCbxState(HEADER_STATE.NONE);
        }
      };

      let columnDefs = [];

      // If the user don't have manage:users permisssion, no need to
      // show the checkbox column as there are no actions available for the
      // selected users.
      if (hasManageUsers) {
        columnDefs.push({
          field: "checkbox",
          colId: "checkbox",
          headerName: "",
          width: 68,
          sortable: false,
          suppressColumnsToolPanel: true,
          suppressSizeToFit: true,
          pinned: "left",
          lockPosition: "left",
          // cellStyle: { paddingLeft: "0px", paddingRight: "0px" },
          suppressMenu: true,
          cellRenderer: CheckboxRenderer,
          cellRendererParams: {
            onToggleCheckbox: onToggleCheckbox,
          },
          headerComponent: GroupHeaderCheckbox,
          headerComponentParams: {
            value: headerCbxState,
            onToggleHeaderCbx: onToggleHeaderCbx,
          },
        });
      }

      columnDefs = [
        ...columnDefs,
        {
          suppressMovable: true,
          suppressMenu: true,
          pinned: "left",
          colId: "fullName",
          field: "fullName",
          headerName: "Name",
          suppressColumnsToolPanel: true,
          comparator: comparator,
          cellRenderer: AvatarWithEmailRenderer,
          cellStyle: { paddingLeft: "5px", paddingRight: 0 },
          minWidth: 240,
          maxWidth: 480,
          cellRendererParams: (params) => {
            const isUserUnmapped =
              params.data.mappingStatus === MAPPING_STATUS.UNMAPPED;
            return {
              src: params?.data?.profilePicture,
              email: params?.data?.employeeEmailId,
              value: params?.data?.fullName,
              nameColor: primary.DEFAULT,
              isUnmapped: params?.data?.mappingStatus === "Unmapped",
              name: (
                <MapPayeeButton
                  emailId={params.data.employeeEmailId}
                  onUserAdded={() => {
                    refreshEmployees();
                    refreshManagers();
                  }}
                  label={params?.data?.fullName}
                  infoTooltipText={
                    isUserUnmapped
                      ? `${t("SETUP_COMMISSION_PLAN")} - ${
                          params?.data?.fullName
                        }`
                      : ""
                  }
                  segmentEntryPoint="Users"
                  alluserRoles={alluserRoles}
                  payoutFreqList={payoutFreqList}
                  mode="only-label"
                  buttonClassName="!h-max !p-0 w-full [&>div]:!text-base [&>div]:!inline-block [&>div]:truncate !justify-start"
                  buttonColor="base"
                  isViewOnly={!hasManageUsers}
                />
              ),
              titleClassName: "min-w-0",
            };
          },
        },
        {
          suppressMovable: true,
          suppressMenu: true,
          pinned: "left",
          headerName: "Status",
          field: "status",
          colId: "status",
          width: 180,
          resizable: false,
          suppressColumnsToolPanel: true,
          suppressSizeToFit: true,
          cellStyle: { textAlign: "center" },
          comparator: comparator,
          cellRenderer: (params) => {
            const { status, joiningDate, deactivationDate, exitDate } =
              params.data;
            const currentStatus = params.value;
            return (
              <UserStatusIndicator
                userStatus={status}
                joiningDate={joiningDate}
                exitDate={exitDate}
                deactivationDate={deactivationDate}
                currentStatus={currentStatus}
              />
            );
          },
        },
        {
          flex: 1,
          minWidth: 200,
          headerName: "Role",
          field: "userRoleName",
          colId: "userRoleName",
          valueFormatter: (params) => {
            return params.value;
          },
          comparator: comparator,
        },
        {
          headerName: "User Source",
          field: "userSource",
          colId: "userSource",
          initialHide: true,
          valueFormatter: (params) => {
            return params.value === null || params.value === "manual"
              ? "Manually managed"
              : "Managed via integrations";
          },
          comparator: comparator,
        },
        {
          headerName: t("PRIMARY_COMMISSION_PLAN"),
          field: "employeePlanDetails",
          colId: "employeePlanDetails",
          comparator: comparator,
          // For exporting the correct value we use value getter even though we have a cell renderer
          valueGetter: (params) => {
            if (params.data.employeePlanDetails.multiplePlans) {
              let primaryPlanNamesArray =
                params.data.employeePlanDetails.multiplePlans.map(
                  (item) => item.planName
                );
              return primaryPlanNamesArray.join(", ");
            }
            return params.data.employeePlanDetails.displayText;
          },
          cellRenderer: (params) => {
            return <PlanRenderer value={params.data.employeePlanDetails} />;
          },
        },
        {
          headerName: "SPIFF Plans",
          field: "employeeSpiffPlanDetails",
          colId: "employeeSpiffPlanDetails",
          // For exporting the correct value we use value getter even though we have a cell renderer
          valueGetter: (params) => {
            if (params.data.employeeSpiffPlanDetails.multiplePlans) {
              let spiffPlanNamesArray =
                params.data.employeeSpiffPlanDetails.multiplePlans.map(
                  (item) => item.planName
                );
              return spiffPlanNamesArray.join(", ");
            }
            return params.data.employeeSpiffPlanDetails.displayText;
          },
          cellRenderer: (params) => {
            return (
              <PlanRenderer value={params.data.employeeSpiffPlanDetails} />
            );
          },
          comparator: comparator,
        },
        {
          headerName: "Employee ID",
          field: "employeeId",
          colId: "employeeId",
          valueGetter: (params) => {
            return params?.data?.employeeId || "-";
          },
          initialHide: true,
          comparator: comparator,
        },
        {
          headerName: "Designation",
          field: "designation",
          colId: "designation",
          valueGetter: (params) => {
            return params?.data?.designation || "-";
          },
          initialHide: true,
          comparator: comparator,
        },
        {
          headerName: "Reporting Manager",
          field: "reportingManagerFullName",
          colId: "reportingManagerFullName",
          valueGetter: (params) => {
            return params?.data?.reportingManagerFullName || "-";
          },
          initialHide: true,
          comparator: comparator,
        },
        {
          headerName: "Joining Date",
          field: "joiningDate",
          colId: "joiningDate",
          valueGetter: (params) => {
            return formatDate({ date: params?.data?.joiningDate }) || "-";
          },
          comparator: dateComparator,
          initialHide: true,
        },
        {
          headerName: "Employment Country",
          field: "employmentCountry",
          colId: "employmentCountry",
          valueGetter: (params) => {
            return params?.data?.employmentCountry || "-";
          },
          initialHide: true,
          comparator: comparator,
        },
        {
          headerName: t("PAYOUT_FREQUENCY"),
          field: "payoutFrequency",
          colId: "payoutFrequency",
          cellRenderer: (params) => {
            return params.value || "-";
          },
          initialHide: true,
          comparator: comparator,
        },
        {
          headerName: t("PAYOUT_CURRENCY"),
          field: "payCurrency",
          colId: "payCurrency",
          valueGetter: (params) => {
            return params?.data?.payCurrency || "-";
          },
          initialHide: true,
          comparator: comparator,
        },
        {
          headerName: "Base Pay",
          field: "fixedPay",
          colId: "fixedPay",
          valueGetter: (params) => {
            return params?.data?.fixedPay || "-";
          },
          valueFormatter: (params) => {
            if (params.value === "-") {
              return "-";
            }
            return formatCurrency(params.value);
          },
          initialHide: true,
          comparator: numComparator,
          type: "rightAligned",
        },
        {
          headerName: "Variable Pay",
          field: "variablePay",
          colId: "variablePay",
          valueGetter: (params) => {
            return params?.data?.variablePay || "-";
          },
          valueFormatter: (params) => {
            if (params.value === "-") {
              return "-";
            }
            return formatCurrency(params.value);
          },
          initialHide: true,
          comparator: numComparator,
          type: "rightAligned",
        },
        {
          headerName: "Exit Date",
          field: "exitDate",
          colId: "exitDate",
          valueGetter: (params) => {
            return formatDate({ date: params?.data?.exitDate }) || "-";
          },
          initialHide: true,
          comparator: dateComparator,
        },
        {
          headerName: "Added Date",
          field: "createdDate",
          colId: "createdDate",
          valueGetter: (params) => {
            return formatDate({ date: params?.data?.createdDate }) || "-";
          },
          comparator: dateComparator,
          flex: 1,
          minWidth: 200,
        },
      ];

      // Render actions only when the user has manage:users or
      // allow:impersonation permission.
      if (
        hasPermissions([
          RBAC_ROLES.MANAGE_USERS,
          RBAC_ROLES.ALLOW_IMPERSONATION,
        ])
      ) {
        columnDefs.push({
          suppressColumnsToolPanel: true,
          headerName: "",
          colId: "actions",
          minWidth: 190,
          width: 190,
          maxWidth: 190,
          pinned: "right",
          suppressSizeToFit: true,
          cellStyle: () => {
            return { padding: 0, display: "flex", justifyContent: "center" };
          },
          resizable: false,
          sortable: false,
          suppressMenu: true,
          suppressMovable: true,
          lockPosition: true,
          cellRenderer: (params) => {
            const isSupportUser = params.data.canUserManageAdmins;
            const isLoggedInUserEmail =
              params.data.employeeEmailId === authEmail;
            const isManageOwnData = hasPermissions(RBAC_ROLES.MANAGE_OWNDATA);
            const showColumn =
              !isSupportUser && (!isLoggedInUserEmail || isManageOwnData);
            const isUserUnmapped =
              params.data.mappingStatus === MAPPING_STATUS.UNMAPPED;

            // menu will be null when there are no possible menu items possible.
            // For example, if the user don't have manage:users and
            // allow:impersonation permissions, there won't be any options to
            // show in dropdown.
            const menu = getMenu(params.data);

            if (!showColumn) {
              return <></>;
            }

            return (
              <div className={`flex h-full items-center`}>
                <div className={isUserUnmapped ? "visible" : "invisible"}>
                  <RBACProtectedComponent
                    permissionId={RBAC_ROLES.MANAGE_USERS}
                  >
                    <MapPayeeButton
                      emailId={params.data.employeeEmailId}
                      onUserAdded={() => {
                        refreshEmployees();
                        refreshManagers();
                      }}
                      label="Map Payee"
                      infoTooltipText={t("SETUP_COMMISSION_PLAN")}
                      segmentEntryPoint="Users"
                      alluserRoles={alluserRoles}
                      payoutFreqList={payoutFreqList}
                      mode="only-label"
                    />
                  </RBACProtectedComponent>
                </div>

                {!isNull(menu) && (
                  <Dropdown
                    trigger={["click"]}
                    overlay={menu}
                    placement="bottomRight"
                  >
                    <EverButton.Icon
                      size="small"
                      type="ghost"
                      color="base"
                      className="bg-transparent"
                      data-testid={`${params?.data?.employeeEmailId} users dd button`}
                      icon={
                        <DotsHorizontalIcon className="text-ever-base-content-mid" />
                      }
                    />
                  </Dropdown>
                )}
              </div>
            );
          },
        });
      }

      // CUSTOM_FIELDS
      Object.values(activeCustomFieldsMap).map((customField) => {
        const systemName = camelCase(customField.systemName);
        const fieldType = customField.fieldType;
        columnDefs.push({
          field: systemName,
          colId: systemName,
          headerName: customField.displayName,
          comparator:
            fieldType === "Date"
              ? dateComparator
              : fieldType === "Number"
              ? numComparator
              : comparator,
          valueGetter: (params) => {
            if (!isNil(params.data?.[systemName])) {
              const fieldValue = params.data[systemName];
              if (fieldType === "Checkbox") {
                if (fieldValue === true) {
                  return "True";
                } else if (fieldValue === false) {
                  return "False";
                } else {
                  return "-";
                }
              }
              return fieldValue ?? "-";
            } else {
              return "-";
            }
          },
          initialHide: true,
        });
      });

      const sortableColumns = columnDefs.map((col) => {
        const isSortable = col.sortable ?? true;
        const sortInfo = orderbyFields.find(
          (item) => item.column === col.field
        );
        const headerParams = col.headerComponentParams ?? {};
        return {
          ...col,
          headerComponentParams: {
            ...headerParams,
            serverSideSortable: isSortable,
            sortOrder: sortInfo?.order ?? "",
            sortByField: col.field,
            sortCallback: isEmpty(allUsers) ? () => {} : sortCallback,
          },
        };
      });

      return sortableColumns;
    }, [
      accessToken,
      orderbyFields,
      authEmail,
      refreshEmployees,
      activeCustomFieldsMap,
      headerCbxState,
    ]);

    if (!hasPermissions(RBAC_ROLES.VIEW_PAYROLL)) {
      agColumns = agColumns.filter(
        (item) => item.field !== "variablePay" && item.field !== "fixedPay"
      );
    }

    return (
      <>
        <MapPayeeButton
          onUserAdded={() => {
            refreshEmployees();
            refreshManagers();
          }}
          label="Map Payee"
          segmentEntryPoint="Users"
          alluserRoles={alluserRoles}
          payoutFreqList={payoutFreqList}
          icon={<MarkerPinIcon className="text-ever-base-content-mid" />}
          iconSize="small"
          buttonColor="base"
          mode="icon-and-label"
          buttonClassName="hidden"
          buttonType="text"
        />
        <UsersTable
          columnDefs={agColumns}
          rowData={allUsers}
          eLoading={eLoading}
          pageCount={pageCount}
          pageSize={pageSize}
          totalRows={totalRows}
          currentPage={currentPage}
          setPageSize={setPageSize}
          setCurrentPage={setCurrentPage}
          headerCbxState={headerCbxState}
          setHeaderCbxState={setHeaderCbxState}
          selectedEmailSet={cumulativeSet}
          refreshEmployees={refreshEmployees}
          accessToken={accessToken}
          filterMode={filterMode}
          searchTerm={searchTerm}
          clearSelection={clearSelection}
          maxSelectionCount={maxSelectionCount}
          orderbyFields={orderbyFields}
          handleChangeOrderbyFields={handleChangeOrderbyFields}
        />
        <ProfileModal
          avatarDetails={selectedUser}
          openModal={openModal}
          setOpenModal={setOpenModal}
          imageStyle={imageStyle}
          setImageStyle={setImageStyle}
          prompt={prompt}
          setPrompt={setPrompt}
          generatedImageBase64={generatedImageBase64}
          setGeneratedImageBase64={setGeneratedImageBase64}
          generatedImageUrl={generatedImageUrl}
          setGeneratedImageUrl={setGeneratedImageUrl}
          imageLoading={imageLoading}
          setImageLoading={setImageLoading}
          accessToken={accessToken}
          email={profileEmail}
          sendPrompt={sendPrompt}
          saveProfilePicture={saveProfilePicture}
          onRemove={onRemove}
          profileImageBase64={profileImageBase64}
          setProfileImageBase64={setProfileImageBase64}
        />
      </>
    );
  }
);

AllUsers.propTypes = {
  refreshEmployees: PropTypes.func.isRequired,
  allUsers: PropTypes.array.isRequired,
};

export default AllUsers;
