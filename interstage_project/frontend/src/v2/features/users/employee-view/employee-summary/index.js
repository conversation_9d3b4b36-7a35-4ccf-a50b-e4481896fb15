import { FilterFunnelIcon } from "@everstage/evericons/duotone";
import { ChevronDownIcon, PlusCircleIcon } from "@everstage/evericons/outlined";
import { CircleIcon } from "@everstage/evericons/solid";
import { Dropdown, Menu, Popover, Space } from "antd";
import { chain, cloneDeep, debounce, findIndex, get, isEmpty } from "lodash";
import { observer, useLocalStore } from "mobx-react";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { useRecoilValue, useRecoilState } from "recoil";
import { twMerge } from "tailwind-merge";

import { sendAnalyticsEvent } from "~/Api/AnalyticsService";
import { longPoll } from "~/Api/asyncTask";
import { exportUsers } from "~/Api/EmployeeService";
import {
  CSV_EXPORT_TYPE,
  MAPPING_STATUS,
  RBAC_ROLES,
  SEARCH_BOX,
  ANALYTICS_EVENTS,
  ANALYTICS_PROPERTIES,
  PLATFORM,
} from "~/Enums";
import { navPortalAtom, everContentRightOpenAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  EverButton,
  EverChip,
  EverHotToastMessage,
  EverInput,
  EverInteractiveChip,
  EverLoader,
  EverNavPortal,
  EverRadio,
  EverTg,
  EverTooltip,
  toast,
  useOnClickOutside,
  EverHorizontalScroller,
} from "~/v2/components";
import RBACProtectedComponent from "~/v2/components/rbac-wrapper/RBACProtectedComponent";
import { formatFilterDisplay } from "~/v2/compound-components/DynamicFields";

import AllUsers from "./AllUsers";
import ExtrasImport from "./ExtrasImport";
import {
  CUSTOM_FIELDS_SECTION,
  EMPLOYEE_CUSTOM_FIELD_COLS,
  EMPLOYEE_EMAIL_COL,
  getUserAdditionalFilterFields,
  getUserFilterDefaultFields,
  USER_FILTERS,
} from "./helper";
import EmployeeSummaryStore from "./store";
import UserFieldSelector from "./UserFieldSelector";
import UserFilterDrawerNew from "./UserFilterDrawerNew";
import {
  BULK_MODE_DELETE,
  BULK_MODE_NEW,
  BULK_MODE_UPDATE,
  EverBulkUpload,
  OVERWRITE_EDIT,
} from "../../ever-bulk-upload";
import EverMapFields from "../../ever-bulk-upload/EverMapFields";
import EverUploader from "../../ever-bulk-upload/EverUploader";
import EverValidator from "../../ever-bulk-upload/EverValidator";
import AddUserButton from "../add-user-button";

const quickFiltersData = [
  {
    displayName: "Unmapped Users",
    filterBy: "mappingStatus",
    value: MAPPING_STATUS.UNMAPPED,
    showIndicator: true,
    indicatorClass: "text-ever-error",
  },
  {
    displayName: "Mapped Users",
    filterBy: "mappingStatus",
    value: MAPPING_STATUS.MAPPED,
    icon: null,
    showIndicator: true,
    indicatorClass: "text-ever-success",
  },
  {
    displayName: "Added / Not Invited Users",
    filterBy: "status",
    value: "Added",
    icon: null,
    showIndicator: true,
    indicatorClass: "text-ever-chartColors-31",
  },
  {
    displayName: "Inactive / Exited Users",
    filterBy: "status",
    value: "Inactive",
    icon: null,
    showIndicator: true,
    indicatorClass: "text-ever-error",
  },
];

const MAX_ALLOWED_ROWS = 2000;

const getFilterNameTranslatedMap = (t) => {
  const SYSTEM_NAME_TO_DISPLAY_NAME_FITLER_FIELDS = {};
  const DEFAULT_FIELDS = getUserFilterDefaultFields(t);
  const ADDITIONAL_FIELDS = getUserAdditionalFilterFields();
  for (const field of [...DEFAULT_FIELDS, ...ADDITIONAL_FIELDS]) {
    SYSTEM_NAME_TO_DISPLAY_NAME_FITLER_FIELDS[field.systemName] =
      field.displayName;
  }
  return SYSTEM_NAME_TO_DISPLAY_NAME_FITLER_FIELDS;
};

const EmployeeSummary = observer((props) => {
  const {
    refreshEmployees,
    pageCount,
    pageSize,
    totalRows,
    currentPage,
    setPageSize,
    setCurrentPage,
    orderbyFields,
    handleChangeOrderbyFields,
    sortCallback,
    searchTerm,
    setSearchTerm,
    refreshManagers,
    eLoading,
    handleChangeFilterMode,
    resetPageNumber,
    quickFilterCount,
    filterMode,
    qFilterLoading,
    alluserRoles,
    userRolesToDisplayNameMap,
    displayNameToUserRoleMap,
    payoutFreqList,
    payoutFrequencyLabelMap,
    payoutFrequencyValueMap,
    activeCustomFieldsForImportUserMap,
  } = props;
  const employeeSummaryStore = useLocalStore(
    (observableProps) => new EmployeeSummaryStore(observableProps),
    props
  );
  const navPortalLocation = useRecoilValue(navPortalAtom);
  const {
    allUsers,
    managers,
    countries,
    filters,
    setFilters,
    isFilterApplied,
    filtersInit,
    quickFilters,
    setQuickFilters,
    managerMap,
    isQuickFilterApplied,
    activeCustomFieldsMap,
    numberOfFiltersApplied,
    bulkImportAllColumnDetails,
    setIsPayColumnsAvailable,
    isSelectedFieldsValid,
    validateBulkUploadUsers,
    importBulkUploadUsers,
    bulkImportActiveTasks,
    setBulkImportActiveTasks,
    bulkDeleteActiveTasks,
    setBulkDeleteActiveTasks,
    setIsCustomFieldsAvailable,
    validateRemoveUserBulkUpload,
    importRemoveUserBulkUpload,
  } = employeeSummaryStore;

  const location = useLocation();

  const { accessToken } = useAuthStore();

  const { hasPermissions } = useUserPermissionStore();

  const { t } = useTranslation();

  // to get search param from URl and set it in the search box
  const [localSearch, setLocalSearch] = useState(() => {
    const queryParams = new URLSearchParams(window.location.search);
    const userSearchParam = queryParams.get("search");
    return userSearchParam || "";
  });
  const [appliedFilters, setAppliedFilters] = useState(filtersInit);
  const [bulkUploadNewVisible, setBulkUploadNewVisible] = useState(false);
  const [bulkUploadUpdateVisible, setBulkUploadUpdateVisible] = useState(false);
  const [bulkUploadRemoveVisible, setBulkUploadRemoveVisible] = useState(false);
  const [exportDropdownOpen, setExportDropdownOpen] = useState(false);

  const [openPopover, setOpenPopover] = useState(false);
  const [exportActive, setExportActive] = useState(false);

  const [plansToDisplayNameMap, setPlansToDisplayNameMap] = useState({});

  const longPollResult = useRef(null);
  const dropdownRef = useRef(null);

  const [rightContentOpen, setRightContentOpen] = useRecoilState(
    everContentRightOpenAtom
  );

  useEffect(() => {
    // Close the drawer when the component is unmounted
    return closeUserFilterDrawerNew;
  }, []);

  const openUserFilterDrawerNew = () => {
    if (isQuickFilterApplied) {
      // Only either quick filters or drawer filters should be applied at a time
      // reset quick filters and display all users
      handleChangeFilterMode({
        value: true,
        constraints: { ...filtersInit },
      });
      setQuickFilters([]);
      setFilters({ ...filtersInit });
      setAppliedFilters({ ...filtersInit });
    }
    setRightContentOpen({ ...rightContentOpen, users: true });
  };

  const closeUserFilterDrawerNew = () => {
    setRightContentOpen({ ...rightContentOpen, users: false });
  };

  //to localize Payout Frequency in Columns
  const payout_freq_index = bulkImportAllColumnDetails.findIndex(
    (x) => x["field"] == "payout_frequency"
  );
  if (payout_freq_index > -1) {
    bulkImportAllColumnDetails[payout_freq_index]["header"] =
      t("PAYOUT_FREQUENCY");
  }

  useEffect(() => {
    if (location?.state?.fromDashboard) {
      //this changes the tags UI to "selected"
      setQuickFilters([
        {
          displayName: "Unmapped Users",
          filterBy: "mappingStatus",
          value: "Unmapped",
          icon: <CircleIcon className="w-1.5 h-1.5 mb-0.5 text-ever-error" />,
        },
      ]);
      handleChangeFilterMode({
        value: true,
        constraints: { ...filters, mappingStatus: "Unmapped" },
      });
    }
  }, []);

  useEffect(() => {
    const successCallback = (data) => {
      const bulkImportTasks = [];
      const bulkDeleteTasks = [];
      for (const task of data.activeTasks) {
        if (task.taskName === "BULK_DELETE_USERS") {
          bulkDeleteTasks.push(task);
        } else {
          bulkImportTasks.push(task);
        }
      }
      setBulkImportActiveTasks(bulkImportTasks);
      setBulkDeleteActiveTasks(bulkDeleteTasks);
    };

    const errorCallback = () => {
      setBulkImportActiveTasks([]);
      setBulkDeleteActiveTasks([]);
    };

    if (!isEmpty(accessToken))
      longPollResult.current = longPoll({
        accessToken,
        requestUrl: "/async-tasks/get-active-user-tasks",
        method: "GET",
        successCallback,
        errorCallback,
        pollInterval: 300000,
      });

    return () => {
      if (!isEmpty(longPollResult) && !isEmpty(longPollResult.current)) {
        longPollResult.current.abort();
      }
    };
  }, [accessToken, setBulkImportActiveTasks, setBulkDeleteActiveTasks]);

  useEffect(() => {
    setIsPayColumnsAvailable(hasPermissions(RBAC_ROLES.EDIT_PAYROLL));
    setIsCustomFieldsAvailable(
      hasPermissions(RBAC_ROLES.MANAGE_USERCUSTOMFIELD)
    );
  }, [setIsPayColumnsAvailable, setIsCustomFieldsAvailable]);

  const debounceFn = useMemo(
    () =>
      debounce((value) => {
        if (value.length > 0) {
          sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.SEARCH_USAGE, {
            [ANALYTICS_PROPERTIES.TEXT_SEARCHED]: value,
          });
        }
        setSearchTerm(value);
        resetPageNumber();
      }, SEARCH_BOX.DEBOUNCE_TIME),
    [setSearchTerm]
  );

  const debounceWrapper = (e) => {
    const searchVal = e.target.value;
    setLocalSearch(searchVal);
    if (
      searchVal.length === 0 ||
      searchVal.length >= SEARCH_BOX.MINIMUM_CHARS
    ) {
      debounceFn(searchVal);
    }
  };

  const ConfirmationNode = () => {
    const [value, setValue] = useState(CSV_EXPORT_TYPE.FILTERED);
    const onChange = (e) => {
      setValue(e.target.value);
    };
    return (
      <Space direction="vertical">
        <EverRadio.Group
          className="exportFilterOption"
          onChange={onChange}
          value={value}
        >
          <Space direction="vertical">
            <EverRadio value={CSV_EXPORT_TYPE.FILTERED}>{`Export ${
              totalRows ?? 0
            } users who match the filter`}</EverRadio>
            <EverRadio value={CSV_EXPORT_TYPE.ALL}>
              {"Export all users"}
            </EverRadio>
          </Space>
        </EverRadio.Group>
        <div className="w-full flex justify-end">
          <EverButton
            size="small"
            type="filled"
            color="primary"
            onClick={() => {
              onExportClick(value);
              setExportDropdownOpen(false);
            }}
          >
            Proceed
          </EverButton>
        </div>
      </Space>
    );
  };

  const onExportClick = (exportType) => {
    // api
    setOpenPopover(false);
    toast.custom(() => (
      <EverHotToastMessage type="loading" description="Exporting users..." />
    ));
    setExportActive(true);

    let requestObj = cloneDeep(filterMode.constraints);
    if (requestObj) {
      requestObj["custom_field"] = {};
      //group custom objects together
      Object.keys(requestObj).forEach((item) => {
        if (item.startsWith("cf_")) {
          requestObj["custom_field"] = {
            ...requestObj["custom_field"],
            [item]: requestObj[item],
          };
          delete requestObj[item];
        }
      });
    }
    sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.USER_EXPORT, {
      [ANALYTICS_PROPERTIES.EXPORT_OPTION]:
        filterMode?.value === true &&
        !isEmpty(allUsers) &&
        exportType === CSV_EXPORT_TYPE.FILTERED
          ? "Filtered"
          : "All",
    });
    exportUsers(
      {
        filters:
          filterMode?.value === true &&
          !isEmpty(allUsers) &&
          exportType === CSV_EXPORT_TYPE.FILTERED
            ? { ...requestObj }
            : null,
        viewPayroll: hasPermissions(RBAC_ROLES.VIEW_PAYROLL),
      },
      accessToken
    )
      .then((response) => {
        if (response.ok) {
          return response;
        } else {
          setExportActive(false);
          throw new Error("Request failed");
        }
      })
      .then((response) => {
        return response.blob();
      })
      .then((blobby) => {
        let objectUrl = window.URL.createObjectURL(blobby);
        let anchor = document.createElement("a");
        anchor.href = objectUrl;
        const date = new Date().toLocaleString();
        anchor.download = `everstage users ${date} .csv`;
        anchor.click();

        window.URL.revokeObjectURL(objectUrl);
        toast.remove();
        toast.custom(() => (
          <EverHotToastMessage
            type="success"
            description="Downloaded Successfully!!"
          />
        ));
        setExportActive(false);
        anchor.remove();
      })
      .catch((error) => {
        toast.remove();
        toast.custom(() => (
          <EverHotToastMessage
            type="error"
            description="Error while exporting users"
          />
        ));
        console.log(error.message);
        setExportActive(false);
      });
  };

  useOnClickOutside(dropdownRef, () =>
    openPopover ? null : setExportDropdownOpen(false)
  );

  const hasManageUsers = hasPermissions(RBAC_ROLES.MANAGE_USERS);
  const hasExportUsers = hasPermissions(RBAC_ROLES.EXPORT_USERS);
  const hasDeleteUsers = hasPermissions(RBAC_ROLES.DELETE_USERS);

  const navOperations = (
    <div className="flex items-center gap-3 justify-end">
      <EverInput.Search
        placeholder="Search by name or email"
        className="w-64"
        onChange={(e) => debounceWrapper(e)}
        value={localSearch}
        size="small"
        allowClear
      />
      <EverButton.Icon
        onClick={() => openUserFilterDrawerNew()}
        icon={
          <FilterFunnelIcon className="size-4 text-ever-base-content-mid" />
        }
        tooltipTitle="Filter users"
        type="ghost"
        size="small"
        color="base"
      />

      {/*
        Show import/export dropdown only when the user has manage:users or
        export:users permissions as these are the only actions in dropdown.
      */}
      <RBACProtectedComponent
        permissionId={[
          RBAC_ROLES.MANAGE_USERS,
          RBAC_ROLES.EXPORT_USERS,
          RBAC_ROLES.DELETE_USERS,
        ]}
      >
        <Dropdown
          className="h-max"
          trigger={["click"]}
          overlay={
            <Menu className="p-1.5">
              <div className="users-menu-div" ref={dropdownRef}>
                {hasManageUsers && (
                  <>
                    <Menu.Item
                      className="!h-max"
                      onClick={() => {
                        setBulkUploadNewVisible(true);
                        setExportDropdownOpen(false);
                      }}
                    >
                      <div className="py-2 my-1 flex flex-col w-full whitespace-pre-line gap-1">
                        <EverTg.SubHeading4 className="text-ever-base-content">
                          Import New Users
                        </EverTg.SubHeading4>
                        <EverTg.Description>
                          Add new users to Everstage in bulk using a CSV file.
                        </EverTg.Description>
                      </div>
                    </Menu.Item>
                    <Menu.Item
                      className="!h-max"
                      onClick={() => {
                        setBulkUploadUpdateVisible(true);
                        setExportDropdownOpen(false);
                      }}
                    >
                      <div className="py-2 my-1 flex flex-col w-full whitespace-pre-line gap-1">
                        <EverTg.SubHeading4 className="text-ever-base-content">
                          Edit Existing Users
                        </EverTg.SubHeading4>
                        <EverTg.Description>
                          Update details of existing users in bulk using a CSV
                          file.
                        </EverTg.Description>
                      </div>
                    </Menu.Item>
                  </>
                )}
                {hasDeleteUsers && (
                  <Menu.Item
                    className="!h-max"
                    onClick={() => {
                      setBulkUploadRemoveVisible(true);
                      setExportDropdownOpen(false);
                    }}
                  >
                    <div className="py-2 my-1 flex flex-col w-full whitespace-pre-line gap-1">
                      <EverTg.SubHeading4 className="text-ever-base-content">
                        Delete Users
                      </EverTg.SubHeading4>
                      <EverTg.Description>
                        Delete users from Everstage using a CSV file.
                      </EverTg.Description>
                    </div>
                  </Menu.Item>
                )}
                {hasExportUsers && (
                  <Menu.Item className="!h-max !px-0">
                    {filterMode?.value === true && !isEmpty(allUsers) ? (
                      <Popover
                        visible={openPopover}
                        onVisibleChange={(visible) => setOpenPopover(visible)}
                        title="Export Users"
                        trigger="click"
                        content={<ConfirmationNode />}
                        placement="left"
                      >
                        <EverButton
                          type="text"
                          color="base"
                          disabled={exportActive ? true : false}
                          className={twMerge(
                            "py-2 my-1 justify-start !h-auto [&>div]:items-start hover:bg-ever-base-100 [&>div]:text-left [&>div]:gap-1 [&>div]:flex-col w-full whitespace-pre-line"
                          )}
                        >
                          <EverTg.SubHeading4 className="text-ever-base-content">
                            Export users
                          </EverTg.SubHeading4>
                          <EverTg.Description className="font-normal">
                            You can export users in a CSV file.
                          </EverTg.Description>
                        </EverButton>
                      </Popover>
                    ) : (
                      <EverButton
                        type="text"
                        color="base"
                        disabled={exportActive ? true : false}
                        onClick={() => {
                          onExportClick(CSV_EXPORT_TYPE.ALL);
                          setExportDropdownOpen(false);
                        }}
                        className={twMerge(
                          "py-2 my-1 !h-auto justify-start [&>div]:items-start hover:bg-ever-base-100 [&>div]:text-left [&>div]:gap-1 [&>div]:flex-col w-full whitespace-pre-line"
                        )}
                      >
                        <EverTg.SubHeading4 className="text-ever-base-content">
                          Export users
                        </EverTg.SubHeading4>
                        <EverTg.Description className="font-normal">
                          You can export users in a CSV file.
                        </EverTg.Description>
                      </EverButton>
                    )}
                  </Menu.Item>
                )}
              </div>
            </Menu>
          }
          overlayStyle={{ width: "300px" }}
          placement="bottomLeft"
          visible={exportDropdownOpen}
        >
          <EverButton
            className="flex justify-between items-center "
            color="primary"
            type="ghost"
            appendIcon={<ChevronDownIcon />}
            size="small"
            onClick={() => setExportDropdownOpen(!exportDropdownOpen)}
          >
            {hasManageUsers && hasExportUsers
              ? "Import / Export"
              : hasManageUsers
              ? "Import"
              : "Export"}
          </EverButton>
        </Dropdown>
      </RBACProtectedComponent>

      <AddUserButton
        onUserAdded={refreshEmployees}
        buttonIcon={<PlusCircleIcon />}
        buttonText={
          <Space className="flex items-center">
            <EverTg.SubHeading4 className="text-ever-base-25">
              New User
            </EverTg.SubHeading4>
          </Space>
        }
        alluserRoles={alluserRoles}
      />
    </div>
  );

  const handleTagsChange = (checked, item) => {
    // Only either quick filters or drawer filters should be applied at a time
    // Applying quick filters hence closing drawer filters
    closeUserFilterDrawerNew();

    let tempQuickFilter = { mappingStatus: null, status: [] };
    const nextSelectedTags = checked ? [item] : []; // no multi select in tags
    if (checked) {
      sendAnalyticsEvent(accessToken, ANALYTICS_EVENTS.QUICK_FILTER, {
        [ANALYTICS_PROPERTIES.QUICK_FILTER_TYPE]:
          nextSelectedTags[0].displayName,
        [ANALYTICS_PROPERTIES.PLATFORM_USED]: PLATFORM.USER,
      });
    }
    setQuickFilters(nextSelectedTags);

    nextSelectedTags.forEach((tags) => {
      if (tags.filterBy === "mappingStatus") {
        tempQuickFilter["mappingStatus"] = tags.value;
      } else {
        tempQuickFilter["status"] = [...tempQuickFilter.status, tags.value];
      }
    });
    if (isEmpty(tempQuickFilter.status)) {
      tempQuickFilter.status = null;
    }
    setFilters({ ...filters, ...tempQuickFilter });

    handleChangeFilterMode({
      value: checked ? true : false,
      constraints: { ...filters, ...tempQuickFilter },
    });
  };

  const onClearAll = () => {
    setAppliedFilters({
      ...filtersInit,
    });
    setFilters({
      ...filtersInit,
    });
    handleChangeFilterMode({
      value: false,
      constraints: {
        ...filtersInit,
      },
    });
  };

  const formatUploadedData = (uploadedData) => {
    const newUploadedData = {
      ...uploadedData,
      values: uploadedData.values.slice(0, MAX_ALLOWED_ROWS),
    };

    return newUploadedData;
  };

  const isCustomFieldEffectiveStartDateRequired = (customFields) => {
    return customFields.some(
      (item) =>
        activeCustomFieldsForImportUserMap?.[item.field]?.isEffectiveDated
    );
  };

  const preProcessSelectorDataUpdates = (selectorData) => {
    //**** CUSTOM FIELDS ****
    /**
     ** If effective dated custom fields are selected and we are not overwriting existing
     ** values, then effective start date field is checked and made mandatory (also it
     ** will be disabled as user shouldn't uncheck it). Otherwise, it is optional.
     */
    const customFieldsMapping = chain(selectorData)
      .get("fields", [])
      .filter((item) => item.section === CUSTOM_FIELDS_SECTION)
      .value();
    const cfEffStartDtField = customFieldsMapping.find(
      (item) =>
        item.field === EMPLOYEE_CUSTOM_FIELD_COLS.EFFECTIVE_START_DATE.field
    );
    let cfEffStartDtFieldRequired = false;
    if (selectorData.overwriteValue !== OVERWRITE_EDIT) {
      cfEffStartDtFieldRequired = isCustomFieldEffectiveStartDateRequired(
        customFieldsMapping.filter((item) => item.isSelected)
      );
    }
    if (cfEffStartDtField) {
      cfEffStartDtField.isRequired = cfEffStartDtFieldRequired;
      if (cfEffStartDtFieldRequired) {
        cfEffStartDtField.isSelected = true;
      }
    }
    //***********************
    return selectorData;
  };

  const preProcessSectionFieldsMappingUpdates = (
    sectionFieldsMapping,
    overwriteValue
  ) => {
    //**** CUSTOM FIELDS ****
    /**
     ** If columns are mapped for atleast one custom field which is effective dated,
     ** and if we are not overwriting then effective start date field should be made
     ** mandatory. Otherwise, it should be optional.
     */
    if (sectionFieldsMapping?.[CUSTOM_FIELDS_SECTION]?.isSelected) {
      const customFieldsMapping = get(
        sectionFieldsMapping,
        [CUSTOM_FIELDS_SECTION, "fields"],
        []
      );
      const cfEffStartDtField = customFieldsMapping.find(
        (item) =>
          item.field === EMPLOYEE_CUSTOM_FIELD_COLS.EFFECTIVE_START_DATE.field
      );
      let cfEffStartDtFieldRequired = false;
      if (overwriteValue !== OVERWRITE_EDIT) {
        cfEffStartDtFieldRequired = isCustomFieldEffectiveStartDateRequired(
          customFieldsMapping.filter((item) => item.chosenHeader)
        );
      }
      if (cfEffStartDtField) {
        cfEffStartDtField.isRequired = cfEffStartDtFieldRequired;
      }
    }
    //***********************
    return sectionFieldsMapping;
  };

  const filterNameTranslatedMap = getFilterNameTranslatedMap(t);
  const filterValueDisplayNameMap = {
    [USER_FILTERS.REPORTING_MANAGER]: Object.keys(managerMap).reduce(
      (acc, key) => {
        const manager = managerMap[key];
        acc[key] = `${manager.firstName} ${manager.lastName}`;
        return acc;
      },
      {}
    ),
    [USER_FILTERS.USER_SOURCE]: {
      manual: "Manually managed",
      hris: "Managed via integrations",
    },
    [USER_FILTERS.ROLE]: userRolesToDisplayNameMap,
    [USER_FILTERS.PAYOUT_FREQUENCY]: payoutFrequencyValueMap,
    [USER_FILTERS.COMMISSION_PLAN]: plansToDisplayNameMap,
  };

  const renderTags = (filterName) => {
    if (
      !isEmpty(filters[filterName]) ||
      typeof filters[filterName] === "boolean"
    ) {
      const filterValue = formatFilterDisplay({
        filterValue: filters[filterName],
        filterName,
        activeCustomFieldsMap,
        filterValueDisplayNameMap,
      });

      return (
        <EverChip
          className="bg-ever-base rounded-full"
          iconClassName="text-ever-base-content-low"
          outlined={true}
          key={filterName}
          size="small"
          closable
          onClose={() => {
            setFilters({ ...filters, [filterName]: null });
            setAppliedFilters({
              ...appliedFilters,
              [filterName]: null,
            });
            if (numberOfFiltersApplied === 1) {
              handleChangeFilterMode({
                value: false,
                constraints: { ...filtersInit },
              });
            } else if (numberOfFiltersApplied > 1) {
              handleChangeFilterMode({
                value: true,
                constraints: { ...filters, [filterName]: null },
              });
            }
          }}
          title={
            <>
              <div key={filterName} className="w-fit">
                <div className="flex items-center gap-1.5 rounded-full">
                  <EverTg.Caption.Medium className="leading-4 text-ever-base-content">
                    {Object.keys(activeCustomFieldsMap).includes(filterName)
                      ? activeCustomFieldsMap[filterName].displayName
                      : filterNameTranslatedMap[filterName]}
                  </EverTg.Caption.Medium>
                  <EverTg.Caption className="leading-4 text-ever-base-content-mid">
                    {filterValue["type"]}
                  </EverTg.Caption>
                  <EverTg.Caption.Medium className="leading-4 text-ever-base-content">
                    {filterValue["value"]}
                  </EverTg.Caption.Medium>
                </div>
              </div>
            </>
          }
        ></EverChip>
      );
    }
    return null;
  };

  return (
    <>
      <EverNavPortal target={navPortalLocation}>
        <div className="@container">
          <div className="justify-between w-full grid grid-cols-1 @7xl:grid-cols-2 items-center max-w-full gap-3 @7xl:gap-8">
            {isFilterApplied ? (
              <div className="h-10 bg-ever-base-200 rounded-lg overflow-hidden flex items-center flex-nowrap pl-3 w-full">
                <EverTg.Caption className="whitespace-nowrap pr-3">
                  Applied Filters
                </EverTg.Caption>
                <EverHorizontalScroller
                  className="flex items-center h-10 gap-2 flex-nowrap"
                  wrapperClassName="h-10 overflow-hidden border-solid grow"
                >
                  {Object.keys(filters).map((filterName) => {
                    return renderTags(filterName);
                  })}
                </EverHorizontalScroller>
                <EverButton size="small" onClick={onClearAll} type="link">
                  Clear all
                </EverButton>
              </div>
            ) : (
              <EverHorizontalScroller
                className="flex items-center h-10 gap-2 flex-nowrap pl-3"
                wrapperClassName="h-10 bg-ever-base-200 rounded-lg overflow-hidden pr-1"
              >
                <EverTg.Caption className="whitespace-nowrap">
                  Quick Filters
                </EverTg.Caption>
                {qFilterLoading ? (
                  <EverLoader.SpinnerLottie className="w-5 h-5" />
                ) : (
                  quickFiltersData.map((item) => (
                    <EverTooltip
                      mouseEnterDelay={0.5}
                      key={item.value}
                      title={
                        item.value === MAPPING_STATUS.UNMAPPED
                          ? t("USERS_WITHOUT_COMMISSION_PLAN")
                          : item.value === MAPPING_STATUS.MAPPED
                          ? t("USERS_HAVE_COMMISSION_PLAN")
                          : item.value === "Added"
                          ? "Users who have not activated their account yet"
                          : "Users who are no longer active"
                      }
                      placement="topLeft"
                    >
                      <div data-testid={`pt-${item.value}`}>
                        <EverInteractiveChip
                          showIndicator={item.showIndicator}
                          isSelected={
                            findIndex(
                              quickFilters,
                              (o) => o.value === item.value
                            ) > -1
                          }
                          size="small"
                          indicatorClass={item.indicatorClass}
                          title={item.displayName}
                          append={
                            <EverTg.SubHeading4 className="text-ever-base-content">
                              {quickFilterCount && quickFilterCount[item.value]}
                            </EverTg.SubHeading4>
                          }
                          prepend={item.icon}
                          onClick={() => {
                            handleTagsChange(
                              findIndex(
                                quickFilters,
                                (o) => o.value === item.value
                              ) > -1
                                ? false
                                : true,
                              item
                            );
                          }}
                        />
                      </div>
                    </EverTooltip>
                  ))
                )}
              </EverHorizontalScroller>
            )}
            {navOperations}
          </div>
        </div>
      </EverNavPortal>
      <UserFilterDrawerNew
        closeDrawer={closeUserFilterDrawerNew}
        allManagers={managers}
        allCountries={countries}
        alluserRoles={alluserRoles}
        payoutFreqList={payoutFreqList}
        allCustomFields={activeCustomFieldsMap}
        setPlansToDisplayNameMap={setPlansToDisplayNameMap}
        filters={filters}
        setFilters={setFilters}
        appliedFilters={appliedFilters}
        setAppliedFilters={setAppliedFilters}
        setQuickFilters={setQuickFilters}
        isQuickFilterApplied={isQuickFilterApplied}
        handleChangeFilterMode={handleChangeFilterMode}
      ></UserFilterDrawerNew>
      {!isEmpty(bulkImportActiveTasks) && (
        <div className="mb-5 flex items-center justify-center">
          <div className="border border-solid border-ever-primary-lite bg-ever-primary-ring p-5 rounded-lg">
            <EverTg.Text>Importing User(s)...</EverTg.Text>
            <EverTg.Text>
              You will be notified via email once the import is complete.
            </EverTg.Text>
          </div>
        </div>
      )}
      {!isEmpty(bulkDeleteActiveTasks) && (
        <div className="mb-5 flex items-center justify-center">
          <div className="border border-solid border-ever-primary-lite bg-ever-primary-ring p-5 rounded-lg">
            <EverTg.Text>Deleting Users...</EverTg.Text>
            <EverTg.Text>
              You will be notified via email once the deletion is complete.
            </EverTg.Text>
          </div>
        </div>
      )}
      <AllUsers
        eLoading={eLoading}
        activeCustomFieldsMap={activeCustomFieldsMap}
        allUsers={allUsers || []}
        refreshEmployees={refreshEmployees}
        refreshManagers={refreshManagers}
        // pagination props
        pageCount={pageCount}
        pageSize={pageSize}
        totalRows={totalRows}
        currentPage={currentPage}
        setPageSize={setPageSize}
        setCurrentPage={setCurrentPage}
        orderbyFields={orderbyFields}
        handleChangeOrderbyFields={handleChangeOrderbyFields}
        sortCallback={sortCallback}
        filterMode={filterMode}
        searchTerm={searchTerm}
        alluserRoles={alluserRoles}
        payoutFreqList={payoutFreqList}
        payoutFrequencyLabelMap={payoutFrequencyLabelMap}
        handleChangeFilterMode={handleChangeFilterMode}
        filters={filters}
        setFilters={setFilters}
        appliedFilters={appliedFilters}
        setAppliedFilters={setAppliedFilters}
      />
      {bulkUploadNewVisible && (
        <EverBulkUpload
          key={BULK_MODE_NEW}
          mode={BULK_MODE_NEW}
          title="Import new users"
          fieldsDef={bulkImportAllColumnDetails}
          onBulkUploadClose={() => setBulkUploadNewVisible(false)}
          displayNameToUserRoleMap={displayNameToUserRoleMap}
          payoutFrequencyLabelMap={payoutFrequencyLabelMap}
          uploaderComponent={{
            getComponent: (commonProps) => {
              return (
                <EverUploader
                  {...commonProps}
                  heading="Import users"
                  descriptions={[
                    "To make it easy for you, we've provided a template with all the required fields to create new users in Everstage.",
                  ]}
                  notes={[
                    "1. Fields marked with an asterisk (*) in the template are mandatory.",
                    "2. Effective date is needed if you're also importing details related to payroll or reporting manger.",
                    "3. You can add a maximum of 2000 users with a single import file.",
                  ]}
                />
              );
            },
            onFormatData: (uploadedData) => {
              return formatUploadedData(uploadedData);
            },
          }}
          mapFieldsComponent={{
            getComponent: (commonProps) => {
              return (
                <EverMapFields
                  {...commonProps}
                  description="Choose the right column from your CSV file against Everstage user fields for a successful import."
                />
              );
            },
            preProcessSectionFieldsMappingUpdates,
          }}
          validationComponent={{
            getComponent: (commonProps, ref) => {
              const renderAfterConfirmation = (
                isLoading,
                handleImportUsers
              ) => (
                <ExtrasImport
                  isLoading={isLoading}
                  handleImportUsers={handleImportUsers}
                />
              );

              return (
                <EverValidator
                  ref={ref}
                  {...commonProps}
                  renderAfterConfirmation={renderAfterConfirmation}
                />
              );
            },
            proceedButtonText: "Import Users",
            onValidation: validateBulkUploadUsers,
            onImport: importBulkUploadUsers,
          }}
        />
      )}
      {bulkUploadUpdateVisible && (
        <EverBulkUpload
          key={BULK_MODE_UPDATE}
          mode={BULK_MODE_UPDATE}
          title="Update existing users"
          payoutFrequencyLabelMap={payoutFrequencyLabelMap}
          fieldsDef={bulkImportAllColumnDetails}
          onBulkUploadClose={() => setBulkUploadUpdateVisible(false)}
          selectorComponent={{
            getComponent: (commonProps) => {
              const openBulkImport = () => {
                setBulkUploadUpdateVisible(false);
                setBulkUploadNewVisible(true);
              };

              return (
                <UserFieldSelector
                  {...commonProps}
                  openBulkImport={openBulkImport}
                />
              );
            },
            preProcessSelectorDataUpdates,
            onProceedValidation: (selectedFields, overwriteValue) => {
              return isSelectedFieldsValid(selectedFields, overwriteValue);
            },
          }}
          uploaderComponent={{
            getComponent: (commonProps) => {
              return (
                <EverUploader
                  {...commonProps}
                  heading="Update users"
                  descriptions={[
                    "To make it easy for you, we've provided a template with all the required fields to update existing users in Everstage.",
                  ]}
                  notes={[
                    "1. Fields marked with an asterisk (*) in the template are mandatory.",
                    "2. Effective date is needed if you're also updating details related to payroll or reporting manger.",
                    "3. You can update a maximum of 2000 users with a single import file.",
                  ]}
                />
              );
            },
            onFormatData: (uploadedData) => {
              return formatUploadedData(uploadedData);
            },
          }}
          mapFieldsComponent={{
            getComponent: (commonProps) => {
              return (
                <EverMapFields
                  {...commonProps}
                  description="Choose the right column from your CSV file against Everstage user fields for a successful import."
                />
              );
            },
            preProcessSectionFieldsMappingUpdates,
          }}
          validationComponent={{
            getComponent: (commonProps, ref) => {
              return <EverValidator ref={ref} {...commonProps} />;
            },
            proceedButtonText: "Update",
            onValidation: validateBulkUploadUsers,
            onImport: importBulkUploadUsers,
          }}
        />
      )}
      {bulkUploadRemoveVisible && (
        <EverBulkUpload
          key={BULK_MODE_DELETE}
          mode={BULK_MODE_DELETE}
          title="Delete users"
          payoutFrequencyLabelMap={payoutFrequencyLabelMap}
          fieldsDef={[EMPLOYEE_EMAIL_COL]}
          onBulkUploadClose={() => setBulkUploadRemoveVisible(false)}
          uploaderComponent={{
            getComponent: (commonProps) => {
              return (
                <EverUploader
                  {...commonProps}
                  heading="Delete users"
                  descriptions={[
                    "To make it easy for you, we've provided a template with all the required fields to delete users from Everstage.",
                  ]}
                  notes={[
                    "1. Fields marked with an asterisk (*) in the template are mandatory.",
                    "2. You can delete a maximum of 2000 users with a single import file.",
                    `3. If the user is on ${t(
                      "COMMISSIONS_LOWERCASE"
                    )}, you will need to remove them from all the ${t(
                      "COMMISSION_LOWERCASE"
                    )} plans and run the ${t(
                      "COMMISSION_LOWERCASE"
                    )} sync to proceed with the deletion.`,
                    "4. If the user is on forecast plans, you will need to remove them from those plans and run the forecast sync to proceed with the deletion.",
                  ]}
                />
              );
            },
            onFormatData: (uploadedData) => {
              return formatUploadedData(uploadedData);
            },
          }}
          mapFieldsComponent={{
            getComponent: (commonProps) => {
              return (
                <EverMapFields
                  {...commonProps}
                  description="Choose the right column from your CSV file against Everstage user fields for a successful import."
                />
              );
            },
            preProcessSectionFieldsMappingUpdates,
          }}
          validationComponent={{
            getComponent: (commonProps, ref) => {
              return <EverValidator ref={ref} {...commonProps} />;
            },
            proceedButtonText: "Proceed",
            onValidation: validateRemoveUserBulkUpload,
            onImport: importRemoveUserBulkUpload,
          }}
        />
      )}
    </>
  );
});

export default EmployeeSummary;
