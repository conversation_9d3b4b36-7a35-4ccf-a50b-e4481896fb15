import {
  EditPencilAltIcon,
  DotsVerticalIcon,
  ScissorsCut02Icon,
  Trash03Icon,
} from "@everstage/evericons/outlined";
import { CircleIcon } from "@everstage/evericons/solid";
import { Dropdown, Menu, Col, Empty, Row, Table } from "antd";
import { sortBy } from "lodash";
import { toJS } from "mobx";
import { observer } from "mobx-react";
import moment from "moment";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import {
  EverButton,
  EverDivider,
  EverGroupAvatar,
  EverTg,
  EverFormatter,
} from "~/v2/components";

import DeleteHistoricalManagers from "./DeleteHistoricalManagers";
import EditHistoricalManagers from "./EditHistoricalManagers";
import { ACTIONS } from "../employee-payroll/history-tabs/constants";

function planDetailsColumns(t, data) {
  const allSettlementEndDatesNull =
    !data || data.every((record) => record.settlementEndDate === null);
  const activePlanData = getActivePlan(data);
  const columns = [
    {
      title: t("COMMISSION_PLAN"),
      dataIndex: "planName",
      key: "planName",
      render: (text, record) => {
        return activePlanData.find((item) => item.planId === record.planId) ? (
          <div className="flex items-center">
            <CircleIcon className="w-2 h-2 text-ever-success mr-2" />
            <span className="truncate" title={record.planName}>
              {record.planName}
            </span>
          </div>
        ) : (
          <span
            className="truncate inline-block w-full"
            title={record.planName}
          >
            {record.planName}
          </span>
        );
      },
    },
    {
      title: "Effective Start Date",
      dataIndex: "effectiveStartDate",
      key: "effectiveStartDate",
      render: (text, record) => {
        if (record.effectiveStartDate) {
          return (
            <EverFormatter.Date
              date={record?.effectiveStartDate}
              className="font-normal"
            />
          );
        }
        return "-";
      },
    },
    {
      title: "Effective End Date",
      dataIndex: "effectiveEndDate",
      key: "effectiveEndDate",
      render: (text, record) => {
        return (
          <EverFormatter.Date
            date={record?.effectiveEndDate}
            className="font-normal"
          />
        );
      },
    },
  ];

  if (!allSettlementEndDatesNull) {
    columns.push({
      title: "Settlement End Date",
      dataIndex: "settlementEndDate",
      key: "settlementEndDate",
      render: (text, record) => {
        return record?.settlementEndDate ? (
          <EverFormatter.Date
            date={record?.settlementEndDate}
            className="font-normal"
          />
        ) : (
          "N.A"
        );
      },
    });
  }

  return columns;
}

const ReportingDetails = observer(({ store, isHRIS }) => {
  const [editFieldData, setEditFieldData] = useState(null);
  const [selectedManagerOption, setSelectedManagerOption] = useState({});
  const [selectedMenu, setSelectedMenu] = useState(null);
  const [visibleEditSplit, setVisibleEditSplit] = useState(false);

  const { employee } = store;

  const { t } = useTranslation();

  const handleClickMenu = (record) => {
    const hierRecord = toJS(record);
    const manager = record?.managerDetails;
    setEditFieldData({
      esd: hierRecord.effectiveStartDate,
      eed: hierRecord?.effectiveEndDate ?? null,
      managerEmailId: hierRecord.reportingManagerEmailId,
      managerName: `${manager.firstName} ${manager.lastName}`,
    });
    setSelectedManagerOption({
      label: `${manager.firstName} ${manager.lastName}`,
      value: hierRecord.reportingManagerEmailId,
    });
  };

  const getMenu = () => {
    return (
      <Menu>
        <Menu.Item
          onClick={() => {
            setSelectedMenu(ACTIONS.SPLIT);
            setVisibleEditSplit(true);
          }}
        >
          <div className="flex items-center gap-3">
            <ScissorsCut02Icon className="w-4 h-4 text-ever-base-content-mid" />
            <EverTg.Text>Split</EverTg.Text>
          </div>
        </Menu.Item>
        <Menu.Item
          onClick={() => {
            setSelectedMenu(ACTIONS.EDIT);
            setVisibleEditSplit(true);
          }}
        >
          <div className="flex items-center gap-3">
            <EditPencilAltIcon className="w-4 h-4 text-ever-base-content-mid" />
            <EverTg.Text>Edit</EverTg.Text>
          </div>
        </Menu.Item>
        {hierarchyDetails.length > 1 && !isHRIS && (
          <Menu.Item onClick={() => setSelectedMenu(ACTIONS.DELETE)}>
            <div className="flex items-center gap-3">
              <Trash03Icon className="w-4 h-4 text-ever-error" />
              <EverTg.Text className="text-ever-error">Delete</EverTg.Text>
            </div>
          </Menu.Item>
        )}
      </Menu>
    );
  };

  const hierarchyDetails = employee?.employeeHierarchy
    ? sortHierachyEmployees(employee.employeeHierarchy)
    : [];

  const hierarchyColumns = [
    {
      title: "Reporting Manager",
      dataIndex: "reportingManagerEmailId",
      key: "reportingManagerEmailId",
      render: (_, record) => {
        const manager = record?.managerDetails;
        return (
          <>
            {manager && (
              <div className="flex items-center gap-2">
                <EverGroupAvatar
                  size="small"
                  avatars={[
                    {
                      firstName: manager.firstName,
                      lastName: manager.lastName,
                      image: manager.profilePicture,
                    },
                  ]}
                />
                <EverTg.Text className="ml-1">
                  {`${manager.firstName} ${manager.lastName}`}
                </EverTg.Text>
              </div>
            )}
          </>
        );
      },
    },
    {
      title: "Effective Start Date",
      dataIndex: "effectiveStartDate",
      key: "effectiveStartDate",
      render: (text, record) => {
        if (record.effectiveStartDate) {
          return (
            <EverFormatter.Date
              date={record?.effectiveStartDate}
              className="font-normal"
            />
          );
        }
        return "-";
      },
    },
    {
      title: "Effective End Date",
      dataIndex: "effectiveEndDate",
      key: "effectiveEndDate",
      render: (text, record) => {
        return (
          <div className="flex items-center justify-between">
            <EverTg.Text>
              <EverFormatter.Date
                date={record?.effectiveEndDate}
                className="font-normal"
              />
            </EverTg.Text>
            <Dropdown trigger={["click"]} overlay={getMenu}>
              <EverButton.Icon
                color="base"
                type="text"
                className="!h-fit !w-fit hover:bg-ever-base-100"
                onClick={() => handleClickMenu(record)}
                icon={
                  <DotsVerticalIcon className="!h-4 !w-4 text-ever-base-content-mid" />
                }
              />
            </Dropdown>
          </div>
        );
      },
    },
  ];

  return (
    <>
      <div className="h-[294px]">
        <Row className="pb-2.5">
          <Col>
            <EverTg.SubHeading4 className="text-ever-base-content">
              Reporting Details
            </EverTg.SubHeading4>
          </Col>
        </Row>
        {employee?.employeeHierarchy ? (
          <Table
            size="small"
            pagination={false}
            dataSource={hierarchyDetails}
            columns={hierarchyColumns}
            scroll={{ y: 200 }}
            scrollToFirstRowOnChange={true}
            bordered
            className="no-borders"
          />
        ) : (
          <div className="border border-solid border-ever-base-400 rounded-lg">
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          </div>
        )}
      </div>

      <div className="mt-2 pb-6">
        <EverDivider></EverDivider>
      </div>

      <Row className="pb-2.5">
        <Col>
          <EverTg.SubHeading4 className="text-ever-base-content">
            {t("COMMISSION")} Plan History
          </EverTg.SubHeading4>
        </Col>
      </Row>
      {employee?.employeeAllPlanDetails ? (
        <Table
          size="small"
          pagination={false}
          dataSource={sortEmployees(employee.employeeAllPlanDetails)}
          columns={planDetailsColumns(t, employee.employeeAllPlanDetails)}
          scroll={{ y: 128 }}
          scrollToFirstRowOnChange={true}
          bordered
          className="no-borders"
        />
      ) : (
        <div className="border border-solid border-ever-base-400 rounded-lg">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      )}

      <EditHistoricalManagers
        store={store}
        isHRIS={isHRIS}
        visible={visibleEditSplit}
        action={selectedMenu}
        editFieldData={editFieldData}
        handleClose={() => setVisibleEditSplit(false)}
        selectedManagerOption={selectedManagerOption}
        setSelectedManagerOption={setSelectedManagerOption}
      />

      <DeleteHistoricalManagers
        visible={selectedMenu === ACTIONS.DELETE}
        store={store}
        editFieldData={editFieldData}
        closeModal={() => setSelectedMenu(null)}
        selectedManagerOption={selectedManagerOption}
      />
    </>
  );
});

function getActivePlan(sEmployees) {
  const currentDate = moment();
  const activePlans = sEmployees.filter((employee) => {
    const startDate = moment(employee.effectiveStartDate);
    const endDate = moment(employee.effectiveEndDate);
    return currentDate.isBetween(startDate, endDate, null, "[]");
  });
  return activePlans;
}

function sortHierachyEmployees(sEmployees) {
  return sortBy(sEmployees, [
    function (o) {
      return moment(o.effectiveStartDate);
    },
  ]).reverse();
}

function sortEmployees(sEmployees) {
  const activePlans = getActivePlan(sEmployees);

  const inactivePlans = sEmployees.filter(
    (employee) =>
      !activePlans.find(
        (activeEmployee) => activeEmployee.planId === employee.planId
      )
  );

  const sortedActivePlans = activePlans.sort((a, b) => {
    const startDateDiff = moment(a.effectiveStartDate).diff(
      moment(b.effectiveStartDate)
    );
    return startDateDiff !== 0
      ? startDateDiff
      : moment(a.effectiveEndDate).diff(moment(b.effectiveEndDate));
  });

  const sortedInactivePlans = inactivePlans.sort((a, b) => {
    const startDateDiff = moment(a.effectiveStartDate).diff(
      moment(b.effectiveStartDate)
    );
    return startDateDiff !== 0
      ? startDateDiff
      : moment(a.effectiveEndDate).diff(moment(b.effectiveEndDate));
  });

  return [...sortedActivePlans, ...sortedInactivePlans];
}

export default ReportingDetails;
