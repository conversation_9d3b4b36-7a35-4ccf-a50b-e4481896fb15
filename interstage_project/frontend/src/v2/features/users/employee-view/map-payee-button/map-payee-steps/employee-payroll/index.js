import { useQuery, gql } from "@apollo/client";
import { observer } from "mobx-react";
import React, { useState, useEffect } from "react";

import Render from "./Render";

const CUSTOM_FIELD = gql`
  query ActiveCustomFieldsByClient {
    activeCustomFieldsByClient {
      systemName
      isMandatory
      options
      fieldType
      displayOrder
      displayName
      isEffectiveDated
    }
  }
`;

const EmployeePayroll = observer((props) => {
  const {
    currentDirty,
    setCurrentDirty,
    store,
    setCanNext,
    setCustomFieldDirty,
    customFieldDirty,
    payoutFreqList,
  } = props;
  const { employee } = store;
  const [isModify, setIsModify] = useState(false);
  const { data, loading } = useQuery(CUSTOM_FIELD, {
    fetchPolicy: "no-cache",
  });
  useEffect(() => {
    if (employee.employeePayroll && employee.employeePayroll.length > 0) {
      setIsModify(true);
    }
  }, [employee]);

  return (
    !loading && (
      <Render
        currentDirty={currentDirty}
        setCurrentDirty={setCurrentDirty}
        store={store}
        isModify={isModify}
        setCanNext={setCanNext}
        customFields={data?.activeCustomFieldsByClient || []}
        setCustomFieldDirty={setCustomFieldDirty}
        customFieldDirty={customFieldDirty}
        payoutFreqList={payoutFreqList}
      />
    )
  );
});

export default EmployeePayroll;
