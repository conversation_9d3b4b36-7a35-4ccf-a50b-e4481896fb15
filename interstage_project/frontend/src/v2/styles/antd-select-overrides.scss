/* SELECT DROPDOWN */
/* AVOID using 'gap' or 'margins' to create space between each list item, it will break virtual scroll calculations */

.ant-select {
  .ant-select-selection-placeholder {
    @apply text-ever-base-content-low text-base;
  }

  &.multiselect-overflow-scroll {
    .ant-select-selector {
      .ant-select-selection-overflow {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
      .ant-select-selection-overflow::-webkit-scrollbar {
        display: none;
      }
    }

    &.overflowing-content {
      .ant-select-arrow {
        box-shadow: -8px 0 12px -4px rgba(0, 23, 128, 0.15);
      }
    }
  }
}

.ant-select-arrow {
  @apply text-ever-base-content-low h-10 flex items-center mt-0 top-0 w-5;
}

.ant-select-clear {
  @apply right-4 -translate-y-0.5;
}

.ant-select-single {
  &:not(.ant-select-customize-input) {
    .ant-select-selector {
      @apply border-ever-base-400 h-10  bg-ever-base-50 rounded-l;
      .ant-select-selection-search-input {
        @apply h-10;
      }
    }
  }
  &.ant-select-sm {
    &:not(.ant-select-customize-input) {
      .ant-select-selector {
        @apply h-8;
      }
      &:not(.ant-select-customize-input) {
        .ant-select-selection-search-input {
          @apply h-8;
        }
      }
    }
    .ant-select-arrow {
      @apply h-8;
    }
  }
  .ant-select-selector {
    .ant-select-selection-item {
      @apply leading-10 max-h-full;
    }
  }
}

.ant-dropdown-menu-item {
  @apply text-xs;
  &:hover {
    @apply bg-ever-base-50;
  }
}

/*ant select dropdown item*/

.ant-select-item {
  @apply font-normal text-ever-base-content bg-transparent p-0 rounded;
  &.ant-select-item-group {
    @apply px-4 pt-4 pb-1 text-ever-base-content-mid;
  }

  .ant-select-item-option-content {
    @apply flex items-center gap-3 py-2 px-4 rounded;
  }

  &:not(.ant-select-item-option-disabled) {
    &.ant-select-item-option-selected {
      @apply bg-ever-primary-lite text-ever-primary-lite-content;
    }
    &.ant-select-item-option-active {
      @apply bg-ever-base-100 text-ever-base-content;
    }
  }
}

.ant-select-item-option-disabled {
  @apply text-ever-base-content-low;
}

.ant-select-item-option-selected {
  .ant-select-item-option-state {
    @apply text-ever-primary-lite-content flex items-center pr-3;
  }
  &:not(.ant-select-item-option-disabled) {
    @apply font-medium text-ever-primary-hover;
    span {
      &:not(.ant-avatar):not(.ant-avatar-string) {
        @apply font-medium text-ever-primary-hover;
      }
    }
  }
  &.ant-select-item-option-disabled {
    .ant-select-item-option-state {
      @apply text-ever-base-content-low;
    }
  }
}

.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  @apply bg-transparent;
}

.ant-form-item-control-input {
  min-height: 36px;
}

.dropdown-font-14 {
  .ant-select-item {
    @apply text-base;
  }
}

/* SELECT/MULTI-SELECT COMMON */

.ant-select {
  @apply font-normal text-ever-base-content;

  .ant-select-selection-overflow {
    @apply flex items-center;
  }

  &.h-100 {
    .ant-select-selector {
      @apply h-full;

      .ant-select-selection-item {
        @apply leading-10;
      }

      .ant-select-selection-search {
        .ant-select-selection-search-input {
          @apply h-full;
        }
      }

      .ant-select-selection-placeholder {
        @apply leading-10;
      }
    }
    &.ant-select-single {
      &:not(.ant-select-customize-input) {
        .ant-select-selector {
          @apply h-full rounded-lg;
        }
      }
    }
  }

  &.rounded {
    .ant-select-selector {
      @apply rounded-lg;
    }
  }

  &:not(.ant-select-disabled) {
    &:hover {
      .ant-select-selector {
        @apply border-ever-base-300 shadow-sm;
      }
    }
  }
  &:not(.ant-select-customize-input) {
    .ant-select-selector {
      @apply border-ever-base-400 bg-ever-base rounded-lg;
    }
  }
}

.ant-select-single {
  .ant-select-selector {
    .ant-select-selection-item {
      @apply leading-8;
    }
    @apply items-center;
  }
}

.ant-select-multiple {
  @apply relative;
  .ant-select-selector {
    @apply rounded-lg;
    min-height: 40px;
  }
  &.ant-select-sm {
    .ant-select-selector {
      min-height: 36px;
    }
  }
  &.h-100 {
    .ant-select-selector {
      .ant-select-selection-item {
        line-height: 22px;
      }
    }
  }
  &.ant-select-show-arrow {
    .ant-select-selector {
      @apply pr-8;
    }
  }
  .ant-select-arrow {
    @apply absolute w-8 px-1 flex justify-center items-center border-0 border-l border-ever-base-400 border-solid right-0 mt-0;
    top: 1px;
    height: calc(100% - 2px);
  }
}

.ant-select-focused {
  &:not(.ant-select-disabled) {
    &.ant-select-single {
      &:not(.ant-select-customize-input) {
        .ant-select-selector {
          @apply border-ever-primary ring ring-ever-primary-ring;
        }

        .ant-select-arrow {
          @apply text-ever-base-content-low;
          .anticon {
            @apply text-ever-primary;
          }
        }
      }
    }
    &.ant-select-multiple {
      &:not(.ant-select-customize-input) {
        .ant-select-selector {
          @apply border-ever-primary ring-2 ring-ever-primary-ring;
        }

        .ant-select-arrow {
          @apply text-ever-base-content-low;
          .anticon {
            @apply text-ever-primary;
          }
        }
      }
    }
  }
}

@mixin select-overflow-number-height {
  .ant-select-selection-item {
    @apply h-6;
  }
}

.ant-select-multiple {
  .ant-select-selection-item {
    @apply bg-ever-base-25 text-ever-base-content border-ever-base-400 border rounded-full h-6 items-center;
  }
  &.ant-select-lg {
    @include select-overflow-number-height;
  }
  &.ant-select-sm {
    @include select-overflow-number-height;
  }
}

.ant-select-disabled {
  &.ant-select-single,
  &.ant-select {
    &:not(.ant-select-customize-input) {
      .ant-select-selector {
        @apply bg-ever-base-200 border-ever-base-ring text-ever-base-content-low;
      }
    }
  }
  &.ant-select-multiple {
    .ant-select-selection-item {
      @apply bg-ever-base-50 border-ever-base-ring text-ever-base-content-low;
    }
  }
}

.ant-select-dropdown {
  @apply bg-ever-base rounded-lg text-ever-base-content border border-solid border-ever-base-400 p-1.5;
  box-shadow: 0px 4px 6px -2px rgba(0, 23, 128, 0.03),
    0px 12px 16px -4px rgba(0, 23, 128, 0.08);
}

.select-right-border-radius-none .ant-select-selector {
  border-top-right-radius: 0px !important;
  border-bottom-right-radius: 0px !important;
}

.select-right-border-width-none .ant-select-selector {
  border-right-width: 0px !important;
}

.select-left-border-radius-none .ant-select-selector {
  border-top-left-radius: 0px !important;
  border-bottom-left-radius: 0px !important;
}

.select-left-border-width-none .ant-select-selector {
  border-left-width: 0px !important;
}

.ant-tag-has-color .anticon-close {
  @apply text-ever-base-content-mid;
}

.ant-tag-has-color .anticon-close:hover {
  @apply text-ever-base-content;
}

.rightRoundedZero .ant-select .ant-select-selector {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}

// pivot table

.datasheet-pivot-table {
  .agg-values-select {
    .ant-select-selection-overflow {
      margin-top: 2px;
    }
  }
  .agg-function-tag-select {
    .ant-select {
      @apply h-6 cursor-text;
    }
  }

  .agg-function-tag-select .ant-select-selector {
    padding: 0 !important;
    height: 24px !important;
    min-height: 24px !important;
    pointer-events: none;
    cursor: text;

    .ant-select-selection-placeholder {
      @apply right-0 h-5;
      top: 60%;
    }
    .ant-select-selection-search-input {
      @apply w-3 ml-3 h-5 !cursor-pointer;
      pointer-events: auto;
    }
  }

  .selected-pivot-tag {
    max-width: 100%;
    display: flex;
    align-items: center;
    @apply rounded-sm h-6;
  }

  .selected-pivot-content-tag {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    width: calc(100% - 58px);
  }

  .ant-select-selector {
    height: auto !important;
  }
}

.rc-virtual-list-holder-inner {
  @apply gap-0.5;
}
