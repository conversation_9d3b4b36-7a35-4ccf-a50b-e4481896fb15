import { SuccessLottie } from "@everstage/evericons/lotties";
import {
  AlertTriangleIcon,
  InfoCircleIcon,
  XCloseIcon,
} from "@everstage/evericons/outlined";
import { Modal } from "antd";
import { isEmpty } from "lodash";
import PropTypes from "prop-types";
import React from "react";
import { twMerge } from "tailwind-merge";

import { EverTg } from "~/v2/components";
import { EverButton } from "~/v2/components/ever-button/EverButton";

/**
 * Renders a Modal component that is built ontop of antd's modal component.
 *
 * @param {React.ReactNode} children - The JSX content to render inside the Modal body.
 * @returns {React.ReactNode} JSX for the EverModal Component
 */

export function EverModal({ children = null, ...props }) {
  return (
    <Modal
      centered={true}
      footer={null}
      closable={true}
      closeIcon={<XCloseIcon className="h-5 w-5 text-ever-base-content-mid" />}
      {...props}
    >
      {children}
    </Modal>
  );
}

EverModal.Confirm = Confirm;
EverModal.Footer = EverModalFooter;

EverModal.propTypes = {
  children: PropTypes.node,
};

EverModal.defaultProps = {
  children: null,
};

EverModal.displayName = "EverModal";

const basicIcons = {
  success: (
    <SuccessLottie
      autoplay
      loop={false}
      animationTriggerEvent="click"
      className="h-16 w-16 text-ever-success"
    />
  ),
  error: <XCloseIcon className="h-8 w-8 text-ever-error" />,
  warning: <AlertTriangleIcon className="h-8 w-8 text-ever-warning" />,
  confirm: <AlertTriangleIcon className="h-8 w-8 text-ever-warning" />,
  info: <InfoCircleIcon className="h-8 w-8 text-ever-info" />,
};

const basicSmallIcons = {
  success: (
    <SuccessLottie
      autoplay
      loop={false}
      animationTriggerEvent="click"
      className="h-10 w-10 text-ever-success"
    />
  ),
  error: <XCloseIcon className="h-6 w-6 text-ever-error" />,
  warning: <AlertTriangleIcon className="h-6 w-6 text-ever-warning" />,
  confirm: <AlertTriangleIcon className="h-6 w-6 text-ever-warning" />,
  info: <InfoCircleIcon className="h-6 w-6 text-ever-info" />,
};

const basicTypeColors = {
  success: "",
  error: "bg-ever-error-lite text-ever-error-lite-content",
  warning: "bg-ever-warning-lite text-ever-warning-lite-content",
  confirm: "bg-ever-warning-lite text-ever-warning-lite-content",
  info: "bg-ever-info-lite text-ever-info-lite-content",
};

function getCommonConfirmComponent({
  icon,
  type,
  mode,
  iconContainerClasses,
  title,
  subtitle,
  noteMessage,
  noteMessageClasses,
  confirmationButtons,
}) {
  const finalIcon =
    icon || (mode === "inline" ? basicSmallIcons[type] : basicIcons[type]);

  if (mode === "inline")
    return (
      <div className="p-4 flex flex-row gap-x-4 items-start">
        <div className="w-12 h-12 shrink-0">
          <div
            className={twMerge(
              "w-full h-full rounded-full flex justify-center items-center",
              basicTypeColors[type],
              iconContainerClasses
            )}
          >
            {finalIcon}
          </div>
        </div>
        <div className="flex flex-col gap-y-3">
          <div>
            <EverTg.Heading2>{title}</EverTg.Heading2>
          </div>
          {!isEmpty(subtitle) && (
            <div className="text-ever-base-content-mid">{subtitle}</div>
          )}
          {!isEmpty(noteMessage) && (
            <div
              className={twMerge(
                "text-ever-base-content-mid",
                noteMessageClasses
              )}
            >
              {noteMessage}
            </div>
          )}
          {!isEmpty(confirmationButtons) && (
            <div className="flex gap-4">
              {confirmationButtons.map((button, i) => {
                return <React.Fragment key={i}>{button}</React.Fragment>;
              })}
            </div>
          )}
        </div>
      </div>
    );

  return (
    <div className="pt-6 pb-4 px-6 flex flex-col items-center">
      <div
        className={twMerge(
          "h-16 w-16 rounded-full flex justify-center items-center",
          basicTypeColors[type],
          iconContainerClasses
        )}
      >
        {finalIcon}
      </div>
      <EverTg.Heading2 className="mt-4 text-center">{title}</EverTg.Heading2>
      {!isEmpty(subtitle) && (
        <div className="text-ever-base-content-mid mt-2 text-center">
          {subtitle}
        </div>
      )}
      {!isEmpty(noteMessage) && (
        <div
          className={twMerge(
            "text-ever-base-content-mid mt-2 text-center",
            noteMessageClasses
          )}
        >
          {noteMessage}
        </div>
      )}
      {!isEmpty(confirmationButtons) && (
        <div className="flex items-center justify-center gap-4 mt-6">
          {confirmationButtons.map((button, i) => {
            return <React.Fragment key={i}>{button}</React.Fragment>;
          })}
        </div>
      )}
    </div>
  );
}

function Confirm({
  icon,
  type,
  mode = "default",
  title,
  subtitle,
  noteMessage,
  noteMessageClasses = "",
  confirmationButtons,
  iconContainerClasses,
  className,
  ...props
}) {
  return (
    <Modal
      centered={true}
      footer={null}
      closable={false}
      closeIcon={<XCloseIcon className="h-5 w-5 text-ever-base-content-mid" />}
      destroyOnClose={true}
      width={mode === "inline" ? 420 : 520}
      className={twMerge("confirm-modal", className)}
      {...props}
    >
      {getCommonConfirmComponent({
        icon,
        type,
        mode,
        title,
        subtitle,
        noteMessage,
        noteMessageClasses,
        confirmationButtons,
        iconContainerClasses,
      })}
    </Modal>
  );
}

Confirm.propTypes = {
  icon: PropTypes.node,
  title: PropTypes.node,
  subtitle: PropTypes.node,
  noteMessage: PropTypes.node,
  noteMessageClasses: PropTypes.string,
  confirmationButtons: PropTypes.arrayOf(PropTypes.node),
  iconContainerClasses: PropTypes.string,
  type: PropTypes.oneOf(["success", "error", "warning", "info"]),
};

Confirm.defaultProps = {
  icon: null,
  title: "",
  subtitle: "",
  noteMessage: "",
  noteMessageClasses: "",
  confirmationButtons: [],
  iconContainerClasses: "",
  type: "info",
};

Confirm.displayName = "EverModal.Confirm";

function modalBaseMethod(
  {
    title,
    mode = "default",
    subtitle = "",
    noteMessage = "",
    noteMessageClasses = "",
    icon = null,
    iconContainerClasses = "",
    okText,
    onOk,
    cancelText,
    onCancel,
    confirmationButtons,
    ...otherProps
  },
  type
) {
  const onOkClick = (e) => {
    Modal.destroyAll();
    if (onOk) onOk(e);
  };

  const onCancelClick = (e) => {
    Modal.destroyAll();
    if (onCancel) onCancel(e);
  };

  const getConfirmationButtons = () => {
    if (confirmationButtons) return confirmationButtons;

    const _confirmationButtons = [
      <EverButton key="ok" onClick={onOkClick}>
        {okText ?? "OK"}
      </EverButton>,
    ];
    if (type === "confirm")
      _confirmationButtons.splice(
        0,
        0,
        <EverButton
          key="cancel"
          color="base"
          type="ghost"
          onClick={onCancelClick}
        >
          {cancelText ?? "Cancel"}
        </EverButton>
      );

    return _confirmationButtons;
  };

  const customContent = (
    <>
      {getCommonConfirmComponent({
        icon,
        type,
        mode,
        title,
        subtitle,
        noteMessage,
        noteMessageClasses,
        confirmationButtons: getConfirmationButtons(),
        iconContainerClasses,
      })}
    </>
  );

  return Modal[type]({
    closable: false,
    onCancel: onCancelClick,
    ...otherProps,
    closeIcon: <XCloseIcon className="h-5 w-5 text-ever-base-content-mid" />,
    className: "custom-ever-modal-method-confirmation",
    content: customContent,
  });
}

EverModal.info = (props) => modalBaseMethod(props, "info");
EverModal.success = (props) => modalBaseMethod(props, "success");
EverModal.error = (props) => modalBaseMethod(props, "error");
EverModal.warning = (props) => modalBaseMethod(props, "warning");
EverModal.confirm = (props) => modalBaseMethod(props, "confirm");

EverModal.destroyAll = Modal.destroyAll;

function EverModalFooter({ children = null, className, ...props }) {
  return (
    <div
      className={twMerge(
        "absolute bottom-0 left-0 right-0 translate-y-[80%] px-6 py-3 bg-ever-base-50 rounded-b-xl",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

EverModalFooter.propTypes = {
  children: PropTypes.node,
};

EverModalFooter.defaultProps = {
  children: null,
};

EverModalFooter.displayName = "EverModal.Footer";
