import { CopyIcon } from "@everstage/evericons/outlined";
import {
  useFloating,
  offset,
  flip,
  shift,
  size,
  autoUpdate,
} from "@floating-ui/react";
import { cloneDeep } from "lodash";
import React, { useState, useRef, useEffect } from "react";
import { CopyToClipboard } from "react-copy-to-clipboard";
import { twMerge } from "tailwind-merge";

import {
  DATATYPE,
  EXPRESSION_FUNCTION_TYPES,
  EXPRESSION_TOKEN_TYPES,
  DATE_PLACEHOLDER,
} from "~/Enums";
import {
  EverCard,
  useOnClickOutside,
  EverPopover,
  EverTooltip,
  toast,
  EverHotToastMessage,
} from "~/v2/components";

import { AutoSuggestionView } from "./AutoSuggestionView";
import { NAVIGATION_KEYS } from "./common";
import { ExpressionboxHelp } from "./ExpressionboxHelp";
import { ExpressionStatus } from "./ExpressionStatus";
import { ExpressionToken } from "./ExpressionToken";
import { ManageFunction } from "./ManageFunction";
import {
  handleNoLabelArg,
  convertToToken,
  prepareFunctionToken,
  isDateRelatedFunction,
} from "./utils";

/**
 * @typedef {Object} ExpressionBoxProps
 * @property {Function} onExpressionChange - callback function that is called when the expression is changed - which happens whenever a token is
 * added or removed.Signature: `onExpressionChange(type: string, currentPosition: number, selectedValue: any)`
 *   - `type`: The type of the action that triggered the change.
 *   - `currentPosition`: The position of the token in the expression.
 *   - `selectedValue`: The value to be inserted or used in the expression, depending on the action type.`
 * @property {import("../types/expression-box-types").ExpressionDesignerStateType} expressionDesignerState - State of the expression designer.
 *   Contains the updated expression, current position, and auto-suggestions.
 *   Example: `{ expression: [...], position: 1, autosuggestions: [...] }`
 * @property {Function} handleUndoRedo - handler function that is triggered whenever a user indicates an undo or redo.  The function takes
 * one argument - `type` which is `undo` or `redo`.  It is supposed to change the expressionDesignerState
 * @property {import("../types/expression-box-types").AutoCompleteContextType} props.fullAutoSuggestionList - The complete array of suggestions obtained from the autocomplete response API.
 *   This array includes all available suggestions without any filtering.
 * @property {import("../types/expression-box-types").ValidationStatus}  validationStatus - The validation status of the expression
 * @property {Object} targetedFunctionConfig - Configuration for targeted functions. If specific values need to be sent to a particular function, pass an object like {"generateHierarchy": {datasheetId: "123", databookId: "33"}}.
 * @property {import("../types/expression-box-types").AutoCompleteContextType} props.autoCompleteResponse - The filtered array of suggestions passed to the expression box state.
 *   This array represents the suggestions that are currently visible or relevant in the expression box.
 *   It might be a subset of the `fullAutoSuggestionList` and is used for managing the state of the autocomplete feature within the expression box.
 *   The structure is defined by the `AutoCompleteContextType` from the specified module
 * @property {Number} maxTokens - The maximum number of tokens allowed in the expression
 * @property {String} className - The className for styling the expression box.
 * @property {Boolean} copyButtonVisibility - The flag determines the visibility of the copy button.
 * @property {String} planType - The type of the plan.
 * @property {Boolean} overrideHotkey - The flag to override the hotkey.
 * @property {function} handleHelpHotkey - The function to handle the help hotkey.
 */

/**
 * ExpressionBox is a composite component that consists of a div and multiple input boxes within it.
 * It was custom built for making it easy to add infix expressions in multiple places in the Everstage app
 *
 * References:
 * @see {@link https://github.com/Everstage/everstage-spm/blob/5e22ca670adc78b98d9a5ae18815d103b0fcdc7a/interstage_project/commission_engine/services/expression_designer/docs/readme.md#L1 | Docs}
 *
 * @param {ExpressionBoxProps} options - @see {@link ExpressionBoxProps}
 * @returns {React.ReactNode} Renders the ExpressionBox
 */

export function ExpressionBox({
  onExpressionChange,
  expressionDesignerState,
  handleUndoRedo,
  validationStatus,
  fullAutoSuggestionList,
  className,
  targetedFunctionConfig,
  autoCompleteResponse,
  maxTokens,
  copyButtonVisibility,
  planType,
  overrideHotkey,
  handleHelpHotkey,
}) {
  const autoSuggestions = expressionDesignerState?.autocompleteValues;
  const currentPosition = expressionDesignerState?.position;

  const [showAutocomplete, setShowAutocomplete] = useState(false);
  const expressionTokenRef = useRef([]);

  const inputRef = useRef(null);
  const expressionCardRef = useRef(null);
  const functionPopupAnchorRef = useRef(null);
  const [helpModalVisible, setHelpModalVisible] = useState(false);

  const scrollRef = useRef(null);
  const hiddenBoxRefs = useRef([]);
  const hiddenBoxSpanRefs = useRef([]);
  const [hiddenBoxWidth, setHiddenBoxWidth] = useState(0);
  const autoSuggestRef = useRef(null);
  const expressionContainer = useRef(null);
  const [showFunctionModal, setShowFunctionModal] = useState(false);
  const [functionContent, setFunctionContent] = useState({}); // This holds the meta data for the currently selected function.
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(0);
  const expression = expressionDesignerState?.expression;
  const [functionArgs, setFunctionArgs] = useState([]); // This holds the list of arguments for the currently edited function.
  const [editPosition, setEditPosition] = useState(-1);
  const selectedColorIndex = useRef({
    index: 0,
    totalCount: 0,
  });
  const colorIndexStack = useRef([]);

  const { refs, floatingStyles, update } = useFloating({
    open: showAutocomplete,
    middleware: [
      offset(0),
      flip({
        fallbackStrategy: "bestFit",
      }),
      shift(),
      size({
        apply({ availableHeight, elements }) {
          const contentHeight = elements.floating.children[0].scrollHeight;
          const maxHeight = Math.min(availableHeight, 420);
          const minHeight = contentHeight < 180 ? contentHeight : 180;

          elements.floating.style.maxHeight = `${maxHeight}px`;
          elements.floating.style.minHeight = `${minHeight}px`;
        },
      }),
    ],
    whileElementsMounted: autoUpdate,
    placement: "bottom",
  });

  // this useEffect updates css position of the function manager popover anchor
  useEffect(() => {
    // set the function manager popover anchor position same as the current tagRef
    if (
      expression[editPosition > -1 ? editPosition : currentPosition]
        ?.tokenType === EXPRESSION_TOKEN_TYPES.FUNCTIONS
    ) {
      functionPopupAnchorRef.current.style.left =
        expressionTokenRef.current[
          editPosition > -1 ? editPosition : currentPosition
        ]?.getBoundingClientRect().left -
        expressionCardRef.current.getBoundingClientRect().left +
        "px";
      functionPopupAnchorRef.current.style.top =
        expressionTokenRef.current[
          editPosition > -1 ? editPosition : currentPosition
        ]?.getBoundingClientRect().top -
        expressionCardRef.current.getBoundingClientRect().top +
        "px";
    }
  }, [
    showFunctionModal,
    expressionTokenRef.current,
    editPosition,
    currentPosition,
    expression,
  ]);

  // this useEffect updates the width of the hidden inputbox when the value of the input box changes
  useEffect(() => {
    setHiddenBoxWidth(
      hiddenBoxSpanRefs.current[currentPosition]?.offsetWidth || 0
    );
  }, [hiddenBoxRefs.current?.[currentPosition]?.value]);

  function manageAutoSuggestView(arg) {
    setShowAutocomplete(arg);
  }

  /**
   * This function is used to set glow effect on the EverCard component
   * to make it look like an input element and it is currently focused
   * @param {Number} [index] - The index of the token to be edited
   * @param {String} type - The type of focus. It can be "focus" or "blur"
   * @param {String} [inputValue] - The value of the input box
   * @returns {void}
   * */
  function setExpressionCardFocusStyle(type, index, inputValue = "") {
    const classes = ["border-ever-primary"];
    if (type === "focus") {
      closeFunctionManagerPopover("cancel"); // because focusing elswhere means user is cancelling the edit mode
      expressionCardRef.current.classList.add(...classes);
      onExpressionChange({
        type: NAVIGATION_KEYS.FOCUS,
        currentPosition: index,
        selectedValue: inputValue,
      });
      manageAutoSuggestView(true);
      setSelectedSuggestionIndex(0);
    } else {
      expressionCardRef.current.classList.remove(...classes);
    }
  }

  /**
   * This function is used to handle the change event on the input box.
   * It calls the onExpressionChange function with the selectedValue and the type of change.
   * @param {String} value - The value of the input box
   * @returns {void}
   * */
  const handleInputChange = (value) => {
    onExpressionChange({
      type: NAVIGATION_KEYS.FOCUS,
      currentPosition: expression.length,
      selectedValue: value,
    });
    manageAutoSuggestView(true);
    setSelectedSuggestionIndex(0);
  };

  /**
   * This function is used to open the function manager popover. It sets the function content,
   * and calls the onExpressionChange function based on the editPosition. If editPosition is greater than -1,
   * it means a token is being edited, so the onExpressionChange function is called with type as edit.
   *
   * It also sets the position of the function manager popover anchor.
   *
   * @param {Object} selectedValue - The selected value from the auto suggestions.
   * @param {Number} editPosition - The position of the token to be edited. If it's greater than -1, it means a token is being edited.
   */
  function openFunctionManagerPopover(selectedValue, editPosition) {
    setFunctionContent({
      type:
        editPosition > -1 ? NAVIGATION_KEYS.EDIT : NAVIGATION_KEYS.SELECTION,
      index: editPosition > -1 ? editPosition : currentPosition,
      value: selectedValue,
      title: selectedValue.value,
    });
    manageAutoSuggestView(false);
    if (editPosition > -1) {
      // add a editMode flag to the currently editing token temporarily
      // this is to help show the dotted border around the token
      onExpressionChange({
        type: NAVIGATION_KEYS.EDIT,
        currentPosition: editPosition,
        selectedValue: {
          ...selectedValue,
          editMode: true,
        },
      });

      if (
        expression[editPosition]?.tokenType === EXPRESSION_TOKEN_TYPES.FUNCTIONS
      ) {
        setFunctionArgs(expression[editPosition]?.token?.args || []);
      }
    } else {
      // add a temp mockToken in the expression array temporarily
      onExpressionChange({
        type: NAVIGATION_KEYS.TEMP,
        currentPosition: currentPosition,
        selectedValue: {
          ...selectedValue,
          mockToken: true,
        },
      });
      setFunctionArgs([]);
    }
    setShowFunctionModal(true);
  }

  /**
   * This function is used to close the function manager popover.
   *
   * It handles different scenarios based on the closeType.
   *
   * If the closeType is "cancel", it removes the temporary mockToken
   * from the expression array and triggers a token change in the main expression array.
   *
   * If the closeType is not "cancel", it removes the editMode and mockToken both from
   * the object and triggers a token change in the main expression array.
   *
   * After handling the closeType scenarios, it sets the focus on the next input only if the popover was open.
   *
   * @param {String} closeType - The type of close action. It can be "cancel" or not specified.
   */
  function closeFunctionManagerPopover(closeType) {
    const isPopoverOpen = showFunctionModal;
    setShowFunctionModal(false);
    let nextFocusIndex = currentPosition;

    // if the popover was closed by clicking on the cancel button or outside the popover
    if (closeType === "cancel") {
      // find the unfinished token of type FUNCTIONS and delete it

      const unfinishedTokenIndex = expression.findIndex(
        (item) =>
          item.mockToken === true &&
          item.tokenType === EXPRESSION_TOKEN_TYPES.FUNCTIONS
      );
      if (unfinishedTokenIndex > -1) {
        onExpressionChange({
          type: NAVIGATION_KEYS.DELETE,
          currentPosition: unfinishedTokenIndex + 1,
        });
        // When unfinished token is deleted, that position itself becomes the next focus index
        nextFocusIndex = unfinishedTokenIndex;
      } else {
        const edittingTokenIndex = expression.findIndex(
          (item) => item.editMode === true
        );
        if (edittingTokenIndex === -1) return;
        const edittingToken = cloneDeep(expression[edittingTokenIndex]);
        delete edittingToken.editMode;
        onExpressionChange({
          type: NAVIGATION_KEYS.EDIT,
          currentPosition: editPosition,
          selectedValue: edittingToken,
        });
        nextFocusIndex = edittingTokenIndex + 1;
      }
    } else {
      const tempObj = cloneDeep(functionContent.value);
      // set the function arguments to the ast.
      tempObj.token.args = functionArgs;
      // Check if the function name in the 'tempObj' token is 'Timezone'
      if (tempObj.token.functionName === EXPRESSION_FUNCTION_TYPES.Timezone) {
        // If it's the 'Timezone' function, add the 'timezoneToLabel' property from 'functionContent'
        tempObj.token.timezoneMap = functionContent.timezoneToLabel;
      }

      // remove the editMode from the object
      delete tempObj.token.editMode;
      delete tempObj.token.mockToken;
      tempObj.token.name = _createV1Name(tempObj);
      tempObj.token.key = _createV1Name(tempObj);
      // Check if the function name in the token property of tempObj is either StartDate or LastDate
      if (isDateRelatedFunction(tempObj.token?.functionName)) {
        // If the function is StartDate or LastDate, remove the 'payoutPeriods' property from the token
        delete tempObj.token.payoutPeriods;
      }
      // Check if the token's function is GetValueFromHierarchy
      if (
        tempObj.token?.functionName ===
        EXPRESSION_FUNCTION_TYPES.GetValueFromHierarchy
      ) {
        // Extract the selected hierarchy field from the token arguments
        const selectedHierarchyField = tempObj.token.args[0]?.token?.key || "";
        const { userModifiedVariables, dataTypesById } =
          targetedFunctionConfig[
            EXPRESSION_FUNCTION_TYPES.GetValueFromHierarchy
          ];

        // Retrieve metadata for the selected hierarchy field from userModifiedVariables
        const metaData =
          userModifiedVariables[selectedHierarchyField]?.metaData || {};

        //Extract the column name from the metadata
        const columnName = metaData?.token?.args[0]?.token?.key;

        //Filter fullAutoSuggestionList to get selected meta data
        const selectedHiearchyField = fullAutoSuggestionList.filter(
          (x) => x.value === selectedHierarchyField
        );

        const selectedColumnData = fullAutoSuggestionList.filter(
          (x) => x.value === columnName
        );

        // If Hierarchy Field is derived from another sheet , output Data Type will be determined from sourceCfMetaData
        // If Hierarchy Field is draft/created newly on sheet, output data type will be determined from meta info

        const outputDataType =
          dataTypesById[
            selectedHiearchyField[0].sourceCfMetaData?.hierarchyForDataTypeId
          ];
        tempObj.token.dataType =
          outputDataType || selectedColumnData[0]?.meta?.dataType || "String";
      }
      onExpressionChange({
        type: NAVIGATION_KEYS.EDIT,
        currentPosition: functionContent?.index,
        selectedValue: tempObj,
      });
      nextFocusIndex = functionContent?.index + 1;
    }
    // focus on the next input only if the popover was open
    if (isPopoverOpen) {
      setTimeout(() => {
        if (expressionCardRef.current) {
          expressionCardRef.current
            .querySelectorAll("input")
            [nextFocusIndex].focus(); // once token added focus on the conrresponding input
        }
      });
    }
    setFunctionArgs([]);
  }

  /**
   * This function is used to handle the keyboard events on the input box.
   * It handles the following events:
   * 1. Down Arrow - It selects the next suggestion
   * 2. Up Arrow - It selects the previous suggestion
   * 3. Enter - It selects the selected suggestion
   * 4. Backspace || Delete - It deletes the last token
   * 5. Arrow Left - It moves the focus to the previous input
   * 6. Arrow Right - It moves the focus to the next input
   * 7. Tab - It closes the auto suggest view
   * 8. Command + z - It triggers the undo action
   * 9. Command + Shift + z - It triggers the redo action
   * @param {Object} event - The keydown event on the input box
   * @returns {void}
   * */

  const handleKeyDown = (event) => {
    if (event.ctrlKey && ["h", "H"].includes(event.key) && !overrideHotkey) {
      setHelpModalVisible(true);
    }
    if (event.ctrlKey && ["h", "H"].includes(event.key) && overrideHotkey) {
      handleHelpHotkey();
    }

    if (event.key === "Escape") {
      document.activeElement.blur();
      manageAutoSuggestView(false);
      event.stopPropagation();
    }
    if (event.key === "Home") {
      event.preventDefault();
      if (hiddenBoxRefs.current[0]) {
        hiddenBoxRefs.current[0].focus();
      }
    }
    if (event.key === "End") {
      event.preventDefault();
    }
    if (event.key === "Enter" && autoSuggestions.length > 0) {
      event.preventDefault();
      handleSuggestionUserInputs("ENTER");
    }
    if (event.key === "ArrowDown") {
      handleSuggestionUserInputs("DOWN");
    }
    if (event.key === "ArrowUp") {
      handleSuggestionUserInputs("UP");
    }
    if (
      (event.key === "Backspace" || event.key === "Delete") &&
      inputRef.current.value === "" &&
      expression.length > 0
    ) {
      hiddenBoxRefs.current.pop();
      expressionTokenRef.current.pop();
      onExpressionChange({ type: NAVIGATION_KEYS.DELETE });
    }
    if (
      event.key === "ArrowLeft" &&
      inputRef.current.value === "" &&
      hiddenBoxRefs.current[expression.length - 1]
    ) {
      hiddenBoxRefs.current[expression.length - 1].focus();
    }

    // command + z
    if (event.metaKey && !event.shiftKey && event.key === "z") {
      event.preventDefault();
      handleUndoRedo("UNDO");
    }
    // command + shift + z
    if (event.metaKey && event.shiftKey && event.key === "z") {
      event.preventDefault();
      handleUndoRedo("REDO");
    }

    // moving out of expression box using Tab, but not shift + Tab
    // close the autosuggest view
    if (event.key === "Tab" && !event.shiftKey) {
      manageAutoSuggestView(false);
      setExpressionCardFocusStyle("blur");
    }

    if (maxTokens && expression.length >= maxTokens) {
      event.preventDefault();
    }
  };

  /**
   * This function is used to handle the change event on the hidden inputs.
   * It calls the onExpressionChange function with the selectedValue and the type of change.
   * @param {Object} event - The change event on the hidden inputs
   * @param {Number} index - The index of the hidden input
   * @returns {void}
   * */
  const handleHiddenInputChange = (value, index) => {
    onExpressionChange({
      type: NAVIGATION_KEYS.FOCUS,
      currentPosition: index,
      selectedValue: value,
    });
    manageAutoSuggestView(true);
  };

  /**
   * This runs when the user clicks on a suggestion or presses enter on a suggestion.
   * It calls the onExpressionChange function with the selectedValue and the type of change.
   * @param {Number} selectedIndex - The index of the clicked suggestion, this will be undefined if selected via keyboard
   **/
  function onTokenSelection(selectedIndex, editPosition) {
    const selectedValue = convertToToken(
      autoSuggestions[selectedIndex ?? selectedSuggestionIndex]
    );

    if (
      selectedValue?.tokenType === "FUNCTIONS" &&
      !["TieredValue", "TieredPercentage"].includes(
        selectedValue?.token?.functionName
      )
    ) {
      openFunctionManagerPopover(selectedValue, editPosition);
      if (editPosition > -1) {
        onExpressionChange({
          type: NAVIGATION_KEYS.EDIT,
          currentPosition: editPosition,
          selectedValue: {
            ...selectedValue,
            editMode: true,
          },
        });
      }
    } else {
      // check if the token is a date token
      const isAddDateToken =
        selectedValue?.tokenType === EXPRESSION_TOKEN_TYPES.CONSTANTS &&
        selectedValue?.token?.args[0] === DATATYPE.DATE;
      if (editPosition > -1) {
        onExpressionChange({
          type: NAVIGATION_KEYS.EDIT,
          currentPosition: editPosition,
          selectedValue,
        });
      } else {
        if (isAddDateToken) {
          // if the value is the date placeholder, then show the date picker
          if (DATE_PLACEHOLDER === selectedValue?.token?.args[1]) {
            onExpressionChange({
              type: NAVIGATION_KEYS.TEMP,
              selectedValue: {
                ...selectedValue,
                mockToken: true,
                isCalenderVisible: true,
              },
            });
          } else {
            // if the value is not the date placeholder, then select the date
            onExpressionChange({
              type: NAVIGATION_KEYS.SELECTION,
              selectedValue: {
                ...selectedValue,
              },
            });
          }
        } else {
          onExpressionChange({
            type: NAVIGATION_KEYS.SELECTION,
            selectedValue: selectedValue,
          });
        }
      }
      if (!isAddDateToken) {
        // focus on the next input
        // the setTimout is important here, otherwise the focus
        // will happen before the expression array is updated
        // and will cause miscalculation in the currentPositon
        setTimeout(() => {
          if (currentPosition < expression.length) {
            expressionCardRef.current
              .querySelectorAll("input")
              [currentPosition + 1].focus();
          } else {
            inputRef?.current?.focus();
          }
        });
      }
    }
    if (hiddenBoxRefs.current[currentPosition]) {
      hiddenBoxRefs.current[currentPosition].value = "";
    }
    inputRef.current.value = "";
  }

  /**
   * This function is used to handle the user inputs on the suggestions.
   * It handles the following events:
   * 1. Down Arrow - It selects the next suggestion
   * 2. Up Arrow - It selects the previous suggestion
   * 3. Enter - It selects the selected suggestion
   * 4. Click - It selects the clicked suggestion
   * @param {String} direction - The direction of the user input. It can be "DOWN", "UP", "ENTER" or "CLICK"
   * @param {Number} [editPosition] - The position of the token to be edited.
   * @param {Number} [suggesstionIndex] - The index of the clicked suggestion, this will be undefined if selected via keyboard
   * @returns {void}
   * */
  function handleSuggestionUserInputs(
    direction,
    editPosition,
    suggesstionIndex
  ) {
    if (maxTokens && expression.length >= maxTokens) return;
    switch (direction) {
      case "DOWN": {
        setSelectedSuggestionIndex((prevIndex) => {
          if (prevIndex === autoSuggestions.length - 1) {
            return 0;
          } else {
            return prevIndex + 1;
          }
        });
        if (showAutocomplete === false) {
          manageAutoSuggestView(true);
        }
        break;
      }
      case "UP": {
        setSelectedSuggestionIndex((prevIndex) => {
          if (prevIndex === 0) {
            return autoSuggestions.length - 1;
          } else {
            return prevIndex - 1;
          }
        });
        break;
      }
      case "ENTER": {
        onTokenSelection(selectedSuggestionIndex, editPosition);
        break;
      }
      case "CLICK": {
        setSelectedSuggestionIndex(suggesstionIndex);
        onTokenSelection(suggesstionIndex, editPosition);
        break;
      }
    }
  }

  /**
   * This function is used to handle the keyboard events on the hidden inputs.
   * It handles the following events:
   * 1. Down Arrow - It selects the next suggestion
   * 2. Up Arrow - It selects the previous suggestion
   * 3. Enter - It selects the selected suggestion
   * 4. Backspace || Delete - It deletes the last token
   * 5. Arrow Left - It moves the focus to the previous input
   * 6. Arrow Right - It moves the focus to the next input
   * 7. Command + z - It triggers the undo action
   * 8. Command + Shift + z - It triggers the redo action
   * @param {Object} event - The keydown event on the hidden inputs
   * @param {Number} index - The index of the hidden input
   * @returns {void}
   * */
  const handleHiddenInputKeyDown = (event, index) => {
    if (event.ctrlKey && ["h", "H"].includes(event.key)) {
      setHelpModalVisible(true);
    }
    if (event.key === "Escape") {
      document.activeElement.blur();
      manageAutoSuggestView(false);
      event.stopPropagation();
    }
    if (event.key === "Home") {
      event.preventDefault();
      if (hiddenBoxRefs.current[0]) {
        hiddenBoxRefs.current[0].focus();
      }
    }
    if (event.key === "End") {
      event.preventDefault();
      inputRef.current.focus();
    }
    if (event.key === "Enter" && autoSuggestions.length > 0) {
      event.preventDefault();
      handleSuggestionUserInputs("ENTER");
    }
    if (
      (event.key === "Backspace" || event.key === "Delete") &&
      hiddenBoxRefs.current[index]?.value === "" &&
      index > 0
    ) {
      hiddenBoxRefs.current.splice(index, 1);
      expressionTokenRef.current.splice(index, 1);
      onExpressionChange({ type: NAVIGATION_KEYS.DELETE });
      setTimeout(() => {
        expressionCardRef.current.querySelectorAll("input")[index - 1].focus();
      });
    }
    if (event.key === "ArrowDown") {
      handleSuggestionUserInputs("DOWN");
    }
    if (event.key === "ArrowUp") {
      handleSuggestionUserInputs("UP");
    }
    if (
      event.key === "ArrowLeft" &&
      hiddenBoxRefs.current[index]?.value === "" &&
      hiddenBoxRefs.current[index - 1]
    ) {
      hiddenBoxRefs.current[index - 1].focus();
    }
    if (
      event.key === "ArrowRight" &&
      hiddenBoxRefs.current[index]?.value === ""
    ) {
      if (hiddenBoxRefs.current[index + 1]) {
        hiddenBoxRefs.current[index + 1].focus();
      } else {
        setTimeout(() => {
          inputRef?.current?.focus();
        });
      }
    }

    // command + z
    if (event.metaKey && !event.shiftKey && event.key === "z") {
      event.preventDefault();
      handleUndoRedo("UNDO");
    }
    // command + shift + z
    if (event.metaKey && event.shiftKey && event.key === "z") {
      event.preventDefault();
      handleUndoRedo("REDO");
    }

    if (maxTokens && expression.length >= maxTokens) {
      event.preventDefault();
    }

    update();
  };

  // close autosuggest on click outside
  useOnClickOutside(expressionContainer, () => {
    manageAutoSuggestView(false);
    setExpressionCardFocusStyle("blur");
  });

  /**
   * This function is used to handle the click or focus event on the expression box.
   * It checks if the click or focus was on any hidden inputs or normal inputs.
   * If yes, then it focuses on the corresponding input.
   * @returns {void}
   * */
  function onClickOrFocusOnExpressionBox() {
    // check if the click was focusing on any hidden inputs or tagRefs
    // if yes, then focus on the corresponding input
    const inputIndex = hiddenBoxRefs.current.findIndex((input) =>
      input?.contains(document.activeElement)
    );

    const tokenIndex = expressionTokenRef.current.findIndex((tag) =>
      tag?.contains(document.activeElement)
    );

    if (inputIndex > -1) {
      // if a hidden input is clicked
      hiddenBoxRefs.current[inputIndex].focus();
    } else if (tokenIndex > -1) {
      // if a token is clicked
      expressionTokenRef.current[tokenIndex].focus();
    } else {
      // if the click is on the expression card
      inputRef.current.focus();
    }
  }

  const lastInputWidth = inputRef.current?.value?.length * 12 || 5;

  // This function is used to safely parse the data.
  // If the data is not a valid JSON, it returns the original data.
  function safeParse(data) {
    try {
      return JSON.parse(data); // Try parsing the data
    } catch {
      return data; // If parsing fails, return the original data
    }
  }
  return (
    <>
      <ExpressionboxHelp
        visible={helpModalVisible}
        setHelpModalVisible={setHelpModalVisible}
        fullAutoSuggestionList={fullAutoSuggestionList}
      />
      <div
        className={twMerge("relative mt-4 text-left w-full", className)}
        ref={expressionContainer}
        onKeyDown={(event) => {
          // Stop the expression container's keydown event from being propagated to the parent.
          event.stopPropagation();
        }}
      >
        <EverPopover
          portalContainer={expressionContainer.current}
          className="border border-solid border-ever-base-400 p-4"
          showArrow={false}
          noTrigger={true}
          showCloseIcon={false}
          // alignOffset={alignOffset}
          open={showFunctionModal}
          onOpenChange={(open) => {
            setShowFunctionModal(open);
          }}
          onClose={() => {
            closeFunctionManagerPopover("cancel");
          }}
          onEscapeKeyDown={() => {
            closeFunctionManagerPopover("cancel");
          }}
          onFocusOutside={() => {
            closeFunctionManagerPopover("cancel");
          }}
          onPointerDownOutside={() => {
            closeFunctionManagerPopover("cancel");
          }}
          content={
            <ManageFunction
              functionContent={functionContent}
              setFunctionContent={setFunctionContent}
              functionArgs={functionArgs}
              setFunctionArgs={setFunctionArgs}
              closeFunctionManagerPopover={closeFunctionManagerPopover}
              fullAutoSuggestionList={fullAutoSuggestionList}
              autoCompleteResponse={autoCompleteResponse}
              targetedFunctionConfig={targetedFunctionConfig}
              planType={planType}
            />
          }
        >
          <div
            ref={functionPopupAnchorRef}
            className="absolute h-8 w-24 pointer-events-none top-2"
          ></div>
        </EverPopover>
        <div className="relative">
          <div className="flex group" ref={refs.setReference}>
            <EverCard
              paddingSize="sm"
              roundedSize="lg"
              className={twMerge(
                "flex group-hover:border-ever-primary flex-wrap mb-2 cursor-text  flex-1 w-full py-[5px] pl-1 max-h-[220px] overflow-y-auto rounded-s-lg",
                copyButtonVisibility
                  ? "rounded-e-none border-r-0"
                  : "rounded-e-lg"
              )}
              ref={expressionCardRef}
              onClick={() => {
                onClickOrFocusOnExpressionBox();
              }}
              onFocus={() => {
                onClickOrFocusOnExpressionBox();
              }}
              onBlur={() => {
                // if the focus is not on any hidden inputs or tagRefs
                // then loop through all the hidden inputs and clear the values
                hiddenBoxRefs.current.forEach((input) => {
                  if (input?.value && input?.style) {
                    input.value = "";
                    input.style.width = 0;
                  }
                });
                // clear the input value of the last input
                inputRef.current.value = "";
              }}
              tabIndex="0"
            >
              <div
                className="w-[calc(100%-25px)] flex flex-wrap max-w-full gap-y-1 pr-2 relative"
                data-testid="expression-input-box"
              >
                {expression.map((_, index) => {
                  // Check if the current index is the beginning of the expression
                  if (index === 0) {
                    // Reset the selected color index to 0
                    selectedColorIndex.current.index = 0;
                    selectedColorIndex.current.totalCount = 0;
                    // Clear the color index stack
                    colorIndexStack.current = [];
                  }
                  // Check if the current expression token is of type BRACKETS
                  if (
                    expression[index]?.tokenType ===
                    EXPRESSION_TOKEN_TYPES.BRACKETS
                  ) {
                    // Check if the bracket token is an opening bracket "("
                    if (expression[index]?.token.name === "(") {
                      // Increment the selected color index and push it onto the color index stack
                      selectedColorIndex.current.index =
                        selectedColorIndex.current.totalCount + 1;
                      selectedColorIndex.current.totalCount =
                        selectedColorIndex.current.totalCount + 1;
                      colorIndexStack.current.push(
                        selectedColorIndex.current.index
                      );
                    } else {
                      // If the bracket token is a closing bracket ")", pop the color index from the stack
                      selectedColorIndex.current.index =
                        colorIndexStack.current.pop();
                    }
                  }

                  return (
                    <React.Fragment key={index}>
                      {/* Hidden span to measure content width */}
                      <>
                        <span
                          ref={(spanRef) => {
                            hiddenBoxSpanRefs.current[index] = spanRef;
                          }}
                          className="invisible whitespace-pre text-[14px] font-inherit absolute h-px"
                        >
                          {hiddenBoxRefs.current?.[index]?.value || " "}
                        </span>
                        <input
                          type="text"
                          ref={(inputRef) => {
                            hiddenBoxRefs.current[index] = inputRef;
                          }}
                          className={twMerge(
                            "focus:!outline-none border-transparent h-7  mx-0 pl-0.5  min-w-2 "
                          )}
                          // The width of the input is calculated based on the current position and the width of the hidden span
                          style={{
                            width:
                              index === currentPosition && hiddenBoxWidth
                                ? Math.max(hiddenBoxWidth, 7) + 7
                                : hiddenBoxSpanRefs.current[index]
                                    ?.offsetWidth + 7 || 7,
                          }}
                          onChange={(event) => {
                            handleHiddenInputChange(
                              event?.target?.value,
                              index
                            );
                          }}
                          onKeyDown={(event) => {
                            handleHiddenInputKeyDown(event, index);
                          }}
                          onFocus={() => {
                            setExpressionCardFocusStyle(
                              "focus",
                              index,
                              hiddenBoxRefs?.current[index]?.value
                            );
                            setEditPosition(-1);
                          }}
                          onPaste={(event) => {
                            event.preventDefault();
                            // Get the clipboard data as text
                            const copiedData =
                              event.clipboardData.getData("text");
                            // Check if the expression is empty
                            if (copiedData) {
                              // Parse the copied data (assuming it's in JSON format)
                              const parsedData = safeParse(copiedData);
                              // Trigger an action to handle the paste event with the parsed data
                              if (
                                typeof parsedData === "object" &&
                                parsedData.type === "ever_expression"
                              ) {
                                onExpressionChange({
                                  type: NAVIGATION_KEYS.PASTE,
                                  currentPosition: index,
                                  selectedValue: parsedData.value,
                                });
                              } else {
                                hiddenBoxRefs.current[index].value =
                                  event.clipboardData.getData("text");
                                handleHiddenInputChange(
                                  event.clipboardData.getData("text"),
                                  index
                                );
                              }
                            }
                          }}
                        />
                      </>

                      <ExpressionToken
                        setEditPosition={setEditPosition}
                        index={index}
                        selectedColorIndex={selectedColorIndex.current.index}
                        //tagRefs={tagRefs}
                        // setTagRefs={setTagRefs}
                        manageAutoSuggestView={manageAutoSuggestView}
                        onExpressionChange={onExpressionChange}
                        openFunctionManagerPopover={openFunctionManagerPopover}
                        currentToken={expression[index]}
                        expressionTokenRef={expressionTokenRef}
                        expressionCardRef={expressionCardRef}
                        handleSuggestionUserInputs={handleSuggestionUserInputs}
                      />
                    </React.Fragment>
                  );
                })}
                <input
                  type="text"
                  className="focus:!outline-none border-transparent ml-1 h-7 p-1"
                  // the width of the last input is calculated based on the length of the input value
                  style={{
                    width:
                      expression.length === 0 ? "100%" : lastInputWidth + 9,
                  }}
                  ref={inputRef}
                  onChange={(event) => handleInputChange(event?.target?.value)}
                  onFocus={() => {
                    setExpressionCardFocusStyle(
                      "focus",
                      expression.length,
                      inputRef?.current?.value
                    );
                    setEditPosition(-1);
                  }}
                  onPaste={(event) => {
                    event.preventDefault();

                    // Get the clipboard data as text
                    const copiedData = event.clipboardData.getData("text");
                    // Check if the expression is empty
                    if (copiedData) {
                      // Parse the copied data (assuming it's in JSON format)
                      const parsedData = safeParse(copiedData);
                      // Trigger an action to handle the paste event with the parsed data
                      if (
                        typeof parsedData === "object" &&
                        parsedData.type === "ever_expression"
                      ) {
                        onExpressionChange({
                          type: NAVIGATION_KEYS.PASTE,
                          currentPosition: expression.length,
                          selectedValue: parsedData.value,
                        });
                      } else {
                        inputRef.current.value =
                          event.clipboardData.getData("text");
                        handleInputChange(event.clipboardData.getData("text"));
                      }
                    }
                  }}
                  // placeholder is set only when the expression is empty
                  placeholder={
                    expression.length === 0 ? "Press Ctrl + H for help" : ""
                  }
                  onKeyDown={(e) => handleKeyDown(e)}
                />
              </div>
              <div
                ref={scrollRef}
                className={twMerge(
                  "w-6 flex justify-center items-center",
                  scrollRef.current?.clientHeight > 220 ? "items-end" : ""
                )}
              >
                <ExpressionStatus
                  status={validationStatus?.status}
                  message={validationStatus?.message}
                />
              </div>
            </EverCard>
            {copyButtonVisibility && (
              <CopyToClipboard
                // when copy the expression, we added the type to differentiate the expression from other text
                text={
                  validationStatus?.status === "VALID"
                    ? JSON.stringify({
                        type: "ever_expression",
                        value: expression,
                      })
                    : ""
                }
                onCopy={() => {
                  // show the toast message when the expression is copied to clipboard and the expression is valid
                  if (validationStatus?.status === "VALID") {
                    toast.custom(() => (
                      <EverHotToastMessage
                        type="success"
                        description={"Expression copied to clipboard!"}
                      />
                    ));
                  }
                }}
              >
                <EverTooltip
                  title={
                    validationStatus?.status === "VALID"
                      ? "Copy Expression to Clipboard"
                      : ""
                  }
                >
                  <div
                    className={`p-2 copyBox transition-all pointer-events-auto border border-solid border-ever-base-400 rounded-e-lg flex items-center justify-center mb-2  py-1 rounded-s-none group-hover:border-ever-primary  border-l-0  bg-ever-base-100    ${
                      validationStatus?.status === "VALID"
                        ? "hover:border-ever-primary cursor-pointer"
                        : "cursor-not-allowed"
                    } ${showAutocomplete && "border-ever-primary"}`}
                    data-testid="copy-button"
                  >
                    <CopyIcon
                      className={`w-5 h-5 text-ever-base-content-mid ${
                        validationStatus?.status === "VALID" &&
                        "hover:text-ever-base-content"
                      }`}
                    />
                  </div>
                </EverTooltip>
              </CopyToClipboard>
            )}
          </div>
          {showAutocomplete && expression.length < maxTokens && (
            <div
              className={twMerge(
                "w-full z-10 p-0 overflow-hidden -translate-y-4 opacity-0 origin-top transition-all duration-300",
                showAutocomplete && "translate-y-0 opacity-1"
              )}
              ref={refs.setFloating}
              style={floatingStyles}
            >
              <AutoSuggestionView
                editPosition={editPosition}
                ref={autoSuggestRef}
                autoSuggestions={autoSuggestions}
                selectedSuggestionIndex={selectedSuggestionIndex}
                handleSuggestionUserInputs={handleSuggestionUserInputs}
                fullAutoSuggestionList={fullAutoSuggestionList}
                expressionCardRef={expressionCardRef}
                validationStatus={validationStatus}
                expression={expression}
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
}

/**
 *
 * @param {Object} obj - The object containing the function token
 * @returns {String} - The v1 name of the function
 */
function _functionDisplayName(name) {
  switch (name) {
    case EXPRESSION_FUNCTION_TYPES.Hierarchy: {
      return "GenerateHierarchy";
    }
    case EXPRESSION_FUNCTION_TYPES.Rolling: {
      return "RollingSum";
    }
    case EXPRESSION_FUNCTION_TYPES.GetValueFromHierarchy: {
      return "GetValueFromHierarchy";
    }
    case EXPRESSION_FUNCTION_TYPES.Timezone: {
      return "ConvertTimezone";
    }
    default: {
      return name;
    }
  }
}
function _createV1Name(obj) {
  const currentFunctionToken = prepareFunctionToken(obj);
  const functionName = _functionDisplayName(
    currentFunctionToken.token.functionName
  );
  return `${functionName}(${currentFunctionToken.token.displayArgs
    .filter((x) => (!Array.isArray(x) && x) || x?.length > 0)
    .map(
      (arg) => arg?.token?.name ?? handleNoLabelArg(arg, currentFunctionToken)
    )
    .join(", ")})`;
}
