import { includes } from "lodash";
import React, { useEffect } from "react";

import { DATATYPE } from "~/Enums";
import { EverSelect } from "~/v2/components";

import { CommonFunctionHeader } from "./helpers/CommonFunctionHeader";
import { ExpressionBoxSelectbox } from "./helpers/ExpressionBoxSelectbox";
import { isDataSheetVariable } from "../utils";

const Option = EverSelect.Option;

/**
 * LastDateFunctionManager is a React component for managing the arguments of a "Last Date" function.
 * It allows users to configure and select various function arguments, such as date columns, units, and year types.
 *
 * @param {Object} props - The component's props
 * @param {Array} props.functionArgs - The list of function arguments
 * @param {Function} props.setFunctionArgs - A function to set the function arguments
 * @param {Object} props.functionContent - Metadata for the currently selected function
 * @param {Array} props.options -Autocomplete suggestions for function arguments.
 * @param {Function} props.setDisableApply - A function to set the "disable apply" state
 * @returns {JSX.Element} - A React JSX element representing the component
 */

export const LastDateFunctionManager = ({
  functionArgs,
  setFunctionArgs,
  functionContent,
  options,
  setDisableApply,
}) => {
  useEffect(() => {
    // Use useEffect to update the "disable apply" state based on function argument values
    if (functionArgs[0] && functionArgs[1]) {
      setDisableApply(false);
    } else {
      setDisableApply(true);
    }
  }, [functionArgs, setDisableApply]);

  // Initialize function arguments if empty
  useEffect(() => {
    if (functionArgs.length === 0) {
      setFunctionArgs([null, null, "Fiscal"]);
    }
  }, []);

  // filter all the date columns
  const filteredOptions = options.filter(
    (o) =>
      // filter the date type columns
      o.dataType === DATATYPE.DATE && isDataSheetVariable(o.token?.tokenType)
  );

  return (
    <CommonFunctionHeader
      functionName={functionContent.value?.token.functionName}
    >
      <ExpressionBoxSelectbox
        showSearch
        allowClear
        className="w-48"
        dropdownMatchSelectWidth
        options={filteredOptions}
        onChange={(_, option) => {
          setFunctionArgs([option?.token, functionArgs[1], functionArgs[2]]);
        }}
        value={functionArgs[0]?.token?.name}
        placeholder="Select Date Column"
      />
      {/* Select Unit */}
      <EverSelect
        showSearch
        allowClear
        className="w-48"
        value={functionArgs[1]}
        placeholder="Select Unit"
        onChange={(value) => {
          setFunctionArgs([functionArgs[0], value, functionArgs[2]]);
        }}
      >
        <Option value="Month">Month</Option>
        <Option value="Quarter">Quarter</Option>
        <Option value="Halfyear">Halfyear</Option>
        <Option value="Year">Year</Option>
      </EverSelect>
      {/* Select Year Type based on Unit */}
      {includes(["Quarter", "Halfyear", "Year"], functionArgs[1]) && (
        <EverSelect
          showSearch
          allowClear
          className="w-48"
          value={functionArgs[2]}
          onChange={(value) => {
            setFunctionArgs([functionArgs[0], functionArgs[1], value]);
          }}
          placeholder="Select year type"
        >
          <Option value="Fiscal">FISCAL</Option>
          <Option value="Calendar">CALENDAR</Option>
        </EverSelect>
      )}
    </CommonFunctionHeader>
  );
};
