import React from "react";

import { EverSelect, EverTg } from "~/v2/components";

export function ExpressionBoxSelectbox({ options, ...rest }) {
  return (
    <EverSelect allowClear {...rest}>
      {options?.map((option) => {
        const path = option?.token?.token?.path;
        const sourceNameHistoryArray = path?.split("<<") ?? [];

        return (
          <EverSelect.Option
            key={option.value}
            value={option.value}
            title={option.label}
            {...option}
          >
            {path ? (
              <div className="flex flex-col ">
                <div className="truncate">
                  <EverTg.Text className="truncate">{option.label}</EverTg.Text>
                </div>
                {sourceNameHistoryArray.length > 1 && (
                  <div className="whitespace-normal">
                    {sourceNameHistoryArray?.slice(1)?.map((item, index) => (
                      <>
                        {index !== 0 && (
                          <EverTg.Caption className="text-ever-base-content-mid  inline-block mr-1">
                            &lt;
                          </EverTg.Caption>
                        )}
                        <EverTg.Caption className="text-ever-base-content-mid inline-block mr-1">
                          {item}
                        </EverTg.Caption>
                      </>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              option.label
            )}
          </EverSelect.Option>
        );
      })}
    </EverSelect>
  );
}
