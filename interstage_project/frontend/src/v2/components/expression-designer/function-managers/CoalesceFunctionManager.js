import { cloneDeep, isEmpty } from "lodash";
import React, { useEffect, useState, useRef } from "react";

import { COALESCE_DATATYPES, DATATYPE, EXPRESSION_TOKEN_TYPES } from "~/Enums";
import { EverTg, EverSelect } from "~/v2/components";

import { ExpressionBoxController } from "../ExpressionBoxController";
import { constantIdentifier } from "../utils";

/**
 * returns the conditional function manager component
 *  * @param {Object} props
 * @param {Array} props.functionArgs - This holds the list of arguments for the currently edited function.It is added to AST.
 * @param {Function} props.setFunctionArgs - The function to set the function arguments
 * @param {Function} props.setDisableApply -A function to disable/enabled the apply button.
 * @param {Array} props.fullAutoSuggestionList - The complete array of suggestions obtained from the autocomplete response API.
 * @param {Object} props.functionContent - This holds the meta data for the currently selected function.
 * @param {Function} props.setFunctionContent - The function to set the function content
 * @returns {JSX.Element}
 */
export function CoalesceFunctionManager({
  functionArgs,
  setFunctionArgs,
  setDisableApply,
  fullAutoSuggestionList,
  functionContent,
  setFunctionContent,
}) {
  const expressionBoxRef = useRef(null);
  const [initialExpression, setInitialExpression] = useState([]);
  const [filteredOptions, setFilteredOptions] = useState([]);

  useEffect(() => {
    if (isEmpty(functionContent?.value?.token?.dataType)) {
      setFunctionContent((prev) => {
        const newPrev = cloneDeep(prev);
        newPrev.value.token.dataType = DATATYPE.STRING;
        return newPrev;
      });
      setDisableApply(true);
    }
    if (isEmpty(initialExpression) && !isEmpty(functionArgs)) {
      updateExpression(functionArgs);
    }
  }, [
    functionContent,
    functionArgs,
    setDisableApply,
    initialExpression,
    setFunctionContent,
  ]);

  useEffect(() => {
    if (!isEmpty(functionContent?.value?.token?.dataType)) {
      setFilteredOptions(
        fullAutoSuggestionList.filter(
          (o) =>
            [EXPRESSION_TOKEN_TYPES.DATASHEET_VARIABLES].includes(o.group) &&
            o.dataType !== functionContent?.value?.token?.dataType
        )
      );
    }
  }, [fullAutoSuggestionList, functionContent]);

  function updateExpression(expression) {
    setInitialExpression(expression);
  }

  const onDataTypeChange = (value) => {
    setDisableApply(true);
    setFunctionContent((prev) => {
      const newPrev = cloneDeep(prev);
      newPrev.value.token.dataType = value;
      return newPrev;
    });
    setFunctionArgs([]);
    updateExpression([]);
    expressionBoxRef.current?.resetExpression();
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-3 items-center">
        <EverTg.SubHeading4 className="text-ever-base-content">
          Data Type:
        </EverTg.SubHeading4>
        <EverSelect
          className={"w-40 h-auto"}
          filterOption
          allowClear
          options={COALESCE_DATATYPES}
          value={functionContent?.value?.token?.dataType}
          onChange={onDataTypeChange}
        />
      </div>
      {functionContent?.value?.token?.dataType && (
        <div className="flex gap-1 flex-col items-start">
          <div className="flex gap-3 items-center">
            <div>{`Coalesce (`}</div>
            <ExpressionBoxController
              expressionBoxRef={expressionBoxRef}
              fullAutoSuggestionList={filteredOptions}
              autoCompleteResponse={filteredOptions}
              className="w-96 h-auto"
              onChange={(value) => {
                if (value.length > 0) {
                  setDisableApply(false);
                } else {
                  setDisableApply(true);
                }
                setFunctionArgs(value);
                updateExpression(value);
              }}
              initialExpression={initialExpression}
              autocompleteSuggester={(params) => {
                return _customAutocompleteSuggester({
                  ...params,
                  dataType: functionContent.value.token.dataType,
                });
              }}
              copyButtonVisibility={false}
              status={{
                status: "INITIAL",
                message: "",
              }}
            />
            <div>{`)`}</div>
          </div>
        </div>
      )}
    </div>
  );
}

function _customAutocompleteSuggester({
  expression,
  position,
  isEditing = false,
  search = "",
  autoCompleteContext,
  dataType,
}) {
  search = search.trim();
  let filteredSuggestions = [];

  // In editing mode, the suggestions should be filtered based on the token type of the token at the current position
  if (isEditing) {
    filteredSuggestions = autoCompleteContext.filter(
      (item) =>
        item.group === expression[position]?.tokenType &&
        item.meta.dataType === dataType
    );

    if (search) {
      filteredSuggestions = filteredSuggestions.filter((item) =>
        item.label.toLowerCase().includes(search.toLowerCase())
      );
    }
  } else {
    // remove the hierarch and percentage variables from suggestions
    filteredSuggestions = autoCompleteContext.filter(
      (item) =>
        [EXPRESSION_TOKEN_TYPES.DATASHEET_VARIABLES].includes(item.group) &&
        item?.meta?.dataType === dataType
    );

    if (search) {
      // filtering the suggestions based on the search string
      filteredSuggestions = filteredSuggestions.filter((item) =>
        item.label.toLowerCase().includes(search.toLowerCase())
      );
      // adding only the string constant suggestions
      const constantSuggestions = ["(", ")"].includes(search)
        ? []
        : constantIdentifier(search).filter(
            (x) => x.meta.dataType === dataType
          );
      filteredSuggestions = [...filteredSuggestions, ...constantSuggestions];
    }
  }

  // filtering the suggestions based on the expression systemNames
  // if the expression has the systemName, then remove it from the suggestions
  const expressionSystemNames = new Set(expression.map((x) => x.token?.key));
  filteredSuggestions = filteredSuggestions.filter(
    (item) => !expressionSystemNames.has(item.value)
  );

  return filteredSuggestions;
}
