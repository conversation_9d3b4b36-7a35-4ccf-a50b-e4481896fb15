import { useQuery, gql } from "@apollo/client";
import { Empty } from "antd";
import { cloneDeep, isEmpty } from "lodash";
import React, { useEffect, useMemo } from "react";

import { DATATYPE } from "~/Enums";
import { EverSelect, EverLoader } from "~/v2/components";

import { CommonFunctionHeader } from "./helpers/CommonFunctionHeader";
import { ExpressionBoxSelectbox } from "./helpers/ExpressionBoxSelectbox";
import { isDataSheetVariable } from "../utils";

// Define a GraphQL query to fetch timezone data
const GET_TIMEZONE_LIST = gql`
  query AllNotificationTimezones {
    allNotificationTimezones
  }
`;

/**
 * TimezoneFunctionManager is a React component for managing the arguments of a specific function.
 * It allows users to configure and select various function arguments, including date columns,
 * source timezone, and destination timezone for timezone conversions.
 *
 * @param {Object} props - The component's props
 * @param {Array} props.functionArgs - The list of function arguments
 * @param {Function} props.setFunctionArgs - A function to set the function arguments
 * @param {Object} props.functionContent - Meta data for the currently selected function
 * @param {Function} props.setFunctionContent - A function to set the function content
 * @param {Array} props.options - An array of suggestions for function arguments
 * @param {Function} props.setDisableApply - a function to disable/enabled the apply button.
 * @returns {JSX.Element} - A React JSX element representing the component
 */

export const TimezoneFunctionManager = ({
  functionArgs,
  setFunctionArgs,
  functionContent,
  setFunctionContent,
  options,
  setDisableApply,
}) => {
  // Use the Apollo Client's useQuery hook to fetch timezone data
  const { loading: tzLoading, data: tzData } = useQuery(GET_TIMEZONE_LIST, {
    fetchPolicy: "cache-first",
  });
  // Extract timezone options and labels from the fetched data
  const { timezones, timezoneToLabel } = useMemo(() => {
    if (isEmpty(tzData)) return { timezones: [], timezoneToLabel: {} };

    const timezoneItems = tzData.allNotificationTimezones.map((item) =>
      JSON.parse(item)
    );

    const timezoneMap = {};
    for (const timezone of timezoneItems) {
      timezoneMap[timezone.value] = timezone.label;
    }

    return { timezones: timezoneItems, timezoneToLabel: timezoneMap };
  }, [tzData]);
  useEffect(() => {
    // Enable or disable the "apply" button based on the function arguments
    if (functionArgs[0] && functionArgs[1] && functionArgs[2]) {
      const clonedFunctionContent = cloneDeep(functionContent);
      clonedFunctionContent.timezoneToLabel = timezoneToLabel;
      setFunctionContent(clonedFunctionContent);
      setDisableApply(false);
    } else {
      setDisableApply(true);
    }
  }, [functionArgs, setDisableApply]);

  useEffect(() => {
    if (functionArgs.length === 0) {
      // Initialize function arguments with default values
      setFunctionArgs([null, "UTC", null]);
    }
  }, []);

  // Filter and display all available date columns as options
  const filteredOptions = options.filter(
    (o) =>
      // Filter the date type columns
      o.dataType === DATATYPE.DATE && isDataSheetVariable(o.token?.tokenType)
  );

  return (
    <CommonFunctionHeader
      functionName={functionContent.value?.token.functionName}
    >
      <ExpressionBoxSelectbox
        showSearch
        allowClear
        className="w-48"
        dropdownMatchSelectWidth
        options={filteredOptions}
        onChange={(_, option) => {
          setFunctionArgs([option?.token, functionArgs[1], functionArgs[2]]);
        }}
        value={functionArgs[0]?.token?.name}
        placeholder="Select Date Column"
      />
      <EverSelect
        className={"w-60"}
        showArrow
        showSearch
        allowClear
        placeholder="From Timezone"
        value={functionArgs[1]}
        options={timezones}
        notFoundContent={
          tzLoading ? (
            <div className="w-full h-full flex items-center">
              <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
              Variables
            </div>
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )
        }
        onChange={(value) => {
          const clonedArgs = cloneDeep(functionArgs);
          clonedArgs[1] = value;
          setFunctionArgs(clonedArgs);
        }}
        filterOption={(input, option) =>
          option.label.toLowerCase().includes(input.toLowerCase())
        }
      />
      <EverSelect
        className={"w-60"}
        showArrow
        showSearch
        allowClear
        placeholder="To Timezone"
        value={functionArgs[2]}
        options={timezones}
        notFoundContent={
          tzLoading ? (
            <div className="w-full h-full flex items-center">
              <EverLoader.SpinnerLottie className="w-10 h-10" /> Loading
              Variables
            </div>
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )
        }
        onChange={(value) => {
          const clonedArgs = cloneDeep(functionArgs);
          clonedArgs[2] = value;
          setFunctionArgs(clonedArgs);
        }}
        filterOption={(input, option) =>
          option.label.toLowerCase().includes(input.toLowerCase())
        }
      />
    </CommonFunctionHeader>
  );
};
