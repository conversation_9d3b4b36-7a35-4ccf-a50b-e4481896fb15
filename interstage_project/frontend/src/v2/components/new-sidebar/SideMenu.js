import { ChevronRightIcon } from "@everstage/evericons/outlined";
import { compact } from "lodash";
import { observer } from "mobx-react";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useQuery as useReactQuery } from "react-query";
import { useLocation } from "react-router-dom";
import { useRecoilValue } from "recoil";
import { twMerge } from "tailwind-merge";
import resolveConfig from "tailwindcss/resolveConfig";

import { getApprovalConfig } from "~/Api/ApprovalWorkflowService";
import { EVERSIDER_PAGE_NAMES, RBAC_ROLES } from "~/Enums";
import { myClientAtom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { useEmployeeStore } from "~/GlobalStores/EmployeeStore";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import { getClientFeatures } from "~/Utils/ClientFeaturesUtils";
import { useModules } from "~/v2/hooks";
import { everstageLogoWhite2022 } from "~/v2/images";
import { config } from "~/v2/twConfig";

import { CustomDropdown } from "./CustomDropdown";
import { getEverMenuElements, getCpqMenuElements } from "./menu-content";
import { RouteButton } from "./RouteButton";
import { sidebarMenuIcons, getMenuList, isRoute } from "./utils";
import { EverTooltip } from "../EverTooltip";

export const SidebarV3 = observer(() => {
  const fullConfig = resolveConfig(config);
  const breakpoint2xl = Number(
    fullConfig.theme.screens["2xl"].replace("px", "")
  );
  const { hasPermissions } = useUserPermissionStore();
  const myClient = useRecoilValue(myClientAtom);
  const clientFeatures = getClientFeatures(myClient);
  const location = useLocation();

  const {
    showContracts,
    isRevenueRole,
    pendingRequestsCount,
    canUserManageAdmins,
    pendingCommAdjCount,
    pendingPlanApprovalRequestCount,
  } = useEmployeeStore();
  const modules = useModules();

  const { t } = useTranslation();
  const { accessToken } = useAuthStore();
  const [primaryMenuElements, setPrimaryMenuElements] = useState([]);

  const menuItemRefs = useRef([]);

  const [showApprovalFeature, setShowApprovalFeature] = useState(false);
  const [expandedSubMenuIndex, setExpandedSubMenuIndex] = useState([]);
  const [pinnedSidebar, setPinnedSidebar] = useState(
    localStorage.getItem("isSidebarPinned") === "true"
  );
  const [expandSidebar, setExpandSidebar] = useState(false);
  const [isScrollable, setIsScrollable] = useState(false);
  const [scrolledToTop, setScrolledToTop] = useState(false);
  const [scrolledToBottom, setScrolledToBottom] = useState(false);
  const [activeIndicatorTopPosition, setActiveIndicatorTopPosition] =
    useState(0);

  const sideBarContainerRef = useRef(null);
  const sidebarScrollDiv = useRef(null);
  const sidebarScrollDivInner = useRef(null);

  const { data: approvalConfigData } = useReactQuery(
    ["getApprovalConfig"],
    () => getApprovalConfig(accessToken),
    {
      retry: false,
      cacheTime: 0,
      refetchOnWindowFocus: false,
    }
  );

  const subMenuList = primaryMenuElements.filter(
    (ele) => ele.type === "submenu"
  );

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;
    if (approvalConfigData?.status === "SUCCESS") {
      const config = approvalConfigData?.data || {};
      setShowApprovalFeature(
        config?.payoutApprovals?.enabled ||
          config?.commissionAdjustmentApprovals?.enabled ||
          false
      );
    }

    sideBarContainerRef.current.addEventListener(
      "mouseenter",
      () => {
        setExpandSidebar(true);
      },
      {
        signal,
      }
    );
    sideBarContainerRef.current.addEventListener(
      "mouseleave",
      () => {
        setExpandSidebar(false);
        const selectedSubmenu = subMenuList.find((ele) => {
          return ele.items.some((item) =>
            isRoute(location.pathname, item.path)
          );
        });
        if (selectedSubmenu?.name) {
          setExpandedSubMenuIndex([selectedSubmenu.name]);
        } else {
          setExpandedSubMenuIndex([]);
        }
      },
      {
        signal,
      }
    );
    return () => {
      controller.abort();
    };
  }, [approvalConfigData, subMenuList, location.pathname]);

  // Common dependencies for useEffect hooks
  const menuDependencies = [
    isRevenueRole,
    showApprovalFeature,
    approvalConfigData,
    showContracts,
    clientFeatures.loading,
  ];

  // Effect to add and clean up the resize event listener for filtering menu elements
  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;
    window.addEventListener("resize", filterMenuElements, { signal });
    // set timeout makes sure that the dom is rendered with updated data
    setTimeout(() => {
      filterMenuElements();
    }, 100);
    return () => {
      controller.abort();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, menuDependencies);

  // if the window is resized to less than 2xl, then unpin the sidebar
  useEffect(() => {
    function unpinSidebarIfLowWidth() {
      if (window.innerWidth < breakpoint2xl) {
        setPinnedSidebar(false);
        localStorage.setItem("isSidebarPinned", "false");
      }
    }
    unpinSidebarIfLowWidth();
    window.addEventListener("resize", unpinSidebarIfLowWidth);
    return () => {
      window.removeEventListener("resize", unpinSidebarIfLowWidth);
    };
  }, []);

  function setIsTopOrBottomAttained() {
    setScrolledToBottom(
      sidebarScrollDiv.current?.scrollTop ===
        sidebarScrollDiv.current?.scrollHeight -
          sidebarScrollDiv.current?.offsetHeight
    );
    setScrolledToTop(sidebarScrollDiv.current?.scrollTop === 0);
  }

  function setIsScrollableOrNot() {
    setIsScrollable(
      sidebarScrollDivInner.current?.clientHeight >
        sidebarScrollDiv.current?.clientHeight
    );
  }

  useEffect(() => {
    const sidebarScrollDivInnerDom = sidebarScrollDivInner.current;
    const controller = new AbortController();
    const signal = controller.signal;

    setTimeout(() => {
      // wait for 300ms to expand animation to complete
      setIsScrollableOrNot();
      setIsTopOrBottomAttained();
    }, 300);

    // on scroll check if the top or bottom is attained
    sidebarScrollDiv.current.addEventListener(
      "scroll",
      () => {
        setIsTopOrBottomAttained();
      },
      { signal }
    );

    // on resize (or sidemenu height change due to opening of a submenu) check if the sidebar is scrollable and if the top or bottom is attained
    const resizeObserver = new ResizeObserver(() => {
      setIsScrollableOrNot();
      setIsTopOrBottomAttained();
    });
    resizeObserver.observe(sidebarScrollDivInnerDom);

    return () => {
      controller.abort();
      resizeObserver?.unobserve(sidebarScrollDivInnerDom);
    };
  }, [expandSidebar]);

  const filterMenuElements = () => {
    // Determine the menu items to display based on the current module.
    // If the CPQ module is active, use CPQ-specific menu elements.
    // Otherwise, use the default Ever menu elements.
    const menuItems = modules.isCPQ
      ? getCpqMenuElements()
      : getEverMenuElements(t);

    // following paraments passed to this function
    // 1) menuList
    // 2) RBAC hasPermission fn
    // 3) additional conditions
    const filteredMenuElements = getMenuList(menuItems, hasPermissions, {
      [EVERSIDER_PAGE_NAMES.FORECAST]: () => clientFeatures?.showForecast,
      [EVERSIDER_PAGE_NAMES.APPROVALS]: () =>
        clientFeatures.showApprovalFeature && showApprovalFeature,
      [EVERSIDER_PAGE_NAMES.CONTRACTS]: () => showContracts === true,
      [EVERSIDER_PAGE_NAMES.KPI]: () =>
        clientFeatures?.showMetrics && canUserManageAdmins,
      [EVERSIDER_PAGE_NAMES.COMMISSION_ADJUSTMENT_APPROVALS]: () =>
        approvalConfigData?.data?.commissionAdjustmentApprovals?.enabled,
      [EVERSIDER_PAGE_NAMES.PAYOUT_APPROVALS]: () =>
        approvalConfigData?.data?.payoutApprovals?.enabled,
      [EVERSIDER_PAGE_NAMES.CRYSTAL]: () =>
        hasPermissions(RBAC_ROLES.MANAGE_CRYSTAL) || isRevenueRole,
      [EVERSIDER_PAGE_NAMES.DATASHEET]: () => clientFeatures.showDataSourcesV2,
      [EVERSIDER_PAGE_NAMES.DATABOOK]: () => !clientFeatures.showDataSourcesV2,
      [EVERSIDER_PAGE_NAMES.TERRITORY_PLANS]: () =>
        clientFeatures?.showTerritoryPlan,
    });

    setPrimaryMenuElements(filteredMenuElements);
  };

  /**
   * Effect hook to handle menu elements based on maximum container height and visibility.
   *
   * @param {Object} sideBarContainerRef - React ref for the sidebar container.
   * @param {Array} primaryMenuElements - Array of menu elements.
   */

  const badgeList = {
    Approvals: {
      count: pendingRequestsCount + pendingCommAdjCount,
      title: "Total pending requests",
    },
    payoutApprovals: {
      count: pendingRequestsCount,
      title: "Pending Requests",
    },
    commissionAdjApprovals: {
      count: pendingCommAdjCount,
      title: "Pending Commission adjustments Requests",
    },
    Commission: {
      count: pendingPlanApprovalRequestCount,
      title: `Pending ${t("COMMISSION")} Plan Requests`,
    },
  };

  function pinAndUnpinSidebar() {
    setPinnedSidebar((oldValue) => {
      const newValue = !oldValue;
      localStorage.setItem("isSidebarPinned", newValue.toString());
      return newValue;
    });
  }

  return (
    <div
      className={twMerge(
        "group transition-all duration-300 grow-0 shrink-0 flex flex-col z-10 relative",
        pinnedSidebar ? "w-52" : "w-16"
      )}
    >
      <div
        className={twMerge(
          "menu-container transition-all duration-300 absolute bg-ever-accent top-0 bottom-0 pt-4 pb-16",
          pinnedSidebar || expandSidebar ? "w-52" : "w-16"
        )}
        ref={sideBarContainerRef}
      >
        <div
          ref={sidebarScrollDiv}
          className="sidebar-scroll overflow-auto h-full"
        >
          <div
            ref={sidebarScrollDivInner}
            className="flex flex-col gap-2 grow px-4 relative pt-0.5"
          >
            <div
              className="w-1 h-9 rounded-r-xl absolute left-0 bg-ever-base transition-all ease-in-out transition-duration-300"
              style={{
                transform: `translate3d(0, ${
                  activeIndicatorTopPosition - 4
                }px, 0)`,
              }}
            ></div>
            {primaryMenuElements.map((ele, index) => {
              let returnItem = null;
              if (ele.type == "routingItem") {
                returnItem = (
                  <RouteButton
                    setActiveIndicatorTopPosition={
                      setActiveIndicatorTopPosition
                    }
                    parentRef={sidebarScrollDivInner}
                    key={ele.name}
                    data={ele}
                    icon={sidebarMenuIcons[ele.icon].main}
                    activeIcon={sidebarMenuIcons[ele.icon].active}
                    selected={sidebarMenuIcons[ele.icon].selected}
                    badgeList={badgeList}
                    expandSidebar={expandSidebar}
                    pinnedSidebar={pinnedSidebar}
                  />
                );
              } else if (ele.type == "submenu") {
                const submenuList = compact(
                  ele.items.map((item) => {
                    if (ele.label === EVERSIDER_PAGE_NAMES.APPROVALS) {
                      const config = approvalConfigData?.data;
                      if (
                        item.label === EVERSIDER_PAGE_NAMES.PAYOUT_APPROVALS
                      ) {
                        return config?.payoutApprovals?.enabled ? item : null;
                      }
                      if (
                        item.label ===
                        EVERSIDER_PAGE_NAMES.COMMISSION_ADJUSTMENT_APPROVALS
                      ) {
                        return config?.commissionAdjustmentApprovals?.enabled
                          ? item
                          : null;
                      }
                    } else {
                      const showSubMenu = hasPermissions(item?.rbacPermission);
                      if (showSubMenu) {
                        return item;
                      }
                      return null;
                    }
                  })
                );
                if (submenuList.length > 0) {
                  returnItem = (
                    <CustomDropdown
                      setActiveIndicatorTopPosition={
                        setActiveIndicatorTopPosition
                      }
                      parentRef={sidebarScrollDivInner}
                      index={ele.name}
                      expandedSubMenuIndex={expandedSubMenuIndex}
                      setExpandedSubMenuIndex={setExpandedSubMenuIndex}
                      key={ele.name}
                      icon={sidebarMenuIcons[ele.icon].main}
                      activeIcon={sidebarMenuIcons[ele.icon].active}
                      options={submenuList}
                      title={ele.name}
                      selected={sidebarMenuIcons[ele.icon].selected}
                      badgeList={badgeList}
                      data={ele}
                      expandSidebar={expandSidebar}
                      pinnedSidebar={pinnedSidebar}
                    />
                  );
                }
              }

              if (!returnItem) {
                return null;
              }

              return (
                <div
                  ref={(el) => (menuItemRefs.current[index] = el)}
                  key={`menu-item-${ele.label}`}
                >
                  {returnItem}
                </div>
              );
            })}
          </div>
        </div>
        {isScrollable && !scrolledToBottom && (
          <div className="h-20 w-full bottom-16 absolute left-0 bg-gradient-to-t from-ever-accent to-transparent z-10 pointer-events-none"></div>
        )}

        {isScrollable && !scrolledToTop && (
          <div className="h-20 w-full top-0 absolute left-0 bg-gradient-to-b from-ever-accent to-transparent z-10 pointer-events-none"></div>
        )}
        <div className="absolute left-0 bottom-0 w-full py-5 pl-5">
          <div
            className={twMerge(
              "overflow-hidden transition-all duration-300",
              expandSidebar || pinnedSidebar ? "w-32" : "w-6"
            )}
          >
            <img
              className="min-h-6 min-w-32"
              src={everstageLogoWhite2022}
              alt="logo"
            />
          </div>

          <EverTooltip title={pinnedSidebar ? "Unpin Sidebar" : "Pin Sidebar"}>
            <div
              onClick={pinAndUnpinSidebar}
              className={twMerge(
                "cursor-pointer hidden 2xl:flex size-6 items-center justify-center rounded border border-solid border-ever-base-400 absolute left-full -translate-x-1/2 top-1/2 -translate-y-1/2 bg-ever-base text-ever-base-content transition-all duration-300 shadow-xl group/pin-sidebar",
                expandSidebar || pinnedSidebar ? "opacity-100" : "opacity-0"
              )}
            >
              <ChevronRightIcon
                className={twMerge(
                  "size-4",
                  pinnedSidebar
                    ? "rotate-180"
                    : "group-hover/pin-sidebar:translate-x-0.5 transition-all"
                )}
              />
            </div>
          </EverTooltip>
        </div>
      </div>
    </div>
  );
});
