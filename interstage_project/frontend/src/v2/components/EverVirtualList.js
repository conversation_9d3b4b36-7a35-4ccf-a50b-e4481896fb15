import { useSpring, animated } from "@react-spring/web";
import { useVirtualizer } from "@tanstack/react-virtual";
import { Grid } from "antd";
import { debounce } from "lodash";
import { PropTypes } from "prop-types";
import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  memo,
  useContext,
  createContext,
  useMemo,
  useLayoutEffect,
} from "react";
const { useBreakpoint } = Grid;
import { twMerge } from "tailwind-merge";

import { EverLoader } from "~/v2/components";

const emptyArray = [];
const emptyRenderItem = () => <></>;
const getGridCount = (screenSizes) => {
  switch (true) {
    case screenSizes.xxl: {
      return 4;
    }
    case screenSizes.xl: {
      return 4;
    }
    case screenSizes.lg: {
      return 3;
    }
    case screenSizes.md: {
      return 2;
    }
    case screenSizes.sm: {
      return 1;
    }
    case screenSizes.xs: {
      return 1;
    }
  }
};

const AnimatedItemsContext = createContext();

function AnimatedItemsProvider({ children = null, initialLoad }) {
  const [animatedIndices, setAnimatedIndices] = useState(new Set());

  useEffect(() => {
    if (initialLoad) {
      setAnimatedIndices(new Set());
    }
  }, [initialLoad]);

  const memorizeIndices = useMemo(
    () => ({
      animatedIndices,
      setAnimatedIndices,
    }),
    [animatedIndices]
  );

  return (
    <AnimatedItemsContext.Provider value={memorizeIndices}>
      {children}
    </AnimatedItemsContext.Provider>
  );
}

const AnimatedElement = memo(({ children = null, animate, index }) => {
  const { animatedIndices, setAnimatedIndices } =
    useContext(AnimatedItemsContext);
  const hasAnimated = animatedIndices.has(index);

  const spring = useSpring({
    from: { opacity: hasAnimated ? 1 : 0, scale: hasAnimated ? 1 : 0.8 },
    to: { opacity: 1, scale: 1 },
    onRest: () => {
      if (!hasAnimated) {
        setAnimatedIndices((prev) => new Set([...prev, index]));
      }
    },
  });

  if (!animate || hasAnimated) {
    return <React.Fragment key={index}>{children}</React.Fragment>;
  }

  return (
    <animated.div className="opacity-0" style={spring}>
      {children}
    </animated.div>
  );
});

AnimatedElement.displayName = "AnimatedElement";

/**
 * This component generates a Responsive Virtual Infinite Scroll Grid/List. It works in tandem with useLazyComponent hook.
 *
 * @param {array} data - JSON data to render in RenderItem component.
 * @param {number} columns - (optional) Defaults to responsive grid if not specificed. To render a single list pass 'number' as 1.
 * @param {node} RenderItem - The JSX element the list needs to display as item in Grid/List.
 * @param {string} renderItemProps - (optional) A attribute to pass props to RenderItem.
 * @param {boolean} canFetchMore - Boolean value which decides whether new data can be fetched.
 * @param {function} fetchNextData - The function required to fetch the next data.
 * @param {boolean} isLoading - Boolean value to know if new data is being fetched or busy.
 * @param {string} listHeightClass - (optional) Provide a tailwind height classname. Can also provide classnames with 'calc'.
 * @param {boolean} initialLoad - Provide the initial loading prop from useLazyComponent for handling API request success/errors.
 * @param {string} messageNoData - Text to show when data is empty
 * @param {string} messageLastData - Text to show when when last row is reached.
 * @param {boolean} animate - Adds a entry animation for every item rendered.
 * @param {boolean} showSpinner - Shows a spinner when data is being fetched.
 * @param {number} itemHeight - (optional) Height of each item in pixels. Defaults to 170.
 * @returns The JSX for the Infinite Virtual Scroll List/Grid.
 */

export function EverVirtualizerInfinite({
  data = emptyArray,
  columns, //Do not assign default values to columns
  RenderItem = emptyRenderItem,
  renderItemProps,
  canFetchMore,
  fetchNextData,
  isLoading,
  listHeightClass,
  initialLoad,
  messageNoData = "No data is available",
  messageLastData = "You're all caught up 🎉",
  animate,
  showSpinner = true,
  itemHeight = 170,
}) {
  const parentRef = useRef(null);

  const [width, setWidth] = useState(0);

  const screens = useBreakpoint();

  const columnCount = columns ?? getGridCount(screens);
  const rowCount = Math.ceil(data.length / columnCount);

  const rowVirtualizer = useVirtualizer({
    count: canFetchMore ? rowCount + 1 : rowCount,
    estimateSize: () => itemHeight,
    getScrollElement: () => parentRef.current,
    overscan: 3,
  });

  const columnVirtualizer = useVirtualizer({
    horizontal: true,
    count: columnCount,
    getScrollElement: () => parentRef.current,
    estimateSize: useCallback(
      () => Math.ceil(width / columnCount),
      [width, columnCount]
    ),
    overscan: 0,
  });

  /* Adding last Row and Col into state to know when last item changed during frame  */
  const [lastRow, setLastRow] = useState(
    [...rowVirtualizer.getVirtualItems()].reverse().pop()
  );

  const [lastCol, setLastCol] = useState(
    [...columnVirtualizer.getVirtualItems()].pop()
  );

  /* If window size changed and columns changed  */
  useEffect(() => {
    const [lastItem] = [...rowVirtualizer.getVirtualItems()].reverse();
    setLastRow(lastItem);
  }, [rowVirtualizer.getVirtualItems()]);

  useEffect(() => {
    const [lastItem] = [...columnVirtualizer.getVirtualItems()].reverse();
    setLastCol(lastItem);
  }, [columnVirtualizer.getVirtualItems()]);

  useLayoutEffect(() => {
    /* TODO: In React 18 need to change this to useLayoutEffect for better performance */
    if (!parentRef.current) {
      return;
    }

    const resizeObserver = new ResizeObserver(
      debounce((entry) => {
        if (entry) {
          const { inlineSize: width } = entry[0].borderBoxSize[0];
          setWidth(width);
        }
      }),
      200
    );

    resizeObserver.observe(parentRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [parentRef]);

  useEffect(() => {
    columnVirtualizer.measure();
  }, [width, columnVirtualizer]);

  useEffect(() => {
    if (!lastRow || !lastCol) {
      return;
    }

    /* Getting exact last index in grid layout */
    const lastVisibleItemIndex = lastRow.index * columnCount + lastCol.index;

    if (
      lastVisibleItemIndex >= data.length - columnCount &&
      canFetchMore &&
      !isLoading
    ) {
      fetchNextData();
    }
  }, [canFetchMore, fetchNextData, isLoading, data.length, lastRow, lastCol]);

  return (
    <AnimatedItemsProvider initialLoad={initialLoad}>
      {!initialLoad && RenderItem ? (
        <div
          className={twMerge(
            "overflow-y-auto overflow-x-hidden",
            listHeightClass ?? "h-full"
          )}
          ref={parentRef}
        >
          <div className="w-full h-full">
            <div
              className="relative w-full"
              style={{
                height: `${rowVirtualizer.getTotalSize()}px`,
              }}
            >
              {rowVirtualizer.getVirtualItems().map((virtualRow) => {
                const isLoaderRow =
                  virtualRow.index > Math.ceil(data.length / columnCount) - 1;
                return columnVirtualizer
                  .getVirtualItems()
                  .map((virtualColumn) => {
                    const index =
                      virtualColumn.index + virtualRow.index * columnCount;
                    const item = data[index];
                    const styler = {
                      width: `${virtualColumn.size}px`,
                      height: `${virtualRow.size}px`,
                      transform: `translateX(${virtualColumn.start}px) translateY(${virtualRow.start}px)`,
                    };
                    return (
                      <div
                        className="absolute top-0 left-0 grid"
                        key={index}
                        style={styler}
                      >
                        {!isLoaderRow && item ? (
                          <AnimatedElement
                            animate={animate}
                            index={index}
                            key={index}
                          >
                            <RenderItem item={item} {...renderItemProps} />
                          </AnimatedElement>
                        ) : null}
                      </div>
                    );
                  });
              })}
            </div>
            {data.length === 0 ? (
              <div className="flex h-full w-full items-center justify-center">
                {messageNoData}
              </div>
            ) : null}
            {!canFetchMore && data.length > 0 ? (
              <div className="mt-2 flex justify-center">{messageLastData}</div>
            ) : null}
            {isLoading && showSpinner ? (
              <div className="flex justify-center">
                <EverLoader.SpinnerLottie className="w-12 h-12" />
              </div>
            ) : null}
          </div>
        </div>
      ) : null}
    </AnimatedItemsProvider>
  );
}

EverVirtualizerInfinite.propTypes = {
  /** This can be used to set Element to be displayed */
  RenderItem: PropTypes.node,
  /** This prop can be used to show animation for newely rendered items in the list */
  animate: PropTypes.bool,
  /** This boolean is used to trigger fetch if next data is available */
  canFetchMore: PropTypes.bool,
  /** This can be used to set the number of columns to be rendered */
  columns: PropTypes.number,
  /** This can be used to set the data for rendering the Grid/List */
  data: PropTypes.array,
  /** This function will be called when user reaches end of list or satisfies overscan rows */
  fetchNextData: PropTypes.func,
  /** This prop can be to determine initial api response to render the Virtual grid/list */
  initialLoad: PropTypes.bool,
  /** This boolean is used check if api request is still fetching */
  isLoading: PropTypes.bool,
  /** This prop is used to set the height of each item in the virtual list (default: 170) */
  itemHeight: PropTypes.number,
  /** This prop is used to set the height of virtual grid/list view  */
  listHeightClass: PropTypes.string,
  /** This prop is used to display text when reached end of data */
  messageLastData: PropTypes.string,
  /** This prop is used to display text when data is available */
  messageNoData: PropTypes.string,
  /** This can be used to set props for the Element that is getting displayed */
  renderItemProps: PropTypes.object,
  /** This prop is used to display spinner when data is being fetched */
  showSpinner: PropTypes.bool,
  /** This prop is used to display a inner/inset shadow when scrolling */
  enableScrollShadow: PropTypes.bool,
};

/* This component is replacement for Ant-d's RC virtuallist */
export function EverVirtualizerDynamic({
  data,
  columns,
  getOption,
  itemHeight,
  onScroll,
  listHeightClass, //tailwind height class either in px,percentage or calc.
  height, //For Ant-D select list height is required
  enableScrollShadow,
}) {
  const parentRef = useRef(null);
  const [width, setWidth] = useState(0);
  const [scrolled, setScrolled] = useState(false);
  const scrollTimeoutRef = useRef(null);

  const screens = useBreakpoint();
  const columnCount = columns ?? getGridCount(screens);

  const rowCount = data.length;

  const rowVirtualizer = useVirtualizer({
    count: rowCount,
    estimateSize: () => itemHeight,
    getScrollElement: () => parentRef.current,
    overscan: 5,
  });

  const columnVirtualizer = useVirtualizer({
    horizontal: true,
    count: columnCount,
    getScrollElement: () => parentRef.current,
    estimateSize: useCallback(
      () => Math.ceil(width / columnCount),
      [width, columnCount]
    ),
    overscan: 0,
  });

  useLayoutEffect(() => {
    if (!parentRef.current) {
      return;
    }

    const resizeObserver = new ResizeObserver((entry) => {
      if (entry) {
        const { inlineSize: width } = entry[0].borderBoxSize[0];
        setWidth(width);
      }
    });

    resizeObserver.observe(parentRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [parentRef]);

  useEffect(() => {
    columnVirtualizer.measure();
  }, [width, columnVirtualizer]);

  const handleScroll = () => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    setScrolled(true);

    scrollTimeoutRef.current = setTimeout(() => {
      setScrolled(false);
    }, 500);
  };

  useEffect(() => {
    const listElement = parentRef.current;

    if (listElement) {
      listElement.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (listElement) {
        listElement.removeEventListener("scroll", handleScroll);
      }

      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div
      className={twMerge(
        "overflow-y-auto overflow-x-hidden",
        listHeightClass ?? "h-full",
        enableScrollShadow && scrolled
          ? "shadow-[inset_0px_10px_10px_-10px_rgba(0,0,0,0.3),inset_0px_-10px_10px_-10px_rgba(0,0,0,0.3)]"
          : ""
      )}
      onScroll={onScroll}
      ref={parentRef}
      style={!listHeightClass && height ? { height: height } : undefined}
    >
      <div
        className="relative w-full"
        style={{
          height: `calc(100% - ${rowVirtualizer.getTotalSize()}px)`,
        }}
      >
        {rowVirtualizer.getVirtualItems().map((virtualRow) => {
          const isLoaderRow =
            virtualRow.index > Math.ceil(data.length / columnCount) - 1;

          return columnVirtualizer.getVirtualItems().map((virtualColumn) => {
            const index = virtualColumn.index + virtualRow.index * columnCount;
            const item = data[index];
            return (
              <div
                className="absolute top-0 left-0 grid"
                key={index}
                style={{
                  width: `${virtualColumn.size}px`,
                  height: `${virtualRow.size}px`,
                  transform: `translateX(${virtualColumn.start}px) translateY(${virtualRow.start}px)`,
                }}
              >
                {!isLoaderRow && item ? (
                  <React.Fragment key={index}>
                    {getOption(item, index, true)}
                  </React.Fragment>
                ) : null}
              </div>
            );
          });
        })}
      </div>
    </div>
  );
}
