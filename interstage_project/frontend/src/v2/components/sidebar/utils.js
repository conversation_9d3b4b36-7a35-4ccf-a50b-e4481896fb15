import {
  Dash<PERSON><PERSON>ottie,
  App<PERSON><PERSON>Lottie,
  <PERSON><PERSON>ottie,
  ContractsLottie,
  DatabookLottie,
  <PERSON>sLottie,
  <PERSON>Lottie,
  QueriesLottie,
  QuotasLottie,
  <PERSON>tingsLottie,
  StatementsLottie,
  <PERSON>ChartLottie,
  CrystalLottie,
  MagicWandLottie,
  TerritoryPlansLottie,
  TransformationLottie,
} from "@everstage/evericons/lotties";
import {
  PieChartIcon as PieChartIconOL,
  LayoutGridAltIcon as LayoutGridIconOL,
  PercentCircleIcon as PercentIconOL,
  MessageChatSquareIcon as MessageChatSquareIconOL,
  TargetIcon as TargetIconOL,
  EditPencilWavyIcon as EditIconOL,
  UsersFullIcon as UsersIconOL,
  SettingsIcon as SettingsIconOL,
  WalletIcon as WalletIconOL,
  DataflowIcon as DataflowIconOL,
  CrystalIcon as CrystalIconOL,
  FileIcon as FileIconOL,
  SearchMdIcon as SearchIconOL,
  MagicWandIcon as MagicWandIconOL,
  InboxIcon as InboxIconOL,
  TerritoryPlansIcon as TerritoryPlansIconOL,
} from "@everstage/evericons/outlined";
import {
  WalletIcon,
  TargetIcon,
  LayoutGridIcon,
  PieChartIcon,
  PercentIcon,
  MessageChatSquareIcon,
  EditIcon,
  UsersIcon,
  SettingsIcon,
  DataflowIcon,
  FileIcon,
  CrystalIcon,
  LineChartIcon,
  CrystalFlareIcon,
  MagicWandIcon,
  TransformationIcon,
  InboxIcon,
  TerritoryPlansIcon,
} from "@everstage/evericons/solid";
export const cpqMenuIcons = {
  quotes: {
    main: <TargetIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: QuotasLottie,
    selected: <TargetIcon className="text-ever-sidebar-base-content w-6 h-6" />,
  },
  catalog: {
    main: <InboxIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: StatementsLottie,
    selected: <InboxIcon className="text-ever-sidebar-base-content w-6 h-6" />,
  },
};

export const sidebarMenuIcons = {
  dashboards: {
    main: <PieChartIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: DashboardLottie,
    selected: (
      <PieChartIcon className="text-ever-sidebar-base-content w-6 h-6" />
    ),
  },
  everai: {
    main: <MagicWandIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: MagicWandLottie,
    selected: (
      <MagicWandIcon className="text-ever-sidebar-base-content w-6 h-6" />
    ),
  },
  databook: {
    main: <LayoutGridIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: DatabookLottie,
    selected: <LayoutGridIcon className="text-ever-sidebar-base-content" />,
  },
  commissions: {
    main: <PercentIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: CommissionLottie,
    selected: (
      <PercentIcon className="text-ever-sidebar-base-content w-6 h-6" />
    ),
  },
  queries: {
    main: (
      <MessageChatSquareIconOL className="text-ever-sidebar-icon w-6 h-6" />
    ),
    active: QueriesLottie,
    selected: (
      <MessageChatSquareIcon className="text-ever-sidebar-base-content w-6 h-6" />
    ),
  },
  territoryPlans: {
    main: <TerritoryPlansIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: TerritoryPlansLottie,
    selected: (
      <TerritoryPlansIcon className="text-ever-sidebar-base-content w-6 h-6" />
    ),
  },
  crystal: {
    main: <CrystalIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: CrystalLottie,
    selected: (
      <div className="flex relative">
        <CrystalIcon className="text-ever-sidebar-base-content w-6 h-6" />
        <CrystalFlareIcon className="text-ever-base w-6 h-6 absolute" />
      </div>
    ),
  },
  quotas: {
    main: <TargetIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: QuotasLottie,
    selected: <TargetIcon className="text-ever-sidebar-base-content w-6 h-6" />,
  },
  contracts: {
    main: <EditIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: ContractsLottie,
    selected: <EditIcon className="text-ever-sidebar-base-content" />,
  },
  people: {
    main: <UsersIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: PeopleLottie,
    selected: <UsersIcon className="text-ever-sidebar-base-content w-6 h-6" />,
  },
  settings: {
    main: <SettingsIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: SettingsLottie,
    selected: (
      <SettingsIcon className="text-ever-sidebar-base-content w-6 h-6" />
    ),
  },
  draws: {
    main: <WalletIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: DrawsLottie,
    selected: <WalletIcon className="text-ever-sidebar-base-content w-6 h-6" />,
  },
  approvals: {
    main: <DataflowIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: ApprovalsLottie,
    selected: (
      <DataflowIcon className="text-ever-sidebar-base-content w-6 h-6" />
    ),
  },
  statements: {
    main: <FileIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: StatementsLottie,
    selected: <FileIcon className="text-ever-sidebar-base-content w-6 h-6" />,
  },
  kpi: {
    main: (
      <LineChartIcon className="text-ever-sidebar-icon w-6 h-6 scale-150" />
    ),
    active: LineChartLottie,
    selected: (
      <LineChartIcon className="text-ever-sidebar-icon w-6 h-6 scale-150" />
    ),
  },
  transformation: {
    main: <TransformationIcon className="text-ever-sidebar-icon w-6 h-6" />,
    active: TransformationLottie,
    selected: (
      <TransformationIcon className="text-ever-sidebar-base-content w-6 h-6" />
    ),
  },
  search: {
    main: <SearchIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    active: () => <SearchIconOL className="text-ever-sidebar-icon w-6 h-6" />,
    selected: (
      <SearchIconOL className="text-ever-sidebar-base-content w-6 h-6" />
    ),
  },
  ...cpqMenuIcons,
};

export const getMenuList = (menuList, hasPermissions, extraConditions) => {
  const filteredMenu = [];
  // Helper function to check if an item is accessible based on client features and permissions
  const isItemAccessible = (item) => {
    let baseCondition =
      !item.rbacPermission || hasPermissions(item.rbacPermission);
    // Apply additional condition if it exists for this label
    if (extraConditions[item.label]) {
      baseCondition = baseCondition && extraConditions[item.label]();
    }
    return baseCondition;
  };

  for (const item of menuList) {
    if (isItemAccessible(item)) {
      if (item.type === "submenu" && Array.isArray(item.items)) {
        // Filter accessible submenu items
        const accessibleSubItems = item.items.filter(isItemAccessible);
        // Only add the submenu if it has accessible items
        if (accessibleSubItems.length > 0) {
          filteredMenu.push({
            ...item,
            items: accessibleSubItems,
          });
        }
      } else {
        // Add non-submenu item directly
        filteredMenu.push(item);
      }
    }
  }

  return filteredMenu;
};
