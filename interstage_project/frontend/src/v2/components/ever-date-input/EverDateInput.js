import { parse, isValid } from "date-fns";
import PropTypes from "prop-types";
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { twMerge } from "tailwind-merge";

// Default placeholder text for each input field
const DEFAULT_TEXT = {
  month: "MM",
  day: "DD",
  year: "YYYY",
};

const ZERO = "0"; // Used for formatting single-digit numbers

function validateDateString(dateString) {
  // Parse the date string according to the given format
  const parsedDate = parse(dateString, "MM/dd/yyyy", new Date());
  // Check if the parsed date is valid
  return isValid(parsedDate);
}

// Function to set the cursor to the end of the contenteditable div
const setCursorToEnd = (divRef) => {
  const element = divRef.current;
  if (!element) return;
  setTimeout(() => {
    const range = document.createRange();
    const selection = window.getSelection();
    range.selectNodeContents(element);
    range.collapse(false); // Move cursor to the end
    selection.removeAllRanges();
    selection.addRange(range);
  }, 100);
};

/**
 * EverDateInput is a component that allows the user to input a date in the format MM/DD/YYYY.
 * It uses the date-fns library to parse and validate the date string.
 * It also uses the tailwind-merge library to merge the className prop with the default styles.
 * It also uses the forwardRef function to forward the ref to the component.
 *
 */
export const EverDateInput = forwardRef(
  ({ value, onChange, handleCloseDatePicker }, ref) => {
    // References to the month, day, and year input fields
    const mmRef = useRef(null);
    const ddRef = useRef(null);
    const yyyyRef = useRef(null);
    const [currentMonth, setCurrentMonth] = useState(DEFAULT_TEXT.month);
    const [currentDay, setCurrentDay] = useState(DEFAULT_TEXT.day);
    const [currentYear, setCurrentYear] = useState(DEFAULT_TEXT.year);

    const getValues = useCallback(() => {
      const isValidDate = validateDateString(value);
      let month = DEFAULT_TEXT.month;
      let day = DEFAULT_TEXT.day;
      let year = DEFAULT_TEXT.year;
      if (isValidDate) {
        const splitDate = value.split("/");
        month = splitDate[0];
        day = splitDate[1];
        year = splitDate[2];
      }
      return { month, day, year };
    }, [value]);

    useEffect(() => {
      const { month, day, year } = getValues();
      if (month && day && year) {
        setCurrentMonth((prevMonth) => {
          // If the value hasn't changed, return the previous value
          if (prevMonth === month.toString()) {
            return prevMonth; // This prevents React from scheduling an update
          }
          // Otherwise return the new value
          return month.toString();
        });
        setCurrentDay((prevDay) => {
          if (prevDay === day.toString()) {
            return prevDay;
          }
          return day.toString();
        });
        setCurrentYear((prevYear) => {
          if (prevYear === year.toString()) {
            return prevYear;
          }
          return year.toString();
        });
      }
    }, [getValues]);

    useEffect(() => {
      if (
        currentMonth !== DEFAULT_TEXT.month &&
        currentDay !== DEFAULT_TEXT.day &&
        currentYear !== DEFAULT_TEXT.year &&
        currentMonth.length === 2 &&
        currentDay.length === 2 &&
        currentYear.length === 4
      ) {
        onChange && onChange(`${currentMonth}-${currentDay}-${currentYear}`);
      }
    }, [currentMonth, currentDay, currentYear, onChange]);

    // Update DOM elements when state changes
    useEffect(() => {
      if (mmRef.current) mmRef.current.textContent = currentMonth;
    }, [currentMonth]);

    useEffect(() => {
      if (ddRef.current) ddRef.current.textContent = currentDay;
    }, [currentDay]);

    useEffect(() => {
      if (yyyyRef.current) yyyyRef.current.textContent = currentYear;
    }, [currentYear]);

    // Handle key down events for the month input
    const handleMonthKeyDown = (e) => {
      console.log("handleMonthKeyDown", e.key);
      if (e.key === "Enter") {
        handleCloseDatePicker();
        return;
      }
      e.stopPropagation();
      e.preventDefault(); // Prevent default key press behavior
      const key = Number.parseInt(e.key); // Convert pressed key to a number
      const currentText = mmRef.current.textContent;

      // Update month text and focus on day input if needed
      const updateMonthText = (newText) => {
        setCurrentMonth(newText);
        if (Number.parseInt(newText) > 1) {
          ddRef.current.focus();
        }
      };

      if (!Number.isNaN(key)) {
        if (currentText === DEFAULT_TEXT.month) {
          if (key > 0) {
            updateMonthText(`${ZERO}${key}`); // Prepend zero for single-digit month
          }
        } else {
          const currentMonth = Number.parseInt(currentText);
          if (currentMonth === 1 && key <= 2) {
            updateMonthText(`1${key}`); // Handle transition from first to second digit
          } else if (key > 0) {
            updateMonthText(`${ZERO}${key}`); // Reset to new month if valid
          }
        }

        // Validate day based on selected month and year
        if (ddRef.current.textContent !== DEFAULT_TEXT.day) {
          let currentMonth = Number.parseInt(mmRef.current.textContent);
          if (currentMonth > 1) {
            currentMonth = key;
          } else {
            currentMonth =
              key <= 2 ? Number.parseInt(`1${key}`) : (currentMonth = key);
          }

          const selectedMonth = currentMonth;
          let maxDayForSelectedMonth = 31; // Default max days

          // Adjust max days for February
          if (selectedMonth === 2) {
            maxDayForSelectedMonth =
              Number.parseInt(yyyyRef.current.textContent) % 4 === 0 ? 29 : 28;
          } else if (
            (selectedMonth < 8 && selectedMonth % 2 === 0) ||
            (selectedMonth > 8 && selectedMonth % 2 !== 0)
          ) {
            maxDayForSelectedMonth = 30; // Adjust for months with 30 days
          }

          // Correct day if it exceeds max days for the month
          if (
            Number.parseInt(ddRef.current.textContent) > maxDayForSelectedMonth
          ) {
            setCurrentDay(maxDayForSelectedMonth?.toString());
          }
        }
      } else if (e.key === "Backspace" && currentText !== DEFAULT_TEXT.month) {
        // Handle backspace to remove digits or reset to default text
        setCurrentMonth(
          Number.parseInt(currentText) > 9
            ? `${ZERO}${currentText.slice(0, -1)}`
            : DEFAULT_TEXT.month
        );
        //mmRef.current.textContent;
      }
      // else if (e.key === "Tab" && currentText === DEFAULT_TEXT.month) {
      //   ddRef.current.focus();
      // }
    };

    // Handle key down events for the day input
    const handleDayKeyDown = (e) => {
      if (e.key === "Enter") {
        handleCloseDatePicker();
        return;
      }
      e.preventDefault(); // Prevent default key press behavior
      const key = Number.parseInt(e.key); // Convert pressed key to a number
      const currentText = ddRef.current.textContent;
      console.log("currentText", e.key);
      // Determine the maximum first digit for the day based on the month
      // if month is 02, then max first digit is 2, else 3
      const MAX_DAY_FIRST_DIGIT = currentMonth === "02" ? 2 : 3;
      let MAX_DAY_SECOND_DIGIT = 1; // Default maximum second digit for the day

      // Adjust maximum second digit for February and other months
      if (currentMonth !== "mm") {
        if (currentMonth === "02") {
          MAX_DAY_SECOND_DIGIT = yyyyRef.current.textContent % 4 === 0 ? 9 : 8; // Leap year check
        } else if (
          (Number.parseInt(currentMonth) < 8 &&
            Number.parseInt(currentMonth) % 2 !== 0) ||
          (Number.parseInt(currentMonth) > 8 &&
            Number.parseInt(currentMonth) % 2 === 0)
        ) {
          MAX_DAY_SECOND_DIGIT = 1;
        } else {
          MAX_DAY_SECOND_DIGIT = 0;
        }
      }

      if (!Number.isNaN(key)) {
        if (currentText === DEFAULT_TEXT.day) {
          if (key > 0) {
            setCurrentDay(`${ZERO}${key}`); // Prepend zero for single-digit day
            if (key > MAX_DAY_FIRST_DIGIT) {
              yyyyRef.current.focus(); // Move focus to year input if day is complete
            }
          }
        } else {
          const currentDay = Number.parseInt(currentText);
          if (
            currentDay === MAX_DAY_FIRST_DIGIT &&
            key <= MAX_DAY_SECOND_DIGIT
          ) {
            setCurrentDay(`${MAX_DAY_FIRST_DIGIT}${key}`); // Transition from first to second digit
            yyyyRef.current.focus(); // Move focus to year input
          } else {
            if (currentDay < MAX_DAY_FIRST_DIGIT) {
              const newValue = `${currentDay}${key}`;
              if (Number.isNaN(mmRef.current.textContent)) {
                setCurrentDay(newValue);
                yyyyRef.current.focus();
              } else {
                const selectedMonth = Number.parseInt(
                  mmRef.current.textContent
                );
                if (selectedMonth === 2 && Number.parseInt(newValue) > 28) {
                  setCurrentDay(`${ZERO}${key}`); // Handle February day overflow
                  yyyyRef.current.focus();
                } else {
                  setCurrentDay(newValue);
                  yyyyRef.current.focus();
                }
              }
            } else {
              if (key > 0) {
                setCurrentDay(`${ZERO}${key}`);
                if (key >= MAX_DAY_FIRST_DIGIT) {
                  yyyyRef.current.focus();
                }
              }
            }
          }
        }
      } else if (e.key === "Backspace" && currentText !== DEFAULT_TEXT.day) {
        // Handle backspace to remove digits or reset to default text
        setCurrentDay(
          Number.parseInt(currentText) > 9
            ? `${ZERO}${currentText.slice(0, -1)}`
            : DEFAULT_TEXT.day
        );
      }
      // else if (e.key === "Tab" && currentText === DEFAULT_TEXT.day) {
      //   yyyyRef.current.focus();
      // }
    };

    // Handle key down events for the year input
    const handleYearKeyDown = (e) => {
      if (e.key === "Enter") {
        handleCloseDatePicker();
        return;
      }
      e.preventDefault(); // Prevent default key press behavior
      const key = Number.parseInt(e.key); // Convert pressed key to a number
      const currentText = yyyyRef.current.textContent;

      if (!Number.isNaN(key)) {
        if (currentText === DEFAULT_TEXT.year) {
          if (key > 0) {
            setCurrentYear(`${key}`); // Replace default text with the first valid digit
          }
        } else if (currentText.length < 4) {
          const newValue = `${currentText}${key}`;
          if (
            currentMonth !== DEFAULT_TEXT.month &&
            currentDay !== DEFAULT_TEXT.day
          ) {
            const selectedMonth = Number.parseInt(mmRef.current.textContent);
            const selectedDay = Number.parseInt(ddRef.current.textContent);
            if (
              selectedMonth === 2 &&
              selectedDay === 29 &&
              newValue.length === 4 &&
              Number.parseInt(newValue) % 4 !== 0
            ) {
              setCurrentDay("28"); // Adjust day for non-leap year
            }
          }
          setCurrentYear(newValue); // Append digit if year is not complete
        } else if (key > 0) {
          setCurrentYear(`${key}`); // Reset year if a new valid digit is pressed after completion
        }
      } else if (e.key === "Backspace" && currentText !== DEFAULT_TEXT.year) {
        // Handle backspace to remove digits or reset to default text
        setCurrentYear(
          currentText.length > 1 ? currentText.slice(0, -1) : DEFAULT_TEXT.year
        );
      }
      // else if (e.key === "Tab" && currentText === DEFAULT_TEXT.year) {
      //   mmRef.current.focus();
      // }
    };

    return (
      <div
        className="flex items-center justify-start rounded-lg border border-solid border-ever-base-400 bg-ever-base gap-x-[2px] px-3 py-[10px] w-full"
        ref={ref}
        onBlur={() => {
          setTimeout(() => {
            if (
              ref.current &&
              !ref.current.contains(document.activeElement) &&
              handleCloseDatePicker
            ) {
              handleCloseDatePicker();
            }
          }, 100);
        }}
      >
        <div
          id="month-input"
          tabIndex={0}
          contentEditable={true}
          suppressContentEditableWarning={true}
          ref={mmRef}
          onKeyDown={handleMonthKeyDown}
          onFocus={(event) => {
            event.stopPropagation();
            setCursorToEnd(mmRef);
          }}
          onClick={(event) => {
            event.stopPropagation();
            setCursorToEnd(mmRef);
          }}
          className={twMerge(
            "caret-transparent px-1 focus:bg-ever-base-200 focus:outline-none rounded-sm text-sm text-ever-base-content",
            `${
              currentMonth === DEFAULT_TEXT.month
                ? "text-ever-base-content-mid"
                : "text-ever-base-content"
            }`
          )}
        />
        <div className="text-ever-base-content-mid text-sm">/</div>
        <div
          id="day-input"
          tabIndex={1}
          contentEditable={true}
          suppressContentEditableWarning={true}
          ref={ddRef}
          onKeyDown={handleDayKeyDown}
          onFocus={() => setCursorToEnd(ddRef)}
          onClick={() => setCursorToEnd(ddRef)}
          className={twMerge(
            "caret-transparent px-1 focus:bg-ever-base-200 focus:outline-none rounded-sm text-sm text-ever-base-content",
            `${
              currentDay === DEFAULT_TEXT.day
                ? "text-ever-base-content-mid"
                : "text-ever-base-content"
            }`
          )}
        />
        <div className="text-ever-base-content-mid text-sm">/</div>
        <div
          id="year-input"
          tabIndex={2}
          contentEditable={true}
          suppressContentEditableWarning={true}
          ref={yyyyRef}
          onKeyDown={handleYearKeyDown}
          onFocus={() => setCursorToEnd(yyyyRef)}
          onClick={() => setCursorToEnd(yyyyRef)}
          className={twMerge(
            "caret-transparent px-1 focus:bg-ever-base-200 focus:outline-none rounded-sm text-sm text-ever-base-content",
            `${
              currentYear === DEFAULT_TEXT.year
                ? "text-ever-base-content-mid"
                : "text-ever-base-content"
            }`
          )}
        />
      </div>
    );
  }
);

EverDateInput.displayName = "EverDateInput";

EverDateInput.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func, // onChange is a function
};
