import { ChevronDownIcon, CheckIcon } from "@everstage/evericons/outlined";
import { Avatar } from "antd";
import { isArray, isNil, uniqBy } from "lodash";
import React, { useState } from "react";
import { twMerge } from "tailwind-merge";

import { EverInput } from "~/v2/components/EverInput";
import { EverListItem } from "~/v2/components/EverListItem";
import { EverTabs } from "~/v2/components/EverTabs";

import { EverLoader } from "./ever-loader";
import { EverSelect } from "./EverSelect";
/**
 * @typedef {Object} SelectBoxItem
 * @property {string} value - unique identifier of the select box item - required
 * @property {string} label - label field of the select box item - required
 */

/**
 * @typedef {Object} ListItem
 * @property {string} value - unique identifier of the list item - required
 * @property {string} label - title of the list item - required
 * @property {string} description - description of the list item
 * @property {string|path} icon - icon for the list item. it can either be a string url, import path or a react node
 */

/**
 * @typedef {Object} NormalDropdownTabPane
 * @property {string} name - tab pane's title - required
 * @property {string} key - tab pane's unique key - required
 * @property {string} listClassname - class name for lists in tab pane
 * @property {boolean} isLazy - indicates whether the list inside the tab is rendered lazily. value should be set to false
 * @property {boolean} loading - indicates whether the list is loading
 * @property {string} searchPlaceholder - placeholder text for the search box
 * @property {boolean} showSearch - indicates whether the list should provide search feature
 * @property {Object[SelectBoxItem]} selectedValues - array of selected items containing label and value properties
 * @property {Object[ListItem]} options - array of selected items containing label and value properties
 * @property {Function} onToggleItem - handler to call toggle item
 */

/**
 * @typedef {Object} LazyDropdownTabPane
 * @property {string} name - tab pane's title - required
 * @property {string} key - tab pane's unique key - required
 * @property {boolean} isLazy - indicates whether the list inside the tab is rendered lazily. value should be set to true
 * @property {Object[SelectBoxItem]} selectedValues - array of selected items containing label and value properties
 * @property {Func} renderLazyList - render props function to return a lazy load component
 */

/**
 * @param {string} placeholder - placeholder text for Select - default ""
 * @param {string} selectClassName - custom class name for Select - default ""
 * @param {string} tabsClassName - custom class name for Tabs - default ""
 * @param {Object} tabBarStyle - custom styling for Tab bar - default {}
 * @param {Array[LazyDropdownTabPane|NormalDropdownTabPane]} tabs - list of tabs containing properties related to each tab - required
 * @param {Function} onRemove - handler receiving final list of objects - default empty function
 */

export function MultiTabDropdownSelect({
  placeholder = "",
  tabsClassName,
  tabs,
  onRemove = () => {},
  value,
  ...props
}) {
  const allTabsSelectedValues = [];
  for (const tab of tabs) {
    allTabsSelectedValues.push(...tab.selectedValues);
  }

  const renderTabBar = (props, DefaultTabBar) => (
    <DefaultTabBar
      {...props}
      className="p-2 [&>.ant-tabs-nav-wrap>.ant-tabs-nav-list]:m-auto [&>.ant-tabs-nav-wrap>.ant-tabs-nav-list]:w-full"
    />
  );

  return (
    <EverSelect
      className="w-full h-full"
      mode="multiple"
      showSearch={false}
      placeholder={placeholder}
      allowClear
      showArrow
      labelInValue
      suffixIcon={<ChevronDownIcon className="h-4 w-4" />}
      value={uniqBy(allTabsSelectedValues, "value")}
      onChange={onRemove}
      dropdownRender={() => (
        <EverTabs renderTabBar={renderTabBar}>
          {tabs.map((tab) => (
            <EverTabs.TabPane
              tab={tab.name}
              key={tab.key}
              className={tab.tabClass || tabsClassName}
            >
              {tab.isLazy ? (
                tab.renderLazyList()
              ) : (
                <MultiTabDropdownSelectNormalTabPane
                  loading={tab.loading}
                  searchPlaceholder={tab.searchPlaceholder}
                  showSearch={tab.showSearch}
                  selectedValues={tab.selectedValues}
                  options={tab.options}
                  onToggleItem={tab.onToggleItem}
                  tabClass={tab.tabClass}
                />
              )}
            </EverTabs.TabPane>
          ))}
        </EverTabs>
      )}
      {...props}
      getPopupContainer={(trigger) => {
        return trigger.parentNode;
      }}
    />
  );
}

function MultiTabDropdownSelectNormalTabPane({
  loading,
  searchPlaceholder,
  showSearch,
  selectedValues,
  options,
  onToggleItem,
  tabClass = "",
}) {
  if (!isArray(selectedValues)) selectedValues = [];
  if (!isArray(options)) options = [];
  if (isNil(onToggleItem)) onToggleItem = () => {};

  const [searchValue, setSearchValue] = useState("");

  const handleOnChange = (event) => {
    setSearchValue(event.target.value);
  };

  const handleOnKeyDown = (event) => {
    event.stopPropagation();
  };

  const filteredDataSource = showSearch
    ? options.filter((option) =>
        option.label.toLowerCase().includes(searchValue.toLowerCase())
      )
    : [...options];

  return (
    <EverLoader indicatorType="spinner" className="p-3" spinning={loading}>
      {showSearch && (
        <div className="mb-3">
          <EverInput.Search
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={handleOnChange}
            onKeyDown={handleOnKeyDown}
          />
        </div>
      )}
      <div className={twMerge("max-h-96 overflow-auto", tabClass)}>
        {filteredDataSource.map((item) => {
          const isSelected = selectedValues.some(
            ({ value }) => value === item.value
          );

          return (
            <EverListItem
              key={item.value}
              onSelect={() =>
                onToggleItem({ value: item.value, label: item.label })
              }
              selectable
              prepend={
                <Avatar
                  className={twMerge(
                    "bg-ever-error text-ever-error-content vertical-align-middle",
                    item.avatarClass
                  )}
                  src={item.icon}
                >
                  {!item.icon && `${item.label[0]}${item.label[1]}`}
                </Avatar>
              }
              subtitle={item.description}
              title={item.label}
              append={
                isSelected && (
                  <CheckIcon className="w-5 h-5 text-ever-primary" />
                )
              }
            />
          );
        })}
      </div>
    </EverLoader>
  );
}
