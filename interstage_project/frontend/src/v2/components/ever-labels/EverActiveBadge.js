import { cva } from "class-variance-authority";
import { PropTypes } from "prop-types";
import React from "react";
import { twMerge } from "tailwind-merge";

import { EverTg } from "../EverTypography";

EverActiveBadge.propTypes = {
  type: PropTypes.oneOf(["base", "success", "error", "warning", "info"]),
  description: PropTypes.string,
};
/**
 * This is a simple badge component that can be used to display active status.
 * This can also be paired with antd's Badge component.
 *
 * @example
 *
 * ```
 * <EverActiveBadge type="x" />
 * ```
 * @param {string} type - The status for the badge that will decide the color scheme of the badge.
 * @param {string} description - The text for the badge that will be added to the badge.
 * @param {string} className - Tailwind class you want to apply to the badge.
 * @param {string} innerClassName - Tailwind class you want to apply to the badge.
 * @returns The JSX for the EverActiveBadge.
 */

export function EverActiveBadge({
  type,
  description = "Active",
  className,
  innerClassName,
}) {
  const statusCva = cva(
    [
      "flex gap-2 px-2 w-max h-6 text-xs rounded-md flex justify-around items-center border-solid",
    ],
    {
      variants: {
        type: {
          success: [
            "text-ever-success-lite-content border-ever-success/30 bg-ever-success-lite",
          ],
          error: [
            "text-ever-error-lite-content border-ever-error/20 bg-ever-error-lite",
          ],
          info: [
            "text-ever-primary-lite-content border-ever-primary/30 bg-ever-primary-lite",
          ],
          base: [
            "text-ever-base-lite-content border-ever-base-400 bg-ever-base-100",
          ],
          warning: [
            "text-ever-warning-lite-content border-ever-warning/30 bg-ever-warning-lite",
          ],
          custom: [""],
        },
      },
    }
  );
  const innerCircle = cva(["w-1.5 h-1.5 rounded-full inline-block"], {
    variants: {
      type: {
        success: ["bg-ever-success-lite-content"],
        error: ["bg-ever-error-lite-content"],
        info: ["bg-ever-primary-lite-content"],
        base: ["bg-ever-base-content"],
        warning: ["bg-ever-warning-lite-content"],
        custom: [""],
      },
    },
  });
  return (
    <div className={twMerge(statusCva({ type }), className)}>
      <div className={twMerge(innerCircle({ type }), innerClassName)}></div>
      <EverTg.Caption.Medium className="self-center">
        {description}
      </EverTg.Caption.Medium>
    </div>
  );
}
