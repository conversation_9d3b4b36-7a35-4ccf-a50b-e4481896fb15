import { isNull, isUndefined } from "lodash";

import { APPROVAL_STATUS_OPTIONS } from "~/Enums";
import { EverTg, EverRadio } from "~/v2/components";
import { formatDate } from "~/v2/components/ever-formatter/EverFormatter";

import DateAndRangeFilter, { DATE_FILTER_LIST } from "./DateAndRangeFilter";
import DropDownFilter from "./DropDownFilter";
import NumberFilter, { NUMBER_FILTER_LIST } from "./NumberFilter";
import PickListFilter from "./PickListFilter";
import TextFilter, { TEXT_FILTER_LIST } from "./TextFilter";

/**
 * Returns a map of filter type value and filter type label (display name).
 *
 * @returns {Object} The filter type map.
 */
const getFilterTypeMap = () => {
  const typeMap = {};
  for (const type of TEXT_FILTER_LIST) {
    typeMap[type.value] = type.label;
  }
  for (const type of NUMBER_FILTER_LIST) {
    typeMap[type.value] = type.label;
  }
  for (const type of DATE_FILTER_LIST) {
    typeMap[type.value] = type.label;
  }
  return typeMap;
};

/**
 * Formats the display of filter values based on the filter type and specific mappings provided.

 * @param {Object} params - The parameters for formatting the filter display.
 * @param {Object} params.filterValue - The filter value object containing type and value(s) to be formatted.
 * @param {string} params.filterName - The name of the filter being formatted.
 * @param {Object} params.activeCustomFieldsMap - A map of active custom fields with their configurations and options.
 * @param {Object} params.filterValueDisplayNameMap - A map that provides display names for specific filter values.

 * @returns {Object} An object containing the formatted filter type and value(s) for display.
 * - `type` {string}: The type of the filter (e.g., "In", "Between").
 * - `value` {string}: The formatted filter value(s) as a string, ready for display.
 */
export const formatFilterDisplay = ({
  filterValue,
  filterName,
  activeCustomFieldsMap,
  filterValueDisplayNameMap,
}) => {
  const typeMapper = getFilterTypeMap();
  const type = typeMapper[filterValue["type"]];
  const value = filterValue["value"];

  if (
    Object.keys(activeCustomFieldsMap).includes(filterName) &&
    filterValue["type"] === "IN"
  ) {
    if (activeCustomFieldsMap[filterName].fieldType === "Dropdown") {
      let options = activeCustomFieldsMap[filterName]["options"];
      if (typeof options === "string") {
        options = JSON.parse(options);
      }
      return {
        type: type,
        value: value
          .map(
            (item) =>
              options.find((option) => option.system_name === item)
                ?.display_name
          )
          .join(", "),
      };
    } else {
      return {
        type: type,
        value: value
          .map((item) => {
            if (String(item).toLowerCase() === "true") {
              return "True";
            }
            return "False";
          })
          .join(", "),
      };
    }
  }

  if (filterValueDisplayNameMap[filterName]) {
    return {
      type: type,
      value: value
        .map((item) => filterValueDisplayNameMap[filterName][item])
        .join(", "),
    };
  }

  if (filterName === "approval_status") {
    return {
      type: type,
      value: value
        .map((item) => {
          return APPROVAL_STATUS_OPTIONS.find((status) => status.value === item)
            ?.label;
        })
        .join(", "),
    };
  }

  if (
    typeof filterValue === "object" &&
    "type" in filterValue &&
    "value1" in filterValue
  ) {
    const value1 = String(filterValue["value1"]);
    if (filterValue["value2"]) {
      const value2 = String(filterValue["value2"]);
      filterValue = { type: type, value: value1 + " to " + value2 };
    } else {
      filterValue = { type: type, value: value1 };
    }
  } else if (
    typeof filterValue === "object" &&
    "type" in filterValue &&
    "value" in filterValue
  ) {
    let value =
      typeof filterValue["value"] === "object"
        ? filterValue["value"].join(", ")
        : filterValue["value"];
    filterValue = { type: type, value: value };
  } else if (
    typeof filterValue === "object" &&
    "type" in filterValue &&
    "date1" in filterValue
  ) {
    let sDate = filterValue.date1 && formatDate({ date: filterValue.date1 });
    let eDate = filterValue.date2 && formatDate({ date: filterValue.date2 });
    filterValue =
      filterValue.type === "BETWEEN"
        ? {
            type: typeMapper[filterValue["type"]],
            value: sDate + " to " + eDate,
          }
        : { type: typeMapper[filterValue["type"]], value: sDate };
  } else {
    filterValue = { type: "In", value: filterValue.join(", ") };
  }
  return filterValue;
};

const isNumberFilterValid = (filter) => {
  return (
    (!isUndefined(filter?.value1) && !isNull(filter?.value1)) ||
    (!isUndefined(filter?.value2) && !isNull(filter?.value2))
  );
};

/**
 * DynamicFieldsContainer is a React functional component that renders a container for individual filter fields.
 * It includes the display name of the field, an optional "Clear" button to reset the filter, and the children components
 * representing the specific filter input (e.g., Dropdown, Date picker).

 * @param {Object} props - The properties object passed to this component.
 * @param {string} props.displayedFieldSystemName - The system name of the displayed field (used as a key).
 * @param {string} props.displayedFieldDisplayName - The display name of the field, shown as a label.
 * @param {Function} props.changeAppliedFilters - Function to update the applied filters.
 * @param {boolean} props.clearTextCondition - Condition to determine whether the "Clear" text should be displayed.
 * @param {React.ReactNode} props.children - The filter input components to be rendered within the container.

 * @returns {JSX.Element} A container component that wraps individual filter fields with a label and optional "Clear" button.
 */
const DynamicFieldsContainer = ({
  displayedFieldSystemName,
  displayedFieldDisplayName,
  changeAppliedFilters,
  clearTextCondition,
  children,
}) => {
  const ClearText = () => (
    <div
      className="pr-2 ml-auto text-ever-primary font-medium cursor-pointer hover:underline"
      onClick={() => changeAppliedFilters({ [displayedFieldSystemName]: null })}
    >
      <EverTg.Text>Clear</EverTg.Text>
    </div>
  );

  return (
    <div
      className="w-full flex flex-col"
      key={displayedFieldSystemName}
      span={8}
    >
      <div className="mb-2 flex gap-3">
        <EverTg.Text className="text-ever-base-content-mid">
          {displayedFieldDisplayName}
        </EverTg.Text>
        {clearTextCondition ? <ClearText /> : null}
      </div>
      {children}
    </div>
  );
};

/**
 * DynamicFields is a React functional component that renders a list of dynamic fields based on the provided filter configurations.
 * Each field type (e.g., Dropdown, Date, Boolean) is rendered using the appropriate input component within a DynamicFieldsContainer.
 * The component maps over the provided filter fields and generates the corresponding UI for each field type.

 * @param {Object} props - The properties object passed to this component.
 * @param {Array} props.filterFields - An array of objects representing the filter fields to be displayed, including type, name, and options.
 * @param {Function} props.changeAppliedFilters - Function to update the applied filters when a filter value changes.

 * @returns {JSX.Element} A list of dynamic fields rendered based on the provided filter configurations.
 */
const DynamicFields = ({ filterFields, changeAppliedFilters }) => {
  const DisplayFields = filterFields.map((displayField) => {
    const displayedFieldSystemName = displayField.systemName;
    const displayedFieldDisplayName = displayField.displayName;
    const displayedFieldFilterValue = displayField.filterValue;
    const displayedFieldOptions = displayField.options;

    switch (displayField.fieldType) {
      case "Dropdown": {
        return (
          <DynamicFieldsContainer
            displayedFieldSystemName={displayedFieldSystemName}
            displayedFieldDisplayName={displayedFieldDisplayName}
            changeAppliedFilters={changeAppliedFilters}
            clearTextCondition={displayedFieldFilterValue?.value}
          >
            <DropDownFilter
              filterValue={displayedFieldFilterValue}
              changeAppliedFilters={changeAppliedFilters}
              systemName={displayedFieldSystemName}
              options={displayField.options}
            />
          </DynamicFieldsContainer>
        );
      }
      case "PickList": {
        return (
          <DynamicFieldsContainer
            displayedFieldSystemName={displayedFieldSystemName}
            displayedFieldDisplayName={displayedFieldDisplayName}
            changeAppliedFilters={changeAppliedFilters}
            clearTextCondition={displayedFieldFilterValue?.value}
          >
            <PickListFilter
              filterValue={displayedFieldFilterValue}
              changeAppliedFilters={changeAppliedFilters}
              systemName={displayedFieldSystemName}
              options={displayField.options}
            />
          </DynamicFieldsContainer>
        );
      }

      case "Date": {
        return (
          <DynamicFieldsContainer
            displayedFieldSystemName={displayedFieldSystemName}
            displayedFieldDisplayName={displayedFieldDisplayName}
            changeAppliedFilters={changeAppliedFilters}
            clearTextCondition={displayedFieldFilterValue}
          >
            <DateAndRangeFilter
              filterValue={displayedFieldFilterValue}
              changeAppliedFilters={changeAppliedFilters}
              systemName={displayedFieldSystemName}
            />
          </DynamicFieldsContainer>
        );
      }

      case "Boolean": {
        return (
          <DynamicFieldsContainer
            displayedFieldSystemName={displayedFieldSystemName}
            displayedFieldDisplayName={displayedFieldDisplayName}
            changeAppliedFilters={changeAppliedFilters}
            clearTextCondition={displayedFieldFilterValue != null}
          >
            <EverRadio.Group
              className="flex-wrap"
              value={displayedFieldFilterValue?.[0]}
              options={displayedFieldOptions.map((item) => ({
                label: item.label,
                value: item.value,
              }))}
              onChange={(event) => {
                changeAppliedFilters({
                  [displayedFieldSystemName]: [event.target.value],
                });
              }}
            />
          </DynamicFieldsContainer>
        );
      }

      case "Number": {
        return (
          <DynamicFieldsContainer
            displayedFieldSystemName={displayedFieldSystemName}
            displayedFieldDisplayName={displayedFieldDisplayName}
            changeAppliedFilters={changeAppliedFilters}
            clearTextCondition={isNumberFilterValid(displayedFieldFilterValue)}
          >
            <NumberFilter
              filterValue={displayedFieldFilterValue}
              changeAppliedFilters={changeAppliedFilters}
              systemName={displayedFieldSystemName}
            />
          </DynamicFieldsContainer>
        );
      }

      case "Text": {
        return (
          <DynamicFieldsContainer
            displayedFieldSystemName={displayedFieldSystemName}
            displayedFieldDisplayName={displayedFieldDisplayName}
            changeAppliedFilters={changeAppliedFilters}
            clearTextCondition={displayedFieldFilterValue?.value}
          >
            <TextFilter
              filterValue={displayedFieldFilterValue}
              changeAppliedFilters={changeAppliedFilters}
              systemName={displayedFieldSystemName}
            />
          </DynamicFieldsContainer>
        );
      }

      case "Email": {
        return (
          <DynamicFieldsContainer
            displayedFieldSystemName={displayedFieldSystemName}
            displayedFieldDisplayName={displayedFieldDisplayName}
            changeAppliedFilters={changeAppliedFilters}
            clearTextCondition={displayedFieldFilterValue?.value}
          >
            <TextFilter
              filterValue={displayedFieldFilterValue}
              changeAppliedFilters={changeAppliedFilters}
              systemName={displayedFieldSystemName}
            />
          </DynamicFieldsContainer>
        );
      }

      default: {
        return null;
      }
    }
  });

  return <>{DisplayFields}</>;
};

export default DynamicFields;
