import { format } from "date-fns";
import React, { useEffect, useState } from "react";

import { EverNewDatePicker, EverSelect } from "~/v2/components";

export const DATE_FILTER_LIST = [
  { label: "In Between", value: "BETWEEN" },
  { label: "Greater Than", value: ">" },
  { label: "Greater than or Equal to", value: ">=" },
  { label: "Less than", value: "<" },
  { label: "Less than or Equal to", value: "<=" },
];
const DateAndRangeFilter = ({
  filterValue,
  changeAppliedFilters,
  systemName,
}) => {
  const [picker, setPicker] = useState(DATE_FILTER_LIST[0].value);
  const date1 =
    filterValue?.date1 && typeof filterValue?.date1 === "string"
      ? new Date(filterValue?.date1)
      : filterValue?.date1 ?? null;
  const date2 =
    filterValue?.date2 && typeof filterValue?.date2 === "string"
      ? new Date(filterValue?.date2)
      : filterValue?.date2 ?? null;

  useEffect(() => {
    if (filterValue?.type) {
      setPicker(filterValue?.type);
    }
  }, [filterValue]);

  return (
    <div className="flex flex-col gap-3">
      <div className="flex flex-row">
        <EverSelect
          className="w-24 h-9 select-right-border-radius-none select-right-border-width-none"
          value={picker}
          onChange={(value) => {
            setPicker(value);
            changeAppliedFilters({ [systemName]: null });
          }}
          options={DATE_FILTER_LIST}
        />
        <div className="date-picker-left-border-none flex-1">
          {picker === "BETWEEN" ? (
            <EverNewDatePicker.RangePicker
              className="w-full"
              placeholder={["From", "To"]}
              value={[date1, date2]}
              onChange={(inputDateArr) => {
                if (inputDateArr) {
                  inputDateArr = {
                    type: picker,
                    date1: format(
                      new Date(Date.parse(inputDateArr[0])),
                      "yyyy-MM-dd"
                    ),
                    date2: format(
                      new Date(Date.parse(inputDateArr[1])),
                      "yyyy-MM-dd"
                    ),
                  };
                }
                changeAppliedFilters({ [systemName]: inputDateArr });
              }}
              allowClear
            />
          ) : (
            <EverNewDatePicker
              className="w-full"
              type={"period"}
              placeholder={"Select date"}
              value={date1}
              onChange={(inputDate) => {
                if (inputDate) {
                  inputDate = {
                    type: picker,
                    date1: format(
                      new Date(Date.parse(inputDate)),
                      "yyyy-MM-dd"
                    ),
                    date2: null,
                  };
                }
                changeAppliedFilters({ [systemName]: inputDate });
              }}
              allowClear
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default DateAndRangeFilter;
