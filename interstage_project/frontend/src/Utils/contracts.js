/**
 * Calculates the number of missing permissions for both account and user levels
 * @param {Object} params - The permissions parameters object
 * @param {Object} params.accountPermissions - Object containing account level permissions
 * @param {Object} params.userPermissions - Object containing user level permissions
 * @returns {Object} Object containing counts of missing permissions for both account and user
 */
export const permissionsCount = ({ accountPermissions, userPermissions }) => {
  // Array of required account level permissions that need to be enabled
  const requiredAccountPermissions = [
    "enableBulkRecipients", // Permission to send documents to multiple recipients at once
    "setRecipEmailLang", // Permission to set recipient email language
    "userOverrideEnabled", // Permission to override user settings
  ];

  // Array of required user level permissions that need to be enabled
  const requiredUserPermissions = [
    "allowRecipientLanguageSelection", // Permission to let recipients select language
    "bulkSend", // Permission to send bulk documents
    "canManageTemplates", // Permission to manage document templates
    "canSendEnvelope", // Permission to send envelopes
    "disableDocumentUpload", // Permission to disable document uploads
  ];

  // Filter account permissions to find which required ones are missing
  const missingAccount = requiredAccountPermissions.filter(
    (key) => accountPermissions?.[key] !== "true"
  );

  // Filter user permissions to find which required ones are missing
  const missingUser = requiredUserPermissions.filter((key) => {
    // Special case for template management permission
    if (key === "canManageTemplates") {
      return userPermissions?.[key] !== "share";
    }
    // Special case for document upload permission
    if (key === "disableDocumentUpload") {
      return userPermissions?.[key] !== "false";
    }
    // Default case checking if permission is enabled
    return userPermissions?.[key] !== "true";
  });

  // Return object with counts of missing permissions
  return {
    missingAccountPermissionsCount: missingAccount.length,
    missingUserPermissionsCount: missingUser.length,
  };
};
