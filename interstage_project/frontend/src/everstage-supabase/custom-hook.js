import React, { useState, useEffect, useMemo, useRef } from "react";
import { useSyncExternalStore } from "use-sync-external-store/shim";
import { useRecoilValue } from "recoil";
import _ from "lodash";

import { myClient<PERSON>tom } from "~/GlobalStores/atoms";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { supabaseClient } from "./supabaseClient";
const supabaseTableName = process.env.REACT_APP_SUPABASE_TABLE_NAME;
console.log(`Subscribing to changes in the ${supabaseTableName} table`);

/**
 * @typedef {import("@supabase/realtime-js").RealtimePostgresChangesFilter} RealtimePostgresChangesFilter
 * @typedef {import("@supabase/realtime-js").RealtimePostgresChangesPayload} RealtimePostgresChangesPayload
 */

/**
 * React hook to subscribe to events of interest on the {process.env.REACT_APP_SUPABASE_TABLE_NAME} table
 * and is pushed to subscribers via websockets.
 *
 * *Note* - you need to write backend code to insert / update / delete rows in the {process.env.REACT_APP_SUPABASE_TABLE_NAME} to see the changes.
 *
 * @param {{
 *             event:string,
 *             filter: string,
 *             table: string,
 *             schema: string
 *        }[]} realtimePostgresChangesFilter- An array of objects of the form {@link RealtimePostgresChangesFilter}
 * @param {Object} options
 * @param {string} options.channelPrefix - A prefix to be used for the channel name. If not provided, a random uuid will be used
 * @param {LocalSupabaseStore} options.testStore - A test store to be used instead of the default LocalSupabaseStore
 *
 * @returns {{
 *          new: object,
 *          old: object,
 *          commit_timestamp: string,
 *          event: string,
 *          schema: string,
 *          table: string,
 *          errors: []
 *          }} realtimePostgresChangesPayload - changes pushed through the websocket {@link RealtimePostgresChangesPayload}
 *
 * @example
 *
 * ```
 * function MyComponent() {
 *   const realtimePostgresChangesFilter = [{event: "INSERT", filter:"task_name=eq.bulk_upload"}];
 *   const channelPrefix = "channel-1"
 *   const updates = useSupabasePostgresChanges(realtimePostgresChangesFilter,{channelPrefix});
 *   return <div>{updates?.new?.data?.status}</div>
 * }
 * ```
 *
 * The above example will return an object like:
 * ```
 * {
 * "schema": "public",
 * "table": "realtime_events_local",
 * "commit_timestamp": "2023-03-10T08:51:44.937Z",
 * "eventType": "INSERT",
 * "new": {
 *    "client_id": 1,
 *    "created_at": "2023-03-10T08:51:44.936696+00:00",
 *    "data": {
 *        "status": "DONE"
 *    },
 *    "id": 798,
 *    "roles": {},
 *    "task_id": "d5d66270-d29c-431c-99a2-0f667176490f",
 *    "task_name": "BULK_CREATE_APPROVAL_INSTANCE",
 *    "updated_at": "2023-03-10T08:54:44.936696+00:00"
 * },
 * "old": {},
 * "errors": null
 * }
 * ```
 */
export function useSupabasePostgresChanges(
  realtimePostgresChangesFilter,
  { channelPrefix = null, testStore = null }
) {
  const { accessToken, email } = useAuthStore();
  const clientId = useClientId();

  const store = useMemo(() => {
    if (testStore) {
      return testStore;
    }
    return new LocalSupabaseStore();
  }, []);

  const [subscribeEffectDep, toggleSubscribeEffectDep] = useState(false);
  const [previousChangesFilter, setPreviousChangesFilter] = useState(
    realtimePostgresChangesFilter
  );

  // When the value of realtimePostgresChangesFilter changes, isDifferent will return true, which will trigger a re-subscription
  if (isDifferent(realtimePostgresChangesFilter, previousChangesFilter)) {
    setPreviousChangesFilter(realtimePostgresChangesFilter);
    toggleSubscribeEffectDep((prev) => !prev);
  }

  const reTriggerOnTimeout = useSyncExternalStore(
    retriggerStore.subscribe.bind(retriggerStore),
    retriggerStore.getSnapshot.bind(retriggerStore)
  );

  // As the toggleSubscribeEffectDep or reTriggerOnTimeout changes, the effect will re-run and re-subscribe
  useEffect(() => {
    let channel = null;
    // If there is nothing to subscribe to, return early
    if (_.isEmpty(realtimePostgresChangesFilter) || !clientId) {
      return;
    }

    (async () => {
      channel = await subscribeForPostgresChanges(
        accessToken,
        channelPrefix,
        realtimePostgresChangesFilter,
        store.addUpdates.bind(store),
        retriggerStore.reTriggerSubscription.bind(retriggerStore),
        clientId,
        email
      );
    })();

    return () => {
      if (channel) {
        unsubscribe(channel);
      }
    };
  }, [subscribeEffectDep, reTriggerOnTimeout, clientId]);
  return useSyncExternalStore(
    store.subscribe.bind(store),
    store.getSnapshot.bind(store)
  );
}

/**
 * Provides a channel name based on the use-case and the client id
 */
function _getChannelName(
  clientId,
  channelPrefix = null,
  realtimeType = "postgres",
  clientAware = true
) {
  const env = process.env.REACT_APP_ACTUAL_ENV;
  let cp = channelPrefix;
  if (!cp) {
    cp = `default-channel-${realtimeType}`;
  }
  return clientAware ? `${cp}-${clientId}-${env}` : cp;
}

// Exporting only for testing
// This function is used to subscribe to postgres changes
export async function subscribeForPostgresChanges(
  accessToken,
  channelPrefix,
  realtimePostgresChangesFilter,
  handleData,
  reTriggerSubscription,
  clientId,
  email
) {
  let channel = await _createChannelForPostgresChanges(
    accessToken,
    channelPrefix,
    realtimePostgresChangesFilter,
    handleData,
    clientId,
    email
  );
  channel.subscribe(async (status) => {
    // If the client is inactive for for more than 10 minutes, supabase will remove the channel, so resubscribing to the channel
    if (status === "TIMED_OUT" || status === "CHANNEL_ERROR") {
      console.log("Channel terminated, cause: ", status);
      supabaseClient?.removeAllChannels();
      reTriggerSubscription();
    }
  });
  return channel;
}

// Exporting only for testing
// This function will unsubscribe from the channel
function unsubscribe(channel) {
  if (supabaseClient) {
    supabaseClient.removeChannel(channel);
  }
}

// This function does a deep compare between the two arrays and return true is they are not equal
function isDifferent(x, y) {
  if (!_.isEmpty(x)) {
    return !_(x).differenceWith(y, _.isEqual).isEmpty();
  }
  return false;
}

// This function is used to create a channel for postgres changes
async function _createChannelForPostgresChanges(
  accessToken,
  channelPrefix,
  realtimePostgresChangesFilter,
  handleData,
  clientId,
  email
) {
  if (!channelPrefix) {
    channelPrefix = email;
  }
  let channelName = _getChannelName(clientId, channelPrefix, "postgres");
  const supabaseAccessToken = await _getSupabaseAccessToken(accessToken);
  supabaseClient.realtime.accessToken = supabaseAccessToken;
  const channel = supabaseClient.channel(channelName);
  for (const realtimeFilter of realtimePostgresChangesFilter) {
    channel.on(
      "postgres_changes",
      {
        event: realtimeFilter.event,
        schema: "public",
        table: supabaseTableName,
        filter: realtimeFilter.filter,
      },
      handleData
    );
  }
  return channel;
}

/**
 *
 * Custom hook to subscribe to events of channel and get the messages broadcasted via websockets
 *
 *
 * @param {string} event - The event to which the user is interested to listen
 * @param {string} channelPrefix - The channel prefix to which the user is subscribing
 * @param {{}} broadcastConfig - The broadcast config to pass to supabase client
 * @param {boolean} broadcastConfig.self - Whether to broadcast the message to the sender of the message also
 * 
 * @return {{}} broadcast - The message broadcasted by the user
 * @return {string} broadcast.event - The event in which the message is broadcasted
 * @return {object} broadcast.payload - The payload of the message sent by the user
 * @return {string} broadcast.type - The type of the realtime feature(broadcast in this case)
 * 
 * @example
 * 
 * ```
  function MyComponent() {
    const event = ["roomId-1","roomId-2"];
    const broadcastConfig = { self: true };
    const channelPrefix: "my-channel",
      
    const [message, makeBroadcast] = useSupabaseBroadcast(event,{channelPrefix,broadcastConfig});
    function sendMessage() {
      makeBroadcast({
        type: "broadcast", // This is constant
        event: event[0], // This is to identify the event to which the user is interested to send a message.
        payload: { user: "<EMAIL>", data: "Hi this is super admin" }, // The payload is the message the user wants to send
      });
    }
    return (
      <div>
        <button onClick={sendMessage} type="primary">
          Publish
        </button>
        <p>{message?.payload?.data}</p>
      </div>
      )}
 * ```
 *
 * The above example returns an object like - 
 * ```
 *  {
 *      event: "roomId-1",
 *      payload: {
 *          data: "Hi this is superadmin",
 *          user: "<EMAIL>"
 *       },
 *      type: "broadcast"
 *   }
 * ```
*/
export function useSupabaseBroadcast(
  event,
  { channelPrefix = null, broadcastConfig = { self: false } } = {},
  clientAware = true
) {
  const { accessToken } = useAuthStore();
  const clientId = useClientId();
  const store = useMemo(() => {
    return new LocalSupabaseStore();
  }, []);
  const broadcastChannel = useRef(null);
  const broadcastSubscriptionStatus = useRef(null);
  const reTriggerOnTimeout = useSyncExternalStore(
    retriggerStore.subscribe.bind(retriggerStore),
    retriggerStore.getSnapshot.bind(retriggerStore)
  );

  // As the channelName or subscribeEffectDep or reTriggerOnTimeout changes,the effect will be triggered again for re-subscription
  useEffect(() => {
    if (!clientId) {
      return;
    }
    (async () => {
      broadcastChannel.current = await _subscribeForBroadcast(
        broadcastConfig,
        event,
        channelPrefix,
        store.addUpdates.bind(store),
        accessToken,
        broadcastSubscriptionStatus,
        broadcastChannel,
        retriggerStore.reTriggerSubscription.bind(retriggerStore),
        clientId,
        clientAware
      );
    })();
    return () => {
      if (broadcastChannel.current) {
        unsubscribe(broadcastChannel.current);
      }
    };
  }, [channelPrefix, reTriggerOnTimeout, clientId]);

  // This function will be used to trigger the broadcast event
  function makeBroadcast(data) {
    if (
      broadcastChannel.current &&
      broadcastSubscriptionStatus.current === "SUBSCRIBED"
    ) {
      broadcastChannel.current.send(data);
    }
  }
  return [
    useSyncExternalStore(
      store.subscribe.bind(store),
      store.getSnapshot.bind(store)
    ),
    makeBroadcast,
  ];
}

// This function is used to create a channel for broadcast
async function _createChannelForBroadcast(
  broadcastConfig,
  event,
  channelPrefix,
  handleData,
  accessToken,
  clientId,
  clientAware
) {
  let broadcastChannel;
  const supabaseAccessToken = await _getSupabaseAccessToken(accessToken);
  supabaseClient.realtime.accessToken = supabaseAccessToken;
  const channelName = _getChannelName(
    clientId,
    channelPrefix,
    "broadcast",
    clientAware
  );

  // If selfBroadcast is true, then the sender will receive the broadcast message as well
  if (broadcastConfig.self) {
    broadcastChannel = supabaseClient.channel(channelName, {
      config: {
        broadcast: {
          self: true,
        },
      },
    });
  } else {
    broadcastChannel = supabaseClient.channel(channelName);
  }
  // Listen to the broadcast event and add the updates to the store
  for (const evt of event) {
    broadcastChannel.on("broadcast", { event: evt }, handleData);
  }
  return broadcastChannel;
}

// This function is used to subscribe to the channel for broadcast
async function _subscribeForBroadcast(
  broadcastConfig,
  event,
  channelPrefix,
  handleData,
  accessToken,
  broadcastSubscriptionStatus,
  broadcastChannel,
  reTriggerSubscription,
  clientId,
  clientAware
) {
  broadcastChannel.current = await _createChannelForBroadcast(
    broadcastConfig,
    event,
    channelPrefix,
    handleData,
    accessToken,
    clientId,
    clientAware
  );
  broadcastChannel.current.subscribe(async (status) => {
    broadcastSubscriptionStatus.current = status;
    // If the client is inactive for for more than 10 minutes, supabase will remove the channel, so resubscribing to the channel
    if (status === "TIMED_OUT" || status === "CHANNEL_ERROR") {
      console.log("Channel terminated, cause: ", status);
      supabaseClient?.removeAllChannels();
      reTriggerSubscription();
    }
  });
  return broadcastChannel.current;
}

/**
 *
 * Custom hook to subscribe to channel and get the active users in that channel via websockets
 *
 *
 * @param {string} event - The event to which the user is interested to listen
 * @param {string} channelName - The name of the channel to subscribe to. The channel name should be unique for each task requiring presence.
 * @param {{}} presenceConfig - The presence config object to be passed to the supabase client
 * @param {string} presenceConfig.key - The key to be used to track the user in the presence channel. By default will be user's email
 * @param {{}} trackingObj - The object that will be tracked by the presence channel. By default will be an object with user's email
 *
 * @return {{}} - presence - The object containing the data and presence type
 * @return {string} presence.presenceType - The type of presence event
 * @return {{} []} presence.data - An array of objects containing the tracked data of each users in the channel
 *
 * @example
 *
 * ```
 * function MyComponent() {
 *   const trackingObj = {user:"<EMAIL>",online_at: new Date()}
 *   const event = ["sync","join","leave"]
 *   const channelPrefix = "presence-channel"
 *   const presenceConfig = {key: "<EMAIL>"}}
 *   const args = {
 *    channelPrefix,
 *    presenceConfig,
 *    trackingObj
 *   }
 *   const presence = useSupabasePresence(event,args);
 *   return <div>{presence.data.map((user) => (
 *    <p>{user.user}</p>
 *   ))}</div>
 * }
 * ```
 *
 * The above example returns an array of objects like-
 *
 * Example of sync event:
 * ```
 * {
 *  "presenceType": "sync",
 *  "data": [
 *   {
 *       "online_at": "2023-03-30T10:34:05.936Z",
 *       "user": "<EMAIL>",
 *       "presence_ref": "F1Er8DKit767OJnG"
 *   },
 *   {
 *       "online_at": "2023-03-30T10:34:09.211Z",
 *       "user": "<EMAIL>",
 *       "presence_ref": "F1Er8PceIx27OL_l"
 *   },
 *  {
 *      "online_at": "2023-03-30T10:34:09.211Z",
 *      "user": "<EMAIL>",
 *      "presence_ref": "F1Er8PceIx27OL_a"
 *  },
 * ]
 * }
 * ```
 *
 * Example of join and leave event:
 * ```
 * {
 *  "presenceType":"join" / "leave",
 *  "data": [
 *   {
 *       "online_at": "2023-03-31T12:31:45.299Z",
 *       "user": "<EMAIL>",
 *       "presence_ref": "F1GBXJC2fynwqJ1j"
 *   }
 * ]
 * }
 * ```
 */

export function useSupabasePresence(
  event,
  {
    channelPrefix = null,
    presenceConfig = { key: null }, // If the key is not specified, by default it will assign user's email for each subscription
    trackingObj = { user: null }, // If the trackingObj is not specified, by default it will assign an object with user's email for each subscription
  } = {}
) {
  const { accessToken, email } = useAuthStore();
  const clientId = useClientId();

  if (presenceConfig.key === null) {
    presenceConfig.key = email;
  }
  if (trackingObj.user === null) {
    trackingObj.user = email;
  }

  const store = useMemo(() => {
    return new LocalSupabaseStore();
  }, []);
  let presenceChannel = null;
  const reTriggerOnTimeout = useSyncExternalStore(
    retriggerStore.subscribe.bind(retriggerStore),
    retriggerStore.getSnapshot.bind(retriggerStore)
  );

  // As the toggleSubscribeEffectDep or reTriggerOnTimeout or channelName is changed, the effect will be triggered again for resubscription
  useEffect(() => {
    if (!clientId) {
      return;
    }
    (async () => {
      presenceChannel = await _subscribeForPresence(
        trackingObj,
        event,
        channelPrefix,
        presenceConfig,
        store.addUpdates.bind(store),
        accessToken,
        retriggerStore.reTriggerSubscription.bind(retriggerStore),
        clientId
      );
    })();
    return () => {
      if (presenceChannel) {
        unsubscribe(presenceChannel);
      }
    };
  }, [channelPrefix, reTriggerOnTimeout, clientId]);

  return useSyncExternalStore(
    store.subscribe.bind(store),
    store.getSnapshot.bind(store)
  );
}

// This function will subscribe to the channel and listen to the presence event
async function _subscribeForPresence(
  trackingObj,
  event,
  channelPrefix,
  presenceConfig,
  handleData,
  accessToken,
  reTriggerSubscription,
  clientId
) {
  let presenceChannel = await _createChannelForPresence(
    channelPrefix,
    presenceConfig,
    accessToken,
    clientId
  );
  for (const evt of event) {
    if (evt === "sync") {
      presenceChannel.on("presence", { event: "sync" }, () => {
        handleData({
          presenceType: "sync",
          data: presenceChannel.presenceState(),
        });
      });
    } else if (evt === "join") {
      presenceChannel.on(
        "presence",
        { event: "join" },
        ({ key, newPresences }) => {
          handleData({ presenceType: "join", data: { [key]: newPresences } });
        }
      );
    } else if (evt === "leave") {
      presenceChannel.on(
        "presence",
        { event: "leave" },
        ({ key, leftPresences }) => {
          handleData({ presenceType: "leave", data: { [key]: leftPresences } });
        }
      );
    }
  }
  presenceChannel.subscribe(async (status) => {
    if (status === "SUBSCRIBED") {
      // This will track the user's presence - when the user subscribes to the channel, the trackingObj will be added to the presence state
      await presenceChannel.track(trackingObj);
    } else if (status === "TIMED_OUT" || status === "CHANNEL_ERROR") {
      console.log("Channel terminated, cause: ", status);
      supabaseClient?.removeAllChannels();
      reTriggerSubscription();
    }
  });
  return presenceChannel;
}

// This function will create the channel for presence
async function _createChannelForPresence(
  channelPrefix,
  presenceConfig,
  accessToken,
  clientId
) {
  const supabaseAccessToken = await _getSupabaseAccessToken(accessToken);
  supabaseClient.realtime.accessToken = supabaseAccessToken;
  const channelName = _getChannelName(clientId, channelPrefix, "presence");
  // This channel will be used to track the presence of the users
  // The key in presenceConfig will be used to group the users in the channel - Eg: if the key is email of the user, then if the user logs in from multiple devices, the user will be grouped together
  const presenceChannel = supabaseClient.channel(channelName, {
    config: {
      presence: presenceConfig,
    },
  });
  return presenceChannel;
}

// This function will get the supabase access token from the server
async function _getSupabaseAccessToken(webAccessToken) {
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${webAccessToken}`,
    },
  };

  const response = await fetch("/common/get-supabase-token", requestOptions);
  const data = await response.json();
  return data.token;
}

// Exporting only for testing
export class LocalSupabaseStore {
  constructor() {
    this.listener = null;
    this.latestPayload = null;
  }

  addUpdates(payload) {
    this.latestPayload = payload;
    this.listener();
  }

  subscribe(listener) {
    this.listener = listener;
  }

  getSnapshot() {
    return this.latestPayload;
  }
}

// The custom-hooks will subscribe to this store and will get the latest triggerIdx value, when there is a timed_out, which will trigger a re-subscription
export class LocalRetriggerStore {
  static MAX_RETRY_ATTEMPTS = 100;

  static retryCount = 0;

  constructor() {
    this.listeners = [];
    this.triggerIdx = 0;
  }

  reTriggerSubscription() {
    if (
      LocalRetriggerStore.retryCount >= LocalRetriggerStore.MAX_RETRY_ATTEMPTS
    ) {
      console.warn(
        `Maximum retry attempts (${LocalRetriggerStore.MAX_RETRY_ATTEMPTS}) reached. Stopping reconnection attempts.`
      );
      return;
    }
    LocalRetriggerStore.retryCount++;
    this.triggerIdx = this.triggerIdx + 1;
    this.notify();
  }

  subscribe(listener) {
    this.listeners.push(listener);
  }

  notify() {
    this.listeners.forEach((listener) => listener());
  }
  getSnapshot() {
    return this.triggerIdx;
  }
}
// This retriggerStore will be used by all custom-hooks to re-trigger the subscription
const retriggerStore = new LocalRetriggerStore();

/**
 * Custom hook to get the client id of the user
 */
function useClientId() {
  const client = useRecoilValue(myClientAtom);
  const [clientIdState, setClientIdState] = useState(null);
  const clientId = client?.clientId ?? null;
  if (clientIdState !== clientId) {
    setClientIdState(clientId);
  }
  return clientIdState;
}
