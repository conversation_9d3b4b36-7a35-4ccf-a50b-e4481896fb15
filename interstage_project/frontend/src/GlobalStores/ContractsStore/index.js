import React from "react";
import { makeAutoObservable, reaction } from "mobx";
import { useLocalStore } from "mobx-react";
import { useAuthStore } from "~/GlobalStores/AuthStore";
import { cloneDeep, isEmpty, orderBy } from "lodash";
import { useApolloClient, gql } from "@apollo/client";

import { permissionsCount } from "~/Utils/contracts";

const GET_ALL_TEMPLATE_DETAILS = gql`
  query AllTemplateDetails($emailId: String!) {
    allTemplateDetails(emailId: $emailId)
  }
`;

const GET_TEMPLATE_DETAILS = gql`
  query TemplateDetails(
    $emailId: String!
    $templateId: String!
    $include: String
  ) {
    templateDetails(
      emailId: $emailId
      templateId: $templateId
      include: $include
    )
  }
`;

const SORT_PARAMS = {
  "Ascending (A - Z)": { key: ["template_name"], order: ["asc"] },
  "Descending ( Z - A )": { key: ["template_name"], order: ["desc"] },
  "Created Date": { key: ["created_at"], order: ["desc"] },
  "Last Modified": { key: ["updated_at"], order: ["desc"] },
};

class ContractsStore {
  allTemplateDetails = [];
  constructor(apolloClient, email, accessToken) {
    this.apolloClient = apolloClient;
    this.email = email;
    this.contracts = [];
    this.loading = false;
    this.isInitialDataSet = false;
    this.accessToken = accessToken;
    this.batchSize = 5;
    this.abortController = null;
    this.hasAllPermissions = false;
    this.refetchContractPermissions = null;
    this.missingUserPermissionsCount = 0;
    this.missingAccountPermissionsCount = 0;
    this.userPermissions = {};
    this.accountPermissions = {};
    makeAutoObservable(this);

    reaction(
      () => [this.userPermissions, this.accountPermissions],
      () => {
        this.calculateAndSetPermissionCounts();
      }
    );
  }

  fetchEnvelopeDetails = async (templateId) => {
    if (this.abortController === null) {
      this.abortController = new AbortController();
    }
    const { signal } = this.abortController;
    const url = `/spm/envelope_details?template_id=${templateId}`;
    return fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
      },
      signal,
    })
      .then(async (response) => {
        if (!response.ok) return null;

        return [await response.json(), "NOT_ABORTED"];
      })
      .catch((error) => {
        if (error.name === "AbortError") return [null, "ABORTED"];
        return [null, "NOT_ABORTED"];
      });
  };

  handleEnvelopeDetails = (
    envelopeFetchPromises,
    searchText = "",
    sortOrder = null
  ) => {
    Promise.all(envelopeFetchPromises).then(
      async (envelopeDetailsResponses) => {
        const isAnyPromiseAborted = envelopeDetailsResponses.some(
          ([, abortStatus]) => abortStatus === "ABORTED"
        );
        if (isAnyPromiseAborted) return;

        for (const [
          envelopeDetailsForTemplateData,
          _,
        ] of envelopeDetailsResponses) {
          if (isEmpty(envelopeDetailsForTemplateData)) continue;
          const originalContract = this.contracts.find(
            (contract) =>
              contract.template_id === envelopeDetailsForTemplateData.templateId
          );
          originalContract["envelopes_for_template"] =
            envelopeDetailsForTemplateData["envelopesForTemplate"];
          originalContract["awaiting"] =
            envelopeDetailsForTemplateData["awaiting"];
          originalContract["completed"] =
            envelopeDetailsForTemplateData["completed"];
          originalContract["is_envelope_details_fetched"] = true;
        }
        this.contracts = [...this.contracts];

        this.abortController = new AbortController();
        this.processEnvelopeSubscription(this.contracts, searchText, sortOrder);
      }
    );
  };
  processEnvelopeSubscription = (
    contracts,
    searchText = "",
    sortOrder = null
  ) => {
    let templatesCount = 0;
    const envelopeFetchPromises = [];

    let filteredAndOrderedContracts = contracts;
    if (sortOrder) {
      filteredAndOrderedContracts = orderBy(
        filteredAndOrderedContracts,
        SORT_PARAMS[sortOrder]["key"],
        SORT_PARAMS[sortOrder]["order"]
      );
    }
    if (!isEmpty(searchText)) {
      filteredAndOrderedContracts = filteredAndOrderedContracts.filter(
        (contract) =>
          contract.template_name
            .toLowerCase()
            .includes(searchText.toLowerCase())
      );
    }

    for (const contract of filteredAndOrderedContracts) {
      if (
        !contract.is_envelope_details_fetched &&
        templatesCount < this.batchSize
      ) {
        envelopeFetchPromises.push(
          this.fetchEnvelopeDetails(contract.template_id)
        );
        templatesCount = templatesCount + 1;
      }
    }

    if (isEmpty(envelopeFetchPromises)) {
      this.abortController = null;
      return;
    }
    this.handleEnvelopeDetails(envelopeFetchPromises, searchText, sortOrder);
  };

  refetchAllTemplates = (fetchPolicy = "no-cache") => {
    this.loading = true;
    this.apolloClient
      .query({
        query: GET_ALL_TEMPLATE_DETAILS,
        variables: { emailId: this.email },
        fetchPolicy,
      })
      .then((result) => {
        this.setStoreData(result.data);
        this.abortController = new AbortController();
        this.processEnvelopeSubscription(this.contracts);
        this.loading = false;
        this.isInitialDataSet = true;
      });
  };

  refetchTemplate = (templateId, include) => {
    this.loading = true;
    this.apolloClient
      .query({
        query: GET_TEMPLATE_DETAILS,
        variables: {
          emailId: this.email,
          templateId: templateId,
          include: include,
        },
        fetchPolicy: "no-cache",
      })
      .then((result) => {
        const contractDetails = JSON.parse(result.data.templateDetails);
        this.updateContract(contractDetails);
        this.loading = false;
      });
  };

  updateContract = (contractDetails) => {
    const _contracts = cloneDeep(this.contracts);
    for (const [index, contract] of _contracts.entries()) {
      if (contract.template_id == contractDetails["template_id"]) {
        const _contract = _contracts[index];
        if (contractDetails["include"].includes("template_details")) {
          _contract["template_name"] = contractDetails["template_name"];
          _contract["template_roles"] = contractDetails["template_roles"];
          _contract["created_at"] = contractDetails["created"];
          _contract["updated_at"] = contractDetails["last_modified"];
          _contract["is_signing_order_enabled"] =
            contractDetails["is_signing_order_enabled"];
          _contract["template_notification_settings"] =
            contractDetails["template_notification_settings"];
        }
        if (contractDetails["include"].includes("envelopes_details")) {
          _contract["envelopes_for_template"] =
            contractDetails["envelopes_for_template"];
          _contract["completed"] = contractDetails["completed"];
          _contract["awaiting"] = contractDetails["awaiting"];
        }
        if (contractDetails["include"].includes("tab_details")) {
          _contract["tab_details"] = contractDetails["tab_details"];
        }
        if (contractDetails["include"].includes("lock_information")) {
          _contract["is_locked"] = contractDetails["is_locked"];
        }
        if (contractDetails["include"].includes("documents")) {
          _contract["template_files"] = contractDetails["template_files"];
        }
        _contract["is_archived"] = contractDetails["is_archived"];
        _contract["plan_name"] = contractDetails["plan_name"];
        _contract["plan_id"] = contractDetails["plan_id"];
        _contract["include"] = contractDetails["include"];
        _contract["fiscal_year"] = contractDetails["fiscal_year"];
        _contract["primary_recipient_role"] =
          contractDetails["primary_recipient_role"];
      }
    }
    this.contracts = _contracts;
  };

  addToContracts = (templateId, include) => {
    this.loading = true;
    this.apolloClient
      .query({
        query: GET_TEMPLATE_DETAILS,
        variables: {
          emailId: this.email,
          templateId: templateId,
          include: include,
        },
        fetchPolicy: "no-cache",
      })
      .then((result) => {
        const contractDetails = JSON.parse(result.data.templateDetails);
        const _contracts = cloneDeep(this.contracts);
        _contracts.push(contractDetails);
        this.contracts = _contracts;
        this.loading = false;
      });
  };

  /**
   * Sets whether the user has all required DocuSign permissions
   * This is used to determine if the user can create/edit contracts
   * @param {boolean} hasAllPermissions - Whether user has all required permissions
   */
  setHasAllPermissions = (hasAllPermissions) => {
    this.hasAllPermissions = hasAllPermissions;
  };
  setRefetchContractPermissions = (refetch) => {
    this.refetchContractPermissions = refetch;
  };
  setMissingUserPermissionsCount = (missingUserPermissionsCount) => {
    this.missingUserPermissionsCount = missingUserPermissionsCount;
  };
  setMissingAccountPermissionsCount = (missingAccountPermissionsCount) => {
    this.missingAccountPermissionsCount = missingAccountPermissionsCount;
  };
  setUserPermissions = (userPermissions) => {
    this.userPermissions = userPermissions;
  };
  setAccountPermissions = (accountPermissions) => {
    this.accountPermissions = accountPermissions;
  };

  calculateAndSetPermissionCounts = () => {
    const { missingAccountPermissionsCount, missingUserPermissionsCount } =
      permissionsCount({
        accountPermissions: this.accountPermissions,
        userPermissions: this.userPermissions,
      });

    this.setMissingAccountPermissionsCount(missingAccountPermissionsCount);
    this.setMissingUserPermissionsCount(missingUserPermissionsCount);

    const hasAllAccountPermissions = missingAccountPermissionsCount === 0;
    const hasAllUserPermissions = missingUserPermissionsCount === 0;
    this.setHasAllPermissions(
      hasAllAccountPermissions && hasAllUserPermissions
    );

    return {
      missingAccountPermissionsCount,
      missingUserPermissionsCount,
    };
  };

  setStoreData = (data) => {
    this.allTemplateDetails = data;
    if (data && data.allTemplateDetails) {
      const localContracts = JSON.parse(data.allTemplateDetails);
      for (const localContract of localContracts) {
        localContract["is_envelope_details_fetched"] = false;
      }

      this.contracts = localContracts;
    }
  };

  onParamsChanged = (searchText, sortOrder) => {
    this.abortController?.abort();
    this.abortController = new AbortController();
    this.processEnvelopeSubscription(this.contracts, searchText, sortOrder);
  };
}

const contractsContext = React.createContext(null);

export const ContractsStoreProvider = (props) => {
  const { children } = props;
  const client = useApolloClient();
  const { email, accessToken } = useAuthStore();
  const store = useLocalStore(
    () => new ContractsStore(client, email, accessToken)
  );

  return (
    <contractsContext.Provider value={store}>
      {store ? children : null}
    </contractsContext.Provider>
  );
};

export const useContractsStore = () => {
  return React.useContext(contractsContext);
};
