import { useQuery } from "@apollo/client";
import { faSpinner as saveIcon } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Switch as HeadlessSwitch } from "@headlessui/react";
import { createCustomer } from "Api/CustomerCreationService";
import { checkMultiClientDomain } from "Api/CheckMultiClientDomainService";
import { getAllInviteTemplates } from "Api/EmailTemplateService";
import {
  Drawer,
  Info,
  Multiselect,
  Select,
  showMessage,
  Switch,
} from "Components";
import { NOTIFICATION_TYPE, SWITCH_STATUS } from "Enums";
import { useAuthStore } from "GlobalStores/AuthStore";
import {
  cloneDeep,
  isEmpty,
  groupBy,
  mapValues,
  omit,
  uniqBy,
  sortBy,
} from "lodash";
import React, { useEffect, useState } from "react";
import {
  connectionOptions,
  getConnectionTypeOptions,
  GET_OPTIONS,
  startMonthOptions,
  statusOptions,
  typeOptions,
  planOptions,
  planSummaryModelOptions,
  profilePermissionOptions,
  warnOnUnlockOptions,
  upstreamEtlVersionOptions,
  crystalCustomPeriodOptions,
  runSettlementReportOptions,
  g2ReviewFormOptions,
  hardDeleteFrequency,
  weeklyFrequencyOptions,
  monthlyFrequencyOptions,
  getFilteredPlanOptions,
} from "../";
import { AddImageIcon } from "../../../icons";
import { FEATURE_CATEGORIES, CATEGORY_ORDER, MODULES, isProdEnv } from "Utils";
import { DrawerHeader } from "~/Components/DrawerHeader";
import { EverButton } from "~/Components/ever-button/EverButton";
import { EverAvatar } from "~/Components/EverAvatar";

export default function NewCustomerDialog(props) {
  const { open, onClose, customersStore } = props;
  const [error, setError] = useState({});
  const { accessToken } = useAuthStore();
  const [loading, setLoading] = useState(false);

  const [name, setName] = useState();
  const [hubspotCompanyId, setHubspotCompanyId] = useState();
  const [domain, setDomain] = useState();
  const [logo, setLogo] = useState();
  const [statementLogo, setStatementLogo] = useState();
  const [previewSrc, setPreviewSrc] = useState();
  const [statementPreviewSrc, setStatementPreviewSrc] = useState();

  const [selectedConnection, setSelectedConnection] = useState(
    connectionOptions[0]
  );
  const [selectedPlanSummaryModel, setSelectedPlanSummaryModel] = useState(
    planSummaryModelOptions[2]
  );
  const [useMultiEngineStormbreaker, setUseMultiEngineStormbreaker] =
    useState(true);
  const [useAgGrid, setUseAgGrid] = useState(false);
  const [
    selectedProfilePicturePermission,
    setSelectedProfilePicturePermission,
  ] = useState(profilePermissionOptions[3]);
  const [selectedWarnOnUnlock, setSelectedWarnOnUnlock] = useState(
    warnOnUnlockOptions[0]
  );

  const [selectedConnectionType, setSelectedConnectionType] = useState(
    getConnectionTypeOptions(connectionOptions[0].name)[0]
  );

  const [currencyOptions, setCurrencyOptions] = useState([]);
  const [selectedCurrency, setSelectedCurrency] = useState(null);

  const [timezoneOptions, setTimezoneOptions] = useState([]);
  const [selectedTimezone, setSelectedTimezone] = useState(null);

  const [selectedStartMonth, setSelectedStartMonth] = useState(
    startMonthOptions[0]
  );

  const [datasheetV2, setDatasheetV2] = useState(true);
  const [showCommissionPercent, setShowCommissionPercent] = useState(false);
  const [settlementV2, setSettlementV2] = useState(true);
  const [allowOnlyAdminsToModifyUserName, setAllowOnlyAdminsToModifyUserName] =
    useState(false);
  const [editLockedQuota, setEditLockedQuota] = useState(false);
  const [enableAutoEnrichment, setEnableAutoEnrichment] = useState(false);
  const [asyncExportDatasheet, setAsyncExportDatasheet] = useState(false);
  const [runSettlementReport, setRunSettlementReport] = useState(
    runSettlementReportOptions[1]
  );
  const [snapshotDataForStatements, setSnapshotDataForStatements] =
    useState(false);
  const [showCommissionBuddy, setShowCommissionBuddy] = useState(true);
  const [showTerritoryPlan, setshowTerritoryPlan] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(statusOptions[1]);
  const [status, setStatus] = useState(false);
  const [selectedSubscriptionPlan, setSelectedSubscriptionPlan] = useState(
    getFilteredPlanOptions(planOptions, status)[0]
  );
  const [enableEverComparison, setEnableEverComparison] = useState(true);
  const [selectedType, setSelectedType] = useState(typeOptions[1]);
  const [type, setType] = useState(false);

  const [hideCategories, setHideCategories] = useState([]);

  const hideCategoryOptions = ["Primary"];
  const [showReturnV1Button, setShowReturnV1Button] = useState(false);
  const [showApprovalFeature, setShowApprovalFeature] = useState(false);
  const [exposeCommReportsInPlan, setExposeCommReportsInPlan] = useState(false);

  const [isMultiClientDomain, setIsMultiClientDomain] = useState(false);
  const [enableConcurrentSessions, setEnableConcurrentSessions] =
    useState(false);
  const [enableSupportUserAccess, setEnableSupportUserAccess] =
    useState(isProdEnv);
  const [enableTsarWebappCustomRoles, setEnableTsarWebappCustomRoles] =
    useState(false);
  const [isSecureAdminUiAuth0UserMgmt, setIsSecureAdminUiAuth0UserMgmt] =
    useState(isProdEnv);
  const [
    allowAdjustmentsToFrozenCommission,
    setAllowAdjustmentsToFrozenCommission,
  ] = useState(false);
  const [splitSummationToLi, setSplitSummationToLi] = useState(false);
  const [showMetrics, setShowMetrics] = useState(false);
  const [avoidIframeInContracts, setAvoidIframeInContracts] = useState(false);
  const [insertMetaDataToVecDb, setInsertMetaDataToVecDb] = useState(false);
  const [isDataSourcesV2, setIsDataSourcesV2] = useState(false); // disable datasheet v2 by default for new customers
  const [showChatGPT, setShowChatGPT] = useState(false);
  const [showPayoutTableBreakdown, setShowPayoutTableBreakdown] =
    useState(false);
  const [chromeExtensionEnabled, setChromeExtensionEnabled] = useState(false);
  const [isolatedSnowflakeDatabase, setIsolatedSnowflakeDatabase] =
    useState(false);
  const [enableEverai, setEnableEverai] = useState(false);
  const [emailInviteTemplateOptions, setEmailInviteTemplateOptions] = useState(
    []
  );
  const [defaultEmailInviteTemplate, setDefaultEmailInviteTemplate] =
    useState("");
  const [selectedEmailInviteTemplate, setSelectedEmailInviteTemplate] =
    useState("");
  const [customCalendar, setCustomCalendar] = useState(false);
  const [showForecast, setShowForecast] = useState(false);
  const [quotaEffectiveDated, setQuotaEffectiveDated] = useState(false);
  const [allowQuotaSettingsOverride, setAllowQuotaSettingsOverride] =
    useState(false);
  const [allowAnnualQuotaEffectiveDated, setallowAnnualQuotaEffectiveDated] =
    useState(false);
  // v1, v2
  const [upstreamEtlVersion, setUpstreamEtlVersion] = useState(
    upstreamEtlVersionOptions[0]
  );
  const [enableCustomWorkflows, setEnableCustomWorkflows] = useState(false);
  const [
    crystalCustomCalendarFuturePeriods,
    setCrystalCustomCalendarFuturePeriods,
  ] = useState(crystalCustomPeriodOptions[5]); // default to 6 periods
  const [modules, setModules] = useState([MODULES.ICM]);

  const { data: optionsData } = useQuery(GET_OPTIONS, {
    fetchPolicy: "no-cache",
    variables: { isClientSpecific: false },
  });
  const [enableHrisIntegration, setEnableHrisIntegration] = useState(false);
  const [newEnableCustomTheme, setNewEnableCustomTheme] = useState(false);
  const [enableSidebarV3, setEnableSidebarV3] = useState(false);
  const [showG2ReviewForm, setShowG2ReviewForm] = useState(
    g2ReviewFormOptions[2]
  );
  const [hardDeleteSyncFrequency, setHardDeleteSyncFrequency] = useState(
    hardDeleteFrequency[0]
  );
  const [dayOfWeek, setDayOfWeek] = useState("*");
  const [dayOfMonth, setDayOfMonth] = useState("*");
  //run sync for multiple period
  const [runSyncMultiplePeriod, setRunSyncMultiplePeriod] = useState(false);

  const [enableExcelUpload, setEnableExcelUpload] = useState(false);

  const [showSupersetDashboard, setShowSupersetDashboard] = useState(true);

  useEffect(() => {
    if (optionsData) {
      const currencyCodes = [];
      const timezones = [];
      (optionsData?.allActiveCountries || []).forEach((country, index) => {
        currencyCodes.push({
          id: index + 1,
          name: country["currencyCode"],
        });
      });
      const timezoneItems = (optionsData?.allNotificationTimezones || []).map(
        (item) => JSON.parse(item)
      );
      timezoneItems.forEach((timezone, index) => {
        if (timezones.indexOf(timezone["label"]) === -1) {
          timezones.push({ id: index + 1, name: timezone["label"] });
        }
      });

      const sortedTimezoneList = timezones.sort((a, b) => {
        const x = a.name.toUpperCase(),
          y = b.name.toUpperCase();
        return x === y ? 0 : x > y ? 1 : -1;
      });

      setTimezoneOptions(sortedTimezoneList);
      setCurrencyOptions(currencyCodes);
      let uniqueOpt = uniqBy(currencyCodes, "name");
      setCurrencyOptions(sortBy(uniqueOpt, ["name"]));
    }
  }, [optionsData]);

  const setTimezoneAndCurrency = () => {
    for (let i = 0; i < currencyOptions.length; i++) {
      if (currencyOptions[i]["name"] === "USD") {
        setSelectedCurrency(currencyOptions[i]);
      }
    }
    for (let i = 0; i < timezoneOptions.length; i++) {
      if (timezoneOptions[i]["name"] === "(GMT+00:00) UTC") {
        setSelectedTimezone(timezoneOptions[i]);
      }
    }
  };

  useEffect(() => {
    setTimezoneAndCurrency();
  }, [currencyOptions, timezoneOptions]);

  useEffect(() => {
    if (logo) {
      let reader = new FileReader();
      reader.onloadend = () => {
        setPreviewSrc(reader.result);
      };
      reader.readAsDataURL(logo);
    }
  }, [logo]);

  useEffect(() => {
    if (statementLogo) {
      let reader = new FileReader();
      reader.onloadend = () => {
        setStatementPreviewSrc(reader.result);
      };
      reader.readAsDataURL(statementLogo);
    }
  }, [statementLogo]);

  useEffect(() => {
    const newStatus =
      selectedStatus?.["name"]?.toLowerCase() ===
      statusOptions[0].name.toLowerCase();
    setStatus(newStatus);
    setSelectedSubscriptionPlan(
      getFilteredPlanOptions(planOptions, newStatus)[0]
    );
    setType(
      selectedType?.["name"]?.toLowerCase() ===
        typeOptions[0].name.toLowerCase()
    );
  }, [selectedStatus, selectedType]);

  useEffect(() => {
    getInviteTemplates();
  }, []);

  const resetForm = () => {
    setName();
    setHubspotCompanyId();
    setDomain();
    setLogo();
    setStatementLogo();
    setPreviewSrc();
    setStatementPreviewSrc();
    setSelectedConnection(connectionOptions[0]);
    setSelectedConnectionType(
      getConnectionTypeOptions(connectionOptions[0].name)[0]
    );
    setTimezoneAndCurrency();
    setSelectedStartMonth(startMonthOptions[0]);
    setDatasheetV2(true);
    setSelectedType(typeOptions[1]);
    setSelectedStatus(statusOptions[1]);
    setHideCategories([]);
    setError({});
    setSettlementV2(false);
    setAllowOnlyAdminsToModifyUserName(false);
    setEditLockedQuota(false);
    setEnableAutoEnrichment(false);
    setAsyncExportDatasheet(false);
    setRunSettlementReport(runSettlementReportOptions[1]);
    setSnapshotDataForStatements(false);
    setEnableEverComparison(true);
    setShowReturnV1Button(false);
    setIsMultiClientDomain(false);
    setExposeCommReportsInPlan(false);
    setEnableConcurrentSessions(false);
    setEnableSupportUserAccess(isProdEnv);
    setEnableTsarWebappCustomRoles(false);
    setIsSecureAdminUiAuth0UserMgmt(isProdEnv);
    setAllowAdjustmentsToFrozenCommission(false);
    setSplitSummationToLi(false);
    setShowMetrics(false);
    setAvoidIframeInContracts(false);
    setInsertMetaDataToVecDb(false);
    setIsDataSourcesV2(false);
    setShowChatGPT(false);
    setShowPayoutTableBreakdown(false);
    setIsolatedSnowflakeDatabase(false);
    setChromeExtensionEnabled(false);
    setEnableEverai(false);
    setUseMultiEngineStormbreaker(false);
    setUseAgGrid(false);
    setSelectedPlanSummaryModel(planSummaryModelOptions[2]);
    setSelectedEmailInviteTemplate(defaultEmailInviteTemplate);
    setSelectedProfilePicturePermission(profilePermissionOptions[3]);
    setSelectedWarnOnUnlock(warnOnUnlockOptions[0]);
    setCustomCalendar(false);
    setShowForecast(false);
    setQuotaEffectiveDated(false);
    setAllowQuotaSettingsOverride(false);
    setallowAnnualQuotaEffectiveDated(false);
    setUpstreamEtlVersion(upstreamEtlVersionOptions[0]);
    setEnableCustomWorkflows(false);
    setCrystalCustomCalendarFuturePeriods(crystalCustomPeriodOptions[5]);
    setNewEnableCustomTheme(false);
    setEnableSidebarV3(false);
    setShowG2ReviewForm(g2ReviewFormOptions[2]);
    setModules([MODULES.ICM]);
    setRunSyncMultiplePeriod(false);
    setHardDeleteSyncFrequency(hardDeleteFrequency[0]);
    setDayOfWeek("*");
    setDayOfMonth("*");
    setEnableExcelUpload(false);
    setShowSupersetDashboard(true);
  };

  const onCloseWrapper = () => {
    onClose();
    resetForm();
  };

  const validDomain = (domain) => {
    const pattern = new RegExp(
      "^((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,})(\\#[-a-z\\d_]*)?$",
      "i"
    );
    return !!pattern.test(domain);
  };

  const validateDomain = (domain) => {
    if (!isEmpty(domain)) {
      let e = cloneDeep(error);
      e.domain = {};
      if (!validDomain(domain)) {
        e.domain.msg = "Please provide a valid domain";
      }
      setError(e);
    }
  };
  const checkMultiDomain = async (domain) => {
    const response = await checkMultiClientDomain(domain, accessToken);
    if (response.ok) {
      const data = await response.json();
      setIsMultiClientDomain(data.result);
    }
  };

  const getInviteTemplates = async () => {
    const response = await getAllInviteTemplates(accessToken);
    if (response.ok) {
      const data = await response.json();
      const inviteTemplates = [];
      data.emailInviteTemplates.forEach((item) => {
        inviteTemplates.push({
          id: item.templateId,
          name: item.templateName,
        });
        if (item.templateName === "email-password-invite") {
          setDefaultEmailInviteTemplate({
            id: item.templateId,
            name: item.templateName,
          });
          setSelectedEmailInviteTemplate({
            id: item.templateId,
            name: item.templateName,
          });
        }
      });
      setEmailInviteTemplateOptions(inviteTemplates);
    }
  };

  const validate = () => {
    let pass = true;
    let e = {};
    if (isEmpty(name)) {
      e.name = {};
      e.name.msg = "Please provide a name for the new customer";
      pass = false;
    }

    if (isEmpty(domain)) {
      e.domain = {};
      e.domain.msg = "Please provide a domain";
      pass = false;
    } else if (!validDomain(domain)) {
      e.domain = {};
      e.domain.msg = "Please provide a valid domain";
      pass = false;
    }
    if (hardDeleteSyncFrequency?.name === "Weekly" && dayOfWeek === "*") {
      e.frequency = {};
      e.frequency.msg = "Please choose the day of the week";
      pass = false;
    }
    if (hardDeleteSyncFrequency?.name === "Monthly" && dayOfMonth === "*") {
      e.frequency = {};
      e.frequency.msg = "Please choose the day of the month";
      pass = false;
    }
    // if (logo) {
    //   e.logo = {};
    //   e.logo.msg = "Please upload a logo";
    //   pass = false;
    // }

    setError(e);
    return pass;
  };

  // Convert timezone value from timezone label
  const getTimezoneValue = (timezoneLabel) => {
    if (!optionsData || !optionsData.allNotificationTimezones) {
      return "";
    }
    const timezoneItems = optionsData.allNotificationTimezones.map((item) =>
      JSON.parse(item)
    );
    return timezoneItems.find((tz) => tz.label === timezoneLabel).value;
  };

  const onSubmit = () => {
    setLoading(true);
    const pass = validate();
    if (pass) {
      let data = new FormData();
      data.append("name", name);
      data.append("hubspotCompanyId", hubspotCompanyId);
      data.append("domain", domain);
      data.append("file", logo);
      data.append("statementLogo", statementLogo);
      data.append("connectionType", selectedConnectionType["name"]);
      data.append("connectionName", selectedConnection["name"]);
      data.append("subscriptionPlan", selectedSubscriptionPlan["name"]);
      data.append("planSummaryModel", selectedPlanSummaryModel["name"]);
      data.append("baseCurrency", selectedCurrency["name"]);
      data.append("fiscalStartMonth", selectedStartMonth["name"]);
      data.append("timezone", getTimezoneValue(selectedTimezone["name"]));
      data.append("datasheetV2", datasheetV2);
      data.append("showCommissionPercent", showCommissionPercent);
      data.append("status", status);
      data.append("type", type);
      data.append("hideCategories", hideCategories);
      data.append("settlementV2", settlementV2);
      data.append(
        "allowOnlyAdminsToModifyUserName",
        allowOnlyAdminsToModifyUserName
      );
      data.append("editLockedQuota", editLockedQuota);
      data.append("isAutoEnrichReport", enableAutoEnrichment);
      data.append("asyncExportDatasheet", asyncExportDatasheet);
      data.append("runSettlementReport", runSettlementReport["key"]);
      data.append("snapshotDataForStatements", snapshotDataForStatements);
      data.append("showCommissionBuddy", showCommissionBuddy);
      data.append("showTerritoryPlan", showTerritoryPlan);
      data.append("enableEverComparison", enableEverComparison);
      data.append("showReturnV1Button", showReturnV1Button);
      data.append("showApprovalFeature", showApprovalFeature);
      data.append("exposeCommReportsInPlan", exposeCommReportsInPlan);
      data.append("enableConcurrentSessions", enableConcurrentSessions);
      data.append("enableSupportUserAccess", enableSupportUserAccess);
      data.append("enableTsarWebappCustomRoles", enableTsarWebappCustomRoles);
      data.append("isSecureAdminUiAuth0UserMgmt", isSecureAdminUiAuth0UserMgmt);
      data.append(
        "allowAdjustmentsToFrozenCommission",
        allowAdjustmentsToFrozenCommission
      );
      data.append("splitSummationToLi", splitSummationToLi);
      data.append("showMetrics", showMetrics);
      data.append("avoidIframeInContracts", avoidIframeInContracts);
      data.append("useMultiEngineStormbreaker", useMultiEngineStormbreaker);
      data.append("useAggridForPdfExport", useAgGrid);
      data.append("showDataSourcesV2", isDataSourcesV2);
      data.append("showChatgpt", showChatGPT);
      data.append("showPayoutTableBreakdown", showPayoutTableBreakdown);
      data.append("isolatedSnowflakeDatabase", isolatedSnowflakeDatabase);
      data.append("chromeExtensionEnabled", chromeExtensionEnabled);
      data.append("enableEverai", enableEverai);
      data.append("emailInviteTemplateId", selectedEmailInviteTemplate.id);
      data.append(
        "profilePicturePermission",
        selectedProfilePicturePermission["name"]
      );
      data.append("warnOnUnlock", selectedWarnOnUnlock["name"]);
      data.append("customCalendar", customCalendar);
      data.append("showForecast", showForecast);
      data.append("quotaEffectiveDated", quotaEffectiveDated);
      data.append("allowQuotaSettingsOverride", allowQuotaSettingsOverride);
      data.append(
        "allowAnnualQuotaEffectiveDated",
        allowAnnualQuotaEffectiveDated
      );
      data.append("upstreamEtlVersion", upstreamEtlVersion.name);
      data.append("enableHrisIntegration", enableHrisIntegration);
      data.append("enableCustomWorkflows", enableCustomWorkflows);
      data.append(
        "crystalCustomCalendarFuturePeriods",
        crystalCustomCalendarFuturePeriods["key"]
      );
      data.append("isReportObjectSharded", String(true));
      data.append("enableCustomTheme", newEnableCustomTheme);
      data.append("enableSidebarV3", enableSidebarV3);
      data.append("showG2ReviewForm", showG2ReviewForm["name"]);
      data.append("modules", modules);
      //run sync for multiple periods
      data.append("runSyncForMultiplePeriod", runSyncMultiplePeriod);
      data.append(
        "hardDeleteSyncFrequency",
        hardDeleteSyncFrequency?.name || "None"
      );
      data.append(
        "dayOfWeek",
        dayOfWeek?.name === undefined ? "*" : dayOfWeek?.id
      );
      data.append(
        "dayOfMonth",
        dayOfMonth?.name === undefined ? "*" : dayOfMonth?.name
      );
      data.append("uploadExcelFilesInCustomObject", enableExcelUpload);
      data.append("showSupersetDashboard", showSupersetDashboard);

      createCustomer(data, accessToken)
        .then((res) => {
          if (res.ok) {
            onCloseWrapper();
            showMessage("Completed", {
              description: "Customer created successfully",
            });
            customersStore.customersRefetch();
          } else {
            let e = {};
            res.json().then((ress) => {
              if (ress?.message) {
                showMessage(ress.message, {
                  type: NOTIFICATION_TYPE.error,
                });
              } else if (ress?.errors?.length) {
                for (let i = 0; i < ress.errors.length; i++) {
                  if (ress.errors[i] === "Name Already exist") {
                    e.name = {};
                    e.name.msg = "Customer name already taken";
                  }
                  if (ress.errors[i] === "Domain Already exist") {
                    e.domain = {};
                    e.domain.msg = "Domain already taken";
                  }
                  if (ress.errors[i] === "Invalid Domain") {
                    e.domain = {};
                    e.domain.msg = "Invalid Domain";
                  }
                }
                setError(e);
              }
            });
          }
          setLoading(false);
        })
        .catch((err) => {
          console.log(err);
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  };

  const inputFieldObjects = [
    {
      key: "customer_name",
      type: "input",
      title: "Customer Name",
      value: name,
      error: error?.name?.msg,
      onChange: setName,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "hubspot_company_id",
      type: "input",
      title: "Hubspot Company ID",
      value: hubspotCompanyId,
      onChange: setHubspotCompanyId,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "domain",
      type: "input",
      title: "Domain",
      value: domain,
      error: error?.domain?.msg,
      onChange: setDomain,
      onBlur: (e) => {
        validateDomain(e.target.value);
        checkMultiDomain(e.target.value);
      },
      notes: (
        <>
          {isMultiClientDomain === true && (
            <b className="mt-2 text-sm text-ever-base-content-low">
              This domain already exists for another client
            </b>
          )}
          <p className="mt-2 text-sm text-ever-base-content-low">
            <b>Note:</b> Domain name should not contain special characters
            except &lsquo;<b>.</b>&rsquo; and &lsquo;
            <b>-</b>&rsquo;
          </p>
          <p className="mt-2 text-sm text-ever-base-content-low">
            Some valid domain formats are{" "}
            <b>everstage.com, everstage.admin.in, everstage-admin.org</b>
          </p>
        </>
      ),
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "logo",
      type: "image",
      title: "Logo",
      value: previewSrc,
      alt: name,
      onChange: setLogo,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "modules",
      type: "multiSelect",
      title: "Modules",
      value: modules,
      options: Object.values(MODULES),
      onChange: setModules,
    },
    {
      key: "auth_connection_name",
      type: "select",
      title: "Auth Connection Name",
      value: selectedConnection,
      options: connectionOptions,
      onChange: (value) => {
        setSelectedConnection(value);
        setSelectedEmailInviteTemplate(
          emailInviteTemplateOptions.find(
            (temp) => temp.name === value.name + "-invite"
          )
        );
        if (selectedConnection !== value) {
          setSelectedConnectionType(getConnectionTypeOptions(value.name)[0]);
        }
      },
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "auth_connection_type",
      type: "select",
      title: "Auth Connection Type",
      value: selectedConnectionType,
      options: getConnectionTypeOptions(selectedConnection.name),
      onChange: setSelectedConnectionType,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "subscription_plan",
      type: "select",
      title: "Subscription Plan",
      value: selectedSubscriptionPlan,
      options: getFilteredPlanOptions(planOptions, status),
      onChange: setSelectedSubscriptionPlan,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "email_invite_template",
      type: "select",
      title: "Email Invite Template",
      value: selectedEmailInviteTemplate,
      options: emailInviteTemplateOptions,
      onChange: setSelectedEmailInviteTemplate,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "plan_summary_model",
      type: "select",
      title: "Plan Summary ChatGPT Model",
      value: selectedPlanSummaryModel,
      options: planSummaryModelOptions,
      onChange: setSelectedPlanSummaryModel,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "base_currency",
      type: "select",
      title: "Base Currency",
      value: selectedCurrency,
      options: currencyOptions,
      onChange: setSelectedCurrency,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "timezone",
      type: "select",
      title: "Timezone",
      value: selectedTimezone,
      options: timezoneOptions,
      onChange: setSelectedTimezone,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "fiscal_start_month",
      type: "select",
      title: "Fiscal Start Month",
      value: selectedStartMonth,
      options: startMonthOptions,
      onChange: setSelectedStartMonth,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    // {
    //   key: "datasheet_v2",
    //   type: "switch",
    //   title: "Datasheet V2",
    //   description: "To enable datasheet v2 experience for an account.",
    //   value: datasheetV2,
    //   onChange: setDatasheetV2,
    //   category: FEATURE_CATEGORIES.NOTIFICATIONS,
    // },
    {
      key: "show_commission_percent",
      type: "switch",
      title: "Show Commission Percent",
      description: "Shows commission percent in statements",
      value: showCommissionPercent,
      onChange: setShowCommissionPercent,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "show_commission_buddy",
      type: "switch",
      title: "Show Commission Buddy",
      description: "Show Commission Percent in Statements",
      value: showCommissionBuddy,
      onChange: setShowCommissionBuddy,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "show_territory_plan",
      type: "switch",
      title: "Show Territory Plan",
      description: "Show TQM",
      value: showTerritoryPlan,
      onChange: setshowTerritoryPlan,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "settlement_v2",
      type: "switch",
      title: "Settlement using payout snapshot",
      description:
        "To use payout snapshot data to calculate settlements for the client.",
      value: settlementV2,
      onChange: setSettlementV2,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "allow_only_admins_to_modify_user_name",
      type: "switch",
      title: "Allow only admins to modify user name",
      value: allowOnlyAdminsToModifyUserName,
      onChange: setAllowOnlyAdminsToModifyUserName,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "edit_locked_quota",
      type: "switch",
      title: "Edit Locked Quota",
      description: "To allow editing of quota in locked periods.",
      value: editLockedQuota,
      onChange: setEditLockedQuota,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "is_auto_enrich_report",
      type: "switch",
      title: "Auto Enrich Report",
      description: "To allow auto enrichment of commission report",
      value: enableAutoEnrichment,
      onChange: setEnableAutoEnrichment,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "async_export_datasheet",
      type: "switch",
      title: "Async Export Datasheet",
      description: "To enable async export of datasheet",
      value: asyncExportDatasheet,
      onChange: setAsyncExportDatasheet,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "run_settlement_report",
      type: "select",
      title: "Enable settlement report",
      description:
        "This will enable settlement report option in the datasheet page.",
      options: runSettlementReportOptions,
      value: runSettlementReport,
      onChange: setRunSettlementReport,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "snapshot_data_for_statements",
      type: "switch",
      title: "Use Snapshot Data for Statements",
      description:
        "Option to use snapshot data for statements. Turn this on only when payout snapshot ETL is turned on",
      value: snapshotDataForStatements,
      onChange: setSnapshotDataForStatements,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "hide_categories",
      type: "multiSelect",
      title: "Hide Categories",
      value: hideCategories,
      options: hideCategoryOptions,
      onChange: setHideCategories,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "status",
      type: "select",
      title: "Status",
      value: selectedStatus,
      options: statusOptions,
      onChange: setSelectedStatus,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "type",
      type: "select",
      title: "Type",
      value: selectedType,
      options: typeOptions,
      onChange: setSelectedType,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "show_approval_feature",
      type: "switch",
      title: "Show Approval Feature",
      description: "To enable approvals feature for an account.",
      value: showApprovalFeature,
      onChange: setShowApprovalFeature,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "expose_comm_reports_in_plan",
      type: "switch",
      title: "Expose commission reports in commission plan",
      description:
        "Option to use Commissions and Quota Attainment data in Commission plan Criteria.",
      value: exposeCommReportsInPlan,
      onChange: setExposeCommReportsInPlan,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "allow_adjusmtments_to_frozen_commission",
      type: "switch",
      title: "Allow Adjustments to Locked Commission",
      description: "Option to add Commission Adjustments to Locked Statements",
      value: allowAdjustmentsToFrozenCommission,
      onChange: setAllowAdjustmentsToFrozenCommission,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "show_chatgpt",
      type: "switch",
      title: "Show ChatGPT",
      description:
        "SSummarize Plan Document details and allow users to get specific details through a chat interface",
      value: showChatGPT,
      onChange: setShowChatGPT,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "isolated_snowflake_database",
      type: "switch",
      title: "Isolated Snowflake Database",
      description:
        "To isolate the client's data in a separate database from other clients",
      value: isolatedSnowflakeDatabase,
      onChange: setIsolatedSnowflakeDatabase,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
      isDisabled: false,
    },
    {
      key: "chrome_extension_enabled",
      type: "switch",
      title: "Enable Chrome extension",
      description: "Enable everstage as a chrome extension for the client",
      value: chromeExtensionEnabled,
      onChange: setChromeExtensionEnabled,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "enable_everai",
      type: "switch",
      title: "Enable everAI",
      description: "Enable everAI RBAC for the client",
      value: enableEverai,
      onChange: setEnableEverai,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "show_return_v1_button",
      type: "switch",
      title: "Allow usage of the copy-paste legacy (v1) data import experience",
      description: "Allow v1 data import experience.",
      value: showReturnV1Button,
      onChange: setShowReturnV1Button,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "show_superset_dashboard",
      type: "switch",
      title: "Show Analytics",
      description: "To enable analytics (superset) feature for an account.",
      value: showSupersetDashboard,
      defaultValue: SWITCH_STATUS.ON,
      onChange: setShowSupersetDashboard,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "profile_picture_permission",
      type: "select",
      title: "Profile Picture Permission",
      value: selectedProfilePicturePermission,
      options: profilePermissionOptions,
      onChange: setSelectedProfilePicturePermission,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "show_payout_table_breakdown",
      type: "switch",
      title: "Show Payout Breakdown Table",
      description:
        "Show payout details when arrears are processed and payments are registered.",
      value: showPayoutTableBreakdown,
      onChange: setShowPayoutTableBreakdown,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "warn_on_unlock",
      type: "select",
      title: "Warning on Unlock",
      value: selectedWarnOnUnlock,
      options: warnOnUnlockOptions,
      onChange: setSelectedWarnOnUnlock,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "upstream_etl_version",
      type: "select",
      title: "Upstream ETL Version",
      description:
        "Enhanced Upstream ETL with batching and checkpointing features.",
      value: upstreamEtlVersion,
      options: upstreamEtlVersionOptions,
      onChange: setUpstreamEtlVersion,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "enable_concurrent_sessions",
      type: "switch",
      title: "Enable Concurrent Sessions",
      description:
        "To enable concurrent sessions for all users under the client. This will allow users to login from multiple devices/ browsers at the same time.",
      value: enableConcurrentSessions,
      onChange: setEnableConcurrentSessions,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "custom_calendar",
      type: "switch",
      title: "Show Custom Calendar",
      value: customCalendar,
      onChange: setCustomCalendar,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "show_forecast",
      type: "switch",
      title: "Show Forecast",
      value: showForecast,
      onChange: setShowForecast,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "enable_custom_theme",
      type: "switch",
      title: "Enable Custom Theme",
      value: newEnableCustomTheme,
      onChange: setNewEnableCustomTheme,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "enable_sidebar_v3",
      type: "switch",
      title: "Enable Sidebar v3",
      value: enableSidebarV3,
      onChange: setEnableSidebarV3,
    },
    {
      key: "show_g2_review_form",
      type: "select",
      options: g2ReviewFormOptions,
      title: "Enable G2 Review Form",
      value: showG2ReviewForm,
      defaultValue: showG2ReviewForm?.name,
      onChange: setShowG2ReviewForm,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "quota_effective_dated",
      type: "switch",
      title: "Quota Effective Dated",
      value: quotaEffectiveDated,
      onChange: setQuotaEffectiveDated,
    },
    {
      key: "allow_quota_settings_override",
      type: "switch",
      title: "Allow Quota Settings Override",
      value: allowQuotaSettingsOverride,
      onChange: setAllowQuotaSettingsOverride,
    },
    {
      key: "allow_annual_quota_effective_dated",
      type: "switch",
      title: "Allow Annual Quota Effective Dated",
      description:
        "To allow setting of effective dated annual quotas with an annual schedule.",
      value: allowAnnualQuotaEffectiveDated,
      onChange: setallowAnnualQuotaEffectiveDated,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "statementLogo",
      type: "image",
      title: "Statement Logo",
      value: statementPreviewSrc,
      alt: "statement logo",
      onChange: setStatementLogo,
      category: FEATURE_CATEGORIES.BASIC_DETAILS,
    },
    {
      key: "enable_hris_integration",
      type: "switch",
      title: "Enable HRIS Integration",
      description: "To enable HRIS Integration for the client",
      value: enableHrisIntegration,
      onChange: setEnableHrisIntegration,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "enable_custom_workflows",
      type: "switch",
      title: "Enable Custom Workflows",
      value: enableCustomWorkflows,
      onChange: setEnableCustomWorkflows,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
      isDisabled: ["PRODUCTION", "PRODUCTION-EU"].includes(
        process.env.REACT_APP_ACTUAL_ENV
      ),
    },
    {
      key: "crystal_custom_calendar_future_periods",
      type: "select",
      title:
        "Number of future periods for Crystal projection - Custom Calendar",
      description:
        "Number of upcoming periods displayed in the dropdown menu when projecting commissions in Crystal.",
      value: crystalCustomCalendarFuturePeriods,
      onChange: setCrystalCustomCalendarFuturePeriods,
      category: FEATURE_CATEGORIES.COMMISSIONS,
      options: crystalCustomPeriodOptions,
    },
    {
      key: "enable_support_user_access",
      type: "switch",
      title: "Enable TSAR",
      description:
        "If this is turned off, login to client's account is possible by common-support-email. If turned on, only support users with a membership can access the client.",
      value: enableSupportUserAccess,
      onChange: setEnableSupportUserAccess,
      category: FEATURE_CATEGORIES.SECURITY,
      isDisabled: isProdEnv,
    },
    {
      key: "enable_tsar_webapp_custom_roles",
      type: "switch",
      title: "Enable TSAR Webapp Custom Roles",
      description:
        "If this is turned off, only Power Admin role is available for TSAR Memberships.",
      value: enableTsarWebappCustomRoles,
      onChange: setEnableTsarWebappCustomRoles,
      category: FEATURE_CATEGORIES.SECURITY,
    },
    {
      key: "is_secure_admin_ui_auth0_user_mgmt",
      type: "switch",
      title: "Secure Admin UI Auth0 User Management",
      description:
        "This flag is for Admin UI; where the user management is done at the client level. Enabling this will - 1. Disable the ability to create new users from the Admin UI. 2. Prevent passwords exposure. 3. Send password reset links directly to the client's high-profile users.",
      value: isSecureAdminUiAuth0UserMgmt,
      onChange: setIsSecureAdminUiAuth0UserMgmt,
      category: FEATURE_CATEGORIES.SECURITY,
    },
  ];

  const weeklyItem = {
    key: "day_of_week",
    type: "select",
    title: "Day Of The Week",
    options: weeklyFrequencyOptions,
    value: dayOfWeek,
    onChange: setDayOfWeek,
    error: error?.frequency?.msg,
  };

  const monthlyItem = {
    key: "day_of_month",
    type: "select",
    title: "Day Of The Month",
    options: monthlyFrequencyOptions,
    value: dayOfMonth,
    onChange: setDayOfMonth,
    error: error?.frequency?.msg,
  };

  const inputFieldObjectsForDevs = [
    {
      key: "split_summation_to_li",
      type: "switch",
      title: "Split Summation Commission to Line Items",
      description: "Option to split summation commission to line items",
      value: splitSummationToLi,
      onChange: setSplitSummationToLi,
      category: FEATURE_CATEGORIES.COMMISSIONS,
    },
    {
      key: "show_metrics",
      type: "switch",
      title: "Show Metrics",
      description:
        "To enable the metrics feature for an account. This will allow users to create and view metrics in the UI.",
      value: showMetrics,
      onChange: setShowMetrics,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    {
      key: "avoid_iframe_in_contracts",
      type: "switch",
      title: "Avoid Iframe in Contracts",
      description: "To avoid using iframe in contracts.",
      value: avoidIframeInContracts,
      onChange: setAvoidIframeInContracts,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "insert_meta_data_to_vec_db",
      type: "switch",
      title: "Insert Meta Data to Vector DB",
      description: "",
      value: insertMetaDataToVecDb,
      onChange: setInsertMetaDataToVecDb,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    // {
    //   key: "show_data_sources_v2",
    //   type: "switch",
    //   title: "Show data sources v2",
    //   description: "To enable the new v2 UI for datasheets and data sources.",
    //   value: isDataSourcesV2,
    //   onChange: setIsDataSourcesV2,
    //   category: FEATURE_CATEGORIES.NEW_FEATURE,
    // },
    {
      key: "use_multi_engine_stormbreaker",
      type: "switch",
      title: "Use multi-engine Stormbreaker for datasheet data read",
      description:
        "Enables the use of multi-engine Stormbreaker for reading datasheet data, enhancing performance. This also set the flag for take_ds_snapshot to true.",
      value: useMultiEngineStormbreaker,
      onChange: setUseMultiEngineStormbreaker,
      category: FEATURE_CATEGORIES.INTERNAL,
    },
    // {
    //   key: "run_sync_for_multiple_period",
    //   type: "switch",
    //   title: "Use commission sync for multiple period",
    //   description:
    //     "Enables user to run commission sync for multiple period instead of just one period",
    //   value: runSyncMultiplePeriod,
    //   onChange: setRunSyncMultiplePeriod,
    //   category: FEATURE_CATEGORIES.COMMISSIONS,
    // },
    {
      key: "upload_excel_files_in_custom_object",
      type: "switch",
      title: "Support .xls/.xlsx data upload",
      description:
        "Enables user to upload .xls/.xlsx file types in custom objects",
      value: enableExcelUpload,
      onChange: setEnableExcelUpload,
      category: FEATURE_CATEGORIES.NEW_FEATURE,
    },
    {
      key: "use_aggrid_for_pdf_export",
      type: "switch",
      title: "Use AG Grid in Statements PDF",
      description:
        "Turning this off will increase the performance of Statements PDF",
      value: useAgGrid,
      onChange: setUseAgGrid,
      category: FEATURE_CATEGORIES.PERFORMANCE_SPECIFIC,
    },
    {
      key: "hard_delete_sync_frequency",
      type: "select",
      title: "Hard Delete Frequency",
      options: hardDeleteFrequency,
      value: hardDeleteSyncFrequency,
      defaultValue: hardDeleteSyncFrequency?.name,
      onChange: setHardDeleteSyncFrequency,
    },
    hardDeleteSyncFrequency?.name === "Weekly" ? weeklyItem : {},
    hardDeleteSyncFrequency?.name === "Monthly" ? monthlyItem : {},
  ];

  const renderCustomerFields = (props) => {
    const {
      type = "",
      title = "",
      value = "",
      defaultValue = "",
      key,
      onChange = () => {},
      options = [],
      error = "",
      alt = "",
      description = "",
      imageSizeDiscalimer = false,
      ...remainingProps
    } = props;
    let content = "";
    let bodyClassName = "";

    switch (type) {
      case "input":
        content = (
          <input
            type="text"
            name={key}
            id={key}
            className="block w-full border-ever-base-300 sm:text-sm focus:ring-ever-base-500 focus:border-ever-base-500 rounded-md"
            placeholder={value}
            onChange={(e) => onChange(e.target.value)}
            {...remainingProps}
          />
        );
        break;
      case "select":
        content = (
          <Select
            label=""
            options={options}
            selected={value}
            setSelected={onChange}
          />
        );
        break;
      case "multiSelect":
        content = (
          <Multiselect
            label=""
            listOptions={options}
            onChange={(_selectedOptions) => onChange(_selectedOptions)}
            defaultSelectedOptions={value}
          />
        );
        break;
      case "switch":
        bodyClassName = "h-9 flex items-center";
        content = (
          <HeadlessSwitch.Group
            as="div"
            className="flex items-center justify-between"
          >
            <Switch
              id={key}
              enabled={value}
              toggleEnabled={() => onChange(!value)}
              {...remainingProps}
            />
          </HeadlessSwitch.Group>
        );
        break;
      case "image":
        content = (
          <div className="max-w-lg flex justify-center items-center px-6 py-4 border-2 border-ever-base-300 border-dashed rounded-md">
            {value ? (
              <EverAvatar src={value} name={alt} size="40" />
            ) : (
              <AddImageIcon />
            )}
            <div className="space-y-1 text-center pl-3">
              <div className="flex items-center text-sm text-ever-base-content-low">
                <label
                  htmlFor={key}
                  className="relative cursor-pointer bg-ever-base rounded-md font-medium text-ever-primary hover:text-ever-primary focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary"
                >
                  <span>Upload a file</span>
                  <input
                    id={key}
                    name="file-upload"
                    type="file"
                    className="sr-only"
                    onChange={(e) => onChange(e.target.files[0])}
                  />
                </label>
                <span className="pl-1">or drag and drop</span>
              </div>
              <div className="text-xs text-ever-base-content-mid">
                PNG, JPG, GIF up to 10MB
              </div>
              {imageSizeDiscalimer ? (
                <div className="text-xs text-ever-base-content-mid">
                  Recommended logo size - 112 x 32 px
                </div>
              ) : (
                ""
              )}
            </div>
          </div>
        );
        break;
      default:
        content = value || "-";
        break;
    }

    return (
      <div key={key}>
        <div className="text-lg font-bold text-ever-base-content-mid flex gap-1">
          {title}{" "}
          {description && <Info className="mt-1" description={description} />}
        </div>
        <div
          className={`mt-1 break-all text-sm text-ever-base-content ${bodyClassName}`}
        >
          {content}
        </div>
        {error ? (
          <div
            className={`text-ever-base-content sm:col-span-2 ${bodyClassName}`}
          >
            <p className="mt-2 text-sm text-red-600" id="name-error">
              {error}
            </p>
          </div>
        ) : null}
      </div>
    );
  };

  const renderAllFeatures = () => {
    const allFields = inputFieldObjects.concat(inputFieldObjectsForDevs);
    const featureCategoryMap = mapValues(
      groupBy(allFields, "category"),
      (inputList) => inputList.map((input) => omit(input, "category"))
    );
    let orderedCategories = Object.entries(featureCategoryMap);
    orderedCategories.sort((a, b) => {
      const keyA = a[0];
      const keyB = b[0];
      return CATEGORY_ORDER[keyA] - CATEGORY_ORDER[keyB];
    });

    return orderedCategories.map((entries, idx) => {
      return (
        <div key={idx} className="p-6 flex flex-col gap-3">
          <div key={entries[0]} className="text-xl">
            {entries[0] !== "undefined" ? entries[0] : "Others"}
          </div>

          <div
            className={`border border-solid border-ever-base-400 bg-ever-base shadow-md rounded-xl p-6`}
          >
            <div className="grid lg:grid-cols-3 md:grid-cols-2 sm:grid-cols-1 gap-8">
              {entries[1].map(
                (fields) => fields?.key && renderCustomerFields(fields)
              )}
            </div>
          </div>
        </div>
      );
    });
  };

  useEffect(() => {
    setDayOfWeek("*");
    setDayOfMonth("*");
  }, [hardDeleteSyncFrequency]);

  return (
    <Drawer open={open} onClose={onClose} disableClickOutClose>
      <div className="w-screen max-w-full">
        <form className="h-full divide-y divide-ever-base-200 flex flex-col bg-ever-base overflow-y-auto">
          <div className="flex-1 h-0 overflow-y-auto pt-20">
            <DrawerHeader
              name={`New Customer`}
              onCloseWrapper={onCloseWrapper}
            />
            {renderAllFeatures()}
          </div>
          <div className="flex-shrink-0 px-4 py-4 flex justify-end gap-3 border-0 border-t border-solid border-ever-base-400 bg-ever-base-100">
            <EverButton type="ghost" color="primary" onClick={onCloseWrapper}>
              Cancel
            </EverButton>
            <EverButton
              onClick={onSubmit}
              disabled={loading}
              prependIcon={
                loading && (
                  <FontAwesomeIcon
                    className="fa-spin fa-pulse h-5 w-5 mr-3 "
                    icon={saveIcon}
                  />
                )
              }
            >
              Save
            </EverButton>
          </div>
        </form>
      </div>
    </Drawer>
  );
}
