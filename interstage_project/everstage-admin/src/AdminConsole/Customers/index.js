import { gql, useQuery } from "@apollo/client";
import { showMessage } from "Components";
import { NOTIFICATION_TYPE } from "Enums";
import { useLocalObservable } from "mobx-react";
import { useEffect, useState } from "react";
import Render from "./Render";
import CustomersStore from "./store";

export const GET_OPTIONS = gql`
  query GetOptions($isClientSpecific: Boolean) {
    allActiveCountries(isClientSpecific: $isClientSpecific) {
      currencyCode
    }
    allNotificationTimezones
  }
`;

export const ALL_QUOTA_CATERGORIES_FOR_CLIENT = gql`
  query AllQuotaCategoriesForClient($clientId: String!) {
    allQuotaCategoriesForClient(clientId: $clientId) {
      quotaCategoryName
      displayName
    }
  }
`;

export const connectionOptions = [
  { id: 1, name: "email-password" },
  { id: 2, name: "everstage-social" },
];

export const PlanOptionTypes = {
  LIVE: "LIVE",
  IMPLEMENTATION: "IMPLEMENTATION",
  COMMON: "COMMON",
};

export const planOptions = [
  { id: 1, name: "BASIC", type: PlanOptionTypes.LIVE },
  { id: 2, name: "ENTERPRISE", type: PlanOptionTypes.LIVE },
  { id: 3, name: "MANUAL", type: PlanOptionTypes.COMMON },
  { id: 4, name: "ICU", type: PlanOptionTypes.COMMON },
  { id: 5, name: "IMPL_SLOT_1", type: PlanOptionTypes.IMPLEMENTATION },
  { id: 6, name: "IMPL_SLOT_2", type: PlanOptionTypes.IMPLEMENTATION },
  { id: 7, name: "ETL_SLOT_1", type: PlanOptionTypes.COMMON },
  { id: 8, name: "ETL_SLOT_2", type: PlanOptionTypes.COMMON },
  { id: 9, name: "ETL_SLOT_3", type: PlanOptionTypes.COMMON },
];

export const getFilteredPlanOptions = (options, isLive) => {
  const type = isLive ? PlanOptionTypes.LIVE : PlanOptionTypes.IMPLEMENTATION;
  const planSpecificOptions = options.filter((plan) => plan.type === type);
  const commonOptions = options.filter(
    (plan) => plan.type === PlanOptionTypes.COMMON
  );
  return planSpecificOptions.concat(commonOptions);
};

export const planSummaryModelOptions = [
  { id: 1, name: "gpt-3.5-turbo" },
  { id: 2, name: "gpt-4" },
  { id: 3, name: "gpt-4-1106-preview" },
];

export const hardDeleteFrequency = [
  { id: 0, name: "None" },
  { id: 1, name: "Daily" },
  { id: 2, name: "Weekly" },
  { id: 3, name: "Monthly" },
];

export const upstreamEtlVersionOptions = [
  { id: 1, name: "v1" },
  { id: 2, name: "v2" },
];

export const evaluationModeOptions = [
  { id: 1, name: "vectorize" },
  { id: 2, name: "serial" },
];

export const connectionTypeOptions = {
  emailPassword: [{ id: 1, name: "email-password" }],
  everstageSocial: [
    { id: 2, name: "G-Suite" },
    { id: 3, name: "Salesforce" },
    { id: 4, name: "Okta" },
  ],
};

export const runSettlementReportOptions = [
  { id: 1, name: "Always", key: "always" },
  { id: 2, name: "If needed", key: "if_needed" },
];

export const getConnectionTypeOptions = (connectionName) =>
  connectionName?.toLowerCase() === connectionOptions[0].name.toLowerCase()
    ? connectionTypeOptions.emailPassword
    : connectionTypeOptions.everstageSocial;

export const startMonthOptions = [
  { id: 1, name: "January" },
  { id: 2, name: "February" },
  { id: 3, name: "March" },
  { id: 4, name: "April" },
  { id: 5, name: "May" },
  { id: 6, name: "June" },
  { id: 7, name: "July" },
  { id: 8, name: "August" },
  { id: 9, name: "September" },
  { id: 10, name: "October" },
  { id: 11, name: "November" },
  { id: 12, name: "December" },
];

export const weeklyFrequencyOptions = [
  { id: 1, name: "Sunday" },
  { id: 2, name: "Monday" },
  { id: 3, name: "Tuesday" },
  { id: 4, name: "Wednesday" },
  { id: 5, name: "Thursday" },
  { id: 6, name: "Friday" },
  { id: 7, name: "Saturday" },
];

export const monthlyFrequencyOptions = Array.from({ length: 28 }, (_, i) => ({
  id: i + 1,
  name: i + 1,
}));

export const statusOptions = [
  { id: 1, name: "Live" },
  { id: 2, name: "Not Live" },
];
export const typeOptions = [
  { id: 1, name: "Test" },
  { id: 2, name: "Production" },
];
export const crystalVersionOptions = [
  { id: 2, name: "3", key: "3" },
  { id: 3, name: "3 (Admin Only)", key: "3_admin_only" },
];

export const profilePermissionOptions = [
  { id: 1, name: "NONE" },
  { id: 2, name: "SELF" },
  { id: 3, name: "ADMIN" },
  { id: 4, name: "ALL" },
];

export const warnOnUnlockOptions = [
  {
    id: 1,
    name: "NONE",
  },
  { id: 2, name: "SUMMARY" },
];

export const g2ReviewFormOptions = [
  { id: "Static", name: "Static" },
  { id: "Dynamic", name: "Dynamic" },
  { id: "Off", name: "Off" },
];

export const crystalCustomPeriodOptions = Array.from(
  { length: 30 },
  (_, index) => ({
    id: index + 1,
    name: String(index + 1),
    key: index + 1,
  })
);

export const objectKnowledgeDateQueryStrategies = [
  { id: 1, name: "Postgres", key: "postgres" },
  { id: 2, name: "Snowflake", key: "snowflake" },
];

export const defaultDashboardStatusValues = {
  NONE: "none",
  PROCESSING: "processing",
  DONE: "done",
  ERROR: "error",
};

const GET_CUSTOMERS = gql`
  {
    allClientAdminConsole {
      clientId
      name
      crmCompanyId
      domain
      logoUrl
      statementLogoUrl
      connectionType
      authConnectionName
      baseCurrency
      fiscalStartMonth
      clientNotification
      payeeNotification
      metaInfo {
        isLive
        isTest
        createdAt
        supportEmail
        password
        deleteNotified
      }
      timeZone
      clientFeatures {
        hideCategories
        showCommissionPercent
        showCommissionBuddy
        showTerritoryPlan
        upstreamEtl
        commissionEtl
        showSupersetDashboard
        datasheetV2
        showSalesforceIntegration
        showStatementsV2
        crystalVersion
        enableEverComparison
        databookSyncStrategy
        managerRollupEd
        showApprovalFeature
        exposeCommReportsInPlan
        showRoles
        showDatasheetPermission
        showCustomObjectPermission
        deleteApprovers
        isNewFrozenPayrollEtl
        enableConcurrentSessions
        chromeExtensionEnabled
        allowAdjustmentsToFrozenCommission
        subscriptionPlan
        showChatgpt
        isolatedSnowflakeDatabase
        showReturnV1Button
        showAdvancedFilter
        showStatementsPdf
        planSummaryModel
        evaluationMode
        profilePicturePermission
        warnOnUnlock
        customCalendar
        payoutSnapshotEtl
        settlementV2
        editLockedQuota
        runSettlementReport
        crmHyperlinks
        showPayoutTableBreakdown
        helpDocUserRole
        documentationUrl
        splitSummationToLi
        quotaEffectiveDated
        allowQuotaSettingsOverride
        allowAnnualQuotaEffectiveDated
        showMetrics
        avoidIframeInContracts
        takeDsSnapshot
        insertMetaDataToVecDb
        enableHrisIntegration
        upstreamEtlVersion
        showForecast
        optimizedPayoutSnapshot
        snapshotDataForStatements
        objectKnowledgeDateQueryStrategy
        enableCustomWorkflows
        useMultiEngineStormbreaker
        enableSupportUserAccess
        enableTsarWebappCustomRoles
        isSecureAdminUiAuth0UserMgmt
        crystalCustomCalendarFuturePeriods
        enableRoundingInTierFunctions
        showDataSourcesV2
        analyticsDefaultDashboardStatus
        enableCustomTheme
        enableSidebarV3
        enableEverai
        showG2ReviewForm
        allowOnlyAdminsToModifyUserName
        runSyncForMultiplePeriod
        modules
        isAutoEnrichReport
        uploadExcelFilesInCustomObject
        asyncExportDatasheet
      }
      isDeleted
    }
  }
`;

const Customers = () => {
  const { data, loading, refetch } = useQuery(GET_CUSTOMERS, {
    // fetchPolicy: "no-cache",
    onError: (error) => {
      let errorMessage = "Something went wrong, please logout and login again";
      if (error?.networkError?.statusCode === 404) {
        errorMessage = error?.networkError?.result?.message || errorMessage;
      }
      showMessage(errorMessage, { type: NOTIFICATION_TYPE.ERROR });
    },
  });
  const [customers, setCustomers] = useState(null);

  const store = useLocalObservable(() => new CustomersStore());
  store.setCustomersRefetch(refetch);

  useEffect(() => {
    if (data && data.allClientAdminConsole) {
      setCustomers(data.allClientAdminConsole);
    }
  }, [data]);
  return (
    <Render
      customers={customers}
      customersStore={store}
      loading={loading}
      refetchCustomers={refetch}
    />
  );
};

export default Customers;
