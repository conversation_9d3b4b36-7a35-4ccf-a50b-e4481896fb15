CREATE OR REPLACE FUNCTION client_166_integration_708d5d72_ddb4_410d_993e_da8ae3885e42(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for Rep
        data['Rep'] = source_data.get('Rep')
        
        # Logic for Sherpa_Assist
        data['Sherpa_Assist'] = source_data.get('Sherpa_Assist')
        
        # Logic for Sherpa_Assist_Split
        str_val = source_data.get('Sherpa_Assist_Split')
        try:
            data['Sherpa_Assist_Split'] = int(str_val)
        except Exception:
            data['Sherpa_Assist_Split'] = None
        
        yield (data,)
$$;
