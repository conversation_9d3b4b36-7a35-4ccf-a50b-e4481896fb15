CREATE OR REPLACE FUNCTION client_190_integration_0c9987be_b79f_40a4_abdc_6f327e864efe(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()

        # Logic for custbody_lux_total_sf_qty_sourced
        str_val = source_data.get("custbody_lux_total_sf_qty_sourced")
        if str_val is None:
            data['custbody_lux_total_sf_qty_sourced'] = None
        else:
            try:
                data['custbody_lux_total_sf_qty_sourced'] = float(str_val)
            except Exception:
                data['custbody_lux_total_sf_qty_sourced'] = None

        # Logic for custbody_lux_total_ff_qty_sourced
        str_val = source_data.get("custbody_lux_total_ff_qty_sourced")
        if str_val is None:
            data['custbody_lux_total_ff_qty_sourced'] = None
        else:
            try:
                data['custbody_lux_total_ff_qty_sourced'] = float(str_val)
            except Exception:
                data['custbody_lux_total_ff_qty_sourced'] = None

        # Logic for custbody_cur_member_qty
        str_val = source_data.get("custbody_cur_member_qty")
        if str_val is None:
            data['custbody_cur_member_qty'] = None
        else:
            try:
                data['custbody_cur_member_qty'] = float(str_val)
            except Exception:
                data['custbody_cur_member_qty'] = None

        # Logic for custbody_lux_total_lf_qty_sourced
        str_val = source_data.get("custbody_lux_total_lf_qty_sourced")
        if str_val is None:
            data['custbody_lux_total_lf_qty_sourced'] = None
        else:
            try:
                data['custbody_lux_total_lf_qty_sourced'] = float(str_val)
            except Exception:
                data['custbody_lux_total_lf_qty_sourced'] = None

        yield (data,)
$$;
