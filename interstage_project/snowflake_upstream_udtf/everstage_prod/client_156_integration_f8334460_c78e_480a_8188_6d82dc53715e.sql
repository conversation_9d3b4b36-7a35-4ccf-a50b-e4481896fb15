CREATE OR REPLACE FUNCTION client_156_integration_f8334460_c78e_480a_8188_6d82dc53715e(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
from datetime import datetime
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for First_Fund_Admin_Paid_Invoice__c
        str_val = source_data.get('First_Fund_Admin_Paid_Invoice__c', "")
        try:
            parts = str_val.split("-")
            date_part = parts[-1]
            data['First_Fund_Admin_Paid_Invoice__c'] = datetime.strptime(date_part, "%m/%d/%Y")
        except Exception as e:
            data['First_Fund_Admin_Paid_Invoice__c'] = None
        
        yield (data,)
$$;
