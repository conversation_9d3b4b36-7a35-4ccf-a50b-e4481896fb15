CREATE OR REPLACE FUNCTION client_76_integration_a2e0bd37_1411_44c1_b071_ef7272113dd2(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
from datetime import datetime
from dateutil.parser import parse
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for sql_date
        date_val = source_data.get('sql_date')
        try:
            if date_val:
                data['sql_date'] = parse(date_val)
        except OverflowError as e:
            data['sql_date'] = datetime.fromtimestamp(int(date_val[:10]))
        
        # Logic for automatic_contract_renewal
        acr = source_data.get('automatic_contract_renewal')
        data['automatic_contract_renewal'] = False
        if acr == "true":
            data['automatic_contract_renewal'] = True
        
        yield (data,)
$$;
