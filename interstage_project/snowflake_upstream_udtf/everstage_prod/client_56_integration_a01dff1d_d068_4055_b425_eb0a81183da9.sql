CREATE OR REPLACE FUNCTION client_56_integration_a01dff1d_d068_4055_b425_eb0a81183da9(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
from datetime import datetime
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for line.txn_amount
        txn_amount = source_data.get('line.txn_amount')
        if txn_amount is not None:
            data['line.txn_amount'] = float(txn_amount / 100)
        
        # Logic for line.txn_date
        txn_date = source_data.get('line.txn_date')
        if txn_date:
            data['line.txn_date'] = datetime.utcfromtimestamp(txn_date)
        
        # Logic for line.applied_at
        applied_at = source_data.get('line.applied_at')
        if applied_at:
            data['line.applied_at'] = datetime.utcfromtimestamp(applied_at)
        
        # Logic for line.applied_amount
        applied_amount = source_data.get('line.applied_amount')
        if applied_amount is not None:
            data['line.applied_amount'] = float(applied_amount / 100)
        
        yield (data,)
$$;
