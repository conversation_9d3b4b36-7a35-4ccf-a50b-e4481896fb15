CREATE OR REPLACE FUNCTION client_59_integration_29a5beba_d5ed_4c30_985f_5e9c2159d323(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
import datetime
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for hs_recurring_billing_end_date
        end_date = source_data.get('hs_recurring_billing_end_date')
        try:
            if end_date:
                end_date = int(end_date)
                s = end_date / 1000
                data['hs_recurring_billing_end_date'] = datetime.datetime.fromtimestamp(s).strftime("%Y-%m-%dT%H:%M:%SZ")
            else:
                data['hs_recurring_billing_end_date'] = None
        except Exception:
            data['hs_recurring_billing_end_date'] = None
        
        # Logic for hs_recurring_billing_period
        term = source_data.get('hs_recurring_billing_period')
        try:
            data['hs_recurring_billing_period'] = int(term.replace("P", "").replace("M", ""))
        except Exception:
            data['hs_recurring_billing_period'] = None
        
        yield (data,)
$$;
