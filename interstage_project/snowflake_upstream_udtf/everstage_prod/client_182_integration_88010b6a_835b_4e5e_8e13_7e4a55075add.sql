CREATE OR REPLACE FUNCTION client_182_integration_88010b6a_835b_4e5e_8e13_7e4a55075add(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()

        # Logic for net_connections
        str_val = source_data.get("net_connections")
        if str_val is None:
            data['net_connections'] = None
        else:
            try:
                data['net_connections'] = float(str_val)
            except Exception:
                parsed_str = str_val.replace("-", "")
                data['net_connections'] = -float(parsed_str)

        yield (data,)
$$;
