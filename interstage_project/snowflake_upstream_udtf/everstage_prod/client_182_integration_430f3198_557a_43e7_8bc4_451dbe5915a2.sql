CREATE OR REPLACE FUNCTION client_182_integration_430f3198_557a_43e7_8bc4_451dbe5915a2(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for eligible_sales
        str_val = source_data.get("eligible_sales")
        if str_val is None:
            data['eligible_sales'] = None
        else:
            try:
                data['eligible_sales'] = float(str_val)
            except Exception:
                parsed_str = str_val.replace("-", "")
                data['eligible_sales'] = -float(parsed_str)

        # Logic for total_net_sls_qty
        str_val = source_data.get("total_net_sls_qty")
        if str_val is None:
            data['total_net_sls_qty'] = None
        else:
            try:
                data['total_net_sls_qty'] = float(str_val)
            except Exception:
                parsed_str = str_val.replace("-", "")
                data['total_net_sls_qty'] = -float(parsed_str)

        # Logic for netsales
        str_val = source_data.get("netsales")
        if str_val is None:
            data['netsales'] = None
        else:
            try:
                data['netsales'] = float(str_val)
            except Exception:
                parsed_str = str_val.replace("-", "")
                data['netsales'] = -float(parsed_str)

        # Logic for gross_profit__std
        str_val = source_data.get("gross_profit__std")
        if str_val is None:
            data['gross_profit__std'] = None
        else:
            try:
                data['gross_profit__std'] = float(str_val)
            except Exception:
                parsed_str = str_val.replace("-", "")
                data['gross_profit__std'] = -float(parsed_str)

        yield (data,)
$$;
