CREATE OR REPLACE FUNCTION client_50_integration_46595e38_e07b_4c4c_8cc2_e0ca76591555(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for contract_term
        contract_term = source_data.get('contract_term')
        l = (contract_term or "0").split(' ')
        try:
            if l[0] == "Monthly":
                data['contract_term'] = 1
            else:
                data['contract_term'] = int(l[0])
        except Exception as e:
            data['contract_term'] = 0
        
        yield (data,)
$$;
