CREATE OR REPLACE FUNCTION client_156_integration_533fac7f_53fb_46b3_b2c1_32a0d5d70ab6(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for X1x_Setup_Amount__c
        orig_val = source_data.get('X1x_Setup_Amount__c')
        if orig_val is not None:
            data['X1x_Setup_Amount__c'] = round(orig_val)
        
        # Logic for Added_ARR__c
        orig_val = source_data.get('Added_ARR__c')
        if orig_val is not None:
            data['Added_ARR__c'] = round(orig_val)
        
        # Logic for Added_ARR_Software_Combined__c
        orig_val = source_data.get('Added_ARR_Software_Combined__c')
        if orig_val is not None:
            data['Added_ARR_Software_Combined__c'] = round(orig_val)
        
        # Logic for Added_ACV_Fund_Admin__c
        orig_val = source_data.get('Added_ACV_Fund_Admin__c')
        if orig_val is not None:
            data['Added_ACV_Fund_Admin__c'] = round(orig_val)
        
        # Logic for Added_ACV_Deal_Admin__c
        orig_val = source_data.get('Added_ACV_Deal_Admin__c')
        if orig_val is not None:
            data['Added_ACV_Deal_Admin__c'] = round(orig_val)
        
        yield (data,)
$$;
