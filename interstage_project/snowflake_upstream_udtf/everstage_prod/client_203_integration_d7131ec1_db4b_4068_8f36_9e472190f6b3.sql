CREATE OR REPLACE FUNCTION client_203_integration_d7131ec1_db4b_4068_8f36_9e472190f6b3(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for co_18_propertyvalue__1
        str_val = source_data.get("Property Value")
        if not str_val:
            data["Property Value"] = None
        else:
            str_val = str_val.replace(",", "")
            data["Property Value"] = float(str_val)
        
        yield (data,)
$$;