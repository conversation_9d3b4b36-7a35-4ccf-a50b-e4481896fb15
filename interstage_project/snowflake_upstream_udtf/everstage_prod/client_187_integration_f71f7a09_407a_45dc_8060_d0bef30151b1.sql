CREATE OR REPLACE FUNCTION client_187_integration_f71f7a09_407a_45dc_8060_d0bef30151b1(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()

        # Logic for variable_pay_overage_rate
        variable_pay_overage_rate = source_data.get('variable_pay_overage_rate')
        result = None
        try:
            result = float(variable_pay_overage_rate)
        except Exception:
            result = None

        data['variable_pay_overage_rate'] = result
        yield (data,)
$$;
