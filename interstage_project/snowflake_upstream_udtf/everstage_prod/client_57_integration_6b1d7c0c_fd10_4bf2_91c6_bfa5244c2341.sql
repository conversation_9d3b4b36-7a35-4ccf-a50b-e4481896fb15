CREATE OR REPLACE FUNCTION client_57_integration_6b1d7c0c_fd10_4bf2_91c6_bfa5244c2341(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for hs_recurring_billing_period
        term = source_data.get('hs_recurring_billing_period')
        try:
            data['hs_recurring_billing_period'] = int(term.replace("P", "").replace("M", ""))
        except Exception:
            data['hs_recurring_billing_period'] = None
        
        # Logic for quantity
        quantity = source_data.get('quantity')
        if quantity is not None:
            data['quantity'] = int(quantity)
        
        yield (data,)
$$;
