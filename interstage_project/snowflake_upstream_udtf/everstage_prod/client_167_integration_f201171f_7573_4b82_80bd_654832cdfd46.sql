CREATE OR REPLACE FUNCTION client_167_integration_f201171f_7573_4b82_80bd_654832cdfd46(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
from datetime import datetime
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for StartDate
        date_str = source_data.get('StartDate')
        try:
            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        except ValueError:
            date_obj = datetime.min
        data['StartDate'] = date_obj.strftime("%Y-%m-%d")
        
        yield (data,)
$$;
