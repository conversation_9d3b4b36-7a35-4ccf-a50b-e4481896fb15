CREATE OR REPLACE FUNCTION client_56_integration_a8d5d8e3_5009_4a2b_b15f_eb25d51ee448(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
from datetime import datetime
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for sub_total
        sub_total = source_data.get('sub_total')
        if sub_total is not None:
            data['sub_total'] = float(sub_total / 100)
        
        # Logic for credits_applied
        credits_applied = source_data.get('credits_applied')
        if credits_applied is not None:
            data['credits_applied'] = float(credits_applied / 100)
        
        # Logic for amount_adjusted
        amount_adjusted = source_data.get('amount_adjusted')
        if amount_adjusted is not None:
            data['amount_adjusted'] = float(amount_adjusted / 100)
        
        # Logic for tax
        tax = source_data.get('tax')
        if tax is not None:
            data['tax'] = float(tax / 100)
        
        # Logic for total
        total = source_data.get('total')
        if total is not None:
            data['total'] = float(total / 100)
        
        # Logic for paid_at
        paid_at = source_data.get('paid_at')
        if paid_at:
            data['paid_at'] = datetime.utcfromtimestamp(paid_at)
        
        yield (data,)
$$;
