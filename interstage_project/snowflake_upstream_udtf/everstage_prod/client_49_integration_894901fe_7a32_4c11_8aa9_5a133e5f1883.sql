CREATE OR REPLACE FUNCTION client_49_integration_894901fe_7a32_4c11_8aa9_5a133e5f1883(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for of_Users__c
        no_of_users = source_data.get('of_Users__c')
        try:
            data['of_Users__c'] = int(no_of_users) if no_of_users else None
        except Exception:
            data['of_Users__c'] = None
        
        # Logic for Term__c
        term = source_data.get('Term__c')
        try:
            str_arr = term.split(" ") if term else []
            val = str_arr[0] if str_arr and len(str_arr) >= 1 else None
            data['Term__c'] = float(val) if val else None
        except Exception:
            data['Term__c'] = None
        
        yield (data,)
$$;
