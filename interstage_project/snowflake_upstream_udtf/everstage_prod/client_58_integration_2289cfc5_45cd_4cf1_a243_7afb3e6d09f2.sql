CREATE OR REPLACE FUNCTION client_58_integration_2289cfc5_45cd_4cf1_a243_7afb3e6d09f2(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for ENTITY_NO
        entity_no = source_data.get('ENTITY_NO')
        if entity_no:
            data['ENTITY_NO'] = entity_no.rstrip()
        
        # Logic for ROLL_ID
        ROLL_ID = source_data.get('ROLL_ID')
        if ROLL_ID:
            ROLL_ID = str(ROLL_ID)
            data['ROLL_ID'] = ROLL_ID.replace('.0', '')
        
        yield (data,)
$$;