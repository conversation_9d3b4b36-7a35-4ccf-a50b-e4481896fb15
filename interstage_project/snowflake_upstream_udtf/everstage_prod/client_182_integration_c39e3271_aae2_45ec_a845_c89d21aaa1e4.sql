CREATE OR REPLACE FUNCTION client_182_integration_c39e3271_aae2_45ec_a845_c89d21aaa1e4(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()

        # Logic for csr_amount
        str_val = source_data.get("csr_amount")
        if str_val is None:
            data['csr_amount'] = None
        else:
            try:
                data['csr_amount'] = float(str_val)
            except Exception:
                parsed_str = str_val.replace("-", "")
                data['csr_amount'] = -float(parsed_str)

        yield (data,)
$$;
