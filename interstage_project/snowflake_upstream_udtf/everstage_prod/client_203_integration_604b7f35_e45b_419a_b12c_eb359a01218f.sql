CREATE OR REPLACE FUNCTION client_203_integration_604b7f35_e45b_419a_b12c_eb359a01218f(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()

        # Logic for co_22_daterangeearnings
        str_val = source_data.get("Date Range Earnings")
        if not str_val:
            data["Date Range Earnings"] = None
        else:
            str_val = str_val.replace(",", "")
            data["Date Range Earnings"] = float(str_val)

        yield (data,)
$$;