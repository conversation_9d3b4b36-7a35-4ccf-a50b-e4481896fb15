CREATE OR REPLACE FUNCTION client_100025_integration_7627b924_bdbc_4343_8d0b_d2e0c7e4e618(source_data VARIANT)
RETURNS TABLE (data VARIANT)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
HANDLER = 'TransformationLogic'
AS
$$
class TransformationLogic:
    def process(self, source_data):
        data = source_data.copy()
        
        # Logic for PercentNotesWrittenToCoreAndSecondaryClients
        str_val = source_data.get("PercentNotesWrittenToCoreAndSecondaryClients")
        if str_val:
            try:
                data["PercentNotesWrittenToCoreAndSecondaryClients"] = float(str_val)
            except Exception:
                data["PercentNotesWrittenToCoreAndSecondaryClients"] = float(str_val.replace("%",""))/100
        else:
            data["PercentNotesWrittenToCoreAndSecondaryClients"] = None
        
        # Logic for PercentNotesWrittenToDigitalClients
        str_val = source_data.get("PercentNotesWrittenToDigitalClients")
        if str_val:
            try:
                data["PercentNotesWrittenToDigitalClients"] = float(str_val)
            except Exception:
                data["PercentNotesWrittenToDigitalClients"] = float(str_val.replace("%",""))/100
        else:
            data["PercentNotesWrittenToDigitalClients"] = None
        
        yield (data,)
$$;